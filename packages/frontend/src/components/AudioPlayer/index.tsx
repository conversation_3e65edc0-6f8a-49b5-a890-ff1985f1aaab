import React, { RefObject, useEffect, useRef, useState } from 'react';
import styles from './player.module.css';
import {
  Play,
  Volume,
  Volume1,
  Volume2,
  VolumeX,
  Pause,
  Loader2Icon,
} from 'lucide-react';

/*

DISABLING REASON: if you load the page directly from url (not clicking on the calls table) the audio player will not work

*/
class AudioController {
  audio: HTMLAudioElement;
  audioCtx: AudioContext;
  gainNode: GainNode;
  analyserNode: AnalyserNode;
  track: MediaElementAudioSourceNode;
  bufferLength: number;
  dataArray: Uint8Array;
  bufferPercentage: number;
  isPlaying: boolean;
  canvas: HTMLCanvasElement;
  canvasCtx: any; //it should be CanvasRenderingContext2D but seems buggy in typescript

  constructor(audio: HTMLAudioElement, canvas: HTMLCanvasElement) {
    this.audio = audio;
    this.canvas = canvas;
    this.canvasCtx = this.canvas.getContext('2d');
    this.isPlaying = false;
    this.audioCtx = new AudioContext();
    this.gainNode = this.audioCtx.createGain();
    this.analyserNode = this.audioCtx.createAnalyser();
    this.track = this.audioCtx.createMediaElementSource(this.audio);

    this.analyserNode.fftSize = 2048;
    this.bufferLength = this.analyserNode.frequencyBinCount;
    this.dataArray = new Uint8Array(this.bufferLength);
    this.analyserNode.getByteFrequencyData(this.dataArray);

    this.bufferPercentage = 75;

    this.track
      .connect(this.gainNode)
      .connect(this.analyserNode)
      .connect(this.audioCtx.destination);
  }

  changeVolume(v: number) {
    this.gainNode.gain.value = v;
  }

  updateFrequency() {
    if (!this.isPlaying) {
      return;
    }

    this.analyserNode.getByteFrequencyData(this.dataArray);

    this.canvasCtx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.canvasCtx.fillStyle = `rgba(0,0,0, 0)`;
    this.canvasCtx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    const barWidth = 3;
    const gapBetweenBars = 2;

    const bufferSize = (this.bufferLength * this.bufferPercentage) / 100;
    const barCount =
      this.bufferLength / (barWidth + gapBetweenBars - gapBetweenBars); //to adjust the number of bars to the width of the canvas
    const maxHeight = 50;

    let x = 0; //x of the canvas
    for (let i = 0; i < barCount; i++) {
      const f = this.dataArray[i]; //255 is max value for frequency
      const perc = (this.dataArray[i] * 100) / 255;
      const h = (perc * this.canvas.height) / 100;

      this.canvasCtx.fillStyle = `rgba(${f}, 194, 160, 1)`; //we use frequnecy to change color dinamically //`rgba(50, 194, ${f}, 1)`;///
      this.canvasCtx.fillRect(x, this.canvas.height - h, barWidth, h);

      x += barWidth + gapBetweenBars;
    }

    requestAnimationFrame(this.updateFrequency.bind(this));
  }
}

interface AudioPlayerProps {
  audioRef: React.RefObject<HTMLAudioElement>;
  loading: boolean;
}

function AudioPlayer({ audioRef, loading }: AudioPlayerProps) {
  const audioController = useRef<AudioController>();
  const canvasRef = useRef<HTMLCanvasElement>();
  const volumeRef = useRef<HTMLInputElement>();
  const progressRef = useRef<HTMLInputElement>();
  const [progress, setProgress] = useState<string>();
  const [duration, setDuration] = useState<string>();
  const [volume, setVolume] = useState<string>('0.6');
  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  const toggglePlay = () => {
    if (audioRef.current) {
      let start = true;
      if (audioController && audioController.current) {
        audioController.current.isPlaying = !audioController.current.isPlaying;
        start = audioController.current.isPlaying;
      }

      if (start) {
        setIsPlaying(true);
        audioRef.current.play();
      } else {
        setIsPlaying(false);
        audioRef.current.pause();
      }
    }
  };

  const monitorProgress = () => {
    if (audioRef.current) {
      if (audioController && audioController.current) {
        audioController.current.updateFrequency();
      }

      const progress = audioRef.current.currentTime;

      if (progressRef.current) {
        progressRef.current.value = `${progress}`;
      }

      setProgress(formatProgress(progress));
    }
  };

  const formatProgress = (n: number) => {
    const secs = `${parseInt(`${n % 60}`, 10)}`.padStart(2, '0');
    const min = parseInt(`${(n / 60) % 60}`, 10);

    return `${min}:${secs}`;
  };

  const loadMetadata = () => {
    if (audioRef.current && progressRef.current) {
      setDuration(formatProgress(audioRef.current.duration));
      progressRef.current.max = String(audioRef.current.duration);
      setProgress(formatProgress(audioRef.current.currentTime));
    }
  };

  const goToTime = (e: Event) => {
    if (audioRef.current) {
      audioRef.current.currentTime = parseInt(
        (e.target as HTMLInputElement)?.value,
      );
    }
  };

  const changeVolume = (e: Event) => {
    if (audioController.current) {
      const v = (e.target as HTMLInputElement)?.value;
      audioController.current.changeVolume(parseFloat(v));
      setVolume(v);
    }
  };

  const toggleVolume = () => {
    if (audioController.current) {
      let nv = '';
      if (volume == '0.6') {
        nv = '1.3';
      } else if (volume == '1.3') {
        nv = '2';
      } else if (volume == '2') {
        nv = '0';
      } else if (volume == '0') {
        nv = '0.6';
      }
      audioController.current.changeVolume(parseFloat(nv));
      setVolume(nv);
    }
  };

  useEffect(() => {
    const aRefCopy = audioRef.current;
    const pBarCopy = progressRef.current;
    const vRefCopy = volumeRef.current;
    if (aRefCopy) {
      if (
        !audioController.current &&
        canvasRef.current &&
        volumeRef.current &&
        progressRef.current
      ) {
        audioController.current = new AudioController(
          aRefCopy,
          canvasRef.current,
        );
        audioController.current.changeVolume(parseFloat(volume)); //set starting volume
      }
      aRefCopy?.addEventListener('loadedmetadata', loadMetadata);
      aRefCopy?.addEventListener('timeupdate', monitorProgress);
    }

    if (pBarCopy) {
      pBarCopy.addEventListener('input', goToTime);
    }

    if (vRefCopy) {
      vRefCopy.addEventListener('input', changeVolume);
    }

    return () => {
      if (aRefCopy) {
        aRefCopy.removeEventListener('loadedmetadata', loadMetadata);
        aRefCopy.removeEventListener('timeupdate', monitorProgress);
      }
      if (pBarCopy) {
        pBarCopy.removeEventListener('input', goToTime);
      }
      if (vRefCopy) {
        vRefCopy.removeEventListener('input', changeVolume);
      }
    };
  }, []);

  return (
    <div className={styles.container}>
      <audio ref={audioRef} controls style={{ display: 'none' }}></audio>
      <div className={styles.loader} style={{ display: loading ? '' : 'none' }}>
        <div>
          <Loader2Icon className="animate-spin" style={{ color: '#ffffff' }} />
        </div>
      </div>
      <div className={styles.player} style={{ display: loading ? 'none' : '' }}>
        <div onClick={toggglePlay} className={styles.iconBtn}>
          {isPlaying ? <Pause /> : <Play />}
        </div>

        <div className={styles.frequencyPanel}>
          <input
            type="range"
            ref={progressRef as RefObject<HTMLInputElement>}
            className={styles.progressBar}
          />
          <canvas
            ref={canvasRef as RefObject<HTMLCanvasElement> | null}
            style={{ width: '100%', height: '20px' }}
          ></canvas>
        </div>
        <div className={styles.progressInfo}>
          {progress}/{duration}
        </div>
        <div className={styles.iconBtn} onClick={toggleVolume}>
          {volume == '0' && <VolumeX />}
          {volume == '0.6' && <Volume />}
          {volume == '1.3' && <Volume1 />}
          {volume == '2' && <Volume2 />}
        </div>
        <input
          type="range"
          max="2"
          step="0.01"
          min="0"
          value={volume}
          ref={volumeRef as RefObject<HTMLInputElement>}
          className={styles.volumeBar}
        />
      </div>
    </div>
  );
}

export default React.memo(AudioPlayer);
