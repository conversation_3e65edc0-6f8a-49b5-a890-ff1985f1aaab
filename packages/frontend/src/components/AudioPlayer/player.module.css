.container {
  width: 100%;
  background-color: #18181b;
  border-radius: 10px;
  padding: 10px;
}

.player {
  position: relative;
  display: flex;
  align-items: center;
}

.loader {
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconBtn {
  color: #ffffff;
  cursor: pointer;
}

.iconBtn:hover {
  color: #dcdcdc;
}

.frequencyPanel {
  flex: 1;
  z-index: 1;
  margin: 0px 4px;
  position: relative;
}

.progressInfo {
  font-size: 14px;
  color: #ffffff;
  padding: 0px 6px;
}

.progressBar {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  z-index: 2;
  transform: translateY(-50%);
  appearance: none;
  margin: 0;
  overflow: hidden;
  background: none;
  cursor: pointer;
}

.progressBar::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 0;
  box-shadow: -300px 0 0 300px #84aa9d70;
}

.progressBar::-moz-range-thumb {
  appearance: none;
  height: 20px;
  width: 0;
  box-shadow: -300px 0 0 300px #84aa9d70;
}

.progressBar::-ms-thumb {
  appearance: none;
  height: 20px;
  width: 0;
  box-shadow: -300px 0 0 300px #84aa9d70;
}

.volumeBar {
  display: none;
  position: absolute;
  appearance: none;
  height: 20px;
  right: 100%;
  top: 50%;
  transform: translateY(-50%) rotate(180deg);
  z-index: 5;
  margin: 0;
  border-radius: 2px;
  background: #ffffff;
}

.volumeBar::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 10px;
  background: #6d78ff;
}

.volumeBar::-moz-range-thumb {
  appearance: none;
  height: 20px;
  width: 10px;
  background: #6d78ff;
}

.volumeBar::-ms-thumb {
  appearance: none;
  height: 20px;
  width: 10px;
  background: #6d78ff;
}
