import CompetitionCountdownTimer from '@/common/CompetitionCountdownTimer';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import { cn } from '@/lib/utils';
import { ChevronRight } from 'lucide-react';
import { useEffect, useState } from 'react';

interface IProps {
  title: string | BreadcrumbItem[];
  rightComponent?: React.ReactNode;
}

export interface BreadcrumbItem {
  title: string;
  href?: string;
}

export default function PageHeader({ title, rightComponent }: IProps) {
  const { isCompetitionOrg } = useUserSession();
  const { goToPage } = useRouting();
  const [breadcrumbs, setBreadCrumbs] = useState<BreadcrumbItem[]>([]);

  useEffect(() => {
    if (typeof title == 'string') {
      setBreadCrumbs([
        {
          title,
        },
      ]);
    } else {
      setBreadCrumbs(title);
    }
  }, [title]);
  return (
    <div className="flex items-center">
      {breadcrumbs.map((b: BreadcrumbItem, i: number) => {
        const hasLink = b.href ? true : false;
        const isLast = i == breadcrumbs.length - 1;
        return (
          <div key={b.title} className="flex items-center">
            <div
              className={cn('pr-2', {
                'cursor-pointer hover:text-black': hasLink,
                'text-muted-foreground text-base': !isLast,
                'font-semibold text-base': isLast,
              })}
              onClick={() => {
                if (hasLink && b.href) {
                  goToPage(b.href);
                }
              }}
            >
              {b.title}
            </div>
            {!isLast && (
              <div className="pr-2">
                <ChevronRight size={18} className="text-muted-foreground" />
              </div>
            )}
          </div>
        );
      })}
      <div className={'pr-0 flex-1'}>
        {isCompetitionOrg && <CompetitionCountdownTimer />}
      </div>
      {rightComponent}
    </div>
  );
}
