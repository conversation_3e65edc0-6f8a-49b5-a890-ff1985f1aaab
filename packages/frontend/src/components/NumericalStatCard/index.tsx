import { cn } from '@/lib/utils';
import { InfoIcon, LucideIcon } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Skeleton } from '../ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { CallDto } from '@/lib/Call/types';
import Analytics from '@/system/Analytics';
import { CallEvents } from '@/system/Analytics/events/CallEvents';

interface INumericalStatCardProps {
  title: string;
  text: string;
  rawValue: number;
  description?: string;
  Icon: LucideIcon;
  loading: boolean;
  recommendedRange: number[];
  call: CallDto;
}

function NumericalStatCard({
  title,
  text,
  rawValue,
  description,
  Icon,
  loading,
  recommendedRange,
  call,
}: INumericalStatCardProps) {
  const [lowerBound, upperBound] = recommendedRange || [0, 0];
  const aboveRecommendedRange = rawValue > upperBound;
  const belowRecommendedRange = rawValue < lowerBound;

  return (
    <Card className="w-full mr-0">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>{title}</CardTitle>
          <Icon className="w-4 h-4 text-muted-foreground" />
        </div>
        <CardDescription>{description || ''}</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <Skeleton className="w-[100px] h-[30px]" />
        ) : (
          <h3 className="text-xl font-semibold">{text}</h3>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {loading ? (
          <Skeleton className="w-[50px] h-[10px]" />
        ) : (
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center space-x-2">
              <div
                className={cn('rounded-full p-1 w-2 h-2', {
                  'bg-green-500':
                    !aboveRecommendedRange && !belowRecommendedRange,
                  'bg-red-500': aboveRecommendedRange || belowRecommendedRange,
                })}
              />
              <p className="text-sm text-muted-foreground">
                {aboveRecommendedRange
                  ? 'Above'
                  : belowRecommendedRange
                    ? 'Below'
                    : 'In'}{' '}
                recommended range
              </p>
            </div>
            <TooltipProvider delayDuration={100}>
              <Tooltip
                onOpenChange={(o) => {
                  if (o) {
                    Analytics.track(CallEvents.STAT_CARD_INFO_HOVERED, {
                      card: title,
                      agentId: call?.agent?.id,
                      id: call?.id,
                      vapiId: call?.vapiId,
                      orgId: call?.orgId,
                    });
                  }
                }}
              >
                <TooltipTrigger>
                  <InfoIcon className="w-4 h-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    The recommended range is between {lowerBound} and{' '}
                    {upperBound}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}

export default NumericalStatCard;
