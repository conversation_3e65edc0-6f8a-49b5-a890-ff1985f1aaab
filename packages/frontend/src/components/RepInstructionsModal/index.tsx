import useUserSession from '@/hooks/useUserSession';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { useState } from 'react';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import { CheckIcon } from 'lucide-react';

export default function RepInstructionsModal({
  description,
  callContext,
}: {
  description: string;
  callContext?: string;
}) {
  const [isOpen, setIsOpen] = useState(true);
  const [isAcknowledged, setIsAcknowledged] = useState(false);
  const { isRepInstructionsMandatory, hideCallContext } = useUserSession();

  const accept = () => {
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen && isRepInstructionsMandatory}>
      <DialogContent className="max-w-4xl overflow-y-auto max-h-[60vh]">
        <DialogHeader className="space-y-4">
          <DialogTitle>AI Roleplay Instructions</DialogTitle>
        </DialogHeader>

        {!!description && (
          <div className="mt-2">
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap">{description}</div>
            </div>
          </div>
        )}
        {!!callContext && !hideCallContext && (
          <div className="mt-6">
            <div className="text-sm font-semibold mb-2 text-primary">
              {"Here's what the prospect was told about this call:"}
            </div>
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap">{callContext}</div>
            </div>
          </div>
        )}

        <div className="mt-8 space-y-6">
          <div className="flex items-start space-x-3">
            <Checkbox
              id="acknowledge"
              checked={isAcknowledged}
              onCheckedChange={(checked) =>
                setIsAcknowledged(checked as boolean)
              }
              className="mt-1"
            />
            <label
              htmlFor="acknowledge"
              className="text-sm font-bold leading-relaxed peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I acknowledge that I have read and understood these instructions
            </label>
          </div>
          <div className="flex justify-end">
            <Button
              variant={'outline'}
              onClick={accept}
              disabled={!isAcknowledged}
            >
              <CheckIcon className="w-4 h-4 mr-2" />
              Accept & Continue
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
