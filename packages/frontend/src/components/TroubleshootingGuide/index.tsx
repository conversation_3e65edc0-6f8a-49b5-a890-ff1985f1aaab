import Image from 'next/image';
import { Separator } from '../ui/separator';
import { Button } from '../ui/button';
import DialogFullScreen from '../ui/Hyperbound/DialogFullScreen';
import Header from '../ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '../ui/Hyperbound/DialogFullScreen/ScrollableContent';
import Link from 'next/link';

interface IProps {
  open: boolean;
  onClose: () => void;
}

export default function TroubleshootingGuide({ open, onClose }: IProps) {
  return (
    <DialogFullScreen open={open} onOpenChange={onClose} width="w-[640px]">
      <Header
        title="Problems with your microphone?"
        onClose={onClose}
        className="px-5 pt-5 mb-4"
      />
      <ScrollableContent>
        <div className="text-sm px-5">
          <div>
            Is the bot not responding at all? If so, then your mic audio is not
            being captured.
          </div>
          <div className="mt-6">
            <Image
              src="/images/micPermissions.png"
              alt="Mic not working"
              width={640}
              height={220}
            />
          </div>
          <div className="mt-6 font-semibold">
            Check your browser permissions
          </div>
          <div className="mt-2">
            Ensure your browser has permission to access your microphone. Go to
            your browser settings, find the permissions section, and make sure
            the microphone access is allowed for this site.
          </div>
          <Separator className="mt-4 mb-4" />
          <div className="mt-6 font-semibold">
            Are you using an external mic with a docking station?
          </div>
          <div className="mt-2">
            Disconnect your external mic/docking station and use earpods or your
            in-built computer mic. We&apos;re aware of this issue and are
            working on it.
          </div>
          <Separator className="mt-4 mb-4" />
          <div className="mt-6 font-semibold">
            Tried everything and still having issues?
          </div>
          <div className="mt-2">
            Contact us at{' '}
            <Link href="mailto:<EMAIL>"><EMAIL></Link> or
            leave us a message using the green chat icon on the bottom right
            corner of the screen
          </div>
        </div>
      </ScrollableContent>
      {/****************************/}
      {/********* FOOTER *********/}
      {/****************************/}
      <div className="flex items-center justify-end mr-4 mb-4">
        <Button onClick={onClose}>Close</Button>
      </div>
    </DialogFullScreen>
  );
}
