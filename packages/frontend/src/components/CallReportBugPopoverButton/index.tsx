import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useUserSession from '@/hooks/useUserSession';
import CallService from '@/lib/Call';
import { CallDto } from '@/lib/Call/types';
import { useAuthInfo } from '@propelauth/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { BugIcon, Loader2Icon } from 'lucide-react';
import { useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';

interface ICallReportBugPopoverProps {
  call: CallDto;
}

export function CallReportBugPopoverButton({
  call,
}: ICallReportBugPopoverProps) {
  const queryClient = useQueryClient();
  const { isInIframe } = useUserSession();
  const [checkedExcludeAnalytics, setCheckedExcludeAnalytics] = useState(
    call.excludeFromAnalytics,
  );
  const [excludeFromAnalyticsReason, setExcludeFromAnalyticsReason] = useState(
    call.excludeFromAnalyticsReason,
  );

  const [checkedReportBug, setCheckedReportBug] = useState(call.buggedOut);
  const [bugDescriptionValue, setBugDescriptionValue] = useState(
    call.bugDescription,
  );
  const errorToastId = useRef<Id | null>(null);
  const authInfo = useAuthInfo();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const toggleReportBugForDemoCallMutation = useMutation({
    mutationFn: CallService.toggleReportBugForDemoCall,
    onSuccess: () => {
      // Analytics.track(CallEvents.DELETE_SUCCESS, {
      //   ...params,
      //   from: location,
      // });
      queryClient.invalidateQueries({ queryKey: ['demoCalls'] });
      queryClient.invalidateQueries({
        queryKey: ['demoCall', true, call.vapiId],
      });
      errorToastId.current = toast.success('Bug report saved!');
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error editing the bug report for the call');
        errorToastId.current = toast.error(
          'There was an error with editing the bug report for the call. Please try again.',
        );
      }
      // Analytics.track(CallEvents.DELETE_ERROR, {
      //   ...params,
      //   from: location,
      // });
    },
  });

  const handleReportBugOrExcludeFromAnalyticsForCallMutation = useMutation({
    mutationFn: CallService.reportBugOrExcludeFromAnalyticsForCall,
    onSuccess: () => {
      // Analytics.track(CallEvents.DELETE_SUCCESS, {
      //   ...params,
      //   from: location,
      // });
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      queryClient.invalidateQueries({
        queryKey: ['call', false, call.vapiId],
      });
      errorToastId.current = toast.success('Sucessfully Reported!');
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error deleting the call');
        errorToastId.current = toast.error(
          'There was an error deleting the call. Please try again.',
        );
      }
      // Analytics.track(CallEvents.DELETE_ERROR, {
      //   ...params,
      //   from: location,
      // });
    },
  });

  return (
    <Popover
      onOpenChange={(open) => {
        if (!open) {
          setCheckedExcludeAnalytics(call.excludeFromAnalytics);
          setExcludeFromAnalyticsReason(call?.excludeFromAnalyticsReason);
          setCheckedReportBug(call.buggedOut);
          setBugDescriptionValue(call.bugDescription);
        }
      }}
    >
      <PopoverTrigger asChild>
        <Button variant="ghost" size={'icon'} className="rounded-full">
          <BugIcon className="h-4 w-4" />
        </Button>
        {/* <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size={"icon"} className="rounded-full">
                <BugIcon className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Report bug</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider> */}
      </PopoverTrigger>
      <PopoverContent className="w-96">
        <div className="grid gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <BugIcon className="w-4 h-4" />
              <h4 className="font-medium leading-none">
                Issues with your call?
              </h4>
            </div>
            <p className="text-sm text-muted-foreground">
              {"Tell us what's wrong & we'll resolve it!"}
            </p>
          </div>
          <div className="">
            <div className="flex">
              <Label className="flex-grow">Report bug</Label>
              <Switch
                checked={checkedReportBug}
                onCheckedChange={(value) => {
                  setCheckedReportBug(value);
                }}
                className="data-[state=checked]:bg-red-500 transition-colors"
                id="report-bug"
              />
            </div>
            {checkedReportBug && (
              <div className="mt-2 space-y-2">
                <Textarea
                  value={bugDescriptionValue}
                  onChange={(e) => setBugDescriptionValue(e.target.value)}
                  id="bugDescription"
                  placeholder="Briefly describe the bug..."
                />
              </div>
            )}
            <br />
            {authInfo?.isLoggedIn && !isInIframe && (
              <div>
                <div className="flex">
                  <Label className="flex-grow">Exclude from analytics</Label>
                  <Switch
                    checked={checkedExcludeAnalytics}
                    onCheckedChange={(value) => {
                      setCheckedExcludeAnalytics(value);
                    }}
                    className="data-[state=checked]:bg-orange-500 transition-colors"
                    id="exclude-from-analytics"
                  />
                </div>
                {checkedExcludeAnalytics && (
                  <div className="mt-2 space-y-2">
                    <Textarea
                      value={excludeFromAnalyticsReason}
                      onChange={(e) =>
                        setExcludeFromAnalyticsReason(e.target.value)
                      }
                      id="excludeDescription"
                      placeholder="Briefly describe the reason for excluding this call from analytics..."
                    />
                  </div>
                )}
              </div>
            )}
          </div>
          <Button
            variant="default"
            disabled={
              handleReportBugOrExcludeFromAnalyticsForCallMutation.isPending ||
              toggleReportBugForDemoCallMutation.isPending
            }
            onClick={() => {
              if (authInfo?.isLoggedIn) {
                handleReportBugOrExcludeFromAnalyticsForCallMutation.mutate({
                  vapiId: call.vapiId,
                  buggedOut: checkedReportBug,
                  bugDescription: checkedReportBug ? bugDescriptionValue : '',
                  excludeFromAnalytics: checkedExcludeAnalytics,
                  excludeFromAnalyticsReason: checkedExcludeAnalytics
                    ? excludeFromAnalyticsReason || ''
                    : '',
                });
              } else {
                toggleReportBugForDemoCallMutation.mutate({
                  vapiId: call.vapiId,
                  buggedOut: checkedReportBug,
                  bugDescription: bugDescriptionValue,
                  demoInboundFormResponseId: hbDemoInboundForm?.id as number,
                });
              }
            }}
          >
            {handleReportBugOrExcludeFromAnalyticsForCallMutation.isPending ||
            toggleReportBugForDemoCallMutation.isPending ? (
              <div className="flex items-center">
                <Loader2Icon className="animate-spin mr-2" />
                Submitting...
              </div>
            ) : (
              'Save & Submit Report'
            )}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
