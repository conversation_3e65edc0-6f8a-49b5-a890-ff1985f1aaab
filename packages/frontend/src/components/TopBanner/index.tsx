import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import usePartnerByCachedPartnerId from '@/hooks/usePartnerByCachedPartnerId';
import { CalendarCheckIcon, LogInIcon, TrophyIcon, XIcon } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import LoginOrSignupModal from '../LoginOrSignupModal';
import { Button } from '../ui/button';
import dayjs from 'dayjs';
import CountdownTimer from '../CountdownTimer';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';

const targetDate = dayjs('2024-08-30T07:00:00Z');

function TopBanner() {
  const [isLoginOrSignupModalOpen, setIsLoginOrSignupModalOpen] =
    useState(false);
  const {
    data: hbDemoInboundForm,
    isLoading,
    isFetched,
  } = useHbDemoInboundForm();
  const { data: partner, isLoading: isPartnerLoading } =
    usePartnerByCachedPartnerId();

  const firstName = hbDemoInboundForm?.name?.split(' ')?.[0]?.trim();

  return (
    <div
      className="fixed top-0 w-full py-2 px-4 text-center flex justify-between items-center bg-primary/95 text-white z-50"
      style={{
        // background: "linear-gradient(to right, #1D2671, #C33764)",
        background: 'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
      }}
    >
      <div className="md:flex space-x-3 items-center hidden">
        <Link
          href="https://hyperbound.ai"
          className="hidden md:block"
          target="_blank"
        >
          <Image
            src={'/images/white-logo-with-text.svg'}
            alt={`Hyperbound logo`}
            className=""
            width={120}
            height={24}
            priority
          />
        </Link>
        {partner?.partnerId && (
          <>
            <XIcon className="text-white w-4 h-4" />
            <Image
              src={partner?.logo as string}
              alt={`${partner?.name} logo`}
              width={24}
              height={24}
              className="rounded-lg"
              priority
            />
            <p className="text-base font-medium">{partner?.name}</p>
          </>
        )}
        <div>
          {/* {firstName ? `Hi, ${firstName}!` : ""}{" "} */}
          {partner?.ctaBannerText || (
            <div className="md:flex items-center hidden">
              Have questions? Email
              <a
                href={`mailto:${partner?.email || '<EMAIL>'}`}
                target="_blank"
                className="underline ml-1"
                rel="noreferrer"
              >
                {partner?.email || '<EMAIL>'}
              </a>
              {/* <p className="text-xs md:text-base">
                🚨 BEAT THE BOT 2.0 global cold calling showdown is officially
                completed!
                🚨
              </p> */}
              {/* <Link
                href={`/competitions/rb2b-btb2/leaderboard`}
              >
                <Button
                  type="submit"
                  size={"default"}
                  className="text-[15px] rounded-full mr-4"
                  variant={"secondary"}
                >
                  <TrophyIcon className="w-4 h-4 mr-2" />
                  {"Check Leaderboard"}
                </Button>
              </Link> */}
            </div>
          )}
        </div>
      </div>
      <div className="flex space-x-3">
        <Link
          href={partner?.ctaUrl || 'https://calendly.com/d/cppn-cqx-39h'}
          target="_blank"
        >
          <Button
            type="submit"
            size={'lg'}
            className="text-[15px] rounded-full"
            variant={'secondary'}
          >
            <CalendarCheckIcon className="w-4 h-4 mr-2" />
            {partner?.ctaBtnText || 'Book a demo to build your own bots'}
          </Button>
        </Link>
        <Button
          size={'lg'}
          className="text-[15px] rounded-full hidden md:flex"
          onClick={() => {
            setIsLoginOrSignupModalOpen(true);
          }}
        >
          <LogInIcon className="w-4 h-4 mr-2" />
          {'Sign in'}
        </Button>
      </div>
      <LoginOrSignupModal
        modalOpen={isLoginOrSignupModalOpen}
        setModalOpen={setIsLoginOrSignupModalOpen}
      />
    </div>
  );
}

export default TopBanner;
