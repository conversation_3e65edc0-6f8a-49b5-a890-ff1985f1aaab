import * as React from 'react';
import * as AccordionPrimitive from '@radix-ui/react-accordion';
import { ChevronDownIcon } from '@radix-ui/react-icons';
import ValidityBadges from '@/common/CreateBuyerForm/Main/AIBotCreatorForm/ActionButtons/ValidityBadges';

import { cn } from '@/lib/utils';

const Accordion = AccordionPrimitive.Root;

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn('border-b', className)}
    {...props}
  />
));
AccordionItem.displayName = 'AccordionItem';

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        'flex flex-1 items-center py-4 text-sm font-medium transition-all hover:underline [&[data-state=open]>.chevron]:rotate-180',
        className,
      )}
      {...props}
    >
      {children}
      <ChevronDownIcon
        className={cn(
          'chevron ml-auto h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200',
          {
            invisible: className?.includes('no-chevron'),
          },
        )}
      />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

export const AccordionTriggerAlt = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger> & {
    isValid?: boolean;
  }
>(({ className, isValid, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        'p-2 flex flex-1 items-center py-4 text-sm font-medium transition-all hover:underline [&[data-state=open]>div>.chevron]:rotate-180 justify-between [&[data-state=open]>div>div>div>.heading]:text-zinc-950 [&[data-state=open]>div>div>*]:text-zinc-950',
        className,
      )}
      {...props}
    >
      {/* Left side content */}

      <div className="flex items-center ">
        {children}

        {/* Right side content: Validity Badges and Chevron */}
        <ChevronDownIcon
          className={cn(
            'chevron ml-auto h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200',
            {
              invisible: className?.includes('no-chevron'),
            },
          )}
        />
      </div>

      {isValid !== undefined && (
        <span className="hover:underline">
          <ValidityBadges isValid={isValid} />
        </span>
      )}
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));

AccordionTriggerAlt.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className="p-3 overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
    {...props}
  >
    <div className={cn('pb-4 pt-0', className)}>{children}</div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
