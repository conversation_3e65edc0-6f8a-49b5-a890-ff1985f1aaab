import React, { useEffect } from 'react';
import Textarea from './textarea';

const AutoExpandingTextarea = React.forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement>
>(({ onChange, ...props }, ref) => {
  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = event.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight}px`;
    onChange?.(event);
  };

  useEffect(() => {
    const textarea = ref as React.RefObject<HTMLTextAreaElement>;
    if (textarea.current) {
      textarea.current.style.height = 'auto';
      textarea.current.style.height = `${textarea.current.scrollHeight}px`;
    }
  }, [props.value]);

  return (
    <Textarea
      {...props}
      ref={ref}
      onChange={handleChange}
      className="overflow-hidden whitespace-pre-wrap break-words"
    />
  );
});

AutoExpandingTextarea.displayName = 'AutoExpandingTextarea';

export default AutoExpandingTextarea;
