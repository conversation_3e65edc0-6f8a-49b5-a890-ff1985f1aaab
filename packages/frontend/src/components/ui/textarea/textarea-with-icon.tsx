import React from 'react';

import { Button } from '../button';
import { AutoSizeTextarea } from '.';

interface TextAreaWithIconProps {
  placeholder: string;
  value: string;
  onChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  onKeyUp: (
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  icon: React.ElementType;
  onIconClick: () => void;
  isRequired?: boolean;
}

const TextAreaWithIcon = ({
  placeholder,
  value,
  onChange,
  onKeyUp,
  icon: Icon,
  onIconClick,
  isRequired,
}: TextAreaWithIconProps) => {
  return (
    <div className="relative flex items-center">
      <AutoSizeTextarea
        isRequired={isRequired}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onKeyUp={onKeyUp}
        className="w-full pl-3 pr-10 border border-zinc-200 rounded-md focus:outline-none focus:ring-2 focus:ring-primary py-2 min-h-8"
      />
      {Icon && (
        <Button
          variant="ghost"
          type="button"
          onClick={onIconClick}
          className="absolute right-2 p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary disabled:text-zinc-400"
          disabled={!value}
        >
          <Icon className="w-4 h-4 text-zinc-500" />
        </Button>
      )}
    </div>
  );
};

export default TextAreaWithIcon;
