import React, { useEffect, useMemo, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import Textarea from './textarea';

interface AutoSizeTextareaProps {
  placeholder: string;
  value: string | undefined;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onKeyUp?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  isRequired?: boolean;
  className?: string;
}

export default function AutoSizeTextarea({
  value,
  isRequired,
  placeholder,
  onChange,
  onKeyUp,
  className,
}: AutoSizeTextareaProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [baseLength, setBaseLength] = useState(150); // Default fallback

  useEffect(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;

      // Estimate base length dynamically
      const avgCharWidth = 8; // Adjust based on font
      const newBaseLength = Math.floor(textarea.clientWidth / avgCharWidth);
      setBaseLength(newBaseLength);
    }
  }, [textareaRef.current?.clientWidth]); // Update when width changes

  const rows = useMemo(() => {
    if (!value?.trim()) return 1;

    const lines = value.split(/\r?\n/);

    const totalRows = lines.reduce((total, line) => {
      return total + Math.floor(line.length / baseLength);
    }, lines.length);

    return Math.max(1, totalRows);
  }, [value, baseLength]);

  return (
    <Textarea
      ref={textareaRef}
      required={isRequired}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onKeyUp={onKeyUp}
      className={cn(className, 'overflow-hidden')}
      rows={rows} // Dynamically set rows based on text wrapping
    />
  );
}
