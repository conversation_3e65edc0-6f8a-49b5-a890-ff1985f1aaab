import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import {
  Dialog,
  DialogPortal,
  DialogOverlay,
} from '@/components/ui/Hyperbound/dialog-for-calls';
import { AnimatePresence, motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface IProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
  slideFromBottom?: boolean;
  width?: string;
  fitHeightToContent?: boolean;
}

export default function DialogFullScreen({
  open,
  onOpenChange,
  children,
  slideFromBottom,
  width,
  fitHeightToContent,
}: IProps) {
  const [exitingPnl, setExitingPnl] = useState<boolean>(false);

  const closeDialog = () => {
    setExitingPnl(true);
    setTimeout(() => {
      onOpenChange(false);
    }, 160);
  };
  //ESC key to close dialog
  const escFunction = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' || event.key === 'Esc' || event.keyCode === 27) {
      closeDialog();
    }
  }, []);

  useEffect(() => {
    document.addEventListener('keydown', escFunction, false);

    return () => {
      document.removeEventListener('keydown', escFunction, false);
    };
  }, [escFunction]);

  let _width = 'w-full';
  let left = ' left-0';
  if (width) {
    _width = width;
    left = ' left-[50%] translate-x-[-50%]';
  }

  /***********************************/
  /************ RENDERING ************/
  /***********************************/

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogPortal>
        <DialogOverlay />
        <div
          className={
            'fixed top-0  right-0 bottom-0  h-full z-50 overflow-hidden ' +
            _width +
            left
          }
        >
          <AnimatePresence>
            {!exitingPnl && (
              <motion.div
                initial={{ opacity: 0, y: 700 }}
                exit={{ opacity: 0, y: 700 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, ease: 'easeOut' }}
                className="flex flex-col h-screen"
              >
                {fitHeightToContent && <div className="flex-1" />}
                <div
                  className={cn(
                    'bg-white m-10 rounded-2xl overflow-hidden flex flex-col',
                    {
                      ' h-full': !fitHeightToContent,
                    },
                  )}
                >
                  {children}
                </div>
                {fitHeightToContent && <div className="flex-1" />}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
