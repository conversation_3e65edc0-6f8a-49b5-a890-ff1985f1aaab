import { useEffect, useRef } from 'react';

export const useScrollableContent = () => {
  const scrollableContainer = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    window.addEventListener('wheel', scrollContent);

    return () => {
      window.removeEventListener('wheel', scrollContent);
    };
  }, []);

  const scrollContent = (e: WheelEvent) => {
    if (scrollableContainer.current) {
      scrollableContainer.current.scrollTop += e.deltaY;
    }
  };

  return scrollableContainer;
};
