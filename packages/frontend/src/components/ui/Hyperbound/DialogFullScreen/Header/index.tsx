import { Loader2Icon, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface IProps {
  title: string;
  onClose: () => void;
  className?: string;
  isLoading?: boolean;
}
export default function Header({
  title,
  onClose,
  className,
  isLoading,
}: IProps) {
  return (
    <div className={cn('flex items-center', className)}>
      <div className="flex items-center font-semibold text-lg">
        <div>{title}</div>
        {isLoading && (
          <div>
            <Loader2Icon className="animate-spin ml-2" size={18} />
          </div>
        )}
      </div>
      <div className="flex-grow"></div>
      <div className="cursor-pointer " onClick={onClose}>
        <X size={20} />
      </div>
    </div>
  );
}
