import { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface IProps {
  children: React.ReactNode;
  className?: string;
}

export default function ScrollableContent({ children, className }: IProps) {
  const scrollableContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener('wheel', scrollContent);

    return () => {
      window.removeEventListener('wheel', scrollContent);
    };
  }, []);

  const scrollContent = (e: WheelEvent) => {
    if (scrollableContainer.current) {
      scrollableContainer.current.scrollTop += e.deltaY;
    }
  };

  return (
    <div
      ref={scrollableContainer}
      className={cn('overflow-auto flex-1', className)}
    >
      {children}
    </div>
  );
}
