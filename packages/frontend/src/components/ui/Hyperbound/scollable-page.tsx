import { cn } from '@/lib/utils';
import { forwardRef, useLayoutEffect, useRef } from 'react';

interface IProps {
  children?: React.ReactNode;
  className?: string;
  onScroll?: (e: Event) => void;
  marginBottom?: number;
}

export default function ScrollablePage({
  children,
  className,
  onScroll,
  marginBottom,
}: IProps) {
  const containerPanel = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (containerPanel.current && onScroll) {
      containerPanel.current.addEventListener('scroll', onScroll);
    }

    function updateSize() {
      if (containerPanel.current) {
        const rect = containerPanel.current.getBoundingClientRect();
        const h = window.innerHeight - rect.top - (marginBottom || 0);
        containerPanel.current.style.height = `${h}px`;
      }
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => {
      window.removeEventListener('resize', updateSize);
      if (containerPanel.current && onScroll) {
        containerPanel.current.removeEventListener('scroll', onScroll);
      }
    };
  }, []);

  return (
    <div ref={containerPanel} className={cn('overflow-hidden', className)}>
      {children}
    </div>
  );
}
