import { cn } from '@/lib/utils';
import { CheckIcon } from 'lucide-react';

interface IProps {
  checked: boolean;
  onToggle: () => void;
  hover?: boolean;
  className?: string;
  disabled?: boolean;
}

export default function Checkbox({
  checked,
  onToggle,
  hover,
  className,
  disabled,
}: IProps) {
  if (!hover) {
    hover = false;
  }

  const hoverClass = hover ? ' hover:bg-muted ' : ' ';

  if (disabled) {
    className = className + ' opacity-30';
  }

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        if (!disabled) {
          onToggle();
        }
      }}
      className={
        'flex items-center rounded-sm text-sm cursor-pointer ' +
        hoverClass +
        className
      }
    >
      <div
        className={cn(
          'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
          checked
            ? 'bg-primary text-primary-foreground'
            : 'opacity-50 [&_svg]:invisible',
        )}
      >
        <CheckIcon className={cn('h-4 w-4')} />
      </div>
    </div>
  );
}
