import { X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useRef, useState } from 'react';

interface IProps {
  current: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  onlyEnter?: boolean;
  className?: string;
}

export default function MultiStringsInput({
  current,
  onChange,
  placeholder,
  disabled,
  onlyEnter,
  className,
}: IProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [kw, setKw] = useState<string>('');

  const remove = (item: string) => {
    const tmp: string[] = [];
    current.forEach((i) => {
      if (i !== item) {
        tmp.push(i);
      }
    });
    onChange([...tmp]);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    const input = inputRef.current;
    if (input) {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        //since we dont have undo, lets protect the user from deleting all the strings
        // if (input.value === "") {
        //   const newSelected = [...current];
        //   newSelected.pop();
        //   onChange(newSelected);
        // }
      } else if (
        e.key === 'Enter' ||
        (!onlyEnter && (e.key === ',' || e.key === ' '))
      ) {
        e.preventDefault();
        e.stopPropagation();
        if (input.value.trim() === '') return;
        setKw('');
        onChange([...current, input.value.trim()]);
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLDivElement>) => {
    const text = e.clipboardData.getData('Text');
    if (text.indexOf(',') > -1) {
      e.preventDefault();
      onChange([...current, ...text.split(',').map((s) => s.trim())]);
    }
  };

  return (
    <div className={className}>
      <div className="flex items-center border py-1 px-1 rounded-md text-sm min-h-10 flex-wrap">
        {current.map((item) => {
          return (
            <Badge
              key={item.replace(' ', '')}
              variant="secondary"
              className="m-1 text-nowrap"
            >
              {item}
              {!disabled && (
                <button
                  className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      remove(item);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={() => remove(item)}
                >
                  <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                </button>
              )}
            </Badge>
          );
        })}
        <input
          ref={inputRef}
          disabled={disabled}
          value={kw}
          onChange={(e) => {
            setKw(e.target.value);
          }}
          placeholder={disabled ? '' : placeholder}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          onBlur={() => {
            const _input = inputRef.current;
            if (_input) {
              if (_input.value.trim() === '') return;
              setKw('');
              onChange([...current, _input.value.trim()]);
            }
          }}
          className="ml-2 bg-transparent outline-none placeholder:text-muted-foreground flex-1"
        />
      </div>
    </div>
  );
}
