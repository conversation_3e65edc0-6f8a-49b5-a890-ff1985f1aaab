import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface IHorizontalDotsLoadingProps {
  className?: string;
}

export default function HorizontalDotsLoading({
  className = '',
}: IHorizontalDotsLoadingProps) {
  return (
    <div className={cn('flex items-center justify-center mx-6', className)}>
      <motion.div
        className="w-[6px] h-[6px] bg-white border border-zinc-500 rounded-full"
        animate={{
          scale: [1, 2, 2, 2, 1],
        }}
        transition={{
          duration: 1,
          ease: 'easeInOut',
          times: [0, 0.2, 0.5, 0.8, 1],
          repeat: Infinity,
          repeatDelay: 0,
        }}
      />
      <motion.div
        className="w-[6px] h-[6px] bg-white border border-zinc-500 rounded-full mx-[12px]"
        animate={{
          scale: [1, 2, 2, 2, 1],
        }}
        transition={{
          duration: 1,
          ease: 'easeInOut',
          times: [0, 0.2, 0.5, 0.8, 1],
          repeat: Infinity,
          repeatDelay: 0,
          delay: 0.2,
        }}
      />
      <motion.div
        className="w-[6px] h-[6px] bg-white border border-zinc-500 rounded-full"
        animate={{
          scale: [1, 2, 2, 2, 1],
        }}
        transition={{
          duration: 1,
          ease: 'easeInOut',
          times: [0, 0.2, 0.5, 0.8, 1],
          repeat: Infinity,
          repeatDelay: 0,
          delay: 0.4,
        }}
      />
    </div>
  );
}
