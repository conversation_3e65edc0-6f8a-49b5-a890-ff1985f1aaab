import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsLeftIcon,
  ChevronsRightIcon,
} from 'lucide-react';

interface IProps {
  from: number;
  numberOfResults: number;
  totNumberOfRows: number;
  updatePagination: (from: number, numberOfResults: number) => void;
  hideFirstLastPageButtons?: boolean;
  hideRowsPerPage?: boolean;
}

export default function TablePaginationFooter({
  from,
  numberOfResults,
  totNumberOfRows,
  updatePagination,
  hideFirstLastPageButtons,
  hideRowsPerPage,
}: IProps) {
  let currentPage = 1;
  let numberOfPages = 1;

  let atFirstPage = false;
  let atLastPage = false;

  if (numberOfResults > 0) {
    currentPage = Math.floor(from / numberOfResults) + 1;
    numberOfPages = Math.ceil(totNumberOfRows / numberOfResults);
  }

  if (from + numberOfResults >= totNumberOfRows) {
    atLastPage = true;
  }

  if (from < numberOfResults) {
    atFirstPage = true;
  }

  const updateNumberOfResPerPage = (value: string) => {
    updatePagination(0, parseInt(value));
  };

  const goToFirstPage = () => {
    updatePagination(0, numberOfResults);
  };

  const nextPage = () => {
    updatePagination(from + numberOfResults, numberOfResults);
  };

  const prevPage = () => {
    updatePagination(from - numberOfResults, numberOfResults);
  };

  const goToLastPage = () => {
    updatePagination(
      numberOfPages * numberOfResults - numberOfResults,
      numberOfResults,
    );
  };

  return (
    <div className="flex items-center">
      <div className="flex mr-6 text-sm text-muted-foreground">
        {from} - {from + numberOfResults} of {totNumberOfRows} row
        {totNumberOfRows > 1 ? 's' : ''}
      </div>
      <div className="flex-1" />
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          {!hideFirstLastPageButtons && (
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={goToFirstPage}
              disabled={atFirstPage}
            >
              <span className="sr-only">Go to first page</span>
              <ChevronsLeftIcon className="h-4 w-4" />
            </Button>
          )}

          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={prevPage}
            disabled={atFirstPage}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center justify-center text-sm font-medium">
          Page {currentPage} of {numberOfPages}
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={nextPage}
            disabled={atLastPage}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
          {!hideFirstLastPageButtons && (
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={goToLastPage}
              disabled={atLastPage}
            >
              <span className="sr-only">Go to last page</span>
              <ChevronsRightIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
      <div className="flex-1" />
      {!hideRowsPerPage && (
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium text-muted-foreground">
            Rows per page
          </p>
          <Select
            value={`${numberOfResults}`}
            onValueChange={updateNumberOfResPerPage}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={numberOfResults} />
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
}
