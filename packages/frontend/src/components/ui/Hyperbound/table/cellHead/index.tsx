import { cn } from '@/lib/utils';

interface IProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export default function TableCellHead({
  children,
  className,
  onClick,
}: IProps) {
  return (
    <td
      className={cn(
        'px-2 py-3 text-muted-foreground text-sm bg-gray-100',
        className,
      )}
      onClick={onClick}
    >
      {children}
    </td>
  );
}
