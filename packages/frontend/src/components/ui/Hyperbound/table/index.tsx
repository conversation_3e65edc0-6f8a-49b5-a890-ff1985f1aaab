import { cn } from '@/lib/utils';
import TableRow from './row';
import TableCell from './cell';
import TableCellHead from './cellHead';
import TableFooter from './footer';
import TableContent from './body';
import TablePaginationFooter from './paginationFooter';
import { useLayoutEffect, useRef } from 'react';
import useFixedWidthPanel from '@/hooks/ui/useFixedWidthPanel';

export {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
  TablePaginationFooter,
};

type TwoChildren = [React.ReactNode, React.ReactNode];

interface IProps {
  tableRef?: any;
  children: TwoChildren;
  className?: string;
  hideFooter?: boolean;
  fitToContent?: boolean;
  fillTillHtmlDivElementId?: string; //fits the table height to div
}

export default function Table({
  tableRef,
  children,
  className,
  hideFooter,
  fitToContent,
  fillTillHtmlDivElementId,
}: IProps) {
  const { panelRef: containerDiv } = useFixedWidthPanel();

  const tableTag = useRef<HTMLTableElement | null>(null);

  useLayoutEffect(() => {
    function updateSize() {
      if (tableTag.current && containerDiv.current && !fitToContent) {
        if (fillTillHtmlDivElementId) {
          //currently used by ..../Calls/Real/List/Import/GongModal/selectcalls
          const el = document.getElementById(fillTillHtmlDivElementId);
          if (el) {
            const rectEl = el.getBoundingClientRect();
            const rectDiv = containerDiv.current.getBoundingClientRect();
            const hContainer = rectEl.top - rectDiv.top - 20;
            containerDiv.current.style.height = `${hContainer}px`;
            const rectTable = tableTag.current.getBoundingClientRect();
            const footerHeight = rectEl.height;
            const h = hContainer - footerHeight;
            tableTag.current.style.height = `${h}px`;
          }
        } else {
          const rectDiv = containerDiv.current.getBoundingClientRect();
          const rectTable = tableTag.current.getBoundingClientRect();
          const footerHeight = rectDiv.height - rectTable.height;
          const h = window.innerHeight - rectTable.top - footerHeight - 20;
          tableTag.current.style.height = `${h}px`;
        }
      }
    }
    window.addEventListener('resize', updateSize);
    setTimeout(() => {
      updateSize();
    });
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return (
    <div
      ref={containerDiv}
      className={cn(
        'rounded-lg overflow-hidden border p-0 bg-white w-full relative',
        className,
      )}
    >
      <table
        ref={(e) => {
          tableTag.current = e || null;
          if (tableRef) {
            tableRef.current = e || null;
          }
        }}
        className={cn(
          'border-collapse w-full h-full block whitespace-nowrap overflow-auto',
        )}
      >
        {children[0]}
      </table>
      {children[1] && !hideFooter && children[1]}
    </div>
  );
}
