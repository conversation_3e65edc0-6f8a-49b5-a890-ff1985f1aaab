import { cn } from '@/lib/utils';

interface IProps {
  children: React.ReactNode;
  className?: string;
  colSpan?: number;
  title?: string;
  onClick?: () => void;
}

export default function TableCell({
  children,
  className,
  colSpan,
  title,
  onClick,
}: IProps) {
  return (
    <td
      className={cn('px-2 py-3 ', className)}
      colSpan={colSpan}
      title={title}
      onClick={onClick}
    >
      {children}
    </td>
  );
}
