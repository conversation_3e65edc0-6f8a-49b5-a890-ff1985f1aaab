import { cn } from '@/lib/utils';
import { CheckIcon } from 'lucide-react';

interface IProps {
  selected: boolean;
  onChange: () => void;
  label: string;
  className?: string;
}

export default function RadioButton({
  selected,
  onChange,
  label,
  className,
}: IProps) {
  return (
    <div
      className={cn('flex items-center cursor-pointer', className)}
      onClick={onChange}
    >
      <div
        className={cn(
          'mr-2 flex h-4 w-4 items-center justify-center  border border-primary rounded-full',
          selected
            ? 'bg-primary text-primary-foreground'
            : 'opacity-50 [&_svg]:invisible',
        )}
      >
        <CheckIcon className={cn('h-4 w-4')} />
      </div>
      <div className="text-sm">{label}</div>
    </div>
  );
}
