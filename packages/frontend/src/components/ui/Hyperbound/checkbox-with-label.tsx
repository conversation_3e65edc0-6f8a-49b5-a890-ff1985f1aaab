import { cn } from '@/lib/utils';
import { CheckIcon } from 'lucide-react';

interface IProps {
  children: React.ReactNode;
  checked: boolean;
  onToggle: () => void;
  hover?: boolean;
  className?: string;
}

export default function Checkbox({
  checked,
  onToggle,
  children,
  hover,
  className,
}: IProps) {
  if (!hover) {
    hover = false;
  }

  const hoverClass = hover ? ' hover:bg-muted ' : ' ';

  return (
    <div
      onClick={onToggle}
      className={
        'flex items-center rounded-sm text-sm cursor-pointer ' +
        hoverClass +
        className
      }
    >
      <div
        className={cn(
          'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
          checked
            ? 'bg-primary text-primary-foreground'
            : 'opacity-50 [&_svg]:invisible',
        )}
      >
        <CheckIcon className={cn('h-4 w-4')} />
      </div>
      {children}
    </div>
  );
}
