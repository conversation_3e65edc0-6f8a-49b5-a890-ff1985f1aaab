import { cn } from '@/lib/utils';
import { SearchIcon } from 'lucide-react';
import { useRef, useState } from 'react';

interface IProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
}

export default function SearchBox({
  value,
  onChange,
  className,
  placeholder,
}: IProps) {
  const [hasFocus, setHasFocus] = useState(false);
  const inputeRef = useRef<HTMLInputElement>(null);

  return (
    <div
      className={cn(
        'flex items-center rounded-lg bg-white border px-2 py-2',
        className,
        {
          'border-gray': !hasFocus,
          'border-black': hasFocus,
        },
      )}
    >
      <div
        className={cn('mr-2', {
          'text-muted-foreground': !hasFocus,
        })}
      >
        <SearchIcon size={16} />
      </div>
      <input
        ref={inputeRef}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="m-0 p-0 outline-none w-full "
        placeholder={placeholder || 'Search...'}
        onFocus={() => {
          setHasFocus(true);
          inputeRef.current?.select();
        }}
        onBlur={() => setHasFocus(false)}
      />
    </div>
  );
}
