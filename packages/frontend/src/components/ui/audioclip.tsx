import { PlayIcon } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { Button } from './button';

const AudioPreviewComponent = ({
  audioSrc,
  currentAudio,
  setCurrentAudio,
}: {
  audioSrc: string;
  currentAudio: HTMLAudioElement | null;
  setCurrentAudio: (audio: HTMLAudioElement) => void;
}) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const handleClick = () => {
    if (audioRef.current && audioSrc) {
      // Stop the currently playing audio if it's different
      if (currentAudio && currentAudio !== audioRef.current) {
        currentAudio.pause();
        currentAudio.currentTime = 0; // Reset the audio
      }

      // Play the new audio
      setCurrentAudio(audioRef.current);
      audioRef.current.play();
    } else {
      console.error('Audio source not found');
    }
  };

  return (
    <Button
      variant={'ghost'}
      className="flex items-center gap-2 cursor-pointer"
      onClick={handleClick}
    >
      <div className="text-zinc-500 text-sm font-normal leading-tight">
        Preview
      </div>
      <PlayIcon className="w-4 h-4 text-zinc-500" />
      <audio ref={audioRef} src={audioSrc} preload="auto" />
    </Button>
  );
};

export default AudioPreviewComponent;
