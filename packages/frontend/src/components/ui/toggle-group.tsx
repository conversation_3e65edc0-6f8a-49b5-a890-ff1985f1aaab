import * as React from 'react';
import * as ToggleGroupPrimitives from '@radix-ui/react-toggle-group';
import { cn } from '@/lib/utils';

const ToggleGroup = React.forwardRef<
  React.ElementRef<typeof ToggleGroupPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitives.Root>
>(({ className, ...props }, ref) => (
  <ToggleGroupPrimitives.Root
    ref={ref}
    className={cn('flex flex-row flex-1', className)}
    {...props}
  />
));
ToggleGroup.displayName = ToggleGroupPrimitives.Root.displayName;

const ToggleGroupItem = React.forwardRef<
  React.ElementRef<typeof ToggleGroupPrimitives.Item>,
  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitives.Item> & {
    selected: boolean;
  }
>(({ className, ...props }, ref) => {
  return (
    <ToggleGroupPrimitives.Item
      ref={ref}
      className={cn(
        'flex flex-1 items-center justify-center py-2 text-sm first:rounded-l last:rounded-r transition-all hover:bg-secondary focus:bg-primary focus:text-white',
        props.selected ? 'bg-primary text-white' : 'bg-white text-black',
        className,
      )}
      {...props}
    />
  );
});
ToggleGroupItem.displayName = ToggleGroupPrimitives.Item.displayName;

export { ToggleGroup, ToggleGroupItem };
