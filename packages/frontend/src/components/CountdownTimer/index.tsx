'use client';

import useCountdown from '@/hooks/useCountdown';
import { JSX } from 'react';

interface ICountdownTimerProps {
  targetDate: string | number | Date;
  expiredMessage?: string;
  abbreviatedSuffix?: boolean;
  onExpire?: () => Promise<void>;
  include?: {
    days?: boolean;
    hours?: boolean;
    minutes?: boolean;
    seconds?: boolean;
  };
  under24hElement?: JSX.Element;
  elementClassName?: string;
  valClassName?: string;
  prefixClassName?: string;
  suffixClassName?: string;
}
export default function CountdownTimer({
  targetDate,
  expiredMessage,
  abbreviatedSuffix = false,
  onExpire,
  include = {
    days: true,
    hours: true,
    minutes: true,
    seconds: true,
  },
  under24hElement,
  elementClassName,
  valClassName,
  prefixClassName,
  suffixClassName,
}: ICountdownTimerProps) {
  const [days, hours, minutes, seconds] = useCountdown(targetDate, onExpire);

  if (days + hours + minutes + seconds <= 0) {
    return <>{expiredMessage || 'Expired'}</>;
  }

  const createE = (
    val: number | string,
    separator: string,
    prefix: string,
    suffix: string,
  ) => {
    return (
      <span className={elementClassName}>
        {separator}
        <span className={prefixClassName}>{prefix}</span>
        <span className={valClassName}>{val}</span>
        <span className={suffixClassName}>{suffix}</span>
      </span>
    );
  };

  if (under24hElement && days <= 0) {
    return under24hElement;
  }

  if (days <= 0) {
    include.days = false;
  }
  if (hours <= 0) {
    include.hours = false;
  }
  if (minutes <= 0) {
    include.minutes = false;
  }
  if (seconds <= 0) {
    include.seconds = false;
  }

  let startingE: 'D' | 'H' | 'M' | 'S' | null = null;
  if (!startingE && include.days) {
    startingE = 'D';
  }
  if (!startingE && include.hours) {
    startingE = 'H';
  }
  if (!startingE && include.minutes) {
    startingE = 'M';
  }
  if (!startingE && include.seconds) {
    startingE = 'S';
  }

  return (
    <>
      {include.days
        ? createE(days, '', '', abbreviatedSuffix ? 'd' : ' days')
        : ''}
      {include.hours
        ? createE(
            hours,
            startingE === 'H' ? '' : ', ',
            '',
            abbreviatedSuffix ? 'h' : ' hours',
          )
        : ''}
      {include.minutes
        ? createE(
            minutes,
            startingE === 'M' ? '' : ', ',
            '',
            abbreviatedSuffix ? 'm' : ' minutes',
          )
        : ''}
      {include.seconds
        ? createE(
            seconds,
            startingE === 'S' ? '' : ', ',
            '',
            abbreviatedSuffix ? 's' : ' seconds',
          )
        : ''}
    </>
  );
}
