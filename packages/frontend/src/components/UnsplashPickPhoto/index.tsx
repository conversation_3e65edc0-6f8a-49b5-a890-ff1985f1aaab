import DialogFullScreen from '../ui/Hyperbound/DialogFullScreen';
import Header from '../ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '../ui/Hyperbound/DialogFullScreen/ScrollableContent';
import UnsplashPickPhotoPanel from './UnsplashPickPhotoPanel';

interface IProps {
  open: boolean;
  onSave: (imgUrl: string, blur_hash: string) => void;
  onCancel: () => void;
}

export default function UnsplashPickPhoto({ open, onSave, onCancel }: IProps) {
  return (
    <DialogFullScreen open={open} onOpenChange={onCancel} width="w-[60%]">
      <Header title="Select image" onClose={onCancel} className="px-4 pt-2  " />

      <ScrollableContent className="mt-3">
        <UnsplashPickPhotoPanel onSave={onSave} />
      </ScrollableContent>
    </DialogFullScreen>
  );
}
