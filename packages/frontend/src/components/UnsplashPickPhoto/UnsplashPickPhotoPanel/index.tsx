import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Loader2Icon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import UnsplashClient from '@/lib/UnsplashClient';

interface IProps {
  onSave: (imgUrl: string, blur_hash: string) => void;
  className?: string;
  selectedUrl?: string;
}

export default function UnsplashPickPhotoPanel({
  className,
  onSave,
  selectedUrl,
}: IProps) {
  const [search, setSearch] = useState<string>('');
  const [photos, setPhotos] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(true);
  const page = useRef<number>(1);

  const searchPhoto = async (queue = false, st: string = '') => {
    let searchString = 'positivity inspirational sales coaching';
    if (st) {
      searchString = st;
    }

    let r;
    try {
      r = await UnsplashClient.get(
        'search/photos/?client_id=gZtRVe6ExunDx04zR_h0H0yXySm7q0RQtgw9cTb3LYw&page=' +
          page.current +
          '&orientation=landscape&content_filter=high&per_page=24&query=' +
          encodeURIComponent(searchString),
      );
    } catch (e) {
      console.log(e);
    }

    if (r) {
      if (r.data && r.data.results) {
        if (queue) {
          setPhotos([...photos, ...r.data.results]);
        } else {
          setPhotos(r.data.results);
        }
      } else {
        setPhotos([]);
      }
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  useEffect(() => {
    if (photos.length == 0) {
      searchPhoto();
    }
  }, []);

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const updateSearch = (s: string) => {
    setSearch(s);
    page.current = 1;
    if (s == '' && timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      searchPhoto(false);
    } else {
      timeoutRef.current = setTimeout(async () => {
        searchPhoto(false, s);
      }, 1000);
    }
  };

  const loadMore = () => {
    setIsLoadingMore(true);
    page.current = page.current + 1;
    searchPhoto(true, search);
  };

  const save = (imgUrl: string, blur_hash: string) => {
    if (onSave) {
      onSave(imgUrl, blur_hash);
    }
  };

  return (
    <div className={className}>
      <div className="p-3 pb-0">
        <Input
          value={search}
          onChange={(e) => {
            updateSearch(e.target.value);
          }}
          placeholder="Search for an image..."
        />
      </div>

      <div className="mt-3">
        <div className={cn('p-3', { 'opacity-80': isLoading })}>
          {isLoading && (
            <div className="text-sm w-full flex justify-center pt-4">
              <Loader2Icon className="animate-spin" size={18} />
            </div>
          )}
          {photos.length == 0 && !isLoading && (
            <div className="text-muted-foreground text-sm w-full text-center pt-4">
              No photo found.
            </div>
          )}
          {photos.length > 0 && !isLoading && (
            <div>
              <div className="grid grid-cols-3 gap-2">
                {photos.map((p: any) => {
                  const imgUrl = p.urls.small_s3;
                  const author = p.user;
                  const isSelected = selectedUrl == imgUrl;
                  return (
                    <div key={p.id} className="">
                      <div
                        onClick={() => {
                          save(imgUrl, p.blur_hash);
                        }}
                        className={cn(
                          `rounded overflow-hidden w-[100%] h-[140px] cursor-pointer border-2`,
                          {
                            'border-teal-400': isSelected,
                            'border-white': !isSelected,
                          },
                        )}
                        style={{
                          backgroundImage: `url('${imgUrl}')`,
                          backgroundRepeat: 'no-repeat',
                          backgroundPosition: 'center',
                        }}
                      ></div>
                      <div className="text-muted-foreground text-xs mt-1">
                        by{' '}
                        <a
                          href={author.portfolio_url}
                          target="_new"
                          className="underline"
                        >
                          {author.name}
                        </a>
                      </div>
                    </div>
                  );
                })}
              </div>
              <div className="flex justify-center w-full">
                <div className="flex-1" />
                <div
                  className="my-2 text-muted-foreground text-sm cursor-pointer hover:text-black underline"
                  onClick={loadMore}
                >
                  Load more
                </div>
                <div
                  className={cn('mt-2 ml-2', {
                    invisible: !isLoadingMore,
                  })}
                >
                  <Loader2Icon className="animate-spin" size={18} />
                </div>
                <div className="flex-1" />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
