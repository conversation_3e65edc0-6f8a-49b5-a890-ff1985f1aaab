import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import {
  CALL_TYPE_TO_ICON,
  CHALLENGE_BOT_VAPI_ID,
} from '@/common/Sidebar/OldSidebar';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useMaintenance from '@/hooks/useMaintenance';
import AgentService from '@/lib/Agent';
import {
  AgentCallType,
  AgentDto,
  AgentStatus,
  AnyAgentDto,
} from '@/lib/Agent/types';
import CallService from '@/lib/Call';
import { RoleEnum } from '@/lib/User/types';
import { cn } from '@/lib/utils';
import { VAPI_EVENTS, VapiManager } from '@/lib/vapi-config';
import Analytics from '@/system/Analytics';
import { BuyerEvents } from '@/system/Analytics/events/BuyerEvents';
import { DemoInboundFormEvents } from '@/system/Analytics/events/DemoInboundFormEvents';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { TooltipArrow } from '@radix-ui/react-tooltip';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AnimatePresence, motion } from 'framer-motion';
import _ from 'lodash';
import {
  AlertTriangleIcon,
  BrainIcon,
  LoaderIcon,
  PhoneIcon,
  PhoneIncomingIcon,
  PhoneOffIcon,
} from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { Id, ToastContainer, toast } from 'react-toastify';
import DemoWelcomeModal from '../DemoWelcomeModal';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '../ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Skeleton } from '../ui/skeleton';
import { Switch } from '../ui/switch';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import animationStyles from './animation.module.css';
import Vapi from '@vapi-ai/web';
import LinksManager from '@/lib/linksManager';
import useUserSession from '@/hooks/useUserSession';
import CallInstructions from '../CallInstructions';
import AgentAvatar from '../Avatars/Agent';
import { CallDto, CallMetadata } from '@/lib/Call/types';

interface IColdCallSimulationCardProps {
  agent: AnyAgentDto;
  isLoadingAgent: boolean;
  latestCall: CallDto | null;
  callOngoing: boolean;
  setCallOngoing: (callOngoing: boolean) => void;
  setLatestCall: (latestCall: CallDto | null) => void;
  isDemoAgent: boolean;
  location: string;
  onCallEnds?: () => void;
  compactView?: boolean;
  callBlitzSessionId?: number;
}

function ColdCallSimulationCard({
  agent,
  isLoadingAgent,
  latestCall,
  callOngoing,
  setCallOngoing,
  setLatestCall,
  isDemoAgent,
  location,
  onCallEnds,
  compactView,
  callBlitzSessionId,
}: IColdCallSimulationCardProps) {
  const authInfo = useAuthInfo();
  const queryClient = useQueryClient();
  const pathname = usePathname();
  const [loading, setLoading] = useState<boolean>(false);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [warningModalOpen, setWarningModalOpen] = useState<boolean>(false);
  const [tooltipOpen, setTooltipOpen] = useState<boolean>(false);
  const { isInIframe } = useUserSession();

  const router = useRouter();
  const errorToastId = useRef<Id | null>(null);
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const { data: maintenance } = useMaintenance(!authInfo.isLoggedIn);
  const org = useActiveOrg();

  const [botIsTalking, setBotIsTalking] = useState<boolean>(false);
  const [botIsThinking, setBotIsThinking] = useState<boolean>(false);
  const [botVolume, setBotVolume] = useState<number>(0);

  const [audioPickUp, setAudioPickUp] = useState<HTMLAudioElement>();
  const [audioRinging, setAudioRinging] = useState<HTMLAudioElement>();
  const [audioEnded, setAudioEnded] = useState<HTMLAudioElement>();

  const [vapi, setVapi] = useState<Vapi>();

  const initVapiClient = async () => {
    if (!vapi) {
      setVapi(await VapiManager.getClient(!authInfo.isLoggedIn, agent.vapiId));
    }
  };

  initVapiClient();

  useEffect(() => {
    queryClient.refetchQueries({
      queryKey: ['maintenance'],
    });
    setAudioPickUp(new Audio(`/audio/iphone-pickup.mp3`));
    setAudioRinging(new Audio(`/audio/start-call.mp3`));
    setAudioEnded(new Audio('/audio/call-end-tone.mp3'));

    return () => {
      clearMonitorUserMic();
      if (audioRinging) {
        audioRinging.pause();
      }
    };
  }, []);

  useEffect(() => {
    if (!vapi) {
      return;
    }

    vapi.on(VAPI_EVENTS.SPEECH_START, () => {
      //console.log("Speech has started");
      setBotIsTalking(true);
    });

    vapi.on(VAPI_EVENTS.SPEECH_END, () => {
      //console.log("Speech has ended");
      setBotIsTalking(false);
    });

    vapi.on(VAPI_EVENTS.CALL_START, () => {
      //console.log("Call has started");
      if (audioRinging) {
        audioRinging.loop = false;
        audioRinging.pause();
        audioRinging.currentTime = 0;
      }
      if (audioPickUp) {
        audioPickUp.play();
      }
    });

    vapi.on(VAPI_EVENTS.CALL_END, () => {
      //console.log("Call has stopped");
    });

    vapi.on(VAPI_EVENTS.VOLUME_LEVEL, (volume) => {
      setBotVolume(volume);
      //console.log(`Assistant volume level: ${volume}`);
    });

    vapi.on(VAPI_EVENTS.ERROR, (e) => {
      if (audioRinging) {
        audioRinging.loop = false;
        audioRinging.pause();
        audioRinging.currentTime = 0;
      }
      console.error('VAPI ERROR LISTENER', e);
      if (!toast.isActive(errorToastId.current as Id) && callOngoing) {
        errorToastId.current = toast.error(
          'There was an error with the recording. Please try again.',
        );
      }
    });

    vapi.on(VAPI_EVENTS.MESSAGE, (message) => {
      if (message.role != 'user') {
        //it should be assistant....but to avoid any confusion
        setBotIsThinking(false);
      } else if (
        message.type == 'speech-update' &&
        message.role == 'user' &&
        message.status == 'stopped'
      ) {
        setBotIsThinking(true);
      }

      //for focus, block assistant after first line
      if (agent.callType == AgentCallType.FOCUS) {
        if (
          message.role == 'assistant' &&
          message.type == 'speech-update' &&
          message.status == 'stopped'
        ) {
          vapi.send({ type: 'control', control: 'mute-assistant' });
        }
      }

      if (
        message.transcriptType === 'final' &&
        message.role === 'assistant' &&
        (message.transcript.includes('Goodbye') ||
          message.transcript.includes('goodbye') ||
          message.transcript.includes('Revoir') ||
          message.transcript.includes('revoir') ||
          message.transcript.includes('Farvel') ||
          message.transcript.includes('farvel') ||
          message.transcript.includes('Adjö') ||
          message.transcript.includes('adjö') ||
          message.transcript.includes('Adiós') ||
          message.transcript.includes('adiós') ||
          message.transcript.toLowerCase().includes("i'll connect you") ||
          message.transcript.toLowerCase().includes('i will connect you') ||
          message.transcript.toLowerCase().includes('connecting you now'))
      ) {
        stopCall();
      } else {
        monitorUserMic();
      }
    });
  }, [latestCall, vapi]);

  // microphone monitor --------
  const timeoutId = useRef<number | undefined>(undefined);
  const micErrorFired = useRef<boolean>(false);

  const clearMonitorUserMic = () => {
    if (timeoutId.current) {
      clearTimeout(timeoutId.current);
    }
  };

  const monitorUserMic = () => {
    clearMonitorUserMic();
    if (!micErrorFired.current) {
      micErrorFired.current = true;
      timeoutId.current = window.setTimeout(() => {
        errorToastId.current = toast.error(
          'We cannot hear you. Please check your microphone.',
        );
      }, 20000);
    }
  };
  //-------------- end mic monitor

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
    onSuccess: (agent) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error editing the buyer. Please try again.',
        );
      }
    },
  });

  useEffect(() => {
    setTimeout(() => {
      setTooltipOpen(true);
    }, 500);
  }, []);

  const createCallMutation = useMutation({
    mutationFn: CallService.createCall,
    onSuccess: (call) => {
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      Analytics.track(BuyerEvents.CALL_INITIALIZED_SUCCESS, {
        agentId: agent.id,
        callId: call.id,
        isDemo: false,
        location,
      });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error creating a call in the db');
        // errorToastId.current = toast.error(
        //   "There was an error creating the buyer bot. Please try again."
        // );
      }
      Analytics.track(BuyerEvents.CALL_INITIALIZED_ERROR, {
        agentId: agent.id,
        err: 'Demo call initialization error',
        isDemo: false,
        location,
      });
    },
  });

  const createDemoCallMutation = useMutation({
    mutationFn: CallService.createDemoCall,
    onSuccess: (call) => {
      queryClient.invalidateQueries({ queryKey: ['demoCalls'] });
      Analytics.track(BuyerEvents.CALL_INITIALIZED_SUCCESS, {
        agentId: agent.id,
        callId: call.id,
        isDemo: true,
        location,
      });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error creating a demo call in the db');
        // errorToastId.current = toast.error(
        //   "There was an error creating the buyer bot. Please try again."
        // );
      }
      Analytics.track(BuyerEvents.CALL_INITIALIZED_ERROR, {
        agentId: agent.id,
        err: 'Demo call initialization error',
        isDemo: true,
        location,
      });
    },
  });

  const startCall = () => {
    if (agent.vapiId === 'cdcf2d2b-dd34-4c84-b723-8aab370fa93a') {
      window.location.href = 'tel:******-949-6522';
      return;
    }

    if (audioRinging) {
      audioRinging.loop = true;
      audioRinging.play();
    }

    //console.log("Starting Vapi", agent.vapiId);
    Analytics.track(BuyerEvents.CALL_STARTED, {
      agentId: agent.id,
      location,
    });
    setWarningModalOpen(false);
    setLoading(true);
    try {
      vapi?.start?.(agent.vapiId)?.then?.((vapiCall) => {
        console.log('MY VAPI CALL', vapiCall);
        if (vapiCall) {
          // Create call in our backend
          if (authInfo.isLoggedIn && !isDemoAgent) {
            createCallMutation.mutate({
              vapiId: vapiCall?.id,
              assistantId: vapiCall?.assistantId as string,
              createdAt: vapiCall?.createdAt,
              updatedAt: vapiCall?.updatedAt,
              type: vapiCall?.type as string,
              // @ts-ignore-next-line
              status: vapiCall?.status,
              vapiMetadata: {
                ...vapiCall,
                summary: '', // Add empty summary since it's required
              } as CallMetadata,
              callBlitzSessionId,
            });
          } else {
            createDemoCallMutation.mutate({
              vapiId: vapiCall?.id,
              assistantId: vapiCall?.assistantId as string,
              createdAt: vapiCall?.createdAt,
              updatedAt: vapiCall?.updatedAt,
              type: vapiCall?.type as string,
              // @ts-ignore-next-line
              status: vapiCall?.status,
              demoInboundFormResponseId: hbDemoInboundForm?.id,
              vapiMetadata: {
                ...vapiCall,
                summary: '', // Add empty summary since it's required
              } as CallMetadata,
            });
          }

          setCallOngoing(true);
          setLatestCall(vapiCall as unknown as CallDto);
        } else {
          Analytics.track(BuyerEvents.CALL_STARTED_VAPI_ERROR, {
            agentId: agent.id,
            location,
          });
          throw new Error('Vapi call not started');
        }
      });
    } catch (err) {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'Recording could not be started. Try again later.',
        );
      }
      Analytics.track(BuyerEvents.CALL_STARTED_VAPI_ERROR, {
        agentId: agent.id,
        err: 'Vapi error',
        location,
      });
      console.log(err);
    }
    setLoading(false);
  };

  const [callCompleted, setCallCompleted] = useState<boolean>(false);
  const stopCall = async () => {
    clearMonitorUserMic();
    setLoading(true);
    if (audioRinging) {
      audioRinging.loop = false;
      audioRinging.pause();
      audioRinging.currentTime = 0;
    }
    vapi?.stop();
    if (audioEnded) {
      audioEnded.play();
    }
    Analytics.track(BuyerEvents.CALL_STOPPED, {
      agentId: agent.id,
      callId: latestCall?.id,
      isDemo: isDemoAgent,
      location,
    });
    if (onCallEnds) {
      setCallCompleted(true);
      onCallEnds();
    } else {
      if (agent?.vapiId === CHALLENGE_BOT_VAPI_ID) {
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ['leaderboard'],
          });
          queryClient.invalidateQueries({
            queryKey: [process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY],
          });
          router.push(`/leaderboard?id=${hbDemoInboundForm?.id}`);
        }, 14000);
      } else {
        setTimeout(() => {
          router.push(
            `${
              pathname.includes('/embed') ? '/embed/' : ''
            }${LinksManager.trainingCalls(String(latestCall?.id))}`,
          );
        }, 4000);
      }
    }
  };

  const [tWidth, setTWidth] = useState<number>(128);
  const [tOpacity, setTOpacity] = useState<number>(0);

  useEffect(() => {
    if (botIsThinking) {
      setTOpacity(1);
    } else if (botIsTalking) {
      setTOpacity(1);
      setTWidth(128 + 6 * botVolume);
    } else {
      setTWidth(128);
      setTOpacity(0);
    }
  }, [botIsTalking, botIsThinking, botVolume]);

  const Icon =
    CALL_TYPE_TO_ICON?.[agent?.callType as keyof typeof CALL_TYPE_TO_ICON]
      ?.Icon;

  return (
    <div>
      <AnimatePresence>
        <motion.div
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0.9 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="w-full md:w-[450px] shadow-md">
            <CardHeader>
              <CallInstructions description={agent?.description} />
            </CardHeader>
            <CardContent className="flex items-center justify-center">
              <div className="flex flex-col items-center text-center">
                <div className={'relative mb-4 '}>
                  <div
                    className={animationStyles.animateCircle}
                    style={{
                      width: tWidth + 'px',
                      height: tWidth + 'px',
                      opacity: tOpacity,
                    }}
                  ></div>

                  <AgentAvatar
                    className={'w-[128px] h-[128px]'}
                    agent={agent}
                  />
                  {!(updateAgentMutation.isPending || !agent) && (
                    <div
                      className={cn(
                        'rounded-full p-1 w-6 h-6 border-white border-[3px] absolute bottom-1 right-2',
                        {
                          'bg-green-500': agent.status === AgentStatus.ACTIVE,
                          'bg-gray-500': agent.status === AgentStatus.INACTIVE,
                        },
                      )}
                    />
                  )}
                </div>
                <p className="text-lg">
                  {agent.firstName} {agent.lastName}
                </p>
                <p className="text-md text-muted-foreground">
                  {agent.jobTitle} @ {agent.companyName}
                </p>
                <div>
                  {(agent.emotionalState || agent.gender) && (
                    <div
                      className={cn('flex mt-2', {
                        'space-x-1': agent.emotionalState && agent.gender,
                        // (emotionalState && salesMethodology) ||
                        // (emotionalState && gender) ||
                        // (gender && salesMethodology),
                      })}
                    >
                      {agent.callType && (
                        <Badge className="mt-1" variant="secondary">
                          {Icon && <Icon className="mr-1 h-3 w-3" />}
                          {CALL_TYPE_OPTIONS.find(
                            (item) => item.value === agent.callType,
                          )?.label ||
                            (agent.callType === 'focus'
                              ? 'Focus Call'
                              : agent.callType)}
                        </Badge>
                      )}
                      {agent?.callType !== 'focus' && (
                        <>
                          {agent.emotionalState ? (
                            <Badge
                              className="mt-1"
                              variant={
                                authInfo?.isLoggedIn ? 'default' : 'outline'
                              }
                            >
                              <BrainIcon className="w-3 h-3 mr-1" />{' '}
                              {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                                (item) => item.value === agent.emotionalState,
                              )?.label ||
                                agent.emotionalState ||
                                ''}
                            </Badge>
                          ) : (
                            <Skeleton className={cn('w-16 h-6 mr-1')} />
                          )}
                        </>
                      )}
                      {agent.bookRate && (
                        <Badge variant="default">
                          Book Rate:{' '}
                          {Number(agent?.bookRate).toLocaleString('en-US', {
                            style: 'percent',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 1,
                          })}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
                {agent?.openerLine &&
                  agent.callType === AgentCallType.FOCUS && (
                    <p className="mt-4">{agent.openerLine}</p>
                  )}
                {(agent as AgentDto)?.status &&
                  !isDemoAgent &&
                  authInfo?.accessHelper?.isAtLeastRole(
                    org?.orgId as string,
                    RoleEnum.ADMIN,
                  ) && (
                    <div className="flex space-x-2 mt-4">
                      {(agent as AgentDto)?.status !== AgentStatus.DRAFT &&
                      !compactView ? (
                        <div className="flex items-center space-x-2">
                          <Switch
                            disabled={
                              isLoadingAgent || updateAgentMutation.isPending
                            }
                            checked={
                              (agent as AgentDto)?.status === AgentStatus.ACTIVE
                            }
                            onCheckedChange={(value) => {
                              updateAgentMutation.mutate({
                                id: agent?.id,
                                status: value
                                  ? AgentStatus.ACTIVE
                                  : AgentStatus.INACTIVE,
                              });
                            }}
                            id="status-toggle"
                          />
                          {updateAgentMutation.isPending || isLoadingAgent ? (
                            <Skeleton className="w-16 h-[22px] rounded-md" />
                          ) : (
                            <Badge
                              variant="default"
                              className={cn({
                                'bg-green-600':
                                  (agent as AgentDto)?.status ===
                                  AgentStatus.ACTIVE,
                                'bg-red-600':
                                  (agent as AgentDto)?.status ===
                                  AgentStatus.INACTIVE,
                              })}
                            >
                              {_.capitalize(
                                (agent as AgentDto)?.status?.toLowerCase(),
                              )}
                            </Badge>
                          )}
                        </div>
                      ) : (
                        <Badge variant="default">
                          {_.capitalize(
                            (agent as AgentDto)?.status?.toLowerCase(),
                          )}
                        </Badge>
                      )}
                    </div>
                  )}
              </div>
            </CardContent>
            <CardFooter>
              <div className="flex w-full justify-between relative">
                {/* <motion.div
                  className="w-full"
                  whileHover={{
                    translateY: !callOngoing ? -2 : 0,
                    transition: { duration: !callOngoing ? 0.3 : 0 },
                  }}
                > */}
                <TooltipProvider
                  disableHoverableContent={
                    loading ||
                    isLoadingAgent ||
                    updateAgentMutation.isPending ||
                    (agent as AgentDto)?.status === AgentStatus.INACTIVE
                  }
                  delayDuration={200}
                >
                  <Tooltip open={tooltipOpen} onOpenChange={setTooltipOpen}>
                    <TooltipTrigger asChild>
                      <span tabIndex={0} className="w-full">
                        <Button
                          disabled={
                            callCompleted ||
                            loading ||
                            isLoadingAgent ||
                            updateAgentMutation.isPending ||
                            (agent as AgentDto)?.status ===
                              AgentStatus.INACTIVE ||
                            agent.vapiId === CHALLENGE_BOT_VAPI_ID
                          }
                          size={'lg'}
                          variant={'default'}
                          className={cn(
                            'w-full text-white hover:text-white shadow-md text-base h-[52px] rounded-2xl cursor-pointer',
                            {
                              'bg-red-500 hover:bg-red-600': callOngoing,
                              'drop-shadow-2xl hover:opacity-80 transition-opacity duration-200 border border-white/50':
                                !callOngoing,
                            },
                          )}
                          style={
                            !callOngoing
                              ? {
                                  background:
                                    agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                                      ? 'linear-gradient(to right, #000000, #5189CE, #A168A2)'
                                      : 'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                                  backgroundImage:
                                    agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                                      ? '-webkit-linear-gradient(to right, #000000, #5189CE, #A168A2)'
                                      : '-webkit-linear-gradient(to bottom, #000000, #5189CE, #A168A2)',
                                }
                              : {}
                          }
                          onClick={
                            callOngoing
                              ? stopCall
                              : () => {
                                  Analytics.track(
                                    BuyerEvents.START_CALL_CLICKED,
                                    {
                                      agentId: agent.id,
                                      location,
                                    },
                                  );
                                  if (hbDemoInboundForm?.blacklisted) {
                                    if (
                                      !toast.isActive(
                                        errorToastId.current as Id,
                                      )
                                    ) {
                                      errorToastId.current = toast.error(
                                        'You have been blocked for inappropriate behavior. Please contact the Hyperbound <NAME_EMAIL>.',
                                      );
                                    }
                                    Analytics.track(
                                      BuyerEvents.START_CALL_CLICKED_BLACKLISTED,
                                      {
                                        agentId: agent.id,
                                        location,
                                      },
                                    );
                                    return;
                                  }

                                  if (agent.vapiId === CHALLENGE_BOT_VAPI_ID) {
                                    if (
                                      hbDemoInboundForm?.presClubChallengeRegistered
                                    ) {
                                      if (
                                        !queryClient.getQueryData([
                                          'warningModalOpened',
                                        ])
                                      ) {
                                        setWarningModalOpen(true);
                                      } else {
                                        startCall();
                                      }
                                    } else {
                                      Analytics.track(
                                        DemoInboundFormEvents.OPENED,
                                        {
                                          invite:
                                            queryClient.getQueryData([
                                              'hbInvite',
                                            ]) || null,
                                          from: 'individual_buyer_page',
                                          agentId: agent.id,
                                          location,
                                        },
                                      );
                                      setModalOpen(true);
                                    }
                                  } else {
                                    if (
                                      !_.isEmpty(hbDemoInboundForm) ||
                                      (authInfo.isLoggedIn && !isDemoAgent)
                                    ) {
                                      if (
                                        !queryClient.getQueryData([
                                          'warningModalOpened',
                                        ])
                                      ) {
                                        setWarningModalOpen(true);
                                      } else {
                                        startCall();
                                      }
                                    } else {
                                      Analytics.track(
                                        DemoInboundFormEvents.OPENED,
                                        {
                                          invite:
                                            queryClient.getQueryData([
                                              'hbInvite',
                                            ]) || null,
                                          from: 'individual_buyer_page',
                                          agentId: agent.id,
                                          location,
                                        },
                                      );
                                      setModalOpen(true);
                                    }
                                  }
                                }
                          }
                        >
                          {loading ? (
                            callOngoing ? (
                              <LoaderIcon className="mr-2 h-5 w-5 animate-pulse" />
                            ) : (
                              <PhoneIncomingIcon className="mr-2 h-5 w-5 animate-pulse" />
                            )
                          ) : callOngoing ? (
                            <PhoneOffIcon className="mr-2 h-5 w-5" />
                          ) : (
                            <PhoneIcon className="mr-2 h-5 w-5" />
                          )}
                          <span>
                            {agent.vapiId === CHALLENGE_BOT_VAPI_ID
                              ? 'Competition has ended'
                              : loading
                                ? callOngoing
                                  ? 'Ending & scoring your call'
                                  : callCompleted
                                    ? `Call Ended`
                                    : `Calling ${agent?.firstName}`
                                : callOngoing
                                  ? 'End call'
                                  : `${
                                      agent.vapiId === CHALLENGE_BOT_VAPI_ID &&
                                      !hbDemoInboundForm?.presClubChallengeRegistered
                                        ? 'Login to '
                                        : ''
                                    }Start Call${
                                      agent.vapiId === CHALLENGE_BOT_VAPI_ID &&
                                      !!hbDemoInboundForm?.id &&
                                      hbDemoInboundForm?.presClubChallengeRegistered
                                        ? ` (${
                                            hbDemoInboundForm?.challengeNumTriesLeft
                                          } ${
                                            hbDemoInboundForm?.challengeNumTriesLeft ===
                                            1
                                              ? 'try'
                                              : 'tries'
                                          } left)`
                                        : ''
                                    }`}
                          </span>
                        </Button>
                      </span>
                    </TooltipTrigger>
                    <TooltipContent
                      hidden={
                        callOngoing ||
                        (agent.vapiId === CHALLENGE_BOT_VAPI_ID &&
                          hbDemoInboundForm?.challengeNumTriesLeft === 0)
                      }
                      side="bottom"
                      className="w-96 text-lg bg-gray-800 text-center rounded-xl"
                    >
                      <TooltipArrow />
                      {agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                        ? '* Please read instructions on the left before starting a call'
                        : 'Click here to start your call'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                {/* </motion.div> */}

                <Dialog
                  open={warningModalOpen}
                  onOpenChange={setWarningModalOpen}
                >
                  <DialogContent className="close-btn">
                    <DialogHeader>
                      <DialogTitle className="flex items-center">
                        {maintenance?.disableCall && (
                          <AlertTriangleIcon className="mr-2 text-yellow-500" />
                        )}
                        {!maintenance?.disableCall
                          ? "Let's get started!"
                          : 'High volume alert, please check back in 1 hour'}
                      </DialogTitle>
                      <DialogDescription className="py-4">
                        {maintenance?.callWarningMessage ||
                          `At times, the bot may be unresponsive, or have unusual lag times. `}
                        We are always working to improve the experience!{' '}
                        {!isInIframe &&
                          `Your
                        call will be visible to other Hyperbound
                        ${authInfo?.isLoggedIn ? '' : ' demo'} users in your org
                        @
                        ${
                          authInfo?.isLoggedIn && !isDemoAgent
                            ? authInfo?.user.email?.split('@')[1]
                            : hbDemoInboundForm?.email?.split('@')[1]
                        } in the 'Call History' tab on the left.`}
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <DialogClose asChild>
                        <Button
                          type="submit"
                          variant={'outline'}
                          onClick={
                            maintenance?.disableCall
                              ? () => setWarningModalOpen(false)
                              : () => {
                                  queryClient.setQueryData(
                                    ['warningModalOpened'],
                                    true,
                                  );
                                  startCall();
                                }
                          }
                        >
                          {!maintenance?.disableCall ? (
                            <>
                              <PhoneIcon className="mr-2 h-4 w-4" />I
                              understand, start call
                            </>
                          ) : (
                            'Ok, I will try later'
                          )}
                        </Button>
                      </DialogClose>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
                {isDemoAgent && (
                  <DemoWelcomeModal
                    agent={agent}
                    modalOpen={modalOpen}
                    setModalOpen={setModalOpen}
                    onSubmit={() => {
                      setModalOpen(false);
                      setTimeout(() => {
                        setWarningModalOpen(true);
                      }, 200);
                    }}
                    registerForChallenge={
                      agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                    }
                  />
                )}
              </div>
            </CardFooter>
          </Card>
        </motion.div>
      </AnimatePresence>
      <ToastContainer position="bottom-right" />
    </div>
  );
}

export default ColdCallSimulationCard;
