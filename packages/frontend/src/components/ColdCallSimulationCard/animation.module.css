.animateCircle {
  position: absolute;
  top: 0px;
  left: 0px;
  bottom: 0;
  right: 0;
  width: 128px;
  height: 128px;
  animation: shadow-rotate 1s linear infinite;
  border-radius: 100%;
  background-color: rgba(50, 194, 160, 1);
  transition: opacity 0.3s ease-in-out;
  opacity: 0;
}

.animateCircle::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 100%;
  box-shadow:
    -4px -4px 16px 0px rgba(50, 194, 160, 1),
    4px 2px 10px 0px rgba(198, 247, 255, 1),
    2px 4px 18px 0px #31c393;
}

@keyframes shadow-rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
