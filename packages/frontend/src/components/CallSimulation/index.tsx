import { useState, useEffect, useRef, memo } from 'react';

import { AgentCallType, AgentDto, AgentStatus } from '@/lib/Agent/types';
import useCallSimulation from '../../hooks/useCallSimulation';
import animationStyles from './animation.module.css';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { cn } from '@/lib/utils';
import { Badge } from '../ui/badge';
import { Skeleton } from '../ui/skeleton';
import {
  CALL_TYPE_TO_ICON,
  CHALLENGE_BOT_VAPI_ID,
} from '@/common/Sidebar/OldSidebar';
import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import {
  AlertTriangleIcon,
  BrainIcon,
  LoaderIcon,
  PhoneIcon,
  PhoneIncomingIcon,
  PhoneOffIcon,
  Tag,
} from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { AnimatePresence, motion } from 'framer-motion';
import { CallDto } from '@/lib/Call/types';
import { Switch } from '../ui/switch';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import AgentService from '@/lib/Agent';
import { Id, toast } from 'react-toastify';
import _ from 'lodash';
import { TooltipArrow } from '@radix-ui/react-tooltip';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { useAuthInfo } from '@propelauth/react';
import useMaintenance from '@/hooks/useMaintenance';
import useOrg from '@/hooks/useOrg';
import dayjs from 'dayjs';
import useUserSession from '@/hooks/useUserSession';
import CallInstructions from '../CallInstructions';
import AgentAvatar from '../Avatars/Agent';

interface ICallSimulationProps {
  agent: AgentDto;
  onCallEnds?: (call: CallDto) => void;
  onCallStarts?: () => void;
  callBlitzSessionId?: number;
  hideBorder?: boolean;
  includeAgentsTags?: boolean;
  playOnLoad?: boolean;
  autoPlayDelay?: number; //if playOnLoad is true => sets the delay to start the call (in seconds) - if set to zero, the call will start immediately
  showAgentStatusBtn?: boolean;
  showTooltips?: boolean;
  showScoringMessageOnEnd?: boolean;
}

function CallSimulation({
  agent,
  onCallEnds,
  callBlitzSessionId,
  hideBorder,
  includeAgentsTags,
  playOnLoad,
  onCallStarts,
  autoPlayDelay,
  showAgentStatusBtn,
  showTooltips,
  showScoringMessageOnEnd,
}: ICallSimulationProps) {
  if (!showTooltips) {
    showTooltips = false;
  }

  const authInfo = useAuthInfo();
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isCallInProgress, setIsCallInProgress] = useState<boolean>(false);
  const [isCallDone, setIsCallDone] = useState<boolean>(false);
  const [warningModalOpen, setWarningModalOpen] = useState<boolean>(false);
  const { data: maintenance } = useMaintenance(!authInfo.isLoggedIn);
  const { data: org } = useOrg();
  const { isInIframe, dbOrg } = useUserSession();

  const onError = (error: Error) => {
    if (error) {
      if (error.message == 'EXCEEDED_MAX_DURATION') {
        setErrorMessage('Call exceeded max duration for this bot.');
      } else {
        setErrorMessage(error.message);
      }
    }
  };

  const showWarningModal = () => {
    if (showTooltips) {
      setWarningModalOpen(true);
    } else {
      startCall();
    }
  };

  ///-------------------- TIMER
  const progress = useRef<number>(0);
  const [progressLabel, setProgressLabel] = useState<string>('00:00');
  const timeoutProgressRef = useRef<ReturnType<typeof setTimeout>>();

  const startProgress = () => {
    if (timeoutProgressRef.current) {
      clearTimeout(timeoutProgressRef.current);
    }
    progress.current = 0;
    setProgressLabel('00:00');
    timeoutProgressRef.current = setTimeout(() => {
      tickProgress();
    }, 1000);
  };

  const tickProgress = () => {
    const p = progress.current + 1;
    const minutes = Math.floor(p / 60);
    const seconds = p - minutes * 60;

    const str_pad_left = (string: number, pad: string, length: number) => {
      return (new Array(length + 1).join(pad) + string).slice(-length);
    };

    const finalTime =
      str_pad_left(minutes, '0', 2) + ':' + str_pad_left(seconds, '0', 2);
    setProgressLabel(finalTime);
    progress.current++;
    timeoutProgressRef.current = setTimeout(() => {
      tickProgress();
    }, 1000);
  };
  ///-------------------- END TIMER

  const stopProgress = () => {
    if (timeoutProgressRef.current) {
      clearTimeout(timeoutProgressRef.current);
    }
  };

  const _onCallStarts = () => {
    setIsCallInProgress(true);
    startProgress();
    if (onCallStarts) {
      onCallStarts();
    }
  };

  const _onCallEnds = (call: CallDto) => {
    setIsCallDone(true);
    setIsCallInProgress(false);
    stopProgress();
    if (onCallEnds) {
      onCallEnds(call);
    }
  };

  const { startCall, endCall, isLoading, callStatus, botStatus } =
    useCallSimulation(
      agent,
      _onCallStarts,
      _onCallEnds,
      onError,
      callBlitzSessionId,
    );

  const imgSize = isInIframe ? 100 : 128;
  const [tWidth, setTWidth] = useState<number>(imgSize);
  const [tOpacity, setTOpacity] = useState<number>(0);

  useEffect(() => {
    if (botStatus.isThinking) {
      setTOpacity(1);
    } else if (botStatus.isTalking) {
      setTOpacity(1);
      setTWidth(imgSize + 6 * botStatus.volume);
    } else {
      setTWidth(imgSize);
      setTOpacity(0);
    }
  }, [botStatus]);

  const Icon =
    CALL_TYPE_TO_ICON?.[agent?.callType as keyof typeof CALL_TYPE_TO_ICON]
      ?.Icon;

  let borderClass = '';
  let shadowStyle = {};
  if (hideBorder) {
    borderClass = 'border-none';
    shadowStyle = { boxShadow: 'none' };
  }

  /*****************************************/
  /************** AGENT STATUS *************/
  /*****************************************/

  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
    onSuccess: (agent, params) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error editing the buyer. Please try again.',
        );
      }
    },
  });

  /*****************************************/
  /********* AUTO PLAY MANAGEMENT **********/
  /*****************************************/

  const [countDownRunning, setCountDownRunning] = useState(false);
  const [countDownProgress, setCountDownProgress] = useState(
    autoPlayDelay || 0,
  );

  useEffect(() => {
    let timer: any;

    if (countDownRunning) {
      timer =
        countDownProgress > 0 &&
        setInterval(() => setCountDownProgress(countDownProgress - 1), 1000);
      if (countDownProgress == 0) {
        setCountDownRunning(false);
        startCall();
      }
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [countDownProgress, countDownRunning]);

  if (!isLoading && !countDownRunning) {
    if (
      playOnLoad &&
      !callStatus.isStarting &&
      !callStatus.started &&
      !callStatus.ended
    ) {
      if (autoPlayDelay && autoPlayDelay > 0) {
        // startCountDownToPlay();
        setCountDownRunning(true);
      } else {
        setTimeout(() => {
          startCall();
        }, 300);
      }
    }
  }

  /*****************************************/
  /************** UTILS *******************/
  /*****************************************/
  const [tooltipOpen, setTooltipOpen] = useState<boolean>(false);

  useEffect(() => {
    setTimeout(() => {
      setTooltipOpen(true);
    }, 500);
  }, []);

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  /*****************************************/
  /************** RENDER *******************/
  /*****************************************/

  if (!agent) {
    return;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        exit={{ scale: 0.9 }}
        transition={{ duration: 0.3 }}
      >
        <Card
          className={`w-full md:w-[450px] shadow-md ${borderClass}`}
          style={shadowStyle}
        >
          <CardHeader>
            <CallInstructions description={agent?.description} />
          </CardHeader>
          <CardContent className={`flex items-center justify-center`}>
            <div className="flex flex-col items-center text-center">
              <div className={'relative mb-4 '}>
                <div
                  className={animationStyles.animateCircle}
                  style={{
                    width: tWidth + 'px',
                    height: tWidth + 'px',
                    opacity: tOpacity,
                  }}
                ></div>

                <AgentAvatar
                  className={cn({
                    'w-[128px] h-[128px]': !isInIframe,
                    'w-[100px] h-[100px]': isInIframe,
                  })}
                  agent={agent}
                />

                <div
                  className={cn(
                    'rounded-full p-1 w-6 h-6 border-white border-[3px] absolute bottom-1 right-2',
                    {
                      'bg-green-500': agent.status === AgentStatus.ACTIVE,
                      'bg-gray-500': agent.status === AgentStatus.INACTIVE,
                    },
                  )}
                />
              </div>
              <p className="text-lg">
                {agent.firstName} {agent.lastName}
              </p>
              <p className="text-md text-muted-foreground">
                {agent.jobTitle} @ {agent.companyName}
              </p>
              <div className="mt-1">
                {(agent.emotionalState || agent.gender) && (
                  <div
                    className={cn('flex mt-2 items-center justify-center', {
                      'space-x-1': agent.emotionalState && agent.gender,
                      // (emotionalState && salesMethodology) ||
                      // (emotionalState && gender) ||
                      // (gender && salesMethodology),
                    })}
                  >
                    {agent.callType && (
                      <Badge className="mt-1" variant="secondary">
                        {Icon && <Icon className="mr-1 h-3 w-3" />}
                        {CALL_TYPE_OPTIONS.find(
                          (item) => item.value === agent.callType,
                        )?.label ||
                          (agent.callType === 'focus'
                            ? 'Focus Call'
                            : agent.callType)}
                      </Badge>
                    )}
                    {agent?.callType !== 'focus' && (
                      <>
                        {agent.emotionalState ? (
                          <Badge className="mt-1" variant={'default'}>
                            <BrainIcon className="w-3 h-3 mr-1" />{' '}
                            {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                              (item) => item.value === agent.emotionalState,
                            )?.label ||
                              agent.emotionalState ||
                              ''}
                          </Badge>
                        ) : (
                          <Skeleton className={cn('w-16 h-6 mr-1')} />
                        )}
                      </>
                    )}
                    {agent.bookRate && (
                      <Badge variant="default">
                        Book Rate:{' '}
                        {Number(agent?.bookRate).toLocaleString('en-US', {
                          style: 'percent',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 1,
                        })}
                      </Badge>
                    )}
                  </div>
                )}
                {includeAgentsTags && (
                  <div className="mt-2 flex items-center justify-center flex-wrap">
                    {agent.tags?.map((tag) => (
                      <Badge
                        key={tag.id}
                        variant="default"
                        className="m-1 bg-teal-600"
                      >
                        <Tag size={12} className="mr-1" />
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
              {agent?.openerLine && agent.callType === AgentCallType.FOCUS && (
                <p className="mt-4">{agent.openerLine}</p>
              )}

              {showAgentStatusBtn && (
                <div className="flex space-x-2 mt-4">
                  {agent?.status !== AgentStatus.DRAFT ? (
                    <div className="flex items-center space-x-2">
                      <Switch
                        disabled={updateAgentMutation.isPending || isPilotEnded}
                        checked={agent?.status === AgentStatus.ACTIVE}
                        onCheckedChange={(value) => {
                          updateAgentMutation.mutate({
                            id: agent?.id,
                            status: value
                              ? AgentStatus.ACTIVE
                              : AgentStatus.INACTIVE,
                          });
                        }}
                        id="status-toggle"
                      />
                      {updateAgentMutation.isPending ? (
                        <Skeleton className="w-16 h-[22px] rounded-md" />
                      ) : (
                        <Badge
                          variant="default"
                          className={cn({
                            'bg-green-600':
                              agent?.status === AgentStatus.ACTIVE,
                            'bg-red-600':
                              agent?.status === AgentStatus.INACTIVE,
                          })}
                        >
                          {_.capitalize(agent?.status?.toLowerCase())}
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <Badge variant="default">
                      {_.capitalize(agent?.status?.toLowerCase())}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            {countDownRunning ? (
              <Button
                key={countDownProgress}
                size={'lg'}
                variant={'default'}
                className="w-full text-white shadow-md text-base h-[52px] rounded-2xl"
                style={{
                  background:
                    agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                      ? 'linear-gradient(to right, #000000, #5189CE, #A168A2)'
                      : 'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                  backgroundImage:
                    agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                      ? '-webkit-linear-gradient(to right, #000000, #5189CE, #A168A2)'
                      : '-webkit-linear-gradient(to bottom, #000000, #5189CE, #A168A2)',
                }}
              >
                Calling in{'  '}
                <AnimatePresence>
                  <motion.div
                    initial={{ scale: 0.4 }}
                    animate={{ scale: 0.9 }}
                    exit={{ scale: 0.9 }}
                    transition={{ duration: 0.5 }}
                    className="text-lg ml-1"
                  >
                    {countDownProgress}
                  </motion.div>
                </AnimatePresence>
              </Button>
            ) : (
              <TooltipProvider
                disableHoverableContent={!showTooltips}
                delayDuration={200}
              >
                <Tooltip
                  open={
                    tooltipOpen &&
                    !isCallInProgress &&
                    showTooltips &&
                    !(isCallDone && showScoringMessageOnEnd)
                  }
                  onOpenChange={setTooltipOpen}
                >
                  <TooltipTrigger asChild>
                    <Button
                      disabled={
                        isLoading ||
                        callStatus.isStarting ||
                        (agent as AgentDto)?.status === AgentStatus.INACTIVE ||
                        (isCallDone && showScoringMessageOnEnd) ||
                        isPilotEnded
                      }
                      size={'lg'}
                      variant={'default'}
                      className={cn(
                        'w-full text-white hover:text-white shadow-md text-base h-[52px] rounded-2xl cursor-pointer',
                        {
                          'bg-red-500 hover:bg-red-600':
                            callStatus.started ||
                            (isCallDone && showScoringMessageOnEnd),
                          'drop-shadow-2xl hover:opacity-80 transition-opacity duration-200 border border-white/50':
                            !callStatus.started &&
                            !(isCallDone && showScoringMessageOnEnd),
                        },
                      )}
                      style={
                        !callStatus.started &&
                        !(isCallDone && showScoringMessageOnEnd)
                          ? {
                              background:
                                agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                                  ? 'linear-gradient(to right, #000000, #5189CE, #A168A2)'
                                  : 'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                              backgroundImage:
                                agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                                  ? '-webkit-linear-gradient(to right, #000000, #5189CE, #A168A2)'
                                  : '-webkit-linear-gradient(to bottom, #000000, #5189CE, #A168A2)',
                            }
                          : {}
                      }
                      onClick={callStatus.started ? endCall : showWarningModal}
                    >
                      {callStatus.started ? (
                        <PhoneOffIcon className="mr-2 h-5 w-5" />
                      ) : callStatus.isStarting ? (
                        <PhoneIncomingIcon className="mr-2 h-5 w-5 animate-pulse" />
                      ) : isCallDone && showScoringMessageOnEnd ? (
                        <LoaderIcon className="mr-2 h-5 w-5 animate-pulse" />
                      ) : (
                        <PhoneIcon className="mr-2 h-5 w-5" />
                      )}
                      <span>
                        {callStatus.started
                          ? 'End Call'
                          : callStatus.isStarting
                            ? `Calling ${agent?.firstName}`
                            : isCallDone && showScoringMessageOnEnd
                              ? 'Ending & scoring your call'
                              : 'Start Call'}
                      </span>
                      {callStatus.started && (
                        <span className="ml-2">{progressLabel}</span>
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="w-96 text-lg bg-gray-800 text-center rounded-xl"
                  >
                    <TooltipArrow />
                    {agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                      ? '* Please read instructions on the left before starting a call'
                      : isPilotEnded
                        ? 'Your pilot has ended. Please contact your admin.'
                        : 'Click here to start your call'}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </CardFooter>
        </Card>

        <Dialog open={warningModalOpen} onOpenChange={setWarningModalOpen}>
          <DialogContent className="close-btn">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                {maintenance?.disableCall && (
                  <AlertTriangleIcon className="mr-2 text-yellow-500" />
                )}
                {!maintenance?.disableCall
                  ? "Let's get started!"
                  : 'High volume alert, please check back in 1 hour'}
              </DialogTitle>
              <DialogDescription className="py-4">
                {maintenance?.callWarningMessage ||
                  `At times, the bot may be unresponsive, or have unusual lag times. `}
                We are always working to improve the experience!
                {dbOrg?.onlyAdminCanViewAllCalls ? (
                  ''
                ) : (
                  <>
                    Your call will be visible to other Hyperbound
                    {authInfo?.isLoggedIn ? '' : ' demo'} users in your org @
                    {authInfo?.user?.email?.split('@')[1]} in the &apos;Org
                    Calls&apos; tab on the left.
                  </>
                )}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  type="submit"
                  variant={'outline'}
                  onClick={
                    maintenance?.disableCall
                      ? () => setWarningModalOpen(false)
                      : () => {
                          queryClient.setQueryData(
                            ['warningModalOpened'],
                            true,
                          );
                          startCall();
                        }
                  }
                >
                  {!maintenance?.disableCall ? (
                    <>
                      <PhoneIcon className="mr-2 h-4 w-4" />I understand, start
                      call
                    </>
                  ) : (
                    'Ok, I will try later'
                  )}
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </motion.div>
    </AnimatePresence>
  );
}

export default memo(CallSimulation);
