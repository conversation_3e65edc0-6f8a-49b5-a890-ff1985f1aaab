import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface IProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  title: string;
  description?: string;
  cancelLabel?: string;
  confirmLabel?: string;
  useBtnBlack?: boolean;
  isLoading?: boolean;
}

export default function ConfirmationModal({
  open,
  onCancel,
  onConfirm,
  title,
  description,
  cancelLabel,
  confirmLabel,
  useBtnBlack,
  isLoading,
}: IProps) {
  if (!cancelLabel) {
    cancelLabel = 'No';
  }

  if (!confirmLabel) {
    confirmLabel = 'Yes';
  }

  const closeModal = (isOpen: boolean) => {
    if (!isOpen) {
      onCancel();
    }
  };

  const cancel = (e: any) => {
    onCancel();
  };

  const confirm = (e: any) => {
    onConfirm();
  };

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">{title}</DialogTitle>
          {description && (
            <DialogDescription className="py-2">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={cancel} disabled={isLoading}>
            {cancelLabel}
          </Button>
          <Button
            variant={useBtnBlack ? 'default' : 'destructive'}
            onClick={confirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              confirmLabel
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
