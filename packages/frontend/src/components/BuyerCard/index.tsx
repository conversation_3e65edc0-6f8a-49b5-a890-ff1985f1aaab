import React from 'react';
import { AgentDto } from '@/lib/Agent/types';
import FullCard from './Full';
import CompactCard from './Compact';
import SelectAgent, { SelectVariationsMode } from './SelectAgent';

export enum CardType {
  FULL = 'full',
  COMPACT = 'compact',
  MINI = 'mini',
  SELECT_ONLY = 'select_only',
}

interface IBuyerCardProps {
  agent: AgentDto;
  type: CardType;
  isSelected?: boolean;
  selectMode?: boolean; //hides buttons and shows a checkbox (only on fullcard)
  className?: string;
  showAgentsTags?: boolean; //shows the agents tags but hides the agent features
  includeAgentsTags?: boolean; //includes the agents tags in the card along the agent features
  showMenu?: boolean; //shows/hides dropdown menu
  showCallIcon?: boolean; //hides the call button
  hideEditBtn?: boolean; //hides the edit button from the dropdown menu
  onCardClick?: (agent: AgentDto) => void;
  onStartCallClick?: (agent: AgentDto) => void;
  onVariationsClick?: (agent: AgentDto) => void; //if undefined, the Variations button will not be displayed
  onShareClick?: (agent: AgentDto) => void; //if undefined, the Variations button will not be displayed
  selectVariationsMode?: SelectVariationsMode;
  onShowVariations?: (agent: AgentDto) => void;
}

function BuyerCard({
  agent,
  type,
  isSelected,
  onCardClick,
  onStartCallClick,
  onVariationsClick,
  onShareClick,
  className,
  selectMode,
  showAgentsTags,
  showMenu,
  showCallIcon,
  includeAgentsTags,
  hideEditBtn,
  selectVariationsMode,
  onShowVariations,
}: IBuyerCardProps) {
  if (showMenu === undefined) {
    showMenu = true; //default
  }

  if (type === CardType.FULL) {
    return (
      <FullCard
        hideEditBtn={hideEditBtn}
        showMenu={true}
        showAgentsTags={showAgentsTags}
        selectMode={selectMode}
        className={className}
        agent={agent}
        isSelected={isSelected}
        onCardClick={onCardClick}
        onStartCallClick={onStartCallClick}
        onVariationsClick={onVariationsClick}
        onShareClick={onShareClick}
      />
    );
  } else if (type === CardType.COMPACT) {
    return (
      <CompactCard
        hideEditBtn={hideEditBtn}
        includeAgentsTags={includeAgentsTags}
        className={className}
        agent={agent}
        onCardClick={onCardClick}
        showMenu={showMenu}
        showAgentsTags={showAgentsTags}
        showCallIcon={showCallIcon}
        isSelected={isSelected}
      />
    );
  } else if (type === CardType.SELECT_ONLY) {
    return (
      <SelectAgent
        onShowVariations={onShowVariations}
        selectVariationsMode={selectVariationsMode}
        showAgentsTags={showAgentsTags}
        className={className}
        agent={agent}
        onCardClick={onCardClick}
        isSelected={isSelected}
      />
    );
  }
}

export default React.memo(BuyerCard);
