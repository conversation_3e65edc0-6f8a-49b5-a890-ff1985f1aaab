import {
  <PERSON>,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Footer,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import { Agent<PERSON>allType, AgentDto, AgentStatus } from '@/lib/Agent/types';
import { BrainIcon, FileStack, PhoneIcon, Tag } from 'lucide-react';
import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import AgentAvatar from '@/components/Avatars/Agent';

export enum SelectVariationsMode {
  SELECT_VARIATIONS = 'SELECT_VARIATIONS',
  SELECT_PARENT_ONLY = 'SELECT_PARENT_ONLY',
}
interface IProps {
  agent: AgentDto;
  isSelected?: boolean;
  className?: string;
  showAgentsTags?: boolean;
  onCardClick?: (agent: AgentDto) => void;
  selectVariationsMode?: SelectVariationsMode;
  onShowVariations?: (agent: AgentDto) => void;
}

export default function SelectAgent({
  agent,
  isSelected,
  className,
  showAgentsTags,
  onCardClick,
  selectVariationsMode,
  onShowVariations,
}: IProps) {
  let bgSelectedStyle = {};
  if (isSelected) {
    bgSelectedStyle = {
      boxShadow:
        '-2px -2px 8px 0px rgba(50, 194, 160, 0.4), 1px 1px 6px 0px rgba(198, 247, 255, 0.4), 4px 2px 6px 0px rgba(49, 195, 146, 0.4)',
    };
  }

  const hasVariations =
    agent.variationParentAgentId &&
    agent.variationParentAgentId == agent.id &&
    agent.variationName &&
    agent.variationName != '';

  const Icon =
    CALL_TYPE_TO_ICON?.[agent?.callType as keyof typeof CALL_TYPE_TO_ICON]
      ?.Icon;

  const _onCardClick = () => {
    if (
      hasVariations &&
      selectVariationsMode === SelectVariationsMode.SELECT_VARIATIONS
    ) {
      _onVariationsClick();
    } else {
      if (onCardClick) {
        onCardClick(agent);
      }
    }
  };

  const _onVariationsClick = () => {
    if (onShowVariations) {
      onShowVariations(agent);
    }
  };

  return (
    <Card
      onClick={_onCardClick}
      className={cn(
        className,
        'w-full h-full ml-0 relative shadow-sm transition-shadow duration-200 cursor-pointer flex flex-col',
        {
          'opacity-50 cursor-not-allowed pointer-events-none': !agent.vapiId,
          'hover:shadow-md': !isSelected,
        },
      )}
      style={bgSelectedStyle}
    >
      <CardHeader>
        <div className="flex flex-row items-start">
          <div className="relative mr-2">
            <AgentAvatar className="w-[52px] h-[52px] relative" agent={agent} />
            <div
              className={cn(
                'rounded-full p-1 w-4 h-4 border-white border-[3px] absolute bottom-0 right-0',
                {
                  'bg-green-500': agent.status === AgentStatus.ACTIVE,
                  'bg-gray-500': agent.status === AgentStatus.INACTIVE,
                },
              )}
            />
          </div>
          <div className="flex-1">
            <CardTitle>
              {agent.firstName} {agent.lastName}
            </CardTitle>
            <CardDescription className="text-[0.76rem] leading-3 mt-2">
              {agent.jobTitle}{' '}
              <span style={{ whiteSpace: 'wrap' }}>
                {'@  ' + agent.companyName}
              </span>
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1 ">
        <div className="">
          {showAgentsTags ? (
            agent.tags?.map((tag) => (
              <Badge
                key={tag.id}
                variant="default"
                className="mr-1 bg-teal-600"
              >
                <Tag size={12} className="mr-1" />
                {tag.name}
              </Badge>
            ))
          ) : (
            <>
              {(agent.emotionalState || agent.gender) && (
                <div
                  className={cn('flex flex-wrap mt-2', {
                    'space-x-1': agent.emotionalState && agent.gender,
                    // (emotionalState && salesMethodology) ||
                    // (emotionalState && gender) ||
                    // (gender && salesMethodology),
                  })}
                >
                  {agent.callType && (
                    <Badge className="mt-1" variant="secondary">
                      {Icon && <Icon className="mr-1 h-3 w-3" />}
                      {CALL_TYPE_OPTIONS.find(
                        (item) => item.value === agent.callType,
                      )?.label ||
                        (agent.callType === 'focus'
                          ? 'Focus Call'
                          : agent.callType)}
                    </Badge>
                  )}
                  {agent.callType != AgentCallType.FOCUS &&
                    (agent.emotionalState ? (
                      <Badge className="mt-1" variant={'default'}>
                        <BrainIcon className="w-3 h-3 mr-1" />{' '}
                        {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                          (item) => item.value === agent.emotionalState,
                        )?.label ||
                          agent.emotionalState ||
                          ''}
                      </Badge>
                    ) : (
                      <Skeleton className={cn('w-16 h-6 mr-1')} />
                    ))}

                  {agent.bookRate && (
                    <Badge className="mt-1" variant="default">
                      Book Rate:{' '}
                      {Number(agent?.bookRate).toLocaleString('en-US', {
                        style: 'percent',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 1,
                      })}
                    </Badge>
                  )}
                </div>
              )}
              {agent?.openerLine && agent.callType === AgentCallType.FOCUS && (
                <p className="mt-4 mb-16">{agent.openerLine}</p>
              )}
            </>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <div className="flex w-full justify-between space-x-2">
          {hasVariations && onShowVariations && (
            <Button
              variant={'default'}
              className={
                'w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200'
              }
              style={{
                backgroundImage:
                  'linear-gradient(to right, #000000, #5189CE, #A168A2)',
              }}
              onClick={(e) => {
                e.stopPropagation();
                _onVariationsClick();
              }}
            >
              <FileStack className="mr-2 h-4 w-4" />
              Select from {agent.variationName} variations
            </Button>
          )}

          {!hasVariations && (
            <Checkbox checked={isSelected} className="w-4 h-4" />
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
