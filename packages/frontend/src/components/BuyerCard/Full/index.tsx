import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import { AgentCallType, AgentDto, AgentStatus } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { FileStack, PhoneIcon, Tag } from 'lucide-react';
import React from 'react';
import AgentDropdownMenu from '../AgentDropdownMenu';
import dayjs from 'dayjs';
import useOrg from '@/hooks/useOrg';
import { TooltipArrow } from '@radix-ui/react-tooltip';
import {
  Toolt<PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import AgentAvatar from '@/components/Avatars/Agent';
import EmotionBadge from '@/common/CreateBuyerForm/BotPreview/EmotionBadge';

interface IFullCardProps {
  agent: AgentDto;
  isSelected?: boolean;
  className?: string;
  selectMode?: boolean;
  showAgentsTags?: boolean;
  showMenu: boolean; //shows/hides dropdown menu
  hideEditBtn?: boolean;
  onCardClick?: (agent: AgentDto) => void;
  onStartCallClick?: (agent: AgentDto) => void;
  onVariationsClick?: (agent: AgentDto) => void; //if undefined, the Variations button will not be displayed
  onShareClick?: (agent: AgentDto) => void;
  onShareWithTeam?: (agent: AgentDto) => void;
}

function FullCard({
  agent,
  isSelected,
  onCardClick,
  onStartCallClick,
  onVariationsClick,
  onShareClick,
  className,
  selectMode,
  showAgentsTags,
  showMenu,
  hideEditBtn,
  onShareWithTeam,
}: IFullCardProps) {
  const { data: org } = useOrg();
  const agentId = agent.providerAgentId ? agent.providerAgentId : agent.vapiId;

  const hasVariations =
    agent.variationParentAgentId &&
    agent.variationName &&
    agent.variationName != '';

  const _onCardClick = () => {
    if (onCardClick) {
      onCardClick(agent);
    }
  };

  const _onStartCallClick = () => {
    if (onStartCallClick) {
      onStartCallClick(agent);
    }
  };

  const _onVariationsClick = () => {
    if (onVariationsClick) {
      onVariationsClick(agent);
    }
  };

  let bgSelectedStyle = {};
  if (isSelected) {
    bgSelectedStyle = {
      boxShadow:
        isSelected &&
        '-2px -2px 8px 0px rgba(50, 194, 160, 0.4), 1px 1px 6px 0px rgba(198, 247, 255, 0.4), 4px 2px 6px 0px rgba(49, 195, 146, 0.4)',
    };
  }

  const Icon =
    CALL_TYPE_TO_ICON?.[agent?.callType as keyof typeof CALL_TYPE_TO_ICON]
      ?.Icon;

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <TooltipProvider delayDuration={200}>
      <Tooltip>
        <TooltipTrigger asChild disabled={!isPilotEnded}>
          <Card
            onClick={_onCardClick}
            className={cn(
              className,
              'w-[340px] ml-0 relative shadow-sm transition-shadow duration-200 cursor-pointer min-h-[220px] h-full',
              {
                'opacity-50 cursor-not-allowed pointer-events-none': !agentId,
                'opacity-70 cursor-not-allowed': isPilotEnded,
                'hover:shadow-md': !isSelected,
              },
            )}
            style={bgSelectedStyle}
          >
            <CardHeader className="py-4">
              <div className="flex flex-row items-start">
                <div className="relative mr-2">
                  <AgentAvatar
                    className="w-[52px] h-[52px] relative"
                    agent={agent}
                  />

                  <div
                    className={cn(
                      'rounded-full p-1 w-4 h-4 border-white border-[3px] absolute bottom-0 right-0',
                      {
                        'bg-green-500': agent.status === AgentStatus.ACTIVE,
                        'bg-gray-500': agent.status === AgentStatus.INACTIVE,
                      },
                    )}
                  />
                </div>
                <div className="flex-1">
                  <CardTitle>
                    {agent.firstName} {agent.lastName}
                  </CardTitle>
                  <CardDescription className="text-[0.76rem] leading-3 mt-2">
                    {agent.jobTitle}{' '}
                    <span style={{ whiteSpace: 'wrap' }}>
                      {'@  ' + agent.companyName}
                    </span>
                  </CardDescription>
                </div>
                {!selectMode && showMenu && (
                  <AgentDropdownMenu
                    agent={agent}
                    onVariationsClick={onVariationsClick}
                    onShareClick={onShareClick}
                    hideEditBtn={hideEditBtn}
                    onShareWithTeam={onShareWithTeam}
                  />
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div>
                {showAgentsTags ? (
                  agent.tags?.map((tag) => (
                    <Badge
                      key={tag.id}
                      variant="default"
                      className="mr-1 bg-teal-600"
                    >
                      <Tag size={12} className="mr-1" />
                      {tag.name}
                    </Badge>
                  ))
                ) : hasVariations ? (
                  <Badge className="mt-1" variant="secondary">
                    Variation: {agent.variationName}
                  </Badge>
                ) : (
                  <>
                    {(agent.emotionalState || agent.gender) && (
                      <div
                        className={cn('flex flex-wrap mt-2', {
                          'space-x-1': agent.emotionalState && agent.gender,
                          // (emotionalState && salesMethodology) ||
                          // (emotionalState && gender) ||
                          // (gender && salesMethodology),
                        })}
                      >
                        {agent.callType && (
                          <Badge className="mt-1" variant="secondary">
                            {Icon && <Icon className="mr-1 h-3 w-3" />}
                            {CALL_TYPE_OPTIONS.find(
                              (item) => item.value === agent.callType,
                            )?.label ||
                              (agent.callType === 'focus'
                                ? 'Focus Call'
                                : agent.callType)}
                          </Badge>
                        )}
                        {agent.callType != AgentCallType.FOCUS &&
                          (agent.emotionalState ? (
                            <EmotionBadge
                              emotionalState={agent.emotionalState}
                            />
                          ) : (
                            <Skeleton className={cn('w-16 h-6 mr-1')} />
                          ))}

                        {agent.bookRate && (
                          <Badge className="mt-1" variant="default">
                            Book Rate:{' '}
                            {Number(agent?.bookRate).toLocaleString('en-US', {
                              style: 'percent',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 1,
                            })}
                          </Badge>
                        )}
                      </div>
                    )}
                    {agent?.openerLine &&
                      agent.callType === AgentCallType.FOCUS && (
                        <p className="mt-4 mb-16">{agent.openerLine}</p>
                      )}
                  </>
                )}
              </div>
            </CardContent>
            <CardFooter className="absolute bottom-0 w-full">
              <div className="flex w-full justify-between space-x-2">
                {!selectMode ? (
                  hasVariations ? (
                    <Button
                      size={'lg'}
                      variant={'default'}
                      className={
                        'w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200'
                      }
                      style={{
                        backgroundImage:
                          'linear-gradient(to right, #000000, #5189CE, #A168A2)',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        _onVariationsClick();
                      }}
                    >
                      <FileStack className="mr-2 h-4 w-4" />
                      See Variations
                    </Button>
                  ) : (
                    <Button
                      size={'lg'}
                      variant={'default'}
                      className={
                        'w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200'
                      }
                      style={{
                        backgroundImage:
                          'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        _onStartCallClick();
                      }}
                      disabled={isPilotEnded}
                    >
                      <PhoneIcon className="mr-2 h-4 w-4" />
                      Call {agent.firstName}
                    </Button>
                  )
                ) : (
                  <div className="flex justify-center items-center">
                    <Checkbox checked={isSelected} className="mr-2" />
                    Click to select
                  </div>
                )}
              </div>
            </CardFooter>
          </Card>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          className="w-96 text-lg bg-gray-800 text-center rounded-xl"
          hidden={!isPilotEnded}
        >
          <TooltipArrow />
          {isPilotEnded
            ? 'Your pilot has ended. Please contact your admin.'
            : ''}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export default React.memo(FullCard);
