/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState, useRef } from 'react';
import {
  CopyPlusIcon,
  Edit2Icon,
  LayoutListIcon,
  LockIcon,
  MoreVerticalIcon,
  PlusIcon,
  FileStack,
  CheckIcon,
  Loader2Icon,
  Forward,
  Power,
  CopyIcon,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  AgentDto,
  AgentStatus,
  AgentCallType,
  TagDto,
} from '@/lib/Agent/types';
import { useAuthInfo } from '@propelauth/react';
import { useRouter } from 'next/navigation';
import useTags from '@/hooks/useTags';
import { cn } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import AgentService, { TagsService } from '@/lib/Agent';
import { Id, toast, ToastContainer } from 'react-toastify';
import CreateNewTagDialog from '@/common/Buyers/ProdSite/multiactionsBar/tagsMenu/createNew';
import useUserSession from '@/hooks/useUserSession';
import ShareAgentWithOrgToggle from './ShareAgentWithOrgToggle';
import LinksManager from '@/lib/linksManager';
import dayjs from 'dayjs';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AppPermissions } from '@/lib/permissions';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';

interface IAgentDropdownMenuProps {
  agent: AgentDto;
  onVariationsClick?: (agent: AgentDto) => void; //if undefined, the Variations button will not be displayed
  onShareClick?: (agent: AgentDto) => void;
  hideEditBtn?: boolean;
}

export type TagStatus = {
  [key: number]: { add: boolean; delete: boolean; isNew: boolean };
};

function AgentDropdownMenu({
  agent,
  onVariationsClick,
  onShareClick,
  hideEditBtn,
}: IAgentDropdownMenuProps) {
  const authInfo = useAuthInfo();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const { canAccess, dbOrg, subOrganizations } = useUserSession();
  const { callTypeOptions } = useCallTypeOptions();

  const _onVariationsClick = () => {
    if (onVariationsClick) {
      onVariationsClick(agent);
    }
  };

  /*****************************/
  /***** TAGS MANAGEMENT *******/
  /*****************************/

  const { data: allTags } = useTags(true, 0, 0, '');
  const [selectedTagsStatus, setSelectedTagsStatus] = useState<TagStatus>({});
  const [loadingTags, setLoadingTags] = useState<{ [key: number]: boolean }>(
    {},
  );
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  const [isCreatenewDialogOpen, setIsCreatenewDialogOpen] =
    useState<boolean>(false);

  const editTagsAgentsLinksMutation = useMutation({
    mutationFn: TagsService.editTagsAgentsLinks,
  });

  useEffect(() => {
    const _selectedTagsStatus: TagStatus = {};
    (agent?.tags || []).map((t) => {
      if (!_selectedTagsStatus[t.id]) {
        _selectedTagsStatus[t.id] = {
          add: false,
          delete: false,
          isNew: false,
        };
      }
    });
    setSelectedTagsStatus(_selectedTagsStatus);
  }, [agent]);

  const toggleTag = async (tag: TagDto, showSuccessToaster = false) => {
    if (!selectedTagsStatus[tag.id]) {
      selectedTagsStatus[tag.id] = {
        add: true,
        delete: false,
        isNew: true,
      };
      saveToDb(tag.id, true);
    } else {
      if (selectedTagsStatus[tag.id].add) {
        selectedTagsStatus[tag.id].add = false;
        selectedTagsStatus[tag.id].delete = true;
        saveToDb(tag.id, true, showSuccessToaster);
      } else {
        selectedTagsStatus[tag.id].add = true;
        selectedTagsStatus[tag.id].delete = false;
        saveToDb(tag.id, false, showSuccessToaster);
      }
    }

    setSelectedTagsStatus({ ...selectedTagsStatus });
  };

  const saveToDb = async (
    tagId: number,
    add: boolean,
    showSuccessToaster = false,
  ) => {
    setLoadingTags({ ...loadingTags, [tagId]: true });

    const forAgents: number[] = [agent.id];

    const addTags: number[] = [];
    const removeTags: number[] = [];

    if (add) {
      addTags.push(tagId);
    } else {
      removeTags.push(tagId);
    }

    let ok = false;
    try {
      await editTagsAgentsLinksMutation.mutateAsync({
        forAgents,
        addTags,
        removeTags,
      });
      ok = true;
    } catch (e) {
      console.error(e);
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving. Please try again.',
        );
      }
    }

    if (ok) {
      if (showSuccessToaster) {
        errorToastId.current = toast.success('Tag successfully added');
      }
      //not sure why but if I add the invalidateQueries to the same array, it wont invalidate
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({
        queryKey: ['orgAgentsByTagsAndVariations'],
      });
      queryClient.invalidateQueries({ queryKey: ['orgAgentVariations'] });
    }
    setLoadingTags({ ...loadingTags, [tagId]: false });
  };

  //************** END TAGS MANAGEMENT

  /***********************************/
  /***** SHARE AGENT WITH ORG  *******/
  /***********************************/

  const tmp: any = {};
  if (agent.subAgents) {
    agent.subAgents.map((a) => {
      // If the variationParentAgentId = id, it means it's not a variation created in the sub org
      // We only want to take in consideration that were copied over to the sub org, and not the variations created from the copied over agent
      const isPrimarilySharedAgent =
        !a.variationParentAgentId || a.variationParentAgentId === a.id;
      if (a.status === AgentStatus.ACTIVE && isPrimarilySharedAgent) {
        tmp[a.orgId] = {
          selected: true,
          canEdit: a.canOrgEditAgent,
        };
      }
    });
  }

  const [orgsPerAgent, setOrgsPerAgent] = useState<any>(tmp);

  const toggleAgentForOrganization = async (
    org: any,
    canEditAgent: boolean,
  ) => {
    if (subOrganizations) {
      const orgDetails: any = subOrganizations.find(
        (subOrg: any) => subOrg.uid == org.orgId,
      );

      let isShared = true;
      if (orgsPerAgent[orgDetails.id]) {
        isShared = !orgsPerAgent[orgDetails.id].selected;
        orgsPerAgent[orgDetails.id] = {
          selected: !orgsPerAgent[orgDetails.id].selected,
          canEdit: canEditAgent,
        };
      } else {
        orgsPerAgent[orgDetails.id] = {
          selected: true,
          canEdit: canEditAgent,
        };
      }

      setOrgsPerAgent({ ...orgsPerAgent });

      let showSuccessToaster = false;

      try {
        await AgentService.toggleShareAgentWithOrg(
          agent.id,
          org.orgId,
          canEditAgent,
        );
        showSuccessToaster = true;
      } catch (e) {
        console.log(e);
        showSuccessToaster = false;
        errorToastId.current = toast.error(
          'Something went wrong. Please try again.',
        );
      }

      if (showSuccessToaster) {
        if (isShared) {
          errorToastId.current = toast.success('Agent successfully shared');
        } else {
          errorToastId.current = toast.success('Agent successfully revoked');
        }
      }

      //not sure why but if I add the invalidateQueries to the same array, it wont invalidate
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({
        queryKey: ['orgAgentsByTagsAndVariations'],
      });
      queryClient.invalidateQueries({ queryKey: ['orgAgentVariations'] });
    }
  };

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  //************** END SHARE AGENT WITH ORG

  /******************************************/
  /**************** STATUS ******************/
  /******************************************/

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
    onSuccess: (agent) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error editing the buyer. Please try again.',
        );
      }
    },
  });

  const toggleAgentStatus = async () => {
    updateAgentMutation.mutate({
      id: agent?.id,
      status:
        agent.status == AgentStatus.INACTIVE
          ? AgentStatus.ACTIVE
          : AgentStatus.INACTIVE,
    });
  };

  ///END STATUS MANAGEMENT

  return (
    <>
      <DropdownMenu
        open={open}
        onOpenChange={(o) => {
          setOpen(o);
        }}
      >
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size={'sm'}
            className="w-8 h-8 rounded-full p-0 top-4 right-4"
          >
            <span className="sr-only">Open menu</span>
            {!agent?.vapiId ? (
              <LockIcon className="w-4 h-4 text-muted-foreground" />
            ) : (
              <MoreVerticalIcon className="h-4 w-4" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="center">
          {canAccess(AppPermissions.MANAGE_TAGS) && (
            <DropdownMenuSub>
              <DropdownMenuSubTrigger
                onClick={(e) => {
                  e.stopPropagation();
                }}
                disabled={isPilotEnded}
              >
                <PlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <div className="flex space-x-2">
                  <p>Add tags</p>
                </div>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent className="w-64">
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setOpen(false);
                      setIsCreatenewDialogOpen(true);
                    }}
                  >
                    <PlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>New tag</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  {allTags?.map((t) => {
                    return (
                      <DropdownMenuItem
                        key={'tag-' + t.id}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          toggleTag(t);
                        }}
                      >
                        <div
                          className={cn(
                            'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                            agent.tags?.find((tag) => tag.id === t.id)
                              ? 'bg-primary text-primary-foreground'
                              : 'opacity-50 [&_svg]:invisible',
                          )}
                        >
                          <CheckIcon className={cn('h-4 w-4')} />
                        </div>
                        <div className="flex space-x-2 items-center">
                          <div className="capitalize flex-1">{t.name}</div>
                          <div>
                            {loadingTags[t.id] && (
                              <Loader2Icon
                                className="animate-spin mr-2"
                                size={14}
                              />
                            )}
                          </div>
                        </div>
                      </DropdownMenuItem>
                    );
                  })}
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          )}

          {canAccess(AppPermissions.MANAGE_BOTS) &&
            !hideEditBtn &&
            agent.canOrgEditAgent && (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  if (agent.callType == AgentCallType.FOCUS) {
                    router.push(
                      `/buyers/${agent.vapiId}/edit/focus?callType=${agent.callType}`,
                    );
                  } else {
                    router.push(
                      `/buyers/${agent.vapiId}/edit/main?callType=${agent.callType}`,
                    );
                  }
                }}
                disabled={isPilotEnded}
              >
                <Edit2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Edit</span>
              </DropdownMenuItem>
            )}

          {agent.callType != AgentCallType.FOCUS && !hideEditBtn && (
            <>
              {canAccess(AppPermissions.CLONE_BOTS) && (
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    disabled={isPilotEnded}
                  >
                    <CopyPlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                    <div className="flex space-x-2">
                      <p>Clone as...</p>
                    </div>
                  </DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent className="w-64">
                      {callTypeOptions.map((ct) => {
                        if (ct.value === AgentCallType.FOCUS) return null;
                        if (ct.value === AgentCallType.GATEKEEPER) return null;
                        if (ct.value === AgentCallType.CUSTOM) return null;
                        const Icon =
                          CALL_TYPE_TO_ICON?.[
                            ct.value as keyof typeof CALL_TYPE_TO_ICON
                          ]?.Icon;
                        return (
                          <DropdownMenuItem
                            key={ct.value}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              router.push(
                                `/buyers/create/main?cloneBuyerId=${agent.vapiId}&cloneBuyerToType=${ct.value}`,
                              );
                            }}
                          >
                            <div className="flex items-center">
                              {Icon && <Icon className="mr-2 h-4 w-4" />}
                              <div className="capitalize">{ct.label}</div>
                            </div>
                          </DropdownMenuItem>
                        );
                      })}
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
              )}

              {
                //user should not be able to create variations from a variation:
                ((onVariationsClick &&
                  !(agent as AgentDto).variationParentAgentId) ||
                  (agent as AgentDto).id ===
                    (agent as AgentDto).variationParentAgentId) && (
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      _onVariationsClick();
                      //router.push(`buyers/variations/${agent.vapiId}`);
                    }}
                  >
                    <FileStack className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>Variations</span>
                  </DropdownMenuItem>
                )
              }
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  const url = `${window.location.host}/buyers/${agent.vapiId}`;
                  navigator.clipboard.writeText(url);
                }}
              >
                <CopyIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Copy URL</span>
              </DropdownMenuItem>
            </>
          )}

          {/* {isAdmin && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                router.push(`/coaching/assignments/${agent.id}`);
              }}
            >
              <ListChecksIcon className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>View assignments</span>
            </DropdownMenuItem>
          )} */}

          <DropdownMenuItem
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              router.push(LinksManager.trainingCalls(`?buyers=${agent.id}`));
            }}
          >
            <LayoutListIcon className="w-4 h-4 mr-2 text-muted-foreground" />
            <span>View calls</span>
          </DropdownMenuItem>

          {canAccess(AppPermissions.SHARE_BOTS) && dbOrg?.canCreateSubOrgs ? (
            onShareClick ? (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onShareClick(agent);
                  //router.push(`buyers/variations/${agent.vapiId}`);
                }}
              >
                <Forward className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Share with org</span>
              </DropdownMenuItem>
            ) : (
              <DropdownMenuSub>
                <DropdownMenuSubTrigger
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  disabled={isPilotEnded}
                >
                  <Forward className="w-4 h-4 mr-2 text-muted-foreground" />
                  <span>Share with org</span>
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent className="w-64">
                    {authInfo?.orgHelper?.getOrgs().map((org) => {
                      if (org.orgId != dbOrg.uid && subOrganizations) {
                        const orgDetails = subOrganizations.find(
                          (subOrg: any) => subOrg.uid == org.orgId,
                        );
                        if (orgDetails) {
                          let orgStatus = orgsPerAgent[orgDetails.id];
                          if (!orgStatus) {
                            orgStatus = {
                              selected: false,
                              canEdit: false,
                            };
                          }
                          return (
                            <DropdownMenuItem
                              key={'org-' + org.orgId}
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                            >
                              <ShareAgentWithOrgToggle
                                org={org}
                                selected={orgStatus.selected}
                                canEdit={orgStatus.canEdit}
                                toggle={(
                                  organization: any,
                                  canEditAgent: boolean,
                                ) => {
                                  toggleAgentForOrganization(
                                    organization,
                                    canEditAgent,
                                  );
                                }}
                              />
                            </DropdownMenuItem>
                          );
                        }
                      }
                    })}
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>
            )
          ) : null}

          {canAccess(AppPermissions.MANAGE_BOTS) && (
            <DropdownMenuItem
              className={
                'cursor-pointer ' +
                (agent.status == AgentStatus.ACTIVE
                  ? 'text-red-400'
                  : 'text-green-500')
              }
              onClick={(e) => {
                e.stopPropagation();
                toggleAgentStatus();
              }}
            >
              <Power className="w-4 h-4 mr-2" />
              <span>
                {agent.status == AgentStatus.ACTIVE ? 'Deactivate' : 'Activate'}
              </span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <CreateNewTagDialog
        open={isCreatenewDialogOpen}
        onClose={(t: TagDto | undefined) => {
          if (t) {
            toggleTag(t, true);
          }
          setIsCreatenewDialogOpen(false);
        }}
      />
      <ToastContainer position="top-right" />
    </>
  );
}

export default React.memo(AgentDropdownMenu);
