import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import Checkbox from '@/components/ui/Hyperbound/checkbox-with-label';
import { ArrowR<PERSON>, ChevronLeft, ShieldCheck } from 'lucide-react';

interface IProps {
  org: any;
  selected: boolean;
  canEdit: boolean;
  toggle: (org: any, canEditAgent: boolean) => void;
}
export default function ShareAgentWithOrgToggle({
  org,
  selected,
  canEdit,
  toggle,
}: IProps) {
  const [current, setCurrent] = useState<number>(0);
  const [canEditAgent, setCanEditAgent] = useState<boolean>(false);

  const next = () => {
    if (selected) {
      doToggle();
    } else {
      setCurrent(current + 1);
    }
  };

  const previous = () => {
    setCurrent(current - 1);
  };

  const doToggle = (_canEditAgent?: boolean) => {
    if (_canEditAgent != undefined) {
      toggle(org, _canEditAgent);
    } else {
      toggle(org, canEditAgent);
    }
    setCurrent(0);
  };

  const toggleCanEdit = () => {
    setCanEditAgent(!canEditAgent);
    doToggle(!canEditAgent);
  };

  return (
    <div className="overflow-hidden">
      <div
        className={`flex text-sm transition ease-out duration-600`}
        style={{ transform: `translateX(-${current * 100}%)` }}
      >
        <div className="shrink-0 w-full">
          <Checkbox checked={selected} onToggle={next} className="w-full">
            <div className="flex space-x-2 items-center w-full">
              <Avatar className="w-4 h-4 relative">
                {org?.orgMetadata?.logo && (
                  <AvatarImage src={org?.orgMetadata?.logo} />
                )}
                <AvatarFallback className="text-sm">
                  {org?.orgName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">{org?.orgName || 'Hyperbound'}</div>
              {selected && canEdit && (
                <div className="">
                  <ShieldCheck size={18} />
                </div>
              )}
            </div>
          </Checkbox>
        </div>

        <div className="flex items-center shrink-0 w-full ">
          <div
            onClick={previous}
            className="hover:bg-muted cursor-pointer h-full flex items-center px-1 mr-2"
          >
            <ChevronLeft size={18} />
          </div>
          <Checkbox
            checked={canEditAgent}
            onToggle={toggleCanEdit}
            className="w-full"
          >
            <div>can edit agent</div>
          </Checkbox>

          <div
            onClick={(e) => doToggle()}
            className="hover:bg-muted cursor-pointer h-full flex items-center px-1"
          >
            <ArrowRight size={18} />
          </div>
        </div>
      </div>
    </div>
  );
}
