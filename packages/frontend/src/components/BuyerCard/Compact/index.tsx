import React from 'react';
import { AgentD<PERSON>, AgentStatus, AgentCallType } from '@/lib/Agent/types';
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { PhoneIcon, Tag } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import AgentDropdownMenu from '../AgentDropdownMenu';
import AgentAvatar from '@/components/Avatars/Agent';
import EmotionBadge from '@/common/CreateBuyerForm/BotPreview/EmotionBadge';

interface ICompactCardProps {
  agent: AgentDto;
  className?: string;
  showCallIcon?: boolean;
  showMenu?: boolean; //shows/hides dropdown menu
  showAgentsTags?: boolean;
  isSelected?: boolean;
  includeAgentsTags?: boolean; //includes the agents tags in the card along the agent features
  onCardClick?: (agent: AgentDto) => void;
  hideEditBtn?: boolean;
  onShareWithTeam?: (agent: AgentDto) => void;
}

function CompactCard({
  agent,
  onCardClick,
  className,
  showCallIcon,
  showMenu,
  showAgentsTags,
  isSelected,
  includeAgentsTags,
  hideEditBtn,
  onShareWithTeam,
}: ICompactCardProps) {
  const _onCardClick = () => {
    if (onCardClick) {
      onCardClick(agent);
    }
  };

  const Icon =
    CALL_TYPE_TO_ICON?.[agent?.callType as keyof typeof CALL_TYPE_TO_ICON]
      ?.Icon;

  let bgSelectedStyle = {};
  if (isSelected) {
    bgSelectedStyle = {
      boxShadow:
        isSelected &&
        '-2px -2px 8px 0px rgba(50, 194, 160, 0.4), 1px 1px 6px 0px rgba(198, 247, 255, 0.4), 4px 2px 6px 0px rgba(49, 195, 146, 0.4)',
    };
  }

  let hoverClass = 'cursor-pointer hover:shadow-md';
  if (!onCardClick) {
    hoverClass = '';
  }

  return (
    <Card
      onClick={_onCardClick}
      className={cn(
        'text-sm shadow-sm transition-shadow duration-200',
        className,
        hoverClass,
      )}
      style={bgSelectedStyle}
    >
      <CardHeader className="py-4">
        <div className="flex flex-row items-stretch space-x-4">
          <div>
            {' '}
            {/* div is needed to position the dot on the avatar */}
            <div className="relative">
              <AgentAvatar
                className="w-[52px] h-[52px] relative"
                agent={agent}
              />
              <div
                className={cn(
                  'rounded-full p-1 w-4 h-4 border-white border-[3px] absolute bottom-0 right-0',
                  {
                    'bg-green-500': agent.status === AgentStatus.ACTIVE,
                    'bg-gray-500': agent.status === AgentStatus.INACTIVE,
                  },
                )}
              />
            </div>
          </div>

          <div className="flex-1">
            <CardTitle>
              {agent.firstName} {agent.lastName}
            </CardTitle>
            <CardDescription className="text-[0.76rem]">
              {agent.jobTitle} {'@  ' + agent.companyName}
            </CardDescription>
            <div>
              {!showAgentsTags && (agent.emotionalState || agent.gender) && (
                <div
                  className={cn('flex flex-wrap mt-2', {
                    'space-x-1': agent.emotionalState && agent.gender,
                    // (emotionalState && salesMethodology) ||
                    // (emotionalState && gender) ||
                    // (gender && salesMethodology),
                  })}
                >
                  {agent.callType && (
                    <Badge className="m-1" variant="secondary">
                      {Icon && <Icon className="mr-1 h-3 w-3" />}
                      {CALL_TYPE_OPTIONS.find(
                        (item) => item.value === agent.callType,
                      )?.label ||
                        (agent.callType === 'focus'
                          ? 'Focus Call'
                          : agent.callType)}
                    </Badge>
                  )}
                  {agent.callType != AgentCallType.FOCUS &&
                    (agent.emotionalState ? (
                      <EmotionBadge emotionalState={agent.emotionalState} />
                    ) : (
                      <Skeleton className={cn('w-16 h-6 mr-1')} />
                    ))}

                  {agent.bookRate && (
                    <Badge className="m-1" variant="default">
                      Book Rate:{' '}
                      {Number(agent?.bookRate).toLocaleString('en-US', {
                        style: 'percent',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 1,
                      })}
                    </Badge>
                  )}
                </div>
              )}

              {(includeAgentsTags || showAgentsTags) &&
                agent.tags?.map((tag) => (
                  <Badge
                    key={tag.id}
                    variant="default"
                    className="m-1 bg-teal-600"
                  >
                    <Tag size={12} className="mr-1" />
                    {tag.name}
                  </Badge>
                ))}
            </div>
          </div>
          {(showMenu || showCallIcon) && (
            <div className="flex flex-col">
              <>
                {showMenu && (
                  <div>
                    <AgentDropdownMenu
                      agent={agent}
                      hideEditBtn={hideEditBtn}
                      onShareWithTeam={onShareWithTeam}
                    />
                  </div>
                )}
              </>
              {showMenu && showCallIcon && <div className="flex-1"></div>}
              <>
                {showCallIcon && (
                  <div
                    className="rounded-lg items-center h-8 w-8 flex justify-center cursor-pointer text-white my-auto"
                    style={{
                      backgroundImage:
                        'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                    }}
                  >
                    <PhoneIcon className="h-4 w-4" />
                  </div>
                )}
              </>
            </div>
          )}
        </div>
      </CardHeader>
    </Card>
  );
}

export default React.memo(CompactCard);
