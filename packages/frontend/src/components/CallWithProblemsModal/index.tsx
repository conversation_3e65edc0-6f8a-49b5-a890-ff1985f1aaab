import { useState, useRef, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import {
  CloudUpload,
  DownloadIcon,
  Info,
  Loader2Icon,
  TriangleAlert,
  UploadIcon,
} from 'lucide-react';

import { Id, toast } from 'react-toastify';
import LinksManager from '@/lib/linksManager';

interface IProps {
  open: boolean;
  vapiId: string;
}
export default function CallWithProblemsModal({ open, vapiId }: IProps) {
  const router = useRouter();

  return (
    <Dialog open={open}>
      <DialogContent className="">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <TriangleAlert size={20} className={'mr-1'} />
            Call recovery in progress
          </DialogTitle>
        </DialogHeader>
        <div className="mt-6">
          <div>
            We apologize for the inconvenience, but we encountered an issue with
            this call.
          </div>
          <div className="mt-4">
            We are actively working to recover it as quickly as possible.
          </div>
          <div className="mt-4">
            If the problem is not resolved within a few minutes, please contact
            our support team.
          </div>
        </div>
        <DialogFooter>
          <Button
            variant={'default'}
            onClick={() => {
              router.push(LinksManager.trainingCalls());
            }}
            className="w-[70px]"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
