import React, { useState, useRef, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContentWithoutPortal as PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, Loader2Icon, FilterX, PlusIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon, LightningBoltIcon } from '@radix-ui/react-icons';
import { CallBlitzDto } from '@/lib/CallBlitz/types';
import useCallBlitzSearch from '@/hooks/useCallBlitzSearch';

interface ICallBlitzSelectProps {
  current: CallBlitzDto | null;
  onFiltersUpdated: (callBlitz: CallBlitzDto | null) => void;
  className?: string;
  onCreateNewClick: () => void;
  createdCallBlitz?: string;
}

function CallBlitzSelect({
  current,
  onFiltersUpdated,
  className,
  onCreateNewClick,
  createdCallBlitz,
}: ICallBlitzSelectProps) {
  const defaultNumberOfResults = 10;

  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );

  const [searchString, setSearchString] = useState<string>('');

  const [open, setOpen] = useState(false);

  const { data: allCallBlitzes, isLoading: isLoadingCallBlitzes } =
    useCallBlitzSearch(0, numberOfResults, searchString, [], true);

  const [callBlitzes, setCallBlitzes] = useState<CallBlitzDto[]>([]);

  const { noMoreCallBlitzes, currentlySelectedCallBlitz } = useMemo(() => {
    if (isLoadingCallBlitzes) {
      return {
        noMoreCallBlitzes: false,
        currentlySelectedCallBlitz: null,
      };
    }
    const callBlitzes = allCallBlitzes || [];
    setCallBlitzes(callBlitzes);
    const noMoreCallBlitzes = callBlitzes.length < numberOfResults;
    const currentlySelectedCallBlitz =
      (current && callBlitzes.find((val) => val.id === current.id)) || null;
    return { noMoreCallBlitzes, currentlySelectedCallBlitz };
  }, [allCallBlitzes?.length, numberOfResults, current, isLoadingCallBlitzes]);

  const [selected, setSelected] = useState<CallBlitzDto | null>(
    currentlySelectedCallBlitz,
  );
  const [prevSelected, setPrevSelected] = useState<CallBlitzDto | null>(
    currentlySelectedCallBlitz,
  );

  if (
    JSON.stringify(currentlySelectedCallBlitz) != JSON.stringify(prevSelected)
  ) {
    setPrevSelected(currentlySelectedCallBlitz);
    setSelected(currentlySelectedCallBlitz);
  }

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const resetAgentsForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetAgentsForSearch.current = false;
      setNumberOfResults(defaultNumberOfResults);
    }, 200);

    setSearchString(s);
  };

  const clearSelection = () => {
    setSelected(null);
    if (onFiltersUpdated) {
      onFiltersUpdated(null);
    }
    setOpen(false);
  };

  const selectItem = (_id: string) => {
    const id = parseInt(_id);

    let newSelection: CallBlitzDto | null = null;

    if (selected && selected.id === id) {
      // If clicking the same item, deselect it
      newSelection = null;
    } else {
      // Select the new item
      const selectedDto = callBlitzes.find((a) => a.id === id) || null;
      newSelection = selectedDto;
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(newSelection);
    }
    setSelected(newSelection);
    setOpen(false);
  };

  const loadMore = () => {
    setNumberOfResults(numberOfResults + 15);
  };

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
    >
      <PopoverTrigger asChild className="w-[400px]">
        <div>
          <div className="text-sm font-medium mb-2">Select a call blitz</div>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn('border w-full', className)}
            style={{ justifyContent: 'start' }}
          >
            <LightningBoltIcon className="mr-2 h-4 w-4" />
            {selected || createdCallBlitz ? (
              <>
                <span className="truncate">
                  {createdCallBlitz || selected?.name}
                </span>
              </>
            ) : (
              'Select Call Blitz'
            )}
            <div className="flex-1"></div>
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="p-0 max-h-[50vh] overflow-y-auto"
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={`Search call blitz...`}
            className="h-9"
            value={searchString}
            onValueChange={filterResults}
          />
          <CommandList>
            <CommandGroup>
              {callBlitzes.map((v) => (
                <CommandItem
                  key={v.id?.toString()}
                  value={v.id?.toString()}
                  className="flex items-center gap-2 p-1 cursor-pointer "
                  onSelect={() => selectItem(v.id?.toString() || '')}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      selected && selected.id === v.id
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex space-x-2 items-center">
                    <div className="capitalize text-sm">{v.name}</div>
                  </div>
                </CommandItem>
              ))}
              {!noMoreCallBlitzes && (
                <CommandItem
                  onSelect={loadMore}
                  className="justify-center text-center aria-selected:bg-transparent hover:cursor-pointer"
                >
                  {isLoadingCallBlitzes ? 'Loading more...' : 'More...'}
                  {isLoadingCallBlitzes && (
                    <Loader2Icon className="animate-spin h-4 w-4" />
                  )}
                </CommandItem>
              )}
            </CommandGroup>
          </CommandList>

          <CommandSeparator />
          <CommandGroup>
            <div className="flex flex-row flex-1">
              {selected && (
                <CommandItem
                  onSelect={clearSelection}
                  className="justify-center hover:cursor-pointer flex-1"
                >
                  <FilterX className="h-4 w-4 mr-2" />
                  Clear selection
                </CommandItem>
              )}
              <CommandItem
                className="justify-center hover:cursor-pointer flex-1"
                onSelect={() => {
                  onCreateNewClick();
                }}
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create new
              </CommandItem>
            </div>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export default React.memo(CallBlitzSelect);
