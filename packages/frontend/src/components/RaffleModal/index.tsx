import {
  CopyCheckIcon,
  LockIcon,
  PhoneIcon,
  ShareIcon,
  TicketIcon,
} from 'lucide-react';
import Image from 'next/image';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';

interface IRaffleModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  locked: boolean;
}

function RaffleModal({ modalOpen, setModalOpen, locked }: IRaffleModalProps) {
  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent
        className={'close-btn text-white'}
        style={{
          backgroundImage:
            'linear-gradient(to right, #30B6E0 100%, #32C490 0%)',
        }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {locked ? (
              <LockIcon className="mr-2 opacity-70" />
            ) : (
              <TicketIcon className="mr-2 opacity-70" />
            )}{' '}
            {locked
              ? 'Make 1 call to unlock your invite link'
              : 'Copied your invite link to the clipboard!'}
          </DialogTitle>
          <DialogDescription className="text-white">
            For <strong>every</strong> friend or teammate that makes at least 1
            call with Hyperbound using your invite link, you get{' '}
            <strong>1 FREE raffle ticket</strong> for a chance to win one of the
            following.
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center space-y-2 py-4">
          <div className="flex space-x-2">
            <div className="bg-white/20 hover:shadow-md hover:transition-shadow hover:duration-300 p-4 w-48 h-48 flex items-center justify-center rounded-xl">
              <div className="flex flex-col items-center">
                <Image
                  src="/images/airpods-max.png"
                  alt="airpods max"
                  width={100}
                  height={100}
                />
                <h3 className="text-md text-center font-medium">
                  Apple AirPods Max
                </h3>
              </div>
            </div>
            <div className="bg-white/20 hover:shadow-md hover:transition-shadow hover:duration-300 p-4 w-48 h-48 flex items-center justify-center rounded-xl">
              <div className="flex flex-col items-center">
                <Image
                  src="/images/chatgpt.png"
                  alt="chatgpt plus"
                  width={100}
                  height={100}
                />
                <h3 className="text-md text-center font-medium">
                  1 year free ChatGPT Plus
                </h3>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <div className="bg-white/20 hover:shadow-md hover:transition-shadow hover:duration-300 p-4 w-48 h-48 flex items-center justify-center rounded-xl">
              <div className="flex flex-col items-center">
                <Image
                  src="/images/square-black-logo.svg"
                  alt="hyperbound discount"
                  width={80}
                  height={80}
                  className="rounded-2xl"
                />
                <h3 className="text-md text-center font-medium mt-3">
                  1 year discount for Hyperbound
                </h3>
              </div>
            </div>
            <div className="bg-white/20 hover:shadow-md hover:transition-shadow hover:duration-300 p-4 w-48 h-48 flex items-center justify-center rounded-xl">
              <div className="flex flex-col items-center">
                <Image
                  src="/images/square-black-logo.svg"
                  alt="hyperbound swag"
                  width={80}
                  height={80}
                  className="rounded-2xl"
                />
                <h3 className="text-md text-center font-medium mt-3">
                  Hyperbound swag
                </h3>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={() => setModalOpen(false)} variant={'secondary'}>
            {locked ? (
              <PhoneIcon className="w-4 h-4 mr-2" />
            ) : (
              <ShareIcon className="w-4 h-4 mr-2" />
            )}{' '}
            {locked ? 'Make a call' : 'Share invite'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default RaffleModal;
