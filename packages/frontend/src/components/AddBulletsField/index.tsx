import { Trash2Icon } from 'lucide-react';
import React, { useState } from 'react';
import { ControllerRenderProps } from 'react-hook-form';
import { PaperPlaneIcon } from '@radix-ui/react-icons';
import TextInputWithIcon from '../ui/textarea/textarea-with-icon';
import { StepOneData } from '@/lib/Agent/types';

interface IAddBulletsFieldProps {
  field: ControllerRenderProps<
    StepOneData,
    | 'companyDetails'
    | 'companyOrgStructure'
    | 'goals'
    | 'objections'
    | 'opinions'
    | 'personalDetails'
  >;
  placeholder: string;
}

function AddBulletsField({ field, placeholder }: IAddBulletsFieldProps) {
  const [text, setText] = useState<string>('');
  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    e.stopPropagation(); // Stop the event from propagating to the parent components
    setText(e.target.value);
  };

  const onAddBullet = () => {
    const newText = text.trim();
    if (newText) {
      field.onChange([...field.value, newText]);
      setText('');
    }
  };

  const onEditBullet = (index: number, value: string) => {
    const newValue = [...field.value];
    newValue[index] = value;
    field.onChange(newValue);
  };

  const onRemoveBullet = (index: number) => {
    field.onChange(field.value.filter((_: string, i: number) => i !== index));
    field.onBlur();
  };

  const onKeyUp = (
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onAddBullet();
    }
  };

  return (
    <div className="space-y-3" onBlur={field.onBlur}>
      {field?.value.map((item: string, i: number) => (
        <TextInputWithIcon
          key={i}
          placeholder={placeholder}
          value={item}
          onChange={(e) => onEditBullet(i, e.target.value)}
          onKeyUp={(e) => onKeyUp(e)}
          icon={Trash2Icon}
          onIconClick={() => onRemoveBullet(i)}
        />
      ))}
      <TextInputWithIcon
        placeholder={placeholder}
        value={text}
        onChange={onChange}
        onKeyUp={onKeyUp}
        icon={PaperPlaneIcon}
        onIconClick={onAddBullet}
      />
    </div>
  );
}

export default AddBulletsField;
