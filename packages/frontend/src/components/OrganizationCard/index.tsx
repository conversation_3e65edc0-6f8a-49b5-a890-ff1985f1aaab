/* eslint-disable @typescript-eslint/no-explicit-any */
import { Card, CardHeader, CardTitle } from '../ui/card';
import { OrganizationDto } from '@/lib/Organization/types';
import { Switch } from '@/components/ui/switch';
import OrganizationAvatar from './components/OrganizationAvatar';
import { cn } from '@/lib/utils';

interface IOrganizationCard {
  org: OrganizationDto;
  orgMetadata?: { [key: string]: any };
  className?: string;
  onClick?: () => void;
  showCanEdit?: boolean;
  canEdit?: boolean;
  onToggleCanEdit?: (canEdit: boolean) => void;
}

export default function OrganizationCard({
  org,
  className,
  onClick,
  canEdit,
  showCanEdit,
  onToggleCanEdit,
  orgMetadata,
}: IOrganizationCard) {
  return (
    <Card
      onClick={onClick}
      className={cn(
        'text-sm shadow-sm transition-shadow duration-200 cursor-pointer hover:shadow-md',
        className,
      )}
    >
      <CardHeader className="py-4">
        <div className="flex flex-row items-stretch space-x-4">
          <div className="relative">
            <OrganizationAvatar
              organization={org}
              orgLogoUrl={orgMetadata?.logo}
            />
          </div>

          <div className="flex flex-1 flex-col justify-between">
            <div className="flex-1">
              <CardTitle>{org.name}</CardTitle>
            </div>
            {showCanEdit && (
              <div className="flex flex-row items-center justify-start mt-4">
                <Switch
                  checked={canEdit}
                  onCheckedChange={(c) => onToggleCanEdit?.(c)}
                  onClick={(e) => e.stopPropagation()}
                />
                <div className="text-xs font-semibold ml-2">Can edit agent</div>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
    </Card>
  );
}
