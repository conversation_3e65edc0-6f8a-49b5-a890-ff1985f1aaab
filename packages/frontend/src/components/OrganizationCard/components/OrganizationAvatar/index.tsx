import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { OrganizationDto } from '@/lib/Organization/types';
import { useEffect, useMemo, useState } from 'react';

interface IOrganizationAvatar {
  organization: OrganizationDto;
  className?: string;
  orgLogoUrl?: string;
}

export default function OrganizationAvatar({
  organization,
  className,
  orgLogoUrl,
}: IOrganizationAvatar) {
  const [isLoading, setIsLoading] = useState(!!organization?.logo);
  const [imageError, setImageError] = useState(false);
  const imageUrl = useMemo(() => {
    return orgLogoUrl || organization?.logo || undefined;
  }, [organization?.logo, orgLogoUrl]);

  const fallbackText = useMemo(() => {
    return organization?.name.charAt(0).toUpperCase() || 'B';
  }, [organization?.name]);

  useEffect(() => {
    if (!imageUrl) return;

    setIsLoading(true);
    setImageError(false);

    const img = new Image();
    img.src = imageUrl;

    img.onload = () => {
      setIsLoading(false);
      setImageError(false);
    };

    img.onerror = () => {
      setIsLoading(false);
      setImageError(true);
    };
  }, [imageUrl]);

  return (
    <Avatar className={className}>
      {isLoading ? (
        <Skeleton className="w-10 h-10 rounded-full border-0" />
      ) : imageError || !imageUrl ? (
        <AvatarFallback className="text-lg">{fallbackText}</AvatarFallback>
      ) : (
        <AvatarImage key={imageUrl} src={imageUrl} alt="Organization Avatar" />
      )}
    </Avatar>
  );
}
