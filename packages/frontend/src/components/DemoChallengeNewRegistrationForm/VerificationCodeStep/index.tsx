import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import DemoService from '@/lib/Demo';
import { DemoInboundFormResponseDto } from '@/lib/Demo/types';
import Analytics from '@/system/Analytics';
import { DemoInboundFormEvents } from '@/system/Analytics/events/DemoInboundFormEvents';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Loader2Icon } from 'lucide-react';
import { useRef } from 'react';
import { useForm } from 'react-hook-form';
import { Id, ToastContainer, toast } from 'react-toastify';
import { z } from 'zod';

interface IVerificationCodeStepProps {
  onSubmitted: () => void;
  submitText?: string;
  demoInboundFormResponse?: DemoInboundFormResponseDto;
}

const verificationCodeFormSchema = z.object({
  code: z.string(),
});

function VerificationCodeStep({
  onSubmitted,
  submitText,
  demoInboundFormResponse,
}: IVerificationCodeStepProps) {
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  // const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const verificationCodeForm = useForm<
    z.infer<typeof verificationCodeFormSchema>
  >({
    resolver: zodResolver(verificationCodeFormSchema),
    defaultValues: {
      code: '',
    },
  });

  const verifyEmailVerificationCodeMutation = useMutation({
    mutationFn: DemoService.verifyEmailVerificationCode,
    onSuccess: async (demoInboundFormResponse, params) => {
      // Analytics.track(DemoInboundFormEvents.SUBMITTED_SUCCESS, {
      //   ...params,
      //   id: demoInboundFormResponse.id,
      // });
      // localStorage?.setItem(
      //   process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY as string,
      //   JSON.stringify(demoInboundFormResponse)
      // );
      // await queryClient.refetchQueries({
      //   queryKey: [process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY],
      // });
      await queryClient.refetchQueries({
        queryKey: [process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY],
      });
      await queryClient.invalidateQueries({
        queryKey: ['topLeaderboard'],
      });
      await queryClient.invalidateQueries({
        queryKey: ['leaderboard'],
      });
      onSubmitted();
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'Incorrect verification code. Please try again.',
          {
            autoClose: 1000,
          },
        );
      }
      console.log('ERROR verifying your code', error);
      Analytics.track(DemoInboundFormEvents.SUBMITTED_ERROR, {
        ...params,
        err: 'Error verifying your code',
      });
    },
  });

  async function onVerificationCodeSubmit(
    values: z.infer<typeof verificationCodeFormSchema>,
  ) {
    verifyEmailVerificationCodeMutation.mutate({
      demoInboundFormResponseId: demoInboundFormResponse?.id as number,
      email: demoInboundFormResponse?.email || '',
      code: values.code,
    });
  }
  return (
    <Form {...verificationCodeForm}>
      <form
        onSubmit={verificationCodeForm.handleSubmit(onVerificationCodeSubmit)}
        className="space-y-8"
      >
        <FormField
          control={verificationCodeForm.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Check your email for a verification code</FormLabel>
              <FormControl>
                <InputOTP
                  maxLength={6}
                  name={field.name}
                  value={field.value}
                  onChange={(value) => {
                    field.onChange(value);
                    if (value.length === 6) {
                      onVerificationCodeSubmit(
                        verificationCodeForm.getValues(),
                      );
                    }
                  }}
                  onBlur={() => {
                    console.log(field.value, field, field.name);
                    Analytics.track(DemoInboundFormEvents.FIELD_COMPLETED, {
                      ...verificationCodeForm.getValues(),
                      blurField: field.name,
                    });
                  }}
                  render={({ slots }) => (
                    <>
                      <InputOTPGroup>
                        {slots.slice(0, 3).map((slot, index) => (
                          <InputOTPSlot key={index} {...slot} />
                        ))}{' '}
                      </InputOTPGroup>
                      <InputOTPGroup>
                        {slots.slice(3).map((slot, index) => (
                          <InputOTPSlot key={index + 3} {...slot} />
                        ))}
                      </InputOTPGroup>
                    </>
                  )}
                />
              </FormControl>
              <FormDescription>
                * Check your spam folder if you don&apos;t see it in your inbox
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          disabled={
            verifyEmailVerificationCodeMutation.isPending ||
            Object.values(verificationCodeForm.formState.dirtyFields).length < 1
          } // here
          className="float-right text-primary"
          variant={'outline'}
        >
          {verifyEmailVerificationCodeMutation.isPending ? (
            <Loader2Icon className="animate-spin" />
          ) : (
            submitText
          )}
        </Button>
      </form>
      <ToastContainer />
    </Form>
  );
}

export default VerificationCodeStep;
