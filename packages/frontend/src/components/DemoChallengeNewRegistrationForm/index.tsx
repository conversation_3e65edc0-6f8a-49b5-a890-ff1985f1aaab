'use client';

import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import AffiliateService from '@/lib/Affiliate';
import { AffiliateDto } from '@/lib/Affiliate/types';
import DemoService from '@/lib/Demo';
import Analytics from '@/system/Analytics';
import { DemoInboundFormEvents } from '@/system/Analytics/events/DemoInboundFormEvents';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Loader2Icon, PhoneIcon } from 'lucide-react';
import { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Id, ToastContainer, toast } from 'react-toastify';
import VerificationCodeStep from './VerificationCodeStep';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';

const salesTeamSizeOptions = [
  {
    label: '1-5 reps',
    value: '1-5 reps',
  },
  {
    label: '5-10 reps',
    value: '5-10 reps',
  },
  {
    label: '10-30 reps',
    value: '10-30 reps',
  },
  {
    label: '30-80 reps',
    value: '30-80 reps',
  },
  {
    label: '81+ reps',
    value: '81+ reps',
  },
];

export const personalEmailDomains = [
  'gmail.com',
  'yahoo.com',
  'hotmail.com',
  'outlook.com',
  'trashmail.com',
  'trash-mail.com',
];

const linkedinRegex =
  /^https?:\/\/((www|\w\w)\.)?linkedin.com\/((in\/[^/]+\/?)|(pub\/[^/]+\/((\w|\d)+\/?){3}))$/;

const formSchema = z.object({
  name: z.string(),
  email: z.string().email('This is not a valid email'),
  // .refine((email) => {
  //   const domain = email.split("@")[1];
  //   // Define a list of personal email domains to blacklist
  //   return !personalEmailDomains.includes(domain);
  // }, "Please use your work email"),
  numberOfSalesReps: z
    .string()
    .min(1, 'This field is required')
    .regex(/^(?!"Choose an option"$).*/, 'This field is required'),
  attribution: z.string().min(1, 'This field is required'),
  linkedinUrl: z
    .string()
    .url('This is not a valid URL')
    .refine((url) => {
      return linkedinRegex.test(url);
    }, 'This is not a valid LinkedIn URL')
    .optional(),
});

interface IDemoChallengeNewRegistrationFormProps {
  onSubmit: () => void;
  agentVapiId?: string;
  submitText?: string;
}

export function DemoChallengeNewRegistrationForm({
  onSubmit: onSubmitted,
  agentVapiId,
  submitText = "Let's go, start call",
}: IDemoChallengeNewRegistrationFormProps) {
  const errorToastId = useRef<Id | null>(null);
  const queryClient = useQueryClient();
  const inviteCode = queryClient.getQueryData(['hbInvite']) as string;
  const [step, setStep] = useState(1);
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const [demoInboundFormResponse, setDemoInboundFormResponse] = useState(null);

  // 1. Define your form.
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: async () => {
      if (inviteCode) {
        let fromAffiliate = queryClient.getQueryData([
          'fromAffiliate',
          inviteCode,
        ]) as AffiliateDto;
        if (!fromAffiliate) {
          fromAffiliate =
            await AffiliateService.getAffiliateByInviteCode(inviteCode);
          queryClient.setQueryData(
            ['fromAffiliate', inviteCode],
            fromAffiliate,
          );
        }
        return {
          name: '',
          email: '',
          numberOfSalesReps: '',
          attribution:
            fromAffiliate?.inviteCode === 'hyperbound-website' ||
            fromAffiliate?.inviteCode === 'hb-landing-page'
              ? ''
              : fromAffiliate?.name,
          linkedinUrl: '',
        };
      } else {
        return {
          name: hbDemoInboundForm?.name || '',
          email: hbDemoInboundForm?.email || '',
          numberOfSalesReps: hbDemoInboundForm?.sizeOfSalesTeam || '',
          attribution: hbDemoInboundForm?.attribution || '',
          linkedinUrl: hbDemoInboundForm?.linkedinUrl || '',
        };
      }
    },
  });

  const createInboundFormResponseMutation = useMutation({
    mutationFn: DemoService.createInboundFormResponse,
    onSuccess: async (demoInboundFormResponse, params) => {
      Analytics.track(DemoInboundFormEvents.SUBMITTED_SUCCESS, {
        ...params,
        id: demoInboundFormResponse.id,
      });
      setDemoInboundFormResponse(demoInboundFormResponse);
      if (!demoInboundFormResponse?.presClubChallengeRegistered) {
        setStep(2);
      } else {
        localStorage?.setItem(
          process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY as string,
          JSON.stringify(demoInboundFormResponse),
        );
        await queryClient.refetchQueries({
          queryKey: [process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY],
        });
        queryClient.invalidateQueries({
          queryKey: ['demoAgents'],
        });
        queryClient.invalidateQueries({
          queryKey: ['demoAgentByVapiId'],
        });
        onSubmitted();
      }
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving your response. Please try again.',
        );
      }
      console.log('ERROR saving inbound response form', error);
      Analytics.track(DemoInboundFormEvents.SUBMITTED_ERROR, {
        ...params,
        err: 'Error saving demo inbound form response',
      });
    },
  });

  const updateInboundFormResponseMutation = useMutation({
    mutationFn: DemoService.updateInboundFormResponseById,
    onSuccess: async (demoInboundFormResponse, params) => {
      Analytics.track(DemoInboundFormEvents.SUBMITTED_SUCCESS, {
        ...params,
        id: demoInboundFormResponse.id,
      });
      console.log('DEMOINBOUND', demoInboundFormResponse);
      setDemoInboundFormResponse(demoInboundFormResponse);
      if (!demoInboundFormResponse?.presClubChallengeRegistered) {
        setStep(2);
      } else {
        localStorage?.setItem(
          process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY as string,
          JSON.stringify(demoInboundFormResponse),
        );
        await queryClient.refetchQueries({
          queryKey: [process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY],
        });
        queryClient.invalidateQueries({
          queryKey: ['demoAgents'],
        });
        queryClient.invalidateQueries({
          queryKey: ['demoAgentByVapiId'],
        });
        onSubmitted();
      }
      //   onSubmitted();
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving your response. Please try again.',
        );
      }
      console.log('ERROR saving inbound response form', error);
      Analytics.track(DemoInboundFormEvents.SUBMITTED_ERROR, {
        ...params,
        err: 'Error saving demo inbound form response',
      });
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    const fromAffiliate = queryClient.getQueryData([
      'fromAffiliate',
      inviteCode,
    ]) as AffiliateDto;

    if (hbDemoInboundForm?.id) {
      // already has an account, so just update the account
      updateInboundFormResponseMutation.mutate({
        id: hbDemoInboundForm.id,
        name: values.name?.trim(),
        email: values.email?.trim()?.toLowerCase(),
        sizeOfSalesTeam: values.numberOfSalesReps?.trim(),
        attribution: values.attribution?.trim(),
        linkedinUrl: values.linkedinUrl?.trim() || '',
      });
    } else {
      createInboundFormResponseMutation.mutate({
        vapiId: agentVapiId,
        name: values.name?.trim(),
        email: values.email?.trim()?.toLowerCase(),
        sizeOfSalesTeam: values.numberOfSalesReps?.trim(),
        attribution: values.attribution?.trim(),
        inviteCode: fromAffiliate?.inviteCode,
        linkedinUrl: values.linkedinUrl?.trim() || '',
      });
    }
  }

  const afterSubmit = async () => {
    localStorage?.setItem(
      process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY as string,
      JSON.stringify(demoInboundFormResponse),
    );
    await queryClient.refetchQueries({
      queryKey: [process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY],
    });
    queryClient.invalidateQueries({
      queryKey: ['demoAgents'],
    });
    queryClient.invalidateQueries({
      queryKey: ['demoAgentByVapiId'],
    });

    onSubmitted();
  };

  if (step === 2) {
    return (
      <VerificationCodeStep
        onSubmitted={afterSubmit}
        submitText={submitText}
        demoInboundFormResponse={demoInboundFormResponse!}
      />
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full name *</FormLabel>
              <FormControl>
                <Input
                  required
                  placeholder="John Doe"
                  {...field}
                  onBlur={(e) => {
                    Analytics.track(DemoInboundFormEvents.FIELD_COMPLETED, {
                      ...form.getValues(),
                      blurField: field.name,
                    });
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="linkedinUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Your LinkedIn Url *</FormLabel>
              <FormControl>
                <Input
                  placeholder="https://www.linkedin.com/in/john-doe/"
                  {...field}
                  onBlur={() => {
                    Analytics.track(DemoInboundFormEvents.FIELD_COMPLETED, {
                      ...form.getValues(),
                      registerForChallenge: true,
                      blurField: field.name,
                    });
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email *</FormLabel>
              <FormControl>
                <Input
                  required
                  placeholder="<EMAIL>"
                  {...field}
                  onBlur={() => {
                    Analytics.track(DemoInboundFormEvents.FIELD_COMPLETED, {
                      ...form.getValues(),
                      blurField: field.name,
                    });
                  }}
                />
              </FormControl>
              <FormDescription className="font-semibold text-primary">
                * To be eligible for the challenge, you will be required to
                verify your email
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="numberOfSalesReps"
          render={({ field }) => (
            <FormItem>
              <FormLabel>What is the size of your sales team? *</FormLabel>
              <Select
                required
                onValueChange={(e) => {
                  field.onChange(e);
                  Analytics.track(DemoInboundFormEvents.FIELD_COMPLETED, {
                    ...form.getValues(),
                    blurField: field.name,
                  });
                }}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an option" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {salesTeamSizeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        {(inviteCode === 'hyperbound-website' ||
          inviteCode === 'hb-landing-page' ||
          !queryClient.getQueryData(['fromAffiliate', inviteCode])) && (
          <FormField
            control={form.control}
            name="attribution"
            render={({ field }) => (
              <FormItem>
                <FormLabel>How&apos;d you hear about us? *</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Sam from YCombinator"
                    {...field}
                    onBlur={() => {
                      Analytics.track(DemoInboundFormEvents.FIELD_COMPLETED, {
                        ...form.getValues(),
                        blurField: field.name,
                      });
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
        <Button
          type="submit"
          disabled={
            createInboundFormResponseMutation.isPending ||
            updateInboundFormResponseMutation.isPending
          }
          className="float-right text-primary"
          variant={'outline'}
        >
          {createInboundFormResponseMutation.isPending ||
          updateInboundFormResponseMutation.isPending ? (
            <Loader2Icon className="animate-spin" />
          ) : (
            submitText
          )}
        </Button>
      </form>
      <ToastContainer />
    </Form>
  );
}
