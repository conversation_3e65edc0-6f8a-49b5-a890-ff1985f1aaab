import { CreatePlaylistModal } from '@/common/CreatePlaylist/CreatePlaylistModal';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import usePartnerByCachedPartnerId from '@/hooks/usePartnerByCachedPartnerId';
import usePlaylists from '@/hooks/usePlaylists';
import useUserSession from '@/hooks/useUserSession';
import CallService from '@/lib/Call';
import { CallDto } from '@/lib/Call/types';
import { PartnerPermission } from '@/lib/Partner/types';
import PlaylistService from '@/lib/Playlist';
import { RoleEnum } from '@/lib/User/types';
import LinksManager from '@/lib/linksManager';
import { AppPermissions } from '@/lib/permissions';
import { cn, formatTranscript } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { CallEvents } from '@/system/Analytics/events/CallEvents';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { DotsVerticalIcon } from '@radix-ui/react-icons';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  CopyIcon,
  DownloadIcon,
  LayoutListIcon,
  LockIcon,
  PlusIcon,
  RefreshCcwIcon,
  Trash2Icon,
  UsersIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Id, toast } from 'react-toastify';

interface ICallActionsDropdownProps {
  call: CallDto;
  location: 'calls_page' | 'individual_call_page';
}

function CallActionsDropdown({ call, location }: ICallActionsDropdownProps) {
  const authInfo = useAuthInfo();
  const queryClient = useQueryClient();
  const errorToastId = React.useRef<Id | null>(null);
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const org = useActiveOrg();
  const router = useRouter();
  const { data: playlists } = usePlaylists();
  const [modalOpen, setModalOpen] = useState(false);
  const {
    isInIframe,
    isCompetitionOrg,
    onlyAdminsCanExportCalls,
    canAccess,
    canRescoreCalls,
    canViewMedia,
  } = useUserSession();
  const { data: partner } = usePartnerByCachedPartnerId();
  const callId = call?.providerCallId
    ? call?.providerCallId
    : call?.vapiId
      ? call?.vapiId
      : '';

  const rescoreCall = async () => {
    if (call?.vapiId) {
      const r = await CallService.rescoreCall(call?.vapiId);
      if (r) {
        errorToastId.current = toast.success(
          'Call rescore started. It may take a few seconds.',
        );
      }
    }
  };

  const deleteDemoCallMutation = useMutation({
    mutationFn: CallService.deleteDemoCall,
    onSuccess: (_, params) => {
      Analytics.track(CallEvents.DELETE_SUCCESS, {
        ...params,
        from: location,
      });
      queryClient.invalidateQueries({ queryKey: ['demoCalls'] });
      router.push('/buyers');
    },
    onError: (_, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error deleting the call');
        errorToastId.current = toast.error(
          'There was an error deleting the call. Please try again.',
        );
      }
      Analytics.track(CallEvents.DELETE_ERROR, {
        ...params,
        from: location,
      });
    },
  });

  const deleteCallMutation = useMutation({
    mutationFn: CallService.deleteCall,
    onSuccess: (_, params) => {
      Analytics.track(CallEvents.DELETE_SUCCESS, {
        ...params,
        from: location,
      });
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      router.push('/buyers');
    },
    onError: (_, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error deleting the call');
        errorToastId.current = toast.error(
          'There was an error deleting the call. Please try again.',
        );
      }
      Analytics.track(CallEvents.DELETE_ERROR, {
        ...params,
        from: location,
      });
    },
  });

  const addCallToPlaylistMutation = useMutation({
    mutationFn: PlaylistService.addCallToPlaylist,
    onSuccess: (data) => {
      // Analytics.track(CallEvents.DELETE_SUCCESS, {
      //   ...params,
      //   from: location,
      // });
      queryClient.invalidateQueries({ queryKey: ['playlists'] });
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      errorToastId.current = toast.success(`Added call to ${data.name}`, {
        position: 'bottom-center',
      });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error adding the call to the playlist');
        errorToastId.current = toast.error(
          'There was an error adding the call to the playlist. Please try again.',
        );
      }
      // Analytics.track(CallEvents.DELETE_ERROR, {
      //   ...params,
      //   from: location,
      // });
    },
  });

  const removeCallFromPlaylistMutation = useMutation({
    mutationFn: PlaylistService.removeCallFromPlaylist,
    onSuccess: (data) => {
      // Analytics.track(CallEvents.DELETE_SUCCESS, {
      //   ...params,
      //   from: location,
      // });
      queryClient.invalidateQueries({ queryKey: ['playlists'] });
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      errorToastId.current = toast.success(`Removed call from ${data.name}`, {
        position: 'bottom-center',
      });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error removing the call from the playlist');
        errorToastId.current = toast.error(
          'There was an error removing the call from the playlist. Please try again.',
        );
      }
      // Analytics.track(CallEvents.DELETE_ERROR, {
      //   ...params,
      //   from: location,
      // });
    },
  });

  const onDownloadAudioClick = async () => {
    const callId = call?.providerCallId ? call?.providerCallId : call?.vapiId;
    if (!callId) {
      return;
    }
    const audioRecodingBlob = await CallService.getAudioCall(callId);
    if (audioRecodingBlob) {
      const url = URL.createObjectURL(audioRecodingBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audio-${callId}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="p-0 rounded-full">
            <span className="sr-only">Open menu</span>
            <DotsVerticalIcon className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <TooltipProvider
            disableHoverableContent={authInfo?.isLoggedIn}
            delayDuration={50}
          >
            <Tooltip>
              <TooltipTrigger disabled={!authInfo.isLoggedIn} asChild>
                <span tabIndex={0}>
                  {!isInIframe && (
                    <DropdownMenuSub>
                      <DropdownMenuSubTrigger
                        disabled={!authInfo?.isLoggedIn}
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                        className={cn({
                          'text-[#b9b9b9]': !authInfo?.isLoggedIn,
                        })}
                      >
                        <PlusIcon
                          className={cn('w-4 h-4 mr-2 text-muted-foreground', {
                            'text-[#b9b9b9]': !authInfo?.isLoggedIn,
                          })}
                        />
                        <div
                          className={cn('flex space-x-2', {
                            'text-[#b9b9b9]': !authInfo?.isLoggedIn,
                          })}
                        >
                          <p>Add to playlist</p>
                          {!authInfo?.isLoggedIn && (
                            <LockIcon className="w-4 h-4 text-[#b9b9b9]" />
                          )}
                        </div>
                      </DropdownMenuSubTrigger>
                      <DropdownMenuPortal>
                        <DropdownMenuSubContent className="w-64">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              setModalOpen(true);
                            }}
                          >
                            <PlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                            <span>New playlist</span>
                          </DropdownMenuItem>
                          {(playlists || [])?.length > 0 && (
                            <>
                              <DropdownMenuSeparator />
                            </>
                          )}
                          {(playlists || []).map((playlist) => {
                            const callInPlaylist = !!playlist.calls?.find(
                              (c) => c?.callId === call?.id,
                            );

                            return (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();

                                  if (callInPlaylist) {
                                    removeCallFromPlaylistMutation.mutate({
                                      callId: call.id,
                                      id: playlist.id,
                                    });
                                  } else {
                                    addCallToPlaylistMutation.mutate({
                                      callId: call.id,
                                      id: playlist.id,
                                    });
                                  }
                                }}
                                key={playlist.id}
                              >
                                <span>{playlist.name}</span>
                                {playlist.shared && (
                                  <UsersIcon className="w-4 h-4 ml-2 text-muted-foreground" />
                                )}
                                {/* {callInPlaylist && ( */}
                                <Checkbox
                                  className="w-4 h-4 ml-auto"
                                  checked={callInPlaylist}
                                />
                                {/* <CheckIcon className="w-4 h-4 ml-auto" />
                      )} */}
                              </DropdownMenuItem>
                            );
                          })}
                        </DropdownMenuSubContent>
                      </DropdownMenuPortal>
                    </DropdownMenuSub>
                  )}
                </span>
              </TooltipTrigger>
              {/* <TooltipContent side="right">
                <p>Book a demo to create playlists</p>
              </TooltipContent> */}
            </Tooltip>
          </TooltipProvider>

          {!isInIframe && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                const url = `${
                  window.location.host
                }${LinksManager.trainingCalls(callId)}`;
                Analytics.track(CallEvents.COPY_URL_CLICKED, {
                  id: call.id,
                  vapiId: call.vapiId,
                  agentId: call.agent?.id,
                  orgId: call.orgId,
                  url,
                });
                navigator.clipboard.writeText(url);
              }}
            >
              <CopyIcon className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>Copy URL</span>
            </DropdownMenuItem>
          )}
          {canRescoreCalls && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={async (e) => {
                e.stopPropagation();
                await rescoreCall();
              }}
            >
              <RefreshCcwIcon className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>Rescore</span>
            </DropdownMenuItem>
          )}
          {(!authInfo?.isLoggedIn &&
            !partner?.permissions?.includes(
              PartnerPermission.DISABLE_CALL_HISTORY,
            )) ||
            (authInfo?.isLoggedIn && !isInIframe && (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={async (e) => {
                  e.stopPropagation();
                  router.push(
                    LinksManager.trainingCalls(`?buyers=${call?.agentId}`),
                  );
                }}
              >
                <LayoutListIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>View call history</span>
              </DropdownMenuItem>
            ))}
          {((!authInfo?.isLoggedIn &&
            (hbDemoInboundForm?.isAdmin ||
              hbDemoInboundForm?.id === call?.demoInboundFormResponseId)) ||
            (authInfo?.isLoggedIn &&
              authInfo?.accessHelper?.isAtLeastRole(
                org?.orgId as string,
                RoleEnum.ADMIN,
              ))) &&
            !isCompetitionOrg && (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  Analytics.track(CallEvents.DELETE_CLICKED, {
                    id: call.id,
                    vapiId: call.vapiId,
                    agentId: call.agent?.id,
                    orgId: call.orgId,
                    from: location,
                  });
                  if (authInfo?.isLoggedIn) {
                    deleteCallMutation.mutate({
                      callId: callId,
                    });
                  } else {
                    deleteDemoCallMutation.mutate({
                      callVapiId: callId,
                      demoInboundFormResponseId:
                        call?.demoInboundFormResponseId,
                    });
                  }
                }}
              >
                <Trash2Icon className="w-4 h-4 mr-2 text-red-400" />
                <span>Delete call</span>
              </DropdownMenuItem>
            )}
          {canViewMedia &&
            (!onlyAdminsCanExportCalls ||
              canAccess(AppPermissions.EXPORT_CALL)) && (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={onDownloadAudioClick}
              >
                <DownloadIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Download recording</span>
              </DropdownMenuItem>
            )}
          {(!onlyAdminsCanExportCalls ||
            canAccess(AppPermissions.EXPORT_CALL)) && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                Analytics.track(CallEvents.TRANSCRIPT_COPY_CLICKED, {
                  id: call.id,
                  vapiId: call.vapiId,
                  agentId: call.agent?.id,
                  orgId: call.orgId,
                  from: location,
                });
                const messages =
                  call?.providerMetadata?.messages ||
                  call?.vapiMetadata?.messages ||
                  [];
                const formattedTranscript = formatTranscript(
                  messages,
                  call?.agent
                    ? `${call?.agent?.firstName} ${call?.agent?.lastName}`
                    : '',
                  callId,
                );

                navigator.clipboard.writeText(formattedTranscript);
              }}
            >
              <CopyIcon className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>Copy transcript</span>
            </DropdownMenuItem>
          )}
          {(!onlyAdminsCanExportCalls ||
            canAccess(AppPermissions.EXPORT_CALL)) && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                Analytics.track(CallEvents.TRANSCRIPT_DOWNLOADED, {
                  id: call?.id,
                  vapiId: call?.vapiId,
                  agentId: call?.agent?.id,
                  orgId: call?.orgId,
                  from: location,
                });
                const messages =
                  call?.providerMetadata?.messages ||
                  call?.vapiMetadata?.messages ||
                  [];
                const formattedTranscript = formatTranscript(
                  messages,
                  call.agent
                    ? `${call.agent?.firstName} ${call.agent.lastName}`
                    : '',
                  callId,
                );

                const blob = new Blob([formattedTranscript], {
                  type: 'text/plain',
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `transcript-${callId}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
              }}
            >
              <DownloadIcon className="w-4 h-4 mr-2 text-muted-foreground" />{' '}
              <span>Download transcript</span>
            </DropdownMenuItem>
          )}
          {call?.vapiMetadata?.summary && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                Analytics.track(CallEvents.SUMMARY_COPY_CLICKED, {
                  id: call?.id,
                  vapiId: call?.vapiId,
                  agentId: call?.agent?.id,
                  orgId: call?.orgId,
                  from: location,
                });
                navigator.clipboard.writeText(
                  call?.vapiMetadata?.summary || '',
                );
              }}
            >
              <CopyIcon className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>Copy summary</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <CreatePlaylistModal
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        onSubmit={() => {
          setModalOpen(false);
        }}
        addCallId={call?.id}
      />
    </div>
  );
}

export default CallActionsDropdown;
