import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { useState, useEffect } from 'react';

interface AvatarComponentProps {
  imageUrl?: string;
  fallbackText?: string;
  className?: string;
  fallbackClassName?: string;
}

export function AvatarComponent({
  imageUrl,
  fallbackText,
  className = '',
  fallbackClassName = '',
}: AvatarComponentProps) {
  const [isLoading, setIsLoading] = useState(!!imageUrl);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (!imageUrl) return;

    setIsLoading(true);
    setImageError(false);

    const img = new Image();
    img.src = imageUrl;

    img.onload = () => {
      setIsLoading(false);
      setImageError(false);
    };

    img.onerror = () => {
      setIsLoading(false);
      setImageError(true);
    };
  }, [imageUrl]);

  return (
    <Avatar className={className}>
      {isLoading ? (
        <Skeleton className="w-10 h-10 rounded-full" />
      ) : imageError || !imageUrl ? (
        <AvatarFallback
          className={fallbackClassName ? fallbackClassName : 'text-lg'}
        >
          {fallbackText}
        </AvatarFallback>
      ) : (
        <AvatarImage key={imageUrl} src={imageUrl} alt="User Avatar" />
      )}
    </Avatar>
  );
}
