import useUserSession from '@/hooks/useUserSession';
import { AvatarComponent } from '../AvatarComponent';
import { cn } from '@/lib/utils';
import { PropsWithChildren } from 'react';

interface IProps extends PropsWithChildren {
  className?: string;
}
export default function UserAvatar({ className, ...props }: IProps) {
  const { user } = useUserSession();

  const imageUrl = user?.pictureUrl;
  const fallbackText =
    `${user?.firstName?.charAt(0) || ''}${user?.lastName?.charAt(0) || ''}` ||
    'U';

  return (
    <AvatarComponent
      imageUrl={imageUrl}
      fallbackText={fallbackText}
      className={cn(className)}
      {...props}
    />
  );
}
