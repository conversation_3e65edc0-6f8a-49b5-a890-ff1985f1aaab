import { useContext, useMemo } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { AvatarComponent } from '../AvatarComponent';
import { AnyAgentDto } from '@/lib/Agent/types';

interface IProps {
  agent?: AnyAgentDto;
  className?: string;
  fallbackTextOverride?: string;
}

export default function AgentAvatar({
  agent,
  className = '',
  fallbackTextOverride = '',
}: IProps) {
  const { firstName, lastName, avatarUrl } = agent || {};
  const { form } = useContext(CreateBuyerBotEditFormContext);

  const botName = form?.watch('firstName');
  const botLastName = form?.watch('lastName');
  const botAvatar = form?.watch('avatarUrl');
  const botAvatarBase64 = form?.watch('avatarBase64');

  const imageUrl = useMemo(() => {
    return botAvatarBase64 || avatarUrl || botAvatar || undefined;
  }, [botAvatar, avatarUrl, botAvatarBase64]);

  const fallbackText = useMemo(() => {
    const firstInitial = firstName?.charAt(0) || botName?.charAt(0) || '';
    const lastInitial = lastName?.charAt(0) || botLastName?.charAt(0) || '';
    return firstInitial + lastInitial || 'B';
  }, [firstName, lastName, botName, botLastName]);

  return (
    <AvatarComponent
      imageUrl={imageUrl}
      fallbackText={fallbackTextOverride ? fallbackTextOverride : fallbackText}
      className={className}
    />
  );
}
