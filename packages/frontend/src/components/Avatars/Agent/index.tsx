import { useContext, useMemo } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { AvatarComponent } from '../AvatarComponent';
import { AnyAgentDto } from '@/lib/Agent/types';

interface IProps {
  agent?: AnyAgentDto;
  className?: string;
}

export default function AgentAvatar({ agent, className = '' }: IProps) {
  const { firstName, lastName, avatarUrl } = agent || {};
  const { form } = useContext(CreateBuyerBotEditFormContext);

  const botName = form?.watch('firstName');
  const botLastName = form?.watch('lastName');
  const botAvatar = form?.watch('avatarUrl');

  const imageUrl = useMemo(() => {
    return avatarUrl || botAvatar || undefined;
  }, [botAvatar, avatarUrl]);

  const fallbackText = useMemo(() => {
    const firstInitial = firstName?.charAt(0) || botName?.charAt(0) || '';
    const lastInitial = lastName?.charAt(0) || botLastName?.charAt(0) || '';
    return firstInitial + lastInitial || 'B';
  }, [firstName, lastName, botName, botLastName]);

  return (
    <AvatarComponent
      imageUrl={imageUrl}
      fallbackText={fallbackText}
      className={className}
    />
  );
}
