import { AgentDto, AnyAgentDto, PublicAgentDto } from '@/lib/Agent/types';
import { DemoInboundForm } from '../DemoInboundForm';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { DemoChallengeNewRegistrationForm } from '../DemoChallengeNewRegistrationForm';
import { TrophyIcon } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

interface IDemoWelcomeModalProps {
  agent?: AnyAgentDto;
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  onSubmit: () => void;
  isClosable?: boolean;
  submitText?: string;
  registerForChallenge?: boolean;
}

function DemoWelcomeModal({
  agent,
  modalOpen,
  setModalOpen,
  onSubmit,
  isClosable = true,
  submitText = "Let's go, start call",
  registerForChallenge = false,
}: IDemoWelcomeModalProps) {
  const pathname = usePathname();

  return (
    <Dialog
      open={modalOpen}
      onOpenChange={
        isClosable
          ? setModalOpen
          : (open) => (open ? setModalOpen(false) : setModalOpen(true))
      }
    >
      <DialogContent
        className={cn({
          'close-btn': isClosable,
          'mt-10': pathname.includes('/embed'),
        })}
      >
        <DialogHeader>
          {!pathname.includes('/embed') && (
            <DialogTitle className="flex items-center">
              {registerForChallenge ? (
                <>
                  Beat the Bot Challenge{' '}
                  <TrophyIcon className="ml-2 w-5 h-5 text-muted-foreground" />
                </>
              ) : (
                'Welcome to the Hyperbound Demo!'
              )}
            </DialogTitle>
          )}
          {registerForChallenge && (
            <DialogDescription className="mb-4">
              Sign up for the challenge to get started with your call
            </DialogDescription>
          )}
          {registerForChallenge ? (
            <DemoChallengeNewRegistrationForm
              agentVapiId={agent?.vapiId}
              onSubmit={onSubmit}
              submitText={submitText}
            />
          ) : (
            // )
            <DemoInboundForm
              agentVapiId={agent?.vapiId}
              onSubmit={onSubmit}
              submitText={submitText}
              registerForChallenge={registerForChallenge}
            />
          )}
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}

export default DemoWelcomeModal;
