import { cn } from '@/lib/utils';
import { useRedirectFunctions } from '@propelauth/react';
import { CalendarCheckIcon, LogInIcon } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';

interface ILoginOrSignupModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  isClosable?: boolean;
}

function LoginOrSignupModal({
  modalOpen,
  setModalOpen,
  isClosable = true,
}: ILoginOrSignupModalProps) {
  const { redirectToLoginPage } = useRedirectFunctions();
  const router = useRouter();
  const pathname = usePathname();

  return (
    <Dialog
      open={modalOpen}
      onOpenChange={
        isClosable
          ? setModalOpen
          : (open) => (open ? setModalOpen(false) : setModalOpen(true))
      }
    >
      <DialogContent
        className={cn('', {
          'close-btn': isClosable,
          'mt-10': pathname.includes('/embed'),
        })}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Have an account with your team already?
          </DialogTitle>
        </DialogHeader>
        <Button
          type="submit"
          variant={'default'}
          className="text-[15px] w-full"
          onClick={() => {
            redirectToLoginPage();
          }}
          size={'lg'}
        >
          <LogInIcon className="w-4 h-4 mr-2" />
          Sign in
        </Button>
        <DialogDescription className="pt-3">
          Book a demo now if you don&apos;t have an account!
        </DialogDescription>
        <Button
          size={'lg'}
          className="text-[15px]"
          variant={'secondary'}
          onClick={() => {
            window.open(
              'https://calendly.com/d/cppn-cqx-39h',
              '_blank',
              'noopener',
            );
          }}
        >
          <CalendarCheckIcon className="w-4 h-4 mr-2" />
          Book a demo to build your own bots!
        </Button>
      </DialogContent>
    </Dialog>
  );
}

export default LoginOrSignupModal;
