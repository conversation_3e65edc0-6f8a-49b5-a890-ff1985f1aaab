import { cn } from '@/lib/utils';
import dayjs from 'dayjs';
import CountdownTimer from '../../../common/Home/Competitions/CountdownTimer';
import Link from 'next/link';
import Image from 'next/image';

interface IProps {
  countdownTo: Date;
}

export default function FreezeScreenWithCountdown({ countdownTo }: IProps) {
  const to = dayjs(countdownTo);
  console.log(to.format('YYYY-MM-DD HH:mm:ss'));

  return (
    <div className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-0 z-50 ">
      <div className="flex items-center w-full pt-6">
        <div className="flex-1" />
        <div className="rounded-2xl p-2 flex flex-wrap items-center space-x-4 justify-center bg-white text-black z-10">
          <Link href="https://hyperbound.ai" target="_blank">
            <Image
              src="/images/black-logo-with-text.svg"
              width={153}
              height={24}
              alt="Hyperbound Logo"
            />
          </Link>
          <span className="">&amp;</span>
          <Link href="https://pclub.io" target="_blank">
            <Image
              src="/images/competition/league-of-sales-legends/pclub.svg"
              width={120}
              height={32}
              alt="PClub Logo"
            />
          </Link>
        </div>
        <div className="flex-1 flex items-center">
          <div className="flex-1" />
          <div className="flex flex-col justify-center items-center text-black  pr-6">
            <CountdownTimer
              abbreviatedSuffix={false}
              bigNumbers={true}
              targetDate={to.add(7, 'days').toDate()}
            />
            <div className="text-right w-full mt-2 text-muted-foreground font-semibold">
              Until competition starts
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
