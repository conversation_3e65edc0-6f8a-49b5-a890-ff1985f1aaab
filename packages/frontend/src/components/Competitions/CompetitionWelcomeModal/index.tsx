import useUserSession from '@/hooks/useUserSession';
import { AnyAgentDto } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { TrophyIcon } from 'lucide-react';
import { usePathname } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../ui/dialog';
import { CompetitionWelcomeForm } from './DemoChallengeNewRegistrationForm';

interface IDemoWelcomeModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  onSubmit: () => void;
  isClosable?: boolean;
  submitText?: string;
}

function CompetitionWelcomeModal({
  modalOpen,
  setModalOpen,
  onSubmit,
  isClosable = true,
  submitText = "Let's get started!",
}: IDemoWelcomeModalProps) {
  const pathname = usePathname();
  const { dbOrg } = useUserSession();

  return (
    <Dialog
      open={modalOpen}
      onOpenChange={
        isClosable
          ? setModalOpen
          : (open) => (open ? setModalOpen(false) : setModalOpen(true))
      }
    >
      <DialogContent
        className={cn({
          'close-btn': isClosable,
          'mt-10': pathname.includes('/embed'),
        })}
      >
        <DialogHeader>
          {!pathname.includes('/embed') && (
            <DialogTitle className="flex items-center">
              Welcome to {dbOrg?.name}!
              <TrophyIcon className="ml-2 w-5 h-5 text-muted-foreground" />
            </DialogTitle>
          )}
          <DialogDescription>
            Please add your LinkedIn URL to join the global leaderboard, follow
            competitors and make your first call.
          </DialogDescription>
          <CompetitionWelcomeForm onSubmit={onSubmit} submitText={submitText} />
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}

export default CompetitionWelcomeModal;
