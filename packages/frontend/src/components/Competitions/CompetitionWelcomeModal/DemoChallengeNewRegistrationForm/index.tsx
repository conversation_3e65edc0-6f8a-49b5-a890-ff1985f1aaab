'use client';

import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import CompetitionLeaderboardService from '@/lib/Competition/Leaderboard';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { Loader2Icon } from 'lucide-react';
import { useRef } from 'react';
import { useForm } from 'react-hook-form';
import { Id, ToastContainer, toast } from 'react-toastify';

const salesTeamSizeOptions = [
  {
    label: '1-5 reps',
    value: '1-5 reps',
  },
  {
    label: '5-10 reps',
    value: '5-10 reps',
  },
  {
    label: '10-30 reps',
    value: '10-30 reps',
  },
  {
    label: '30-80 reps',
    value: '30-80 reps',
  },
  {
    label: '81+ reps',
    value: '81+ reps',
  },
];

const linkedinRegex =
  /^https?:\/\/((www|\w\w)\.)?linkedin.com\/((in\/[^/]+\/?)|(pub\/[^/]+\/((\w|\d)+\/?){3}))$/;

const formSchema = z.object({
  //numberOfSalesReps: z
  //  .string()
  //  .min(1, 'This field is required')
  //  .regex(/^(?!"Choose an option"$).*/, 'This field is required'),
  //attribution: z.string().min(1, 'This field is required'),
  linkedinUrl: z
    .string()
    .url('This is not a valid URL')
    .refine((url) => {
      return linkedinRegex.test(url);
    }, 'This is not a valid LinkedIn URL')
    .optional(),
});

interface IDemoChallengeNewRegistrationFormProps {
  onSubmit: () => void;
  submitText?: string;
}

export function CompetitionWelcomeForm({
  onSubmit: onSubmitted,
  submitText = "Let's go, start call",
}: IDemoChallengeNewRegistrationFormProps) {
  const errorToastId = useRef<Id | null>(null);

  // 1. Define your form.
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: async () => {
      return {
        //numberOfSalesReps: '',
        //attribution: '',
        linkedinUrl: '',
      };
    },
  });

  const updateCompetitionUserMetadataMutation = useMutation({
    mutationFn: CompetitionLeaderboardService.updateCompetitionUserMetadata,
    onSuccess: async (response, params) => {
      onSubmitted();
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving your response. Please try again.',
        );
      }
      console.log('ERROR saving response', error);
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    updateCompetitionUserMetadataMutation.mutate({
      //sizeOfSalesTeam: values.numberOfSalesReps?.trim(),
      //attribution: values.attribution?.trim(),
      linkedinUrl: values.linkedinUrl?.trim() || '',
    });
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="linkedinUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Your LinkedIn Url *</FormLabel>
              <FormControl>
                <Input
                  placeholder="https://www.linkedin.com/in/john-doe/"
                  {...field}
                />
              </FormControl>
              <FormMessage />
              <p className="text-sm text-muted-foreground mt-2">
                Click{' '}
                <a
                  href="https://www.linkedin.com/profile/edit"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline text-blue-500"
                >
                  here
                </a>{' '}
                to get your LinkedIn URL
              </p>
            </FormItem>
          )}
        />

        {/*<FormField
          control={form.control}
          name="numberOfSalesReps"
          render={({ field }) => (
            <FormItem>
              <FormLabel>What is the size of your sales team? *</FormLabel>
              <Select
                required
                onValueChange={(e) => {
                  field.onChange(e);
                }}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an option" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {salesTeamSizeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />*/}
        {/*}<FormField
          control={form.control}
          name="attribution"
          render={({ field }) => (
            <FormItem>
              <FormLabel>How&apos;d you hear about us? *</FormLabel>
              <FormControl>
                <Input placeholder="Adam Robinson from RB2B" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />*/}
        <Button
          type="submit"
          disabled={updateCompetitionUserMetadataMutation.isPending}
          className="float-right text-primary"
          variant={'outline'}
        >
          {updateCompetitionUserMetadataMutation.isPending ? (
            <Loader2Icon className="animate-spin" />
          ) : (
            submitText
          )}
        </Button>
      </form>
      <ToastContainer />
    </Form>
  );
}
