import useMaintenance from '@/hooks/useMaintenance';
import { cn } from '@/lib/utils';
import { useAuthInfo } from '@propelauth/react';

function MaintenanceBanner() {
  const authInfo = useAuthInfo();
  const { data: maintenance } = useMaintenance(!authInfo.isLoggedIn);

  return (
    <div
      className={cn(
        'fixed top-0 w-full py-3 px-4 text-center font-semibold flex justify-between items-center bg-yellow-600 text-white z-50',
        {
          'top-16': !authInfo.isLoggedIn,
        },
      )}
    >
      <div className="flex space-x-3 items-center"></div>
      <div className="items-center space-x-2 hidden md:flex">
        <p>{maintenance?.bannerMessage || 'Maintenance underway currently'}</p>
      </div>
      <div className="flex space-x-3">
        {/* <Button
          type="submit"
          className="h-8"
          variant={"secondary"}
          onClick={() => {
            window.open(
              "https://calendly.com/sriharsha-g/30min",
              "_blank",
              "noopener"
            );
          }}
        >
          Book a personalized demo
        </Button> */}
        {/* <Button
          type="submit"
          className="h-8 hover:bg-transparent hover:text-background"
          variant={"outline"}
          onClick={() => redirectToLoginPage()}
        >
          Login
        </Button>
        <Button
          type="submit"
          className="h-8"
          variant={"secondary"}
          onClick={() => redirectToSignupPage()}
        >
          Sign up for free
        </Button> */}
      </div>
    </div>
  );
}

export default MaintenanceBanner;
