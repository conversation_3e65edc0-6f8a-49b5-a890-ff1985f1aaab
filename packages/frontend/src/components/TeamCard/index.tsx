import { TeamDto } from '@/lib/User/types';
import { useEffect, useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { ChevronRight, GripVertical } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Checkbox } from '../ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';

interface ITeamCardProps {
  team: TeamDto;
  selectable?: boolean;
  depth?: number;
  expandedTeams?: number[];
  onExpand?: (teamId: number) => void;
  onSelect?: (team: TeamDto) => void;
  selectedTeams?: number[];
  onTeamMove?: (draggedTeamId: number, targetTeamId: number | null) => void;
  isDragging?: boolean;
  draggedTeamId?: number | null;
  draggable?: boolean;
  onCardClick?: () => void;
}

export default function TeamCard({
  team,
  selectable,
  depth = 0,
  expandedTeams: _expandedTeams,
  onExpand,
  onSelect,
  selectedTeams,
  onTeamMove,
  isDragging,
  draggedTeamId,
  draggable,
  onCardClick,
}: ITeamCardProps) {
  const [expandedTeams, setExpandedTeams] = useState<number[]>(
    _expandedTeams || [],
  );

  useEffect(() => {
    setExpandedTeams(_expandedTeams || []);
  }, [_expandedTeams]);

  const [dragState, setDragState] = useState({
    isDragOver: false,
    isValidDropTarget: false,
    draggedTeamId: null as number | null,
  });

  const isDescendant = (
    teamId: number,
    potentialAncestorId: number,
  ): boolean => {
    const checkChildren = (currentTeam: TeamDto): boolean => {
      if (currentTeam.id === potentialAncestorId) return true;
      return (
        currentTeam.children?.some((child) => child.id === teamId) || false
      );
    };
    return checkChildren(team);
  };

  const isValidDropTarget = (draggedId: number, targetId: number): boolean => {
    return draggedId !== targetId && !isDescendant(targetId, draggedId);
  };

  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData('text/plain', team.id.toString());
    e.dataTransfer.effectAllowed = 'move';
    setDragState((prev) => ({ ...prev, draggedTeamId: team.id }));
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    const draggedId =
      parseInt(e.dataTransfer.getData('text/plain')) || draggedTeamId;
    if (!draggedId) return;

    const isValid = isValidDropTarget(draggedId, team.id);

    setDragState((prev) => ({
      ...prev,
      isDragOver: true,
      isValidDropTarget: isValid,
    }));

    e.dataTransfer.dropEffect = isValid ? 'move' : 'none';
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setDragState((prev) => ({ ...prev, isDragOver: false }));
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const draggedTeamId = parseInt(e.dataTransfer.getData('text/plain'));

    if (isValidDropTarget(draggedTeamId, team.id)) {
      onTeamMove?.(draggedTeamId, team.id);
    }

    setDragState({
      isDragOver: false,
      isValidDropTarget: false,
      draggedTeamId: null,
    });
  };

  const handleDragEnd = () => {
    setDragState({
      isDragOver: false,
      isValidDropTarget: false,
      draggedTeamId: null,
    });
  };

  const getCardClasses = () => {
    const baseClasses = 'h-[58px] my-2 transition-all duration-200';

    if (dragState.draggedTeamId === team.id || draggedTeamId === team.id) {
      return `${baseClasses} opacity-50 scale-95 cursor-grabbing`;
    }

    if (dragState.isDragOver && dragState.isValidDropTarget) {
      return `${baseClasses} border-blue-400 border-2 border-dashed bg-blue-50 bg-opacity-80 shadow-lg shadow-blue-200 scale-102 ring-2 ring-blue-300 ring-opacity-50`;
    }

    if (dragState.isDragOver && !dragState.isValidDropTarget) {
      return `${baseClasses} border-red-400 border-2 border-dashed bg-red-50 bg-opacity-80 shadow-lg shadow-red-200 ring-2 ring-red-300 ring-opacity-50`;
    }

    return `${baseClasses} hover:shadow-md`;
  };

  const onToggleExpand = (teamId: number) => {
    if (onExpand) {
      onExpand(teamId);
    } else {
      setExpandedTeams((prev) => {
        if (prev.includes(teamId)) {
          return prev.filter((id) => id !== teamId);
        }
        return [...prev, teamId];
      });
    }
  };

  return (
    <div>
      <div className="z-30 relative cursor-pointer">
        <Card
          className={getCardClasses()}
          onClick={onCardClick}
          draggable={draggable}
          {...(draggable && {
            onDragStart: handleDragStart,
            onDragOver: handleDragOver,
            onDragLeave: handleDragLeave,
            onDrop: handleDrop,
            onDragEnd: handleDragEnd,
          })}
        >
          <CardContent
            className={cn(
              'py-3 pl-3 flex flex-1 flex-row items-center overflow-hidden transition-all duration-200',
              dragState.isDragOver &&
                dragState.isValidDropTarget &&
                'transform translate-x-1',
              dragState.isDragOver &&
                !dragState.isValidDropTarget &&
                'transform translate-x-0',
            )}
          >
            {draggable && (
              <div
                className={cn(
                  'flex items-center mr-2 transition-all duration-200 cursor-grab',
                  dragState.draggedTeamId === team.id ||
                    draggedTeamId === team.id
                    ? 'opacity-100 text-blue-500'
                    : 'opacity-30 hover:opacity-70 hover:text-blue-400',
                )}
              >
                <GripVertical className="w-4 h-4" />
              </div>
            )}

            {selectable && (
              <div className="flex items-center justify-center mr-2">
                <Checkbox
                  checked={selectedTeams?.includes(team.id)}
                  onCheckedChange={() => {
                    onSelect?.(team);
                  }}
                />
              </div>
            )}
            {team.children && team.children.length > 0 && (
              <div className="flex items-center justify-center">
                <ChevronRight
                  className={cn(
                    expandedTeams.includes(team.id) ? 'rotate-90' : '',
                    'transition-transform duration-200 cursor-pointer w-4 h-4 text-muted-foreground',
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    onToggleExpand(team.id);
                  }}
                />
              </div>
            )}
            <div className="flex flex-col ml-2">
              <p className="text-sm font-medium">{team.name}</p>
              <div className="flex flex-row items-center">
                <div className="flex flex-row items-center">
                  {team.users?.slice(0, 3).map((user, index) => (
                    <TooltipProvider key={user.id} delayDuration={0}>
                      <Tooltip>
                        <TooltipTrigger>
                          <Avatar
                            className={cn(
                              'w-6 h-6 border-2 border-white',
                              index === 0 ? 'ml-0' : 'ml-[-5px]',
                            )}
                          >
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback>
                              {user.firstName.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div>
                            {user.firstName} {user.lastName}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                </div>
                <div className="text-xs text-muted-foreground ml-2">
                  {team.users?.length} members
                </div>
                <div className="text-muted-foreground mx-1">•</div>
                <div className="text-xs text-muted-foreground">Team for NA</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      {expandedTeams.includes(team.id) &&
        team.children &&
        team.children.length > 0 && (
          <div className="pl-4 mt-2 z-20">
            {team.children.map((subTeam, index) => (
              <div key={subTeam.id} className="relative flex flex-row">
                <div className="z-20 ml-4">
                  <TeamCard
                    team={subTeam}
                    selectable={selectable}
                    depth={depth + 1}
                    onExpand={onToggleExpand}
                    expandedTeams={expandedTeams}
                    selectedTeams={selectedTeams}
                    onSelect={onSelect}
                    onTeamMove={onTeamMove}
                    isDragging={isDragging || dragState.draggedTeamId !== null}
                    draggedTeamId={draggedTeamId || dragState.draggedTeamId}
                    draggable={draggable}
                  />
                </div>
                <div
                  className=" absolute z-10 bg-transparent"
                  style={{ marginTop: `-29px`, height: `75px` }}
                >
                  <Card
                    className={cn(
                      'h-full w-full border-r-0 rounded-none bg-transparent',
                      index ===
                        (team.children && team.children.length
                          ? team.children.length - 1
                          : -1)
                        ? 'rounded-b-xl'
                        : '',
                    )}
                  >
                    <CardContent className="h-full w-full" />
                  </Card>
                </div>
              </div>
            ))}
          </div>
        )}
    </div>
  );
}
