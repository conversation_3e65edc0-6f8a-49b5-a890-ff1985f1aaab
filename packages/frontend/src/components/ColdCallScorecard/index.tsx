import {
  AlertTriangleIcon,
  BarChartHorizontalBigIcon,
  CheckCheckIcon,
  CheckCircle2Icon,
  ChevronDown,
  ChevronUp,
  EditIcon,
  FileQuestionIcon,
  Maximize2,
  PlayCircle,
  SquareArrowUpRight,
  XCircleIcon,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Badge } from '../ui/badge';
import { Skeleton } from '../ui/skeleton';
import { motion } from 'framer-motion';
import { useRef, useState } from 'react';
import Link from 'next/link';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import CallService from '@/lib/Call';
import { Id, toast } from 'react-toastify';
import { Button } from '../ui/button';
import { useParams } from 'next/navigation';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '../ui/alert-dialog';
import { useAuthInfo } from '@propelauth/react';
import { UserDto } from '@/lib/User/types';
import useOrgCurrentUser from '@/hooks/useOrgCurrentUser';
import useUserSession from '@/hooks/useUserSession';
import { RepsCanEditScoreResults } from '@/lib/Organization/types';
import LinksManager from '@/lib/linksManager';
import { QuestionMarkIcon } from '@radix-ui/react-icons';
interface IColdCallScorecardProps {
  scorecard: any;
  loading: boolean;
  coachingInfo: any;
  caller: UserDto;
  disableToggleCriteria?: boolean;
}

function ColdCallScorecard({
  scorecard,
  loading,
  coachingInfo,
  caller,
  disableToggleCriteria,
}: IColdCallScorecardProps) {
  const { dbOrg } = useUserSession();

  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  const { id } = useParams();
  const authInfo = useAuthInfo();
  const { data: curUser } = useOrgCurrentUser();
  const { isAdmin } = useUserSession();

  if (!authInfo?.isLoggedIn) {
    disableToggleCriteria = true;
  } else {
    if (dbOrg?.repsCanEditScoreResults === RepsCanEditScoreResults.NO) {
      disableToggleCriteria = true;
    }
  }

  const toggleScorecardCriteriaMutation = useMutation({
    mutationFn: CallService.toggleScorecardCriteriaForCall,
    onSuccess: (data, params) => {
      queryClient.invalidateQueries({
        queryKey: ['callScorecard', false, id],
      });
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error toggling the scorecard criteria');
        errorToastId.current = toast.error(
          'There was an error toggling the scorecard criteria. Please try again.',
        );
      }
    },
  });

  const toggleDisputedScorecardCriteriaMutation = useMutation({
    mutationFn: CallService.toggleDisputedScorecardCriteriaForCall,
    onSuccess: (data, params) => {
      queryClient.invalidateQueries({
        queryKey: ['callScorecard', false, id],
      });
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error toggling the scorecard criteria');
        errorToastId.current = toast.error(
          'There was an error toggling the scorecard criteria. Please try again.',
        );
      }
    },
  });

  const renderEmptyScorecard = () => {
    return (
      <div className="w-full h-[150px] flex justify-center items-center">
        <div className="flex flex-col space-y-2 text-muted-foreground text-sm items-center">
          <BarChartHorizontalBigIcon />
          <p>No scorecard could be found</p>
        </div>
      </div>
    );
  };

  const [coachingPanels, setCoachingPanels] = useState<any>({});

  const toggleCoachingInfo = (criterion: string) => {
    if (coachingInfo && coachingInfo.by_criterion) {
      if (
        coachingInfo?.by_criterion[criterion] &&
        coachingInfo?.by_criterion[criterion].length > 0
      ) {
        if (coachingPanels[criterion]) {
          coachingPanels[criterion] = 0;
        } else {
          coachingPanels[criterion] = '150px';
        }
        setCoachingPanels({ ...coachingPanels });
      }
    }
  };

  const str_pad_left = (string: any, pad: any, length: any) => {
    return (new Array(length + 1).join(pad) + string).slice(-length);
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          {scorecard.sectionTitle}{' '}
          <Badge variant={'secondary'}>
            {scorecard.passedCriteriaCount} / {scorecard.totalCriteriaCount}
          </Badge>
        </CardTitle>
        {/* <CardDescription>{scorecard.description}</CardDescription> */}
      </CardHeader>
      <CardContent className="px-3">
        {loading ? (
          <div className="space-y-3 px-3">
            <div className="flex justify-between">
              <Skeleton className="w-3/4 h-[15px] rounded-full" />
              <Skeleton className="w-[15px] h-[15px] rounded-full" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="w-3/4 h-[15px] rounded-full" />
              <Skeleton className="w-[15px] h-[15px] rounded-full" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="w-3/4 h-[15px] rounded-full" />
              <Skeleton className="w-[15px] h-[15px] rounded-full" />
            </div>
          </div>
        ) : scorecard ? (
          scorecard.criteria.map((criterion: any) => {
            let coachingCalls: any = [];
            if (coachingInfo && coachingInfo.by_criterion) {
              if (coachingInfo?.by_criterion[criterion.criterion]) {
                coachingCalls = coachingInfo.by_criterion[criterion.criterion];
              }
            }

            return (
              <div key={criterion.criterion} className="m-0">
                <div
                  className="flex items-center p-3 rounded-lg cursor-pointer transition-all hover:bg-accent hover:text-accent-foreground group"
                  onClick={() => {
                    toggleCoachingInfo(criterion.criterion);
                  }}
                >
                  <div className="space-y-1 mr-3 flex-1">
                    <p className="text-sm font-medium leading-none">
                      {criterion.criterion}
                    </p>
                  </div>

                  {disableToggleCriteria ||
                  (caller?.id !== curUser?.id && !isAdmin) ? (
                    <div className="flex items-center">
                      {criterion.passed ? (
                        <CheckCircle2Icon className="w-4 h-4 text-green-600" />
                      ) : (
                        <XCircleIcon className="w-4 h-4 text-red-600" />
                      )}

                      {criterion.disputed ? (
                        <AlertTriangleIcon className="ml-2 w-4 h-4 text-muted-foreground" />
                      ) : criterion.toggled ? (
                        <CheckCheckIcon className="ml-2 w-4 h-4 text-muted-foreground" />
                      ) : null}
                    </div>
                  ) : (
                    <div className="flex items-center">
                      {!criterion.toggled && !criterion.disputed ? (
                        <AlertDialog>
                          <TooltipProvider delayDuration={50}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span tabIndex={0}>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant={'ghost'}
                                      className="p-2 m-0 font-medium rounded-full"
                                    >
                                      {criterion.passed ? (
                                        <CheckCircle2Icon className="w-4 h-4 text-green-600" />
                                      ) : (
                                        <XCircleIcon className="w-4 h-4 text-red-600" />
                                      )}
                                    </Button>
                                  </AlertDialogTrigger>
                                </span>
                              </TooltipTrigger>
                              <TooltipContent side="bottom">
                                {dbOrg?.repsCanEditScoreResults ===
                                  RepsCanEditScoreResults.YES || isAdmin ? (
                                  <p>
                                    Incorrectly scored? Click to change score
                                    {!isAdmin && <> &amp; report</>}
                                  </p>
                                ) : (
                                  <p>Dispute score</p>
                                )}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              {dbOrg?.repsCanEditScoreResults ===
                                RepsCanEditScoreResults.YES || isAdmin ? (
                                <>
                                  <AlertDialogTitle>
                                    Are you absolutely sure?
                                  </AlertDialogTitle>
                                  {isAdmin ? (
                                    <AlertDialogDescription>
                                      You can always change this back.
                                    </AlertDialogDescription>
                                  ) : (
                                    <AlertDialogDescription>
                                      Your admins will be notified that you made
                                      this change.
                                    </AlertDialogDescription>
                                  )}
                                </>
                              ) : (
                                <>
                                  <AlertDialogTitle>
                                    Do you want to despute this score?
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Your admins will be notified that you made
                                    this change.
                                  </AlertDialogDescription>
                                </>
                              )}
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => {
                                  if (
                                    isAdmin &&
                                    dbOrg?.repsCanEditScoreResults ===
                                      RepsCanEditScoreResults.DISPUTE_ONLY
                                  ) {
                                    toggleDisputedScorecardCriteriaMutation.mutate(
                                      {
                                        callId: id as string,
                                        sectionTitle: scorecard.sectionTitle,
                                        criterion: criterion.criterion,
                                      },
                                    );
                                  } else {
                                    toggleScorecardCriteriaMutation.mutate({
                                      callId: id as string,
                                      sectionTitle: scorecard.sectionTitle,
                                      criterion: criterion.criterion,
                                    });
                                  }
                                }}
                              >
                                Change and report
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      ) : criterion.toggled || isAdmin ? (
                        <TooltipProvider delayDuration={50}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span tabIndex={0}>
                                <Button
                                  variant={'ghost'}
                                  className="p-2 m-0 font-medium rounded-full"
                                  onClick={() => {
                                    if (
                                      isAdmin &&
                                      dbOrg?.repsCanEditScoreResults ===
                                        RepsCanEditScoreResults.DISPUTE_ONLY
                                    ) {
                                      toggleDisputedScorecardCriteriaMutation.mutate(
                                        {
                                          callId: id as string,
                                          sectionTitle: scorecard.sectionTitle,
                                          criterion: criterion.criterion,
                                        },
                                      );
                                    } else if (
                                      dbOrg?.repsCanEditScoreResults ===
                                      RepsCanEditScoreResults.YES
                                    ) {
                                      //its either user or admin
                                      toggleScorecardCriteriaMutation.mutate({
                                        callId: id as string,
                                        sectionTitle: scorecard.sectionTitle,
                                        criterion: criterion.criterion,
                                      });
                                    }
                                  }}
                                >
                                  {criterion.passed ? (
                                    <CheckCircle2Icon className="w-4 h-4 text-green-600" />
                                  ) : (
                                    <XCircleIcon className="w-4 h-4 text-red-600" />
                                  )}
                                </Button>
                              </span>
                            </TooltipTrigger>
                            <TooltipContent side="bottom">
                              {!(
                                isAdmin &&
                                dbOrg?.repsCanEditScoreResults ===
                                  RepsCanEditScoreResults.DISPUTE_ONLY
                              ) &&
                              !(
                                dbOrg?.repsCanEditScoreResults ===
                                RepsCanEditScoreResults.YES
                              ) ? (
                                <p>Contact your admin to change this score</p>
                              ) : (
                                <p>Change score</p>
                              )}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      ) : criterion.passed ? (
                        <CheckCircle2Icon className="w-4 h-4 text-green-600" />
                      ) : (
                        <XCircleIcon className="w-4 h-4 text-red-600" />
                      )}

                      {criterion.disputed ? (
                        isAdmin ? (
                          <TooltipProvider delayDuration={50}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span tabIndex={0}>
                                  <Button
                                    variant={'ghost'}
                                    className="p-2 m-0 font-medium rounded-full"
                                    onClick={() => {
                                      toggleScorecardCriteriaMutation.mutate({
                                        callId: id as string,
                                        sectionTitle: scorecard.sectionTitle,
                                        criterion: criterion.criterion,
                                      });
                                    }}
                                  >
                                    <AlertTriangleIcon className="w-4 h-4 text-muted-foreground" />
                                  </Button>
                                </span>
                              </TooltipTrigger>
                              <TooltipContent side="bottom">
                                Cancel rep&apos;s dispute
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : (
                          <AlertTriangleIcon className="ml-2 w-4 h-4 text-muted-foreground" />
                        )
                      ) : criterion.toggled ? (
                        <CheckCheckIcon className="ml-2 w-4 h-4 text-muted-foreground" />
                      ) : (
                        <div className="ml-2 w-4"></div>
                      )}
                    </div>
                  )}

                  {criterion.coaching && criterion.coaching != '' && (
                    <TooltipProvider delayDuration={50}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <FileQuestionIcon size={16} />
                        </TooltipTrigger>
                        <TooltipContent
                          side="bottom"
                          className="whitespace-pre-wrap max-w-[40vw]"
                        >
                          {criterion.coaching ||
                            (isAdmin
                              ? 'Edit scorecard to enable personalized AI coaching feedback for this criteria'
                              : 'Ask your admin to enable personalized AI coaching feedback for this criteria.')}
                          {criterion.improvement && (
                            <div className="mt-4">
                              <div className="text-sm font-semibold mb-2">
                                What could you do differently next time?
                              </div>
                              {criterion.improvement}
                            </div>
                          )}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}

                  {coachingCalls.length > 0 ? (
                    <motion.div
                      animate={{
                        rotate:
                          !coachingPanels[criterion.criterion] ||
                          coachingPanels[criterion.criterion] == 0
                            ? -180
                            : 0,
                      }}
                      transition={{
                        ease: 'easeOut',
                        duration: 0.2,
                        delay: 0.1,
                      }}
                      className="ml-1 text-muted-foreground group-hover:text-yellow-500"
                    >
                      <ChevronUp size={16} />
                    </motion.div>
                  ) : (
                    <div className="w-[20px]"></div>
                  )}
                </div>

                {coachingCalls.length > 0 && (
                  <motion.div
                    initial={false}
                    animate={{
                      height: coachingPanels[criterion.criterion] || 0,
                    }}
                    transition={{
                      ease: 'easeOut',
                      duration: 0.4,
                      delay: 0.1,
                    }}
                    className="rounded-lg overflow-hidden"
                  >
                    <div className="h-[150px] overflow-auto p-2 ">
                      <p className="mb-2 font-medium text-muted-foreground">
                        Calls you might want to listen to...
                      </p>
                      <div>
                        {coachingCalls.map((c: any) => {
                          const minutes = Math.floor(c.callLength / 60);
                          const seconds = c.callLength % 60;
                          const m = minutes < 10 ? '0' + minutes : minutes;
                          const s = seconds < 10 ? '0' + seconds : seconds;

                          const finalTime =
                            str_pad_left(minutes, '0', 2) +
                            ':' +
                            str_pad_left(seconds, '0', 2);

                          return (
                            <Link
                              key={c.callId}
                              href={LinksManager.trainingCalls(
                                `${c.callVapiId}?tab=scorecard`,
                              )}
                              target="_blank"
                            >
                              <div className="flex items-center py-1 px-2 cursor-pointer hover:bg-muted rounded">
                                <div className="flex-1">
                                  {c.firstName} {c.lastName}
                                </div>
                                <div className="text-center w-[50px] mr-2">
                                  {finalTime}
                                </div>
                                <div>
                                  <SquareArrowUpRight size={18} />
                                </div>
                              </div>
                            </Link>
                          );
                        })}
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            );
          })
        ) : (
          renderEmptyScorecard()
        )}
      </CardContent>
      <CardFooter>
        {/* <Badge variant={"secondary"}>
          {scorecard.passedCriteriaCount} / {scorecard.totalCriteriaCount}
        </Badge> */}
      </CardFooter>
    </Card>
  );
}

export default ColdCallScorecard;
