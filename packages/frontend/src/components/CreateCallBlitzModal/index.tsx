import { cn } from '@/lib/utils';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import FoldersTreeView from '@/common/AIRoleplay/buyerBots/foldersTreeView';
import { AgentFolderDto } from '@/lib/Agent/types';
import { useMemo, useState } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';

export interface CreateCallBlitzModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (name: string, selectedFolders: AgentFolderDto[]) => void;
  className?: string;
}

export default function CreateCallBlitzModal({
  open,
  onOpenChange,
  className,
  onSave: _onSave,
}: CreateCallBlitzModalProps) {
  const [selectedFolders, setSelectedFolders] = useState<AgentFolderDto[]>([]);
  const [name, setName] = useState('');

  const isValid = useMemo(() => {
    return name.length > 0 && selectedFolders.length > 0;
  }, [name, selectedFolders]);

  const selectSubFolders = (folder: AgentFolderDto): AgentFolderDto[] => {
    let result: AgentFolderDto[] = [];
    if (folder.children && folder.children.length > 0) {
      for (const child of folder.children) {
        if (!child.agent) {
          result.push(child);
          result = result.concat(selectSubFolders(child));
        }
      }
    }
    return result;
  };

  const changeFolderSelection = (folder: AgentFolderDto, selected: boolean) => {
    let newSelectedFolders = [...selectedFolders];
    const allFolders = [folder, ...selectSubFolders(folder)];
    if (selected) {
      allFolders.forEach((f) => {
        if (!newSelectedFolders.some((sf) => sf.id === f.id)) {
          newSelectedFolders.push(f);
        }
      });
    } else {
      newSelectedFolders = newSelectedFolders.filter(
        (f) => !allFolders.some((af) => af.id === f.id),
      );
    }
    setSelectedFolders(newSelectedFolders);
  };

  const onSave = (name: string, selectedFolders: AgentFolderDto[]) => {
    _onSave(name, selectedFolders);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn('min-w-[80vw]', className)}>
        <DialogHeader>
          <DialogTitle>Create a new Call Blitz</DialogTitle>
          <DialogDescription>
            Select which folders you want to include in your new call blitz.
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-row overflow-y-auto h-[60vh]">
          <div className="w-full mx-1">
            <div className="flex flex-col items-start my-4">
              <div className="text-sm font-medium mb-2">* Call Blitz name:</div>
              <Input
                className="min-w-[400px] w-fit"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>
            <FoldersTreeView
              mode="selection"
              onFolderSelected={changeFolderSelection}
              selectedFolders={selectedFolders}
            />
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={() => onSave(name, selectedFolders)}
            disabled={!isValid}
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
