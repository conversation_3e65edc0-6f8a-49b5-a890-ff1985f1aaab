import { CopyCheckIcon, CopyIcon } from 'lucide-react';
import { Button } from '../ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { useCallback, useState } from 'react';

export const CopyButton = ({
  text,
  disabled = false,
}: {
  text: string;
  disabled?: boolean;
}) => {
  const [isCopied, setIsCopied] = useState(false);

  const onCopyClick = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(text);

      setIsCopied(true);

      const timer = setTimeout(() => {
        setIsCopied(false);
      }, 1500);

      return () => {
        setIsCopied(false);
        clearTimeout(timer);
      };
    } catch (err) {
      console.error('Unable to copy to clipboard:', err);
    }
  }, [text]);

  return (
    <TooltipProvider delayDuration={50} disableHoverableContent={disabled}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={'ghost'}
            disabled={disabled}
            onClick={onCopyClick}
            className="rounded-full p-2 text-muted-foreground"
          >
            {isCopied ? (
              <CopyCheckIcon className="w-4 h-4 text-green-600" />
            ) : (
              <CopyIcon className="w-4 h-4" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Copy to clipboard</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
