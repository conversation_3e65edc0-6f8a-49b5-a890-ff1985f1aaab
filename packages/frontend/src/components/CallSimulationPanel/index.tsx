import { AnyAgentDto } from '@/lib/Agent/types';
import CallService from '@/lib/Call';
import { CallDto } from '@/lib/Call/types';
import { useEffect, useMemo, useState } from 'react';
import AgentProfile from './agentProfile';
import Call from './call';
import CallSummary from '@/common/Calls/AIRoleplay/Summary';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import useUserSession from '@/hooks/useUserSession';
import { XIcon } from 'lucide-react';
import { AppPermissions } from '@/lib/permissions';
import { TaskAndAttempts } from '@/lib/LearningModule/types';

interface IProps {
  agent: AnyAgentDto;
  onAbort?: () => void;
  navigateToCallSummary?: boolean;
  narrowWidthDisplay?: boolean;
  showLeaderboardDateFilter?: boolean;
  taskAndAttempts?: TaskAndAttempts;
}

const BG_IMAGES = [
  'bg-call-1.jpg',
  'bg-call-2.jpeg',
  'bg-call-3.jpg',
  'bg-call-4.jpg',
  'bg-call-5.jpeg',
];

export default function CallSimulationPanel({
  agent,
  onAbort,
  navigateToCallSummary,
  narrowWidthDisplay = false,
  showLeaderboardDateFilter = false,
  taskAndAttempts,
}: IProps) {
  const isMultiParty = useMemo(() => {
    if (agent?.supportingAgentInfo && agent?.supportingAgentInfo?.length > 0) {
      return true;
    } else {
      return false;
    }
  }, [agent]);
  const [pageStatus, setPageStatus] = useState('profile'); //summary
  const [bgImage, setBgImage] = useState<string>();
  const router = useRouter();
  const { isLoggedIn, isCompetitionOrg, canAccess } = useUserSession();

  useEffect(() => {
    const rn = Math.floor(Math.random() * BG_IMAGES.length);
    if (isCompetitionOrg) {
      setBgImage('linear-gradient(to bottom, #1D2671, #C33764)');
    } else {
      if (agent.vapiId == 'd3ed5f66-15b2-43ab-9bfb-bbaf9fb20b3b') {
        //santa claus bg
        setBgImage(`url('/images/bg-calls/xmas.png')`);
      } else {
        setBgImage(`url('/images/bg-calls/${BG_IMAGES[rn]}')`);
      }
    }
  }, [isCompetitionOrg]);

  /***********************************/
  /******* STATUS MANAGEMENT *********/
  /***********************************/

  const [call, setCall] = useState<CallDto>();
  const [gatekeeperCall, setGatekeeperCall] = useState<CallDto>();
  const [isVideoCall, setIsVideoCall] = useState(false);
  const [gatekeeperCalled, setGatekeeperCalled] = useState(false);

  const startCall = (isGatekeeper: boolean, useVideo: boolean) => {
    setIsVideoCall(useVideo);
    if (
      agent.gatekeepers?.length &&
      agent.gatekeepers?.length > 0 &&
      !gatekeeperCalled
    ) {
      switchToGatekeeper(agent.gatekeepers[0]);
      setPageStatus('callGatekeeper');
    } else {
      if (isGatekeeper) {
        setPageStatus('callGatekeeper');
      } else {
        setPageStatus('call');
      }
    }
  };

  const endCallWithGatekeeper = (
    gkCall: CallDto,
    isGatekeeperConnectingToBuyer: boolean,
  ) => {
    if (isGatekeeperConnectingToBuyer) {
      setGatekeeperCalled(true);
      setGatekeeperCall(gkCall);
      setPageStatus('call'); //on to normal call:
    } else {
      setCall(gkCall);
      if (navigateToCallSummary) {
        router.push(LinksManager.trainingCalls(gkCall.vapiId));
      } else {
        setPageStatus('summary');
      }
    }
  };

  const openCallScoreSummary = async (c: CallDto) => {
    if (gatekeeperCall) {
      await CallService.linkGatekeeperCallToAgentCall(gatekeeperCall.id, c.id);
    }

    setCall(c);
    if (navigateToCallSummary) {
      const callId = c.providerCallId ? c.providerCallId : c.vapiId;
      router.push(LinksManager.trainingCalls(callId));
    } else {
      setPageStatus('summary');
    }
  };

  /***********************************/
  /***** GATEKEEPER MANAGEMENT *******/
  /***********************************/

  const [gatekeeperAgent, setGatekeeperAgent] = useState<AnyAgentDto>();

  const switchToGatekeeper = (gk: AnyAgentDto) => {
    setGatekeeperAgent(gk);
    setPageStatus('profileGatekeeper');
  };

  const openMainAgent = () => {
    setPageStatus('profile');
  };

  const abort = () => {
    if (onAbort) {
      onAbort();
    }
  };

  /***********************************/
  /************ RENDERING ************/
  /***********************************/

  return (
    <div className="h-full relative">
      {pageStatus !== 'summary' && (
        <div className="absolute flex flex-row space-x-2 items-center top-[20px] left-[30px] bg-white backdrop-blur-3xl px-3 py-2 rounded-lg border-[1px] border-gray-50 shadow-2xl">
          <Image
            src={`/images/black-logo-with-text.svg`}
            alt="Hyperbound logo"
            width={120}
            height={24}
            priority
          />

          {isCompetitionOrg && (
            <>
              <XIcon className="w-4 h-4" />
              <Image
                src={`/images/competition/league-of-sales-legends/pclub.svg`}
                alt="RB2B logo"
                width={86}
                height={24}
                priority
              />
            </>
          )}
        </div>
      )}

      {pageStatus === 'profile' && (
        <AgentProfile
          agent={agent}
          startCall={startCall}
          showAgentStatusBtn={
            isLoggedIn &&
            !isCompetitionOrg &&
            canAccess(AppPermissions.MANAGE_BOTS)
          }
          onGatekeeperSelected={switchToGatekeeper}
          bgImage={bgImage}
          narrowWidthDisplay={narrowWidthDisplay}
          showLeaderboardDateFilter={showLeaderboardDateFilter}
          isMultiParty={isMultiParty}
        />
      )}
      {pageStatus === 'profileGatekeeper' && gatekeeperAgent && (
        <AgentProfile
          bgImage={bgImage}
          agent={gatekeeperAgent}
          startCall={startCall}
          showAgentStatusBtn={isLoggedIn && !isCompetitionOrg}
          isGatekeeper={true}
          guardedAgent={agent}
          onGuardedAgentSelected={openMainAgent}
          narrowWidthDisplay={narrowWidthDisplay}
          showLeaderboardDateFilter={showLeaderboardDateFilter}
        />
      )}
      {pageStatus === 'call' && (
        <Call
          bgImage={bgImage}
          agent={agent}
          onEnd={openCallScoreSummary}
          onAbort={abort}
          doVideoCall={isVideoCall}
          narrowWidthDisplay={narrowWidthDisplay}
          isMultiParty={isMultiParty}
        />
      )}
      {pageStatus === 'callGatekeeper' && gatekeeperAgent && (
        <Call
          bgImage={bgImage}
          agent={gatekeeperAgent}
          onEnd={endCallWithGatekeeper}
          onAbort={abort}
          isGatekeeper={true}
          guardedAgent={agent}
          doVideoCall={isVideoCall}
          narrowWidthDisplay={narrowWidthDisplay}
        />
      )}
      {pageStatus === 'summary' && call?.vapiId && (
        <div className="bg-white overflow-auto rounded-t-3xl pt-1 h-full">
          <CallSummary
            vapiId={call?.vapiId}
            showLeaderboardDateFilter={showLeaderboardDateFilter}
            taskAndAttempts={taskAndAttempts}
          />
        </div>
      )}
    </div>
  );
}
