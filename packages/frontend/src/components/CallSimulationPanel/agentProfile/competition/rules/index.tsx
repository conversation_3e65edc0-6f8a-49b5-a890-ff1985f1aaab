import { AgentDto } from '@/lib/Agent/types';
import Markdown from 'react-markdown';
import { Id, toast } from 'react-toastify';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

interface IProps {
  agent: AgentDto;
}

export default function CompetitionRules({ agent }: IProps) {
  return (
    <Markdown rehypePlugins={[rehypeRaw]} remarkPlugins={[remarkGfm]}>
      {agent.competitionAgent?.metadata?.publicInfo?.rules || ''}
    </Markdown>
  );
}
