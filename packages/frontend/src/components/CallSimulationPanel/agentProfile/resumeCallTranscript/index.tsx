import { AnyAgentDto } from '@/lib/Agent/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import AgentAvatar from '@/components/Avatars/Agent';

interface IProps {
  agent: AnyAgentDto;
}

export default function ResumeCallTranscript({ agent }: IProps) {
  let resumeCallTranscript: any[] = [];
  if (agent && agent.research) {
    const existingAgentResearch = JSON.parse(agent.research || '{}');
    if (existingAgentResearch.messages) {
      resumeCallTranscript = existingAgentResearch.messages.split('\n');
      resumeCallTranscript.pop(); //last message is empty.....splitting on \n
    }
  }

  return (
    <div className="h-[400px] overflow-auto pr-2">
      {(resumeCallTranscript || []).map((s: string, i: number) => {
        const isAssistant = i % 2 === 0;
        if (isAssistant) {
          return (
            <div key={i} className="flex mb-4">
              <AgentAvatar className="w-8 h-8 mr-2" agent={agent} />

              <div
                className={
                  'rounded-xl text-xs py-3 px-4 max-w-[60%] bg-gray-100 rounded-tl-md'
                }
              >
                {s}
              </div>
            </div>
          );
        } else {
          return (
            <div key={i} className="flex mb-4">
              <div className="flex-1"></div>
              <div
                className={
                  'rounded-xl text-xs py-3 px-4 max-w-[60%] bg-blue-500 text-white rounded-br-md'
                }
              >
                {s}
              </div>
              <Avatar className="w-8 h-8 ml-2">
                <AvatarFallback className="text-sm">Rep</AvatarFallback>
              </Avatar>
            </div>
          );
        }
      })}
    </div>
  );
}
