import DatesFilter from '@/common/Analytics/DashboardTab/Filters/DateFilter';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import useOrgLeaderboard from '@/hooks/useOrgLeaderboard';
import useUserSession from '@/hooks/useUserSession';
import { AnyAgentDto } from '@/lib/Agent/types';
import AnalyticsService from '@/lib/Analytics';
import { DateFilterType } from '@/lib/Analytics/types';
import LinksManager from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useState } from 'react';

interface IProps {
  agent: AnyAgentDto;
  showLeaderboardDateFilter?: boolean;
}

export default function Leaderborad({
  agent,
  showLeaderboardDateFilter = false,
}: IProps) {
  const { isLoggedIn, blurLeaderboard, defaultLeaderboardDateRange } =
    useUserSession();

  const defaultDateRange = AnalyticsService.getDatesRange({
    fromDate: new Date(),
    toDate: new Date(),
    range: defaultLeaderboardDateRange,
  });

  const [filterState, setFilterState] = useState<DateFilterType>({
    fromDate: defaultDateRange.from,
    toDate: defaultDateRange.to,
    range: defaultLeaderboardDateRange,
  });

  const onDatesUpdated = (dates: DateFilterType) => {
    const { from, to } = AnalyticsService.getDatesRange(dates);
    setFilterState({
      range: dates.range,
      fromDate: from,
      toDate: to,
    });
  };

  const { data: leaderboard, isLoading: isLoadingLeaderboard } =
    useOrgLeaderboard(filterState.fromDate, filterState.toDate, agent?.id);

  return (
    <div className="px-2">
      {showLeaderboardDateFilter && (
        <div className="mb-6">
          <div className="text-sm ml-1 mb-1 font-semibold">Date range:</div>
          <DatesFilter current={filterState} onDatesUpdated={onDatesUpdated} />
        </div>
      )}
      {isLoadingLeaderboard ? (
        <div className="space-y-2 max-h-96 overflow-auto">
          {[1, 2, 3].map((num) => (
            <div key={num} className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
              <Skeleton className="h-4 w-[50px]" />
            </div>
          ))}
        </div>
      ) : (
        <div
          className={
            'space-y-2 overflow-auto text-sm ' + (blurLeaderboard && 'blur')
          }
        >
          {isLoggedIn ? (
            !leaderboard?.leaderboard?.length && <p>No active reps found.</p>
          ) : (
            <p>Book a demo to access leaderboard</p>
          )}

          {(leaderboard?.leaderboard || []).map((rep, i) => (
            <Link
              key={i}
              href={
                !blurLeaderboard
                  ? agent
                    ? LinksManager.trainingCalls(
                        `?buyers=${agent.id}&reps=${rep.userId}`,
                      )
                    : LinksManager.members(`${rep.userId}`)
                  : ''
              }
              className="h-min text-black"
            >
              <Button
                variant={'ghost'}
                className="flex w-full text-left items-center py-8 px-4 rounded-lg"
              >
                <p
                  className={cn(
                    'text-xl text-muted-foreground/50 mr-4 font-bold',
                    {
                      'text-green-600': rep.rank <= 3,
                    },
                  )}
                >
                  {rep.rank === 1 ? '⭐️' : rep.rank}
                </p>
                <Avatar className="h-8 w-8">
                  {rep?.avatar && <AvatarImage src={rep.avatar} alt="Avatar" />}
                  <AvatarFallback className="text-muted-foreground">
                    {rep?.firstName?.charAt(0) || ''}
                    {rep?.lastName?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-2 space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {rep?.firstName || ''} {rep?.lastName || ''}
                  </p>
                </div>
                <div className="ml-auto font-semibold text-xl">
                  {Number(rep.score || 0).toLocaleString('en-US', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                  <span className="text-xs"> / 100</span>
                </div>
              </Button>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}
