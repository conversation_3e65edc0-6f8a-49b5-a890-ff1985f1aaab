import { AgentCallType, AnyAgentDto } from '@/lib/Agent/types';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Cog, Edit2Icon, LayoutListIcon, LockIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import useUserSession from '@/hooks/useUserSession';
import LinksManager from '@/lib/linksManager';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  agent: AnyAgentDto;
}

export default function SettingsBtn({ agent }: IProps) {
  const router = useRouter();
  const { isLoggedIn, isPilotEnded, isCompetitionOrg, canAccess } =
    useUserSession();

  const editDropdownItem = canAccess(AppPermissions.MANAGE_BOTS) && (
    <DropdownMenuItem
      className="cursor-pointer"
      onClick={(e) => {
        e.stopPropagation();
        if (agent.callType == AgentCallType.FOCUS) {
          router.push(
            `/buyers/${agent.vapiId}/edit/focus?callType=${agent.callType}`,
          );
        } else {
          router.push(
            `/buyers/${agent.vapiId}/edit/main?callType=${agent.callType}`,
          );
        }
      }}
      disabled={isPilotEnded || !isLoggedIn}
    >
      {isLoggedIn ? (
        <Edit2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
      ) : (
        <LockIcon className="w-4 h-4 mr-2 text-muted-foreground" />
      )}
      <span>Edit</span>
    </DropdownMenuItem>
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger>
              <Button
                disabled={false}
                size={'lg'}
                variant={'outline'}
                className="text-white hover:text-black w-[52px] h-[52px] rounded-full bg-white/10 p-4 border-0"
              >
                <Cog size={24} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p>Buyer Settings</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="center">
        {isCompetitionOrg ? (
          <></>
        ) : isLoggedIn ? (
          editDropdownItem
        ) : (
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger disabled>{editDropdownItem}</TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Book a demo to customise your own buyer bot</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        <DropdownMenuItem
          className="cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            router.push(LinksManager.trainingCalls(`?buyers=${agent.id}`));
          }}
          disabled={isPilotEnded}
        >
          <LayoutListIcon className="w-4 h-4 mr-2 text-muted-foreground" />
          <span>Call history</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
