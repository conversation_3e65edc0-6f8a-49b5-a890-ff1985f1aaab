import EmotionBadge from '@/common/CreateBuyerForm/BotPreview/EmotionBadge';
import AgentAvatar from '@/components/Avatars/Agent';
import { Skeleton } from '@/components/ui/skeleton';
import { AnyAgentDto } from '@/lib/Agent/types';

interface IMultiPartyAgentCard {
  agent: AnyAgentDto;
}

export default function MultiPartyAgentCard({ agent }: IMultiPartyAgentCard) {
  return (
    <div className="flex flex-col bg-[#FFFFFFE5] p-3 rounded-[12px] shadow-md items-center">
      <AgentAvatar className="w-[48px] h-[48px]" agent={agent} />
      <div className="flex items-center mt-2">
        <div className="text-[#2E3035] font-semibold leading-5 text-sm mr-1">
          {`${agent.firstName} ${agent.lastName}`}
        </div>
        <div className="flex items-center text-white text-[10px] w-[16px] h-[14px] rounded-[4px] px-[3px] mr-[2px] bg-[linear-gradient(180deg,_#3DC3E6_0%,_#49C8CF_33.33%,_#36C4BF_98.96%)] font-medium leading-[100%] tracking-[0%] ">
          AI
        </div>
      </div>
      <div className="text-[#71717A] mt-1 text-xs">
        {agent.jobTitle} @ {agent.companyName}
      </div>
      <div className="mt-2">
        {agent?.emotionalState ? (
          <EmotionBadge emotionalState={agent?.emotionalState} size="small" />
        ) : (
          <Skeleton className="w-[150px] h-[30px] rounded-xl" />
        )}
      </div>
    </div>
  );
}
