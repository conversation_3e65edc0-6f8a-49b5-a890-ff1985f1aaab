import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import {
  CALL_TYPE_TO_ICON,
  CHALLENGE_BOT_VAPI_ID,
} from '@/common/Sidebar/OldSidebar';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import useUserSession from '@/hooks/useUserSession';
import AgentService from '@/lib/Agent';
import {
  AgentCallType,
  AgentDto,
  AgentStatus,
  AnyAgentDto,
  checkIsPublicAgent,
} from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import _ from 'lodash';
import {
  AlertTriangleIcon,
  BrainIcon,
  LayoutListIcon,
  LockIcon,
  PhoneIcon,
  Tag,
  TrophyIcon,
  Video,
} from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import Leaderborad from './leaderboard';
import ResumeCallTranscript from './resumeCallTranscript';
import SettingsBtn from './settingsBtn';
import DemoWelcomeModal from '@/components/DemoWelcomeModal';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import useMaintenance from '@/hooks/useMaintenance';
import { useAuthInfo } from '@propelauth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import Analytics from '@/system/Analytics';
import { BuyerEvents } from '@/system/Analytics/events/BuyerEvents';
import { DemoInboundFormEvents } from '@/system/Analytics/events/DemoInboundFormEvents';
import CompetitionRules from './competition/rules';
import CompetitionWhatAreYouSelling from './competition/whatAreYouSelling';
import CompetitionScoring from './competition/scoring';
import Link from 'next/link';
import LinksManager from '@/lib/linksManager';
import CallInstructions from '@/components/CallInstructions';
import AgentAvatar from '@/components/Avatars/Agent';
import { getResearchData } from '@/common/CreateBuyerForm/Main/utils';
import { useCompetitionInfoForAgentPanel } from '@/hooks/useLearningModules';
import Image from 'next/image';
import RepInstructionsModal from '@/components/RepInstructionsModal';

interface IProps {
  agent: AnyAgentDto;
  includeAgentsTags?: boolean;
  showAgentStatusBtn?: boolean;
  onGatekeeperSelected?: (gk: AnyAgentDto) => void;
  isGatekeeper?: boolean;
  guardedAgent?: AnyAgentDto;
  onGuardedAgentSelected?: (a: AnyAgentDto) => void;
  bgImage?: string;
  startCall: (isGatekeeper: boolean, useVideo: boolean) => void;
  narrowWidthDisplay?: boolean;
  showLeaderboardDateFilter?: boolean;
}

export default function AgentProfile({
  agent,
  includeAgentsTags,
  showAgentStatusBtn,
  isGatekeeper,
  bgImage,
  startCall,
  narrowWidthDisplay = false,
  showLeaderboardDateFilter = false,
}: IProps) {
  const isPublicAgent = checkIsPublicAgent(agent);
  const Icon =
    CALL_TYPE_TO_ICON?.[agent?.callType as keyof typeof CALL_TYPE_TO_ICON]
      ?.Icon;
  const [agentStatus, setAgentStatus] = useState<AgentStatus>(agent?.status);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const authInfo = useAuthInfo();
  const [warningModalOpen, setWarningModalOpen] = useState<boolean>(false);
  const {
    blurLeaderboard,
    isPilotEnded,
    videoCallEnabled,
    isLoggedIn,
    isInIframe,
    isCompetitionOrg,
    competitionTag,
  } = useUserSession();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const { data: maintenance } = useMaintenance(!isLoggedIn);
  const {
    data: internalCompetitions,
    isLoading: isLoadingInternalCompetitionInfo,
  } = useCompetitionInfoForAgentPanel(agent.id);

  const isVideo = useRef(false);

  let showResumeCallTranscript = false;
  if (agent && agent.research) {
    const existingAgentResearch = JSON.parse(agent.research || '{}');
    if (existingAgentResearch.messages) {
      const tmp = existingAgentResearch.messages.split('\n');
      tmp.pop(); //last message is empty.....splitting on \n
      if (tmp.length > 0) {
        showResumeCallTranscript = true;
      }
    }
  }

  const { warm_call_context, discovery_call_context } = useMemo(
    () => getResearchData(agent),
    [agent],
  );

  const errorToastId = useRef<Id | null>(null);

  const startCallInternal = () => {
    startCall(isGatekeeper || false, isVideo.current);
  };

  const onClickStartCall = async (video: boolean) => {
    // video calls cannot be taken in a narrow width display
    if (video && narrowWidthDisplay) {
      router.push(`/buyers/${agent.vapiId}?autostart=video`);
      return;
    }
    isVideo.current = video;
    if (hbDemoInboundForm?.blacklisted) {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'You have been blocked for inappropriate behavior. Please contact the Hyperbound <NAME_EMAIL>.',
        );
      }
      Analytics.track(BuyerEvents.START_CALL_CLICKED_BLACKLISTED, {
        agentId: agent.id,
      });
      return;
    }

    await queryClient.refetchQueries({
      queryKey: ['maintenance'],
    });

    if (
      !_.isEmpty(hbDemoInboundForm) ||
      (authInfo.isLoggedIn && !isPublicAgent)
    ) {
      if (!queryClient.getQueryData(['warningModalOpened'])) {
        setWarningModalOpen(true);
      } else {
        startCallInternal();
      }
    } else {
      Analytics.track(DemoInboundFormEvents.OPENED, {
        invite: queryClient.getQueryData(['hbInvite']) || null,
        from: 'individual_buyer_page',
        agentId: agent.id,
      });
      setModalOpen(true);
    }
  };

  const [callAutostarted, setCallAutostarted] = useState(false);
  useEffect(() => {
    const autostartCall = searchParams.get('autostart');
    if (autostartCall && !callAutostarted) {
      // so that call does not autostart multiple times
      setCallAutostarted(true);
      onClickStartCall(autostartCall === 'video');
    }
  }, [searchParams.toString(), callAutostarted]);

  /*****************************************/
  /************** AGENT STATUS *************/
  /*****************************************/

  const queryClient = useQueryClient();

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
    onSuccess: (agent) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error editing the buyer. Please try again.',
        );
      }
    },
  });

  /*****************************************/
  /*************** RENDERING ***************/
  /*****************************************/

  return (
    <div className="flex flex-col h-full">
      <div
        className={
          'bg-white mx-[10px] flex flex-col flex-1 rounded-xl overflow-hidden bg-no-repeat bg-cover'
        }
        style={{ backgroundImage: bgImage }}
      >
        <div className={'flex-1 flex flex-col mt-4 px-4'}>
          {isCompetitionOrg && (
            <div>
              <div className="w-full flex flex-row justify-end space-x-2">
                <Link
                  href={`/competitions/${competitionTag}/leaderboard`}
                  className="h-min"
                >
                  <Button
                    className="bg-white text-primary hover:bg-white/90"
                    variant={'default'}
                  >
                    <TrophyIcon className="w-4 h-4 mr-2" />
                    View leaderboard
                  </Button>
                </Link>
                <Link href={LinksManager.trainingCalls()} className="h-min">
                  <Button
                    className="bg-white text-primary hover:bg-white/90"
                    variant={'default'}
                  >
                    <LayoutListIcon className="w-4 h-4 mr-2" />
                    View calls
                  </Button>
                </Link>
                <Badge className="text-sm">
                  Tries Left:{' '}
                  {(agent as AgentDto)?.competitionAgent?.user?.numTriesLeft ||
                    0}
                </Badge>
              </div>
            </div>
          )}
          <div className="flex items-center h-full">
            {/****** AGENT DETAILS *******/}
            {/****** AGENT DETAILS *******/}
            {/****** AGENT DETAILS *******/}
            <div
              className={cn(
                'rounded-xl p-6 flex-1 mx-10 flex flex-col bg-white/70 backdrop-blur-3xl border-2 border-white overflow-x-hidden overflow-y-auto shadow-xl',
                {
                  'h-[60vh]': !narrowWidthDisplay,
                  'my-4': !isCompetitionOrg,
                  'my-2': isCompetitionOrg,
                },
              )}
            >
              <CallInstructions
                description={agent?.description}
                {...(agent.callType === AgentCallType.WARM && {
                  callContext: warm_call_context,
                })}
                {...(agent.callType === AgentCallType.DISCOVERY && {
                  callContext: discovery_call_context,
                })}
              />

              <div className="flex flex-col items-center text-center mt-6 flex-1 mb-4">
                <div className={'relative mb-4'}>
                  <AgentAvatar
                    className="w-[140px] h-[140px] border-2 border-white"
                    agent={agent}
                  />

                  <div
                    className={cn(
                      'rounded-full p-1 w-6 h-6 border-white border-[3px] absolute bottom-2 right-3',
                      {
                        'bg-green-500': agentStatus === AgentStatus.ACTIVE,
                        'bg-gray-500': agentStatus === AgentStatus.INACTIVE,
                      },
                    )}
                  ></div>
                </div>
                <p className="text-lg font-medium text-black/80">
                  {agent.firstName} {agent.lastName}
                </p>
                <p className="text-sm text-muted-foreground">
                  {agent.jobTitle} @ {agent.companyName}
                </p>
                {!narrowWidthDisplay && (
                  <div className="mt-2">
                    {(agent.emotionalState || agent.gender) && (
                      <div
                        className={cn('flex mt-2 items-center justify-center', {
                          'space-x-1': agent.emotionalState && agent.gender,
                          // (emotionalState && salesMethodology) ||
                          // (emotionalState && gender) ||
                          // (gender && salesMethodology),
                        })}
                      >
                        {agent.callType && (
                          <Badge className="mt-1" variant="secondary">
                            {Icon && <Icon className="mr-1 h-3 w-3" />}
                            {CALL_TYPE_OPTIONS.find(
                              (item) => item.value === agent.callType,
                            )?.label ||
                              (agent.callType === 'focus'
                                ? 'Focus Call'
                                : agent.callType)}
                          </Badge>
                        )}
                        {agent?.callType !== 'focus' && (
                          <>
                            {agent.emotionalState ? (
                              <Badge className="mt-1" variant={'default'}>
                                <BrainIcon className="w-3 h-3 mr-1" />{' '}
                                {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                                  (item) => item.value === agent.emotionalState,
                                )?.label ||
                                  agent.emotionalState ||
                                  ''}
                              </Badge>
                            ) : (
                              <Skeleton className={cn('w-16 h-6 mr-1')} />
                            )}
                          </>
                        )}
                        {agent.bookRate && (
                          <Badge className="mt-1" variant="default">
                            Book Rate:{' '}
                            {Number(agent?.bookRate).toLocaleString('en-US', {
                              style: 'percent',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 1,
                            })}
                          </Badge>
                        )}
                        {agent.gatekeepers?.length > 0 && (
                          <Badge className="mt-1" variant="default">
                            <LockIcon size={16} className="mr-2" />
                            Has Gatekeeper
                          </Badge>
                        )}
                      </div>
                    )}
                    {includeAgentsTags && (
                      <div className="mt-1 flex items-center justify-center">
                        {!isPublicAgent &&
                          agent.tags?.map((tag) => (
                            <Badge
                              key={tag.id}
                              variant="default"
                              className="m-1 bg-teal-600"
                            >
                              <Tag size={12} className="mr-1" />
                              {tag.name}
                            </Badge>
                          ))}
                      </div>
                    )}
                  </div>
                )}
                {agent?.openerLine &&
                  agent.callType === AgentCallType.FOCUS && (
                    <p className="mt-4">{agent.openerLine}</p>
                  )}

                {showAgentStatusBtn && (
                  <div className="flex space-x-2 mt-4">
                    {agent?.status !== AgentStatus.DRAFT ? (
                      <div className="flex items-center space-x-2">
                        <Switch
                          disabled={
                            updateAgentMutation.isPending || isPilotEnded
                          }
                          checked={agentStatus === AgentStatus.ACTIVE}
                          onCheckedChange={(value) => {
                            if (agentStatus == AgentStatus.ACTIVE) {
                              setAgentStatus(AgentStatus.INACTIVE);
                            } else {
                              setAgentStatus(AgentStatus.ACTIVE);
                            }
                            updateAgentMutation.mutate({
                              id: agent?.id,
                              status: value
                                ? AgentStatus.ACTIVE
                                : AgentStatus.INACTIVE,
                            });
                          }}
                          id="status-toggle"
                        />
                        {updateAgentMutation.isPending ? (
                          <Skeleton className="w-16 h-[22px] rounded-md" />
                        ) : (
                          <Badge
                            variant="default"
                            className={cn({
                              'bg-green-600':
                                agentStatus === AgentStatus.ACTIVE,
                              'bg-red-600':
                                agentStatus === AgentStatus.INACTIVE,
                            })}
                          >
                            {_.capitalize(agentStatus.toLowerCase())}
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <Badge variant="default">
                        {_.capitalize(agent?.status?.toLowerCase())}
                      </Badge>
                    )}
                  </div>
                )}

                {/*

                //disabeling gatekeeper selection for now.....a gatekeeper always answers the call

                {!isGatekeeper &&
                  agent.gatekeepers?.length > 0 &&
                  onGatekeeperSelected && (
                    <div className="mt-8">
                      <Select
                        onValueChange={(v: string) => {
                          const aig = Number(v);
                          agent?.gatekeepers?.map((gk) => {
                            if (gk.id === aig) {
                              onGatekeeperSelected(gk);
                            }
                          });
                        }}
                      >
                        <SelectTrigger>
                          <div className="flex items-center text-sm">
                            <DoorOpenIcon className="w-4 h-4 mr-2" />
                            Choose gatekeeper
                          </div>
                        </SelectTrigger>

                        <SelectContent>
                          {agent?.gatekeepers?.map((gk, i) => {
                            return (
                              <SelectItem
                                key={"gk-" + gk.id}
                                value={String(gk.id)}
                              >
                                <div className="flex items-center">
                                  <Avatar className="w-6 h-6 mr-2">
                                    <AvatarImage
                                      src={`/images/${gk?.avatar}`}
                                    />
                                    <AvatarFallback className="text-sm">
                                      {gk?.firstName?.charAt(0) || ""}
                                      {gk?.lastName?.charAt(0) || ""}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="capitalize">
                                    {gk?.firstName || ""} {gk?.lastName || ""}
                                  </div>
                                </div>
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                {isGatekeeper && guardedAgent && (
                  <div className="mt-8">
                    <div
                      className="flex items-center rounded-full border px-4 py-2 cursor-pointer hover:bg-muted"
                      onClick={() => {
                        if (onGuardedAgentSelected) {
                          onGuardedAgentSelected(guardedAgent);
                        }
                      }}
                    >
                      <ChevronLeft className="w-4 h-4 mr-2" />
                      <Avatar className="w-6 h-6 mr-2">
                        <AvatarImage src={`/images/${guardedAgent?.avatar}`} />
                        <AvatarFallback className="text-sm">
                          {guardedAgent?.firstName?.charAt(0) || ""}
                          {guardedAgent?.lastName?.charAt(0) || ""}
                        </AvatarFallback>
                      </Avatar>
                      <div className="capitalize">
                        {guardedAgent?.firstName || ""}{" "}
                        {guardedAgent?.lastName || ""}
                      </div>
                    </div>
                  </div>
                )}

                   */}
              </div>
            </div>
            {/**************************/}
            {/****** RIGHT PANEL FOR NON-COMPETITION-ORGS ******/}
            {/**************************/}
            {!narrowWidthDisplay && !isCompetitionOrg && (
              <div
                className={cn(
                  'rounded-xl p-6 flex-1 mr-10 bg-white/70 backdrop-blur-3xl border-2 border-white h-[60vh] overflow-hidden hidden md:block shadow-xl',
                  {
                    'my-4': !isCompetitionOrg,
                    'my-2': isCompetitionOrg,
                  },
                )}
              >
                {!isLoadingInternalCompetitionInfo &&
                  internalCompetitions &&
                  internalCompetitions.length > 0 && (
                    <div>
                      <Image
                        src={'/images/competition/comp-agent-banner.svg'}
                        alt="Competition Logo"
                        width={86}
                        height={24}
                        className="w-full"
                      />
                      <div>
                        {internalCompetitions.map((comp, i: number) => {
                          return (
                            <div key={i} className="mt-4 text-sm">
                              <div className="font-semibold">
                                Part of {comp.name}
                              </div>
                              <div className="mt-2">{comp.description}</div>
                              {comp.incorporateFillerWordsScore && (
                                <div className="mt-4 text-sm">
                                  <div className="font-semibold">
                                    Filler words score enabled
                                  </div>
                                  <div className="mt-2">
                                    Filler words score reflects how often you
                                    use words like “um” and “uh” during calls.
                                    This is used as a tie-breaker for this
                                    competition.
                                  </div>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                {!internalCompetitions ||
                  (internalCompetitions.length == 0 && (
                    <Tabs defaultValue="leaderboard" className="">
                      <TabsList className="bg-white/10 backdrop-blur-3xl border-white border">
                        <TabsTrigger value="leaderboard">
                          Leaderboard{' '}
                          {(blurLeaderboard || !isLoggedIn) && (
                            <LockIcon size={16} className="ml-1" />
                          )}
                        </TabsTrigger>
                        {showResumeCallTranscript && (
                          <TabsTrigger value="prev-transcript">
                            Previous Transcript
                          </TabsTrigger>
                        )}
                      </TabsList>
                      <TabsContent
                        value="leaderboard"
                        className="pt-4 overflow-auto max-h-[60vh]"
                      >
                        <Leaderborad
                          agent={agent}
                          showLeaderboardDateFilter={showLeaderboardDateFilter}
                        />
                      </TabsContent>
                      <TabsContent value="prev-transcript" className="pt-4">
                        <ResumeCallTranscript agent={agent} />
                      </TabsContent>
                    </Tabs>
                  ))}
              </div>
            )}
            {/**************************/}
            {/****** RIGHT PANEL FOR COMPETITION-ORGS ******/}
            {/**************************/}
            {!narrowWidthDisplay && isCompetitionOrg && (
              <div
                className={cn(
                  'rounded-xl p-6 flex-1 mr-10 bg-white/70 backdrop-blur-3xl border-2 border-white h-[60vh] overflow-hidden hidden md:block shadow-xl',
                  {
                    'my-4': !isCompetitionOrg,
                    'my-2': isCompetitionOrg,
                  },
                )}
              >
                <Tabs defaultValue="rules" className="w-full">
                  <TabsList className="bg-white/10 backdrop-blur-3xl border-2 border-white">
                    <TabsTrigger value="rules">Overview</TabsTrigger>
                    <TabsTrigger value="whatYouAreSelling">
                      What you&apos;re selling?
                    </TabsTrigger>
                    <TabsTrigger value="scoring">Scoring</TabsTrigger>
                    {showResumeCallTranscript && (
                      <TabsTrigger value="prev-transcript">
                        Transcript
                      </TabsTrigger>
                    )}
                  </TabsList>
                  <TabsContent
                    value="rules"
                    className="pt-4 overflow-auto max-h-[48vh] prose max-w-4xl mt-3 text-sm leading-snug w-full"
                  >
                    <CompetitionRules agent={agent as AgentDto} />
                  </TabsContent>
                  <TabsContent
                    value="whatYouAreSelling"
                    className="pt-4 overflow-auto max-h-[48vh] prose max-w-4xl mt-3 text-sm leading-snug"
                  >
                    <CompetitionWhatAreYouSelling agent={agent as AgentDto} />
                  </TabsContent>
                  <TabsContent
                    value="scoring"
                    className="pt-4 overflow-auto max-h-[48vh] prose max-w-4xl mt-3 text-sm leading-snug"
                  >
                    <CompetitionScoring agent={agent as AgentDto} />
                  </TabsContent>
                  <TabsContent value="prev-transcript" className="pt-4">
                    <ResumeCallTranscript agent={agent} />
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </div>
        </div>
      </div>
      {/******************************************/}
      {/**************** FOOTER ******************/}
      {/******************************************/}
      <div className="h-[70px] mx-[10px] flex items-center">
        <div className="flex-1"></div>
        <div className="mr-2">
          <SettingsBtn agent={agent} />
        </div>
        <div>
          {isCompetitionOrg &&
            ((agent as AgentDto)?.competitionAgent?.user?.numTriesLeft || 0) >
              0 && (
              <Button
                disabled={agent.status === AgentStatus.INACTIVE || isPilotEnded}
                variant={'default'}
                size={'lg'}
                className="shadow-md text-base drop-shadow-2xl hover:opacity-80 transition-opacity duration-200"
                style={{
                  background:
                    'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                  backgroundImage:
                    '-webkit-linear-gradient(to bottom, #000000, #5189CE, #A168A2)',
                }}
                onClick={() => onClickStartCall(false)}
              >
                <PhoneIcon className="mr-2" size={20} />
                <span>Start Call</span>
              </Button>
            )}
          {!isCompetitionOrg && (
            <Button
              disabled={agent.status === AgentStatus.INACTIVE || isPilotEnded}
              variant={'default'}
              size={'lg'}
              className="shadow-md text-base drop-shadow-2xl hover:opacity-80 transition-opacity duration-200"
              style={{
                background:
                  'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                backgroundImage:
                  '-webkit-linear-gradient(to bottom, #000000, #5189CE, #A168A2)',
              }}
              onClick={() => onClickStartCall(false)}
            >
              <PhoneIcon className="mr-2" size={20} />
              <span>Start Call</span>
            </Button>
          )}
        </div>
        {videoCallEnabled && (
          <div>
            <Button
              disabled={agent.status === AgentStatus.INACTIVE || isPilotEnded}
              variant={'default'}
              size={'lg'}
              className="ml-2 shadow-md text-base drop-shadow-2xl hover:opacity-80 transition-opacity duration-200"
              onClick={() => onClickStartCall(true)}
            >
              <Video className="mr-2" size={20} />
              <span>Start Video Call</span>
            </Button>
          </div>
        )}
        <div className="flex-1"></div>
      </div>
      <Dialog open={warningModalOpen} onOpenChange={setWarningModalOpen}>
        <DialogContent className="close-btn">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              {maintenance?.disableCall && (
                <AlertTriangleIcon className="mr-2 text-yellow-500" />
              )}
              {!maintenance?.disableCall
                ? "Let's get started!"
                : 'High volume alert, please check back in 1 hour'}
            </DialogTitle>
            <DialogDescription className="py-4">
              {maintenance?.callWarningMessage ||
                `At times, the bot may be unresponsive, or have unusual lag times. `}
              We are always working to improve the experience!{' '}
              {!isInIframe &&
                !isCompetitionOrg &&
                `Your
                        call will be visible to other Hyperbound
                        ${isLoggedIn ? '' : ' demo'} users in your org
                        @
                        ${
                          isLoggedIn && !isPublicAgent
                            ? authInfo?.user?.email?.split('@')[1]
                            : hbDemoInboundForm?.email?.split('@')[1]
                        } in the 'Call History' tab on the left.`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button
                type="submit"
                variant={'outline'}
                onClick={
                  maintenance?.disableCall
                    ? () => setWarningModalOpen(false)
                    : () => {
                        queryClient.setQueryData(['warningModalOpened'], true);
                        startCallInternal();
                      }
                }
              >
                {!maintenance?.disableCall ? (
                  <>
                    <PhoneIcon className="mr-2 h-4 w-4" />I understand, start
                    call
                  </>
                ) : (
                  'Ok, I will try later'
                )}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <DemoWelcomeModal
        agent={agent}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        onSubmit={() => {
          setModalOpen(false);
          setTimeout(() => {
            setWarningModalOpen(true);
          }, 200);
        }}
        registerForChallenge={agent?.vapiId === CHALLENGE_BOT_VAPI_ID}
      />

      <RepInstructionsModal
        description={agent?.description || ''}
        {...(agent.callType === AgentCallType.WARM && {
          callContext: warm_call_context,
        })}
        {...(agent.callType === AgentCallType.DISCOVERY && {
          callContext: discovery_call_context,
        })}
      />
    </div>
  );
}
