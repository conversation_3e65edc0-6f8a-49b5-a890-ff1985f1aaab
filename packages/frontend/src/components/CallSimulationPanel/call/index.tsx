import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import {
  CALL_TYPE_TO_ICON,
  CHALLENGE_BOT_VAPI_ID,
} from '@/common/Sidebar/OldSidebar';
import TroubleshootingGuide from '@/components/TroubleshootingGuide';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import HorizontalDotsLoading from '@/components/ui/Hyperbound/horizontalDotsLoading';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useCallSimulation from '@/hooks/useCallSimulation';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useUserSession from '@/hooks/useUserSession';
import { AnyAgentDto } from '@/lib/Agent/types';
import { CallDto } from '@/lib/Call/types';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import {
  BrainIcon,
  Loader2Icon,
  MonitorPlay,
  MonitorStop,
  PhoneIncomingIcon,
  PhoneOff,
  TriangleAlert,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import boxStyles from './style.module.css';
import AgentAvatar from '@/components/Avatars/Agent';

interface IProps {
  agent: AnyAgentDto;
  onEnd: (call: CallDto, isGatekeeperConnectingToBuyer: boolean) => void;
  isGatekeeper?: boolean;
  guardedAgent?: AnyAgentDto;
  onAbort?: () => void;
  bgImage?: string;
  doVideoCall: boolean;
  narrowWidthDisplay?: boolean;
}

export default function Call({
  agent,
  onEnd,
  isGatekeeper,
  guardedAgent,
  onAbort,
  bgImage,
  doVideoCall,
  narrowWidthDisplay = false,
}: IProps) {
  const sessionUser = useUserSession();

  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const callerFirstName =
    sessionUser?.firstName || hbDemoInboundForm?.name?.split(' ')[0] || '';
  const callerLastName =
    sessionUser?.lastName || hbDemoInboundForm?.name?.split(' ')[1] || '';
  const validCallerNamePresent = !!callerFirstName || !!callerLastName;

  const [isCallInProgress, setIsCallInProgress] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showMediaProblemsBunner, setShowMediaProblemsBunner] =
    useState<boolean>(false);
  const [showTroubleshootingGuide, setShowTroubleshootingGuide] =
    useState<boolean>(false);
  const blockNavigation = useRef<boolean>(false);

  /**************************************/
  /*************** TIMER ****************/
  /**************************************/
  const progress = useRef<number>(0);
  const [progressLabel, setProgressLabel] = useState<string>('00:00');
  const timeoutProgressRef = useRef<ReturnType<typeof setTimeout>>();

  const startProgress = () => {
    if (timeoutProgressRef.current) {
      clearTimeout(timeoutProgressRef.current);
    }
    progress.current = 0;
    setProgressLabel('00:00');
    timeoutProgressRef.current = setTimeout(() => {
      tickProgress();
    }, 1000);
  };

  const tickProgress = () => {
    const p = progress.current + 1;
    const minutes = Math.floor(p / 60);
    const seconds = p - minutes * 60;

    const str_pad_left = (string: number, pad: string, length: number) => {
      return (new Array(length + 1).join(pad) + string).slice(-length);
    };

    const finalTime =
      str_pad_left(minutes, '0', 2) + ':' + str_pad_left(seconds, '0', 2);
    setProgressLabel(finalTime);
    progress.current++;
    timeoutProgressRef.current = setTimeout(() => {
      tickProgress();
    }, 1000);
  };
  ///-------------------- END TIMER

  /**************************************/
  /*************** VIDEO ****************/
  /**************************************/

  const isVideoCall = doVideoCall;
  const videoPreview = useRef<HTMLVideoElement>(null);
  const sharedScreenPreview = useRef<HTMLVideoElement>(null);
  const [isSharingScreen, setIsSharingScreen] = useState<boolean>(false);

  const startVideoPreview = () => {
    if (isVideoCall && videoPreview.current) {
      const localPartecipant = getLocalPartecipant();
      if (localPartecipant) {
        const newVideo = localPartecipant.tracks.video.persistentTrack;
        const tracks: MediaStreamTrack[] = [];
        if (newVideo) tracks.push(newVideo);
        const newStream = new MediaStream(tracks);
        videoPreview.current.srcObject = newStream;
        videoPreview.current.play();
      }
    }
  };

  const shareScreen = () => {
    startScreenShare();
  };

  const stopScreenSharing = () => {
    setIsSharingScreen(false);
    if (sharedScreenPreview.current) {
      sharedScreenPreview.current.srcObject = null;
    }
    stopScreenShare();
  };

  const onScreenShared = () => {
    const localPartecipant = getLocalPartecipant();
    if (localPartecipant && sharedScreenPreview.current) {
      const newVideo = localPartecipant.tracks.screenVideo.persistentTrack;
      const tracks: MediaStreamTrack[] = [];
      if (newVideo) tracks.push(newVideo);
      const newStream = new MediaStream(tracks);
      sharedScreenPreview.current.srcObject = newStream;
      sharedScreenPreview.current.play();
      setIsSharingScreen(true);
    }
  };

  /**************************************/
  /********** STATE FUNCTIONS ***********/
  /**************************************/

  const stopProgress = () => {
    if (timeoutProgressRef.current) {
      clearTimeout(timeoutProgressRef.current);
    }
  };

  const _onCallStarts = () => {
    setIsCallInProgress(true);
    startProgress();
    startVideoPreview();
  };

  const _onCallEnds = (
    call: CallDto,
    isGatekeeperConnectingToBuyer: boolean,
  ) => {
    setIsCallInProgress(false);
    stopProgress();
    if (!blockNavigation.current) {
      startEndCallProcess(call, isGatekeeperConnectingToBuyer);
    }
  };

  const onError = (error: Error) => {
    if (error && !blockNavigation.current) {
      blockNavigation.current = true;
      if (error.message == 'EXCEEDED_MAX_DURATION') {
        setErrorMessage('Call exceeded max duration for this bot.');
        const c: CallDto | undefined = getCallInfo();
        startEndCallProcess(c, false);
      }
      if (error.message == 'MIC_OR_VIDEO_PERMISSION_DENIED') {
        setErrorMessage(
          'We cannot hear you. Please check your mic permissions.',
        );
        setShowMediaProblemsBunner(true);
        if (endCall) {
          endCall();
        }
      } else {
        setErrorMessage(error.message);
      }
    }
  };

  const onMediaProblems = (audioProblems: boolean, videoProblems: boolean) => {
    //console.log(audioProblems, videoProblems);
    if (audioProblems) {
      setShowMediaProblemsBunner(true);
    } else {
      setShowMediaProblemsBunner(false);
    }
  };

  const openTroubleshootingGuide = () => {
    setShowTroubleshootingGuide(true);
  };

  /**************************************/
  /************ AUTO START **************/
  /**************************************/

  const {
    startCall,
    endCall,
    isLoading,
    callerStatus,
    botStatus,
    getCallInfo,
    callStatus,
    getLocalPartecipant,
    startScreenShare,
    stopScreenShare,
  } = useCallSimulation(
    agent,
    _onCallStarts,
    _onCallEnds,
    onError,
    undefined,
    onScreenShared,
    onMediaProblems,
  );

  const leaveCall = () => {
    if (endCall) {
      endCall();
    }
    if (!blockNavigation.current) {
      startEndCallProcess(getCallInfo(), false);
    }
  };

  useEffect(() => {
    if (!isLoading) {
      startCall(isVideoCall);
    }
  }, [isLoading]);

  /**************************************/
  /************** END CALL **************/
  /**************************************/

  const [showEndCallProcess, setShowEndCallProcess] = useState<boolean>(false);
  const [endCallProcessMessage, setEndCallProcessMessage] =
    useState<string>('Ending call...');

  const startEndCallProcess = (
    call: CallDto | undefined,
    isGatekeeperConnectingToBuyer: boolean,
  ) => {
    setShowEndCallProcess(true);

    if (isGatekeeper) {
      if (onEnd) {
        onEnd(call || ({} as CallDto), isGatekeeperConnectingToBuyer);
      }
    } else if (!call) {
      setTimeout(() => {
        if (onAbort) {
          onAbort();
        }
      }, 1000);
    } else {
      setTimeout(() => {
        setEndCallProcessMessage('Analyzing recording...');

        setTimeout(() => {
          setEndCallProcessMessage('Analyzing transcript...');

          setTimeout(() => {
            setEndCallProcessMessage('Scoring...');

            setTimeout(() => {
              if (onEnd) {
                onEnd(call, isGatekeeperConnectingToBuyer);
              }
            }, 1300);
          }, 1000);
        }, 1100);
      }, 1200);
    }
  };

  const Icon =
    CALL_TYPE_TO_ICON?.[agent?.callType as keyof typeof CALL_TYPE_TO_ICON]
      ?.Icon;

  /**************************************/
  /************** RENDERING *************/
  /**************************************/

  return (
    <div className="flex flex-col h-full">
      <div
        className={
          'bg-white mx-[10px] flex flex-col flex-1 rounded-3xl overflow-hidden bg-no-repeat bg-cover'
        }
        style={{ backgroundImage: bgImage }}
      >
        <div
          className={cn('flex-1 m-10', {
            'm-10': !narrowWidthDisplay,
            'm-0': narrowWidthDisplay,
          })}
        >
          <div className="flex flex-col h-full">
            <div
              className={cn('flex items-center justify-center h-full', {
                // "flex-col": narrowWidthDisplay,
              })}
            >
              <AnimatePresence>
                {/*********************/}
                {/******** BOT ********/}
                {/*********************/}
                <motion.div
                  initial={{ x: -40 }}
                  animate={{ x: 0 }}
                  transition={{ duration: 0.5, ease: 'easeOut' }}
                  className={cn(
                    'rounded-3xl p-6 bg-white/70 backdrop-blur-3xl shadow-xl border-2 border-white flex flex-col items-center justify-center ' +
                      (botStatus.isTalking ? boxStyles.talking : '') +
                      (botStatus.isThinking ? boxStyles.thinking : ''),
                    {
                      'w-[400px] h-[400px]': !narrowWidthDisplay,
                      'w-[200px] h-[200px]': narrowWidthDisplay,
                    },
                  )}
                >
                  <div
                    className={cn(
                      'rounded-full bg-transparent transition ease-in-out',
                      {
                        'bg-white/40':
                          botStatus.isTalking || botStatus.isThinking,
                        'p-4': !narrowWidthDisplay,
                        'p-3': narrowWidthDisplay,
                      },
                    )}
                    style={{
                      transitionDuration: '1500ms',
                    }}
                  >
                    <AgentAvatar
                      className={cn('border-2 border-white', {
                        'w-[128px] h-[128px]': !narrowWidthDisplay,
                        'w-[86px] h-[86px]': narrowWidthDisplay,
                      })}
                      agent={agent}
                    />
                  </div>
                  <p
                    className={cn(
                      'mt-1 text-center font-medium text-black/80 line-clamp-1',
                      {
                        'text-lg': !narrowWidthDisplay,
                        'text-base pb-2': narrowWidthDisplay,
                      },
                    )}
                  >
                    {agent.firstName} {agent.lastName}
                  </p>
                  {!narrowWidthDisplay && (
                    <p className="text-sm text-muted-foreground">
                      {agent.jobTitle} @ {agent.companyName}
                    </p>
                  )}
                  {!narrowWidthDisplay && !isGatekeeper && (
                    <div className="mt-2">
                      {(agent.emotionalState || agent.gender) && (
                        <div
                          className={cn(
                            'flex mt-2 items-center justify-center',
                            {
                              'space-x-1': agent.emotionalState && agent.gender,
                              // (emotionalState && salesMethodology) ||
                              // (emotionalState && gender) ||
                              // (gender && salesMethodology),
                            },
                          )}
                        >
                          {agent.callType && (
                            <Badge className="mt-1" variant="secondary">
                              {Icon && <Icon className="mr-1 h-3 w-3" />}
                              {CALL_TYPE_OPTIONS.find(
                                (item) => item.value === agent.callType,
                              )?.label ||
                                (agent.callType === 'focus'
                                  ? 'Focus Call'
                                  : agent.callType)}
                            </Badge>
                          )}
                          {agent?.callType !== 'focus' && (
                            <>
                              {agent.emotionalState ? (
                                <Badge className="mt-1" variant={'default'}>
                                  <BrainIcon className="w-3 h-3 mr-1" />{' '}
                                  {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                                    (item) =>
                                      item.value === agent.emotionalState,
                                  )?.label ||
                                    agent.emotionalState ||
                                    ''}
                                </Badge>
                              ) : (
                                <Skeleton className={cn('w-16 h-6 mr-1')} />
                              )}
                            </>
                          )}
                          {agent.bookRate && (
                            <Badge variant="default">
                              Book Rate:{' '}
                              {Number(agent?.bookRate).toLocaleString('en-US', {
                                style: 'percent',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 1,
                              })}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  {/* {isGatekeeper && (
                    <div className="flex items-center rounded-full border px-4 py-2 cursor-pointer hover:bg-muted mt-6">
                      <Avatar className="w-6 h-6 mr-2">
                        <AvatarImage src={`/images/${guardedAgent?.avatar}`} />
                        <AvatarFallback className="text-sm">
                          {guardedAgent?.firstName?.charAt(0) || ""}
                          {guardedAgent?.lastName?.charAt(0) || ""}
                        </AvatarFallback>
                      </Avatar>
                      <div className="capitalize">
                        {guardedAgent?.firstName || ""}{" "}
                        {guardedAgent?.lastName || ""}
                      </div>
                    </div>
                  )} */}
                </motion.div>

                {isLoading || callStatus.isStarting ? (
                  <div
                    className={cn('flex h-[400px] flex-col justify-center', {
                      'h-[200px]': narrowWidthDisplay,
                      'h-[400px]': !narrowWidthDisplay,
                    })}
                  >
                    <HorizontalDotsLoading
                      className={cn({
                        'mx-3': narrowWidthDisplay,
                      })}
                    />
                  </div>
                ) : (
                  <div className="w-10"></div>
                )}

                {/*********************/}
                {/******** USER *******/}
                {/*********************/}

                {isVideoCall ? (
                  <motion.div
                    initial={{ x: 40 }}
                    animate={{ x: 0 }}
                    transition={{ duration: 0.5, ease: 'easeOut' }}
                    className={
                      'rounded-3xl bg-white/70 backdrop-blur-3xl shadow-xl border-2 border-white overflow-hidden relative ' +
                      (callerStatus.isTalking ? boxStyles.talking : '') +
                      (isCallInProgress
                        ? ' w-[600px] h-[400px]'
                        : '  w-[400px] h-[400px]')
                    }
                  >
                    {/* SCREEN SHARING VIDEO */}
                    <video
                      ref={sharedScreenPreview}
                      className={
                        'rounded-3xlm-0 p-0  h-[400px] bg-black ' +
                        (isSharingScreen && isCallInProgress ? '' : 'hidden')
                      }
                    ></video>

                    {/* USER VIDEO */}
                    {/* w-[200px] h-[112px] */}
                    <motion.div
                      initial={{
                        width: '600px',
                        height: '400px',
                      }}
                      animate={{
                        width: isSharingScreen ? '200px' : '600px',
                        height: isSharingScreen ? '112px' : '400px',
                      }}
                      exit={{
                        width: '600px',
                        height: '400px',
                      }}
                      transition={{ duration: 0.5, ease: 'easeOut' }}
                      className={
                        '' +
                        (isSharingScreen &&
                          ' absolute left-[10px] bottom-[10px]') +
                        (isCallInProgress ? '' : ' hidden')
                      }
                    >
                      <video
                        ref={videoPreview}
                        className={
                          'rounded-xl m-0 p-0 bg-black ' +
                          (isSharingScreen ? '' : 'h-[400px]')
                        }
                      ></video>
                    </motion.div>

                    <div
                      className={
                        'h-full flex-col flex items-center justify-center ' +
                        (isCallInProgress ? 'hidden' : '')
                      }
                    >
                      <div
                        className={cn('p-4 rounded-full bg-transparent', {
                          'bg-white/40 transition duration-300 ease-in-out':
                            callerStatus.isTalking || callerStatus.isThinking,
                        })}
                      >
                        <Avatar
                          className={
                            'w-[128px] h-[128px]  border-2 border-white'
                          }
                        >
                          {sessionUser?.avatar && (
                            <AvatarImage src={sessionUser?.avatar} />
                          )}
                          <AvatarFallback className="text-xl capitalize bg-black/20 text-white">
                            {sessionUser.firstName?.charAt(0)}
                            {sessionUser.lastName?.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                      </div>
                      <p className="text-lg mt-1 font-medium text-black/80">
                        {sessionUser.firstName} {sessionUser.lastName}
                      </p>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ x: 40 }}
                    animate={{ x: 0 }}
                    transition={{ duration: 0.5, ease: 'easeOut' }}
                    className={cn(
                      'rounded-3xl p-6 bg-white/70 backdrop-blur-3xl shadow-xl border-2 border-white flex-col flex items-center justify-center ' +
                        (callerStatus.isTalking ? boxStyles.talking : ''),
                      {
                        'w-[400px] h-[400px]': !narrowWidthDisplay,
                        'w-[200px] h-[200px]': narrowWidthDisplay,
                      },
                    )}
                  >
                    <div
                      className={cn(
                        'rounded-full bg-transparent transition ease-in-out',
                        {
                          'bg-white/40':
                            callerStatus.isTalking || callerStatus.isThinking,
                          'p-4': !narrowWidthDisplay,
                          'p-3': narrowWidthDisplay,
                        },
                      )}
                      style={{
                        transitionDuration: '500ms',
                      }}
                    >
                      <Avatar
                        className={cn('border-2 border-white', {
                          'w-[128px] h-[128px]': !narrowWidthDisplay,
                          'w-[86px] h-[86px]': narrowWidthDisplay,
                        })}
                      >
                        {sessionUser?.avatar && (
                          <AvatarImage src={sessionUser?.avatar} />
                        )}
                        <AvatarFallback className="text-xl capitalize bg-black/20 text-white">
                          {validCallerNamePresent
                            ? `${callerFirstName?.charAt(0)}${callerLastName?.charAt(0)}`
                            : `A`}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    <p
                      className={cn(
                        'text-center mt-1 font-medium text-black/80 line-clamp-1',
                        {
                          'text-lg': !narrowWidthDisplay,
                          'text-base': narrowWidthDisplay,
                        },
                      )}
                    >
                      {validCallerNamePresent
                        ? `${callerFirstName} ${callerLastName}`
                        : `You`}
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
        {/******************************************/}
        {/******** MEDIA PROBLEMS BUNNER ***********/}
        {/******************************************/}
        <div className={'w-full ' + (showMediaProblemsBunner ? '' : 'hidden')}>
          <div className="m-4 bg-yellow-500 p-2 rounded-lg flex justify-center">
            <TriangleAlert size={16} className="mr-4" />
            <div className="mr-1">
              Are you speaking right now? We can&apos;t hear you.
            </div>
            <div
              className="underline cursor-pointer"
              onClick={openTroubleshootingGuide}
            >
              Troubleshooting guide.
            </div>
          </div>
        </div>
      </div>
      {/******************************************/}
      {/**************** FOOTER ******************/}
      {/******************************************/}
      <div className="h-[70px] mx-[10px] flex items-center text-white">
        <div className="flex-1"></div>
        {isLoading && (
          <Button
            variant={'default'}
            size={'lg'}
            className="shadow-md text-base drop-shadow-2xl hover:opacity-80 transition-opacity duration-200"
            disabled
          >
            <Loader2Icon size={28} className="animate-spin" />
            Calling {agent.firstName}
          </Button>
        )}
        {!isLoading && !showEndCallProcess && (
          <>
            {isVideoCall && (
              <div>
                {isSharingScreen ? (
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          size={'lg'}
                          variant={'outline'}
                          className="mr-6 text-white hover:text-black h-[52px] rounded-full bg-white/10 p-4 border-0"
                          onClick={stopScreenSharing}
                        >
                          <MonitorStop size={20} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>Stop screen sharing</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          disabled={!isCallInProgress}
                          size={'lg'}
                          variant={'outline'}
                          className="mr-6 text-white hover:text-black h-[52px] rounded-full bg-white/10 p-4 border-0"
                          onClick={shareScreen}
                        >
                          <MonitorPlay size={20} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>Share screen</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            )}

            <div className="mr-6 text-xl font-semibold">{progressLabel}</div>
            <div>
              <Button
                variant={'default'}
                size={'lg'}
                className={cn(
                  'text-base hover:opacity-80 transition-opacity duration-200',
                  {
                    'bg-red-500': !callStatus?.isStarting,
                  },
                )}
                style={
                  callStatus?.isStarting
                    ? {
                        background:
                          agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                            ? 'linear-gradient(to right, #000000, #5189CE, #A168A2)'
                            : 'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                        backgroundImage:
                          agent?.vapiId === CHALLENGE_BOT_VAPI_ID
                            ? '-webkit-linear-gradient(to right, #000000, #5189CE, #A168A2)'
                            : '-webkit-linear-gradient(to bottom, #000000, #5189CE, #A168A2)',
                      }
                    : {}
                }
                onClick={leaveCall}
                disabled={isLoading || callStatus.isStarting}
              >
                {callStatus?.isStarting ? (
                  <PhoneIncomingIcon className="mr-2 animate-pulse" size={20} />
                ) : (
                  <PhoneOff className="mr-2" size={20} />
                )}
                {callStatus?.isStarting
                  ? `Calling ${agent?.firstName}`
                  : 'End Call'}
              </Button>
            </div>
          </>
        )}
        {showEndCallProcess && (
          <>
            <div className="mr-2">
              <Loader2Icon size={24} className="animate-spin" />
            </div>
            <div className="w-[200px] text-sm font-semibold">
              {endCallProcessMessage}
            </div>
          </>
        )}
        <div className="flex-1 text-sm font-semibold text-red-500 mr-4 ml-4">
          {errorMessage}
        </div>
      </div>
      <TroubleshootingGuide
        open={showTroubleshootingGuide}
        onClose={() => setShowTroubleshootingGuide(false)}
      />
    </div>
  );
}
