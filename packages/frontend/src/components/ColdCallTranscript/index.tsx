import { AnyAgentDto } from '@/lib/Agent/types';
import { VapiCallMessage } from '@/lib/Call/types';
import { DemoInboundFormResponseDto } from '@/lib/Demo/types';
import { UserDto } from '@/lib/User/types';
import { cn, formatTime, formatTranscript } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { CallEvents } from '@/system/Analytics/events/CallEvents';
import {
  CopyCheckIcon,
  CopyIcon,
  DownloadIcon,
  Loader2Icon,
  MessageCircleIcon,
  ArrowUp,
  HammerIcon,
} from 'lucide-react';
import { LegacyRef, useEffect, useRef, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Button } from '../ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { AnimatePresence, motion } from 'framer-motion';
import { useAuthInfo } from '@propelauth/react';
import useAudioCall from '@/hooks/useAudioCall';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { ExclamationTriangleIcon } from '@radix-ui/react-icons';
import MicHelpDialog from './MicHelpDialog';
import useUserSession from '@/hooks/useUserSession';
import AgentAvatar from '../Avatars/Agent';

interface IColdCallTranscriptProps {
  transcript: VapiCallMessage[];
  agent: AnyAgentDto;
  loading: boolean;
  callId: string;
  recordingUrl: string;
  caller?: UserDto;
  demoInboundFormResponse?: DemoInboundFormResponseDto;
  callVapiId: string;
  callOrgId?: number;
  videoRecordingUrl?: string;
}

function ColdCallTranscript({
  transcript,
  agent,
  loading,
  caller,
  demoInboundFormResponse,
  callId,
  callVapiId,
  callOrgId,
  recordingUrl,
  videoRecordingUrl,
}: IColdCallTranscriptProps) {
  const [isCopied, setIsCopied] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const latestMessage = useRef<HTMLElement>();
  const messagesContainer = useRef<HTMLElement>();
  const isAudioPlaying = useRef<boolean>(false);

  //************* FETCH AUDIO FILE *********************/

  const authInfo = useAuthInfo();
  const isDemo = !authInfo?.isLoggedIn;
  const [loadingAudio, setLoadingAudio] = useState<boolean>(true);
  const [canLoadAudio, setCanLoadAudio] = useState<boolean>(false);
  const { data, isSuccess } = useAudioCall(callVapiId, canLoadAudio, isDemo);
  const { onlyAdminsCanExportCalls, isAdmin } = useUserSession();
  const audioRecodingBlob = data;

  //loadingAudio is important....we need to minimize the number of times we set the audioRef.src
  if (
    audioRef.current &&
    audioRecodingBlob &&
    isSuccess &&
    loadingAudio &&
    loadingAudio &&
    !videoRecordingUrl
  ) {
    const binaryData = [];
    binaryData.push(audioRecodingBlob);
    audioRef.current.src = window.URL.createObjectURL(new Blob(binaryData));
    audioRef.current.load();
    audioRef.current.addEventListener('canplay', () => {
      setLoadingAudio(false);
    });
  }

  useEffect(() => {
    if (callVapiId) {
      setCanLoadAudio(true);
    }
  }, [callVapiId]);

  useEffect(() => {
    const el = messagesContainer.current;
    if (el) {
      el.addEventListener('scrollend', scrollListener); //if you need to use "scroll", see below and remove .scrollTo() and uncomment "use with scroll event" line
    }

    return () => {
      if (el) {
        el.removeEventListener('scrollend', scrollListener);
      }
    };
  }, []);

  const expectedNextStrollingOffset = useRef<number>(0);
  const isUserScrolling = useRef<boolean>(false);
  const [showResumeAutoScroll, setShowResumeAutoScroll] =
    useState<boolean>(false);

  const isTranscriptionScrolledToBottom = (element: HTMLElement) => {
    const rest = element.scrollHeight - element.scrollTop;
    return Math.abs(element.clientHeight - rest) < 1;
  };

  const scrollListener = (e: Event) => {
    const el = e.target as HTMLElement;
    if (e.target) {
      const s = el.scrollTop;
      if (!isUserScrolling.current) {
        if (
          s != expectedNextStrollingOffset.current &&
          !isTranscriptionScrolledToBottom(el)
        ) {
          if (isAudioPlaying.current) {
            isUserScrolling.current = true;
            setShowResumeAutoScroll(true);
          }
        }
      }
    }
  };

  const resumeAutoScroll = () => {
    setShowResumeAutoScroll(false);
    isUserScrolling.current = false;
    if (
      latestMessage.current &&
      latestMessage.current.parentElement &&
      latestMessage.current.parentElement.parentElement
    ) {
      //latestMessage.current.parentElement.parentElement.scrollTop = expectedNextStrollingOffset.current; //use with scroll event
      latestMessage.current.parentElement.parentElement.scrollTo({
        top: expectedNextStrollingOffset.current,
        behavior: 'smooth',
      });
    }
  };

  const formattedTranscript = formatTranscript(
    transcript || [],
    agent ? `${agent.firstName} ${agent.lastName}` : '',
    callId,
  );

  const callerFirstName =
    caller?.firstName || demoInboundFormResponse?.name?.split(' ')[0] || '';
  const callerLastName =
    caller?.lastName || demoInboundFormResponse?.name?.split(' ')[1] || '';

  const onTimeUpdate = () => {
    let go = false;
    let time: number = 0;
    if (audioRef?.current) {
      time = audioRef?.current?.currentTime;
      go = true;
    } else if (videoRef?.current) {
      time = videoRef?.current?.currentTime;
      go = true;
    }
    if (go) {
      const transcriptTime = transcript[0]?.time;
      const index = transcript.findIndex(
        (message) => message.time - transcriptTime > time * 1000,
      );
      const message = transcript[index - 1];
      if (message) {
        const el = document.getElementById(`${index - 1}`);
        if (el) {
          if (latestMessage.current) {
            latestMessage.current?.classList.remove('shadow-inner');
            latestMessage.current?.classList.remove('shadow-gray-300');
          }

          el.classList.add('shadow-inner');
          el.classList.add('shadow-gray-300');

          if (el.parentElement) {
            const container = el.parentElement;
            if (container.parentElement) {
              const currentScroll =
                container.offsetTop - container.parentElement.offsetTop;
              expectedNextStrollingOffset.current = currentScroll;
              if (!isUserScrolling.current) {
                //container.parentElement.scrollTop = currentScroll; //use with scroll event
                container.parentElement.scrollTo({
                  top: currentScroll,
                  behavior: 'smooth',
                });
              }
            }
            latestMessage.current = el;
          }
        }
      }
    }
  };

  const handlePlayPause = (e: Event) => {
    if (e.type == 'play') {
      isAudioPlaying.current = true;
    } else if (e.type == 'pause') {
      isAudioPlaying.current = false;
    }

    if (audioRef?.current) {
      Analytics.track(CallEvents.RECORDING_CLICKED, {
        played: !audioRef.current.paused,
        currentTime: audioRef.current.currentTime,
        agentId: agent?.id,
        id: callId,
        vapiId: callVapiId,
        orgId: callOrgId,
      });
    }
  };

  // Set up an effect to attach the time update listener
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current?.addEventListener('timeupdate', onTimeUpdate);
      audioRef.current?.addEventListener('play', handlePlayPause);
      audioRef.current?.addEventListener('pause', handlePlayPause);
      return () => {
        audioRef.current?.removeEventListener('timeupdate', onTimeUpdate);
        audioRef.current?.removeEventListener('play', handlePlayPause);
        audioRef.current?.removeEventListener('pause', handlePlayPause);
      };
    }
  }, [audioRef.current]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current?.addEventListener('timeupdate', onTimeUpdate);
      videoRef.current?.addEventListener('play', handlePlayPause);
      videoRef.current?.addEventListener('pause', handlePlayPause);
      return () => {
        videoRef.current?.removeEventListener('timeupdate', onTimeUpdate);
        videoRef.current?.removeEventListener('play', handlePlayPause);
        videoRef.current?.removeEventListener('pause', handlePlayPause);
      };
    }
  }, [videoRef.current]);

  const onCopyClick = async () => {
    Analytics.track(CallEvents.TRANSCRIPT_COPY_CLICKED, {
      agentId: agent?.id,
      id: callId,
      vapiId: callVapiId,
      orgId: callOrgId,
      from: 'individual_call_page',
    });
    try {
      // Use navigator.clipboard.writeText to copy text to clipboard

      await navigator.clipboard.writeText(formattedTranscript);

      // Update state to indicate that the text has been copied
      setIsCopied(true);

      // Reset the copied state after a short delay
      setTimeout(() => {
        setIsCopied(false);
      }, 1500);
    } catch (err) {
      console.error('Unable to copy to clipboard:', err);
    }
  };

  const onDownloadClick = async () => {
    Analytics.track(CallEvents.TRANSCRIPT_DOWNLOADED, {
      agentId: agent?.id,
      id: callId,
      vapiId: callVapiId,
      orgId: callOrgId,
      from: 'individual_call_page',
    });
    const blob = new Blob([formattedTranscript], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-${callId}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  function forwardAudio(startTime: number) {
    if (audioRef?.current) {
      Analytics.track(CallEvents.TRANSCRIPT_MESSAGE_CLICKED, {
        agentId: agent?.id,
        id: callId,
        vapiId: callVapiId,
        orgId: callOrgId,
        oldTime: audioRef.current.currentTime,
        newTime: startTime,
      });
      audioRef.current.currentTime = startTime;
      audioRef?.current?.play();
    } else if (videoRef?.current) {
      Analytics.track(CallEvents.TRANSCRIPT_MESSAGE_CLICKED, {
        agentId: agent?.id,
        id: callId,
        vapiId: callVapiId,
        orgId: callOrgId,
        oldTime: videoRef.current.currentTime,
        newTime: startTime,
      });
      videoRef.current.currentTime = startTime;
      videoRef?.current?.play();
    }
  }

  const renderMessage = (message: VapiCallMessage, i: number) => {
    return (
      <motion.div
        key={i}
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ y: -50 }}
        transition={{
          duration: 0.4,
          delay: i * 0.1,
        }}
      >
        <div
          id={`${i}`}
          onClick={() =>
            forwardAudio((message.time - transcript[0].time) / 1000)
          }
          className={cn(
            'flex items-center space-x-4 mt-1 cursor-pointer hover:shadow-inner hover:shadow-gray-300 rounded-lg py-3 px-2',
            {
              'justify-end': message.role === 'user',
              'justify-start': message.role === 'bot',
            },
          )}
        >
          {message.role === 'bot' &&
            (i === 0 || transcript[i - 1].role !== message.role) && (
              <AgentAvatar className="w-8 h-8" agent={agent} />
            )}
          <div
            className={cn('flex flex-col', {
              'ml-10': i > 0 && transcript[i - 1].role !== message.role,
              'items-end': message.role === 'user',
              'items-start': message.role === 'bot',
            })}
          >
            <span className="text-xs text-muted-foreground">
              {formatTime(message.time - transcript[0].time)}
            </span>
            <div
              className={cn('rounded-xl text-xs py-3 px-4 max-w-[250px] mt-1', {
                'bg-gray-100 rounded-tl-md': message.role === 'bot',
                'bg-blue-500 text-white rounded-br-md': message.role === 'user',
              })}
            >
              {message.message}
            </div>
          </div>
          {message.role === 'user' &&
            (i === 0 || transcript[i - 1].role !== message.role) && (
              <Avatar className="w-8 h-8">
                {caller?.avatar && <AvatarImage src={caller.avatar} />}
                <AvatarFallback className="text-sm text-muted-foreground">
                  {callerFirstName?.charAt(0) || ''}
                  {callerLastName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
            )}
        </div>
      </motion.div>
    );
  };

  const onDownloadAudioClick = () => {
    Analytics.track(CallEvents.RECORDING_DOWNLOADED, {
      agentId: agent?.id,
      id: callId,
      vapiId: callVapiId,
      orgId: callOrgId,
      from: 'individual_call_page',
    });

    if (audioRecodingBlob) {
      const url = URL.createObjectURL(audioRecodingBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audio-${callId}.wav`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const [openMicHelpDialog, setOpenMicHelpDialog] = useState(false);

  return (
    <div className="w-full px-4 py-4">
      <div className="w-full">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-semibold">Recording</h3>
          </div>
          {audioRecodingBlob && (!onlyAdminsCanExportCalls || isAdmin) && (
            <TooltipProvider
              delayDuration={50}
              disableHoverableContent={loading}
            >
              <Tooltip
                onOpenChange={(o) => {
                  if (o) {
                    Analytics.track(CallEvents.RECORDING_DOWNLOAD_HOVERED, {
                      agentId: agent?.id,
                      id: callId,
                      vapiId: callVapiId,
                      orgId: callOrgId,
                    });
                  }
                }}
              >
                <TooltipTrigger asChild>
                  <Button
                    variant={'ghost'}
                    disabled={loading}
                    onClick={onDownloadAudioClick}
                    className="rounded-full p-2 text-muted-foreground"
                  >
                    <DownloadIcon className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Download recording</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        <div className="mt-3">
          {videoRecordingUrl ? (
            <video controls className="rounded-lg w-full" ref={videoRef}>
              <source src={videoRecordingUrl}></source>
            </video>
          ) : (
            <div className="relative">
              {loadingAudio && (
                <div className="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center z-50 rounded-full bg-muted">
                  <Loader2Icon className="animate-spin" />
                </div>
              )}
              <audio
                ref={audioRef}
                controls
                controlsList="nodownload"
                className="w-full"
              >
                <source type="audio/wav" src={recordingUrl} />
              </audio>

              {/* <AudioPlayer audioRef={audioRef} loading={loadingAudio} /> */}
            </div>
          )}
        </div>
      </div>
      <div className="w-full mt-6">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold">Transcript</h3>
            <p className="text-muted-foreground mt-1">
              Click on messages to skip to that part of the audio
            </p>
          </div>
          {transcript?.length > 0 && (
            <div className="flex items-center space-x-0">
              <TooltipProvider
                delayDuration={50}
                disableHoverableContent={loading}
              >
                <Tooltip
                  onOpenChange={(o) => {
                    if (o) {
                      Analytics.track(CallEvents.TRANSCRIPT_DOWNLOAD_HOVERED, {
                        agentId: agent?.id,
                        id: callId,
                        vapiId: callVapiId,
                        orgId: callOrgId,
                      });
                    }
                  }}
                >
                  <TooltipTrigger asChild>
                    <Button
                      variant={'ghost'}
                      disabled={loading}
                      onClick={onDownloadClick}
                      className="rounded-full p-2 text-muted-foreground"
                    >
                      <DownloadIcon className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Download transcript</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider
                delayDuration={50}
                disableHoverableContent={loading}
              >
                <Tooltip
                  onOpenChange={(o) => {
                    if (o) {
                      Analytics.track(CallEvents.TRANSCRIPT_COPY_HOVERED, {
                        agentId: agent?.id,
                        id: callId,
                        vapiId: callVapiId,
                        orgId: callOrgId,
                      });
                    }
                  }}
                >
                  <TooltipTrigger asChild>
                    <Button
                      variant={'ghost'}
                      disabled={loading}
                      onClick={onCopyClick}
                      className="rounded-full p-2 text-muted-foreground"
                    >
                      {isCopied ? (
                        <CopyCheckIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <CopyIcon className="w-4 h-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy to clipboard</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>
        <div
          className="max-h-[480px] overflow-y-auto pb-16 mt-4"
          ref={messagesContainer as LegacyRef<HTMLDivElement> | undefined}
          style={{ paddingRight: '4px' }}
        >
          <AnimatePresence>
            {transcript?.length > 0 ? (
              transcript.map(renderMessage)
            ) : (
              <div className="w-full h-[150px] flex justify-center items-center">
                <div className="flex flex-col space-y-2 text-muted-foreground text-sm items-center">
                  {loading ? (
                    <Loader2Icon className="animate-spin" />
                  ) : (
                    <MessageCircleIcon />
                  )}
                  <p>
                    {loading ? 'Loading transcript...' : 'No transcript found'}
                  </p>
                </div>
              </div>
            )}
          </AnimatePresence>
        </div>
        <div
          className={
            'flex justify-center ' + (showResumeAutoScroll ? '' : 'invisible')
          }
          style={{ position: 'relative', top: '-40px' }}
        >
          <Button onClick={resumeAutoScroll} style={{ fontSize: '12px' }}>
            <ArrowUp style={{ height: '16px' }} />
            &nbsp;&nbsp;Resume Transcript Auto-Scroll
          </Button>
        </div>
      </div>
      <div className="w-full">
        <Alert className="pb-4 pt-3 border-red-200">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle className="font-semibold text-sm">
            Transcription not accurate or bot could not hear you?
          </AlertTitle>
          <AlertDescription className="text-xs">
            Listen to the recording and check your mic to make sure your audio
            quality is good. Your audio might be muffled or the bot could not
            pick up your audio.
            <Button
              onClick={() => {
                setOpenMicHelpDialog(true);
              }}
              className="w-full mt-3"
            >
              <HammerIcon className="mr-2 w-4 h-4 text-white" /> Debug
            </Button>
          </AlertDescription>
        </Alert>
      </div>
      <MicHelpDialog
        open={openMicHelpDialog}
        onOpenChange={setOpenMicHelpDialog}
      />
    </div>
  );
}

export default ColdCallTranscript;
