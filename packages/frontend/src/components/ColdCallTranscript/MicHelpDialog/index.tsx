import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { BugIcon, Mic } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface IProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
export default function MicHelpDialog({ open, onOpenChange }: IProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="close-btn mt-12">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Mic className="mr-2" size={20} />
            Problems with your mic?
          </DialogTitle>
          <DialogDescription>
            Is the bot not responding at all? If so, then your mic audio is not
            being captured.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-6 text-sm">
          <p className="font-semibold">
            Is your audio muffled or unclear or has a lot of background noise?
          </p>
          <ol className="mt-4">
            <li>
              1) Try going to a quieter place or switch to a different mic.
            </li>
          </ol>
          <Separator className="my-4" />
          <p className="font-semibold">
            Are you using an external mic with a docking station?
          </p>
          <ol className="mt-4">
            <li>
              1) Disconnect your external mic/docking station and use earpods or
              your in-built computer mic. We&apos;re aware of this issue and are
              working on it.
            </li>
          </ol>
          <Separator className="my-4" />
          <p className="font-semibold">
            It&apos;s possible your browser is blocking your mic for Hyperbound
          </p>
          <ol className="mt-4">
            <li>
              1) If you&apos;re using Google Chrome, click on the{' '}
              <span className="font-semibold">three dots icon</span> on the top
              right corner --&gt;{' '}
              <span className="font-semibold">Settings</span> --&gt; Search
              <span className="font-semibold">&quot;Microphone&quot;</span> in
              the search bar --&gt; Click on
              <span className="font-semibold">
                &quot;Site settings&quot;
              </span>{' '}
              --&gt; In the{' '}
              <span className="font-semibold">&quot;Permissions&quot;</span>
              section, click{' '}
              <span className="font-semibold">&quot;Microphone&quot;</span> and
              please ensure that{' '}
              <span className="font-semibold">https://app.hyperbound.ai</span>{' '}
              is set to{' '}
              <span className="font-semibold">
                &quot;Allowed to use your microphone&quot;
              </span>
            </li>
            <li className="mt-4">
              2) Then, go back to your{' '}
              <span className="font-semibold">https://app.hyperbound.ai</span>{' '}
              and refresh the screen
            </li>
            <Separator className="my-4" />
            <p className="font-semibold">
              Tried everything and still having issues?
            </p>
            <div className="flex space-x-2 items-center mt-4">
              <p>Report a bug by clicking the</p>
              <BugIcon className="w-4 h-4" />
              <p> icon at the top right</p>
            </div>
          </ol>
        </div>
        <DialogFooter>
          <Button
            variant={'default'}
            onClick={() => {
              onOpenChange(false);
            }}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
