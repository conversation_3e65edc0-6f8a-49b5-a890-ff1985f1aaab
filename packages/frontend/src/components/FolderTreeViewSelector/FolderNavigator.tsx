import { AgentFolderDto } from '@/lib/Agent/types';

import Folder from './Folder';

interface IProps {
  folders: AgentFolderDto[];
  showFolderContent: (f: AgentFolderDto) => void;
  showingContentForFolderId: number | undefined;
  openAddFolderModal: (parentFolder: AgentFolderDto | undefined) => void;
  teamId?: number;
}

export default function FoldersNavigator({
  folders,
  showFolderContent,
  showingContentForFolderId,
  openAddFolderModal,
  teamId,
}: IProps) {
  /***********************************/
  /************ FE INIT **************/
  /***********************************/

  return (
    <div className="overflow-y-auto">
      {folders.map((f, i) => {
        const isLastChildren = folders.length == i + 1;
        if (!f.agent) {
          return (
            <Folder
              key={f.id}
              folder={f}
              onShowContent={showFolderContent}
              showingContentForFolderId={showingContentForFolderId}
              openAddFolderModal={openAddFolderModal}
              teamId={teamId}
              isLastChildren={isLastChildren}
            />
          );
        }
      })}
    </div>
  );
}
