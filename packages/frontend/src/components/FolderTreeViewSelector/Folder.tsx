import { AgentFolderDto } from '@/lib/Agent/types';
import { Check, CheckIcon, ChevronRight, XIcon } from 'lucide-react';
import { useContext, useEffect, useMemo, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { cn } from '@/lib/utils';

import { useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';

interface IProps {
  folder: AgentFolderDto;
  onShowContent: (f: AgentFolderDto) => void;
  onToggleExpand?: (f: AgentFolderDto) => void;
  showingContentForFolderId: number | undefined;
  openAddFolderModal: (parentFolder: AgentFolderDto | undefined) => void;
  isLastChildren: boolean;
  teamId?: number;
}

export default function Folder({
  folder,
  onShowContent,
  onToggleExpand,
  showingContentForFolderId,
  openAddFolderModal,
  teamId,
}: IProps) {
  const { form } = useContext(CreateBuyerBotEditFormContext);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isContentShown, setIsContentShown] = useState<boolean>(
    showingContentForFolderId == folder.id,
  );
  const queryClient = useQueryClient();

  const isSelected = useMemo<boolean>(() => {
    const folders = form.getValues()?.folders || [];
    return folders.some((f: AgentFolderDto) => folder.id === f.id);
  }, [form.getValues(), folder]);

  let hasSubFolders = false;
  if (folder && folder.children) {
    folder.children.map((f) => {
      if (!f.agent) {
        hasSubFolders = true;
      }
    });
  }

  useEffect(() => {
    if (showingContentForFolderId == folder.id) {
      setIsContentShown(true);
    } else {
      openIfContentOpenIsChild(folder.children || []);
      setIsContentShown(false);
    }
  }, [showingContentForFolderId]);

  /***********************************/
  /************ ACTIONS **************/
  /***********************************/

  const openIfContentOpenIsChild = (children: AgentFolderDto[]) => {
    children.forEach((f) => {
      if (f.id == showingContentForFolderId) {
        setIsOpen(true);
      } else {
        openIfContentOpenIsChild(f.children || []);
      }
    });
  };

  const toggleOpenFolder = () => {
    setIsOpen(!isOpen);
    if (onToggleExpand) {
      onToggleExpand(folder);
    }
  };

  const showContent = () => {
    if (hasSubFolders) {
      toggleOpenFolder();
    }
    setIsContentShown(true);
    if (onShowContent) {
      onShowContent(folder);
    }
  };

  const startAddFolder = () => {
    openAddFolderModal(folder);
    showContent();
  };

  /********** EDIT FOLDER INFO **********/

  const [isEditFolderModalOpen, setIsEditFolderModalOpen] = useState(false);

  const startEditFolderInfo = () => {
    setIsEditFolderModalOpen(true);
  };

  const cancelEditFolderInfo = () => {
    setIsEditFolderModalOpen(false);
  };

  const onFolderEdited = (nf: AgentFolderDto) => {
    folder.name = nf.name;
    folder.description = nf.description;
    cancelEditFolderInfo();
  };

  /********** ADD BUYER **********/

  const [isNewBuyerModalOpen, setIsNewBuyerModalOpen] = useState(false);

  const startAddBuyer = () => {
    setIsNewBuyerModalOpen(true);
  };

  const cancelAddBuyer = () => {
    setIsNewBuyerModalOpen(false);
  };

  const addAgents = (agents: AgentFolderDto[]) => {
    if (!folder.children) {
      folder.children = [];
    }
    folder.children.concat(agents);
    queryClient.invalidateQueries({ queryKey: ['agents-folders'] });
    cancelAddBuyer();
    showContent();
  };

  return (
    <div className="text-sm">
      <div
        className={cn(
          'flex-1 flex items-center p-2 cursor-pointer rounded-lg group',
        )}
      >
        <motion.div
          animate={{
            rotate: isOpen ? 90 : 0,
          }}
          transition={{
            ease: 'easeOut',
            duration: 0.2,
          }}
          className={cn('text-muted-foreground mr-2', {
            'opacity-0': !hasSubFolders,
          })}
          onClick={(e) => {
            e.stopPropagation();
            if (hasSubFolders) {
              toggleOpenFolder();
            }
          }}
        >
          <ChevronRight size={16} />
        </motion.div>

        <div
          className="flex items-center flex-1"
          onClick={(e) => {
            e.stopPropagation();
            onShowContent(folder);
          }}
        >
          <div className="text-muted-foreground pr-2">
            <Image
              src={
                isContentShown
                  ? '/images/icons/folderOpen.svg'
                  : '/images/icons/folderClosed.svg'
              }
              alt={isContentShown ? 'Folder Open' : 'Folder Closed'}
              width={16}
              height={16}
            />
          </div>
          <div className="flex-1 mr-3 flex items-center">{folder.name}</div>
        </div>

        <div
          className={cn(
            isSelected
              ? 'w-4 h-4'
              : 'invisible group-hover:visible flex items-center',
          )}
        >
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger>
                <Check className="w-4 h-4" />
              </TooltipTrigger>
              <TooltipContent side="bottom">
                {isSelected ? 'Selected' : 'Select'}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <AnimatePresence>
        {folder.children && isOpen && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ duration: 0.2 }}
            className="ml-6 overflow-hidden"
          >
            {folder.children.map((f, i) => {
              const isLast = (folder?.children?.length || 0) == i + 1;
              if (!f.agent) {
                return (
                  <Folder
                    key={f.id}
                    folder={f}
                    onShowContent={onShowContent}
                    onToggleExpand={onToggleExpand}
                    showingContentForFolderId={showingContentForFolderId}
                    openAddFolderModal={openAddFolderModal}
                    teamId={teamId}
                    isLastChildren={isLast}
                  />
                );
              }
            })}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
