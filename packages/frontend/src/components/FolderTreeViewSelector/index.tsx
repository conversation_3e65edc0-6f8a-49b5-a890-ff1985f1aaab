import { useContext, useEffect, useState } from 'react';
import { AgentFolderDto } from '@/lib/Agent/types';
import { useAgentsFolders } from '@/hooks/useAgentsFolders';
import { ChevronDownIcon, FilterX, PlusIcon } from 'lucide-react';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import FolderNavigator from './FolderNavigator';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { cn } from '@/lib/utils';
import NewFolderModal from '@/common/AIRoleplay/buyerBots/foldersTreeView/newFolderModal';
import Image from 'next/image';
import { Badge } from '../ui/badge';

interface IProps {
  filterByTeamId?: number;
}

export default function FoldersTreeViewSelector({ filterByTeamId }: IProps) {
  const [folders, setFolders] = useState<AgentFolderDto[]>([]);

  const [newFolderModalOpen, setNewFolderModalOpen] = useState(false);
  const { form, existingAgent, folderForContent, setFolderForContent } =
    useContext(CreateBuyerBotEditFormContext);
  const {
    data: dbFolders,
    isLoading: isLoadingFolders,
    refetch: refetchTree,
  } = useAgentsFolders(filterByTeamId);

  const handleSelectFolder = (folder: AgentFolderDto) => {
    // Check if folder is already selected
    const isSelected = folderForContent.some((f) => f.id === folder.id);

    if (isSelected) {
      // If selected, remove it
      handleClearSingleFolder(folder.id);
    } else {
      // If not selected, add it
      const updatedFolders = [...folderForContent, folder];
      setFolderForContent(updatedFolders);
      form.setValue('folders', updatedFolders as AgentFolderDto[]);
    }
  };

  const handleClearSingleFolder = (folderId: number) => {
    const updatedFolders = folderForContent.filter((f) => f.id !== folderId);
    setFolderForContent(updatedFolders);
    form.setValue('folders', updatedFolders);
  };

  const handleClearAllFolders = () => {
    setFolderForContent([]);
    form.setValue('folders', [] as AgentFolderDto[]);
  };

  const startAddNewFolder = () => setNewFolderModalOpen(true);
  const cancelAddFolder = () => setNewFolderModalOpen(false);

  const addNewFolder = (newFolder: AgentFolderDto) => {
    setFolders((prev) => [...prev, newFolder]);
    refetchTree();
    cancelAddFolder();
  };

  useEffect(() => {
    if (
      (!folderForContent || folderForContent.length === 0) &&
      existingAgent?.folders
    ) {
      const initialFolders =
        existingAgent?.folders?.map((f) => ({
          ...f,
          name: f?.parentFolder?.name || f.name,
          id: f?.parentFolderId || f.id,
        })) || [];

      setFolderForContent(initialFolders);
    }
  }, [existingAgent?.folders]);

  useEffect(() => {
    if (!isLoadingFolders && dbFolders) {
      setFolders(dbFolders);
    }
  }, [dbFolders, isLoadingFolders]);

  return (
    <>
      <Form {...form}>
        <FormField
          control={form.control}
          name="folders"
          render={({ field, fieldState }) => (
            <FormItem className="w-full" onBlur={field.onBlur}>
              <FormLabel>Folder</FormLabel>
              <FormControl>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div
                      className={cn(
                        'w-full flex items-center gap-2 p-1 border rounded-md bg-white shadow-sm',
                        fieldState.invalid && 'border-red-500',
                      )}
                    >
                      <div
                        className="flex-1 flex gap-2 flex-wrap"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {folderForContent.length > 0 ? (
                          folderForContent.map((folder) => (
                            <Badge
                              key={folder.id}
                              className="flex items-center gap-1 px-2 "
                              variant="secondary"
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                // handleRemoveFolder(folder.id);
                              }}
                            >
                              <Image
                                src="/images/icons/folderClosed.svg"
                                alt="Folder Closed"
                                width={12}
                                height={12}
                              />
                              {folder.name}
                              {/* TODO: add this button */}
                              {/* <Button
                                variant="ghost"
                                size="icon"
                                className="z-50"
                              >
                                <XIcon
                                  className="w-4 h-4 cursor-pointer z"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    console.log('clicked');
                                    // handleRemoveFolder(folder.id);
                                  }}
                                />
                              </Button> */}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-gray-500">Select Folder</span>
                        )}
                      </div>
                      <ChevronDownIcon className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </DropdownMenuTrigger>

                  <DropdownMenuContent
                    onBlur={field.onBlur}
                    className="min-w-[var(--radix-popper-anchor-width)] p-0 border-0"
                    sideOffset={0}
                    align="start"
                  >
                    {/* Clear All Option (if folders are selected) */}
                    {folderForContent.length > 0 && (
                      <DropdownMenuItem className="px-2 py-1.5 text-sm font-medium text-[#2e3035]">
                        <div
                          className="w-full flex items-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleClearAllFolders();
                          }}
                        >
                          <FilterX className="w-4 h-4 mx-2" />
                          Clear All
                        </div>
                      </DropdownMenuItem>
                    )}

                    <DropdownMenuSeparator />

                    {/* Scrollable Folder List */}
                    <div className="max-h-60 overflow-y-auto">
                      <FolderNavigator
                        folders={folders}
                        showFolderContent={handleSelectFolder}
                        showingContentForFolderId={folderForContent[0]?.id}
                        openAddFolderModal={startAddNewFolder}
                        teamId={filterByTeamId}
                      />
                    </div>

                    <DropdownMenuSeparator />

                    {/* Fixed "Create a New Folder" Button at the Bottom */}
                    <div className="sticky bottom-0 bg-white  border-gray-200">
                      <DropdownMenuItem className="px-2 py-1.5 text-sm font-medium text-[#2e3035]">
                        <div
                          className="w-full flex i`tems-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            startAddNewFolder();
                          }}
                        >
                          <PlusIcon className="w-4 h-4 mx-2" />
                          Create a new folder
                        </div>
                      </DropdownMenuItem>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />
      </Form>

      <NewFolderModal
        isOpen={newFolderModalOpen}
        cancel={cancelAddFolder}
        onNewFolder={addNewFolder}
        numberOfRoots={folders.length}
        teamId={filterByTeamId}
      />
    </>
  );
}
