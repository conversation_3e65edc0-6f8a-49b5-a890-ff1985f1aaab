import OrganizationService from '@/lib/Organization';
import { OrganizationDto } from '@/lib/Organization/types';
import { useEffect, useState } from 'react';
import { Label } from 'recharts';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function HyperboundAgentsManagementPage() {
  const [allOrgs, setAllOrgs] = useState<OrganizationDto[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [selectedOrganization, setSelectedOrganization] =
    useState<OrganizationDto>();

  const Init = async () => {
    const orgs = await OrganizationService.getAllOrganizations();
    setAllOrgs(orgs);
    setIsLoading(false);
  };

  useEffect(() => {
    Init();
  }, []);

  return (
    <div className="m-4">
      <div className="font-semibold text-xl">
        Duplicate Agents and/or Scorecards
      </div>
      <div className="mt-2">
        <div>
          1. select to which organization you want to assign the new
          agent/scorecard
        </div>
        <div>2. find the agent and click Clone</div>
        <div>3. find the scorecard and click Clone</div>
        <div>
          If you want to duplicate an agent, a scorecard and assign the new
          scorecard to the new agent, click on Duplicate both and sync
        </div>
      </div>

      <div className="mt-8 max-w-[300px]">
        <div className="font-semibold">Select Organization</div>
        <div>
          <Select
            value={String(selectedOrganization?.id)}
            onValueChange={(sid: string) => {
              const selectedId = parseInt(sid);
              allOrgs.map((org: any) => {
                if (org.id == selectedId) {
                  setSelectedOrganization(org);
                }
              });
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder={'Select'} />
            </SelectTrigger>
            <SelectContent side="top">
              {allOrgs.map((org: any) => (
                <SelectItem key={'org-' + org.id} value={`${org.id}`}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
