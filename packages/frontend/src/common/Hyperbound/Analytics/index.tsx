import PageHeader from '@/components/PageHeader';
import { But<PERSON> } from '@/components/ui/button';
import DashboardTemplatesService from '@/lib/Analytics/DashboardTemplates';
import DashboardTemplate from '@/lib/Analytics/DashboardTemplates/types';
import {
  CheckCircle2,
  ChevronLeft,
  CopyPlus,
  Edit,
  Edit2,
  Eye,
  Plus,
  Save,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import UpsertTemplate from './UpsertTemplate';
import DashboardTemplateView from './DashboardTemplate';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import CloneToOrg from './CloneToOrg';

export default function Analytics() {
  const [allTemplates, setAlTemplates] = useState<DashboardTemplate[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [pageStatus, setPageStatus] = useState<string>('list');
  const [editingTemplate, setEditingTemplate] = useState<
    DashboardTemplate | undefined
  >(undefined);

  const fetchTemplates = async () => {
    setIsLoading(true);

    const templates = await DashboardTemplatesService.getTemplates();

    setAlTemplates(templates || []);

    setIsLoading(false);
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const show = (p: string) => {
    setPageStatus(p);
  };

  if (pageStatus == 'list' || pageStatus == 'preview') {
    return (
      <div className="bg-[#FBFBFB] h-[100vh] py-4 px-6">
        <div className="flex items-center">
          <PageHeader title="Manage Dashboard Templates" />
          <div className="flex-1"></div>
          <Button
            onClick={() => {
              show('upsert-template');
            }}
          >
            <Plus size={16} className="mr-1" />
            Add new template
          </Button>
        </div>

        <div className="mt-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-[50vh]">
              Loading...
            </div>
          ) : (
            <div>
              {allTemplates.length === 0 && <div>No template found</div>}
              {allTemplates.length > 0 && (
                <Table hideFooter={true} fitToContent={true}>
                  <TableContent>
                    <TableRow className="sticky top-0 z-50">
                      <TableCellHead>Name</TableCellHead>
                      <TableCellHead>For user type</TableCellHead>
                      <TableCellHead>Add to new organization</TableCellHead>
                      <TableCellHead>Show in homepage</TableCellHead>
                      <TableCellHead>
                        Is default rep dashboard (in analytics)
                      </TableCellHead>
                      <TableCellHead>&nbsp;</TableCellHead>
                    </TableRow>
                    {allTemplates.map((template) => {
                      return (
                        <TableRow key={template.id} className="group">
                          <TableCell>{template.title}</TableCell>
                          <TableCell>{template.forUserType}</TableCell>
                          <TableCell>
                            {template.addToNewOrg && (
                              <CheckCircle2
                                size={18}
                                className="text-green-500"
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            {template.showInHome && (
                              <CheckCircle2
                                size={18}
                                className="text-green-500"
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            {template.defaultForReps && (
                              <CheckCircle2
                                size={18}
                                className="text-green-500"
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="invisible group-hover:visible">
                              <Button
                                onClick={() => {
                                  setEditingTemplate(template);
                                  show('cloneToOrg');
                                }}
                                variant={'ghost'}
                                title="Add to organization"
                              >
                                <CopyPlus size={18} />
                              </Button>
                              <Button
                                onClick={() => {
                                  setEditingTemplate(template);
                                  show('upsert-template');
                                }}
                                variant={'ghost'}
                                title="Edit"
                              >
                                <Edit2 size={18} />
                              </Button>
                              <Button
                                onClick={() => {
                                  setEditingTemplate(template);
                                  show('preview');
                                }}
                                variant={'ghost'}
                                title="Preview"
                              >
                                <Eye size={18} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableContent>

                  <TableFooter>&nbsp;</TableFooter>
                </Table>
              )}
              {pageStatus == 'preview' && editingTemplate && (
                <div className="mt-8">
                  <div className="text-base">
                    Preview:{' '}
                    <span className="font-semibold ">
                      {editingTemplate.title}
                    </span>
                  </div>
                  <DashboardTemplateView dashboard={editingTemplate} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  } else if (pageStatus == 'upsert-template') {
    return (
      <UpsertTemplate
        onClose={() => {
          show('list');
          fetchTemplates();
        }}
        template={editingTemplate}
      />
    );
  } else if (pageStatus == 'cloneToOrg' && editingTemplate) {
    return (
      <CloneToOrg
        onClose={() => {
          show('list');
        }}
        template={editingTemplate}
      />
    );
  }
}
