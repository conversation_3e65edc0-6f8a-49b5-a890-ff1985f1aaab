import { useState, useRef, useEffect } from 'react';
import {
  DashboardWidgetDto,
  DashboardWidgetTemplateDto,
} from '@/lib/AnalyticsOld/types';
import { Id, toast } from 'react-toastify';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useQueryClient } from '@tanstack/react-query';
import EditingCommands from '../../../AnalyticsOld/DashboardTab/EditingCommands';
import AddChart from '../../../AnalyticsOld/DashboardTab/AddChart';
import Grid from '../../../AnalyticsOld/DashboardTab/Grid';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import { useRouter } from 'next/navigation';
import DashboardTemplate, {
  DashboardTemplateWidget,
} from '@/lib/AnalyticsOld/DashboardTemplates/types';
import DashboardTemplatesService from '@/lib/AnalyticsOld/DashboardTemplates';

interface IDashboardTabProps {
  dashboard: DashboardTemplate; //dashboard template!
  isEditing?: boolean;
  onDashboardUpdated?: (d: DashboardTemplate) => void;
}

export default function DashboardTemplateView({
  dashboard,
  isEditing,
  onDashboardUpdated,
}: IDashboardTabProps) {
  if (isEditing == undefined) {
    isEditing = false;
  }

  if (isEditing && !onDashboardUpdated) {
    throw new Error('onDashboardUpdated is required when isEditing is true');
  }

  const [name, setName] = useState<string>(dashboard.title);
  const [isCloning, setIsCloning] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [counter, setCounter] = useState<number>(0); //used to force refresh
  const router = useRouter();
  const queryClient = useQueryClient();

  /*********************************/
  /************ ADD WIDGET *********/
  /*********************************/

  const [openPickWidget, setOpenPickWidget] = useState<boolean>(false);

  const startAddChart = () => {
    setOpenPickWidget(true);
  };

  const endAddChart = () => {
    setOpenPickWidget(false);
  };

  const addWidget = async (widgetTemplate: DashboardWidgetTemplateDto) => {
    endAddChart();

    let wid = 0;
    let maxY = 0; //to position new widget at the bottom
    dashboard.widgets.map((widget) => {
      const id = parseInt(widget.props.i.replace('n', ''));
      const y = parseInt(widget.props.y);
      if (id > wid) {
        wid = id;
      }
      if (y > maxY) {
        maxY = y;
      }
    });
    wid++;

    dashboard.widgets.push({
      id: wid,
      isNew: true,
      userId: 0,
      name: widgetTemplate.name,
      description: widgetTemplate.description,
      dashboardTemplateId: dashboard.id,
      props: {
        i: 'n' + wid,
        w: widgetTemplate.width,
        h: widgetTemplate.height,
        x: 0,
        y: maxY,
        minW: widgetTemplate.width,
        minH: widgetTemplate.height,
        resizeHandles: ['se'],
        isBounded: true,
      },
      appliedFilters: {},
      widgetTemplateId: widgetTemplate.id,
      type: widgetTemplate.type,
      customFilters: widgetTemplate.customFilters,
    });
  };

  /*********************************/
  /************ ADD FILTERS ********/
  /*********************************/

  const startAddFilter = () => {};

  /*********************************/
  /************ DELETE *************/
  /*********************************/

  const [openDeleteConfirmation, setOpenDeleteConfirmation] =
    useState<boolean>(false);

  const startDeleteTab = () => {
    setOpenDeleteConfirmation(true);
  };

  const doDeleteDashboardTab = async () => {
    setIsDeleting(true);
    setOpenDeleteConfirmation(false);
    let ok = false;
    try {
      if (dashboard.id) {
        await DashboardTemplatesService.deleteTemplate(dashboard.id);
      }

      ok = true;
    } catch (e) {
      toast.error(
        'Only a dashboard creator or an admin can delete a dashboard. Try again or contact support.',
      );
    }
    setIsDeleting(false);

    if (ok) {
      //dashboard
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      router.push(`/analytics`);
    }
  };

  /*********************************/
  /*** SAVE WIDGET CHANGES ****/
  /*********************************/

  const updateWidgetsUpdated = (
    newWidgets: DashboardTemplateWidget[] | DashboardWidgetDto[],
  ) => {
    dashboard.widgets = newWidgets as DashboardTemplateWidget[];
    if (onDashboardUpdated) {
      onDashboardUpdated(dashboard);
    }
  };

  const deleteWidget = (
    widget: DashboardTemplateWidget | DashboardWidgetDto,
  ) => {
    console.log(dashboard.widgets, widget);
    const tmp = dashboard.widgets.map((w) => {
      if (w.id == widget.id) {
        console.log(w.name);
        w.isDelete = true;
      }
      return w;
    });
    dashboard.widgets = tmp;
    console.log(tmp, '-------');
    if (onDashboardUpdated) {
      onDashboardUpdated(dashboard);
    }
    setCounter((c) => c + 1);
  };

  /*********************************/
  /************** RENDER ***********/
  /*********************************/

  return (
    <div className="mt-6">
      {isEditing && (
        <div className="flex items-center mx-6 mb-6">
          <Label className="mr-2">Name:</Label>
          <div className="mr-2">
            <Input
              value={name}
              onChange={(e) => {
                setName(e.target.value);
                dashboard.title = e.target.value;
                if (onDashboardUpdated) {
                  onDashboardUpdated(dashboard);
                }
              }}
              className="w-56"
              placeholder=""
            />
          </div>
          <div className="flex-1"></div>
          <EditingCommands
            onAddChart={startAddChart}
            onAddFilter={startAddFilter}
            hideCloneBtn={true}
            onDeleteTab={startDeleteTab}
            isCloning={isCloning}
            isDeleting={isDeleting}
          />
        </div>
      )}

      <Grid
        key={'grid-' + dashboard.widgets.length + (isEditing ? '-e' : '')}
        widgets={dashboard.widgets}
        isEditing={isEditing}
        onWidgetsUpdated={updateWidgetsUpdated}
        onWidgetsDelete={deleteWidget}
      />
      <AddChart
        open={openPickWidget}
        onOpenChange={endAddChart}
        onWidgetClick={addWidget}
      />
      <DeleteConfirmationModal
        open={openDeleteConfirmation}
        onCancel={() => {
          setOpenDeleteConfirmation(false);
        }}
        onConfirm={doDeleteDashboardTab}
        title="Delete dashboard"
        description="Are you sure you want to delete this dashboard?"
      />
    </div>
  );
}
