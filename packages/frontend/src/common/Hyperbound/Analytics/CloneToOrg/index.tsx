import PageHeader from '@/components/PageHeader';
import DashboardTemplatesService from '@/lib/Analytics/DashboardTemplates';
import DashboardTemplate from '@/lib/Analytics/DashboardTemplates/types';
import { useEffect, useState } from 'react';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { CheckCircle2, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@radix-ui/react-dropdown-menu';
import { Input } from '@/components/ui/input';
import { DashboardTabDto } from '@/lib/Analytics/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

interface IProps {
  onClose: () => void;
  template: DashboardTemplate;
}

export default function CloneToOrg({ onClose, template }: IProps) {
  const [sharingStats, setSharingStats] = useState<any>();
  const [showSharingPanel, setShowSharingPanel] = useState<boolean>(false);
  const [addToAllOrgs, setAddToAllOrgs] = useState<boolean>(false);
  const [newDashboardInfo, setNewDashboardInfo] = useState<DashboardTabDto>();
  const accessibilityOptions = ['Shared with organization', 'Admins only'];
  const [accessibility, setAccessibility] = useState<string>(
    'Shared with organization',
  );
  const [saving, setIsSaving] = useState<boolean>(false);
  const [selectedOrganization, setSelectedOrganization] = useState<any>({
    id: 0,
    name: '',
  });
  const [errorMsg, setErrorMsg] = useState<string>('');

  const fetchSharingStats = async () => {
    if (template && template.id) {
      const s = await DashboardTemplatesService.getSharingStats(template.id);
      console.log(s);
      setSharingStats(s);
    }
  };
  useEffect(() => {
    if (template) {
      fetchSharingStats();
    }
  }, [template]);

  const startSharing = () => {
    if (template.isForAdminOnly) {
      setAccessibility('Admins only');
    } else {
      setAccessibility('Shared with organization');
    }
    setNewDashboardInfo({
      id: 0,
      title: template.title,
      index: 0,
      userId: 0,
      isPersonal: false,
      isForAdminOnly: template.isForAdminOnly,
      filters: '',
      dashboardTemplateId: template.id,
      showInHome: template.showInHome,
      widgets: [],
      defaultForReps: template.defaultForReps,
      hideWidgetsFilters: false,
    });
    setShowSharingPanel(true);
  };

  const changeAccessiblity = (value: string) => {
    setAccessibility(value);
    if (newDashboardInfo) {
      if (value == 'Admins only') {
        setNewDashboardInfo({ ...newDashboardInfo, isForAdminOnly: true });
      } else {
        setNewDashboardInfo({ ...newDashboardInfo, isForAdminOnly: false });
      }
    }
  };

  const saveNew = async () => {
    setIsSaving(true);

    setErrorMsg('');

    if (!newDashboardInfo || newDashboardInfo.title == '') {
      setErrorMsg('Title cannot be empty');
    } else if (
      (selectedOrganization && selectedOrganization.id > 0) ||
      addToAllOrgs
    ) {
      let go = true;
      try {
        await DashboardTemplatesService.createDashboardTabForOrg(
          selectedOrganization.id,
          template.id,
          newDashboardInfo.title,
          newDashboardInfo.isForAdminOnly,
          newDashboardInfo.showInHome,
          newDashboardInfo.defaultForReps,
          addToAllOrgs,
        );
      } catch (e) {
        console.log(e);
        go = false;
        setErrorMsg('Error creating dashboared, check console (#1)');
      }

      if (go) {
        await fetchSharingStats();
        setShowSharingPanel(false);
      }
    } else {
      setErrorMsg('No oganization selected');
    }

    setIsSaving(false);
  };

  return (
    <div className="bg-[#FBFBFB] h-[100vh] py-4 px-6">
      <div className="flex items-center">
        <PageHeader title={'Share ' + template.title + ' with orgs'} />
        <div className="flex-1"></div>
        <Button
          onClick={() => {
            onClose();
          }}
          className="mr-2"
          variant={'outline'}
          disabled={saving}
        >
          Close
        </Button>
        <Button
          onClick={() => {
            startSharing();
          }}
          disabled={saving}
        >
          <Plus size={16} className="mr-1" />
          Add new organization
        </Button>
      </div>

      {sharingStats && (
        <div className="mt-6">
          <div>
            <div>
              Template: <span className="font-semibold">{template.title}</span>
            </div>
            <div>
              For uer type:{' '}
              <span className="font-semibold">{template.forUserType}</span>
            </div>
            <div>
              Automatically added to new organization:{' '}
              <span className="font-semibold">
                {template.addToNewOrg ? 'YES' : 'NO'}
              </span>
            </div>
            <div>
              Shown in homepage:{' '}
              <span className="font-semibold">
                {template.showInHome ? 'YES' : 'NO'}
              </span>
            </div>
            <div>
              Is dashboard for reps:{' '}
              <span className="font-semibold">
                {template.defaultForReps ? 'YES' : 'NO'}
              </span>
            </div>
          </div>

          {showSharingPanel && (
            <div className="mt-6 border p-4 rounded-lg max-w-[50%]">
              <div className="mb-4">
                <Label className="font-semibold">Organization</Label>
                <div>
                  <Select
                    value={String(selectedOrganization.id)}
                    onValueChange={(sid: string) => {
                      const selectedId = parseInt(sid);
                      sharingStats.allOrgs.map((org: any) => {
                        if (org.id == selectedId) {
                          setSelectedOrganization(org);
                        }
                      });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={'Select'} />
                    </SelectTrigger>
                    <SelectContent side="top">
                      {sharingStats.allOrgs.map((org: any) => (
                        <SelectItem key={'org-' + org.id} value={`${org.id}`}>
                          {org.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="mt-2 flex items-center">
                  <Switch
                    checked={addToAllOrgs}
                    onCheckedChange={(e) => {
                      setAddToAllOrgs(e);
                    }}
                    className="mr-2"
                  />
                  Add to all orgs (skips the orgs that are alredy using this
                  template)
                </div>
              </div>

              <div className="mb-4">
                <Label className="font-semibold">Dashboard Name</Label>
                <div>
                  <Input
                    value={newDashboardInfo?.title}
                    onChange={(e) => {
                      if (newDashboardInfo) {
                        setNewDashboardInfo({
                          ...newDashboardInfo,
                          title: e.target.value,
                        });
                      }
                    }}
                  />
                </div>
              </div>

              <div className="mb-6">
                <Label className="font-semibold">Accessibility</Label>
                <div>
                  <Select
                    value={accessibility}
                    onValueChange={changeAccessiblity}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={'Select'} />
                    </SelectTrigger>
                    <SelectContent side="top">
                      {accessibilityOptions.map((ao) => (
                        <SelectItem key={ao.replace(' ', '-')} value={`${ao}`}>
                          {ao}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="mb-4">
                <Switch
                  checked={newDashboardInfo?.showInHome}
                  onCheckedChange={(e) => {
                    if (newDashboardInfo) {
                      setNewDashboardInfo({
                        ...newDashboardInfo,
                        showInHome: e,
                      });
                    }
                  }}
                  className="mr-2"
                />
                Show on home page
              </div>

              <div className="flex items-center">
                <div className="text-red-500">{errorMsg}</div>
                <div className="text-green-500">
                  {saving && addToAllOrgs && 'This will take a few minutes'}
                </div>
                <div className="flex-1"></div>
                <Button
                  onClick={() => {
                    saveNew();
                  }}
                  disabled={saving}
                >
                  <Plus size={16} className="mr-1" />
                  Save
                </Button>
              </div>
            </div>
          )}

          <div className="mt-4">
            <div className="font-semibold mb-2">Currently used by:</div>
            {sharingStats.sharingStats &&
            sharingStats.sharingStats.length > 0 ? (
              <Table hideFooter={true} fitToContent={true}>
                <TableContent>
                  <TableRow className="sticky top-0 z-50">
                    <TableCellHead>ID</TableCellHead>
                    <TableCellHead>Dashboard Name</TableCellHead>
                    <TableCellHead>Organization</TableCellHead>
                    <TableCellHead>Accessible only to admins</TableCellHead>
                    <TableCellHead>Shown in homepage</TableCellHead>
                  </TableRow>
                  {sharingStats.sharingStats.map((s: any, i: number) => {
                    return (
                      <TableRow key={'db-' + i}>
                        <TableCell>{s.id}</TableCell>
                        <TableCell>{s.title}</TableCell>
                        <TableCell>{s.organizationName}</TableCell>
                        <TableCell>
                          {s.isForAdminOnly && (
                            <CheckCircle2
                              size={18}
                              className="text-green-500"
                            />
                          )}
                        </TableCell>
                        <TableCell>
                          {s.showInHome && (
                            <CheckCircle2
                              size={18}
                              className="text-green-500"
                            />
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableContent>

                <TableFooter>&nbsp;</TableFooter>
              </Table>
            ) : (
              'Not shared yet'
            )}
          </div>
        </div>
      )}
    </div>
  );
}
