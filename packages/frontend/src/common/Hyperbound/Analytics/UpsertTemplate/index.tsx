import PageHeader from '@/components/PageHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import DashboardTemplate, {
  DashboardTemplateUserType,
} from '@/lib/Analytics/DashboardTemplates/types';
import { ChevronLeft, ChevronRight, Loader2Icon, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import DashboardTemplatesService from '@/lib/Analytics/DashboardTemplates';
import DashboardTemplateView from '../DashboardTemplate';

interface IProps {
  onClose: () => void;
  template?: DashboardTemplate;
}

export default function UpsertTemplate({ onClose, template }: IProps) {
  const [pageState, setPageState] = useState<string>('details');
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const [editingTemplate, setEditingTemplate] = useState<DashboardTemplate>({
    id: 0,
    title: '',
    isPersonal: false,
    isForAdminOnly: false,
    filters: '',
    widgets: [],
    forUserType: DashboardTemplateUserType.Reps,
    addToNewOrg: false,
    showInHome: false,
    defaultForReps: false,
  });

  useEffect(() => {
    if (template) {
      setEditingTemplate(template);
    }
  }, [template]);

  const save = async () => {
    setIsSaving(true);
    setErrorMsg('');

    let go = true;

    if (editingTemplate.title == '') {
      go = false;
      setErrorMsg('title is required');
    }
    let savedTemplate: DashboardTemplate | undefined = undefined;
    if (go) {
      try {
        if (editingTemplate.id) {
          savedTemplate = await DashboardTemplatesService.update(
            editingTemplate.id,
            editingTemplate.title,
            '',
            editingTemplate.isPersonal,
            editingTemplate.isForAdminOnly,
            editingTemplate.forUserType,
            editingTemplate.addToNewOrg,
            editingTemplate.showInHome,
            editingTemplate.defaultForReps,
          );
        } else {
          savedTemplate = await DashboardTemplatesService.createNew(
            editingTemplate.title,
            '',
            editingTemplate.isPersonal,
            editingTemplate.isForAdminOnly,
            editingTemplate.forUserType,
            editingTemplate.addToNewOrg,
            editingTemplate.showInHome,
            editingTemplate.defaultForReps,
          );
        }

        if (!savedTemplate.widgets) {
          if (template?.widgets && template?.widgets.length > 0) {
            savedTemplate.widgets = template?.widgets;
          } else {
            savedTemplate.widgets = [];
          }
        }
      } catch (err) {
        go = false;
        console.log(err);
        setErrorMsg('Something went wrong. Please try again');
      }
    }

    if (go && savedTemplate) {
      setEditingTemplate(savedTemplate);
      setPageState('edit-widgets');
    }
    setIsSaving(false);
  };

  const saveWidgets = async () => {
    setIsSaving(true);

    if (editingTemplate) {
      try {
        const tmp = [];
        for (const w of editingTemplate.widgets) {
          if (w.isDelete) {
            await DashboardTemplatesService.deleteWidget(w.id);
          } else if (w.isNew) {
            tmp.push(
              await DashboardTemplatesService.addWidget(
                editingTemplate.id,
                w.widgetTemplateId,
                w.name,
                w.description,
                w.props,
                w.appliedFilters,
              ),
            );
          } else {
            tmp.push(
              await DashboardTemplatesService.updateWidget(
                w.id,
                w.name,
                w.description,
                w.props,
                w.appliedFilters,
              ),
            );
          }
        }
      } catch (e) {
        console.log(e);
        setErrorMsg('Something went wrong. Please try again');
      }
    }
    setIsSaving(false);
    onClose();
  };

  if (pageState == 'details') {
    return (
      <div className="bg-[#FBFBFB] h-[100vh] py-4 px-6">
        <div className="flex items-center">
          <PageHeader title="Edit template" />
          <div className="flex-1"></div>
        </div>

        <div className="mt-6">
          <div className="w-[40%]">
            <Label>Title</Label>
            <Input
              placeholder="Enter title"
              value={editingTemplate.title}
              onChange={(e) => {
                setEditingTemplate({
                  ...editingTemplate,
                  title: e.target.value,
                });
              }}
            />
          </div>
          <div className="w-[40%] mt-6">
            <Label>For user</Label>
            <Select
              onValueChange={(v: string) => {
                setEditingTemplate({
                  ...editingTemplate,
                  forUserType:
                    DashboardTemplateUserType[
                      v as keyof typeof DashboardTemplateUserType
                    ],
                });
              }}
              value={editingTemplate.forUserType}
            >
              <SelectTrigger>
                <div>{editingTemplate.forUserType}</div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={DashboardTemplateUserType.Reps}>
                  Reps
                </SelectItem>
                <SelectItem value={DashboardTemplateUserType.Admins}>
                  Admins
                </SelectItem>
                <SelectItem value={DashboardTemplateUserType.CROs}>
                  CROs
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-[40%] mt-8 flex items-center">
            <Switch
              checked={editingTemplate.addToNewOrg}
              onCheckedChange={(e) => {
                setEditingTemplate({ ...editingTemplate, addToNewOrg: e });
              }}
              className="mr-2"
            />
            <div>
              Create a dashboard from this template for any new organization
            </div>
          </div>
          <div className="w-[40%] mt-8 flex items-center">
            <Switch
              checked={editingTemplate.showInHome}
              onCheckedChange={(e) => {
                setEditingTemplate({ ...editingTemplate, showInHome: e });
              }}
              className="mr-2"
            />
            <div>
              Add as home page dashboard for {editingTemplate.forUserType}
            </div>
          </div>
          <div className="w-[40%] mt-8 flex items-center">
            <Switch
              checked={editingTemplate.defaultForReps}
              onCheckedChange={(e) => {
                setEditingTemplate({ ...editingTemplate, defaultForReps: e });
              }}
              className="mr-2"
            />
            <div>Set as default dashboard for reps in analytics</div>
          </div>
          <div className="flex items-center w-[40%] mt-6">
            {isSaving && (
              <div>
                <Loader2Icon className="animate-spin" size={16} />
              </div>
            )}
            {!isSaving && <div className="text-red-500">{errorMsg}</div>}

            <div className="flex-1"></div>
            <Button
              onClick={() => {
                onClose();
              }}
              variant={'outline'}
              className="mr-2"
              disabled={isSaving}
            >
              <ChevronLeft size={16} className="mr-1" />
              Back
            </Button>
            <Button onClick={save} disabled={isSaving}>
              Next <ChevronRight size={16} className="ml-1" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (pageState == 'edit-widgets') {
    return (
      <div className="bg-[#FBFBFB] h-[100vh] py-4 px-0">
        <div className="flex items-center px-6">
          <PageHeader title="Edit template" />
          <div className="flex-1"></div>
          {isSaving && (
            <div className="mr-2">
              <Loader2Icon className="animate-spin" size={16} />
            </div>
          )}
          {!isSaving && <div className="text-red-500 mr-2">{errorMsg}</div>}

          <Button
            onClick={() => {
              onClose();
            }}
            variant={'outline'}
            className="mr-2"
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button onClick={saveWidgets} disabled={isSaving}>
            Save
          </Button>
        </div>
        <div className="mt-6">
          <DashboardTemplateView
            onDashboardUpdated={(t: DashboardTemplate) => {
              console.log(2, t.widgets);
              setEditingTemplate(JSON.parse(JSON.stringify(t)));
            }}
            dashboard={JSON.parse(JSON.stringify(editingTemplate))}
            isEditing={true}
          />
        </div>
      </div>
    );
  }
}
