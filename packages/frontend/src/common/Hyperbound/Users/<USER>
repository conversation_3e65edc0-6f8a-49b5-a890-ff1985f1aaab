import PageHeader from '@/components/PageHeader';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import InviteUsers from './InviteUsers';

export default function UsersManagementPage() {
  const [show, setShow] = useState<string>('home');

  return (
    <div className="bg-[#FBFBFB] h-[100vh] py-4 px-6">
      <div className="flex items-center">
        <PageHeader title="Manage Hyperbound Users" />
        <div className="flex-1"></div>
        {show != 'invite-users' && (
          <Button
            onClick={() => {
              setShow('invite-users');
            }}
          >
            <Plus size={16} className="mr-1" />
            Invite users
          </Button>
        )}
      </div>

      {show == 'home' && <div></div>}

      {show == 'invite-users' && (
        <InviteUsers
          onClose={() => {
            setShow('home');
          }}
        />
      )}
    </div>
  );
}
