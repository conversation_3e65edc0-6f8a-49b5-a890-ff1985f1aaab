import { Button } from '@/components/ui/button';
import OrganizationService from '@/lib/Organization';
import { OrganizationDto } from '@/lib/Organization/types';
import { Label } from '@radix-ui/react-dropdown-menu';
import { Textarea } from '@tremor/react';
import { ChevronLeft, ChevronRight, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RoleEnum } from '@/lib/User/types';

interface IProps {
  onClose: () => void;
}

export default function InviteUsers({ onClose }: IProps) {
  const [show, setShow] = useState<string>('add-list');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [allOrgs, setAllOrgs] = useState<OrganizationDto[]>([]);
  const [listRaw, setListRaw] = useState<string>('');
  const [allEmails, setAllEmails] = useState<string[]>([]);
  const [error, setError] = useState<string>('');
  const [selectedOrganization, setSelectedOrganization] =
    useState<OrganizationDto>();
  const [role, setRole] = useState<RoleEnum>(RoleEnum.MEMBER);
  const [sending, setSending] = useState<boolean>(false);
  const [invitations, setInvitations] = useState<any>({});

  const Init = async () => {
    const orgs = await OrganizationService.getAllOrganizations();
    setAllOrgs(orgs);
    setIsLoading(false);
  };

  useEffect(() => {
    Init();
  }, []);

  const parseListRaw = () => {
    if (listRaw.indexOf(',') > -1) {
      setAllEmails(listRaw.split(',').map((e) => e.trim()));
    } else {
      setAllEmails(listRaw.split('\n').map((e) => e.trim()));
    }
    setShow('list-preview');
  };

  const removeEmail = (e: string) => {
    const tmp = [];
    for (const ee of allEmails) {
      if (ee != e) {
        tmp.push(ee);
      }
    }
    setAllEmails([...tmp]);
  };

  const send = async () => {
    setError('');
    setSending(true);
    if (
      !selectedOrganization ||
      !selectedOrganization.id ||
      !selectedOrganization.uid
    ) {
      console.log(selectedOrganization);
      setError('select valid organization');
    } else {
      let go = false;
      let sentInvitations: any;
      try {
        sentInvitations = await OrganizationService.inviteMembers(
          selectedOrganization.id,
          allEmails,
          role,
        );
        go = true;
      } catch (e) {
        console.log(e);
        setError('something went wrong - check dev tools for more details');
      }

      setInvitations(sentInvitations);

      if (go) {
        setShow('success');
      }
    }
    setSending(false);
  };

  if (show == 'add-list') {
    return (
      <div className="mt-6 w-[35%] ">
        <div>
          Copy and paste a list of email addresses, one per line or comma
          separated
        </div>
        <Textarea
          value={listRaw}
          onChange={(e) => {
            setListRaw(e.target.value);
          }}
          className="mt-2 h-[45vh]"
        />
        <div className="flex items-center mt-2">
          <div className="text-red-500">{error}</div>
          <div className="flex-1"></div>
          <Button onClick={parseListRaw}>
            Next
            <ChevronRight size={16} className="ml-2" />
          </Button>
        </div>
      </div>
    );
  } else if (show == 'list-preview') {
    return (
      <div className="mt-6 w-[35%] pb-20">
        {allEmails.map((e: string, i: number) => {
          return (
            <div key={e} className="flex items-center mb-2">
              <div className="w-[30px]">{i + 1}</div>
              <div className="flex-1">{e}</div>
              <div>
                <Button
                  onClick={() => {
                    removeEmail(e);
                  }}
                >
                  <Trash2 size={16} />
                </Button>
              </div>
            </div>
          );
        })}
        <div className="flex items-center mt-4 pb-20">
          <Button
            onClick={() => {
              setShow('add-list');
            }}
            variant={'outline'}
          >
            <ChevronLeft size={16} className="mr-2" />
            Back
          </Button>
          <div className="flex-1"></div>
          <Button
            onClick={() => {
              setShow('select-org-and-role');
            }}
          >
            Next
            <ChevronRight size={16} className="ml-2" />
          </Button>
        </div>
      </div>
    );
  } else if (show == 'select-org-and-role') {
    return (
      <div className="mt-6 w-[35%] pb-20">
        <div>
          <Label className="font-semibold">Organization</Label>
          <div>
            <Select
              value={String(selectedOrganization?.id)}
              onValueChange={(sid: string) => {
                const selectedId = parseInt(sid);
                allOrgs.map((org: any) => {
                  if (org.id == selectedId) {
                    setSelectedOrganization(org);
                  }
                });
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={'Select'} />
              </SelectTrigger>
              <SelectContent side="top">
                {allOrgs.map((org: any) => (
                  <SelectItem key={'org-' + org.id} value={`${org.id}`}>
                    {org.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="mt-4">
          <Label className="font-semibold">Role</Label>
          <div>
            <Select
              value={String(role)}
              onValueChange={(sid: string) => {
                if (sid == String(RoleEnum.MEMBER)) {
                  setRole(RoleEnum.MEMBER);
                } else if (sid == String(RoleEnum.ADMIN)) {
                  setRole(RoleEnum.ADMIN);
                } else if (sid == String(RoleEnum.TEMP)) {
                  setRole(RoleEnum.TEMP);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={'Select'} />
              </SelectTrigger>
              <SelectContent side="top">
                <SelectItem value={String(RoleEnum.MEMBER)}>
                  {RoleEnum.MEMBER}
                </SelectItem>
                <SelectItem value={String(RoleEnum.ADMIN)}>
                  {RoleEnum.ADMIN}
                </SelectItem>
                <SelectItem value={String(RoleEnum.TEMP)}>
                  {RoleEnum.TEMP}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex items-center mt-4 pb-20">
          <Button
            onClick={() => {
              setError('');
              setShow('list-preview');
            }}
            variant={'outline'}
          >
            <ChevronLeft size={16} className="mr-2" />
            Back
          </Button>
          <div className="text-red-500">{error}</div>
          <div className="flex-1"></div>
          <Button onClick={send} disabled={sending}>
            Send {allEmails.length} invitation{allEmails.length > 1 && 's'}
          </Button>
        </div>
      </div>
    );
  } else if (show == 'success') {
    return (
      <div className="mt-6 w-[35%] ">
        <div>
          {allEmails.length} invitation{allEmails.length > 1 && 's'} sent.{' '}
        </div>
        <div className="mt-4 flex items-start">
          <div className="border rounded-lg bg-white mr-2 p-4">
            <div className="font-semibold mb-4">Successfully sent</div>
            {invitations && invitations.successfull && (
              <div>
                {invitations.successfull.map((e: string, i: number) => {
                  return (
                    <div key={e} className="mb-2">
                      {e}
                    </div>
                  );
                })}
                <div className="mt-6 font-medium">
                  {invitations.successfull.length} successfull
                </div>
              </div>
            )}
          </div>
          <div className="border rounded-lg bg-white p-4">
            <div className="font-semibold mb-4">Failed</div>
            {invitations && invitations.failed && (
              <div>
                {invitations.failed.map((e: string, i: number) => {
                  return (
                    <div key={e} className="mb-2">
                      {i + 1}&nbsp;{e}
                    </div>
                  );
                })}
                <div className="mt-6 font-medium">
                  {invitations.failed.length} failed
                </div>
              </div>
            )}
          </div>
        </div>
        <Button onClick={onClose} className="mt-4">
          Close
        </Button>
      </div>
    );
  }
}
