import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useUserSession from '@/hooks/useUserSession';
import { motion } from 'framer-motion';
import { ChevronUp, CogIcon, LogOutIcon, UserIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import LinksManager from '@/lib/linksManager';
import posthog from 'posthog-js';
import { useLogoutFunction } from '@propelauth/react';
import { useQueryClient } from '@tanstack/react-query';

interface IProps {
  open: boolean;
  lockSidebarOpen: (o: boolean) => void;
}

export default function UserDropDown({ open, lockSidebarOpen }: IProps) {
  const router = useRouter();

  const { isLoggedIn, user, redirectToAccountPage, dbUser, isCompetitionOrg } =
    useUserSession();
  const logout = useLogoutFunction();
  const queryClient = useQueryClient();

  const onLogout = async () => {
    if (isLoggedIn) {
      posthog.reset();
      logout(true);
      localStorage?.removeItem(
        process.env.NEXT_PUBLIC_ACTIVE_ORG_DATA_LOCAL_STORAGE_KEY as string,
      );
    } else {
      localStorage.removeItem(
        process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY as string,
      );
    }
    await queryClient.invalidateQueries();
  };

  return (
    <DropdownMenu
      onOpenChange={(e) => {
        lockSidebarOpen(e);
      }}
    >
      <DropdownMenuTrigger asChild>
        <div className="flex items-center ml-[14px] mr-[18px] cursor-pointer rounded-lg px-[2px] py-[5px] hover:bg-gray-100">
          <div className="">
            <Avatar className="w-[34px] h-[34px]">
              {user?.pictureUrl && <AvatarImage src={user?.pictureUrl} />}
              <AvatarFallback className="text-xs text-[#2e3035]">
                {user?.firstName?.charAt(0) || ''}
                {user?.lastName?.charAt(0) || ''}
              </AvatarFallback>
            </Avatar>
          </div>

          <motion.div
            animate={{ opacity: open ? '100' : '0' }}
            initial={{ opacity: '0' }}
            transition={{
              duration: 0.02,
              delay: 0.05,
            }}
            className={cn(
              'ml-[14px] text-[14px] h-[34px] flex-1 flex flex-col justify-center pt-[2px]',
              { hidden: !open },
            )}
          >
            <div className="">
              {user?.firstName} {user?.lastName}
            </div>
            <div className="text-[10px] text-muted-foreground">
              {user?.email}
            </div>
          </motion.div>

          <motion.div
            animate={{ opacity: open ? '100' : '0' }}
            initial={{ opacity: '0' }}
            transition={{
              duration: 0.02,
              delay: 0.05,
            }}
            className={cn('mr-2', { hidden: !open })}
          >
            <ChevronUp size={14} />
          </motion.div>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[250px]">
        <DropdownMenuItem
          disabled={!isLoggedIn}
          className={cn('flex items-center space-x-2 cursor-pointer')}
          onClick={() => router.push(LinksManager.members(`${dbUser?.id}`))}
        >
          <UserIcon className="w-4 h-4 text-[#2e3035]" />
          <p>My profile</p>
        </DropdownMenuItem>
        {!isCompetitionOrg && (
          <DropdownMenuItem
            disabled={!isLoggedIn}
            className={cn('flex items-center space-x-2 cursor-pointer')}
            onClick={() => redirectToAccountPage()}
          >
            <CogIcon className="w-4 h-4 text-[#2e3035]" />
            <p>Settings</p>
          </DropdownMenuItem>
        )}

        <DropdownMenuSeparator />

        <DropdownMenuItem
          className={cn(
            'flex items-center space-x-2 cursor-pointer text-red-600',
          )}
          onClick={onLogout}
        >
          <LogOutIcon className="w-4 h-4 " />
          <p>Log out</p>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
