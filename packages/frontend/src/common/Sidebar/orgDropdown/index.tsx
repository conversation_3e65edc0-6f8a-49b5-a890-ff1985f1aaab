/* eslint-disable @typescript-eslint/no-explicit-any */
import { refreshHeaders } from '@/app/auth-wrapper';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import useUserSession from '@/hooks/useUserSession';
import LinksManager from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import { useAuthInfo, useLogoutFunction } from '@propelauth/react';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import posthog from 'posthog-js';
import {
  ArrowLeftRightIcon,
  Blocks,
  ChevronDown,
  CogIcon,
  Lock,
  PlusCircle,
  SearchIcon,
  Server,
  UserPlus2Icon,
  UsersIcon,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useMemo, useState } from 'react';
import CreateNewSubOrganization from '../CreateNewSubOrganization';
import * as Sentry from '@sentry/nextjs';
import { SwitchingModalState, SwitchingOrgsModal } from '../SwitchingOrgsModal';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Input } from '@/components/ui/input';
import { UserStatus } from '@/lib/User/types';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  open: boolean;
  lockSidebarOpen: (o: boolean) => void;
}

type OrgDetails = {
  orgId: string;
  logo: string;
  orgName: string;
  locked: boolean;
};

export default function OrgDropdown({ open, lockSidebarOpen }: IProps) {
  const router = useRouter();
  const queryClient = useQueryClient();

  const {
    org,
    sortedOrgs,
    isLoggedIn,
    isAdmin,
    isMember,
    canCreateSubOrgs,
    isPilotEnded,
    poweredByBranding,
    isCompetitionOrg,
    onlyAdminsCanSeeMembersPage,
    dbOrg,
    canAccess,
    userOrgs,
    loadedAllOrgs,
    canAccessIntegrations,
  } = useUserSession();

  const logout = useLogoutFunction();

  //-------- HACKY CODE FOR LEAGUE OF LEGENDS COMPETITION ORG ------------
  // we need to add a switch between a normal org and the comp org
  // the switch should appear only if the user has only 2 orgs

  let switchToOrg: any = undefined;

  if (sortedOrgs.length == 2) {
    let otherOrg: any = undefined;
    let compOrg: any = undefined;
    sortedOrgs.forEach((org) => {
      if (
        org.orgName.toLowerCase().includes('league') &&
        org.orgName.toLowerCase().includes('sales') &&
        org.orgName.toLowerCase().includes('legends')
      ) {
        compOrg = org;
      } else {
        otherOrg = org;
      }
    });
    if (compOrg) {
      if (compOrg.orgId === dbOrg?.uid) {
        switchToOrg = otherOrg;
      } else {
        switchToOrg = compOrg;
      }
    }
  }
  ////------ END HACKY CODE FOR COMP

  const authInfo = useAuthInfo();
  const [openCreateNewOrg, setOpenCreateNewOrg] = useState(false); //create new org modal state
  const [switchingOrgsState, setSwitchingOrgsState] =
    useState<SwitchingModalState>('closed');
  const [switchedOrg, setSwitchedOrg] = useState<boolean>(false);

  const [search, setSearch] = useState('');

  const onLogout = async () => {
    posthog.reset();
    logout(true);
    localStorage?.removeItem(
      process.env.NEXT_PUBLIC_ACTIVE_ORG_DATA_LOCAL_STORAGE_KEY as string,
    );
    await queryClient.invalidateQueries();
  };

  const switchOrgs = async (
    org: { orgId: string; orgName: string },
    navigate = true,
  ) => {
    try {
      setSwitchingOrgsState('switching');
      await refreshHeaders(authInfo, {
        id: org.orgId,
        name: org.orgName,
        userId: authInfo.user?.userId || '',
      });

      setSwitchingOrgsState('closed');
      if (navigate) {
        router.push('/');
      }
    } catch (e) {
      Sentry.captureException(e, {
        extra: {
          message: 'Error in refreshing headers',
        },
      });
      setSwitchingOrgsState('error');
    }
  };

  const switchToActiveOrg = () => {
    if (loadedAllOrgs && switchingOrgsState !== 'switching' && !switchedOrg) {
      const activeOrg = userOrgs?.find((o) => o.status === UserStatus.ACTIVE);

      if (activeOrg) {
        setSwitchedOrg(true);
        switchOrgs(
          { orgId: activeOrg.orgUid, orgName: activeOrg.orgName },
          false,
        );
      } else {
        onLogout();
      }
    }
  };

  const filteredOrgs = useMemo(() => {
    const res: OrgDetails[] = [];

    const allOrgsInDb: any = {};

    dbOrg?.subOrganizations?.map((so: any) => {
      allOrgsInDb[so.uid] = so.name;
    });

    sortedOrgs?.map((org) => {
      allOrgsInDb[org.orgId] = undefined;
      const userOrg = userOrgs?.find((uo) => uo.orgUid === org.orgId);

      let add = true;
      if (search?.length) {
        if (!org.orgName.toLowerCase().includes(search.toLowerCase())) {
          add = false;
        }
      }

      if (userOrg?.status === UserStatus.DEPROVISIONED && !userOrg?.isAdmin) {
        add = false;
      }

      if (add) {
        res.push({
          orgId: org.orgId,
          logo: org.orgMetadata.logo,
          orgName: org.orgName,
          locked: userOrg?.status === UserStatus.DEPROVISIONED,
        });
      }
    });

    if (!search?.length && isAdmin) {
      //orgs that the admin is not part of but should see
      const tmpOrgs = Object.keys(allOrgsInDb);
      for (const uid of tmpOrgs) {
        const orgName = allOrgsInDb[uid];
        const userOrg = userOrgs?.find((uo) => uo.orgUid === uid);

        if (userOrg?.status === UserStatus.DEPROVISIONED && !userOrg?.isAdmin) {
          continue;
        }

        if (orgName) {
          res.push({
            orgId: uid,
            logo: '',
            orgName: orgName,
            locked: userOrg?.status === UserStatus.DEPROVISIONED,
          });
        }
      }
    }

    const isCurrentOrgDeprovisioned =
      userOrgs?.find((uo) => uo.orgUid === org?.orgId)?.status ===
      UserStatus.DEPROVISIONED;

    if (isCurrentOrgDeprovisioned && loadedAllOrgs) {
      switchToActiveOrg();
    }
    return res;
  }, [sortedOrgs?.length, search, dbOrg, userOrgs, loadedAllOrgs, org]);

  return (
    <>
      {switchToOrg && (
        <div
          className="flex items-center mt-4 mb-0 ml-[14px] mr-[18px] cursor-pointer px-[5px]"
          onClick={() => {
            switchOrgs(switchToOrg);
          }}
        >
          <Image
            src={
              switchToOrg?.orgMetadata?.logo || '/images/square-black-logo.svg'
            }
            alt={`${switchToOrg?.orgName} logo`}
            width={23}
            height={23}
            className="rounded-md flex-shrink-0"
            priority
          />
          <motion.div
            animate={{ opacity: open ? '100' : '0' }}
            initial={{ opacity: '0' }}
            transition={{
              duration: 0.02,
              delay: 0.05,
            }}
            className={cn(
              'flex-1 ml-[14px] flex flex-col leading-none cursor-pointer',
            )}
          >
            <div
              className={cn(
                'max-h-[23px] w-[150px] truncate overflow-hidden whitespace-nowrap cursor-pointer',
              )}
            >
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className={cn(
                        'text-sm text-left pt-[2px] max-h-[23px] w-[150px] truncate overflow-hidden whitespace-nowrap cursor-pointer',
                      )}
                    >
                      Go back to <b>{switchToOrg?.orgName}</b>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    {switchToOrg?.orgName}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </motion.div>
        </div>
      )}
      <DropdownMenu
        onOpenChange={(e) => {
          lockSidebarOpen(e);
        }}
      >
        <DropdownMenuTrigger asChild>
          <div
            className={cn(
              'flex items-center mt-4 mb-4 ml-[14px] mr-[18px] cursor-pointer border rounded-lg px-[5px]',
              {
                'py-[5px]': !poweredByBranding?.text,
              },
            )}
          >
            {!poweredByBranding?.iconLogo && (
              <Image
                src={org?.orgMetadata?.logo || '/images/square-black-logo.svg'}
                alt={`${org?.orgName} logo`}
                width={23}
                height={23}
                className="rounded-md flex-shrink-0"
                priority
              />
            )}
            {!!poweredByBranding?.iconLogo && (
              <img
                className="rounded-md flex-shrink-0"
                src={poweredByBranding.iconLogo.url}
                alt={poweredByBranding.iconLogo.altText || 'Logo'}
                style={{
                  width: poweredByBranding.iconLogo.width,
                  height: poweredByBranding.iconLogo.height,
                }}
              />
            )}
            <motion.div
              animate={{ opacity: open ? '100' : '0' }}
              initial={{ opacity: '0' }}
              transition={{
                duration: 0.02,
                delay: 0.05,
              }}
              className={cn('flex-1 ml-[14px] flex flex-col leading-none', {
                'h-[34px]': !!poweredByBranding?.text,
              })}
            >
              <div
                className={cn(
                  'max-h-[23px] w-[150px] truncate overflow-hidden whitespace-nowrap',
                  {
                    'pt-[0px]': !!poweredByBranding?.text,
                  },
                )}
              >
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger disabled>
                      <div
                        className={cn(
                          'text-sm text-left pt-[2px] max-h-[23px] w-[150px] truncate overflow-hidden whitespace-nowrap',
                          {
                            'pt-[0px]': !!poweredByBranding?.text,
                          },
                        )}
                      >
                        {org?.orgName}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="top">{org?.orgName}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              {!!poweredByBranding?.text && (
                <div className="text-xs text-muted-foreground line-clamp-1 leading-none">
                  powered by {poweredByBranding.text}
                </div>
              )}
            </motion.div>
            <motion.div
              animate={{ opacity: open ? '100' : '0' }}
              initial={{ opacity: '0' }}
              transition={{
                duration: 0.02,
                delay: 0.05,
              }}
              className="self-center"
            >
              <ChevronDown size={14} />
            </motion.div>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[250px]">
          {(!onlyAdminsCanSeeMembersPage || isAdmin) &&
            canAccess(AppPermissions.VIEW_USERS) &&
            !isCompetitionOrg && (
              <DropdownMenuItem
                disabled={!isLoggedIn}
                className={cn('flex items-center space-x-2 cursor-pointer')}
                onClick={() => router.push(LinksManager.members())}
              >
                <UserPlus2Icon className="w-4 h-4 text-[#2e3035]" />
                {!canAccess(AppPermissions.MANAGE_USERS) ? (
                  <p>View members</p>
                ) : (
                  <p>Manage &amp; invite members</p>
                )}
              </DropdownMenuItem>
            )}
          {(canAccess(AppPermissions.MANAGE_TEAMS) || isMember) &&
            !isCompetitionOrg && (
              <DropdownMenuItem
                disabled={!isLoggedIn}
                className={cn('flex items-center space-x-2 cursor-pointer')}
                onClick={() => router.push(LinksManager.teams())}
              >
                <UsersIcon className="w-4 h-4 text-[#2e3035]" />
                {isMember ? <p>View teams</p> : <p>Manage teams</p>}
              </DropdownMenuItem>
            )}
          {!isCompetitionOrg && isAdmin && (
            <DropdownMenuItem
              disabled={!isLoggedIn}
              className={'flex items-center space-x-2 cursor-pointer'}
              onClick={() => router.push(LinksManager.organizationSettings())}
            >
              <CogIcon className="w-4 h-4 text-[#2e3035]" />
              <p>Workspace settings</p>
            </DropdownMenuItem>
          )}
          {!isCompetitionOrg && <DropdownMenuSeparator />}
          {isLoggedIn &&
            canCreateSubOrgs &&
            canAccess(AppPermissions.DEVELOPER_SETTINGS) &&
            !isCompetitionOrg && (
              <DropdownMenuItem
                className={'flex items-center space-x-2 cursor-pointer'}
                onClick={() => router.push(LinksManager.developers())}
              >
                <Server className="w-4 h-4 text-[#2e3035]" />
                <p>Developer settings</p>
              </DropdownMenuItem>
            )}

          {!isCompetitionOrg &&
            canAccess(AppPermissions.INTEGRATIONS) &&
            canAccessIntegrations && (
              <DropdownMenuItem
                disabled={!isLoggedIn}
                className={'flex items-center space-x-2 cursor-pointer'}
                onClick={() => router.push(LinksManager.integrations())}
              >
                <Blocks className="w-4 h-4 text-[#2e3035]" />
                <p>Integrations</p>
              </DropdownMenuItem>
            )}

          {!isCompetitionOrg && isAdmin && <DropdownMenuSeparator />}

          {((sortedOrgs || [])?.length > 1 || canCreateSubOrgs) && (
            <DropdownMenuSub>
              <DropdownMenuSubTrigger
                className={cn('flex items-center space-x-2 cursor-pointer')}
              >
                <ArrowLeftRightIcon className="w-4 h-4 text-[#2e3035]" />
                <p>Switch organization</p>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent className="max-h-[600px] overflow-y-auto">
                  <div>
                    <div className="relative">
                      <div className="absolute left-0 top-0 bottom-0 flex flex-col pl-2.5 justify-center">
                        <SearchIcon className="text-muted-foreground w-4 h-4" />
                      </div>
                      <Input
                        className=" pl-8"
                        placeholder="Search"
                        value={search}
                        onChange={(e) => {
                          setSearch(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          e.stopPropagation();
                        }}
                      />
                    </div>
                  </div>
                  {canCreateSubOrgs && (
                    <>
                      <DropdownMenuItem
                        onClick={() => {
                          setOpenCreateNewOrg(true);
                        }}
                        className="py-2"
                        key={'new-org'}
                        disabled={isPilotEnded}
                      >
                        <div className="flex space-x-2 items-center">
                          <div className="w-6 h-6 flex items-center justify-center">
                            <PlusCircle size={18} />
                          </div>
                          <p className="text-base font-medium">Create new</p>
                        </div>
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />
                    </>
                  )}
                  {filteredOrgs?.map((org, i) => {
                    return (
                      <React.Fragment key={org.orgId}>
                        <DropdownMenuItem
                          onClick={() => {
                            if (!org.locked) {
                              switchOrgs(org);
                            }
                          }}
                          className={cn('py-2', {
                            'text-muted-foreground': org.locked,
                          })}
                          key={org.orgId}
                        >
                          <div className="flex space-x-2 items-center">
                            <Avatar className="w-6 h-6 relative">
                              {org?.logo && <AvatarImage src={org?.logo} />}
                              <AvatarFallback className="text-sm">
                                {org?.orgName?.charAt(0) || ''}
                              </AvatarFallback>
                            </Avatar>
                            <p className="text-base font-medium">
                              {org?.orgName || 'Hyperbound'}
                            </p>
                            {org.locked && (
                              <div className="text-muted-foreground">
                                <Lock size={14} />
                              </div>
                            )}
                          </div>
                        </DropdownMenuItem>
                        {i < filteredOrgs.length - 1 && (
                          <DropdownMenuSeparator />
                        )}
                      </React.Fragment>
                    );
                  })}
                  {filteredOrgs?.length === 0 && (
                    <div className="px-2 py-2 text-center">No org found</div>
                  )}
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <CreateNewSubOrganization
        open={openCreateNewOrg}
        onOpenChange={setOpenCreateNewOrg}
      />
      <SwitchingOrgsModal
        state={switchingOrgsState}
        setState={setSwitchingOrgsState}
      />
    </>
  );
}
