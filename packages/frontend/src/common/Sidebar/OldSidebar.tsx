'use client';

import CountdownTimer from '@/components/CountdownTimer';
import LoginOrSignupModal from '@/components/LoginOrSignupModal';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useMaintenance from '@/hooks/useMaintenance';
import useOrgCurrentUser from '@/hooks/useOrgCurrentUser';
import usePartnerByCachedPartnerId from '@/hooks/usePartnerByCachedPartnerId';
import useUserSession from '@/hooks/useUserSession';
import { AgentCallType } from '@/lib/Agent/types';
import { API } from '@/lib/Client';
import LinksManager from '@/lib/linksManager';
import { PartnerPermission } from '@/lib/Partner/types';
import { cn } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { SidebarEvents } from '@/system/Analytics/events/SidebarEvents';
import {
  OrgMemberInfo,
  useAuthInfo,
  useLogoutFunction,
} from '@propelauth/react';
import { LightningBoltIcon } from '@radix-ui/react-icons';
import { useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import _ from 'lodash';
import {
  ArrowLeftRightIcon,
  BarChart2Icon,
  Blocks,
  Book,
  BookMarked,
  BotIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  CircleHelpIcon,
  CirclePlusIcon,
  ClockIcon,
  CogIcon,
  DoorOpenIcon,
  FlameKindlingIcon,
  Goal,
  Headset,
  HomeIcon,
  LaptopIcon,
  LayoutListIcon,
  ListChecks,
  ListChecksIcon,
  ListTodoIcon,
  ListVideoIcon,
  LockIcon,
  LogOutIcon,
  NotebookPen,
  NotebookTabs,
  Phone,
  PhoneIcon,
  PlusCircle,
  RefreshCwIcon,
  ScanEyeIcon,
  SearchIcon,
  SnowflakeIcon,
  Target,
  TriangleAlertIcon,
  UserCheckIcon,
  UserIcon,
  UserPlus2Icon,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from 'next/navigation';
import posthog from 'posthog-js';
import React, { useEffect, useMemo, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import CreateAssignmentModal from '../CreateAssignment/CreateAssignmentModal';
import NewScorecardModal from '../Scorecards/old/upsertScorecardModal';
import CreateNewSubOrganization from './CreateNewSubOrganization';
import { refreshHeaders } from '@/app/auth-wrapper';

export const CHALLENGE_BOT_VAPI_ID = '123';

export interface ISidebarProps {
  className?: string;
}

export interface ISidebarItem {
  title: string;
  icon: any;
  to?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
  disabled?: boolean;
  subPages?: ISidebarItem[];
  selected: boolean;
  open?: boolean;
  setOpen?: (o: boolean) => void;
  isNew?: boolean;
  hide?: boolean;
}

export const CALL_TYPE_TO_ICON = {
  [AgentCallType.COLD]: {
    Icon: SnowflakeIcon,
  },
  [AgentCallType.GATEKEEPER]: {
    Icon: DoorOpenIcon,
  },
  [AgentCallType.WARM]: {
    Icon: FlameKindlingIcon,
  },
  [AgentCallType.DISCOVERY]: {
    Icon: SearchIcon,
  },
  [AgentCallType.FOCUS]: {
    Icon: ScanEyeIcon,
  },
  [AgentCallType.RENEWAL]: {
    Icon: RefreshCwIcon,
  },
  [AgentCallType.CHECKIN]: {
    Icon: UserCheckIcon,
  },
  [AgentCallType.DEMO]: {
    Icon: LaptopIcon,
  },
  [AgentCallType.CUSTOM]: {
    Icon: CircleHelpIcon,
  },
  [AgentCallType.MANAGER_ONE_ON_ONE]: {
    Icon: Users,
  },
};

export const { Icon: ColdCallIcon } = CALL_TYPE_TO_ICON[AgentCallType.COLD];
export const { Icon: GatekeeperCallIcon } =
  CALL_TYPE_TO_ICON[AgentCallType.GATEKEEPER];
export const { Icon: WarmCallIcon } = CALL_TYPE_TO_ICON[AgentCallType.WARM];
export const { Icon: DiscoveryCallIcon } =
  CALL_TYPE_TO_ICON[AgentCallType.DISCOVERY];
export const { Icon: FocusCallIcon } = CALL_TYPE_TO_ICON[AgentCallType.FOCUS];
export const { Icon: CheckinCallIcon } =
  CALL_TYPE_TO_ICON[AgentCallType.CHECKIN];
export const { Icon: RenewalCallIcon } =
  CALL_TYPE_TO_ICON[AgentCallType.RENEWAL];

export default function OldSidebar({ className }: ISidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const authInfo = useAuthInfo();

  const logout = useLogoutFunction();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const { data: curUser } = useOrgCurrentUser();
  const { data: maintenance } = useMaintenance(!authInfo.isLoggedIn);
  const {
    redirectToOrgPage,
    redirectToAccountPage,
    org,
    dbOrg,
    sortedOrgs,
    isAdmin,
    isTemp,
    isMember,
    canAccessRealCallsScoring,
    canCreateSubOrgs,
  } = useUserSession();
  // const { data: leaderboard, isLoading: isLoadingLeaderboard } =
  //   useCompetitionLeaderboard("rb2b-btb2");
  const [openCreateNewOrg, setOpenCreateNewOrg] = useState(false);
  const [isLoginOrSignupModalOpen, setIsLoginOrSignupModalOpen] =
    useState(false);
  const { data: partner, isLoading } = usePartnerByCachedPartnerId();

  const queryClient = useQueryClient();
  // const { data: demoCalls, isLoading: demoCallsLoading } = useDemoCalls();
  const [assignmentModalOpen, setAssignmentModalOpen] =
    useState<boolean>(false);
  // const { data: topLeaderboard } = useTopLeaderboard(565);

  const [open, setOpen] = useState({
    'Buyer Bots': true,
    Analytics: false,
    'Call History': true,
    Coaching: true,
  });

  const searchParams = useSearchParams();

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  useEffect(() => {
    if (searchParams?.get('invite')) {
      const inviteCode = searchParams?.get('invite');
      queryClient.setQueryData(['hbInvite'], inviteCode);
    }

    if (searchParams?.get('partnerId')) {
      const partnerId = searchParams?.get('partnerId');
      queryClient.setQueryData(['partnerId'], partnerId);
    }
  }, [searchParams?.get('invite'), searchParams.get('partnerId')]);

  const topItems: ISidebarItem[] = useMemo(() => {
    const sidebarItems: ISidebarItem[] = [
      {
        title: authInfo.isLoggedIn ? 'Home' : 'Leaderboard',
        icon: <HomeIcon className="w-4 h-4" />,
        to: '/home',
        selected: pathname?.includes('/home'),
        disabled: !authInfo.isLoggedIn || isTemp,
      },
      {
        title: 'Buyer Bots',
        icon: <BotIcon className="w-4 h-4" />,
        to: `/buyers`,
        selected: false,
        subPages: [
          {
            title: 'Cold Calls',
            icon: <ColdCallIcon className="w-4 h-4" />,
            to: `/buyers?callType=${AgentCallType.COLD}`,
            selected:
              pathname?.includes('/buyers') &&
              searchParams.get('callType') === AgentCallType.COLD,
          },
          {
            title: 'Discovery Calls',
            icon: <DiscoveryCallIcon className="w-4 h-4" />,
            to: `/buyers?callType=${AgentCallType.DISCOVERY}`,
            selected:
              pathname?.includes('/buyers') &&
              searchParams.get('callType') === AgentCallType.DISCOVERY,
          },
          {
            title: 'Warm Calls',
            icon: <WarmCallIcon className="w-4 h-4" />,
            to: `/buyers?callType=${AgentCallType.WARM}`,
            selected:
              pathname?.includes('/buyers') &&
              searchParams.get('callType') === AgentCallType.WARM,
          },
          {
            title: 'Check-In Calls',
            icon: <CheckinCallIcon className="w-4 h-4" />,
            to: `/buyers?callType=${AgentCallType.CHECKIN}`,
            selected:
              pathname?.includes('/buyers') &&
              searchParams.get('callType') === AgentCallType.CHECKIN,
            disabled: !authInfo.isLoggedIn,
          },
          {
            title: 'Renewal Calls',
            icon: <RenewalCallIcon className="w-4 h-4" />,
            to: `/buyers?callType=${AgentCallType.RENEWAL}`,
            selected:
              pathname?.includes('/buyers') &&
              searchParams.get('callType') === AgentCallType.RENEWAL,
            disabled: !authInfo.isLoggedIn,
          },
          {
            title: 'Gatekeeper Calls',
            icon: <GatekeeperCallIcon className="w-4 h-4" />,
            to: `/buyers?callType=${AgentCallType.GATEKEEPER}`,
            selected:
              pathname?.includes('/buyers') &&
              searchParams.get('callType') === AgentCallType.GATEKEEPER,
            disabled: !authInfo.isLoggedIn,
          },
          {
            title: 'Focus Calls',
            icon: <FocusCallIcon className="w-4 h-4" />,
            to: `/buyers?callType=${AgentCallType.FOCUS}`,
            selected:
              pathname?.includes('/buyers') &&
              searchParams.get('callType') === AgentCallType.FOCUS,
            disabled: !authInfo.isLoggedIn,
          },
        ],
        open: open['Buyer Bots'],
        setOpen: (o: boolean) =>
          setOpen((state) => ({ ...state, 'Buyer Bots': o })),
      },
      {
        title: 'Call History',
        icon: <LayoutListIcon className="w-4 h-4" />,
        open: open['Call History'],
        subPages: [
          {
            title: 'Simulated Calls',
            icon: <Phone className="w-4 h-4" />,
            to: LinksManager.trainingCalls(),
            selected: pathname?.includes(LinksManager.trainingCalls()),
            disabled:
              !authInfo.isLoggedIn &&
              partner?.permissions?.includes(
                PartnerPermission.DISABLE_CALL_HISTORY,
              ),
          },
          {
            title: 'Real Calls',
            icon: <Headset className="w-4 h-4" />,
            to: LinksManager.realCalls(),
            disabled:
              !authInfo.isLoggedIn || isTemp || !canAccessRealCallsScoring,
            selected: pathname?.includes(LinksManager.realCalls()),
          },
          {
            title: 'Playlists',
            icon: <ListVideoIcon className="w-4 h-4" />,
            to: '/playlists',
            selected: pathname?.includes('/playlists'),
            disabled: !authInfo.isLoggedIn || isTemp,
          },
        ],
        selected: false,
        setOpen: (o: boolean) =>
          setOpen((state) => ({ ...state, 'Call History': o })),
      },
      {
        title: 'Training Plans',
        icon: <Goal className="w-4 h-4" />,
        to: '/training-plans',
        selected: pathname?.includes('/training-plans'),
        disabled: !authInfo.isLoggedIn || isTemp,
        isNew: true,
      },
      {
        title: authInfo.isLoggedIn ? 'Call Blitz' : 'Simulated Dialer',
        icon: <LightningBoltIcon className="w-4 h-4" />,
        to: '/call-blitz',
        selected: pathname?.includes('/call-blitz'),
        disabled: !authInfo.isLoggedIn || isTemp,
      },
      {
        title: 'Custom Scorecards',
        icon: <Target className="w-4 h-4" />,
        to: LinksManager.scorecards(),
        selected: pathname?.includes(LinksManager.scorecards()),
        disabled: !authInfo.isLoggedIn || isTemp,
        hide: authInfo.isLoggedIn,
      },
      {
        title: 'Analytics',
        icon: <BarChart2Icon className="w-4 h-4" />,
        to: '/analytics',
        disabled: !authInfo.isLoggedIn || isTemp,
        selected: pathname?.includes('/analytics'),
      },
      {
        title: 'Coaching',
        icon: <Book className="w-4 h-4" />,
        to: '/knowledge',
        selected: false,
        disabled: isTemp,
        subPages: [
          {
            title: 'Knowledge Gap',
            icon: <NotebookTabs className="w-4 h-4" />,
            to: `/coaching/knowledge-gap`,
            disabled: isTemp,
            selected:
              pathname?.includes('/coaching/knowledge-gap') &&
              searchParams.get('tab') === 'usage',
          },
          {
            title: 'Assignments',
            icon: <ListTodoIcon className="w-4 h-4" />,
            to: `/coaching/assignments`,
            selected: pathname?.includes('/coaching/assignments'),
            disabled: !authInfo.isLoggedIn || isTemp,
          },
        ],
        open: open['Coaching'],
        setOpen: (o: boolean) =>
          setOpen((state) => ({ ...state, Coaching: o })),
      },
      {
        title: 'Integrations',
        icon: <Blocks className="w-4 h-4" />,
        to: LinksManager.integrations(),
        selected: pathname?.includes(LinksManager.integrations()),
        disabled: isTemp,
        hide: authInfo.isLoggedIn,
      },
    ];

    return sidebarItems;
  }, [
    authInfo?.isLoggedIn,
    redirectToOrgPage,
    pathname,
    searchParams.get('callType'),
    searchParams.get('tab'),
    open,
    partner?.permissions?.length,
    isTemp,
  ]);

  const renderBuyerDropdownItem = (type: string) => {
    if (type == 'Onboarding Plan') {
      return (
        <DropdownMenuItem
          disabled={true}
          className={cn('flex items-center space-x-2 cursor-pointer', {
            'pointer-events-none': false,
          })}
        >
          <NotebookPen className="w-5 h-5" />
          <p>Onboarding Plan</p>
          <LockIcon className="w-4 h-4 text-[#2e3035]" />
        </DropdownMenuItem>
      );
    } else if (type == 'Quiz') {
      return (
        <DropdownMenuItem
          disabled={true}
          className={cn('flex items-center space-x-2 cursor-pointer', {
            'pointer-events-none': false,
          })}
        >
          <ListChecks className="w-5 h-5" />
          <p>Quiz</p>
          <LockIcon className="w-4 h-4 text-[#2e3035]" />
        </DropdownMenuItem>
      );
    } else if (type == 'Content Library') {
      return (
        <DropdownMenuItem
          disabled={true}
          className={cn('flex items-center space-x-2 cursor-pointer', {
            'pointer-events-none': false,
          })}
        >
          <BookMarked className="w-5 h-5" />
          <p>Content Library</p>
          <LockIcon className="w-4 h-4 text-[#2e3035]" />
        </DropdownMenuItem>
      );
    } else if (type == 'Scorecards') {
      return (
        <DropdownMenuItem
          disabled={!authInfo.isLoggedIn || isPilotEnded}
          className={cn('flex items-center space-x-2 cursor-pointer', {
            'pointer-events-none': !authInfo.isLoggedIn,
          })}
          onClick={() => setOpenNewScorecarModal(true)}
        >
          <Target className="w-5 h-5" />
          <p>Scorecard</p>
          {!authInfo?.isLoggedIn && (
            <LockIcon className="w-4 h-4 text-[#2e3035]" />
          )}
        </DropdownMenuItem>
      );
    } else if (type === 'Buyer Bot') {
      return (
        <DropdownMenuItem
          key={'ddmi-' + type}
          disabled={!authInfo.isLoggedIn || isPilotEnded}
          className={cn('flex items-center space-x-2 cursor-pointer', {
            'pointer-events-none': !authInfo.isLoggedIn,
          })}
          onClick={() => router.push(`/buyers/create/init`)}
        >
          <BotIcon className="w-5 h-5" />
          <p>Buyer Bot</p>
          {!authInfo?.isLoggedIn && (
            <LockIcon className="w-4 h-4 text-[#2e3035]" />
          )}
        </DropdownMenuItem>
      );
    } else if (type === 'Assignment') {
      return (
        <DropdownMenuItem
          key={'ddmi-' + type}
          disabled={!authInfo.isLoggedIn || isPilotEnded}
          className={cn('flex items-center space-x-2 cursor-pointer', {
            'pointer-events-none': !authInfo.isLoggedIn,
          })}
          onClick={() => {
            setAssignmentModalOpen(true);
          }}
        >
          <ListChecksIcon className="w-5 h-5" />
          <p>Assignment</p>
          {!authInfo?.isLoggedIn && (
            <LockIcon className="w-4 h-4 text-[#2e3035]" />
          )}
        </DropdownMenuItem>
      );
    } else if (type === 'Call Blitz') {
      return (
        <DropdownMenuItem
          key={'ddmi-' + type}
          disabled={!authInfo.isLoggedIn || isPilotEnded}
          className={cn('flex items-center space-x-2 cursor-pointer', {
            'pointer-events-none': !authInfo.isLoggedIn,
          })}
          onClick={() => router.push(`/call-blitz`)}
        >
          <LightningBoltIcon className="w-5 h-5" />
          <p>Call Blitz</p>
          {!authInfo?.isLoggedIn && (
            <LockIcon className="w-4 h-4 text-[#2e3035]" />
          )}
        </DropdownMenuItem>
      );
    } else {
      const item = topItems
        .find((item) => item.title === 'Buyer Bots')
        ?.subPages?.find((subItem) => subItem.title === type);

      if (item) {
        return (
          <DropdownMenuItem
            key={'ddmi-' + type}
            disabled={!authInfo.isLoggedIn || isPilotEnded}
            className={cn('flex items-center space-x-2 cursor-pointer')}
            onClick={() => router.push(item.to as string)}
          >
            <div className={cn('flex items-center justify-center')}>
              {item.icon}
            </div>
            <p>{item.title}</p>
          </DropdownMenuItem>
        );
      }
    }
  };

  const renderSidebarItem = (item: ISidebarItem, i: number) => {
    if (item.hide) {
      return;
    }
    const link = (
      <Link
        className={cn('flex justify-start items-center space-x-2', {
          'text-foreground': item.selected && !item.disabled,
          'pointer-events-none': item.disabled,
        })}
        href={!item.disabled ? item.to || '' : ''}
        onClick={(e) => {
          Analytics.track(SidebarEvents.TAB_CLICKED, {
            item: item.title,
          });
          if (!item.disabled && item.onClick) {
            item.onClick(e);
          }
        }}
      >
        <div
          className={cn('flex items-center justify-center', {
            'text-accent-foreground': item.selected && !item.disabled,
          })}
        >
          {item.icon}
        </div>
        <p className="text-md font-medium">{item.title}</p>
        {item.isNew && <span className="text-red-500">(New!)</span>}
        {item.disabled && <LockIcon className="w-4 h-4 text-[#878789]" />}
      </Link>
    );

    return (
      <li
        key={item.title}
        className={cn('px-2 py-[7px] mb-[2px] z-40', {
          'bg-[#EDEDED] text-white rounded-lg': item.selected && !item.disabled,
          'text-[#2e3035]': !item.disabled,
          'text-[#878789]': item.disabled,
          'rounded-lg hover:bg-[#EDEDED] hover:duration-200 duration-300 hover:text-foreground':
            !item.selected && !item.disabled,
        })}
      >
        {item.disabled ? (
          <TooltipProvider
            key={item.title}
            disableHoverableContent={!item.disabled}
            delayDuration={50}
          >
            <Tooltip
              onOpenChange={(o) => {
                if (o) {
                  Analytics.track(SidebarEvents.TAB_HOVERED, {
                    item: item.title,
                  });
                }
              }}
            >
              <TooltipTrigger disabled={item.disabled} asChild>
                <span tabIndex={0}>{link}</span>
              </TooltipTrigger>
              <TooltipContent side="right">
                {isTemp ? (
                  <p>This is a temporary demo account</p>
                ) : !authInfo.isLoggedIn ? (
                  <p>Book a demo to access {item.title}</p>
                ) : (
                  <p>Contact Hyperbound team to access {item.title}</p>
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          link
        )}
      </li>
    );
  };

  const renderSidebarDropdownItem = (item: ISidebarItem, i: number) => {
    const links = item.subPages?.map((subItem, i) => {
      const l = (
        <Link
          key={'link-' + subItem.title}
          className={cn('flex justify-start items-center space-x-2', {
            'text-foreground': subItem?.selected && !subItem.disabled,
            'pointer-events-none': subItem.disabled,
          })}
          href={!subItem.disabled ? subItem.to || '' : ''}
          onClick={(e) => {
            Analytics.track(SidebarEvents.TAB_CLICKED, {
              item: subItem.title,
            });
            if (!subItem.disabled && subItem.onClick) {
              subItem.onClick(e);
            }
          }}
        >
          <div
            className={cn('flex items-center justify-center', {
              'text-accent-foreground': subItem?.selected && !subItem.disabled,
            })}
          >
            {subItem.icon}
          </div>
          <p className="text-md font-medium">{subItem.title}</p>
          {subItem.isNew && <span className="text-red-500">(New!)</span>}
          {subItem.disabled && <LockIcon className="w-4 h-4 text-[#878789]" />}
        </Link>
      );

      return (
        <li
          key={'li-' + subItem.title}
          className={cn('px-2 py-[7px] mb-[2px] z-40 ml-6', {
            'bg-[#EDEDED] text-white rounded-lg':
              subItem.selected && !subItem.disabled,
            'text-[#2e3035]': !subItem.disabled,
            'text-[#878789]': subItem.disabled,
            'rounded-lg hover:bg-[#EDEDED] hover:duration-200 duration-300 hover:text-foreground':
              !subItem.selected && !subItem.disabled,
          })}
        >
          {subItem.disabled ? (
            <TooltipProvider
              key={subItem.title}
              disableHoverableContent={!subItem.disabled}
              delayDuration={50}
            >
              <Tooltip
                onOpenChange={(o) => {
                  if (o) {
                    Analytics.track(SidebarEvents.TAB_HOVERED, {
                      item: subItem.title,
                    });
                  }
                }}
              >
                <TooltipTrigger disabled={subItem.disabled} asChild>
                  <span tabIndex={0}>{l}</span>
                </TooltipTrigger>
                <TooltipContent side="right">
                  {subItem.title == 'Real Calls' ? (
                    <p>Contact us for access</p>
                  ) : (
                    <p>Book a demo to access {subItem.title}</p>
                  )}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            l
          )}
        </li>
      );
    });

    const mainTab = (
      <Collapsible open={item.open} onOpenChange={item.setOpen}>
        <div className="w-full">
          <CollapsibleTrigger
            className={cn('flex px-2 py-[7px] mb-[2px] z-40 w-full', {
              'bg-[#EDEDED] text-white rounded-lg':
                item.selected && !item.disabled,
              'text-[#2e3035]': !item.disabled,
              'text-[#878789]': item.disabled,
              'rounded-lg hover:bg-[#EDEDED] hover:duration-200 duration-300 hover:text-foreground':
                !item.selected && !item.disabled,
            })}
            onClick={(e) => {
              Analytics.track(SidebarEvents.TAB_CLICKED, {
                item: item.title,
              });
            }}
          >
            <div
              className={cn(
                'flex justify-start items-center space-x-2 w-full',
                {
                  'text-foreground': item.selected && !item.disabled,
                  'pointer-events-none': item.disabled,
                },
              )}
            >
              <div
                className={cn('flex items-center justify-center', {
                  'text-accent-foreground': item.selected && !item.disabled,
                })}
              >
                {item.icon}
              </div>
              <p className="text-md font-medium">{item.title}</p>
              {item.open ? (
                <ChevronDownIcon className="w-4 h-4" />
              ) : (
                <ChevronRightIcon className="w-4 h-4" />
              )}
              {item.disabled && <LockIcon className="w-4 h-4 text-[#878789]" />}
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <ul>{links}</ul>
          </CollapsibleContent>
        </div>
      </Collapsible>
    );

    return mainTab;
  };

  // const nameCleanedLeaderboardItems = useMemo(() => {
  //   return [...(leaderboard?.items || [])].map((item) => ({
  //     ...item,
  //     nameClean: cleanName(`${item?.firstName || ""} ${item?.lastName || ""}`),
  //   }));
  // }, [leaderboard?.items]);

  // console.log(nameCleanedLeaderboardItems);

  const onLogout = async () => {
    Analytics.track(SidebarEvents.LOGOUT_CLICKED);
    if (authInfo?.isLoggedIn) {
      posthog.reset();
      logout(true);
      localStorage?.removeItem(
        process.env.NEXT_PUBLIC_ACTIVE_ORG_DATA_LOCAL_STORAGE_KEY as string,
      );
    } else {
      localStorage.removeItem(
        process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY as string,
      );
    }
    await queryClient.invalidateQueries();
  };

  const firstName =
    authInfo?.user?.firstName || hbDemoInboundForm?.name?.split(' ')[0] || '';
  const lastName =
    authInfo?.user?.lastName || hbDemoInboundForm?.name?.split(' ')[1] || '';

  const [openNewScorecardModal, setOpenNewScorecarModal] =
    useState<boolean>(false);

  const orgLogoAndNameHeader = (
    <Button
      variant="ghost"
      className="p-2 cursor-pointer"
      {...(authInfo?.isLoggedIn
        ? {}
        : {
            onClick: () => {
              setIsLoginOrSignupModalOpen(true);
            },
          })}
    >
      <div className="flex space-x-2 items-center">
        <Link href="">
          <Image
            src={
              org?.orgMetadata?.logo ||
              partner?.logo ||
              '/images/square-black-logo.svg'
            }
            alt={`Hyperbound logo`}
            width={20}
            height={20}
            className="rounded-md flex-shrink-0"
            priority
          />
        </Link>
        <p className="text-[15px] font-medium">
          {org?.orgName || partner?.name || 'Hyperbound'}
        </p>
        <ChevronDownIcon className="w-3 h-3" />
      </div>
    </Button>
  );
  const switchOrgs = async (org: OrgMemberInfo) => {
    localStorage.removeItem(
      process.env.NEXT_PUBLIC_ACTIVE_ORG_DATA_LOCAL_STORAGE_KEY as string,
    );
    await refreshHeaders(authInfo, {
      id: org.orgId,
      name: org.orgName,
      userId: authInfo.user?.userId || '',
    });

    router.push('/');
  };

  return (
    <div
      className={cn(
        'fixed z-50 w-[254px] md:flex md:flex-col bottom-0 top-0 border-r-0.5 bg-[#FBFBFB] border-line-primary border-r hidden',
        {
          'top-16': !authInfo.isLoggedIn && !maintenance,
          'top-[108px]': !authInfo.isLoggedIn && maintenance,
          'top-[52px]': authInfo.isLoggedIn && maintenance,
          // "w-[254px]": authInfo.isLoggedIn,
          // "w-[354px]": !authInfo.isLoggedIn,
        },
        className,
      )}
    >
      <div
        className={cn('h-full overflow-y-auto')}
        style={{
          scrollbarColor: '#C1C1C1 #FBFBFB',
        }}
      >
        {/* <AnimatePresence> */}
        {authInfo?.isLoggedIn && (
          <div className="w-full flex items-center space-x-2 py-4 px-4">
            <Image
              src={`/images/square-logo-transparent.svg`}
              alt="Hyperbound logo"
              width={24}
              height={24}
              className="rounded-lg"
              priority
            />
          </div>
        )}
        <div
          className={cn('space-x-3 pb-2 px-2', {
            'pt-1': authInfo?.isLoggedIn,
            'pt-4': !authInfo?.isLoggedIn,
          })}
        >
          <div className="flex justify-between items-center">
            <DropdownMenu>
              {authInfo?.isLoggedIn ? (
                <DropdownMenuTrigger asChild>
                  {orgLogoAndNameHeader}
                </DropdownMenuTrigger>
              ) : (
                orgLogoAndNameHeader
              )}
              <DropdownMenuContent align="start" className="w-60">
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    disabled={!authInfo.isLoggedIn}
                    className={cn('flex items-center space-x-2 cursor-pointer')}
                    onClick={() => redirectToOrgPage(org?.orgId)}
                  >
                    <CogIcon className="w-4 h-4 text-[#2e3035]" />
                    <p>Org settings</p>
                  </DropdownMenuItem>
                  {(isAdmin || isMember) && (
                    <DropdownMenuItem
                      disabled={!authInfo.isLoggedIn}
                      className={cn(
                        'flex items-center space-x-2 cursor-pointer',
                      )}
                      onClick={() => router.push(LinksManager.members())}
                    >
                      <UserPlus2Icon className="w-4 h-4 text-[#2e3035]" />
                      {isMember ? (
                        <p>View members</p>
                      ) : (
                        <p>Manage &amp; invite members</p>
                      )}
                    </DropdownMenuItem>
                  )}

                  {((authInfo?.orgHelper?.getOrgIds() || [])?.length > 1 ||
                    canCreateSubOrgs) && (
                    <DropdownMenuSub>
                      <DropdownMenuSubTrigger
                        className={cn(
                          'flex items-center space-x-2 cursor-pointer',
                        )}
                      >
                        <ArrowLeftRightIcon className="w-4 h-4 text-[#2e3035]" />
                        <p>Organizations</p>
                      </DropdownMenuSubTrigger>
                      <DropdownMenuPortal>
                        <DropdownMenuSubContent>
                          {canCreateSubOrgs && (
                            <>
                              <DropdownMenuItem
                                onClick={() => {
                                  setOpenCreateNewOrg(true);
                                }}
                                className="py-2"
                                key={'new-org'}
                                disabled={isPilotEnded}
                              >
                                <div className="flex space-x-2 items-center">
                                  <div className="w-6 h-6 flex items-center justify-center">
                                    <PlusCircle size={18} />
                                  </div>
                                  <p className="text-base font-medium">
                                    Create new
                                  </p>
                                </div>
                              </DropdownMenuItem>

                              <DropdownMenuSeparator />
                            </>
                          )}
                          {sortedOrgs.map((org, i) => (
                            <React.Fragment key={org.orgId}>
                              <DropdownMenuItem
                                onClick={() => {
                                  switchOrgs(org);
                                }}
                                className="py-2"
                                key={org.orgId}
                              >
                                <div className="flex space-x-2 items-center">
                                  <Avatar className="w-6 h-6 relative">
                                    {org?.orgMetadata?.logo && (
                                      <AvatarImage
                                        src={org?.orgMetadata?.logo}
                                      />
                                    )}
                                    <AvatarFallback className="text-sm">
                                      {org?.orgName?.charAt(0) || ''}
                                    </AvatarFallback>
                                  </Avatar>
                                  <p className="text-base font-medium">
                                    {org?.orgName || 'Hyperbound'}
                                  </p>
                                </div>
                              </DropdownMenuItem>
                              {i < sortedOrgs.length - 1 && (
                                <DropdownMenuSeparator />
                              )}
                            </React.Fragment>
                          ))}
                        </DropdownMenuSubContent>
                      </DropdownMenuPortal>
                    </DropdownMenuSub>
                  )}

                  <DropdownMenuItem
                    disabled={!authInfo.isLoggedIn}
                    className={cn('flex items-center space-x-2 cursor-pointer')}
                    onClick={() => router.push(LinksManager.scorecards())}
                  >
                    <Target className="w-4 h-4 text-[#2e3035]" />
                    <p>Custom Scorecards</p>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    disabled={!authInfo.isLoggedIn}
                    className={cn('flex items-center space-x-2 cursor-pointer')}
                    onClick={() => router.push(LinksManager.integrations())}
                  >
                    <Blocks className="w-4 h-4 text-[#2e3035]" />
                    <p>Integrations</p>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className={cn('flex items-center space-x-2 cursor-pointer')}
                    onClick={onLogout}
                  >
                    <LogOutIcon className="w-4 h-4 text-red-500" />
                    <p>Log out</p>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
            {(authInfo?.isLoggedIn || !_.isEmpty(hbDemoInboundForm)) && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="p-2 cursor-pointer">
                    <Avatar className="w-6 h-6">
                      {authInfo?.user?.pictureUrl && (
                        <AvatarImage src={authInfo?.user?.pictureUrl} />
                      )}
                      <AvatarFallback className="text-xs text-[#2e3035]">
                        {firstName?.charAt(0) || ''}
                        {lastName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                  <DropdownMenuGroup>
                    <DropdownMenuLabel className="font-medium">
                      {authInfo?.isLoggedIn
                        ? authInfo?.user?.email
                        : hbDemoInboundForm?.email || ''}
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      disabled={!authInfo.isLoggedIn}
                      className={cn(
                        'flex items-center space-x-2 cursor-pointer',
                      )}
                      onClick={() =>
                        router.push(LinksManager.members(`${curUser?.id}`))
                      }
                    >
                      <UserIcon className="w-4 h-4 text-[#2e3035]" />
                      <p>View profile</p>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      disabled={!authInfo.isLoggedIn}
                      className={cn(
                        'flex items-center space-x-2 cursor-pointer',
                      )}
                      onClick={() => redirectToAccountPage()}
                    >
                      <CogIcon className="w-4 h-4 text-[#2e3035]" />
                      <p>Settings</p>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className={cn(
                        'flex items-center space-x-2 cursor-pointer',
                      )}
                      onClick={onLogout}
                    >
                      <LogOutIcon className="w-4 h-4 text-[#2e3035]" />
                      <p>Log out</p>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
        {!authInfo?.isLoggedIn || isAdmin ? (
          <div className="px-2">
            <DropdownMenu>
              <DropdownMenuTrigger
                onClick={() => {
                  Analytics.track(SidebarEvents.CREATE_CLICKED);
                }}
                asChild
              >
                <Button
                  size={'default'}
                  variant={'outline'}
                  className={'w-full rounded-lg shadow-md shadow-gray-200'}
                >
                  <CirclePlusIcon className="mr-2 w-3 h-3" /> Create new
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-60">
                <DropdownMenuGroup>
                  {!authInfo.isLoggedIn ? (
                    <>
                      <TooltipProvider delayDuration={50}>
                        <Tooltip
                          onOpenChange={(o) => {
                            if (o) {
                              Analytics.track(
                                SidebarEvents.CREATE_TAB_HOVERED,
                                {
                                  item: 'Buyer Bot',
                                },
                              );
                            }
                          }}
                        >
                          <TooltipTrigger asChild>
                            <span tabIndex={0}>
                              {renderBuyerDropdownItem('Buyer Bot')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>Book a demo to build your own buyer bot</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider delayDuration={50}>
                        <Tooltip
                          onOpenChange={(o) => {
                            if (o) {
                              Analytics.track(
                                SidebarEvents.CREATE_TAB_HOVERED,
                                {
                                  item: 'Assignment',
                                },
                              );
                            }
                          }}
                        >
                          <TooltipTrigger asChild>
                            <span tabIndex={0}>
                              {renderBuyerDropdownItem('Assignment')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>Book a demo to create an assignment</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider delayDuration={50}>
                        <Tooltip
                          onOpenChange={(o) => {
                            if (o) {
                              Analytics.track(
                                SidebarEvents.CREATE_TAB_HOVERED,
                                {
                                  item: 'Call Blitz',
                                },
                              );
                            }
                          }}
                        >
                          <TooltipTrigger asChild>
                            <span tabIndex={0}>
                              {renderBuyerDropdownItem('Call Blitz')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>Book a demo to access call blitz</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider delayDuration={50}>
                        <Tooltip
                          onOpenChange={(o) => {
                            if (o) {
                              Analytics.track(
                                SidebarEvents.CREATE_TAB_HOVERED,
                                {
                                  item: 'Scorecards',
                                },
                              );
                            }
                          }}
                        >
                          <TooltipTrigger asChild>
                            <span tabIndex={0}>
                              {renderBuyerDropdownItem('Scorecards')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>Book a demo to access custom scorecards</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider delayDuration={50}>
                        <Tooltip
                          onOpenChange={(o) => {
                            if (o) {
                              Analytics.track(
                                SidebarEvents.CREATE_TAB_HOVERED,
                                {
                                  item: 'Onboarding Plan',
                                },
                              );
                            }
                          }}
                        >
                          <TooltipTrigger asChild>
                            <span tabIndex={0}>
                              {renderBuyerDropdownItem('Onboarding Plan')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>Book a demo to access onboarding plans</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider delayDuration={50}>
                        <Tooltip
                          onOpenChange={(o) => {
                            if (o) {
                              Analytics.track(
                                SidebarEvents.CREATE_TAB_HOVERED,
                                {
                                  item: 'Quiz',
                                },
                              );
                            }
                          }}
                        >
                          <TooltipTrigger asChild>
                            <span tabIndex={0}>
                              {renderBuyerDropdownItem('Quiz')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>Book a demo to access quiz</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider delayDuration={50}>
                        <Tooltip
                          onOpenChange={(o) => {
                            if (o) {
                              Analytics.track(
                                SidebarEvents.CREATE_TAB_HOVERED,
                                {
                                  item: 'Content Library',
                                },
                              );
                            }
                          }}
                        >
                          <TooltipTrigger asChild>
                            <span tabIndex={0}>
                              {renderBuyerDropdownItem('Content Library')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>Book a demo to access content library</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </>
                  ) : (
                    <>
                      {renderBuyerDropdownItem('Buyer Bot')}
                      {renderBuyerDropdownItem('Assignment')}
                      {renderBuyerDropdownItem('Call Blitz')}
                      {renderBuyerDropdownItem('Scorecards')}
                      {renderBuyerDropdownItem('Onboarding Plan')}
                      {renderBuyerDropdownItem('Quiz')}
                      {renderBuyerDropdownItem('Content Library')}
                    </>
                  )}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
            <NewScorecardModal
              open={openNewScorecardModal}
              onClose={() => {
                setOpenNewScorecarModal(false);
              }}
            />
          </div>
        ) : (
          <div className="px-2">
            <DropdownMenu>
              <DropdownMenuTrigger
                onClick={() => {
                  Analytics.track(SidebarEvents.CREATE_CLICKED);
                }}
                asChild
              >
                <Button
                  size={'default'}
                  variant={'outline'}
                  className={'w-full rounded-lg shadow-md shadow-gray-200'}
                >
                  <PhoneIcon className="mr-2 w-4 h-4" /> Practice a call
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-60">
                <DropdownMenuGroup>
                  {topItems
                    .find((item) => item.title === 'Buyer Bots')
                    ?.subPages?.map((subItem, i) =>
                      renderBuyerDropdownItem(subItem.title),
                    )}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
        <div className="flex flex-col justify-between pb-4 mt-4">
          <ul className="flex-none px-2">
            {topItems.map((item, i) => (
              <React.Fragment key={i}>
                {item?.subPages?.length
                  ? renderSidebarDropdownItem(item, i)
                  : renderSidebarItem(item, i)}
              </React.Fragment>
            ))}
            {!authInfo?.isLoggedIn && (
              <>
                <hr className="mt-3" />
                <li className="px-3 mt-3">
                  <a
                    href="https://hyperbound.ai/dsar-form"
                    className="text-sm leading-6 text-gray-600 hover:text-gray-900"
                  >
                    Data Subject Access Request Form
                  </a>
                </li>
                <li className="px-3 mt-3">
                  <a
                    href="https://hyperbound.ai/dsar-form"
                    className="text-sm leading-6 text-gray-600 hover:text-gray-900"
                  >
                    Do not sell or share my personal information
                  </a>
                </li>
                <li className="px-3 mt-3">
                  <a
                    href="https://hyperbound.ai/dsar-form"
                    className="text-sm leading-6 text-gray-600 hover:text-gray-900"
                  >
                    Limit the use of my sensitive personal information
                  </a>
                </li>
              </>
            )}
          </ul>

          {/* {!authInfo?.isLoggedIn && (
            <AnimatePresence>
              <motion.div
                whileHover={{
                  translateY: -2,
                  transition: { duration: 0.3 },
                }}
              >
                <div
                  className={cn(
                    "px-2 py-4 mx-2 border-2 border-white/50 w-[238px] mt-4 flex flex-col text-base rounded-2xl text-white hover:text-white shadow-2xl drop-shadow-2xl text-wrap duration-200"
                  )}
                  style={{
                    background: "linear-gradient(to bottom, #1D2671, #C33764)",
                    backgroundImage:
                      "-webkit-linear-gradient(to bottom, #000000, #5189CE, #A168A2)",
                  }}
                >
                  <div className="px-2">
                    <div className="flex items-center justify-between">
                      <p className="font-semibold text-sm">
                        Beat the Bot Challenge
                      </p>
                      <TrophyIcon className="w-6 h-6 opacity-50" />
                    </div>
                    <Link href="/competitions/rb2b-btb2/leaderboard">
                      <Button
                        variant={"link"}
                        className="py-0 px-0 hover:no-underline"
                        // onClick={onLeaderboardClicked}
                      >
                        <div className="flex text-white/80">
                          <p className="text-sm">See full public leaderboard</p>
                          <MoveRightIcon className="ml-2" />
                        </div>
                      </Button>
                    </Link>
                  </div>

                  <div className="mt-2">
                    {nameCleanedLeaderboardItems.slice(0, 10).map(
                      (leaderboardItem: any, i: number) => {
                        const aggScorePerc =
                          (leaderboardItem.highestScoreCallDetails
                            ?.aggregatedScorecardDto?.aggregateScore || 0) *
                          100;
                        return (
                          <Button
                            key={leaderboardItem.id}
                            variant={"ghost"}
                            className="flex w-full text-left items-center py-2 px-4 rounded-lg hover:bg-white/10 hover:text-white"
                          >
                            <div className="flex w-9">
                              <p
                                className={cn(
                                  "text-xl mr-4 font-bold text-green-100"
                                )}
                              >
                                {leaderboardItem?.rank}
                              </p>
                            </div>
                            <div className="flex justify-center items-center w-6 h-6 bg-white rounded-sm">
                              <Link
                                href={
                                  leaderboardItem.linkedInUrl ||
                                  "https://www.linkedin.com/company/hyperbound-ai/"
                                }
                                target="_blank"
                              >
                                <Image
                                  width={10}
                                  height={10}
                                  src={"/images/linkedin-icon.svg"}
                                  alt=""
                                />
                              </Link>
                            </div>
                            <div className="w-44 px-4 text-center truncate space-y-1">
                              <p className="text-sm font-medium leading-none">
                                {leaderboardItem?.firstName || ""}{" "}
                                {leaderboardItem?.lastName || ""}
                              </p>
                            </div>
                            <div className="ml-auto font-semibold text-xl">
                              {aggScorePerc.toLocaleString("en-US", {
                                minimumFractionDigits: 1,
                                maximumFractionDigits: 1,
                              })}
                            </div>
                          </Button>
                        );
                      }
                    )}
                  </div>
                  <Link
                    href="https://app.hyperbound.ai/competitions/rb2b-btb2/leaderboard"
                    target="_blank"
                  >
                    <Button
                      className="bg-white rounded-xl text-black hover:bg-white/90 mt-2 w-full"
                      size={"lg"}
                    >
                      Check Leaderboard
                    </Button>
                  </Link>
                </div>
              </motion.div>
            </AnimatePresence>
          )} */}

          {dbOrg?.pilotDetails?.expiryDate && (
            <div>
              <hr className="mx-2" />
              <div
                className={cn(
                  'px-2 mx-2 w-[93%] mt-4 flex flex-col text-base text-primary text-wrap duration-200',
                )}
                // style={{
                //   backgroundImage:
                //     "linear-gradient(to right, #2FB6E1 0%, #32C490 100%)",
                // }}
              >
                <div className="px-2">
                  <div className="flex items-center justify-between">
                    <p className="font-semibold text-sm">Pilot Period</p>
                    {isPilotEnded ? (
                      <TriangleAlertIcon className="w-4 h-4 opacity-50" />
                    ) : (
                      <ClockIcon className="w-4 h-4 opacity-50" />
                    )}
                  </div>
                  <div className="mt-2">
                    <span
                      className={cn({
                        'text-red-500 font-semibold': isPilotEnded,
                      })}
                    >
                      <CountdownTimer
                        targetDate={
                          dbOrg?.pilotDetails?.expiryDate ?? new Date()
                        }
                        abbreviatedSuffix
                        expiredMessage="Ended"
                      />
                    </span>
                    {!isPilotEnded && (
                      <p className="text-muted-foreground text-sm">Remaining</p>
                    )}
                  </div>
                  {dbOrg?.pilotDetails?.surveyLink ? (
                    <Link
                      href={dbOrg?.pilotDetails?.surveyLink}
                      target="_blank"
                    >
                      <Button className="mt-2 w-full" size={'default'}>
                        <CircleHelpIcon className="w-4 h-4 mr-2" />
                        Submit Feedback Survey
                      </Button>
                    </Link>
                  ) : dbOrg?.pilotDetails?.faqLink ? (
                    <Link href={dbOrg?.pilotDetails?.faqLink} target="_blank">
                      <Button className="mt-2 w-full" size={'default'}>
                        <CircleHelpIcon className="w-4 h-4 mr-2" />
                        View FAQ
                      </Button>
                    </Link>
                  ) : null}
                </div>
              </div>
            </div>
          )}
          {isTemp && (
            <div>
              <hr className="mx-2" />
              <div
                className={cn(
                  'px-2 mx-2 w-[93%] mt-4 flex flex-col text-base text-primary text-wrap duration-200',
                )}
                // style={{
                //   backgroundImage:
                //     "linear-gradient(to right, #2FB6E1 0%, #32C490 100%)",
                // }}
              >
                <div className="px-2">
                  <div className="flex items-center justify-between">
                    <p className="font-semibold text-sm">
                      This is a temporary account to demo Hyperbound
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        {authInfo?.isLoggedIn && (
          <CreateAssignmentModal
            modalOpen={assignmentModalOpen}
            setModalOpen={setAssignmentModalOpen}
            onSubmit={() => {
              setAssignmentModalOpen(false);
            }}
          />
        )}
        <ToastContainer position="bottom-right" />
        <CreateNewSubOrganization
          open={openCreateNewOrg}
          onOpenChange={setOpenCreateNewOrg}
        />
      </div>
      <LoginOrSignupModal
        modalOpen={isLoginOrSignupModalOpen}
        setModalOpen={setIsLoginOrSignupModalOpen}
      />
    </div>
  );
}
