import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useUserSession from '@/hooks/useUserSession';
import { cn } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { SidebarEvents } from '@/system/Analytics/events/SidebarEvents';
import { LockIcon } from 'lucide-react';
import Link from 'next/link';
import { ISidebarItem } from '..';
import { motion } from 'framer-motion';

interface IProps {
  item: ISidebarItem;
  open: boolean;
}

export default function Item({ item, open }: IProps) {
  const { isTemp, isLoggedIn } = useUserSession();

  if (!item.disabled && !item.hide) {
    return (
      <Link
        className="pl-[10px] overflow-hidden h-[40px] flex items-center mb-[4px]"
        href={item.to || ''}
        target={item.openInNewTab ? '_blank' : undefined}
        rel={item.openInNewTab ? 'noopener noreferrer' : undefined}
      >
        <div
          className={cn(
            'flex items-center hover:bg-gray-100 px-[13px] py-[13px] rounded-lg overflow-hidden flex-1 group',
            {
              'bg-gray-100': item.selected,
            },
          )}
        >
          <div className=" ">{item.icon}</div>
          {open && (
            <motion.div
              animate={{ opacity: open ? '100' : '0' }}
              initial={{ opacity: '0' }}
              transition={{
                duration: 0.02,
                delay: 0.05,
              }}
              className="flex-1 text-[14px] ml-[18px] h-[14px] leading-none"
            >
              {item.title}
            </motion.div>
          )}
        </div>
        <div className="ml-[9px] h-[40px]">
          {item.selected ? (
            <div className="w-[3px] bg-[#09090B] h-full rounded-tl-3xl rounded-bl-3xl"></div>
          ) : (
            <div className="w-[3px]"></div>
          )}
        </div>
      </Link>
    );
  } else if (!item.hide) {
    return (
      <TooltipProvider key={item.title} delayDuration={50}>
        <Tooltip
          onOpenChange={(o) => {
            if (o) {
              Analytics.track(SidebarEvents.TAB_HOVERED, {
                item: item.title,
              });
            }
          }}
        >
          <TooltipTrigger disabled={item.disabled} asChild>
            <div tabIndex={0} className={'flex items-center opacity-30'}>
              <div className="pl-[24px] py-[16px]">{item.icon}</div>
              {open && (
                <motion.div
                  animate={{ opacity: open ? '100' : '0' }}
                  initial={{ opacity: '0' }}
                  transition={{
                    duration: 0.02,
                    delay: 0.05,
                  }}
                  className="text-[14px] ml-[18px]"
                >
                  {item.title}
                </motion.div>
              )}
              {open && <LockIcon size={14} className="ml-2" />}
            </div>
          </TooltipTrigger>
          <TooltipContent side="bottom" className="ml-2">
            {isTemp ? (
              <p>This is a temporary demo account</p>
            ) : !isLoggedIn ? (
              <p>Book a demo to access {item.title}</p>
            ) : (
              <p>
                {item.disabledText ||
                  `Contact Hyperbound team to access ${item.title}`}
              </p>
            )}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
}
