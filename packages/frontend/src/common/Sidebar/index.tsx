import CountdownTimer from '@/components/CountdownTimer';
import { Button } from '@/components/ui/button';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useUserSession from '@/hooks/useUserSession';
import LinksManager, { Pages } from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import { LightningBoltIcon } from '@radix-ui/react-icons';
import { motion } from 'framer-motion';
import _ from 'lodash';
import {
  BarChart2Icon,
  BookOpenText,
  BotIcon,
  CalendarIcon,
  CheckIcon,
  CircleHelpIcon,
  ClockIcon,
  Headset,
  HomeIcon,
  LayoutListIcon,
  LibraryBig,
  LinkIcon,
  ListChecks,
  LockIcon,
  Target,
  TrophyIcon,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, {
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import Item from './Item';
import OrgDropdown from './orgDropdown';
import UserDropDown from './userDropDown';
import { useSSOUrl } from '@/app/auth-wrapper';

export interface ISidebarItem {
  title: string;
  icon: React.ReactNode;
  to?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
  disabled?: boolean;
  subPages?: ISidebarItem[];
  selected: boolean;
  openInNewTab?: boolean;
  open?: boolean;
  setOpen?: (o: boolean) => void;
  isNew?: boolean;
  hide?: boolean;
  disabledText?: string;
}

export default function Sidebar() {
  const {
    isOrgActive,
    isLoggedIn,
    isTemp,
    isPilotEnded,
    isCompetitionOrg,
    competitionTag,
    dbOrg,
    hideHomePage,
    poweredByBranding,
    showPlanAssessment,
    onlyAccessFromIframes,
    hideAiRoleplay,
    hideAllBots,
    showCopySSOUrlButton,
  } = useUserSession();

  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const pathname = usePathname();

  const [open, setOpen] = useState(false); //sidebar state

  /**************************/
  /******** INIT **********/
  /**************************/

  const topItems: ISidebarItem[] = useMemo(() => {
    if (isLoggedIn && !isOrgActive) {
      return [
        {
          title: 'Home',
          icon: <HomeIcon size={16} />,
          to: Pages.HOME,
          selected: pathname?.includes(Pages.HOME),
        },
      ];
    }
    const sidebarItems: ISidebarItem[] = [
      {
        title:
          isLoggedIn || isCompetitionOrg
            ? isCompetitionOrg
              ? 'Competition Home'
              : 'Home'
            : 'Leaderboard',
        icon: <HomeIcon size={16} />,
        to: Pages.HOME,
        selected: pathname?.includes(Pages.HOME),
        disabled: !isLoggedIn || isTemp,
        hide: hideHomePage,
      },
      ...(!isCompetitionOrg && !hideAiRoleplay
        ? [
            {
              title: 'AI Roleplays',
              icon: <BotIcon size={16} />,
              to: isLoggedIn ? Pages.AI_ROLEPLAY : '/buyers',
              selected: pathname?.includes(
                isLoggedIn ? Pages.AI_ROLEPLAY : '/buyers',
              ),
            },
          ]
        : []),
      ...(!onlyAccessFromIframes
        ? [
            {
              title: 'Call History',
              icon: <LayoutListIcon className="w-4 h-4" />,
              to: LinksManager.trainingCalls(),
              selected: pathname?.includes(Pages.TRAINING_CALLS),
            },
          ]
        : []),
      ...(isCompetitionOrg
        ? [
            {
              title: 'Leaderboard',
              icon: <TrophyIcon size={16} />,
              to: `/competitions/${competitionTag}/leaderboard`,
              selected: pathname?.includes(
                `/competitions/${competitionTag}/leaderboard`,
              ),
            },
          ]
        : []),
      ...(!isCompetitionOrg && !onlyAccessFromIframes
        ? [
            {
              title: isLoggedIn ? 'Call Blitz' : 'Simulated Dialer',
              icon: <LightningBoltIcon className="w-4 h-4" />,
              to: '/call-blitz',
              selected: pathname?.includes('/call-blitz'),
              disabled: !isLoggedIn || isTemp,
            },
          ]
        : []),
      ...(!isCompetitionOrg
        ? [
            {
              title: 'Analytics',
              icon: <BarChart2Icon size={16} />,
              to: Pages.ANALYTICS,
              disabled: !isLoggedIn || isTemp,
              selected: pathname?.includes(Pages.ANALYTICS),
            },
          ]
        : []),
    ];

    return sidebarItems;
  }, [
    pathname,
    isLoggedIn,
    isOrgActive,
    isTemp,
    isCompetitionOrg,
    competitionTag,
  ]);

  const bottomItems: ISidebarItem[] = useMemo(() => {
    if (isLoggedIn && !isOrgActive) {
      return [];
    }
    const sidebarItems: ISidebarItem[] = [
      ...(isCompetitionOrg
        ? [
            {
              title: 'Book a demo',
              icon: <CalendarIcon size={16} />,
              to: 'https://forms.default.com/422737',
              selected: false,
              openInNewTab: true,
            },
            {
              title: 'AI Roleplay',
              icon: <BotIcon size={16} />,
              to: isLoggedIn ? '/ai-roleplay' : '/buyers',
              selected: pathname?.includes(
                isLoggedIn ? '/ai-roleplay' : '/buyers',
              ),
            },
            {
              title: 'Real Call Scoring',
              icon: <Headset size={16} />,
              to: isLoggedIn ? '/real-calls' : '/real-calls',
              selected: pathname?.includes(
                isLoggedIn ? '/real-calls' : '/real-calls',
              ),
            },
            {
              title: 'Analytics',
              icon: <BarChart2Icon size={16} />,
              to: '/analytics',
              disabled: !isLoggedIn || isTemp,
              selected: pathname?.includes('/analytics'),
            },
          ]
        : []),
      ...(!isCompetitionOrg && !onlyAccessFromIframes && !hideAllBots
        ? [
            {
              title: 'All Bots',
              icon: <LibraryBig size={16} />,
              to: '/buyers',
              selected: pathname?.includes('/buyers'),
              hide: !isLoggedIn,
            },
          ]
        : []),
      ...(!onlyAccessFromIframes
        ? [
            {
              title: 'Learning Modules',
              icon: <BookOpenText size={16} />,
              to: LinksManager.learningModules(),
              selected: pathname?.includes(Pages.LEARNING_MODULES),
              disabled: !isLoggedIn || isTemp,
            },
          ]
        : []),
      // ...(!hideCoaching
      //   ? [
      //       {
      //         title: 'Coaching',
      //         icon: <Book size={16} />,
      //         to: '/coaching/knowledge-gap',
      //         selected: pathname?.includes('/coaching/knowledge-gap'),
      //         disabled: !isLoggedIn || isTemp,
      //       },
      //     ]
      //   : []),
      {
        title: 'AI Scorecards',
        icon: <Target size={16} />,
        to: LinksManager.scorecards(),
        selected: pathname?.includes(Pages.SCORECARDS),
        disabled: !isLoggedIn || isTemp,
      },
      {
        title: 'Plan Assessment',
        icon: <ListChecks size={16} />,
        to: '/plan-assessment',
        selected: pathname?.includes('/plan-assessment'),
        disabled: !isLoggedIn || isTemp,
        hide: !showPlanAssessment,
      },
    ];

    return sidebarItems;
  }, [pathname, isLoggedIn, isOrgActive, isTemp, isCompetitionOrg]);

  /****************************************************************************/
  /********************************* DROPDOWNS ********************************/
  /****************************************************************************/
  /*
     When a dropdown is opened, it fire mouseLeave that closes the sidebar.
    To prevent that, we lock the sidebar when the dropdown is opened.
    But on close, we need to check if the mouse is still over the sidebar.
  */
  /****************************************************************************/

  const isSidebarLocked = useRef<boolean>(false);
  const sidebarPanel = useRef<HTMLDivElement>(null);
  const currentMousePosition = useRef<{ x: number; y: number }>({ x: 0, y: 0 });

  const lockSidebarOpen = (lockOpen: boolean) => {
    isSidebarLocked.current = lockOpen;
    if (!lockOpen && sidebarPanel.current) {
      const rect = sidebarPanel.current.getBoundingClientRect();
      if (rect.x <= currentMousePosition.current.x) {
        setOpen(false);
      }
    }
  };

  const onMouseMove = (e: MouseEvent) => {
    currentMousePosition.current = { x: e.x, y: e.y };
  };

  useEffect(() => {
    document.addEventListener('mousemove', onMouseMove);
    return () => {
      document.removeEventListener('mousemove', onMouseMove);
    };
  }, []);

  /**************************/
  /******** RENDER **********/
  /**************************/

  useLayoutEffect(() => {
    function updateSize() {
      if (sidebarPanel.current) {
        const rect = sidebarPanel.current.getBoundingClientRect();
        const h = window.innerHeight - rect.top;
        sidebarPanel.current.style.height = `${h}px`;
      }
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  const ssoUrl = useSSOUrl();
  const [isSSOUrlCopied, setIsSSOUrlCopied] = useState(false);
  const onCopySSOUrl = async () => {
    if (!ssoUrl || isSSOUrlCopied) {
      return;
    }
    await navigator.clipboard.writeText(ssoUrl);
    setIsSSOUrlCopied(true);
    setTimeout(() => {
      setIsSSOUrlCopied(false);
    }, 2000);
  };

  const pilotCountdown = (
    <CountdownTimer
      targetDate={dbOrg?.pilotDetails?.expiryDate ?? new Date()}
      expiredMessage="Ended"
      under24hElement={
        open ? undefined : (
          <p
            className={cn(' text-teal-600', {
              '': open,
              'flex flex-col': !open,
            })}
          >
            <span className="font-semibold">&lt;1</span>{' '}
            <span className="uppercase text-[12px] font-medium">day</span>
          </p>
        )
      }
      include={{
        days: true,
        hours: open,
        minutes: open,
        seconds: false,
      }}
      elementClassName="text-center text-teal-600 text-[13px]"
      valClassName="font-semibold"
      suffixClassName="uppercase text-[11px]"
    />
  );

  return (
    <motion.div
      animate={{ width: open ? '274px' : '68px' }}
      initial={{ width: '68px' }}
      transition={{
        ease: 'easeInOut',
        duration: 0.1,
        delay: 0,
      }}
      className="z-50 bg-white border-line-primary border-r h-full flex flex-col pb-4 fixed overflow-hidden"
      onMouseEnter={() => {
        setOpen(true);
      }}
      onMouseLeave={() => {
        if (!isSidebarLocked.current) {
          setOpen(false);
        }
      }}
      ref={sidebarPanel}
    >
      {/* HYPERBOUND LOGO */}
      <div className="flex items-center mt-4 ml-[17px]">
        <Image
          src={`/images/hyperbound-logo.svg`}
          alt="Hyperbound logo"
          width={28}
          height={28}
          className="rounded-lg"
          priority
        />

        {!poweredByBranding?.logo && (
          <motion.div
            animate={{ opacity: open ? '100' : '0' }}
            initial={{ opacity: '0' }}
            transition={{
              duration: 0.02,
              delay: 0.05,
            }}
            className="flex-1 ml-[12px]"
          >
            <Image
              src={`/images/hyperbound-logo-text.svg`}
              alt="Hyperbound logo"
              width={104}
              height={16}
              priority
            />
          </motion.div>
        )}

        {!!poweredByBranding?.logo && (
          <motion.div
            animate={{ opacity: open ? '100' : '0' }}
            initial={{ opacity: '0' }}
            transition={{
              duration: 0.02,
              delay: 0.05,
            }}
            className="flex-1 flex flex-row items-stretch"
          >
            <div className="mx-[12px] py-2">
              <div className=" border-l h-full" />
            </div>
            <img
              src={poweredByBranding.logo.url}
              alt={poweredByBranding.logo.altText || 'Logo'}
              style={{
                width: poweredByBranding.logo.width,
                height: poweredByBranding.logo.height,
              }}
            />
          </motion.div>
        )}
      </div>

      {/* ORG SELECTOR */}
      {isLoggedIn ? (
        <OrgDropdown open={open} lockSidebarOpen={lockSidebarOpen} />
      ) : (
        <div className="mt-10"></div>
      )}

      {topItems.map((item, index) => {
        return <Item key={'sb-' + index} item={item} open={open} />;
      })}

      {/* <div className="text-xs text-muted-foreground ml-[14px] mt-4 mb-2">COACH</div> */}
      <div className="flex-1"></div>

      {dbOrg?.pilotDetails?.expiryDate &&
        !isPilotEnded &&
        (open ? (
          <div className="rounded-xl border border-[#D7F2F5] bg-[#F9FDFD] mx-2 mb-2">
            <div
              className={cn('flex flex-col text-base text-primary text-wrap')}
            >
              <div className="p-2">
                <div className="bg-[#E7F9F8] rounded-md py-0.5 flex flex-row px-2 items-center">
                  <p className="text-left w-full flex-1 line-clamp-1">
                    {pilotCountdown}
                  </p>
                  <ClockIcon className="w-3.5 h-3.5 text-teal-600" />
                </div>
                <p className="line-clamp-1 font-medium text-sm mt-2">
                  Your Hyperbound pilot is active
                </p>
                {dbOrg?.pilotDetails?.surveyLink ? (
                  <Link href={dbOrg?.pilotDetails?.surveyLink} target="_blank">
                    <Button
                      className="mt-2 w-full bg-white"
                      size={'default'}
                      variant={'outline'}
                    >
                      <CircleHelpIcon className="w-4 h-4 mr-2" />
                      Submit Feedback Survey
                    </Button>
                  </Link>
                ) : dbOrg?.pilotDetails?.faqLink ? (
                  <Link href={dbOrg?.pilotDetails?.faqLink} target="_blank">
                    <Button className="mt-2 w-full" size={'default'}>
                      <CircleHelpIcon className="w-4 h-4 mr-2" />
                      View FAQ
                    </Button>
                  </Link>
                ) : null}
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-[#EBF9FA] rounded-md mx-3 py-0.5 mb-2">
            <p className="text-center w-full">{pilotCountdown}</p>
          </div>
        ))}

      {dbOrg?.pilotDetails?.expiryDate &&
        isPilotEnded &&
        (open ? (
          <div className="rounded-xl  bg-red-50 mx-2 mb-2">
            <div
              className={cn('flex flex-col text-base text-primary text-wrap')}
            >
              <div className="p-2 flex flex-col items-start">
                <div className="bg-red-100 rounded-md flex flex-col items-center py-1 px-2 text-red-600">
                  <LockIcon className="w-4 h-4  font-semibold" />
                  <span className="text-[10px]">OVER</span>
                </div>
                <p className="line-clamp-1 font-medium text-sm mt-3">
                  Your pilot has ended
                </p>
                <p className="line-clamp-1 text-sm text-muted-foreground mt-1">
                  Contact us to chat about next steps.
                </p>
                {dbOrg?.pilotDetails?.surveyLink ? (
                  <Link href={dbOrg?.pilotDetails?.surveyLink} target="_blank">
                    <Button
                      className="mt-2 w-full bg-white"
                      size={'default'}
                      variant={'outline'}
                    >
                      <CircleHelpIcon className="w-4 h-4 mr-2" />
                      Submit Feedback Survey
                    </Button>
                  </Link>
                ) : dbOrg?.pilotDetails?.faqLink ? (
                  <Link href={dbOrg?.pilotDetails?.faqLink} target="_blank">
                    <Button
                      className="mt-2 w-full bg-white"
                      size={'default'}
                      variant={'outline'}
                    >
                      <CircleHelpIcon className="w-4 h-4 mr-2" />
                      View FAQ
                    </Button>
                  </Link>
                ) : null}
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-red-50 rounded-md mx-3 mb-2 flex flex-col items-center py-1 text-red-600">
            <LockIcon className="w-4 h-4  font-semibold" />
            <span className="text-[10px]">OVER</span>
          </div>
        ))}

      {showCopySSOUrlButton && (
        <div className="flex flex-row justify-center w-full mb-4">
          <div
            className="flex flex-row items-center w-full mx-4 rounded-full shadow-lg bg-white border px-3 py-1.5 cursor-pointer"
            onClick={onCopySSOUrl}
          >
            {isSSOUrlCopied ? (
              <CheckIcon className="w-4 h-4 text-green-500" />
            ) : (
              <LinkIcon className="text-black h-4 w-4" />
            )}
            {open && (
              <div className="flex-1 flex flex-row justify-center pr-4">
                <span className="text-sm font-medium ml-2  truncate overflow-hidden whitespace-nowrap">
                  {isSSOUrlCopied ? 'Copied Deeplink URL' : 'Copy Deeplink URL'}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
      {bottomItems.map((item, index) => {
        return <Item key={'sb-' + index} item={item} open={open} />;
      })}

      <div className="h-2"></div>

      {(isLoggedIn || !_.isEmpty(hbDemoInboundForm)) && (
        <UserDropDown open={open} lockSidebarOpen={lockSidebarOpen} />
      )}
    </motion.div>
  );
}
