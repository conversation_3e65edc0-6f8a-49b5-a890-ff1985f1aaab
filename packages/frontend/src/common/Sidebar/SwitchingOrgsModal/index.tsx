import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Loader2Icon } from 'lucide-react';

export type SwitchingModalState = 'closed' | 'switching' | 'error';
export const SwitchingOrgsModal = ({
  state,
  setState,
}: {
  state: SwitchingModalState;
  setState: (newState: SwitchingModalState) => unknown;
}) => {
  return (
    <Dialog
      open={state !== 'closed'}
      onOpenChange={(open) => {
        if (state === 'error' && !open) {
          setState('closed');
        }
      }}
    >
      <DialogContent className={state === 'error' ? 'close-btn' : ''}>
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {state === 'error' ? 'Error' : 'Switching'}
          </DialogTitle>
          <DialogDescription className="py-4">
            {state === 'switching' && (
              <Loader2Icon
                size={20}
                className="inline-block animate-spin text-black mr-2 pb-0.5"
              />
            )}
            {state === 'error'
              ? 'There was an error in switching orgs. Please logout and login again.'
              : 'Switching orgs…'}
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};
