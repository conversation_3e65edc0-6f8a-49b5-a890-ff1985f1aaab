import { useState, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { CheckCircle2, CloudUpload, Info, Loader2Icon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Checkbox from '@/components/ui/Hyperbound/checkbox-with-label';
import OrganizationService from '@/lib/Organization';
import { toast, ToastContainer } from 'react-toastify';
import { useDropzone } from 'react-dropzone';
import { createPortal } from 'react-dom';

interface IProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
export default function CreateNewSubOrganization({
  open,
  onOpenChange,
}: IProps) {
  const [name, setName] = useState<string>('');
  const [domainExtension, setDomainExtension] = useState<string>('');
  const [enableAutoJoiningByDomain, setEnableAutoJoiningByDomain] =
    useState<boolean>(false);
  const [membersMustHaveMatchingDomain, setMembersMustHaveMatchingDomain] =
    useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [canSave, setCanSave] = useState<boolean>(false);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [errors, setErrors] = useState<any>({
    name: '',
    domain: '',
  });
  const [isSuccess, setIsSuccess] = useState<boolean>(false);

  const verifyCanSave = (n: string | undefined, d: string | undefined) => {
    let _name = name;
    let _domainExtension = domainExtension;
    if (n) {
      _name = n;
    }
    if (d) {
      _domainExtension = d;
    }

    let noErrors = false;

    if (_name.length > 0) {
      const regex = /^[-_ a-zA-Z0-9]+$/; ///^[a-zA-Z0-9]+$/;
      if (regex.test(_name)) {
        noErrors = true;
        setErrors({ ...errors, name: '' });
      } else {
        setErrors({ ...errors, name: 'only alphanumeric charactes' });
      }
    }

    if (noErrors && _domainExtension.length > 0) {
      setCanSave(true);
    } else {
      setCanSave(false);
    }
  };

  const save = async () => {
    let logo = undefined;
    if (acceptedFiles[0]) {
      logo = acceptedFiles[0];
    }

    setSaving(true);

    let res = undefined;
    try {
      res = await OrganizationService.createSubOrganization(
        name,
        domainExtension,
        enableAutoJoiningByDomain,
        membersMustHaveMatchingDomain,
        logo,
      );

      if (!res) {
        toast.error('An error occurred while creating the organization.');
      } else {
        setIsSuccess(true);
      }
    } catch (e: any) {
      toast.error('An error occurred while creating the organization.');
    }

    if (res) {
      close();
    }

    setSaving(false);
  };

  const close = () => {
    setName('');
    setDomainExtension('');
    setEnableAutoJoiningByDomain(false);
    setMembersMustHaveMatchingDomain(false);
    setCanSave(false);
    setSaving(false);
    setImagePreview('');
    setIsSuccess(false);
    onOpenChange(false);
  };

  /// LOGO UPLOAD:

  const onDrop = useCallback((files: File[]) => {
    let image: string = '';

    if (files) {
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > 1024 * 1024 * 2) {
          //2MB
          toast.error('File size must be less than 2MB');
          return;
        }
        image = URL.createObjectURL(files[i]);
      }

      setImagePreview(image);
    }
  }, []);

  const { acceptedFiles, getRootProps, getInputProps, isDragActive } =
    useDropzone({ onDrop });

  const renderForm = (
    <>
      <DialogHeader>
        <DialogTitle className="flex items-center">
          Create new organization
        </DialogTitle>
      </DialogHeader>
      <div className="mt-6">
        <div className="">
          <Label className="mb-2">Name *</Label>
          <div className="flex items-center">
            <Input
              value={name}
              onChange={(e) => {
                setName(e.target.value);
                verifyCanSave(e.target.value, undefined);
              }}
              className="w-full"
              placeholder=""
            />
          </div>
          <div className="text-sm text-red-600 h-[20px]">{errors.name}</div>
        </div>
        <div className="mt-4">
          <Label className="mb-2 flex">
            Domain *
            <TooltipProvider delayDuration={50}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="w-4 h-4 ml-1 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  Domain is the domain name of the organization. Eg:
                  hyperbound.ai
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          <div className="">
            <Input
              value={domainExtension}
              onChange={(e) => {
                setDomainExtension(e.target.value);
                verifyCanSave(undefined, e.target.value);
              }}
              className="w-full"
              placeholder=""
            />
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            i.e. hyperbound.ai (no &quot;www&quot; needed)
          </p>
        </div>
        <div className="mt-8">
          <Label className="mb-2 flex">Logo</Label>
          <div {...getRootProps()}>
            <input {...getInputProps()} />
            <div className="bg-muted rounded-lg p-2 cursor-pointer">
              <div className="border border-dashed border-slate-300 p-3 flex flex-col items-center justify-center rounded-lg">
                {!imagePreview && (
                  <div className="mb-2">
                    <CloudUpload className="w-5 h-5 text-muted-foreground" />
                  </div>
                )}
                {isDragActive ? (
                  <>
                    <div className="text-sm text-muted-foreground mb-2">
                      Drop file here...
                    </div>
                    <div className="text-xs text-muted-foreground">&nbsp;</div>
                  </>
                ) : imagePreview ? (
                  <img
                    src={imagePreview}
                    alt="logo"
                    className="w-12 h-12 rounded-full"
                  />
                ) : (
                  <>
                    <div className="text-sm text-muted-foreground mb-2">
                      Click to select or drag&apos;n&apos;drop a file here
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Upload a square image for better results
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="mt-6">
          <Checkbox
            className="py-2"
            checked={enableAutoJoiningByDomain}
            onToggle={() =>
              setEnableAutoJoiningByDomain(!enableAutoJoiningByDomain)
            }
          >
            Any user with an @{domainExtension} email address can join
          </Checkbox>
        </div>
        <div>
          <Checkbox
            checked={membersMustHaveMatchingDomain}
            onToggle={() =>
              setMembersMustHaveMatchingDomain(!membersMustHaveMatchingDomain)
            }
          >
            Users without an @{domainExtension} email address cannot be invited
          </Checkbox>
        </div>
      </div>
      <DialogFooter>
        <Button
          variant="default"
          onClick={save}
          disabled={saving || !canSave}
          className="w-[70px]"
        >
          {saving ? <Loader2Icon className="animate-spin" /> : 'Save'}
        </Button>
        <Button variant="outline" onClick={close}>
          Close
        </Button>
      </DialogFooter>
    </>
  );

  const renderSuccess = (
    <>
      <DialogHeader>
        <DialogTitle>Organization created successfully</DialogTitle>
      </DialogHeader>
      <div className="my-4 p-4 border rounded-lg bg-[#9ce0f2]/10 border-[#9ce0f2]/50">
        <div className="flex flex-row items-center justify-start mb-4">
          <CheckCircle2 className="w-6 h-6 mr-2 text-[#28a0a0]" />
          <p className="font-medium">Organization "{name}" has been created</p>
        </div>
        <p className="text-sm">
          You can now invite users to join the organization. Switch to the
          organization by selecting it from the organization dropdown menu.
        </p>
      </div>
      <DialogFooter>
        <Button variant="outline" onClick={close}>
          Close
        </Button>
      </DialogFooter>
    </>
  );

  return (
    <>
      <Dialog open={open}>
        <DialogContent>{isSuccess ? renderSuccess : renderForm}</DialogContent>
      </Dialog>
      {createPortal(<ToastContainer />, document.body)}
    </>
  );
}
