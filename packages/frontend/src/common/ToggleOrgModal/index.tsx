import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import useUserSession from '@/hooks/useUserSession';
import { useAuthInfo } from '@propelauth/react';
import Image from 'next/image';

interface ICreateAssignmentModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  onSubmit: () => void;
}

function ToggleOrgModal({
  modalOpen,
  setModalOpen,
  onSubmit,
}: ICreateAssignmentModalProps) {
  const authInfo = useAuthInfo();
  const { sortedOrgs } = useUserSession();

  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">Switch Orgs</DialogTitle>
          <DialogDescription className="py-4">
            <ScrollArea className="h-72 w-48 rounded-md border">
              {sortedOrgs.map((org) => (
                <Button
                  key={org.orgId}
                  variant="ghost"
                  className="p-2 cursor-pointer"
                >
                  <div className="flex space-x-2 items-center">
                    <Image
                      src={'/images/square-black-logo.svg'}
                      alt={`Hyperbound logo`}
                      width={20}
                      height={20}
                      className="rounded-md flex-shrink-0"
                      priority
                    />
                    <p className="text-base font-medium">
                      {org?.orgName || 'Hyperbound'}
                    </p>
                  </div>
                </Button>
              ))}
            </ScrollArea>
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}

export default ToggleOrgModal;
