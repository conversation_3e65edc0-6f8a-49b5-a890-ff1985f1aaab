import React, { useState } from 'react';
import { TeamDto } from '@/lib/User/types';
import { cn } from '@/lib/utils';
import {
  Edit2,
  ArrowRight,
  Loader2Icon,
  ChevronLeft,
  CheckIcon,
} from 'lucide-react';

interface ISelectTeamProps {
  team: TeamDto;
  selected: boolean;
  toggleTeam: (t: TeamDto, isAdmin: boolean) => void;
}
export default function SelectTeam({
  team,
  selected,
  toggleTeam,
}: ISelectTeamProps) {
  const [current, setCurrent] = useState<number>(0);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);

  const next = () => {
    if (selected) {
      doToggle();
    } else {
      setCurrent(current + 1);
    }
  };

  const previous = () => {
    setCurrent(current - 1);
  };

  const doToggle = (newAdminStatus?: boolean) => {
    if (toggleTeam) {
      if (newAdminStatus) {
        toggleTeam(team, newAdminStatus);
      } else {
        toggleTeam(team, isAdmin);
      }
    }
    setCurrent(0);
  };

  const toggleAdmin = () => {
    setIsAdmin(!isAdmin);
    doToggle(!isAdmin);
  };

  return (
    <div className="overflow-hidden">
      <div
        className={`flex text-sm transition ease-out duration-600`}
        style={{ transform: `translateX(-${current * 100}%)` }}
      >
        {/********************/}
        {/***** TEAM BTN *****/}
        {/********************/}
        <div
          className="text-sm flex intems-center pl-2 py-1 hover:bg-muted cursor-pointer shrink-0 w-full"
          onClick={() => {
            next();
          }}
        >
          <div
            className={cn(
              'mr-2 flex items-center justify-center rounded-sm border border-primary',
              selected
                ? 'bg-primary text-primary-foreground'
                : 'opacity-50 [&_svg]:invisible',
            )}
          >
            <CheckIcon className={cn('h-4 w-4')} />
          </div>
          <div>{team.name}</div>
        </div>

        {/********************/}
        {/***** ROLE BTN *****/}
        {/********************/}
        <div className="flex items-center shrink-0 w-full ">
          <div
            onClick={previous}
            className="hover:bg-muted cursor-pointer h-full flex items-center px-1 mr-2"
          >
            <ChevronLeft size={18} />
          </div>
          <div
            className="text-sm flex intems-center cursor-pointer flex-1"
            onClick={() => {
              toggleAdmin();
            }}
          >
            <div
              className={cn(
                'mr-2 flex items-center justify-center rounded-sm border border-primary',
                isAdmin
                  ? 'bg-primary text-primary-foreground'
                  : 'opacity-50 [&_svg]:invisible',
              )}
            >
              <CheckIcon className={cn('h-4 w-4')} />
            </div>
            <div>Is Admin</div>
          </div>

          <div
            onClick={(e) => doToggle()}
            className="hover:bg-muted cursor-pointer h-full flex items-center px-1"
          >
            <ArrowRight size={18} />
          </div>
        </div>
      </div>
    </div>
  );
}
