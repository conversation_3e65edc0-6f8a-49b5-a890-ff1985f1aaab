'use client';

import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import DashboardNavbar from '@/common/DashboardNavbar';
import { CHALLENGE_BOT_VAPI_ID } from '@/common/Sidebar/OldSidebar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useAgent } from '@/hooks/useAgent';
import useDemoAgentByVapiId from '@/hooks/useDemoAgentByVapiId';
import useDemoAgents from '@/hooks/useDemoAgents';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useMobile from '@/hooks/useMobile';
import useOrgAgents from '@/hooks/useOrgAgents';
import useOrgVariations from '@/hooks/useOrgVariations';
import useRemainingAgents from '@/hooks/useRemainingAgents';
import useOrgAgentVariations from '@/hooks/useOrgAgentVariations';
import AgentService from '@/lib/Agent';
import {
  AgentCallType,
  AgentDto,
  AgentStatus,
  AnyAgentDto,
  PublicAgentDto,
  VariationDto,
} from '@/lib/Agent/types';
import { RoleEnum } from '@/lib/User/types';
import { cn } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { BuyerEvents } from '@/system/Analytics/events/BuyerEvents';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  BrainIcon,
  CopyPlusIcon,
  Edit2Icon,
  EditIcon,
  LayoutListIcon,
  ListChecksIcon,
  LockIcon,
  MoreVerticalIcon,
  PlusIcon,
  FileStack,
  Headphones,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import LinksManager from '@/lib/linksManager';
import usePartnerByCachedPartnerId from '@/hooks/usePartnerByCachedPartnerId';
import CallSimulationPanel from '@/components/CallSimulationPanel';
import AgentAvatar from '@/components/Avatars/Agent';

export default function BuyersDemoSite() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const authInfo = useAuthInfo();
  const errorToastId = useRef<Id | null>(null);
  const searchParams = useSearchParams();
  const curSearchParams = new URLSearchParams(searchParams);
  const {
    data: orgAgents,
    isLoading,
    fetchStatus,
  } = useOrgAgents(curSearchParams?.get('callType') as AgentCallType);
  const { data: partner, isLoading: isPartnerLoading } =
    usePartnerByCachedPartnerId();

  let variationId = '';
  let fetchVariatons = false;
  if (curSearchParams && curSearchParams.get('variation')) {
    variationId = curSearchParams.get('variation') || '';
    if (variationId != '') {
      fetchVariatons = authInfo?.isLoggedIn || false; //only loggred user can access variations
    }
  }
  const { data: agentVariations } = useOrgAgentVariations(
    variationId,
    fetchVariatons,
  );

  const org = useActiveOrg();
  const { data: demoAgents, isLoading: isLoadingDemoAgents } = useDemoAgents(
    !authInfo?.isLoggedIn,
  );
  const {
    data: myAgent,
    isFetched: isFetchedMyAgent,
    isLoading: isLoadingMyAgent,
    isSuccess: isSuccessMyAgent,
    isError: isErrorMyAgent,
  } = useAgent(searchParams.get('id') as string);
  const {
    data: demoAgent,
    isLoading: isLoadingDemoAgent,
    isFetched: isFetchedDemoAgent,
  } = useDemoAgentByVapiId(authInfo?.isLoggedIn ? isErrorMyAgent : true);

  const pathname = usePathname();
  const { data: remainingAgents, isLoading: isLoadingRemainingAgents } =
    useRemainingAgents();
  const typingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [shouldAnimate, setShouldAnimate] = useState(true);
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const [latestCall, setLatestCall] = useState<any>();
  const [callOngoing, setCallOngoing] = useState<boolean>(false);
  const isMobile = useMobile();

  if (!curSearchParams.get('callType')) {
    curSearchParams.set('callType', AgentCallType.COLD);
    router.replace(`${pathname}?${curSearchParams.toString()}`);
  }

  useEffect(() => {
    // After the initial render, set shouldAnimate to false
    setShouldAnimate(false);
  }, []);

  const changeCallType = (callType: AgentCallType) => {
    if (authInfo?.isLoggedIn) {
      curSearchParams.set('callType', callType);
      router.replace(`${pathname}?${curSearchParams.toString()}`);
    } else {
      errorToastId.current = toast.info(
        'Book a demo to access different call types.',
      );
    }
  };

  const { data: allVariations } = useOrgVariations(true);

  const changeVariation = (vapiId: string) => {
    if (vapiId == 'clear-variations') {
      curSearchParams.delete('variation');
      router.replace(`${pathname}?${curSearchParams.toString()}`);
    } else {
      curSearchParams.set('variation', vapiId);
      router.replace(`${pathname}?${curSearchParams.toString()}`);
    }
  };

  const curAgent = demoAgent || myAgent;
  const isLoadingCurAgent = isLoadingDemoAgent || isLoadingMyAgent;

  const demoBots = (demoAgents || []).filter(
    (a: AnyAgentDto) => a.vapiId !== CHALLENGE_BOT_VAPI_ID,
  );

  const filteredDemoBots = useMemo(() => {
    return demoBots.filter(
      (agent: AnyAgentDto) =>
        agent.callType === (curSearchParams?.get('callType') as AgentCallType),
    );
  }, [demoBots, curSearchParams?.get('callType')]);

  const filteredRemainingAgents = useMemo(() => {
    return (remainingAgents || []).filter(
      (agent: AnyAgentDto) =>
        agent.callType === (curSearchParams?.get('callType') as AgentCallType),
    );
  }, [remainingAgents, curSearchParams?.get('callType')]);

  let agents = !authInfo?.isLoggedIn
    ? [...filteredDemoBots, ...filteredRemainingAgents]
    : orgAgents;

  if (agentVariations) {
    agents = agentVariations;
  }

  const [search, setSearch] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<AnyAgentDto[]>(
    agents || [],
  );

  useEffect(() => {
    if (fetchStatus === 'idle') {
      setFilteredAgents(agents || []);
    }
    // filterAgents(search, filterByType);
    // }
    if (
      fetchStatus === 'idle' &&
      agents?.length &&
      (!curSearchParams.has('id') || curSearchParams.get('id') === 'undefined')
    ) {
      curSearchParams.set('id', agents?.[0].vapiId);
      router.replace(`${pathname}?${curSearchParams.toString()}`);
    }
  }, [agents?.length, fetchStatus]);

  useEffect(() => {
    if (
      !curSearchParams.has('id') ||
      curSearchParams.get('id') === 'undefined'
    ) {
      curSearchParams.set('id', filteredAgents?.[0]?.vapiId);
      router.replace(`${pathname}?${curSearchParams.toString()}`);
    }
  }, [filteredAgents?.[0]?.vapiId]);

  useEffect(() => {
    return () => {
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
      }
    };
  }, []);


  const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value) {
      setShouldAnimate(false);
    } else {
      setShouldAnimate(true);
    }
    setSearch(value);
    if (typingTimerRef.current) {
      clearTimeout(typingTimerRef.current);
    }

    typingTimerRef.current = setTimeout(() => {
      // Track your Analytics event here
      Analytics.track(BuyerEvents.SEARCHED_BUYERS, {
        searchValue: value,
      });
    }, 800);

    if (value) {
      setFilteredAgents(
        agents?.filter(
          (a) =>
            `${a.firstName} ${a.lastName}`
              .toLowerCase()
              .includes(value.toLowerCase()) ||
            a.jobTitle.toLowerCase().includes(value.toLowerCase()) ||
            a.companyName.toLowerCase().includes(value.toLowerCase()) ||
            (a as AgentDto).variationName
              ?.toLowerCase()
              .includes(value.toLowerCase()),
        ) ?? [],
      );
    } else {
      setFilteredAgents(agents || []);
    }
  };

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
    onSuccess: (agent, params) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error editing the buyer. Please try again.',
        );
      }
    },
  });

  const curAgents = authInfo?.isLoggedIn ? orgAgents : filteredDemoBots;

  const onStartCall = useCallback(
    (agent: AnyAgentDto) => {
      Analytics.track(BuyerEvents.SELECTED_FROM_BUYERS, {
        agentId: agent.id,
        isMobile,
      });
      if (isMobile) {
        router.push(`/buyers/${agent.vapiId}`);
      } else {
        curSearchParams.set('id', agent.vapiId);
        router.replace(`${pathname}?${curSearchParams.toString()}`, {
          scroll: false,
        });
        queryClient.refetchQueries({
          queryKey: authInfo?.isLoggedIn
            ? ['agent', agent.vapiId]
            : ['demoAgentByVapiId', agent.vapiId, hbDemoInboundForm?.id],
        });
      }
    },
    [authInfo?.isLoggedIn, hbDemoInboundForm?.id],
  );

  const renderAgentCard = (agent: AnyAgentDto, i: number) => {
    const card = (
      <Card
        key={agent.id}
        onClick={() => {
          onStartCall(agent);
        }}
        className={cn(
          'w-full md:w-[430px] ml-0 flex items-center relative shadow-sm transition-shadow duration-300 cursor-pointer ',
          {
            'opacity-50 cursor-not-allowed pointer-events-none': !agent.vapiId,
            'hover:shadow-xl': agent.id !== curAgent?.id,
            'ml-2 shadow-xl bg-muted/40': agent.id === curAgent?.id,
          },
        )}
      >
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size={'sm'}
              className="w-8 h-8 rounded-full p-0 absolute top-4 right-4"
            >
              <span className="sr-only">Open menu</span>
              {!agent?.vapiId ? (
                <LockIcon className="w-4 h-4 text-muted-foreground" />
              ) : (
                <MoreVerticalIcon className="h-4 w-4" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center">
            {authInfo?.accessHelper?.isAtLeastRole(
              org?.orgId as string,
              RoleEnum.ADMIN,
            ) && (
              <>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (agent.callType == AgentCallType.FOCUS) {
                      router.push(`buyers/focus?${agent.vapiId}`);
                    } else {
                      router.push(`/buyers/${agent.vapiId}/edit/main`);
                    }
                  }}
                >
                  <Edit2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                  <span>Edit</span>
                </DropdownMenuItem>
                {agent.callType != AgentCallType.FOCUS && (
                  <>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(
                          `/buyers/create/main?cloneBuyerId=${agent.vapiId}`,
                        );
                      }}
                    >
                      <CopyPlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                      <span>Clone</span>
                    </DropdownMenuItem>
                    {
                      //user should not be able to create variations from a variation:
                      ((authInfo?.isLoggedIn &&
                        !(agent as AgentDto).variationParentAgentId) ||
                        (agent as AgentDto).id ===
                          (agent as AgentDto).variationParentAgentId) && (
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`buyers/variations/${agent.vapiId}`);
                          }}
                        >
                          <FileStack className="w-4 h-4 mr-2 text-muted-foreground" />
                          <span>Variations</span>
                        </DropdownMenuItem>
                      )
                    }
                  </>
                )}

                {/* <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(`/coaching/assignments/${agent.id}`);
                    }}
                  >
                    <ListChecksIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>View assignments</span>
                  </DropdownMenuItem> */}
              </>
            )}
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                Analytics.track(BuyerEvents.VIEW_CALLS_CLICKED, {
                  agentId: agent.id,
                  from: 'buyers_page',
                });
                const allBuyersExceptCurrent = (curAgents || [])
                  .filter((a) => a.id !== agent.id && !!a.vapiId)
                  .map((a) => a.id);
                router.push(LinksManager.trainingCalls(`?buyers=${agent.id}`));
              }}
            >
              <LayoutListIcon className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>View calls</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <CardHeader className="flex flex-col py-4 w-full">
          <div className="flex flex-row items-start space-x-4 ">
            <div className="relative">
              <AgentAvatar
                className="w-[52px] h-[52px] relative"
                agent={agent}
              />

              {!(updateAgentMutation.isPending || isLoading) && (
                <div
                  className={cn(
                    'rounded-full p-1 w-4 h-4 border-white border-[3px] absolute bottom-0 right-1',
                    {
                      'bg-green-500': agent.status === AgentStatus.ACTIVE,
                      'bg-gray-500': agent.status === AgentStatus.INACTIVE,
                    },
                  )}
                />
              )}
            </div>
            <div>
              <CardTitle>
                {agent.firstName} {agent.lastName}
              </CardTitle>
              <CardDescription className="text-[0.76rem]">
                {agent.jobTitle} {'@  ' + agent.companyName}
              </CardDescription>
              <div>
                {(agent.emotionalState || agent.gender) && (
                  <div
                    className={cn('flex flex-wrap mt-2', {
                      'space-x-1': agent.emotionalState && agent.gender,
                      // (emotionalState && salesMethodology) ||
                      // (emotionalState && gender) ||
                      // (gender && salesMethodology),
                    })}
                  >
                    {agent.callType && (
                      <Badge className="mt-1" variant="secondary">
                        {CALL_TYPE_OPTIONS.find(
                          (item) => item.value === agent.callType,
                        )?.label ||
                          (agent.callType === 'focus'
                            ? 'Focus Call'
                            : agent.callType)}
                      </Badge>
                    )}
                    {agent.callType != AgentCallType.FOCUS &&
                      (agent.emotionalState ? (
                        <Badge
                          className="mt-1"
                          variant={authInfo?.isLoggedIn ? 'default' : 'outline'}
                        >
                          <BrainIcon className="w-3 h-3 mr-1" />{' '}
                          {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                            (item) => item.value === agent.emotionalState,
                          )?.label ||
                            agent.emotionalState ||
                            ''}
                        </Badge>
                      ) : (
                        <Skeleton className={cn('w-16 h-6 mr-1')} />
                      ))}

                    {agent.bookRate && (
                      <Badge className="mt-1" variant="default">
                        Book Rate:{' '}
                        {Number(agent?.bookRate).toLocaleString('en-US', {
                          style: 'percent',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 1,
                        })}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* {(agent as AgentDto)?.status && (
            <div className="flex space-x-2">
              {(agent as AgentDto)?.status !== AgentStatus.DRAFT ? (
                <div className="flex items-center space-x-2">
                  {authInfo?.accessHelper?.isAtLeastRole(
                    org?.orgId as string,
                    RoleEnum.ADMIN
                  ) && (
                    <Switch
                      disabled={isLoading || updateAgentMutation.isPending}
                      checked={
                        (agent as AgentDto)?.status === AgentStatus.ACTIVE
                      }
                      onCheckedChange={(value) => {
                        updateAgentMutation.mutate({
                          id: agent?.id,
                          status: value
                            ? AgentStatus.ACTIVE
                            : AgentStatus.INACTIVE,
                        });
                      }}
                      id="status-toggle"
                    />
                  )}
                  {authInfo?.accessHelper?.isAtLeastRole(
                    org?.orgId as string,
                    RoleEnum.ADMIN
                  ) &&
                    (updateAgentMutation.isPending || isLoading ? (
                      <Skeleton className="w-16 h-[22px] rounded-md" />
                    ) : (
                      <p>
                        {(agent as AgentDto)?.status === AgentStatus.ACTIVE
                          ? "Active"
                          : "Inactive"}
                      </p>
                    ))}
                </div>
              ) : (
                <Badge variant="default">
                  {_.capitalize((agent as AgentDto)?.status?.toLowerCase())}
                </Badge>
              )}
            </div>
          )} */}
        </CardContent>
        {/* <CardFooter className="absolute bottom-0 w-full">
          <div className="flex w-full justify-between space-x-2">
            <Button
              size={"lg"}
              variant={"default"}
              className={
                "w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200"
              }
              style={{
                backgroundImage:
                  "linear-gradient(to right, #2FB6E1 0%, #32C490 100%)",
              }}
              onClick={(e) => {
                e.stopPropagation();
                onStartCall(agent);
              }}
            >
              <PhoneIcon className="mr-2 h-4 w-4" />
              Call {agent.firstName}
            </Button>
          </div>
        </CardFooter> */}
      </Card>
    );
    return (
      // <motion.div
      //   key={agent.id}
      //   whileHover={{
      //     translateX: agent.id !== curAgent?.id ? 10 : 0,
      //     transition: { duration: 0.3 },
      //   }}
      //   // whileTap={{
      //   //   translateX: agent.id !== demoAgent?.id ? 10 : 0,
      //   //   transition: { duration: 0.3 },
      //   // }}
      //   initial={shouldAnimate ? { opacity: 0, y: -50 } : {}}
      //   animate={shouldAnimate ? { opacity: 1, y: 0 } : {}}
      //   exit={shouldAnimate ? { y: -50 } : {}}
      //   transition={{ duration: 0.5, delay: shouldAnimate ? i * 0.1 : 0 }}
      // >
      <div key={agent.id}>
        {!agent?.vapiId ? (
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger asChild>
                <span tabIndex={0}>{card}</span>
              </TooltipTrigger>
              <TooltipContent side="top" className="w-96">
                <p>Book a demo to unlock new bots and build your own bots</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          card
        )}
      </div>
      // </motion.div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <DashboardNavbar
        breadcrumbs={[
          { title: 'Buyer Bots' },
          {
            title: `${
              CALL_TYPE_OPTIONS.find(
                (c) => c.value === searchParams.get('callType'),
              )?.label || ''
            }s`,
          },
        ]}
        titleRight={
          <div className="flex items-center space-x-2">
            {!authInfo?.isLoggedIn && <Badge>FREE DEMO</Badge>}
          </div>
        }
        subContent={
          !authInfo?.isLoggedIn && (
            <p className="mt-2">
              Choose an AI buyer &amp; start a roleplay conversation in &lt; 10
              secs
            </p>
          )
        }
        rightContent={
          authInfo?.accessHelper?.isAtLeastRole(
            org?.orgId as string,
            RoleEnum.ADMIN,
          ) && (
            <div className="flex">
              {/* <Link
                onClick={() => {
                  // Analytics.track(CallEvents.START_NEW_CALL_CLICKED, {
                  //   agentId: agent?.id,
                  //   id: call?.id,
                  //   vapiId: call?.vapiId,
                  //   orgId: call?.orgId,
                  // });
                }}
                href={`/buyers/create/init`}
                className="h-min"
              >
                <Button variant={"default"}>
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Create new
                </Button>
              </Link>

              <Link
                onClick={() => {
                  // Analytics.track(CallEvents.START_NEW_CALL_CLICKED, {
                  //   agentId: agent?.id,
                  //   id: call?.id,
                  //   vapiId: call?.vapiId,
                  //   orgId: call?.orgId,
                  // });
                }}
                href={`/buyers/focus`}
                className="h-min"
              >
                <Button variant={"default"}>
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Create new Focus
                </Button>


              </Link> */}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant={'default'}>
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Create new
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="center">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={(e) => {
                      router.push(`/buyers/create/init`);
                    }}
                  >
                    Buyer Bot
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={(e) => {
                      router.push(`/buyers/focus`);
                    }}
                  >
                    Focus Call
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )
        }
      />
      <div className="flex-1 overflow-hidden">
        <div className="h-full w-full relative">
          <div className="absolute top-0 bottom-0 left-0 right-0">
            <div className="flex h-full">
              <div className="flex flex-col pt-4 px-4 border-r md:w-[475px] h-full relative w-full">
                <div className="flex flex-col absolute overflow-y-auto overflow-x-hidden left-0 right-0 top-0 bottom-0 p-4">
                  <div className="flex">
                    <div className="flex-1">
                      <Input
                        placeholder="Search buyer bots by name, job title, or company..."
                        value={search}
                        onChange={onSearchChange}
                        className="max-w-md"
                      />
                    </div>
                    {/* <div className="ml-2">
                      <Select disabled={variationId ? true : false} onValueChange={(value: AgentCallType) => { changeCallType(value) }} defaultValue={(curSearchParams.get("callType") as AgentCallType)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a call type" />
                        </SelectTrigger>
                        <SelectContent>
                          {CALL_TYPE_OPTIONS.map((option) => {
                            return (<SelectItem
                              key={option.value}
                              value={option.value}
                            >
                              {option.label}
                            </SelectItem>)
                          })}
                        </SelectContent>
                      </Select>
                    </div> */}
                    <div className="ml-2">
                      <Select
                        disabled={!authInfo?.isLoggedIn}
                        onValueChange={(v: string) => {
                          changeVariation(v);
                        }}
                        defaultValue={variationId}
                      >
                        <SelectTrigger>
                          <LockIcon className="w-4 h-4 mr-2" />
                          Choose a variation
                        </SelectTrigger>
                        <SelectContent>
                          {allVariations?.map((v) => {
                            return (
                              <SelectItem key={v.vapiId} value={v.vapiId}>
                                {v.name}
                              </SelectItem>
                            );
                          })}
                          <SelectItem
                            key="clear-variations"
                            value="clear-variations"
                          >
                            - Clear Variations -
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="mt-4 space-y-2">
                    <p className="text-muted-foreground mb-3">
                      {filteredAgents?.length === 0
                        ? 'No buyer bots found'
                        : `${filteredAgents?.length}${
                            filteredAgents?.length !== agents?.length
                              ? ` of ${agents?.length}`
                              : ''
                          } buyer bots${search ? ` for "${search}"` : ''}`}
                    </p>
                    {/* <AnimatePresence> */}
                    {(filteredAgents || []).map(renderAgentCard)}
                    {/* </AnimatePresence> */}
                  </div>
                </div>
              </div>
              <div className="hidden md:flex flex-col items-center flex-grow relative h-full">
                {!!curAgent && (
                  <div className="z-0 absolute left-0 right-0 top-0 bottom-0">
                    <div className="flex-1 w-full h-full">
                      <div className="h-full flex flex-col">
                        <DashboardNavbar
                          breadcrumbs={[
                            {
                              title: `${curAgent?.firstName || ''} ${curAgent?.lastName || ''} `,
                            },
                          ]}
                          rightContent={
                            <div className="flex space-x-2">
                              <div className="flex space-x-4">
                                {!(authInfo?.isLoggedIn && demoAgent) &&
                                  !!curAgent && (
                                    <Link
                                      href={LinksManager.trainingCalls(
                                        `?buyer=${curAgent?.id}`,
                                      )}
                                      className="h-min"
                                    >
                                      <Button variant={'default'}>
                                        <LayoutListIcon className="w-4 h-4 mr-2" />
                                        View calls
                                      </Button>
                                    </Link>
                                  )}

                                <TooltipProvider delayDuration={50}>
                                  <Tooltip>
                                    <TooltipTrigger disabled>
                                      <Button variant={'outline'} disabled>
                                        <EditIcon className="w-4 h-4 mr-2" />
                                        Edit
                                        <LockIcon className="w-4 h-4 ml-2" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent side="left">
                                      <p>
                                        Book a demo to customise your own buyer
                                        bot
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                          }
                        />
                        <div className="flex-1 bg-gradient-to-t from-[#1B1D20] to-black/70 flex flex-col">
                          <div className="flex items-center">
                            <div className="flex-grow"></div>
                            <div className="text-sm text-white flex items-center p-1">
                              <Headphones size={18} className="mr-2" />
                              Call with {curAgent?.firstName}{' '}
                              {curAgent?.lastName}
                            </div>
                            <div className="flex-grow"></div>
                          </div>
                          {!!curAgent && (
                            <CallSimulationPanel
                              agent={curAgent}
                              navigateToCallSummary={true}
                              narrowWidthDisplay
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
