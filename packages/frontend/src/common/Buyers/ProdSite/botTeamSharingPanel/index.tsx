import React, { useEffect, useState } from 'react';
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
} from '@/components/ui/sheet';
import { But<PERSON> } from '@/components/ui/button';
import { RoleEnum, TeamDto } from '@/lib/User/types';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { Loader2 } from 'lucide-react';
import dayjs from 'dayjs';
import useUserSession from '@/hooks/useUserSession';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardTitle, CardHeader } from '@/components/ui/card';
import TeamCard from '@/components/TeamCard';
import { useDebounce } from '@/hooks/ui/useDebounce';
import TeamsService from '@/lib/User/Team';
import { AgentDto } from '@/lib/Agent/types';
import AgentService from '@/lib/Agent';

interface ITeamShareAgentPanelProps {
  agent: AgentDto;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onShareWithTeams: () => void;
}

function TeamShareAgentPanel({
  agent,
  open,
  onOpenChange,
  onShareWithTeams,
}: ITeamShareAgentPanelProps) {
  const authInfo = useAuthInfo();
  const org = useActiveOrg();

  const { dbOrg } = useUserSession();

  const [teams, setTeams] = useState<TeamDto[]>([]);
  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  const [madeChanges, setMadeChanges] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedTeams, setSelectedTeams] = useState<number[]>(
    agent.teams || [],
  );

  const fetchTeams = async () => {
    setIsLoading(true);
    try {
      const teams = await TeamsService.getAll(0, 500, search, true, true);
      setTeams(teams);
    } catch (e) {
      console.log(`Error fetching teams`, e);
    } finally {
      setIsLoading(false);
    }
  };

  const debouncedFetchTeams = useDebounce(fetchTeams, 1000);

  useEffect(() => {
    debouncedFetchTeams();
  }, [search]);

  useEffect(() => {
    setSelectedTeams(agent.teams || []);
  }, [agent]);

  const shareWithTeam = async () => {
    try {
      setIsSaving(true);
      await AgentService.shareAgentWithTeams(agent.id, selectedTeams);
      setMadeChanges(false);
    } catch (e) {
      console.log(`Error sharing agent with teams`, e);
    } finally {
      setIsSaving(false);
      onOpenChange(false);
      onShareWithTeams?.();
    }
  };

  const _onOpenChange = () => {
    setSearch('');
    setMadeChanges(false);
    onOpenChange(false);
  };

  if (!agent) {
    return null;
  }

  return (
    <Sheet open={open} onOpenChange={_onOpenChange}>
      <SheetContent className="flex flex-col max-w-[90vw] md:max-w-[90vw] sm:max-w-[90vw]">
        <SheetHeader>
          <SheetTitle>
            Sharing agent: {agent.firstName} {agent.lastName}
          </SheetTitle>
        </SheetHeader>
        <div className="flex flex-row justify-between gap-6 overflow-y-auto">
          <Card className={cn('mt-4 flex-1 px-4 pt-2 pb-4')}>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-start items-center mb-6">
                <p className="font-semibold">Teams</p>
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin ml-4" />
                ) : null}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0 pr-4">
              {teams?.map((team) => (
                <TeamCard
                  key={team.id}
                  team={team}
                  selectable
                  onSelect={(team) => {
                    setMadeChanges(true);
                    setSelectedTeams((prev) => {
                      if (prev.includes(team.id)) {
                        return prev.filter((id) => id !== team.id);
                      }
                      return [...prev, team.id];
                    });
                  }}
                  selectedTeams={selectedTeams}
                />
              ))}
            </CardContent>
          </Card>
        </div>

        {authInfo?.accessHelper?.isAtLeastRole(
          org?.orgId as string,
          RoleEnum.ADMIN,
        ) ? (
          <Button
            onClick={() => shareWithTeam()}
            disabled={isPilotEnded || !madeChanges || isSaving}
          >
            {isSaving ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : null}
            {isSaving ? 'Saving...' : 'Save changes'}
          </Button>
        ) : null}
      </SheetContent>
    </Sheet>
  );
}

export default React.memo(TeamShareAgentPanel);
