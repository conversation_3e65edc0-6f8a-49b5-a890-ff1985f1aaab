import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { AgentDto } from '@/lib/Agent/types';
import React, { useRef } from 'react';

import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import AgentDropdownMenu from '@/components/BuyerCard/AgentDropdownMenu';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import useOrg from '@/hooks/useOrg';
import useUserSession from '@/hooks/useUserSession';
import AgentService from '@/lib/Agent';
import { AgentCallType } from '@/lib/Agent/types';
import { cn, timeAgo } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import {
  ChevronDown,
  ChevronUp,
  FileStack,
  Minus,
  PhoneIcon,
  Tag,
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { Id, toast } from 'react-toastify';
import AgentAvatar from '@/components/Avatars/Agent';
import EmotionBadge from '@/common/CreateBuyerForm/BotPreview/EmotionBadge';
import { SortParam } from '..';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import MultiPartyTooltipContent from './multipartyTooltipContext';

interface IListLayoutProps {
  agents: AgentDto[];
  onVariationsClick: (agent: AgentDto) => void;
  onStartCallClick: (agent: AgentDto) => void;
  onTagsClick: (agent: AgentDto) => void;
  selectMode: boolean;
  selectedAgents: AgentDto[];
  showAgentsTags: boolean;
  onLineClick: (agent: AgentDto) => void;
  onShareClick: (agent: AgentDto) => void;
  onShareWithTeam: (agent: AgentDto) => void;
  sorting: SortParam[];
  onSortingUpdated: (sortBy: SortParam[]) => void;
}

function ListLayout({
  agents,
  onVariationsClick,
  onStartCallClick,
  selectMode,
  selectedAgents,
  showAgentsTags,
  onLineClick,
  onShareClick,
  onShareWithTeam,
  sorting,
  onSortingUpdated,
}: IListLayoutProps) {
  const curSearchParams = useSearchParams();
  const { data: org } = useOrg();
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  const { isAdmin, allowAgentPublicToggle } = useUserSession();

  const toggleAgentPublicMutation = useMutation({
    mutationFn: AgentService.toggleAgentPublic,
    onSuccess: (agent) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error making the buyer public. Please try again.',
        );
      }
    },
  });

  const toggleAgentPublic = async (agentId: number) => {
    toggleAgentPublicMutation.mutate({
      id: agentId,
    });
  };

  const _onVariationsClick = (agent: AgentDto) => {
    if (onVariationsClick) {
      onVariationsClick(agent);
    }
  };

  const _onShareClick = (agent: AgentDto) => {
    if (onShareClick) {
      onShareClick(agent);
    }
  };

  const _onStartCallClick = (agent: AgentDto) => {
    if (onStartCallClick) {
      onStartCallClick(agent);
    }
  };

  const isFocusCalls = curSearchParams.get('callType') === AgentCallType.FOCUS;

  const _onLineClick = (agent: AgentDto) => {
    if (onLineClick) {
      onLineClick(agent);
    }
  };

  const selectedAgentsIds = selectedAgents.map((a) => a.id);
  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  const sortingByLastUpdatedTmp = sorting.find(
    (s) => s.category == 'lastUpdated',
  );
  let sortingByLastUpdated: string | undefined = undefined;
  if (sortingByLastUpdatedTmp) {
    sortingByLastUpdated = sortingByLastUpdatedTmp.asc ? 'asc' : 'desc';
  }

  const updateLastUpdatedSorting = () => {
    const tmp: SortParam[] = [];
    let found = false;
    for (const s of sorting) {
      if (s.category == 'lastUpdated') {
        if (s) {
          found = true;
          if (!s.asc) {
            tmp.push({ category: 'lastUpdated', asc: true });
          } else {
            //just skip it
          }
        } else {
          tmp.push({ category: 'lastUpdated', asc: false });
        }
      } else {
        tmp.push(s);
      }
    }
    if (!found) {
      tmp.push({ category: 'lastUpdated', asc: false });
    }
    onSortingUpdated(tmp);
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          {selectMode && <TableHead>&nbsp;</TableHead>}
          <TableHead>&nbsp;</TableHead>
          <TableHead>Name</TableHead>
          <TableHead>Call type</TableHead>
          <TableHead>Emotional state</TableHead>
          <TableHead>Created at</TableHead>
          <TableHead
            className="flex items-center cursor-pointer hover:text-black"
            onClick={updateLastUpdatedSorting}
          >
            Last updated
            {sortingByLastUpdated ? (
              sortingByLastUpdated == 'asc' ? (
                <ChevronUp size={14} className="ml-2" />
              ) : (
                <ChevronDown size={14} className="ml-2" />
              )
            ) : (
              <Minus size={14} className="ml-2" />
            )}
          </TableHead>
          <TableHead>Created by</TableHead>
          {showAgentsTags ? (
            <TableHead>Tags</TableHead>
          ) : (
            <TableHead>Variations</TableHead>
          )}
          {isFocusCalls && <TableHead>Opener line</TableHead>}
          {isAdmin && allowAgentPublicToggle && (
            <TableHead>Public for Demo</TableHead>
          )}
          {!selectMode && <TableHead>&nbsp;</TableHead>}
          {!selectMode && <TableHead>&nbsp;</TableHead>}
        </TableRow>
      </TableHeader>
      <TableBody>
        {agents.map((a) => {
          const hasVariations = a.variationParentAgentId === a.id;

          const createdAt = dayjs(a.createdAt).format('MMM D, YYYY');
          const updatedAt = timeAgo(new Date(a.updatedAt));

          return (
            <TableRow
              key={a.id}
              onClick={() => {
                _onLineClick(a);
              }}
              className={cn({
                'opacity-70 cursor-not-allowed pointer-events-none':
                  isPilotEnded,
              })}
            >
              {selectMode && (
                <TableCell>
                  <Checkbox
                    className="w-6 h-6"
                    checked={selectedAgentsIds.includes(a.id)}
                  />
                </TableCell>
              )}
              <TableCell>
                <div className="flex relative w-fit">
                  <AgentAvatar className="w-8 h-8" agent={a} />
                  {a.supportingAgentInfo &&
                    a.supportingAgentInfo?.length > 0 && (
                      <div className="absolute bottom-[-4px] right-[-4px] flex items-center justify-center text-[12px] bg-[#F4F4F5] text-[#71717A] rounded-full w-4 h-4 font-semibold border-2 border-white">
                        {a.supportingAgentInfo?.length}
                      </div>
                    )}
                </div>
              </TableCell>
              <TableCell>
                <div>
                  {a?.supportingAgentInfo && a?.supportingAgentInfo?.length > 0
                    ? `${a.scenarioName}`
                    : `${a.firstName} ${a.lastName}`}
                </div>
                <div className="text-muted-foreground text-xs">
                  {a?.supportingAgentInfo && a?.supportingAgentInfo?.length > 0
                    ? `${a.firstName} ${a.lastName} (Primary)`
                    : `${a.jobTitle} @  ${a.companyName}`}
                </div>
              </TableCell>
              <TableCell>
                {CALL_TYPE_OPTIONS.find((item) => item.value === a.callType)
                  ?.label ||
                  (a.callType === 'focus' ? 'Focus Call' : a.callType)}
              </TableCell>
              <TableCell>
                {a.emotionalState && a.emotionalState != 'none' && (
                  <div className="inline-flex rounded-full items-center text-nowrap text-xs">
                    {a?.supportingAgentInfo &&
                    a?.supportingAgentInfo?.length > 0 ? (
                      <TooltipProvider delayDuration={50}>
                        <Tooltip>
                          <TooltipTrigger className="">
                            <EmotionBadge emotionalState={'Multiple'} />
                          </TooltipTrigger>
                          <TooltipContent
                            side="bottom"
                            className="bg-white border border-[#E4E4E7] p-0 rounded-[6px]"
                          >
                            <MultiPartyTooltipContent agent={a} />
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <EmotionBadge emotionalState={a.emotionalState} />
                    )}
                  </div>
                )}
              </TableCell>
              <TableCell>{createdAt}</TableCell>
              <TableCell>{updatedAt}</TableCell>
              <TableCell>
                {a.owner?.firstName} {a.owner?.lastName}
              </TableCell>
              {showAgentsTags ? (
                <TableHead>
                  {a.tags?.map((tag) => (
                    <Badge
                      key={tag.id}
                      variant="default"
                      className="mt-1 mr-1 bg-teal-600"
                    >
                      <Tag size={12} className="mr-1" />
                      {tag.name}
                    </Badge>
                  ))}
                </TableHead>
              ) : (
                <TableCell>{a.variationName}</TableCell>
              )}
              {isFocusCalls && (
                <TableCell className="max-w-48">{a.openerLine}</TableCell>
              )}
              {isAdmin && allowAgentPublicToggle && (
                <TableCell className="text-center">
                  {!a.variationName && (
                    <Switch
                      checked={a.isPublic}
                      onCheckedChange={() => toggleAgentPublic(a.id)}
                      onClick={(e) => e.stopPropagation()}
                      disabled={toggleAgentPublicMutation.isPending}
                    />
                  )}
                </TableCell>
              )}
              {!selectMode && (
                <TableCell>
                  {hasVariations ? (
                    <div
                      className="rounded-lg items-center h-8 w-8 flex justify-center cursor-pointer text-white"
                      style={{
                        backgroundImage:
                          'linear-gradient(to right, #000000, #5189CE, #A168A2)',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        _onVariationsClick(a);
                      }}
                    >
                      <FileStack className="h-4 w-4" />
                    </div>
                  ) : (
                    <div
                      className="rounded-lg items-center h-8 w-8 flex justify-center cursor-pointer text-white"
                      style={{
                        backgroundImage:
                          'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        _onStartCallClick(a);
                      }}
                    >
                      <PhoneIcon className="h-4 w-4" />
                    </div>
                  )}
                </TableCell>
              )}
              {!selectMode && (
                <TableCell>
                  <AgentDropdownMenu
                    agent={a}
                    onVariationsClick={_onVariationsClick}
                    onShareClick={_onShareClick}
                    onShareWithTeam={onShareWithTeam}
                  />
                </TableCell>
              )}
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
}

export default React.memo(ListLayout);
