import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { AgentDto } from '@/lib/Agent/types';
import React, { useRef } from 'react';

import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import AgentDropdownMenu from '@/components/BuyerCard/AgentDropdownMenu';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import useOrg from '@/hooks/useOrg';
import useUserSession from '@/hooks/useUserSession';
import AgentService from '@/lib/Agent';
import { AgentCallType } from '@/lib/Agent/types';
import { cn, timeAgo } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { BrainIcon, FileStack, PhoneIcon, Tag } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { Id, toast } from 'react-toastify';
import AgentAvatar from '@/components/Avatars/Agent';

interface IListLayoutProps {
  agents: AgentDto[];
  onVariationsClick: (agent: AgentDto) => void;
  onStartCallClick: (agent: AgentDto) => void;
  onTagsClick: (agent: AgentDto) => void;
  selectMode: boolean;
  selectedAgents: AgentDto[];
  showAgentsTags: boolean;
  onLineClick: (agent: AgentDto) => void;
}
function ListLayout({
  agents,
  onVariationsClick,
  onStartCallClick,
  onTagsClick,
  selectMode,
  selectedAgents,
  showAgentsTags,
  onLineClick,
}: IListLayoutProps) {
  const curSearchParams = useSearchParams();
  const { data: org } = useOrg();
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  const { isAdmin, allowAgentPublicToggle } = useUserSession();

  const toggleAgentPublicMutation = useMutation({
    mutationFn: AgentService.toggleAgentPublic,
    onSuccess: (agent, params) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error making the buyer public. Please try again.',
        );
      }
    },
  });

  const toggleAgentPublic = async (agentId: number) => {
    toggleAgentPublicMutation.mutate({
      id: agentId,
    });
  };

  const _onVariationsClick = (agent: AgentDto) => {
    if (onVariationsClick) {
      onVariationsClick(agent);
    }
  };

  const _onTagsClick = (agent: AgentDto) => {
    if (onTagsClick) {
      onTagsClick(agent);
    }
  };

  const _onStartCallClick = (agent: AgentDto) => {
    if (onStartCallClick) {
      onStartCallClick(agent);
    }
  };

  const isFocusCalls = curSearchParams.get('callType') === AgentCallType.FOCUS;

  const _onLineClick = (agent: AgentDto) => {
    if (onLineClick) {
      onLineClick(agent);
    }
  };

  const selectedAgentsIds = selectedAgents.map((a) => a.id);
  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <Table>
      <TableHeader>
        <TableRow>
          {selectMode && <TableHead>&nbsp;</TableHead>}
          <TableHead>&nbsp;</TableHead>
          <TableHead>Name</TableHead>
          <TableHead>Call type</TableHead>
          <TableHead>Emotional state</TableHead>
          <TableHead>Created at</TableHead>
          <TableHead>Last updated</TableHead>
          <TableHead>Created by</TableHead>
          {showAgentsTags ? (
            <TableHead>Tags</TableHead>
          ) : (
            <TableHead>Variations</TableHead>
          )}
          {isFocusCalls && <TableHead>Opener line</TableHead>}
          {isAdmin && allowAgentPublicToggle && (
            <TableHead>Public for Demo</TableHead>
          )}
          {!selectMode && <TableHead>&nbsp;</TableHead>}
          {!selectMode && <TableHead>&nbsp;</TableHead>}
        </TableRow>
      </TableHeader>
      <TableBody>
        {agents.map((a) => {
          // console.log(a);
          const hasVariations =
            a.variationParentAgentId &&
            a.variationName &&
            a.variationName != '';

          const createdAt = dayjs(a.createdAt).format('MMM D, YYYY');
          const updatedAt = timeAgo(new Date(a.updatedAt));

          // console.log(a);
          return (
            <TableRow
              key={a.id}
              onClick={() => {
                _onLineClick(a);
              }}
              className={cn({
                'opacity-70 cursor-not-allowed pointer-events-none':
                  isPilotEnded,
              })}
            >
              {selectMode && (
                <TableCell>
                  <Checkbox
                    className="w-6 h-6"
                    checked={selectedAgentsIds.includes(a.id)}
                  />
                </TableCell>
              )}
              <TableCell>
                <AgentAvatar className="w-8 h-8" agent={a} />
              </TableCell>
              <TableCell>
                <div>
                  {a.firstName} {a.lastName}
                </div>
                <div className="text-muted-foreground text-xs">
                  {a.jobTitle} @ {a.companyName}
                </div>
              </TableCell>
              <TableCell>
                {CALL_TYPE_OPTIONS.find((item) => item.value === a.callType)
                  ?.label ||
                  (a.callType === 'focus' ? 'Focus Call' : a.callType)}
              </TableCell>
              <TableCell>
                {a.emotionalState && (
                  <div className="inline-flex rounded-full items-center bg-green-50 py-1 px-2 text-nowrap text-green-600 border border-green-300 text-xs">
                    <BrainIcon className="mr-1" size={14} />{' '}
                    {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                      (item) => item.value === a?.emotionalState,
                    )?.label ||
                      a.emotionalState ||
                      ''}
                  </div>
                )}
              </TableCell>
              <TableCell>{createdAt}</TableCell>
              <TableCell>{updatedAt}</TableCell>
              <TableCell>
                {a.owner?.firstName} {a.owner?.lastName}
              </TableCell>
              {showAgentsTags ? (
                <TableHead>
                  {a.tags?.map((tag) => (
                    <Badge
                      key={tag.id}
                      variant="default"
                      className="mt-1 mr-1 bg-teal-600"
                    >
                      <Tag size={12} className="mr-1" />
                      {tag.name}
                    </Badge>
                  ))}
                </TableHead>
              ) : (
                <TableCell>{a.variationName}</TableCell>
              )}
              {isFocusCalls && (
                <TableCell className="max-w-48">{a.openerLine}</TableCell>
              )}
              {isAdmin && allowAgentPublicToggle && (
                <TableCell className="text-center">
                  {!a.variationName && (
                    <Switch
                      checked={a.isPublic}
                      onCheckedChange={(c) => toggleAgentPublic(a.id)}
                      onClick={(e) => e.stopPropagation()}
                      disabled={toggleAgentPublicMutation.isPending}
                    />
                  )}
                </TableCell>
              )}
              {!selectMode && (
                <TableCell>
                  {hasVariations ? (
                    <div
                      className="rounded-lg items-center h-8 w-8 flex justify-center cursor-pointer text-white"
                      style={{
                        backgroundImage:
                          'linear-gradient(to right, #000000, #5189CE, #A168A2)',
                      }}
                      onClick={() => {
                        _onVariationsClick(a);
                      }}
                    >
                      <FileStack className="h-4 w-4" />
                    </div>
                  ) : (
                    <div
                      className="rounded-lg items-center h-8 w-8 flex justify-center cursor-pointer text-white"
                      style={{
                        backgroundImage:
                          'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                      }}
                      onClick={() => {
                        _onStartCallClick(a);
                      }}
                    >
                      <PhoneIcon className="h-4 w-4" />
                    </div>
                  )}
                </TableCell>
              )}
              {!selectMode && (
                <TableCell>
                  <AgentDropdownMenu
                    agent={a}
                    onVariationsClick={_onVariationsClick}
                  />
                </TableCell>
              )}
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
}

export default React.memo(ListLayout);
