import { AGENT_EMOTIONAL_STATE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import AgentAvatar from '@/components/Avatars/Agent';
import { AgentDto } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { BrainIcon } from 'lucide-react';

export interface IMultiPartyTooltipContent {
  agent: AgentDto;
}
export default function MultiPartyTooltipContent({
  agent,
}: IMultiPartyTooltipContent) {
  const emotionalStateOption = AGENT_EMOTIONAL_STATE_OPTIONS.find(
    (item) => item.value === agent.emotionalState,
  );
  const allAgents = [agent, ...(agent.supportingAgentInfo ?? [])];
  return allAgents.map((agent: AgentDto, index: number) => {
    return (
      <div
        className={cn('flex items-center px-2 py-[6px]', {
          'border-b border-[#E4E4E7 ]': index !== allAgents.length - 1,
        })}
        key={index}
      >
        <AgentAvatar
          className="w-6 h-6 mr-3"
          agent={agent}
          fallbackTextOverride={agent.firstName.charAt(0)}
        />
        <div className="flex flex-col gap-[2px]">
          <div className="text-[#09090B] text-xs font-medium leading-4">
            {index === 0
              ? `${agent.firstName} ${agent.lastName} (Primary)`
              : `${agent.firstName} ${agent.lastName}`}
          </div>
          <div className="flex gap-2 items-center">
            <div className="flex items-center">
              <div className=" flex items-center">
                <BrainIcon
                  className={cn(
                    'w-3 h-3 mr-1',
                    emotionalStateOption?.textColor,
                  )}
                />
              </div>
              <div
                className={cn(
                  'leading-tight text-nowrap truncate max-w-[200px] font-medium text-xs ',
                  emotionalStateOption?.textColor,
                )}
              >
                {emotionalStateOption?.label}
              </div>
            </div>

            <div className="text-[#71717A] text-xs leading-4">
              {agent.jobTitle}
            </div>
          </div>
        </div>
      </div>
    );
  });
}
