import React, { useEffect, use<PERSON>emo, useState } from 'react';
import { AgentDto, AgentStatus } from '@/lib/Agent/types';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import BuyerCard, { CardType } from '@/components/BuyerCard';
import { Button } from '@/components/ui/button';
import { RoleEnum } from '@/lib/User/types';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { Loader2 } from 'lucide-react';
import dayjs from 'dayjs';
import useUserSession from '@/hooks/useUserSession';
import AgentService from '@/lib/Agent';
import { useQueryClient } from '@tanstack/react-query';
import OrganizationCard from '@/components/OrganizationCard';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Card, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from '@/components/ui/card';
import { OrganizationDto } from '@/lib/Organization/types';

interface IBotSharingPanelProps {
  agent: AgentDto;
  open: boolean;
  onOpenChange: () => void;
}

function BotSharingPanel({ agent, open, onOpenChange }: IBotSharingPanelProps) {
  const authInfo = useAuthInfo();
  const org = useActiveOrg();
  const queryClient = useQueryClient();

  const { dbOrg, subOrganizations, sortedOrgs } = useUserSession();

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  const [madeChanges, setMadeChanges] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');

  const filteredSubOrganizations = useMemo(() => {
    const filtered = subOrganizations?.filter((org) => org.uid !== dbOrg?.uid);
    if (search) {
      return filtered?.filter((org) =>
        org.name.toLowerCase().includes(search.toLowerCase()),
      );
    }
    return filtered;
  }, [subOrganizations, dbOrg, search]);

  /***********************************/
  /***** SHARE AGENT WITH ORG  *******/
  /***********************************/

  const [orgsPerAgent, setOrgsPerAgent] = useState<{
    [key: string]: { selected: boolean; canEdit: boolean };
  }>({});

  useEffect(() => {
    const newOrgsPerAgent: {
      [key: string]: { selected: boolean; canEdit: boolean };
    } = {};
    agent.subAgents?.map((a) => {
      // If the variationParentAgentId = id, it means it's not a variation created in the sub org
      // We only want to take in consideration that were copied over to the sub org, and not the variations created from the copied over agent
      const isPrimarilySharedAgent =
        !a.variationParentAgentId || a.variationParentAgentId === a.id;

      if (a.status === AgentStatus.ACTIVE && isPrimarilySharedAgent) {
        newOrgsPerAgent[a.orgId] = {
          selected: true,
          canEdit: a.canOrgEditAgent,
        };
      }
    });
    setOrgsPerAgent(newOrgsPerAgent);
  }, [agent]);

  const orgsSharedWith = useMemo(() => {
    return filteredSubOrganizations?.filter(
      (org) => orgsPerAgent[org.id]?.selected,
    );
  }, [filteredSubOrganizations, orgsPerAgent]);

  const orgsNotSharedWith = useMemo(() => {
    return filteredSubOrganizations?.filter(
      (org) => !orgsPerAgent[org.id]?.selected,
    );
  }, [filteredSubOrganizations, orgsPerAgent]);

  const shareAgent = async () => {
    try {
      setIsSaving(true);
      await AgentService.bulkToggleShareAgentWithOrg(
        agent.id,
        Object.keys(orgsPerAgent).map((orgId) => ({
          orgUid:
            subOrganizations?.find((o) => o.id === Number(orgId))?.uid || '',
          canEditAgent: orgsPerAgent[orgId].canEdit,
        })),
        true,
      );
      setMadeChanges(false);
    } catch (e) {
      console.log(`Error sharing agent with orgs`, e);
    } finally {
      setIsSaving(false);
    }
    //not sure why but if I add the invalidateQueries to the same array, it wont invalidate
    queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
    queryClient.invalidateQueries({
      queryKey: ['orgAgentsByTagsAndVariations'],
    });
    queryClient.invalidateQueries({ queryKey: ['orgAgentVariations'] });
  };

  const _onCardClick = (subOrg: OrganizationDto) => {
    setMadeChanges(true);
    if (orgsPerAgent[subOrg.id]) {
      delete orgsPerAgent[subOrg.id];
    } else {
      orgsPerAgent[subOrg.id] = {
        selected: true,
        canEdit: false,
      };
    }

    setOrgsPerAgent({ ...orgsPerAgent });
  };

  const _onToggleCanEdit = (subOrg: OrganizationDto, canEdit: boolean) => {
    setMadeChanges(true);
    if (orgsPerAgent[subOrg.id]) {
      orgsPerAgent[subOrg.id] = {
        selected: true,
        canEdit: canEdit,
      };
    }

    setOrgsPerAgent({ ...orgsPerAgent });
  };

  const _onOpenChange = () => {
    setOrgsPerAgent({});
    setSearch('');
    setMadeChanges(false);
    onOpenChange();
  };

  if (!agent) {
    return null;
  }

  return (
    <Sheet open={open} onOpenChange={_onOpenChange}>
      <SheetContent className="flex flex-col max-w-[90vw] md:max-w-[90vw] sm:max-w-[90vw]">
        <SheetHeader>
          <SheetTitle>
            Agent: {agent.firstName} {agent.lastName}
          </SheetTitle>
          <BuyerCard
            key={agent.id}
            agent={agent}
            type={CardType.COMPACT}
            showMenu={false}
            showAgentsTags={false}
            showCallIcon={false}
            hideEditBtn={true}
            isSelected={false}
          />
        </SheetHeader>

        <p className="text-lg font-semibold text-foreground mt-4">
          Organizations
        </p>
        <Input
          placeholder="Search an organization"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pr-10 py-4"
        />
        <div className="flex flex-row justify-between gap-6 overflow-y-auto">
          <Card className={cn('mt-4 flex-1 px-4 pt-2 pb-4')}>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center mb-6">
                <p className="font-semibold">Not shared with</p>
                <Badge variant={'secondary'}>{orgsNotSharedWith?.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0 pr-4">
              {orgsNotSharedWith?.length ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {orgsNotSharedWith?.map((orgDetails) => (
                    <div key={orgDetails.id} className="relative">
                      <OrganizationCard
                        org={orgDetails}
                        orgMetadata={
                          sortedOrgs.find((org) => org.orgId === orgDetails.uid)
                            ?.orgMetadata || undefined
                        }
                        className={cn('transition-all duration-200')}
                        onClick={() => _onCardClick(orgDetails)}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-sm text-muted-foreground">
                  No organizations found
                </p>
              )}
            </CardContent>
          </Card>

          <Card className={cn('mt-4 flex-1 px-4 pt-2 pb-4')}>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center mb-6">
                <p className="font-semibold">Shared with</p>
                <Badge variant={'secondary'}>{orgsSharedWith?.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0 pr-4">
              {orgsSharedWith?.length ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {orgsSharedWith?.map((orgDetails) => (
                    <div key={orgDetails.id} className="relative">
                      <OrganizationCard
                        org={orgDetails}
                        orgMetadata={
                          sortedOrgs.find((org) => org.orgId === orgDetails.uid)
                            ?.orgMetadata || undefined
                        }
                        className={cn('transition-all duration-200')}
                        onClick={() => _onCardClick(orgDetails)}
                        onToggleCanEdit={(canEdit) =>
                          _onToggleCanEdit(orgDetails, canEdit)
                        }
                        canEdit={orgsPerAgent[orgDetails.id]?.canEdit}
                        showCanEdit
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-sm text-muted-foreground">
                  No organizations found
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {authInfo?.accessHelper?.isAtLeastRole(
          org?.orgId as string,
          RoleEnum.ADMIN,
        ) ? (
          <Button
            onClick={() => shareAgent()}
            disabled={isPilotEnded || !madeChanges || isSaving}
          >
            {isSaving ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : null}
            {isSaving ? 'Saving...' : 'Save changes'}
          </Button>
        ) : null}
      </SheetContent>
    </Sheet>
  );
}

export default React.memo(BotSharingPanel);
