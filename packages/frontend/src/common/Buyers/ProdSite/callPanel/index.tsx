import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  useLayoutEffect,
} from 'react';
import { AgentDto } from '@/lib/Agent/types';
import {
  Dialog,
  DialogPortal,
  DialogOverlay,
} from '@/components/ui/Hyperbound/dialog-for-calls';
import { Headphones, X } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import CallSimulationPanel from '@/components/CallSimulationPanel';
interface ICallPanelProps {
  agent: AgentDto;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function CallPanel({ open, agent, onOpenChange }: ICallPanelProps) {
  /***********************************/
  /************* FE EVENTS ***********/
  /***********************************/

  const [divAnimationCompleted, setDivAnimationCompleted] = useState(false);
  const [exitingPnl, setExitingPnl] = useState<boolean>(false);

  const closeDialog = () => {
    setExitingPnl(true);
    setTimeout(() => {
      onOpenChange(false);
    }, 160);
  };

  //ESC key to close dialog
  const escFunction = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' || event.key === 'Esc' || event.keyCode === 27) {
      closeDialog();
    }
  }, []);

  // MOUSE SCROLL - with position:fixed container, mouse wheel wont work, we need to use JS:

  useEffect(() => {
    document.addEventListener('keydown', escFunction, false);

    return () => {
      document.removeEventListener('keydown', escFunction, false);
    };
  }, [escFunction]);

  const scrollableContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener('wheel', scrollContent);

    return () => {
      //setPageStatus("profile");
      window.removeEventListener('wheel', scrollContent);
    };
  }, []);

  const scrollContent = (e: WheelEvent) => {
    if (scrollableContainer.current) {
      scrollableContainer.current.scrollTop += e.deltaY;
    }
  };

  useLayoutEffect(() => {
    function updateSize() {
      if (scrollableContainer.current) {
        const s = scrollableContainer.current.getBoundingClientRect();
        scrollableContainer.current.style.height =
          window.innerHeight - s.y - 1 + 'px';
      }
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  /***********************************/
  /************ RENDERING ************/
  /***********************************/

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogPortal>
        <DialogOverlay />
        <div className="fixed top-0 left-0 right-0 bottom-0 w-full h-full z-50 overflow-hidden">
          <div className="flex items-center">
            <div className="flex-grow"></div>
            <div className="text-sm text-white flex items-center">
              <Headphones size={18} className="mr-2" />
              Call with {agent.firstName} {agent.lastName}
            </div>
            <div className="flex-grow"></div>
            <div
              className="p-2 cursor-pointer text-white"
              onClick={closeDialog}
            >
              <X size={20} />
            </div>
          </div>
          <AnimatePresence>
            {!exitingPnl && (
              <motion.div
                initial={{ opacity: 0, y: 700 }}
                exit={{ opacity: 0, y: 700 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, ease: 'easeOut' }}
                className="flex flex-col h-screen"
                onAnimationComplete={() => {
                  if (scrollableContainer.current) {
                    const s =
                      scrollableContainer.current.getBoundingClientRect();
                    scrollableContainer.current.style.height =
                      window.innerHeight - s.y - 1 + 'px';
                  }

                  setDivAnimationCompleted(true);
                }}
                ref={scrollableContainer}
              >
                <CallSimulationPanel
                  agent={agent}
                  onAbort={closeDialog}
                  navigateToCallSummary={true}
                  showLeaderboardDateFilter={true}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </DialogPortal>
    </Dialog>
  );
}

export default CallPanel;
