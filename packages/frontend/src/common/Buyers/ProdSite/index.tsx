'use client';

import { useEffect, useRef, useState, useLayoutEffect } from 'react';
import DashboardNavbar from '@/common/DashboardNavbar';
import BuyerCard, { CardType } from '@/components/BuyerCard';
import { Button } from '@/components/ui/button';
import useOrgAgents from '@/hooks/useOrgAgents';
import {
  AgentCallType,
  AgentDto,
  AgentEmotionalState,
  AgentStatus,
  TagCondition,
} from '@/lib/Agent/types';
import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowDownToLine,
  CirclePlusIcon,
  DownloadIcon,
  Loader2Icon,
  Share2Icon,
} from 'lucide-react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import Filters from './filters';
import ListLayout from './listLayout';
import VariationsPanel from './variationsPanel';
import useOrgAgentsById from '@/hooks/useOrgAgentsById';
import useOrgAgentsFiltered from '@/hooks/useOrgAgentsFiltered';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import MultiactionsBar from './multiactionsBar';
import CallPanel from './callPanel';
import dayjs from 'dayjs';
import useOrg from '@/hooks/useOrg';
import { useAgent } from '@/hooks/useAgent';
import useUserSession from '@/hooks/useUserSession';
import { AppPermissions } from '@/lib/permissions';
import BotSharingPanel from './botSharingPanel';
import AgentService from '@/lib/Agent';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import BotTeamSharingPanel from './botTeamSharingPanel';
import { useQueryClient } from '@tanstack/react-query';

const AGENTS_LOAD_STEP = 20;

export enum AGENTS_LAYOUT {
  GRID = 'grid',
  LIST = 'list',
}

export interface SortParam {
  category: string;
  asc: boolean;
}

export class PageState {
  searchString: string = '';
  agentStatus: AgentStatus = AgentStatus.ACTIVE;
  callType: AgentCallType = AgentCallType.COLD;
  variations: string[] = [];
  tags: string[] = [];
  tagCondition: TagCondition = TagCondition.OR;
  emotionalState: AgentEmotionalState | undefined = undefined;
  sortBy: SortParam[] = [];
  teams: number[] = [];
}

export default function BuyersProdSite() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const curSearchParams = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { data: dbOrg } = useOrg();
  const queryClient = useQueryClient();

  const { canAccess, allBotsPage_showTagsForBots, isManagerHierarchyEnabled } =
    useUserSession();

  const pageRef = useRef<HTMLDivElement | null>(null);

  /**************************************/
  /********* INIT PAGE STATE ************/
  /**************************************/

  const ps = new PageState();
  let openAgentVapiId = '';

  if (curSearchParams) {
    if (curSearchParams.get('agentId')) {
      openAgentVapiId = curSearchParams.get('agentId') || '';
    }

    if (curSearchParams.get('search')) {
      ps.searchString = curSearchParams.get('search') || '';
    }

    if (curSearchParams.get('tagCondition')) {
      ps.tagCondition =
        TagCondition[
          (
            curSearchParams.get('tagCondition') || 'or'
          ).toUpperCase() as keyof typeof TagCondition
        ];
    }

    if (curSearchParams.get('callType')) {
      ps.callType = (curSearchParams.get('callType') as AgentCallType) || '';
    } else {
      curSearchParams.set('callType', ps.callType);
    }

    if (curSearchParams.get('emotionalState')) {
      ps.emotionalState =
        (curSearchParams.get('emotionalState') as AgentEmotionalState) ||
        undefined;
    } else {
      curSearchParams.delete('emotionalState');
    }

    if (curSearchParams.get('botStatus')) {
      ps.agentStatus =
        (curSearchParams.get('botStatus') as AgentStatus) || undefined;
    }

    if (curSearchParams.get('variations')) {
      if (curSearchParams.get('variations') != '') {
        ps.variations = curSearchParams.get('variations')?.split(',') || [];
      } else {
        ps.variations = [];
      }
    } else {
      ps.variations = [];
    }

    if (curSearchParams.get('tags')) {
      if (curSearchParams.get('tags') != '') {
        ps.tags = curSearchParams.get('tags')?.split(',') || [];
      } else {
        ps.tags = [];
      }
    } else {
      ps.tags = [];
    }

    if (curSearchParams.get('sortBy')) {
      const sortBy = curSearchParams.get('sortBy')?.split(',') || [];
      sortBy.map((s) => {
        const [category, asc] = s.split(':');
        ps.sortBy.push({ category, asc: asc == 'true' });
      });
    }

    if (curSearchParams.get('teams')) {
      ps.teams = curSearchParams.get('teams')?.split(',').map(Number) || [];
    }
  }

  const [numberOfResults, setNumberOfResults] =
    useState<number>(AGENTS_LOAD_STEP);

  /**************************************/
  /*********** LOAD AGENTS **************/
  /**************************************/

  const lastUpdatedSortingTmp = ps.sortBy.find(
    (s) => s.category == 'lastUpdated',
  );
  let lastUpdatedSorting: string | undefined = undefined;
  if (lastUpdatedSortingTmp) {
    lastUpdatedSorting = lastUpdatedSortingTmp.asc ? 'asc' : 'desc';
  }

  const {
    data: tmpAgents,
    isLoading: isLoadingAgents,
    isFetching: isFetchingAgents,
  } = useOrgAgents(
    ps.callType,
    true,
    0,
    numberOfResults,
    ps.searchString,
    ps.agentStatus,
    false,
    lastUpdatedSorting,
    ps.teams,
  );

  //fetch variations if needed
  let variationsIds: string[] = [];
  let tagsIds: string[] = [];
  let tagCondition: TagCondition = TagCondition.OR;

  let useComboFilterVariationsTags = false;
  if (
    (ps.tags &&
      ps.tags.length > 0 &&
      ps.variations &&
      ps.variations.length > 0) ||
    ps.emotionalState
  ) {
    useComboFilterVariationsTags = true;
  } else if (ps.tags && ps.tags.length > 0) {
    useComboFilterVariationsTags = true;
  }

  if (ps.variations && ps.variations.length > 0) {
    variationsIds = ps.variations;
  }

  if (ps.tags && ps.tags.length > 0) {
    tagsIds = ps.tags;
  }

  if (ps.tagCondition) {
    tagCondition = ps.tagCondition;
  }

  const {
    data: agentsWithVariations,
    isLoading: isLoadingAgentsWithVariations,
  } = useOrgAgentsById(
    variationsIds.map(Number),
    !useComboFilterVariationsTags,
  );

  const { data: agentsFiltered, isLoading: isLoadingAgentsFiltered } =
    useOrgAgentsFiltered(
      useComboFilterVariationsTags,
      ps.searchString,
      variationsIds.map(Number),
      tagsIds.map(Number),
      tagCondition,
      ps.callType,
      ps.emotionalState,
      ps.teams,
    );

  let agents: AgentDto[] | undefined = [],
    isLoadingAgnts = false;

  if (useComboFilterVariationsTags) {
    agents = agentsFiltered;
    isLoadingAgnts = isLoadingAgentsFiltered;
  } else if (variationsIds.length > 0) {
    agents = agentsWithVariations;
    isLoadingAgnts = isLoadingAgentsWithVariations;
  } else {
    agents = tmpAgents;
    isLoadingAgnts = isLoadingAgents;
  }

  //------------ MINOR SUPPORT FUNCTIONS -------//

  const [openVariations, setOpenVariations] = useState(false);
  const [openBotSharing, setOpenBotSharing] = useState(false);
  const [openBotTeamSharing, setOpenBotTeamSharing] = useState(false);
  const [isExportingSCORM, setIsExportingSCORM] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<AgentDto>();
  const openAgentVariationsPanel = (agent: AgentDto) => {
    setSelectedAgent(agent);
    setOpenVariations(true);
  };

  const openBotSharingPanel = (agent: AgentDto) => {
    setSelectedAgent(agent);
    setOpenBotSharing(true);
  };

  const openBotTeamSharingPanel = (agent: AgentDto) => {
    setSelectedAgent(agent);
    setOpenBotTeamSharing(true);
  };

  const loadMore = () => {
    setNumberOfResults((old) => old + AGENTS_LOAD_STEP);
  };

  const [showAgentsTags, setShowAgentsTags] = useState<boolean>(
    allBotsPage_showTagsForBots ? allBotsPage_showTagsForBots : false,
  );
  const toggleShowTags = () => {
    setShowAgentsTags(!showAgentsTags);
  };

  /**************************************/
  /*********** UPDATE FILTERS ***********/
  /**************************************/

  const updatePageState = (ps: PageState) => {
    if (ps.searchString) {
      curSearchParams.set('search', ps.searchString);
    } else {
      curSearchParams.delete('search');
    }

    if (ps.callType) {
      if (ps.callType == AgentCallType.COLD) {
        curSearchParams.delete('callType');
      } else {
        curSearchParams.set('callType', ps.callType);
      }
    } else {
      curSearchParams.delete('callType');
    }

    if (ps.agentStatus) {
      if (ps.agentStatus == AgentStatus.ACTIVE) {
        curSearchParams.delete('botStatus');
      } else {
        curSearchParams.set('botStatus', ps.agentStatus);
      }
    } else {
      curSearchParams.delete('botStatus');
    }

    if (ps.variations && ps.variations.length > 0) {
      curSearchParams.set('variations', ps.variations.join(','));
    } else {
      curSearchParams.delete('variations');
    }

    if (ps.tags && ps.tags.length > 0) {
      curSearchParams.set('tags', ps.tags.join(','));
    } else {
      curSearchParams.delete('tags');
    }

    if (ps.tagCondition) {
      curSearchParams.set('tagCondition', ps.tagCondition);
    } else {
      curSearchParams.delete('tagCondition');
    }

    if (ps.emotionalState) {
      curSearchParams.set('emotionalState', ps.emotionalState);
    } else {
      curSearchParams.delete('emotionalState');
    }

    if (ps.sortBy && ps.sortBy.length > 0) {
      const sortBy = ps.sortBy.map((s) => `${s.category}:${s.asc}`);
      curSearchParams.set('sortBy', sortBy.join(','));
    } else {
      curSearchParams.delete('sortBy');
    }

    if (ps.teams && ps.teams.length > 0) {
      curSearchParams.set('teams', ps.teams.join(','));
    } else {
      curSearchParams.delete('teams');
    }

    router.replace(`${pathname}?${curSearchParams.toString()}`);
  };

  const updateSorting = (sortBy: SortParam[]) => {
    ps.sortBy = sortBy;
    updatePageState(ps);
  };

  /**************************************/
  /******* CONTROL PAGE SCROLL **********/
  /**************************************/

  //NOT WORKING: the scrolling element is not the pageRef but some element way up in the node tree, need to refactor too much for now

  useLayoutEffect(() => {
    const el = pageRef.current;
    if (el) {
      el.addEventListener('scroll', onPageScroll);
    }

    return () => {
      if (el) {
        el.removeEventListener('scroll', onPageScroll);
      }
    };
  }, [pageRef.current]);

  const onPageScroll = (e: Event) => {
    const obj = e.target as HTMLElement;

    if (obj.scrollTop === obj.scrollHeight - obj.offsetHeight) {
      loadMore();
    }
  };

  /**************************************/
  /************ START CALL **************/
  /**************************************/

  const [isUserOnCall, setIsUserOnCall] = useState<boolean>(false);
  const { data: _forceCallAgent, isLoading: isLoadingAgentOnCall } =
    useAgent(openAgentVapiId);
  const [agentOnCall, setAgentOnCall] = useState<AgentDto>();

  useEffect(() => {
    if (!isLoadingAgentOnCall && _forceCallAgent) {
      startCall(_forceCallAgent);
    }
  }, [isLoadingAgentOnCall]);

  const startCall = (agent: AgentDto) => {
    //to open a panel instead of redirecting:
    // setOpenVariations(false);
    // setAgentOnCall(agent);
    // setIsUserOnCall(true);
    // curSearchParams.set("agentId", agent.vapiId);
    // router.replace(`${pathname}?${curSearchParams.toString()}`);
    const agentId = agent.providerAgentId
      ? agent.providerAgentId
      : agent.vapiId;
    router.push(`/buyers/${agentId}`);
  };

  const closeCallPanel = (o: boolean) => {
    setIsUserOnCall(o);
    setAgentOnCall(undefined);

    curSearchParams.delete('agentId');
    router.replace(`${pathname}?${curSearchParams.toString()}`);
  };

  /**************************************/
  /************** LAYOUT ****************/
  /**************************************/

  const [layout, setLayout] = useState<AGENTS_LAYOUT>(AGENTS_LAYOUT.LIST);
  const changePageLayout = (layout: AGENTS_LAYOUT) => {
    setLayout(layout);
  };

  /**************************************/
  /********* MULTI-EDITING **************/
  /**************************************/

  const [isMultiediting, setIsMultiediting] = useState<boolean>(false);
  const [selectedAgentsByIds, setSelectedAgentsByIds] = useState<{
    [key: number]: boolean;
  }>({});
  const [selectedAgents, setSelectedAgents] = useState<AgentDto[]>([]);

  const startEmptyMultiediting = () => {
    resetSelectedAgents();
    setIsMultiediting(true);
  };

  const startMultiediting = (agent: AgentDto) => {
    if (selectedAgentsByIds[agent.id]) {
      const tmp = selectedAgents.filter((a) => a.id != agent.id);
      if (tmp.length == 0) {
        resetSelectedAgents();
        //setIsMultiediting(false); //?????? should we turn editing off with the last toggle? it depends a lot on the actions available on the bar
      } else {
        setSelectedAgentsByIds((c) => {
          c[agent.id] = false;
          return { ...c };
        });
        setSelectedAgents([...tmp]);
      }
    } else {
      setSelectedAgentsByIds((c) => {
        c[agent.id] = true;
        return { ...c };
      });
      setSelectedAgents((c) => {
        return [agent, ...c];
      });
      setIsMultiediting(true);
    }
  };

  const stopMultiediting = () => {
    resetSelectedAgents();
    setIsMultiediting(false);
  };

  const resetSelectedAgents = () => {
    setSelectedAgents([]);
    setSelectedAgentsByIds({});
  };

  const clickOnCard = (agent: AgentDto) => {
    if (isMultiediting) {
      startMultiediting(agent);
    } else {
      if (agent.variationParentAgentId == agent.id) {
        openAgentVariationsPanel(agent);
      } else {
        startCall(agent);
      }
    }
  };

  const toggleSelectAll = () => {
    if (agents) {
      if (selectedAgents.length === agents.length) {
        // Deselect all
        resetSelectedAgents();
      } else {
        // Select all
        const newSelectedIds: { [key: number]: boolean } = {};
        agents.forEach((agent) => {
          newSelectedIds[agent.id] = true;
        });
        setSelectedAgentsByIds(newSelectedIds);
        setSelectedAgents(agents);
      }
    }
  };

  const exportSelectedBotsScormPackage = async () => {
    try {
      setIsExportingSCORM(true);
      await AgentService.bulkExportSCORMPackage(
        selectedAgents.map((a) => a.id),
      );
      setIsExportingSCORM(false);
    } catch (error) {
      console.error('Error exporting selected bots scorm package', error);
    }
  };

  const shareSelectedBotsWithOrgs = async () => {
    setOpenBotSharing(true);
  };

  const exportAllBotsScormPackage = async () => {
    try {
      setIsExportingSCORM(true);
      await AgentService.bulkExportSCORMPackage(agents?.map((a) => a.id) || []);
      setIsExportingSCORM(false);
    } catch (error) {
      console.error('Error exporting all bots scorm package', error);
    }
  };

  const onShareWithTeams = () => {
    setOpenBotTeamSharing(false);
    queryClient.invalidateQueries({
      predicate: (query) =>
        ['orgAgentsFiltered', 'orgAgents'].includes(
          query.queryKey[0] as string,
        ),
    });
  };

  /**************************************/
  /************** RENDER ****************/
  /**************************************/

  let showSeparator = false;
  let hasVariations = false;
  let hasBots = false;
  if (agents) {
    for (const a of agents) {
      if (a.variationParentAgentId == a.id) {
        hasVariations = true;
      } else {
        hasBots = true;
      }

      if (hasVariations && hasBots) {
        break;
      }
    }
  }

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <div className="w-full" ref={pageRef}>
      <DashboardNavbar
        breadcrumbs={[{ title: 'Buyer Bots' }]}
        rightContent={
          canAccess(AppPermissions.CREATE_BOT) && (
            <div className="flex">
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Button
                    onClick={() => {}}
                    variant="outline"
                    disabled={isPilotEnded}
                    className="mr-2"
                  >
                    <DownloadIcon className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={exportAllBotsScormPackage}
                  >
                    <Share2Icon className="w-4 h-4 mr-2" />
                    Export SCORM package
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                onClick={() => {
                  router.push(`/buyers/create/init`);
                }}
                variant={'default'}
                disabled={isPilotEnded}
              >
                <CirclePlusIcon className="w-4 h-4 mr-2" />
                Create new
              </Button>
            </div>
          )
        }
      />
      <Filters
        isShowingTags={showAgentsTags}
        toggleShowTags={toggleShowTags}
        filters={ps}
        onFiltersUpdated={updatePageState}
        onPageLayoutChange={changePageLayout}
        layout={layout}
        onStartMultiediting={startEmptyMultiediting}
        onStopMultiediting={stopMultiediting}
        isMultiEditing={isMultiediting}
        showCallTypeFilter={true}
        hideStatusFilter={!canAccess(AppPermissions.VIEW_ALL_BOTS)}
        isUpdating={isLoadingAgents || isFetchingAgents}
        showTeamsFilter={isManagerHierarchyEnabled}
      />

      <AnimatePresence>
        {isMultiediting && (
          <motion.div
            animate={{ height: 'auto', opacity: 1 }}
            initial={{ height: 0, opacity: 0 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{
              duration: 0.2,
              delay: 0,
            }}
          >
            <MultiactionsBar
              onClose={stopMultiediting}
              selectedAgents={selectedAgents}
              isShowingTags={showAgentsTags}
              toggleShowTags={toggleShowTags}
              forceShowTags={true}
              totalAgents={agents}
              onSelectAll={toggleSelectAll}
              onExportBots={exportSelectedBotsScormPackage}
              onShareWithOrgs={shareSelectedBotsWithOrgs}
              isExportingSCORM={isExportingSCORM}
              isSharingWithOrgs={openBotSharing}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/***************** EMPTY STATE ****************/}
      {agents?.length === 0 && isLoadingAgnts && (
        <div className="mx-4 my-8 font-semibold text-base">Loading...</div>
      )}

      {/***************** EMPTY STATE ****************/}
      {agents?.length === 0 && !isLoadingAgnts && (
        <div className="mx-4 my-8 font-semibold text-base">
          No buyers for{' '}
          {CALL_TYPE_OPTIONS.find((c) => c.value === ps.callType)?.label || ''}s
          found
        </div>
      )}

      {/***************** GRID LAYOUT ****************/}

      {layout == AGENTS_LAYOUT.GRID && hasVariations && (
        <div className="mx-4 text-base font-semibold mb-6">Variations</div>
      )}
      {
        //VARIATIONS FIRST:
        layout == AGENTS_LAYOUT.GRID && (
          <div className="flex flex-wrap gap-4 m-4 items-stretch">
            {agents?.map((a) => {
              if (a.variationParentAgentId == a.id) {
                showSeparator = true;
                return (
                  <motion.div
                    key={a.id}
                    animate={{ x: 0, opacity: 1 }}
                    initial={{ x: -20, opacity: 0 }}
                  >
                    <BuyerCard
                      key={a.id}
                      agent={a}
                      isSelected={selectedAgentsByIds[a.id]}
                      type={CardType.FULL}
                      onVariationsClick={openAgentVariationsPanel}
                      onShareClick={openBotSharingPanel}
                      onShareWithTeam={(a: AgentDto) =>
                        openBotTeamSharingPanel(a)
                      }
                      onStartCallClick={startCall}
                      onCardClick={clickOnCard}
                      selectMode={isMultiediting}
                      showAgentsTags={showAgentsTags}
                    />
                  </motion.div>
                );
              }
            })}
          </div>
        )
      }

      {layout == AGENTS_LAYOUT.GRID && showSeparator && (
        <div className="mt-8 mb-4 mx-4 flex justify-center"></div>
      )}

      {layout == AGENTS_LAYOUT.GRID && hasBots && (
        <div className="mx-4 text-base font-semibold mb-6">Individuals</div>
      )}

      {
        //BOTS:
        layout == AGENTS_LAYOUT.GRID && (
          <div className="flex flex-wrap gap-4 m-4 items-stretch mb-10">
            {agents?.map((a) => {
              if (a.variationParentAgentId != a.id) {
                return (
                  <motion.div
                    key={a.id}
                    animate={{ x: 0, opacity: 1 }}
                    initial={{ x: -20, opacity: 0 }}
                  >
                    <BuyerCard
                      key={a.id}
                      agent={a}
                      isSelected={selectedAgentsByIds[a.id]}
                      type={CardType.FULL}
                      onVariationsClick={openAgentVariationsPanel}
                      onShareClick={openBotSharingPanel}
                      onShareWithTeam={(a: AgentDto) =>
                        openBotTeamSharingPanel(a)
                      }
                      onStartCallClick={startCall}
                      onCardClick={clickOnCard}
                      selectMode={isMultiediting}
                      showAgentsTags={showAgentsTags}
                    />
                  </motion.div>
                );
              }
            })}
          </div>
        )
      }

      {/***************** LIST LAYOUT ****************/}

      {layout == AGENTS_LAYOUT.LIST && (
        <div className="flex flex-wrap gap-4 m-4">
          <motion.div
            animate={{ x: 0, opacity: 1 }}
            initial={{ x: -10, opacity: 0 }}
            className="w-full"
          >
            <ListLayout
              agents={agents || []}
              onVariationsClick={openAgentVariationsPanel}
              onShareClick={openBotSharingPanel}
              onShareWithTeam={(a: AgentDto) => openBotTeamSharingPanel(a)}
              onStartCallClick={startCall}
              onTagsClick={startMultiediting}
              selectMode={isMultiediting}
              selectedAgents={selectedAgents}
              showAgentsTags={showAgentsTags}
              onLineClick={clickOnCard}
              sorting={ps.sortBy}
              onSortingUpdated={updateSorting}
            />
          </motion.div>
        </div>
      )}

      {agents?.length == numberOfResults && (
        <div className="flex justify-center">
          <Button
            onClick={loadMore}
            className="m-4"
            variant="ghost"
            disabled={isLoadingAgents}
          >
            {isLoadingAgents ? (
              <Loader2Icon className="animate-spin mr-2" />
            ) : (
              <ArrowDownToLine className="mr-2" />
            )}
            Load More
          </Button>
        </div>
      )}

      {/* PANEL FOR VARIATIONS */}
      <VariationsPanel
        onCardClick={startCall}
        open={openVariations}
        onOpenChange={() => {
          setOpenVariations(false);
        }}
        agent={selectedAgent || ({} as AgentDto)}
      />

      <BotSharingPanel
        open={openBotSharing}
        onOpenChange={() => {
          setOpenBotSharing(false);
        }}
        agent={selectedAgent || ({} as AgentDto)}
        selectedAgents={selectedAgents}
      />

      <BotTeamSharingPanel
        open={openBotTeamSharing}
        onOpenChange={() => {
          setOpenBotTeamSharing(false);
        }}
        agent={selectedAgent || ({} as AgentDto)}
        onShareWithTeams={onShareWithTeams}
      />

      {/* CALL PANEL */}
      {agentOnCall && (
        <CallPanel
          key={'call-panel-' + agentOnCall?.id}
          open={isUserOnCall}
          agent={agentOnCall}
          onOpenChange={closeCallPanel}
        />
      )}
    </div>
  );
}
