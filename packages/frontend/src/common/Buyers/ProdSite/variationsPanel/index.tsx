import React, { useState } from 'react';
import { AgentDto } from '@/lib/Agent/types';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import BuyerCard, { CardType } from '@/components/BuyerCard';
import useOrgAgentVariations from '@/hooks/useOrgAgentVariations';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { RoleEnum } from '@/lib/User/types';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { EyeOff, Eye } from 'lucide-react';
import useOrg from '@/hooks/useOrg';
import dayjs from 'dayjs';

interface IVariationsPanelProps {
  agent: AgentDto;
  open: boolean;
  onOpenChange: () => void;
  onCardClick: (agent: AgentDto) => void;
}

function VariationsPanel({
  agent,
  open,
  onOpenChange,
  onCardClick,
}: IVariationsPanelProps) {
  const router = useRouter();
  const authInfo = useAuthInfo();
  const org = useActiveOrg();

  const { data: dbOrg } = useOrg();

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  const [showAgentTags, setShowAgentTags] = useState<boolean>(false);

  let fetchVariations = true;

  if (!agent || agent?.vapiId == '') {
    fetchVariations = false;
  }

  const { data: agentVariations } = useOrgAgentVariations(
    agent.vapiId,
    fetchVariations,
  );

  const _onCardClick = (agent: AgentDto) => {
    if (onCardClick) {
      onCardClick(agent);
    }
  };

  if (!agent) {
    return null;
  }

  const toggleShowTags = () => {
    setShowAgentTags((old) => !old);
  };
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="flex flex-col">
        <SheetHeader>
          <SheetTitle>
            <div>
              {agent.variationName
                ? `Variation: ` + agent.variationName
                : 'No variation found for:'}
            </div>
          </SheetTitle>
          <BuyerCard
            agent={agent}
            type={CardType.COMPACT}
            onCardClick={_onCardClick}
            showMenu={false}
            showCallIcon={true}
            showAgentsTags={showAgentTags}
          />
        </SheetHeader>
        {agentVariations?.length == 0 ? (
          authInfo?.accessHelper?.isAtLeastRole(
            org?.orgId as string,
            RoleEnum.ADMIN,
          ) ? (
            <Button
              onClick={() => {
                router.push(`/buyers/variations/${agent.vapiId}`);
              }}
              disabled={isPilotEnded}
            >
              Create variations
            </Button>
          ) : null
        ) : (
          <>
            <div className="flex">
              <div className="flex-1"></div>
              <Button variant="ghost" onClick={toggleShowTags}>
                {showAgentTags ? (
                  <div className="flex items-center">
                    <div className="w-6">
                      <EyeOff size="16" />
                    </div>
                    <div className="w-[70px]">Hide tags</div>
                  </div>
                ) : (
                  <div className="flex">
                    <div className="w-6 pt-[2px]">
                      <Eye size="16" />
                    </div>
                    <div className="w-[70px]">Show tags</div>
                  </div>
                )}
              </Button>
            </div>

            <ScrollArea className="grow">
              {agentVariations?.map((a) => {
                return (
                  <BuyerCard
                    key={a.id}
                    agent={a}
                    type={CardType.COMPACT}
                    className="mb-4"
                    onCardClick={_onCardClick}
                    showMenu={true}
                    showCallIcon={true}
                    showAgentsTags={showAgentTags}
                    hideEditBtn={true}
                  />
                );
              })}
            </ScrollArea>
            {authInfo?.accessHelper?.isAtLeastRole(
              org?.orgId as string,
              RoleEnum.ADMIN,
            ) && (
              <Button
                className="mt-4"
                onClick={() => {
                  router.push(`/buyers/variations/${agent.vapiId}`);
                }}
                disabled={isPilotEnded}
              >
                Edit variations
              </Button>
            )}
          </>
        )}
      </SheetContent>
    </Sheet>
  );
}

export default React.memo(VariationsPanel);
