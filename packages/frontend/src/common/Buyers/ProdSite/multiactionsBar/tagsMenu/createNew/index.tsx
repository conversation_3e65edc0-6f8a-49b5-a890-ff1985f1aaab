import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tag, Loader2Icon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { TagsService } from '@/lib/Agent';
import { Id, toast } from 'react-toastify';
import { TagDto } from '@/lib/Agent/types';

interface ICreateNewTagDialogProps {
  open: boolean;
  onClose: (t?: TagDto) => void;
}

function CreateNewTagDialog({ open, onClose }: ICreateNewTagDialogProps) {
  const queryClient = useQueryClient();
  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const errorToastId = useRef<Id | null>(null);

  const updateName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
  };

  const updateDescription = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescription(e.target.value);
  };

  const createTagMutation = useMutation({
    mutationFn: TagsService.createTag,
  });

  const save = async () => {
    if (name != '') {
      setIsSaving(true);

      let ok = false;
      let newTag: TagDto | undefined = undefined;
      try {
        newTag = await createTagMutation.mutateAsync({
          name,
          description,
        });
        ok = true;
      } catch (e) {
        if (!toast.isActive(errorToastId.current as Id)) {
          errorToastId.current = toast.error(
            'There was an error saving. Please try again.',
          );
        }
      }
      if (ok) {
        queryClient.invalidateQueries({ queryKey: ['agentsTags'] });
        setName('');
        setDescription('');
        setIsSaving(false);
        onClose(newTag);
      }
    }
  };

  //first div.onClick is needed to prevent the clicks from propagating to the BuyerCard triggerring the onCardClick event
  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      <Dialog
        open={open}
        onOpenChange={() => {
          onClose(undefined);
        }}
      >
        <DialogContent className="close-btn">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Tag className="w-5 h-5 mr-2" />
              New tag
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={name}
                className="col-span-3"
                onChange={updateName}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                value={description}
                className="col-span-3"
                onChange={updateDescription}
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={save} type="submit" disabled={isSaving}>
              {isSaving ? (
                <Loader2Icon className="animate-spin mx-1" />
              ) : (
                <>Save</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default React.memo(CreateNewTagDialog);
