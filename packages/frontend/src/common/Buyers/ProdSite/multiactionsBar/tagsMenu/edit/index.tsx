import { useState, useRef } from 'react';
import DialogFullScreen from '@/components/ui/Hyperbound/DialogFullScreen';
import {
  Tags,
  Loader2Icon,
  Trash2,
  Pencil,
  Save,
  CircleX,
  Minus,
} from 'lucide-react';
import useTags from '@/hooks/useTags';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { TagsService } from '@/lib/Agent';
import { Id, toast } from 'react-toastify';
import { Button } from '@/components/ui/button';
import { TagDto } from '@/lib/Agent/types';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import Header from '@/components/ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '@/components/ui/Hyperbound/DialogFullScreen/ScrollableContent';

interface IEditTagsProps {
  open: boolean;
  onClose: () => void;
}

export default function EditTags({ open, onClose }: IEditTagsProps) {
  const { data: allTags } = useTags(true, 0, 0, ''); //pagination + search works, not added cause Im not sure is needed for now

  const [editingTag, setEditingTag] = useState<TagDto>();
  const [deletingTag, setDeletingTag] = useState<TagDto>();

  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const errorToastId = useRef<Id | null>(null);

  const startEditTag = (tag: TagDto) => {
    cancelDelete();
    cancelEditing();
    setName(tag.name);
    setDescription(tag.description || '');
    setEditingTag(tag);
  };

  const startDeleteTag = (tag: TagDto) => {
    cancelDelete();
    cancelEditing();
    setDeletingTag(tag);
  };

  const updateName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
  };

  const updateDescription = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescription(e.target.value);
  };

  const cancelEditing = () => {
    setEditingTag(undefined);
    setName('');
    setDescription('');
  };

  const cancelDelete = () => {
    setDeletingTag(undefined);
  };

  const closeModal = () => {
    cancelDelete();
    cancelEditing();
    onClose();
  };

  /************************************/
  /********** SAVE TO DB **************/
  /************************************/

  const queryClient = useQueryClient();

  const updateTagMutation = useMutation({
    mutationFn: TagsService.updateTag,
  });

  const deleteTagMutation = useMutation({
    mutationFn: TagsService.deleteTag,
  });

  const performDelete = async () => {
    setIsSaving(true);
    let ok = false;
    try {
      await deleteTagMutation.mutateAsync(deletingTag?.id as number);
      ok = true;
    } catch (e) {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error deleting. Please try again.',
        );
      }
    }
    if (ok) {
      setIsSaving(false);
      cancelDelete();
      queryClient.invalidateQueries({
        queryKey: ['agentsTags'],
      });
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({
        queryKey: ['orgAgentsByTagsAndVariations'],
      });
    }
  };

  const saveEditedTag = async () => {
    if (name != '') {
      setIsSaving(true);

      let ok = false;
      try {
        const t = await updateTagMutation.mutateAsync({
          id: editingTag?.id as number,
          name,
          description,
        });
        ok = true;
      } catch (e) {
        if (!toast.isActive(errorToastId.current as Id)) {
          errorToastId.current = toast.error(
            'There was an error saving. Please try again.',
          );
        }
      }
      if (ok) {
        queryClient.invalidateQueries({ queryKey: ['agentsTags'] });
        queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
        queryClient.invalidateQueries({
          queryKey: ['orgAgentsByTagsAndVariations'],
        });
        setIsSaving(false);
        cancelEditing();
      }
    }
  };

  /************************************/
  /********** RENDERING ***************/
  /************************************/

  return (
    <DialogFullScreen open={open} onOpenChange={onClose}>
      <Header title={`Tags`} onClose={close} className="p-6" />

      <ScrollableContent className="px-6 pb-8 text-sm">
        <div className="mt-2 ">
          {allTags?.map((tag) => {
            let plural = 's';
            if (tag.numberOfAgents && tag.numberOfAgents == 1) {
              plural = '';
            }

            const isEditing = editingTag?.id == tag.id;
            const isDeleting = deletingTag?.id == tag.id;

            let className = isEditing ? 'bg-muted' : '';
            className = isDeleting ? 'bg-red-400' : className;

            return (
              <>
                <div
                  key={tag.id}
                  className={'mb-2 flex p-2 rounded' + className}
                >
                  <div className="mr-2">
                    {!isEditing ? (
                      tag.name
                    ) : (
                      <>
                        <Label htmlFor="name" className="text-right">
                          Name
                        </Label>
                        <Input
                          className="bg-white"
                          id="name"
                          value={name}
                          onChange={updateName}
                        />
                      </>
                    )}
                  </div>
                  <div className="mr-2">
                    {!isEditing && (
                      <Badge variant={'secondary'}>
                        {tag.numberOfAgents}&nbsp;agent{plural}
                      </Badge>
                    )}
                  </div>
                  <div className="flex-1 mr-2">
                    {!isEditing ? (
                      tag.description
                    ) : (
                      <>
                        <Label htmlFor="description" className="text-right">
                          Description
                        </Label>
                        <Textarea
                          className="bg-white"
                          id="description"
                          value={description}
                          onChange={updateDescription}
                        />
                      </>
                    )}
                  </div>
                  <div>
                    {isDeleting && (
                      <div className="flex items-center">
                        <div className="mr-2">
                          Are you sure you want to delete this tag?
                        </div>
                        <Button
                          className="mr-2"
                          variant="outline"
                          onClick={cancelDelete}
                        >
                          No
                        </Button>
                        <Button
                          disabled={isSaving}
                          variant="destructive"
                          onClick={performDelete}
                        >
                          {isSaving ? (
                            <Loader2Icon className="animate-spin" />
                          ) : (
                            'Yes'
                          )}
                        </Button>
                      </div>
                    )}
                    {isDeleting || isEditing ? (
                      ''
                    ) : (
                      <>
                        <Button
                          variant="ghost"
                          onClick={() => {
                            startEditTag(tag);
                          }}
                        >
                          <Pencil size="16" />
                        </Button>
                        <Button
                          variant="ghost"
                          onClick={() => {
                            startDeleteTag(tag);
                          }}
                        >
                          <Trash2 size="16" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                {isEditing && (
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={cancelEditing}>
                      Cancel
                    </Button>
                    <Button disabled={isSaving} onClick={saveEditedTag}>
                      {isSaving ? (
                        <Loader2Icon className="animate-spin" />
                      ) : (
                        'Save'
                      )}
                    </Button>
                  </div>
                )}
              </>
            );
          })}
        </div>
      </ScrollableContent>

      <div className="flex items-center">
        <div className="flex-1" />
        <div className="flex items-center p-6">
          <Button variant="outline" onClick={closeModal}>
            Close
          </Button>
        </div>
      </div>
    </DialogFullScreen>
  );
}
