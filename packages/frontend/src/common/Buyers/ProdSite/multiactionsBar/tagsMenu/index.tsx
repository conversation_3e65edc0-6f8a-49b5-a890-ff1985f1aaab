import React, { useState, useRef, useEffect } from 'react';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tags,
  CirclePlus,
  Pencil,
  CheckIcon,
  SquareCheck,
  Minus,
  TagsIcon,
  PlusIcon,
  PencilIcon,
  Loader2Icon,
} from 'lucide-react';
import { AgentDto, TagDto } from '@/lib/Agent/types';
import useTags from '@/hooks/useTags';
import CreateNewTagDialog from './createNew';
import { cn } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { TagsService } from '@/lib/Agent';
import { Id, toast } from 'react-toastify';
import EditTags from './edit';
import { Button } from '@/components/ui/button';

interface ITagsMenuProps {
  selectedAgents: AgentDto[];
  onClose?: () => void;
}

type NewTagsConfiguration = {
  [key: number]: {
    add: boolean;
    remove: boolean;
    ignore: boolean;
    fromAgents: boolean;
  };
};

function TagsMenu({ selectedAgents, onClose }: ITagsMenuProps) {
  const [open, setOpen] = useState(false);
  const [isCreatenewDialogOpen, setIsCreatenewDialogOpen] =
    useState<boolean>(false);
  const [isEditTagsOpen, setIsEditTagsOpen] = useState<boolean>(false);
  const [newTagsToApply, setNewTagsToApply] = useState<NewTagsConfiguration>(
    {},
  ); //this stores the list of tags we are going to newly apply
  const [tagsNotFromAgents, setTagsNotFromAgents] = useState<number[]>([]); //this stores the list of tags that are not coming from the agents
  const [canSave, setCanSave] = useState(false);

  /**************************************/
  /********** INIT COMPONENT ************/
  /**************************************/

  const { data: allTagsFromDB } = useTags(true, 0, 0, ''); //pagination + search works, not added cause Im not sure is needed for now

  const [allTags, setAllTags] = useState<TagDto[]>([]);
  const [dbTags, setDbTags] = useState<TagDto[]>([]);

  useEffect(() => {
    //************
    //calculate all the tags currently selected and how they are applied:
    //************

    const selectedTags: TagDto[] = [];
    const selectedTagsStatus: {
      [key: number]: { appliedToNumbOfAgents: number; appliedToAll: boolean };
    } = {}; //this stores info regarding the current tags of the agents
    selectedAgents?.map((a) => {
      //console.log(a.tags);
      (a.tags || []).map((t) => {
        if (!selectedTagsStatus[t.id]) {
          selectedTagsStatus[t.id] = {
            appliedToNumbOfAgents: 1,
            appliedToAll: false,
          };
          selectedTags.push(t);
        } else {
          selectedTagsStatus[t.id].appliedToNumbOfAgents++;
        }
      });
    });

    const _newTagsToApply: NewTagsConfiguration = {};

    Object.keys(selectedTagsStatus).map((k) => {
      if (!_newTagsToApply[Number(k)]) {
        _newTagsToApply[Number(k)] = {
          add: false,
          remove: false,
          ignore: true,
          fromAgents: true,
        };
      }

      //console.log(selectedTagsStatus[Number(k)].appliedToNumbOfAgents, selectedAgents.length);
      if (
        selectedTagsStatus[Number(k)].appliedToNumbOfAgents ==
        selectedAgents.length
      ) {
        _newTagsToApply[Number(k)].add = true;
        _newTagsToApply[Number(k)].ignore = false;
      } else {
        _newTagsToApply[Number(k)].add = false;
        _newTagsToApply[Number(k)].ignore = true;
      }
    });

    tagsNotFromAgents.map((tid) => {
      let state = {
        add: true,
        remove: false,
        ignore: false,
        fromAgents: false,
      };
      if (_newTagsToApply[tid]) {
        //tag is now coming from a newly selected agents
        state = _newTagsToApply[tid];
      }
      _newTagsToApply[tid] = state;
    });

    //console.log(selectedAgents.length, selectedTagsStatus, _newTagsToApply);

    /******************************
     * COMBINE tags in allTags in such a way that on top there are the tags that belong to the selected agents
     *****************************/
    const _allTags: TagDto[] = selectedTags;
    allTagsFromDB?.map((t) => {
      if (!selectedTagsStatus[t.id]) {
        _allTags.push(t);
      }
    });

    setAllTags([..._allTags]);
    setDbTags([..._allTags]);
    setNewTagsToApply({ ..._newTagsToApply });
  }, [selectedAgents, allTagsFromDB]);

  /**************************************/
  /********** FILTER TAGS ***************/
  /**************************************/

  const [searchString, setSearchString] = useState<string>('');

  const filterTags = async (s: string) => {
    if (s == '') {
      setAllTags([...dbTags]);
    } else {
      const tmp = dbTags.filter((t) => {
        return t.name.toLowerCase().includes(s.toLowerCase());
      });
      setAllTags([...tmp]);
    }
    setSearchString(s);
  };

  /**************************************/
  /********** TOGGLE TAGS ***************/
  /**************************************/

  /*
    for tags coming from the agents, the loop is add -> remove -> ingore -> add .....
    for all other tags: add -> remove -> add ...
  */

  const toggleTag = (tid: string) => {
    const tagId = Number(tid);
    let currentStatus = newTagsToApply[tagId];

    if (!currentStatus) {
      currentStatus = {
        add: true,
        remove: false,
        ignore: false,
        fromAgents: false,
      };
    } else if (currentStatus.add) {
      currentStatus = {
        add: false,
        remove: true,
        ignore: false,
        fromAgents: currentStatus.fromAgents,
      };
    } else if (currentStatus.remove) {
      if (currentStatus.fromAgents) {
        currentStatus = {
          add: false,
          remove: false,
          ignore: true,
          fromAgents: currentStatus.fromAgents,
        };
      } else {
        currentStatus = {
          add: true,
          remove: false,
          ignore: false,
          fromAgents: currentStatus.fromAgents,
        };
      }
    } else if (currentStatus.ignore) {
      currentStatus = {
        add: true,
        remove: false,
        ignore: false,
        fromAgents: currentStatus.fromAgents,
      };
    }

    if (!currentStatus.fromAgents) {
      if (currentStatus.add) {
        setTagsNotFromAgents([...tagsNotFromAgents, tagId]);
      } else {
        setTagsNotFromAgents((old) => old.filter((t) => t != tagId));
      }
    }

    setNewTagsToApply({ ...newTagsToApply, [tagId]: currentStatus });

    setCanSave(true);
  };

  /**************************************/
  /********** SAVE TO DB ****************/
  /**************************************/

  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  const [isApplyingTags, setIsApplyingTags] = useState(false);

  const saveToDb = async () => {
    setIsApplyingTags(true);
    const forAgents: number[] = [];
    selectedAgents.map((a) => {
      forAgents.push(a.id);
    });

    const addTags: number[] = [];
    const removeTags: number[] = [];

    Object.keys(newTagsToApply).map((tid) => {
      const s = newTagsToApply[Number(tid)];
      if (!s.ignore) {
        if (s.add) {
          addTags.push(Number(tid));
        } else if (s.remove) {
          removeTags.push(Number(tid));
        }
      }
    });

    //console.log(forAgents, addTags, removeTags);

    let ok = false;
    try {
      const newTag = await TagsService.editTagsAgentsLinks({
        forAgents,
        addTags,
        removeTags,
      });
      ok = true;
    } catch (e) {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving. Please try again.',
        );
      }
    }

    setIsApplyingTags(false);

    if (ok) {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({
        queryKey: ['orgAgentsByTagsAndVariations'],
      });
      setOpen(false);
      if (onClose) {
        onClose();
      }
    }
  };

  return (
    <>
      <Popover
        open={open}
        onOpenChange={(o) => {
          setOpen(o);
        }}
      >
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            role="combobox"
            aria-expanded={open}
            className="rounded-full p-4"
          >
            <TagsIcon className="h-5 w-5" />
          </Button>
        </PopoverTrigger>
        <PopoverContent align="start" className="w-[200px] p-0">
          <Command filter={(value, search) => 1}>
            <CommandInput
              placeholder="Search tags..."
              className="h-9"
              value={searchString}
              onValueChange={filterTags}
            />
            <CommandList>
              <CommandGroup heading="Tags">
                {allTags?.map((t) => {
                  let checked = false;
                  if (newTagsToApply[t.id] && newTagsToApply[t.id].remove) {
                    checked = true;
                  } else if (!newTagsToApply[t.id]) {
                    checked = true;
                  }
                  return (
                    <CommandItem
                      key={t.id}
                      value={String(t.id)}
                      onSelect={toggleTag}
                    >
                      <div
                        className={cn(
                          'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                          checked
                            ? 'opacity-50 [&_svg]:invisible'
                            : 'bg-primary text-primary-foreground',
                        )}
                      >
                        {newTagsToApply[t.id]?.add && (
                          <CheckIcon className="h-4 w-4" />
                        )}

                        {newTagsToApply[t.id]?.ignore && (
                          <Minus className="h-4 w-4" />
                        )}
                      </div>

                      <div className="flex space-x-2 items-center">
                        <div className="capitalize">{t.name}</div>
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
            <CommandSeparator />
            <CommandGroup>
              <CommandItem
                className="justify-center text-center hover:cursor-pointer"
                onSelect={() => {
                  setIsCreatenewDialogOpen(true);
                }}
              >
                <PlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>New tag</span>
              </CommandItem>
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup>
              <CommandItem
                className="justify-center text-center hover:cursor-pointer"
                onSelect={() => {
                  setIsEditTagsOpen(true);
                }}
              >
                <PencilIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Edit tags</span>
              </CommandItem>
            </CommandGroup>
            {canSave && (
              <React.Fragment>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    className="justify-center text-center"
                    onSelect={saveToDb}
                    disabled={isApplyingTags}
                  >
                    {isApplyingTags ? (
                      <Loader2Icon className="animate-spin" size={14} />
                    ) : (
                      <SquareCheck size="14" />
                    )}
                    &nbsp;Apply
                  </CommandItem>
                </CommandGroup>
              </React.Fragment>
            )}
          </Command>
        </PopoverContent>
      </Popover>
      <CreateNewTagDialog
        open={isCreatenewDialogOpen}
        onClose={() => {
          setIsCreatenewDialogOpen(false);
          setOpen(true);
        }}
      />
      <EditTags
        open={isEditTagsOpen}
        onClose={() => {
          setIsEditTagsOpen(false);
          setOpen(true);
        }}
      />
    </>
  );
}

export default React.memo(TagsMenu);
