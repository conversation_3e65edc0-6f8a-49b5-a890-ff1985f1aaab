import React, { useState, useEffect } from 'react';
import { X, DownloadIcon, Loader2, CornerUpRight } from 'lucide-react';
import { AgentDto } from '@/lib/Agent/types';
import TagsMenu from './tagsMenu';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface IMultiactionsBarProps {
  onClose?: () => void;
  selectedAgents: AgentDto[];
  isShowingTags: boolean;
  toggleShowTags: () => void;
  forceShowTags?: boolean;
  totalAgents?: AgentDto[];
  onSelectAll?: (checked: boolean) => void;
  onExportBots?: () => void;
  isExportingSCORM?: boolean;
  onShareWithOrgs?: () => void;
  isSharingWithOrgs?: boolean;
}

function MultiactionsBar({
  onClose,
  selectedAgents,
  isShowingTags,
  toggleShowTags,
  forceShowTags,
  totalAgents,
  onSelectAll,
  onExportBots,
  isExportingSCORM,
  onShareWithOrgs,
  isSharingWithOrgs,
}: IMultiactionsBarProps) {
  const [initiallyShowingTags, setInitiallyShowingTags] = useState<boolean>();

  useEffect(() => {
    setInitiallyShowingTags(isShowingTags);

    if (forceShowTags) {
      if (!isShowingTags) {
        toggleShowTags();
      }
    }
  }, []);

  const _onClose = () => {
    if (onClose) {
      if (!initiallyShowingTags && forceShowTags) {
        if (isShowingTags) {
          toggleShowTags();
        }
      }
      onClose();
    }
  };

  const handleExportBots = () => {
    onExportBots?.();
  };

  return (
    <div className="flex mx-4 mb-6 rounded-full pl-2 pr-4 py-1 items-center border bg-popover text-popover-foreground shadow-sm">
      <Button
        variant="ghost"
        className="cursor-pointer rounded-full m-1 p-1 w-6 h-6"
        onClick={_onClose}
      >
        <X className="text-black w-4 h-4" />
      </Button>

      <div className="flex items-center gap-3 ml-2">
        <Checkbox
          checked={
            totalAgents &&
            totalAgents.length > 0 &&
            selectedAgents.length === totalAgents.length
          }
          onCheckedChange={(checked) => onSelectAll?.(!!checked)}
          className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
        />
        <span className="text-sm">
          {selectedAgents.length === totalAgents?.length
            ? 'Deselect all'
            : 'Select all'}
          <span className="text-muted-foreground ml-1">
            ({selectedAgents.length}/{totalAgents?.length || 0})
          </span>
        </span>
      </div>

      <Separator orientation="vertical" className="mx-4 h-5" />

      <div className="flex items-center gap-2">
        <TagsMenu selectedAgents={selectedAgents} onClose={_onClose} />

        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild className="">
              <Button
                variant="ghost"
                className="flex"
                onClick={handleExportBots}
                disabled={isExportingSCORM}
              >
                {isExportingSCORM ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <DownloadIcon size="18" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Export SCORM package</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild className="">
              <Button
                variant="ghost"
                className="flex"
                onClick={() => {
                  onShareWithOrgs?.();
                }}
                disabled={isSharingWithOrgs}
              >
                {isSharingWithOrgs ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <CornerUpRight size="18" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Share with organizations...</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="flex-1" />
    </div>
  );
}

export default React.memo(MultiactionsBar);
