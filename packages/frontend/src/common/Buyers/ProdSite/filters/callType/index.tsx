import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import useAgentStatsByCallType from '@/hooks/useAgentStatsByCallType';
import { AgentCallType } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { ChevronDown, X } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface IProps {
  current: AgentCallType[];
  onCallTypesUpdated: (callTypes: AgentCallType[]) => void;
}

export default function CallTypeFilter({
  current,
  onCallTypesUpdated,
}: IProps) {
  const { data: stats, isLoading: isLoadingStats } = useAgentStatsByCallType();

  const toggleCallType = (callType: AgentCallType) => {
    const tmp = [];
    if (current.find((val) => val === callType)) {
      tmp.push(...current.filter((val) => val !== callType));
    } else {
      tmp.push(...current);
      tmp.push(callType);
    }

    if (onCallTypesUpdated) {
      onCallTypesUpdated(tmp);
    }
  };

  return (
    <div className="flex items-center">
      {CALL_TYPE_OPTIONS.map((ct, i) => {
        if (i < 5) {
          if (ct.value === AgentCallType.GATEKEEPER) return null;
          const Icon =
            CALL_TYPE_TO_ICON?.[ct.value as keyof typeof CALL_TYPE_TO_ICON]
              ?.Icon;
          const selected = current?.includes(ct.value);
          return (
            <div
              key={ct.value}
              onClick={() => toggleCallType(ct.value)}
              className={cn(
                'flex items-center border p-2 rounded-lg mr-2 cursor-pointer flex-1 overflow-hidden ',
                {
                  'bg-black text-white hover:bg-black/80': selected,
                  'hover:bg-gray-100': !selected,
                },
              )}
            >
              <div className="mr-2">{Icon && <Icon className="h-4 w-4" />}</div>
              <div className="capitalize flex-1 text-nowrap mr-1">
                {ct.label.replaceAll('Call', 'Bots ')}
              </div>
              <div
                className={cn('text-muted-foreground text-sm text-nowrap', {
                  'text-white': selected,
                })}
              >
                {stats?.[ct.value] || '0'} bot
                {stats?.[ct.value]?.length != 1 && 's'}
              </div>
            </div>
          );
        }
      })}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center border rounded-lg cursor-pointer overflow-hidden text-nowrap px-3 py-2">
            All types <ChevronDown size={16} className="ml-2" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {CALL_TYPE_OPTIONS?.map((ct, i: number) => {
            if (ct.value === AgentCallType.GATEKEEPER) return null;
            const Icon =
              CALL_TYPE_TO_ICON?.[ct.value as keyof typeof CALL_TYPE_TO_ICON]
                ?.Icon;
            const selected = current?.includes(ct.value);
            return (
              <DropdownMenuItem
                className={'p-0 mb-1 rounded-lg'}
                onClick={() => {}}
                key={'pl-' + ct.value}
              >
                <div
                  key={'pl-' + ct.value}
                  className={cn(
                    'flex items-center w-full  rounded-lg px-2 py-1',
                    { 'bg-black text-white': selected },
                  )}
                  onClick={() => {
                    toggleCallType(ct.value);
                  }}
                >
                  <div className="flex-1 mr-6">
                    {ct.label.replaceAll('Call', 'Bots ')}
                  </div>
                  <div
                    className={cn('text-muted-foreground text-sm text-nowrap', {
                      'text-white': selected,
                    })}
                  >
                    {stats?.[ct.value] || '0'} bot
                    {stats?.[ct.value]?.length != 1 && 's'}
                  </div>
                </div>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
