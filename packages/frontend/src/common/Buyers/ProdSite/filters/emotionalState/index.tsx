import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_TO_ICON,
} from '@/common/CreateBuyerForm/constants';
import useAgentStatsByCallType from '@/hooks/useAgentStatsByCallType';
import { AgentEmotionalState } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { ChevronDown, X, Brain } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface IProps {
  current: AgentEmotionalState | undefined;
  onEmotionalStateUpdated: (
    emotionalState: AgentEmotionalState | undefined,
  ) => void;
}

export default function EmotionalStateFilter({
  current,
  onEmotionalStateUpdated,
}: IProps) {
  const toggleEmotionalState = (
    emotionalState: AgentEmotionalState | undefined,
  ) => {
    if (onEmotionalStateUpdated) {
      onEmotionalStateUpdated(emotionalState);
    }
  };

  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center border rounded-md cursor-pointer overflow-hidden text-nowrap px-2 py-1.5 shadow-sm">
            <Brain className="mr-2 h-4 w-4" />
            {AGENT_EMOTIONAL_STATE_OPTIONS.find((es) => es.value === current)
              ?.label ?? 'All emotional states'}{' '}
            <ChevronDown size={16} className="ml-2" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            className={'p-0 mb-1 rounded-lg'}
            onClick={() => {
              toggleEmotionalState(undefined);
            }}
            key={'pl-all'}
          >
            <div
              key={'pl-all'}
              className={cn('flex items-center w-full  rounded-lg px-2 py-1', {
                'bg-black text-white': current === undefined,
              })}
              onClick={() => {
                toggleEmotionalState(undefined);
              }}
            >
              <div className="flex-1 mr-6">All emotional states</div>
            </div>
          </DropdownMenuItem>
          {AGENT_EMOTIONAL_STATE_OPTIONS?.map((es, i: number) => {
            const selected = current === es.value;
            return (
              <DropdownMenuItem
                className={'p-0 mb-1 rounded-lg'}
                onClick={() => {}}
                key={'pl-' + es.value}
              >
                <div
                  key={'pl-' + es.value}
                  className={cn(
                    'flex items-center w-full  rounded-lg px-2 py-1',
                    { 'bg-black text-white': selected },
                  )}
                  onClick={() => {
                    toggleEmotionalState(es.value);
                  }}
                >
                  <div className="flex-1 mr-6">{es.label}</div>
                </div>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
