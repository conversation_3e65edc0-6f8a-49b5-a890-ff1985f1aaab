import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  AgentCallType,
  AgentEmotionalState,
  AgentStatus,
  TagCondition,
} from '@/lib/Agent/types';
import {
  AlignJustify,
  LayoutGrid,
  EyeOff,
  Eye,
  Loader2,
  CheckIcon,
  SquareIcon,
} from 'lucide-react';
import { useRef, useState } from 'react';
import { AGENTS_LAYOUT, PageState } from '..';
import { cn } from '@/lib/utils';
import VariationsFilter from './variations';
import TagsFilter from './tags';
import { Button } from '@/components/ui/button';
import CallTypeFilterPanel from './callType';
import EmotionalStateFilter from './emotionalState';
interface IFiltersProps {
  filters: PageState;
  onFiltersUpdated: (filters: PageState) => void;
  onPageLayoutChange?: (layout: AGENTS_LAYOUT) => void;
  onStartMultiediting?: () => void;
  onStopMultiediting?: () => void;
  isMultiEditing?: boolean;
  layout?: AGENTS_LAYOUT;
  toggleShowTags: () => void;
  isShowingTags: boolean;
  hideAddNewTagBtn?: boolean;
  hideStatusFilter?: boolean;
  showCallTypeFilter?: boolean;
  onOpenFavFolderLayout?: () => void;
  onAddFolderFolderLayout?: () => void;
  disabled?: boolean;
  isUpdating?: boolean;
}

export default function Filters({
  filters,
  onFiltersUpdated,
  onPageLayoutChange,
  layout,
  onStartMultiediting,
  onStopMultiediting,
  isShowingTags,
  toggleShowTags,
  hideAddNewTagBtn,
  hideStatusFilter,
  showCallTypeFilter,
  disabled,
  isUpdating,
  isMultiEditing,
}: IFiltersProps) {
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [search, setSearch] = useState('');

  const onSearchChange = (e: any) => {
    setSearch(e.target.value);

    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      if (onFiltersUpdated) {
        onFiltersUpdated({ ...filters, searchString: e.target.value });
      }
    }, 500);
  };

  const changeCallType = (value: AgentCallType[]) => {
    if (value.length > 0) {
      if (onFiltersUpdated) {
        onFiltersUpdated({ ...filters, callType: value[value.length - 1] });
      }
    }
  };

  const changeEmotionalState = (value: AgentEmotionalState | undefined) => {
    if (onFiltersUpdated) {
      onFiltersUpdated({ ...filters, emotionalState: value });
    }
  };

  const [isAgentStatusActive, setIsAgentStatusActive] = useState<boolean>(
    filters.agentStatus === AgentStatus.INACTIVE ? false : true,
  );

  const changeStatus = (value: AgentStatus) => {
    if (onFiltersUpdated) {
      onFiltersUpdated({ ...filters, agentStatus: value });
    }
  };

  const changeLayout = (l: AGENTS_LAYOUT) => {
    if (onPageLayoutChange) {
      onPageLayoutChange(l);
    }
  };

  const onVariationsSelected = (variationsIds: string[]) => {
    if (onFiltersUpdated) {
      onFiltersUpdated({ ...filters, variations: variationsIds });
    }
  };

  const onTagsSelected = (tagsId: string[], tagCondition?: TagCondition) => {
    if (onFiltersUpdated) {
      onFiltersUpdated({
        ...filters,
        tags: tagsId,
        tagCondition: tagCondition!,
      });
    }
  };

  return (
    <>
      <div
        className={
          'flex mx-4 mt-4 mb-4 ' +
          (disabled && 'opacity-50 pointer-events-none ')
        }
      >
        <div className="relative flex-1">
          <Input
            placeholder="Search by name, job title, variation, or company..."
            value={search}
            onChange={onSearchChange}
            className="pr-10"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            {isUpdating && <Loader2 className="animate-spin" size={16} />}
          </div>
        </div>
        <div className="ml-2">
          <VariationsFilter
            current={filters.variations}
            onFiltersUpdated={onVariationsSelected}
          />
        </div>
        <div className="ml-2">
          <TagsFilter
            isShowingTags={isShowingTags}
            toggleShowTags={toggleShowTags}
            current={filters.tags}
            onFiltersUpdated={onTagsSelected}
            onStartMultiediting={onStartMultiediting}
            hideAddNewTag={hideAddNewTagBtn}
          />
        </div>
        <div className="ml-2">
          <EmotionalStateFilter
            current={filters.emotionalState}
            onEmotionalStateUpdated={changeEmotionalState}
          />
        </div>

        <div className="mr-1">
          <Button variant="ghost" onClick={toggleShowTags}>
            {isShowingTags ? (
              <div className="flex items-center">
                <div className="w-6">
                  <EyeOff size="16" />
                </div>
                <div className="w-[70px]">Hide tags</div>
              </div>
            ) : (
              <div className="flex">
                <div className="w-6 pt-[2px]">
                  <Eye size="16" />
                </div>
                <div className="w-[70px]">Show tags</div>
              </div>
            )}
          </Button>
        </div>

        <div className="mr-1">
          <Button
            variant={isMultiEditing ? 'default' : 'ghost'}
            onClick={() =>
              isMultiEditing ? onStopMultiediting?.() : onStartMultiediting?.()
            }
          >
            <div className="flex">
              <div className="w-6 pt-[2px]">
                {isMultiEditing ? (
                  <CheckIcon size="16" />
                ) : (
                  <SquareIcon size="16" />
                )}
              </div>
              <div className="">Select multiple</div>
            </div>
          </Button>
        </div>

        <div className="ml-24"></div>
        {!hideStatusFilter && (
          <div className="flex space-x-2 items-center ml-4 mr-4">
            <Switch
              checked={isAgentStatusActive}
              onCheckedChange={(value) => {
                setIsAgentStatusActive(value);
                if (value) {
                  changeStatus(AgentStatus.ACTIVE);
                } else {
                  changeStatus(AgentStatus.INACTIVE);
                }
              }}
            />
            <Badge
              variant="default"
              className={isAgentStatusActive ? 'bg-green-600' : 'bg-red-600'}
            >
              {isAgentStatusActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>
        )}

        {onPageLayoutChange && (
          <div className="flex items-center">
            <div
              onClick={() => {
                changeLayout(AGENTS_LAYOUT.LIST);
              }}
              className={cn(
                'border border-r-0 rounded-l-full pl-4 pr-3 py-2 cursor-pointer hover:opacity-80',
                {
                  'text-white': layout == AGENTS_LAYOUT.LIST,
                  'bg-[#59bebf]': layout == AGENTS_LAYOUT.LIST,
                },
              )}
            >
              <AlignJustify size={17} />
            </div>
            <div
              onClick={() => {
                changeLayout(AGENTS_LAYOUT.GRID);
              }}
              className={cn(
                'border rounded-r-full pr-4 pl-3 py-2 cursor-pointer hover:opacity-80',
                {
                  'text-white': layout == AGENTS_LAYOUT.GRID,
                  'bg-[#59bebf]': layout == AGENTS_LAYOUT.GRID,
                },
              )}
            >
              <LayoutGrid size={17} />
            </div>
          </div>
        )}
      </div>
      {showCallTypeFilter && (
        <div className="mx-4 mb-4">
          <CallTypeFilterPanel
            current={[filters.callType]}
            onCallTypesUpdated={changeCallType}
          />
        </div>
      )}
    </>
  );
}
