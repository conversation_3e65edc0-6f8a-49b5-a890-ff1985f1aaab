import React, { useState, useRef, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  CheckIcon,
  Loader2Icon,
  FilterX,
  TagsIcon,
  PlusIcon,
  SlidersHorizontalIcon,
  LockIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import { TagCondition, TagDto } from '@/lib/Agent/types';
import useTags from '@/hooks/useTags';
import CreateNewTagDialog from '../../multiactionsBar/tagsMenu/createNew';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import dayjs from 'dayjs';
import useOrg from '@/hooks/useOrg';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import useUserSession from '@/hooks/useUserSession';
import { AppPermissions } from '@/lib/permissions';

interface ITagsFilterProps {
  current: string[];
  onFiltersUpdated: (tagsIds: string[], tagCondition?: TagCondition) => void;
  onStartMultiediting?: () => void;
  toggleShowTags?: () => void;
  isShowingTags?: boolean;
  hideAddNewTag?: boolean;
}

function TagsFilter({
  current,
  isShowingTags,
  onFiltersUpdated,
  onStartMultiediting,
  toggleShowTags,
  hideAddNewTag,
}: ITagsFilterProps) {
  const { isLoggedIn, canAccess } = useUserSession();
  const { data: dbOrg } = useOrg();

  if (!current) {
    current = [];
  }

  const [isCreatenewDialogOpen, setIsCreatenewDialogOpen] =
    useState<boolean>(false);

  const [searchString, setSearchString] = useState<string>('');
  const [tagCondition, setTagCondition] = useState<TagCondition>(
    TagCondition.OR,
  );

  const [open, setOpen] = useState(false);

  const { data: allTags, isLoading: isLoadingTags } = useTags(
    true,
    0,
    0,
    searchString,
  );
  const tags = allTags || [];

  const currentlySelectedTags: TagDto[] = [];
  if (allTags && allTags.length > 0) {
    allTags.map((t) => {
      if (current.indexOf(String(t.id)) > -1) {
        currentlySelectedTags.push(t);
      }
    });
  }

  const [selected, setSelected] = useState<TagDto[]>(currentlySelectedTags);

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const resetAgentsForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetAgentsForSearch.current = true;
    }, 200);

    setSearchString(s);
  };

  const clearAll = () => {
    setSelected([]);
    setTagCondition(TagCondition.OR);
    if (onFiltersUpdated) {
      onFiltersUpdated([], TagCondition.OR);
    }
    setOpen(false);
  };

  const changeTagCondition = (newTagCondition: string) => {
    const newValue =
      TagCondition[newTagCondition?.toUpperCase() as keyof typeof TagCondition];
    setTagCondition(newValue);
    if (onFiltersUpdated) {
      onFiltersUpdated(
        selected.map((a) => String(a.id)),
        newValue,
      );
    }
  };

  const toggleTag = (_id: string) => {
    const id = parseInt(_id);

    let newSelection: TagDto[] = [];

    if (selected.find((val) => val.id === id)) {
      newSelection = selected.filter((val) => val.id !== id) as TagDto[];
    } else {
      let selectedTagDto: TagDto | null = null;
      tags.map((a) => {
        if (a.id == id) {
          selectedTagDto = a;
          return;
        }
      });

      if (selectedTagDto) {
        newSelection = [...selected, selectedTagDto] as TagDto[];
      }
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(
        newSelection.map((a) => String(a.id)),
        tagCondition,
      );
    }
    setSelected([...newSelection] as TagDto[]);
  };

  const _onStartMultiediting = () => {
    if (onStartMultiediting) {
      onStartMultiediting();
      setOpen(false);
    }
  };

  const _toggleShowTags = () => {
    if (toggleShowTags) {
      toggleShowTags();
    }
  };

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  if (isLoggedIn) {
    return (
      <>
        <Popover
          open={open}
          onOpenChange={(o) => {
            setOpen(o);
          }}
        >
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="border w-full"
            >
              <TagsIcon className="mr-2 h-4 w-4" />
              Tags
              {selected.length > 0 && (
                <>
                  <Separator orientation="vertical" className="mx-2 h-4" />
                  <div className="space-x-1 lg:flex">
                    <Badge
                      variant="secondary"
                      className="rounded-sm px-1 font-normal"
                    >
                      {selected.length} selected
                    </Badge>
                  </div>
                </>
              )}
              <div className="flex-1"></div>
              <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent align="start" className="p-0">
            <Command filter={(value, search) => 1}>
              <CommandInput
                placeholder="Search tags..."
                className="h-9"
                value={searchString}
                onValueChange={filterResults}
              />
              <CommandList>
                <CommandGroup heading="Tags">
                  {tags.map((v) => (
                    <CommandItem
                      key={v.id}
                      value={String(v.id)}
                      onSelect={toggleTag}
                    >
                      <div
                        className={cn(
                          'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                          selected?.find((val) => val.id === v.id)
                            ? 'bg-primary text-primary-foreground'
                            : 'opacity-50 [&_svg]:invisible',
                        )}
                      >
                        <CheckIcon className={cn('h-4 w-4')} />
                      </div>
                      <div className="flex space-x-2 items-center">
                        <div className="capitalize">{v.name}</div>
                      </div>
                    </CommandItem>
                  ))}

                  {isLoadingTags ? (
                    <CommandItem className="justify-center text-center">
                      <Loader2Icon className="animate-spin" />
                    </CommandItem>
                  ) : (
                    <>
                      {tags.length === 0 ? (
                        <CommandItem className="justify-center text-center">
                          No tag found
                        </CommandItem>
                      ) : null}
                    </>
                  )}
                </CommandGroup>
              </CommandList>
              <ToggleGroup
                className="mt-3"
                onValueChange={changeTagCondition}
                value={tagCondition}
                type="single"
              >
                <ToggleGroupItem
                  value={TagCondition.AND}
                  selected={tagCondition === TagCondition.AND}
                >
                  Match all
                </ToggleGroupItem>
                <ToggleGroupItem
                  value={TagCondition.OR}
                  selected={tagCondition === TagCondition.OR}
                >
                  Match any
                </ToggleGroupItem>
              </ToggleGroup>
              {canAccess(AppPermissions.MANAGE_TAGS) &&
                !hideAddNewTag && (
                  <>
                    {onStartMultiediting && (
                      <>
                        <CommandSeparator />
                        <CommandGroup>
                          <CommandItem
                            className="justify-center text-center"
                            onSelect={_onStartMultiediting}
                            disabled={isPilotEnded}
                          >
                            <SlidersHorizontalIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                            <span>Manage tags</span>
                          </CommandItem>
                        </CommandGroup>
                      </>
                    )}{' '}
                    <CommandSeparator />
                    <CommandGroup>
                      <CommandItem
                        className="justify-center text-center"
                        onSelect={() => {
                          setIsCreatenewDialogOpen(true);
                        }}
                        disabled={isPilotEnded}
                      >
                        <PlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                        <span>New tag</span>
                      </CommandItem>
                    </CommandGroup>
                  </>
                )}
              {selected?.length > 0 && (
                <>
                  <CommandSeparator />
                  <CommandGroup>
                    <CommandItem
                      onSelect={clearAll}
                      className="justify-center text-center"
                    >
                      <FilterX size="14" />
                      &nbsp;Clear filters
                    </CommandItem>
                  </CommandGroup>
                </>
              )}
            </Command>
          </PopoverContent>
        </Popover>
        <CreateNewTagDialog
          open={isCreatenewDialogOpen}
          onClose={() => {
            setIsCreatenewDialogOpen(false);
            setOpen(true);
          }}
        />
      </>
    );
  } else {
    return (
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              className="border-dashed opacity-45"
            >
              <TagsIcon className="mr-2 h-4 w-4" />
              Tags
              <Separator orientation="vertical" className="mx-2 h-4" />
              <LockIcon className="w-4 h-4 text-muted-foreground" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Book a demo to access Tags</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
}

export default React.memo(TagsFilter);
