import React, { useState, useRef, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { FileStack, CheckIcon, Loader2Icon, FilterX } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import useOrgVariations from '@/hooks/useOrgVariations';
import { VariationDto } from '@/lib/Agent/types';
import useOrgAgentsById from '@/hooks/useOrgAgentsById';

interface IVariationsFilterProps {
  current: string[];
  onFiltersUpdated: (variationsIds: string[]) => void;
}

function VariationsFilter({
  onFiltersUpdated,
  current,
}: IVariationsFilterProps) {
  const defaultNumberOfResults = 10;
  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );
  const [searchString, setSearchString] = useState<string>('');

  const [open, setOpen] = useState(false);

  const { data: allVariations, isLoading: isLoadingVariations } =
    useOrgVariations(true, 0, numberOfResults, searchString);
  const variations = allVariations || [];

  let noMoreVariations = false;
  const presentInList = useRef<any>({});

  if (allVariations) {
    if (allVariations.length > 0) {
      if (allVariations.length < numberOfResults) {
        noMoreVariations = true;
      }

      allVariations.map((a) => {
        presentInList.current[a.id] = true;
      });
    }
  }

  if (!current) {
    current = [];
  }

  const { data: currentlySelectedAgents } = useOrgAgentsById(
    current.map(Number),
  );

  const tmpSelectedVariations: VariationDto[] = [];
  currentlySelectedAgents?.map((a) => {
    tmpSelectedVariations.push({
      id: a.id,
      name: a.variationName || '',
      vapiId: a.vapiId,
    });
  });

  const [selected, setSelected] = useState<VariationDto[]>(
    tmpSelectedVariations,
  );

  useEffect(() => {
    if (current) {
      if (current.length == 0) {
        setSelected([] as VariationDto[]);
      }
    } else {
      setSelected([] as VariationDto[]);
    }
  }, [current]);

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const resetAgentsForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetAgentsForSearch.current = true;
      presentInList.current = {};
      setNumberOfResults(defaultNumberOfResults);
      //setSearch(s);
    }, 500);

    setSearchString(s);
  };

  const clearAll = () => {
    setSelected([]);
    if (onFiltersUpdated) {
      onFiltersUpdated([]);
    }
    setOpen(false);
  };

  const toggleVariation = (_id: string) => {
    const id = parseInt(_id);

    let newSelection: VariationDto[] = [];

    if (selected.find((val) => val.id === id)) {
      newSelection = selected.filter((val) => val.id !== id) as VariationDto[];
    } else {
      let selectedVariationDto: VariationDto | null = null;
      variations.map((a) => {
        if (a.id == id) {
          selectedVariationDto = a;
          return;
        }
      });

      if (selectedVariationDto) {
        newSelection = [...selected, selectedVariationDto] as VariationDto[];
      }
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(newSelection.map((a) => String(a.id)));
    }
    setSelected([...newSelection] as VariationDto[]);
  };

  const loadMore = () => {
    setNumberOfResults(numberOfResults + 15);
  };

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border"
        >
          <FileStack className="mr-2 h-4 w-4" />
          Variations
          {selected.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {selected.length} selected
                </Badge>
              </div>
            </>
          )}
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[200px] p-0">
        <Command filter={(value, search) => 1}>
          <CommandInput
            placeholder="Search variations..."
            className="h-9"
            value={searchString}
            onValueChange={filterResults}
          />
          <CommandList>
            {selected.filter((a) => !presentInList.current[a.id]).length >
              0 && <CommandGroup heading="Selected"></CommandGroup>}
            <CommandGroup
              heading="Variations"
              className="max-h-[50vh] overflow-y-scroll"
            >
              {variations.map((v) => (
                <CommandItem
                  key={v.id}
                  value={String(v.id)}
                  onSelect={toggleVariation}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      selected?.find((val) => val.id === v.id)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex space-x-2 items-center">
                    <div className="capitalize">{v.name}</div>
                  </div>
                </CommandItem>
              ))}

              {isLoadingVariations ? (
                <CommandItem className="justify-center text-center">
                  <Loader2Icon className="animate-spin" />
                </CommandItem>
              ) : (
                <>
                  {variations.length > 0 ? (
                    <>
                      {!noMoreVariations && (
                        <CommandItem
                          onSelect={loadMore}
                          className="justify-center text-center"
                        >
                          More...
                        </CommandItem>
                      )}
                    </>
                  ) : (
                    <CommandItem className="justify-center text-center">
                      No variations found
                    </CommandItem>
                  )}
                </>
              )}
            </CommandGroup>
          </CommandList>
          {selected?.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export default React.memo(VariationsFilter);
