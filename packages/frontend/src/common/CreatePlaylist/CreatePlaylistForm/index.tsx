import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { CallDto } from '@/lib/Call/types';
import PlaylistService from '@/lib/Playlist';
import { UpdatePlaylistDto } from '@/lib/Playlist/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { InfoIcon, Loader2Icon, UsersIcon } from 'lucide-react';
import React, { useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { Id, toast } from 'react-toastify';
import { z } from 'zod';

interface ICreatePlaylistFormProps {
  onSubmit: () => void;
  defaultPlaylistValues?: UpdatePlaylistDto;
  defaultPlaylistId?: number;
  addCallId?: number;
}

const formSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  shared: z.boolean().optional(),
});

function CreatePlaylistForm({
  onSubmit: onSubmitted,
  defaultPlaylistValues = {
    name: '',
    description: '',
    shared: false,
  },
  defaultPlaylistId,
  addCallId,
}: ICreatePlaylistFormProps) {
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultPlaylistValues
      ? {
          name: defaultPlaylistValues.name,
          description: defaultPlaylistValues.description || '',
          shared: defaultPlaylistValues.shared,
        }
      : {
          name: '',
          description: '',
          shared: false,
        },
  });

  const createPlaylistMutation = useMutation({
    mutationFn: PlaylistService.createPlaylist,
    onSuccess: async (playlist, params) => {
      // Analytics.track(AssignmentEvents.CREATED_SUCCESS, {
      //   ...params,
      // });
      queryClient.invalidateQueries({ queryKey: ['playlists'] });

      if (addCallId) {
        try {
          await PlaylistService.addCallToPlaylist({
            id: playlist.id,
            callId: addCallId,
          });

          queryClient.invalidateQueries({ queryKey: ['orgCalls'] });

          errorToastId.current = toast.success(
            `Added call to ${playlist.name}`,
            {
              position: 'bottom-center',
            },
          );

          onSubmitted();
        } catch (err) {
          errorToastId.current = toast.success(`Created ${playlist.name}`, {
            position: 'bottom-center',
          });
        }
      } else {
        errorToastId.current = toast.success(`Created ${playlist.name}`, {
          position: 'bottom-center',
        });

        onSubmitted();
      }
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error creating your playlist. Please try again.',
        );
      }
      console.log('ERROR creating playlist', error);
      // Analytics.track(AssignmentEvents.CREATED_ERROR, {
      //   ...params,
      //   err: "Error creating assignment",
      // });
    },
  });

  const updatePlaylistByIdMutation = useMutation({
    mutationFn: PlaylistService.updatePlaylistById,
    onSuccess: async (playlist, params) => {
      // Analytics.track(AssignmentEvents.CREATED_SUCCESS, {
      //   ...params,
      // });
      queryClient.invalidateQueries({ queryKey: ['playlists'] });
      errorToastId.current = toast.success(`Saved ${playlist.name}`, {
        position: 'bottom-center',
      });
      onSubmitted();
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving your playlist. Please try again.',
        );
      }
      console.log('ERROR saving playlist', error);
      // Analytics.track(AssignmentEvents.CREATED_ERROR, {
      //   ...params,
      //   err: "Error creating assignment",
      // });
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
    if (defaultPlaylistValues?.name) {
      updatePlaylistByIdMutation.mutate({
        id: defaultPlaylistId!,
        ...values,
      });
    } else {
      createPlaylistMutation.mutate({
        ...values,
      });
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  required
                  type="text"
                  placeholder="Playlist name"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea placeholder="Optional description" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="shared"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-2">
              <UsersIcon className="w-4 h-4 text-muted-foreground mt-2" />
              <FormLabel className="mt-2">Shared?</FormLabel>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={(value) => {
                    field.onChange(value);
                  }}
                  id="status-toggle"
                />
                {/* <Input required type="text" {...field} /> */}
              </FormControl>
              <TooltipProvider delayDuration={100}>
                <Tooltip
                // onOpenChange={(o) => {
                //   if (o) {
                //     Analytics.track(CallEvents.STAT_CARD_INFO_HOVERED, {
                //       card: title,
                //       agentId: call?.agent?.id,
                //       id: call?.id,
                //       vapiId: call?.vapiId,
                //       orgId: call?.orgId,
                //     });
                //   }
                // }}
                >
                  <TooltipTrigger>
                    <InfoIcon className="w-4 h-4 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent side="right" align="end">
                    <p>
                      Shared playlists can be accessed and edited by all members
                      of the org
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          disabled={
            !form.formState.isValid ||
            createPlaylistMutation.isPending ||
            updatePlaylistByIdMutation.isPending
          } // here
          className="float-right text-primary"
          variant={'outline'}
        >
          {defaultPlaylistValues?.name ? (
            updatePlaylistByIdMutation.isPending ? (
              <>
                <Loader2Icon className="animate-spin mr-2" />
                Saving
              </>
            ) : (
              'Save'
            )
          ) : createPlaylistMutation.isPending ? (
            <>
              <Loader2Icon className="animate-spin mr-2" />
              Creating
            </>
          ) : (
            'Create'
          )}
        </Button>
      </form>
    </Form>
  );
}

export default CreatePlaylistForm;
