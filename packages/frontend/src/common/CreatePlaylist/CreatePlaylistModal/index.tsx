import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import CreatePlaylistForm from '../CreatePlaylistForm';
import { UpdatePlaylistDto } from '@/lib/Playlist/types';

interface ICreatePlaylistModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  onSubmit: () => void;
  defaultPlaylistValues?: UpdatePlaylistDto;
  defaultPlaylistId?: number;
  addCallId?: number;
}

export function CreatePlaylistModal({
  modalOpen,
  setModalOpen,
  onSubmit,
  defaultPlaylistValues,
  defaultPlaylistId,
  addCallId,
}: ICreatePlaylistModalProps) {
  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent
        className="close-btn"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {defaultPlaylistValues?.name
              ? 'Edit playlist'
              : 'Create new playlist'}
          </DialogTitle>
          <DialogDescription className="py-4">
            <CreatePlaylistForm
              onSubmit={onSubmit}
              defaultPlaylistValues={defaultPlaylistValues}
              defaultPlaylistId={defaultPlaylistId}
              addCallId={addCallId}
            />
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
