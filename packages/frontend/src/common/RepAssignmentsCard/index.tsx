import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import useAssignments from '@/hooks/useAssignmentsByUser';
import useOrgCurrentUser from '@/hooks/useOrgCurrentUser';
import { AssignmentDto } from '@/lib/Assignment/types';
import { cn } from '@/lib/utils';
import { ProgressCircle } from '@tremor/react';
import dayjs from 'dayjs';
import { Loader2Icon, Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import AssignmentService from '@/lib/Assignment';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface IRepAssignmentsCardProps {
  repId?: number;
}

function RepAssignmentsCard({ repId }: IRepAssignmentsCardProps) {
  const { data: curUser } = useOrgCurrentUser();
  const { data: allAssignments, isLoading } = useAssignments(
    repId || (curUser?.id as number),
  );
  const router = useRouter();

  const assignments = allAssignments; //?.filter(a => dayjs(a.dueDate).isAfter(dayjs()));

  //************ DELETE ASSIGMENTS  ************/
  const queryClient = useQueryClient();

  const [requestDeleteConfirmation, setRequestDeleteConfirmation] =
    useState(false);
  const [deleteAssignment, setDeleteAssignment] =
    useState<AssignmentDto | null>(null);

  const startDeleteAssignment = async (assignment: AssignmentDto) => {
    setDeleteAssignment(assignment);
    setRequestDeleteConfirmation(true);
  };

  const handleDeleteAssignment = async () => {
    setRequestDeleteConfirmation(false);
    if (deleteAssignment) {
      await AssignmentService.deleteAssignment(deleteAssignment.id);
      queryClient.invalidateQueries({ queryKey: ['adminAssignments'] });
      queryClient.invalidateQueries({
        queryKey: ['adminAssignmentsByAgentId'],
      });
      queryClient.invalidateQueries({ queryKey: ['assignments'] });
      queryClient.invalidateQueries({ queryKey: ['adminAssignmentsByUser'] });
    }
  };

  const handleCancelDeleteAssignment = async () => {
    setRequestDeleteConfirmation(false);
    setDeleteAssignment(null);
  };

  return (
    <div className="flex justify-center">
      <div>
        <Card>
          <CardHeader>
            <CardTitle>Assignments</CardTitle>
            <CardDescription>Active assignments by buyer bot</CardDescription>
          </CardHeader>
          <CardContent>
            {(assignments || [])?.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Buyer bot</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Due Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody
                  style={{
                    opacity: isLoading ? 0 : 1,
                    transition: 'opacity 0.5s ease-in-out',
                  }}
                >
                  {assignments?.map((assignment, i) => {
                    const completedRatio =
                      assignment.numCompletedCalls /
                      assignment.numAssignedCalls;

                    return (
                      <TableRow
                        className="cursor-pointer"
                        onClick={() =>
                          router.push(`/buyers/${assignment?.agent?.vapiId}`)
                        }
                        key={assignment.id}
                      >
                        <TableCell className="flex items-center">
                          <Avatar className="h-9 w-9">
                            <AvatarImage
                              src={`${assignment.agent?.avatar}`}
                              alt="Avatar"
                            />
                            <AvatarFallback className="text-muted-foreground">
                              {assignment?.agent?.firstName?.charAt(0) || ''}
                              {assignment?.agent?.lastName?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <div className="ml-2 text-sm font-medium">
                            {assignment?.agent?.firstName}{' '}
                            {assignment?.agent?.lastName}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <div style={{ width: '40px' }}>
                              <ProgressCircle
                                color={
                                  completedRatio >= 0.75
                                    ? 'emerald'
                                    : completedRatio >= 0.4
                                      ? 'yellow'
                                      : 'red'
                                }
                                value={completedRatio * 100}
                                size="sm"
                              >
                                <span className="text-xs text-gray-700 font-medium">
                                  {Number(completedRatio).toLocaleString(
                                    'en-US',
                                    {
                                      minimumFractionDigits: 0,
                                      maximumFractionDigits: 0,
                                      style: 'percent',
                                    },
                                  )}
                                </span>
                              </ProgressCircle>
                            </div>
                            <div className="text-xs text-muted-foreground ml-1">
                              {assignment.numCompletedCalls}/
                              {assignment.numAssignedCalls} calls done
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {dayjs(assignment.dueDate).format('MMM D, YYYY')}
                        </TableCell>
                        {/* <TableCell>
                            <Button variant={"ghost"} onMouseDown={(e) => { e.preventDefault(); e.stopPropagation() }} onClick={() => startDeleteAssignment(assignment)}>
                              <Trash2 />
                            </Button>
                          </TableCell> */}
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            ) : (
              <div className="flex flex-col space-y-4 items-center justify-center h-[200px] border rounded-lg border-dotted">
                {isLoading ? (
                  <Loader2Icon className="animate-spin" />
                ) : (
                  <p className="text-muted-foreground">No assignments yet</p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
        <DeleteConfirmationModal
          open={requestDeleteConfirmation}
          onCancel={handleCancelDeleteAssignment}
          onConfirm={handleDeleteAssignment}
          title={'Delete assignment'}
          description={'Are you sure you want to delete this assignment?'}
        />
      </div>
    </div>
  );
}

export default RepAssignmentsCard;
