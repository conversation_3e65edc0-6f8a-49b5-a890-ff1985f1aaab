import useUserSession from '@/hooks/useUserSession';
import { AgentCallType, AgentDto } from '@/lib/Agent/types';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  CalendarIcon,
  ChevronLeftIcon,
  PhoneIcon,
  RefreshCwIcon,
} from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import dayjs from 'dayjs';
import Link from 'next/link';
import CallActionsDropdown from '@/components/CallActionsDropdown';
import { CallReportBugPopoverButton } from '@/components/CallReportBugPopoverButton';
import { CallDto, CallScoringStatus } from '@/lib/Call/types';
import AgentAvatar from '@/components/Avatars/Agent';
import { AvatarComponent } from '@/components/Avatars/AvatarComponent';
import '@/app/globals.css';
import { getColorFromScore } from '../tabs/feedback';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { timeAgo } from '@/lib/utils';
import EmotionBadge from '@/common/CreateBuyerForm/BotPreview/EmotionBadge';
import { cn } from '@/lib/utils';
import MultiPartyTooltipContent from '@/common/Buyers/ProdSite/listLayout/multipartyTooltipContext';
import { useMemo } from 'react';

export const getCallTypeLabelAndIcon = (callType: AgentCallType) => {
  const callTypeObj = CALL_TYPE_OPTIONS.find((item) => item.value === callType);
  const Icon = callTypeObj?.Icon;
  const label = callTypeObj?.label;
  return (
    <div className="flex items-center">
      {Icon && <Icon className="w-3 h-3 mr-1" />}
      {label}
    </div>
  );
};
interface IProps {
  isLoadingCall: boolean;
  call?: CallDto;
  rescoreCall: () => void;
}

export default function CallSummaryHeader({
  isLoadingCall,
  call,
  rescoreCall,
}: IProps) {
  const { isLoggedIn, isPilotEnded, dbUser, canRescoreCalls } =
    useUserSession();

  const router = useRouter();
  const pathname = usePathname();

  const caller = call?.caller;
  const agent = call?.agent as AgentDto;
  const callMetadata = call?.providerMetadata
    ? call?.providerMetadata
    : call?.vapiMetadata;
  const demoInboundFormResponse = call?.demoInboundFormResponse;

  const callerFirstName =
    caller?.firstName || demoInboundFormResponse?.name?.split(' ')[0] || '';
  const callerLastName =
    caller?.lastName || demoInboundFormResponse?.name?.split(' ')[1] || '';
  const fallbackText = `${callerFirstName?.charAt(0) || ''}` || 'U';

  const isMultiPartyCall = useMemo(
    () =>
      call?.agent?.supportingAgentInfo &&
      call?.agent?.supportingAgentInfo?.length > 0,
    [call?.agent?.supportingAgentInfo],
  );
  return (
    <>
      <div className="flex flex-wrap items-center border-b p-3 border-[#E4E4E7] justify-between flex-wrap">
        <div className="flex space-x-4 items-center">
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger asChild>
                <span tabIndex={0}>
                  <div
                    className="border border-[#E4E4E7] p-[8px] rounded-lg flex items-center space-x-2 text-[#71717A] font-medium text-sm leading-5 cursor-pointer"
                    onClick={() => router.push(`/calls`)}
                  >
                    <ChevronLeftIcon className="w-4 h-4 text-[#71717A]" />
                  </div>
                </span>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <div>Back</div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <div className="flex items-center">
            {callerFirstName ? (
              <div className="flex gap-2 items-center">
                <div className="text-[#71717A] font-medium text-sm leading-5">
                  Rep
                </div>
                <div
                  className={cn(
                    'flex items-center border border-[#E4E4E7] bg-[#FBFBFB] rounded-[100px] p-1 pr-2',
                    {
                      'cursor-pointer': isLoggedIn,
                    },
                  )}
                  onClick={() => {
                    if (isLoggedIn) {
                      router.push(`/members/${caller?.id}`);
                    }
                  }}
                >
                  <AvatarComponent
                    imageUrl={caller?.avatar}
                    fallbackText={fallbackText}
                    className="w-5 h-5 mr-[6px] text-sm"
                    fallbackClassName="text-sm"
                  />
                  <div className="font-medium text-sm leading-5 max-w-[200px] truncate">
                    {callerFirstName} {callerLastName}
                  </div>
                </div>
              </div>
            ) : (
              <Skeleton className="w-[150px] h-[30px] rounded-xl" />
            )}
            <div className="flex items-center h-4 w-px bg-gray-400 mx-2" />
            {agent?.firstName ? (
              <div className="flex gap-2 items-center">
                <div className="text-[#71717A] font-medium text-sm leading-5">
                  Buyer
                </div>
                <div
                  className="flex items-center rounded-[100px] p-1 pr-2 border border-[rgba(63,197,209,0.2)] bg-[#FFFFFF] bg-gradient-to-b from-[#3dc3e6]/[0.05] via-[#49c8cf]/[0.05] to-[#36c4bf]/[0.05] cursor-pointer"
                  onClick={() => {
                    const agentProviderId = agent?.providerAgentId
                      ? agent?.providerAgentId
                      : agent?.vapiId;
                    router.push(`/buyers/${agentProviderId}`);
                  }}
                >
                  <AgentAvatar className="w-5 h-5 mr-[6px]" agent={agent} />
                  <div className="font-medium text-sm leading-5 mr-[6px] max-w-[200px] truncate">
                    {!isMultiPartyCall
                      ? `${agent?.firstName} ${agent?.lastName}`
                      : `${agent?.firstName}`}
                  </div>
                  <div className="flex items-center text-white text-[10px] w-[16px] h-[14px] rounded-[4px] px-[3px] mr-[2px] bg-[linear-gradient(180deg,_#3DC3E6_0%,_#49C8CF_33.33%,_#36C4BF_98.96%)] font-medium leading-[100%] tracking-[0%] ">
                    AI
                  </div>
                </div>
              </div>
            ) : (
              <Skeleton className="w-[150px] h-[30px] rounded-xl" />
            )}
            <div className="flex items-center h-4 w-px bg-gray-400 mx-2" />
            {agent?.emotionalState ? (
              <EmotionBadge
                emotionalState={agent?.emotionalState}
                size="small"
              />
            ) : (
              <Skeleton className="w-[150px] h-[30px] rounded-xl" />
            )}
            {agent?.supportingAgentInfo ? (
              <>
                <div className="flex items-center h-4 w-px bg-gray-400 mx-2" />
                <div className="flex gap-2 items-center">
                  <div className="text-[#71717A] font-medium text-sm leading-5">
                    Committee
                  </div>
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger className="">
                        <div
                          className="flex items-center rounded-[100px] p-1 pr-2 border border-[rgba(63,197,209,0.2)] bg-[#FFFFFF] bg-gradient-to-b from-[#3dc3e6]/[0.05] via-[#49c8cf]/[0.05] to-[#36c4bf]/[0.05] cursor-pointer"
                          onClick={() => {
                            const agentProviderId = agent?.providerAgentId
                              ? agent?.providerAgentId
                              : agent?.vapiId;
                            router.push(`/buyers/${agentProviderId}`);
                          }}
                        >
                          <div className="mr-[6px] flex items-center">
                            <AgentAvatar
                              className="w-5 h-5 border border-[#F5FCFD]"
                              agent={agent}
                            />
                            {agent.supportingAgentInfo.map((agent, index) => (
                              <div key={index}>
                                <AgentAvatar
                                  className="w-5 h-5 -ml-1 border border-[#F5FCFD]"
                                  agent={agent}
                                  fallbackTextOverride={agent.firstName.charAt(
                                    0,
                                  )}
                                />
                              </div>
                            ))}
                          </div>

                          <div className="flex items-center text-white text-[10px] w-[16px] h-[14px] rounded-[4px] px-[3px] mr-[2px] bg-[linear-gradient(180deg,_#3DC3E6_0%,_#49C8CF_33.33%,_#36C4BF_98.96%)] font-medium leading-[100%] tracking-[0%] ">
                            AI
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        side="bottom"
                        className="bg-white border border-[#E4E4E7] p-0 rounded-[6px]"
                      >
                        <MultiPartyTooltipContent agent={agent} />
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </>
            ) : (
              <></>
            )}
            <div className="flex items-center h-4 w-px bg-gray-400 mx-2" />
            <div className="flex">
              {call?.agent?.callType ? (
                <div className="px-2 py-1 rounded-[100px] bg-[#F4F4F5] text-xs font-medium text-[#2E3035] leading-5">
                  <div>{getCallTypeLabelAndIcon(call?.agent?.callType)}</div>
                </div>
              ) : (
                <Skeleton className="w-[100px] h-[30px] rounded-xl" />
              )}
            </div>
            <div className="flex items-center h-4 w-px bg-gray-400 mx-2" />
            {callMetadata?.startedAt ? (
              <div className="flex items-center">
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span tabIndex={0}>
                        <div className="flex items-center py-[3px] cursor-pointer text-[#71717A] hover:text-[#09090B]">
                          <CalendarIcon className="w-[14px] h-[14px] mr-2" />
                          {timeAgo(callMetadata?.startedAt || '')}
                        </div>
                      </span>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <div className="text-sm text-white leading-5">
                        {dayjs(callMetadata?.startedAt).format(
                          'MMM D, YYYY, h:mm A',
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            ) : (
              <Skeleton className="w-[100px] h-[30px] rounded-xl" />
            )}
            <div className="flex items-center h-4 w-px bg-gray-400 mx-2" />
            {call?.scorecard?.finalCallScore !== undefined ? (
              <div className="flex gap-2 items-center">
                <div className="text-[#71717A] font-medium text-sm leading-5">
                  Score
                </div>
                <div
                  className={`w-2 h-2 rounded-full ${getColorFromScore(Math.round(call?.scorecard?.finalCallScore ? call?.scorecard?.finalCallScore * 100 : 0), true)}`}
                ></div>
                <div className="text-sm font-medium text-[#09090B] leading-5 mr-1">
                  {call?.scorecard?.finalCallScore
                    ? Math.round(call?.scorecard?.finalCallScore * 100)
                    : '0'}
                  <span className="text-[#71717A]">/100</span>
                </div>
              </div>
            ) : (
              <Skeleton className="w-[115px] h-[30px] rounded-xl" />
            )}
            {call &&
              call?.scoringStatus !== CallScoringStatus.SCORING &&
              canRescoreCalls && (
                <TooltipProvider
                  delayDuration={50}
                  disableHoverableContent={isLoadingCall}
                >
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={'ghost'}
                        disabled={isLoadingCall}
                        onClick={rescoreCall}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        <RefreshCwIcon className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>Rescore this call</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
          </div>
        </div>
        <div className="flex items-center">
          {agent && (
            <Link
              href={
                isLoggedIn
                  ? `/buyers/${agent.vapiId}`
                  : pathname.includes('/embed')
                    ? `/embed/buyers/${agent?.vapiId}`
                    : `/buyers/${agent?.vapiId}`
              }
              className="h-min mr-2"
            >
              <Button variant={'default'} disabled={isPilotEnded}>
                <PhoneIcon className="w-4 h-4 mr-2" />
                Start new call
              </Button>
            </Link>
          )}
          {isLoggedIn && call?.callerId === dbUser?.id && (
            <CallReportBugPopoverButton call={call!} />
          )}
          {!isPilotEnded && (
            <CallActionsDropdown call={call!} location="individual_call_page" />
          )}
        </div>
      </div>
    </>
  );
}
