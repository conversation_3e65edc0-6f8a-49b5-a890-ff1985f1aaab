import { Skeleton } from '@/components/ui/skeleton';
import useCallCoaching, { useDemoCallCoaching } from '@/hooks/useCallCoaching';
import { CallDto } from '@/lib/Call/types';
import {
    ArrowLeft,
  Check,
  ChevronLeft,
  ChevronUp,
  ExternalLink,
  Phone,
  PhoneCall,
  Sparkles,
  SparklesIcon,
  X,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import Link from 'next/link';
import LinksManager from '@/lib/linksManager';
import Markdown from 'react-markdown';
import rehypeRaw from 'rehype-raw'; //to enable HTML in markdown
import remarkGfm from 'remark-gfm'; //to enable the extensions to markdown that GitHub adds with GFM
import animationStyles from './animation.module.css';
import { cn } from '@/lib/utils';
import useUserSession from '@/hooks/useUserSession';
import AgentAvatar from '@/components/Avatars/Agent';
import { AppPermissions } from '@/lib/permissions';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { ISelectedCriteria } from '..';
import { ICriterion, ICriterionNavigation, ISection } from '../scorecardSection';
import useCallCriteriaResources from '@/hooks/useCallCriteriaResources';
import { LearningMaterialDto, ScorecardConfigMaterialScope, ScorecardConfigMaterialType } from '@/lib/ScorecardConfig/types';
import MaterialCard from '@/common/Scorecards/LearningMaterials/MaterialCard';
import { Carousel, CarouselApi, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { TooltipPortal } from '@radix-ui/react-tooltip';
import useRouting from '@/hooks/useRouting';


interface ICoaching{
    agentId: number;
    coachingInfo: ICriterion;
    selectedCriteria: ISelectedCriteria | undefined;
    sectionInfo: ISection | undefined;
    finalCallScore: number;
    scorecardConfigId: number;
    criteriaInfoList: ICriterionNavigation[];
    initialIndex: number
}

export default function Coaching({ agentId, coachingInfo, selectedCriteria, sectionInfo, finalCallScore, scorecardConfigId, criteriaInfoList,initialIndex}: ICoaching) {
  const {
    isCompetitionOrg,
    aiCoachTextOverride,
    isLoggedIn,
    hideSimilarCalls,
    canAccess
} = useUserSession();
      const searchParams = useSearchParams()
    const criteriaParam = searchParams.get('criteria')
    const carouselCardClickRef = useRef<boolean>(false)
    const {setMultipleUrlParameters} = useRouting()
    const [carouselApi, setCarouselApi] = useState<CarouselApi>()
    const [currentIndex, setCurrentIndex] = useState(initialIndex);
    const params = useParams();
    const vapiId = params?.id as string;
    const {data: coachingResources, isLoading: isLoadingCoachingResources} = useCallCriteriaResources(vapiId, agentId, scorecardConfigId, coachingInfo?.criterion, finalCallScore, isLoggedIn);
  


  const str_pad_left = (string: any, pad: any, length: any) => {
    return (new Array(length + 1).join(pad) + string).slice(-length);
  };

  /*****************************/
  /********* RENDER ************/
  /*****************************/


  // Initial carousel scroll to required slide
  useEffect(() => {
    if (carouselApi) {
      if(!carouselCardClickRef.current){
        carouselApi.scrollTo(initialIndex, true);
      }
      const onSelect = () => {
        setCurrentIndex(carouselApi.selectedScrollSnap());
      }
      carouselApi.on('select', onSelect);
    }
  }, [carouselApi, initialIndex]);

  // Reset the ref for handling back navigation
  useEffect(() => {
    carouselCardClickRef.current = false;
  }, [criteriaParam]);
  
    return (
      <AnimatePresence>
        <div className="flex flex-col h-full">
        <>
        <div className="flex items-center cursor-pointer mb-4 w-fit" onClick={() => {
            setMultipleUrlParameters({tab: 'feedback', criteria: null})
            
        }}>
            <div>
                <ChevronLeft size={16} className='text-[#71717A] mr-2' />
            </div>
            <div className='text-[#71717A] text-sm font-medium'>
            View full scorecard
            </div>
            
        </div>
         {/* NAVIGATION */}
         <div className='flex items-center mb-5 relative'>
          <TooltipProvider delayDuration={50}>
            <Carousel className={cn('relative pr-[52px] box-border w-full flex-1', {
              'pl-[40px]': criteriaInfoList.length > 4,
            })}
            opts={{
              slidesToScroll: 1,
              align: 'start',
            }}
            setApi={setCarouselApi}
            >
              {criteriaInfoList.length > 4 && <CarouselPrevious className='absolute left-0 top-1/2'
              onClick={() => {
                if (carouselApi) {
                  if(currentIndex - 4 < 0){ 
                    carouselApi.scrollTo(0);
                  }else{
                    carouselApi.scrollTo(currentIndex - 4);
                  }
                }
              }}
              />}
              <CarouselContent className=''>
                {
                  criteriaInfoList.map((c: ICriterionNavigation, i: number) => {
                      return (
                        <CarouselItem key={i} className='basis-auto shrink-0 grow-0 w-[170px] pl-4 relative'>
                                  
                            <Tooltip>
                              <TooltipTrigger className='w-full'>
                          
                                      <div tabIndex={0} className={
                                        cn('p-3 rounded-lg h-[64px] cursor-pointer flex',{
                                          'bg-[#FCFEFE]': selectedCriteria?.sectionIndex === c.sectionIndex && selectedCriteria?.criteriaIndex === c.criteriaIndex,
                                          'border border-teal-500': selectedCriteria?.sectionIndex === c.sectionIndex && selectedCriteria?.criteriaIndex === c.criteriaIndex,
                                          'border border-[#E4E4E7]': selectedCriteria?.sectionIndex !== c.sectionIndex || selectedCriteria?.criteriaIndex !== c.criteriaIndex,
                                        })
                                      }
                                      onClick={() => {
                                        setMultipleUrlParameters(
                                          {tab: 'criteria', criteria: c.criterion}
                                        )
                                        if (carouselApi) {
                                          carouselCardClickRef.current = true // Track manual click
                                          carouselApi.scrollTo(i);
                                        }
                                      }}
                                      >
                                    <div className={
                                      cn('mr-2 flex justify-center items-center rounded-sm h-[12px] w-[12px] mt-1',{
                                        'bg-green-600': c.passed,
                                        'bg-red-600': !c.passed,
                                      })
                                    }>
                                    {
                                      c.passed ? (<Check className='text-white' size={16} />) : (<X className='text-white' size={16} />)
                                    }
                                    </div>
                                    <div className="text-left text-sm font-medium leading-5 line-clamp-2">
                                      {c.criterion}
                                    </div>
                                  </div>
                            
                              </TooltipTrigger>
                              <TooltipPortal>
                              <TooltipContent side="bottom" align='center' className=''>
                                  <div className='flex flex-col gap-1'>
                                  <div className='text-xm '>
                                      {c.criterion}
                                    </div>
                                    <div className='text-xs text-gray-500'>
                                      {c.sectionTitle}
                                    </div>
                                    
                                  </div>
                                
                              </TooltipContent>
                              </TooltipPortal>
                             
                            </Tooltip>
                          
                          
                        </CarouselItem>
                      )
                    })
                }
              </CarouselContent>
              {criteriaInfoList.length > 4 && <CarouselNext className='absolute right-[12px] top-1/2'
               onClick={() => {
                if (carouselApi) {
                  if(currentIndex + 4 > criteriaInfoList.length-1){ 
                    carouselApi.scrollTo(criteriaInfoList.length-1);
                  }else{
                    carouselApi.scrollTo(currentIndex + 4);
                  }
                }
              }}
              />}
          
            </Carousel>
            </TooltipProvider>
         </div>
        {/* HEADER */}
        <div className='flex items-center'>
            <div className={
                cn('w-full flex items-center rounded-sm h-[12px] w-[12px] mr-3',{
                    'bg-green-600': coachingInfo.passed,
                    'bg-red-600': !coachingInfo.passed,
                })
            }>
                <div className='flex items-center justify-center'>
                {coachingInfo.passed ? (
                      <Check className="text-white" size={12} />
                    ) : (
                      <X className=" text-white" size={12} />
                    )}
                </div>
            </div>
            <div className='w-full text-sm font-medium leading-5'>
                        {coachingInfo.criterion}
            </div>
        </div>
        <div className='text-xs text-[#71717A] ml-6 mt-2'>
                        {sectionInfo?.sectionTitle}
                    </div>
        <div className='flex flex-col h-full'>
        <div className='flex-1 relative'>
        <div className={
          cn('flex flex-col mt-4 pr-3 absolute overflow-y-auto left-0 right-0 top-0 bottom-0', {
          })
        }>

                 <motion.div
                    initial={{ opacity: 0, y: -50 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ y: -50 }}
                    transition={{
                      duration: 0.4,
                      delay: 0,
                    }}
                    
                    className={'mb-3'}
                  >
                    {/********** AI FEEDBACK ********/}
                    <div
                      className=""
                      style={{ zIndex: 0 }}
                    >
                      <div
                        className={
                          'rounded-lg' +
                          (animationStyles.animationContainer)
                        }
                      >
                        <div
                          className="p-3 border rounded-lg "
                          style={{
                            background: coachingInfo.coaching ? '#FCFEFE' : '#ffdbdb',
                            zIndex: 10,
                          }}
                        >
                          <div className="text-xs flex items-center text-[#2091AE]">
                            <div>
                              <Sparkles
                                size={14}
                                className={cn({
                                  'text-red-500': !coachingInfo.coaching,
                                  'text-[#2091AE]': coachingInfo.coaching,
                                })}
                              />
                            </div>
                            <div
                              className={cn('ml-2 font-medium', {
                                'text-red-500': !coachingInfo.coaching,
                              })}
                            >
                              {aiCoachTextOverride || 'Hyperbound AI'}
                              {!coachingInfo.coaching ? ' Disabled' : ''}
                            </div>
                          </div>
                          <div className="mt-4">
                            <div className="text-sm font-semibold">
                              Why were you scored this way?
                            </div>
                            <Markdown
                              remarkPlugins={[remarkGfm]}
                              rehypePlugins={[rehypeRaw]}
                              className="prose max-w-none mt-3 text-sm leading-snug w-full"
                            >
                              {coachingInfo.coaching ||
                                (canAccess(AppPermissions.MANAGE_SCORECARDS)
                                  ? 'Edit scorecard to enable personalized AI coaching feedback for this criteria'
                                  : 'Ask your admin to enable personalized AI coaching feedback for this criteria.')}
                            </Markdown>
                          </div>

                          {coachingInfo.improvement && (
                            <div className="mt-4">
                              <div className="text-sm font-semibold">
                                What could you do differently next time?
                              </div>
                              <Markdown
                                remarkPlugins={[remarkGfm]}
                                rehypePlugins={[rehypeRaw]}
                                className="prose max-w-none mt-3 text-sm leading-snug"
                              >
                                {coachingInfo.improvement}
                              </Markdown>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {/********** SIMILAR CALLS ************/}
                    {isLoggedIn ? isLoadingCoachingResources ? 
                    <div className='mt-4'>
                     <Skeleton className='w-full h-[40px] mb-3' />
                     <Skeleton className='w-full h-[40px] mb-3' /> 
                    </div>
                    : <>
                    {
                      coachingResources?.similarCalls && coachingResources?.similarCalls?.length > 0 &&
                      !isCompetitionOrg &&
                      !hideSimilarCalls && (
                        <div className="mt-4">
                          <div className="text-sm font-medium">
                            Similar calls to listen to:
                          </div>
                          <div className='grid grid-cols-2 gap-2'>
                          {coachingResources?.similarCalls?.map((sc: any, j: number) => {
                              const callLength = dayjs(sc.callEndedAt).diff(
                                dayjs(sc.callStartedAt),
                                'second',
                              );

                              const minutes = Math.floor(callLength / 60);
                              const seconds = callLength % 60;

                              const finalTime =
                                str_pad_left(minutes, '0', 2) +
                                ':' +
                                str_pad_left(seconds, '0', 2);

                              return (
                                <Link
                                  key={'sc-' + j}
                                  className="mt-2 flex items-center p-3 border rounded-lg text-muted-foreground"
                                  href={LinksManager.trainingCalls(
                                    `${sc.callVapiId}?tab=criteria&criteria=${coachingInfo?.criterion}`,
                                  )}
                                  target="_blank"
                                >
                                  <div className="mr-3">
                                    <Phone size={16} className='fill-current text-[#B8B8BC]' strokeWidth={1}/>
                                  </div>
                                  <div>
                                    <AgentAvatar
                                      className="w-7 h-7"
                                      agent={sc}
                                      fallbackTextOverride={`${sc?.firstName?.charAt(0)}`}
                                    />
                                  </div>
                                  <div className="text-normal text-black ml-2 font-medium">
                                    {sc?.firstName} {sc?.lastName}
                                  </div>
                                  <div className="text-muted-foreground ml-4 flex-1">
                                    {finalTime}
                                  </div>
                                  <div>
                                    {dayjs(sc.callStartedAt).format('MMM D')}
                                  </div>
                                  <div className="ml-2">
                                    <ExternalLink size={18} />
                                  </div>
                                </Link>
                              );
                            })}
                          </div>
                          <div>
                            
                          </div>
                        </div>
                      )
                    }
                    {
                      coachingResources?.learningMaterials && coachingResources?.learningMaterials?.length > 0 &&
                      <div className="mt-4">
                        <div className="text-sm font-medium mb-2">
                          Learning materials:
                        </div>
                       <div className="grid grid-cols-4 gap-2">
                        {coachingResources?.learningMaterials?.map((material: LearningMaterialDto) => {
                          return <MaterialCard key={material.id} material={material} />
                        })}
                       </div>
                      </div>
                    }
                    </>
                    : <></>
                    } 
                    
                  </motion.div>
                  </div>
                  </div>
                  </div>
            {/* {!dbOrg?.isCompetitionOrg && !scoringErrorMessage && (
          <div className="border border-yellow-400 hover:border-yellow-300 rounded-lg p-2">
            <div
              className="flex items-center cursor-pointer"
              onClick={toggleScoreIncorrectInfoPanel}
            >
              <div className="mr-3 text-yellow-400">
                <Lightbulb size={16} />
              </div>
              <div className="flex-1 text-sm">Incorrectly scored?</div>
              <div className="ml-1">
                <ChevronRight size={16} />
              </div>
            </div>
          </div>
        )} */}
          
          </>
        </div>
      </AnimatePresence>
    );
}
