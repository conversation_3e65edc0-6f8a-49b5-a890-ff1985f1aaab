import { useAggregateScoreBreakdownForSingleCallModal } from '@/common/AggregateScoreBreakdownModal';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useCallScorecard from '@/hooks/useCallScorecard';
import useCallStats from '@/hooks/useCallStats';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import { CallDto } from '@/lib/Call/types';
import { Lock, LockIcon } from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import Leaderboard from './leaderboard';
import CallSummaryQuestionsAndObjections from './questionsAndObjections';
import CallSummaryFeedback, { ISelectedCriteria } from './feedback';
import Link from 'next/link';
import ViewLearningMaterials from '@/common/Scorecards/LearningMaterials/View';
import { useIsInIframe } from '@/hooks/useIsInIframe';
import { TaskAndAttempts } from '@/lib/LearningModule/types';
import { useParams, useSearchParams } from 'next/navigation';
import { ScorecardConfigMaterialScope } from '@/lib/ScorecardConfig/types';
import CallAnalytics from './analytics';
import Coaching from './feedback/coaching';
import {
  ICriterion,
  ICriterionNavigation,
  ISection,
} from './feedback/scorecardSection';
interface IProps {
  isLoadingCall: boolean;
  call?: CallDto;
  rescoreCall: () => void;
  openTab?: string;
  onlyScorecard?: boolean;
  showLeaderboardDateFilter?: boolean;
  taskAndAttempts?: TaskAndAttempts;
}

export default function CallSummaryTabs({
  isLoadingCall,
  call,
  openTab,
  onlyScorecard = false,
  taskAndAttempts,
}: IProps) {
  const params = useParams();
  const vapiId = params.id as string;
  const tabListRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  const [indicatorLeft, setIndicatorLeft] = useState(0);
  const [indicatorWidth, setIndicatorWidth] = useState(0);
  const scorecardScrollContainerRef = useRef<HTMLDivElement>(null);
  const previousScorecardScrollRef = useRef<number>(0);
  const [shouldRestoreScroll, setShouldRestoreScroll] = useState(false);
  const {
    isLoggedIn,
    isCompetitionOrg,
    competitionTag,
    blurLeaderboard,
    canAccessScorecardsMaterials,
  } = useUserSession();
  const searchParams = useSearchParams()
  const { modal } = useAggregateScoreBreakdownForSingleCallModal(vapiId);

  const { setMultipleUrlParameters } = useRouting();
  const isInIframe = useIsInIframe();
  const criteriaParam = searchParams.get("criteria");
  const [currentTab, setCurrentTab] = useState<string>('feedback');

  const selectedCriteria = useMemo(() => {
    if(call?.scorecard?.criteria?.length && call?.scorecard?.criteria?.length > 0){
      for (let s = 0; s < call?.scorecard?.criteria?.length; s++) {
        const section = call.scorecard.criteria[s];
        for (let c = 0; c < section.criteria.length; c++) {
          if (section.criteria[c].criterion === criteriaParam) {
            return { sectionIndex: s, criteriaIndex: c };
          }
        }
      }
    }
    if(openTab === 'criteria'){
      return { sectionIndex: 0, criteriaIndex: 0 }; // fallback
    } else{
      return undefined // If not on criteria tab
    }
  }, [openTab, criteriaParam, call?.scorecard?.criteria]);

  useEffect(() => {
    if (openTab && openTab !== '') {
      setCurrentTab(openTab);
    } else {
      setCurrentTab('feedback');
    }
  }, [openTab]);

  useEffect(() => {
    const el = tabRefs.current[currentTab];
    if (el) {
      setIndicatorLeft(el.offsetLeft);
      setIndicatorWidth(el.offsetWidth);
    }
  }, [currentTab, canAccessScorecardsMaterials]);

  /*****************************/
  /*********** INIT ************/
  /*****************************/

  const {
    data: stats,
    isLoading: isLoadingStats,
    isFetched: isStatsFetched,
  } = useCallStats(vapiId || '', !isLoggedIn, !isLoadingCall);

  const { data: scorecard, isLoading: isLoadingScorecard } = useCallScorecard(
    vapiId || '',
    !isLoggedIn,
    isStatsFetched && !isLoadingCall,
  );

  useEffect(() => {
    if (scorecard && !isLoadingScorecard && isInIframe) {
      const scormParams: Record<string, string> = {};

      const minScore = 0;
      const maxScore = 100;
      const rawScore = Number(
        (scorecard?.passedScore / scorecard?.totalScore) * 100 || 0,
      );

      scormParams['cmi.score.min'] = minScore.toString();
      scormParams['cmi.score.max'] = maxScore.toString();
      scormParams['cmi.score.raw'] = `${Number(
        (scorecard?.passedScore / scorecard?.totalScore) * 100 || 0,
      )}`;

      if (taskAndAttempts) {
        const isCurrentAttemptApproved =
          rawScore >= Number(taskAndAttempts.task.minScorecardScore) ||
          taskAndAttempts.task.minScorecardScore === 0;
        const necessarySuccessfulAttempts =
          taskAndAttempts.task.minNumberOfAttempts || 1;

        const attempts = taskAndAttempts.attempts || [];
        const successfulAttempts = attempts.filter((a) => a.passed).length;
        const isApproved =
          successfulAttempts + (isCurrentAttemptApproved ? 1 : 0) >=
          necessarySuccessfulAttempts;

        const isCompleted = taskAndAttempts.task.maxNumberOfAttempts
          ? attempts.length + 1 >= taskAndAttempts.task.maxNumberOfAttempts
          : isApproved;

        const completionStatus = isCompleted ? 'completed' : 'incomplete';
        const successStatus = isApproved ? 'passed' : 'failed';

        scormParams['cmi.completion_status'] = completionStatus;

        if (isCompleted) {
          scormParams['cmi.success_status'] = successStatus;
        }
      } else {
        scormParams['cmi.completion_status'] = 'completed';
        scormParams['cmi.success_status'] =
          rawScore >= 80 ? 'passed' : 'failed';
      }

      window.parent?.postMessage(
        {
          type: 'SCORECARD_LOADED',
          scormParams,
        },
        '*',
      );
    }
  }, [scorecard, isLoadingScorecard, isInIframe, taskAndAttempts]);

  /*****************************/
  /********* ACTIONS ***********/
  /*****************************/

  const onTabChange = (t: string) => {
    setCurrentTab(t);
    setMultipleUrlParameters({tab: t});
  };

  useEffect(() => {
    if (openTab === 'feedback' || openTab === '' && shouldRestoreScroll) {
      requestAnimationFrame(() => {
        if (scorecardScrollContainerRef.current) {
          scorecardScrollContainerRef.current.scrollTop =
            previousScorecardScrollRef.current;
        }
      });
      setShouldRestoreScroll(false);
    }
  }, [openTab, shouldRestoreScroll]);
  const triggersOtherThanScorecards = (
    <>
      <TabsTrigger
        value="analytics"
        onClick={() => onTabChange('analytics')}
        className="px-10 py-2 hover:text-[#1A829D] text-muted-foreground border-b border-transparent shadow-none rounded-none data-[state=active]:text-[#1A829D] data-[state=active]:shadow-none"
        ref={(el) => {
          tabRefs.current['analytics'] = el;
        }}
      >
        Analytics
      </TabsTrigger>

      {isCompetitionOrg ? (
        <Link
          href={`/competitions/${competitionTag}/leaderboard?tab=${call?.agentId}`}
        >
          {isLoggedIn && (
            <TabsTrigger
              value="leaderboard"
              onClick={() => onTabChange('leaderboard')}
              className="px-10 py-2 hover:text-[#1A829D] text-muted-foreground border-b border-transparent shadow-none rounded-none data-[state=active]:text-[#1A829D] data-[state=active]:shadow-none"
              ref={(el) => {
                tabRefs.current['leaderboard'] = el;
              }}
            >
              Leaderboard
            </TabsTrigger>
          )}
        </Link>
      ) : (
        <>
          {isLoggedIn && (
            <TabsTrigger
              value="leaderboard"
              disabled={blurLeaderboard}
              onClick={
                blurLeaderboard ? undefined : () => onTabChange('leaderboard')
              }
              ref={(el) => {
                tabRefs.current['leaderboard'] = el;
              }}
              className="px-10 py-2 hover:text-[#1A829D] text-muted-foreground border-b border-transparent shadow-none rounded-none data-[state=active]:text-[#1A829D] data-[state=active]:shadow-none"
            >
              {blurLeaderboard ? (
                <LockIcon size={12} className="mr-1" />
              ) : (
                <></>
              )}
              Leaderboard
            </TabsTrigger>
          )}
        </>
      )}
      {!isLoggedIn && (
        <TabsTrigger
          value="dummy2"
          className="px-10 py-2 hover:text-[#1A829D] text-muted-foreground border-b border-transparent shadow-none rounded-none data-[state=active]:text-[#1A829D] data-[state=active]:shadow-none"
          ref={(el) => {
            tabRefs.current['dummy2'] = el;
          }}
        >
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger asChild className="opacity-[0.5]">
                <div className="flex items-center">
                  {/* <Trophy size={12} className="mr-1" /> */}
                  Leaderboard <Lock size={12} className="ml-2" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                Book a demo to access leaderboard
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </TabsTrigger>
      )}

      <TabsTrigger
        value="objections"
        onClick={() => onTabChange('objections')}
        className="px-10 py-2 hover:text-[#1A829D] text-muted-foreground border-b border-transparent shadow-none rounded-none data-[state=active]:text-[#1A829D] data-[state=active]:shadow-none"
        ref={(el) => {
          tabRefs.current['objections'] = el;
        }}
      >
        {/* <AlignLeft size={12} className="mr-1" /> */}
        Objections
      </TabsTrigger>

      {/* {call?.relatedGatekeeperCall && (
        <TabsTrigger
          value="gatekeeper"
          onClick={() => onTabChange('gatekeeper')}
          ref={(el) => {
            tabRefs.current['gatekeeper'] = el;
          }}
          className='px-10 py-2 text-muted-foreground border-b border-transparent shadow-none rounded-none data-[state=active]:text-[#1A829D] data-[state=active]:shadow-none'
        >
          Gatekeeper
        </TabsTrigger>
      )} */}
      {canAccessScorecardsMaterials && (
        <TabsTrigger
          value="learning-materials"
          onClick={() => onTabChange('learning-materials')}
          ref={(el) => {
            tabRefs.current['learning-materials'] = el;
          }}
          className="px-10 py-2 hover:text-[#1A829D] text-muted-foreground border-b border-transparent shadow-none rounded-none data-[state=active]:text-[#1A829D] data-[state=active]:shadow-none"
        >
          {/* <BookMarked size={12} className="mr-1" /> */}
          Resources
        </TabsTrigger>
      )}
    </>
  );

  /*****************************/
  /********* RENDER ************/
  /*****************************/
  const getCriteriaInfoList = (criteriaInfo: ISection[]) => {
    const criteriaInfoList: ICriterionNavigation[] = [];
    let initialIndex = 0;
    criteriaInfo.forEach((section: ISection, sectionIndex: number) => {
      section.criteria.forEach(
        (criteria: ICriterion, criteriaIndex: number) => {
          const sectionCriteria = {
            ...criteria,
            sectionIndex: sectionIndex,
            criteriaIndex: criteriaIndex,
            sectionTitle: section.sectionTitle,
          };
          criteriaInfoList.push(sectionCriteria);
          if (
            sectionIndex === selectedCriteria?.sectionIndex &&
            criteriaIndex === selectedCriteria?.criteriaIndex
          ) {
            initialIndex = criteriaInfoList.length - 1;
          }
        },
      );
    });
    return { criteriaInfoList, initialIndex };
  };

  const { criteriaInfoList, initialIndex } = useMemo(
    () => getCriteriaInfoList(call?.scorecard?.criteria || []),
    [call?.scorecard?.criteria, selectedCriteria, criteriaParam],
  );

  return (
    <div className="flex flex-col pt-3 pl-3 h-full">
      {selectedCriteria ? (
        <>
          <Coaching
            agentId={call?.agentId || 0}
            finalCallScore={call?.scorecard?.finalCallScore || 0}
            selectedCriteria={selectedCriteria}
            coachingInfo={
              call?.scorecard.criteria[selectedCriteria.sectionIndex].criteria[
                selectedCriteria.criteriaIndex
              ]
                ? call?.scorecard.criteria[selectedCriteria.sectionIndex]
                    .criteria[selectedCriteria.criteriaIndex]
                : {
                    criterion: '',
                    passed: false,
                    explanation: '',
                    coaching: '',
                    improvement: '',
                  }
            }
            sectionInfo={
              call?.scorecard.criteria[selectedCriteria.sectionIndex]
            }
            scorecardConfigId={call?.scorecard?.scorecardConfigId || 0}
            criteriaInfoList={criteriaInfoList}
            initialIndex={initialIndex}
          />
        </>
      ) : (
        <Tabs
          defaultValue={currentTab}
          value={currentTab}
          className="flex flex-col w-full h-full"
        >
          <div className="flex items-center pr-3">
            <div className="flex-1">
              <TabsList
                ref={tabListRef}
                className="relative w-full p-0 rounded-none bg-[#FFFFFF] flex-wrap justify-between after:content-[''] after:absolute after:left-0 after:right-0 after:h-[1px] 
             after:bg-[#E4E4E7] after:bottom-[-6px]"
              >
                <TabsTrigger
                  value="feedback"
                  onClick={() => onTabChange('feedback')}
                  ref={(el) => {
                    tabRefs.current['feedback'] = el;
                  }}
                  className="px-10 hover:text-[#1A829D] text-muted-foreground border-b border-transparent shadow-none rounded-none data-[state=active]:text-[#1A829D] data-[state=active]:shadow-none"
                >
                  Feedback
                </TabsTrigger>

                {!onlyScorecard && triggersOtherThanScorecards}
                <div
                  className="absolute z-10 bottom-[-6px] h-[2px] bg-[#1A829D] transition-all duration-300 ease-in-out"
                  style={{ width: indicatorWidth, left: indicatorLeft }}
                />
              </TabsList>
            </div>
          </div>

          <TabsContent value="feedback" className="h-full relative">
            <CallSummaryFeedback
              isLoadingCall={isLoadingCall}
              call={call}
              className="flex flex-col h-full relative"
              scorecardScrollContainerRef={scorecardScrollContainerRef}
              previousScorecardScrollRef={previousScorecardScrollRef}
              setShouldRestoreScroll={setShouldRestoreScroll}
            />
          </TabsContent>
          <TabsContent value="analytics" className="h-full relative">
            <CallAnalytics
              isLoading={isLoadingStats}
              stats={stats}
              agentId={call?.agent?.id || 0}
              callerId={call?.callerId || 0}
              className="flex flex-col h-full relative pr-3"
            />
          </TabsContent>
          <TabsContent value="objections" className="h-full relative">
            <CallSummaryQuestionsAndObjections
              call={call}
              className="flex flex-col h-full relative"
            />
          </TabsContent>
          <TabsContent value="leaderboard" className="h-full relative">
            <Leaderboard
              agentId={call?.agent?.id}
              className="flex flex-col h-full relative pr-3"
              showLeaderboardDateFilter={true}
            />
          </TabsContent>
          <TabsContent value="learning-materials" className="h-full relative">
            {scorecard && scorecard?.scorecardConfigId && (
              <ViewLearningMaterials
                scorecardConfigId={scorecard?.scorecardConfigId}
                scope={ScorecardConfigMaterialScope.SCORECARD}
              />
            )}
          </TabsContent>
          <div className="" />
          {modal}
        </Tabs>
      )}
    </div>
  );
}
