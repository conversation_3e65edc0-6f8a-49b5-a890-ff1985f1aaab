import { Button } from '@/components/ui/button';
import useCallObjections from '@/hooks/useCallObjections';
import useCallQuestions from '@/hooks/useCallQuestions';
import useUserSession from '@/hooks/useUserSession';
import { CallDto } from '@/lib/Call/types';
import LinksManager from '@/lib/linksManager';
import { cn, formatDuration } from '@/lib/utils';
import { ChevronDown, ChevronUp, ExternalLink, Info } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';
import { DemoInboundFormResponseDto } from '@/lib/Demo/types';
import { Skeleton } from '@/components/ui/skeleton';
import AgentAvatar from '@/components/Avatars/Agent';
import { parseMultiSpeakerMessage } from '../../transcript/utils';

interface IProps {
  className?: string;
  call?: CallDto;
}

export default function CallSummaryQuestionsAndObjections({
  className,
  call,
}: IProps) {
  const { isLoggedIn } = useUserSession();

  const callMetadata = call?.providerMetadata
    ? call?.providerMetadata
    : call?.vapiMetadata;
  const callId = call?.providerCallId
    ? call?.providerCallId
    : call?.vapiId || '';

  const agent: AgentDto | undefined = call?.agent;
  const caller: UserDto | undefined = call?.caller;
  const demoInboundFormResponse: DemoInboundFormResponseDto | undefined =
    call?.demoInboundFormResponse;
  const callerFirstName =
    caller?.firstName || demoInboundFormResponse?.name?.split(' ')[0] || '';
  const callerLastName =
    caller?.lastName || demoInboundFormResponse?.name?.split(' ')[1] || '';

  const [clampSummary, setClampSummary] = useState(true);

  const {
    data: questions,
    isLoading: isLoadingQuestions,
    isSuccess: isQuestionsSuccess,
    isFetched: isQuestionsFetched,
  } = useCallQuestions(callId, !isLoggedIn, true);

  const { data: objections, isLoading: isLoadingObjections } =
    useCallObjections(callId, !isLoggedIn, isQuestionsFetched);

  const findAgent = (secondsFromStart: number, objection: string) => {
    if (agent?.supportingAgentInfo && agent?.supportingAgentInfo?.length > 0) {
      const message = callMetadata?.messages?.find(
        (m) => m.secondsFromStart === secondsFromStart,
      );
      // If message is not found, return agent
      if (!message) {
        return agent;
      } else {
        // If message is found, return the right agent
        const parsedMessage = parseMultiSpeakerMessage(message.message);
        // Find out which speaker mentioned this objection
        const speaker = parsedMessage.find((m) =>
          m.message.includes(objection),
        );
        if (agent.firstName.toLowerCase() === speaker?.speaker.toLowerCase()) {
          return agent;
        } else {
          const supportingAgent = agent?.supportingAgentInfo?.find(
            (a) => a.firstName.toLowerCase() === speaker?.speaker.toLowerCase(),
          );
          if (supportingAgent) {
            return supportingAgent;
          } else {
            return agent;
          }
        }
      }
    } else {
      return agent;
    }
  };

  return (
    <AnimatePresence>
      <div className={className}>
        <div
          className={
            'flex flex-col overflow-y-auto mt-4 pr-3 absolute left-0 right-0 top-0 bottom-0'
          }
        >
          {callMetadata?.summary && callMetadata?.transcript?.length > 0 && (
            <div className="w-full">
              <div className="text-normal mb-2 font-semibold">Summary</div>
              <div>
                <div className={cn('', { 'line-clamp-2': clampSummary })}>
                  {callMetadata?.summary}
                </div>
                <Button
                  variant={'link'}
                  className="font-semibold p-0 text-sm text-muted-foreground hover:no-underline"
                  onClick={() => {
                    setClampSummary(!clampSummary);
                  }}
                >
                  <div className="text-xs flex items-center text-muted-foreground mt-2">
                    <div>{clampSummary ? 'Show more' : 'Show less'}</div>
                    <div>
                      {clampSummary ? (
                        <ChevronDown size={16} />
                      ) : (
                        <ChevronUp size={16} />
                      )}
                    </div>
                  </div>
                </Button>
              </div>
            </div>
          )}

          <div className="flex items-center text-muted-foreground my-6 text-xs">
            <Info size={16} className="mr-2" /> We&apos;re always working on
            improving the accuracy of objection detection and handling.
          </div>

          {/********************************/}
          {/********* OBJECTIONS ***********/}
          {/********************************/}
          <div className="pt-2">
            <h3 className="text-normal font-semibold">
              Buyer objections and rep responses...
            </h3>

            {isLoadingObjections && (
              <div className="mt-2">
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
              </div>
            )}

            {(objections || [])?.length > 0 && !isLoadingObjections ? (
              <div className="mt-4 space-y-8 mr-10 w-full">
                {objections?.map((objection, i: number) => {
                  return (
                    <div key={objection.objection}>
                      <motion.div
                        initial={{ opacity: 0, y: -50 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ y: -50 }}
                        transition={{
                          duration: 0.4,
                          delay: i * 0.1,
                        }}
                      >
                        <Alert>
                          <AlertDescription className="flex items-center space-x-2">
                            <AgentAvatar
                              className="w-8 h-8 mr-2"
                              agent={findAgent(
                                objection.secondsFromStart,
                                objection.objection,
                              )}
                            />

                            <Badge variant={'secondary'}>
                              {formatDuration(
                                objection.secondsFromStart * 1000,
                              )}
                            </Badge>
                            <p>{objection.objection}</p>
                          </AlertDescription>
                        </Alert>
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0, y: -50 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ y: -50 }}
                        transition={{
                          duration: 0.4,
                          delay: i * 0.2,
                        }}
                        className="ml-10"
                      >
                        <Alert className="mt-2">
                          <AlertDescription className="flex items-center space-x-2">
                            <Avatar className="w-8 h-8 mr-1">
                              {caller?.avatar && (
                                <AvatarImage src={caller?.avatar} />
                              )}
                              <AvatarFallback className="text-sm">
                                {callerFirstName?.charAt(0) || ''}
                                {callerLastName?.charAt(0) || ''}
                              </AvatarFallback>
                            </Avatar>
                            <Badge variant={'secondary'}>
                              {formatDuration(
                                objection.secondsFromStart * 1000,
                              )}
                            </Badge>
                            <p>{objection.response}</p>
                          </AlertDescription>
                        </Alert>
                      </motion.div>
                      {/* <Separator className="my-6" /> */}
                    </div>
                  );
                })}
              </div>
            ) : (
              <h3 className="text-muted-foreground mt-2">
                No objections identified
              </h3>
            )}
          </div>

          {/********************************/}
          {/********* QUESTIONS ************/}
          {/********************************/}
          <div className="pt-8 mb-10">
            <h3 className="text-normal font-semibold">Rep asked...</h3>

            {isLoadingQuestions && (
              <div className="mt-2">
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
              </div>
            )}
            {(questions || [])?.length > 0 && !isLoadingQuestions ? (
              <div className="mt-4 space-y-2">
                {questions?.map((question, i: number) => {
                  return (
                    <motion.div
                      key={question.question}
                      initial={{ opacity: 0, y: -50 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ y: -50 }}
                      transition={{
                        duration: 0.4,
                        delay: i * 0.1,
                      }}
                    >
                      <Alert>
                        <AlertDescription className="flex items-center space-x-2">
                          <Avatar className="w-8 h-8 mr-1">
                            {caller?.avatar && (
                              <AvatarImage src={caller?.avatar} />
                            )}
                            <AvatarFallback className="text-sm">
                              {callerFirstName?.charAt(0) || ''}
                              {callerLastName?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <Badge variant={'secondary'}>
                            {formatDuration(question.secondsFromStart * 1000)}
                          </Badge>
                          <p>{question.question}</p>
                        </AlertDescription>
                      </Alert>
                    </motion.div>
                  );
                })}
              </div>
            ) : (
              <h3 className="text-muted-foreground  mt-2">
                No questions identified
              </h3>
            )}
          </div>
        </div>
      </div>
    </AnimatePresence>
  );
}
