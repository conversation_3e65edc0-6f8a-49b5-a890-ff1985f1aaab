.animationContainer {
  position: relative;
  overflow: hidden;
}

.animationContainer::before {
  content: '';
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  width: 999px;
  height: 999px;
  background: conic-gradient(rgba(0, 0, 0, 0), #2091ae, rgba(0, 0, 0, 0) 25%);
  animation: shadow-rotate 1s linear 1;
  opacity: 0;
}

@keyframes shadow-rotate {
  0% {
    transform: rotate(180deg);
    opacity: 0;
  }

  0.001% {
    transform: rotate(180deg);
    opacity: 1;
  }

  99.999% {
    transform: rotate(360deg);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg);
    opacity: 0;
  }
}
