import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import useUserSession from '@/hooks/useUserSession';
import { RepsCanEditScoreResults } from '@/lib/Organization/types';
import { XCircleIcon } from 'lucide-react';
import Image from 'next/image';

interface IProps {
  open: boolean;
  setModalOpen: (modalOpen: boolean) => void;
}

export default function IncorrectlyScoredModal({ open, setModalOpen }: IProps) {
  const { dbOrg, isAdmin } = useUserSession();

  return (
    <Dialog open={open} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Incorrectly scored?
          </DialogTitle>
        </DialogHeader>
        <div className="overflow-hidden">
          {dbOrg?.repsCanEditScoreResults ===
            RepsCanEditScoreResults.DISPUTE_ONLY && !isAdmin ? (
            <div className="mt-2 flex items-top mb-2">
              <div>You can dispute scores by clicking on</div>
              <XCircleIcon className="w-4 h-4 text-red-600 mx-1 inline" />
              <div>next to scorecard criteria below.</div>
            </div>
          ) : (
            <div className="mt-2 mb-2">
              You can edit incorrect scores by clicking on{' '}
              <XCircleIcon className="w-4 h-4 text-red-600 mx-1 inline" /> next
              to scorecard criteria below.
            </div>
          )}

          <Image
            src="/incorrect-score-overwrite.gif"
            width={400}
            alt="report wrong sscore"
            height={200}
            className="rounded-lg"
          />

          <div className="mt-2 flex items-center">
            <div className="flex-1">
              Thanks for your help, our scorecards improve with your feedback!
            </div>
          </div>
        </div>
        <DialogFooter className="flex items-center">
          <Button
            className="ml-2 flex items-center"
            variant={'default'}
            onClick={() => {
              setModalOpen(false);
            }}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
