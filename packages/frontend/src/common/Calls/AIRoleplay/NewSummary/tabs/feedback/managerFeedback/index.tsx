import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { AutoSizeTextarea } from '@/components/ui/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useIsLoggedIn } from '@/hooks/useIsLoggedIn';
import useUserSession from '@/hooks/useUserSession';
import CallService from '@/lib/Call';
import { CallCommentDto, CallDto } from '@/lib/Call/types';
import { cn, timeAgo } from '@/lib/utils';
import { useQueryClient } from '@tanstack/react-query';
import { AnimatePresence, motion } from 'framer-motion';
import { Loader2Icon, Pencil, Trash2 } from 'lucide-react';
import { useMemo, useState } from 'react';

export default function ManagerFeedback({ call }: { call: CallDto }) {
  const { isAdmin, dbUser, isObserver } = useUserSession();

  const queryClient = useQueryClient();
  const isLoggedIn = useIsLoggedIn();
  const [openDeleteComment, setOpenDeleteComment] = useState<boolean>(false);
  const [commentContent, setCommentContent] = useState<string>('');
  const [writingComment, setWritingComment] = useState<boolean>(false);
  const [editingComment, setEditingComment] = useState<CallCommentDto | null>(
    null,
  );
  const [isSubmittingComment, setIsSubmittingComment] =
    useState<boolean>(false);
  const [isDeletingComment, setIsDeletingComment] = useState<boolean>(false);

  const { comments, callId } = useMemo(() => {
    return {
      callId: call?.providerCallId
        ? call?.providerCallId
        : call?.vapiId
          ? call?.vapiId
          : '',
      comments: call?.comments || [],
    };
  }, [call]);

  const handleEditComment = (comment: CallCommentDto) => {
    setEditingComment(comment);
    setCommentContent(comment.content);
  };

  const handleDeleteComment = async (commentId: number) => {
    try {
      setIsDeletingComment(true);
      await CallService.deleteCallComment(callId, commentId);

      await queryClient.invalidateQueries({
        queryKey: ['call', !isLoggedIn, callId],
      });
      setCommentContent('');
      setWritingComment(false);
    } catch (error) {
      console.error('Error deleting comment:', error);
    } finally {
      setOpenDeleteComment(false);
      setTimeout(() => {
        setIsDeletingComment(false);
      }, 300);
    }
  };

  const handleSubmitComment = async () => {
    console.log('handleSubmitComment', commentContent);
    if (!commentContent.trim()) return;

    try {
      setIsSubmittingComment(true);
      if (editingComment) {
        await CallService.updateCallComment(
          callId,
          editingComment.id,
          commentContent,
        );
        setEditingComment(null);
      } else {
        await CallService.createCallComment(callId, commentContent);
      }

      await queryClient.invalidateQueries({
        queryKey: ['call', !isLoggedIn, callId],
      });
    } catch (error) {
      console.error('Error saving comment:', error);
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const renderTextWithLinks = (text: string) => {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const parts = text.split(urlRegex);

    return parts.map((part, index) => {
      if (urlRegex.test(part)) {
        return (
          <a
            key={index}
            href={part}
            target="_blank"
            rel="noopener noreferrer"
            className="text-[#3DC3E6] hover:text-[#2BA8C8] underline break-all"
          >
            {part}
          </a>
        );
      }
      return part;
    });
  };

  if (!callId || (!isAdmin && !isObserver && comments?.length === 0)) {
    return null;
  }

  return (
    <div>
      {(comments && comments.length > 0) || writingComment ? (
        <>
          <div className="text-[#09090B] flex items-center gap-2 text-[14px] leading-[135%] font-medium mb-2">
            <div className="tracking-tight">Manager Feedback</div>
            {comments?.[0]?.userId === dbUser?.id && (
              <div
                className={cn(
                  'flex items-center',
                  isSubmittingComment || isDeletingComment
                    ? 'pointer-events-none opacity-50'
                    : '',
                )}
              >
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={'ghost'}
                        onClick={() => handleEditComment(comments?.[0])}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        <Pencil
                          size={16}
                          className="text-[#71717A] hover:text-[#2E3035]"
                        />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit feedback</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                {!openDeleteComment ? (
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant={'ghost'}
                          onClick={() => {
                            setOpenDeleteComment(true);
                          }}
                          className="rounded-full p-2 text-muted-foreground"
                        >
                          <Trash2 size={16} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Remove feedback</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <Popover
                    open={openDeleteComment}
                    onOpenChange={setOpenDeleteComment}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant={'ghost'}
                        onClick={() => {
                          setOpenDeleteComment(true);
                        }}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[360px]">
                      <div className="flex flex-1 flex-col gap-4">
                        <div className="text-sm">
                          Are you sure you want to remove this feedback?
                        </div>
                        <div className="flex flex-1 gap-2 justify-center">
                          <Button
                            variant="ghost"
                            disabled={isDeletingComment}
                            onClick={() => setOpenDeleteComment(false)}
                          >
                            Cancel
                          </Button>
                          <Button
                            variant="destructive"
                            onClick={() =>
                              handleDeleteComment(comments?.[0]?.id)
                            }
                            disabled={isDeletingComment}
                          >
                            {isDeletingComment ? 'Removing' : 'Yes, remove'}
                            {isDeletingComment && (
                              <AnimatePresence>
                                <motion.div
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  exit={{ opacity: 0, x: -20 }}
                                  transition={{ duration: 0.3 }}
                                >
                                  <Loader2Icon className="animate-spin ml-2" />
                                </motion.div>
                              </AnimatePresence>
                            )}
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                )}
              </div>
            )}
          </div>
          <div className="">
            {comments.map((comment) => (
              <div key={comment.id}>
                <div className="leading-relaxed">
                  {renderTextWithLinks(comment.content)}
                </div>
                <div className="flex items-start justify-between mt-2 mb-4">
                  <div className="flex items-center gap-2">
                    <div className=" text-muted-foreground">Submitted by</div>
                    <Avatar className="w-5 h-5">
                      <AvatarImage src={comment.user?.avatar} />
                      <AvatarFallback className="text-xs">
                        {comment.user?.firstName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>

                    <span className="text-sm font-medium text-[#2E3035]">
                      {comment.user?.firstName} {comment.user?.lastName}
                    </span>
                    <div className="text-muted-foreground">
                      {timeAgo(comment.updatedAt)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="flex items-center justify-between w-full bg-[#FBFBFB] border border-[#E4E4E7] rounded-lg p-3">
          <div className="text-[#2E3035] text-sm font-medium leading-5">
            Add feedback to this call for {call?.caller?.firstName}{' '}
            {call?.caller?.lastName}
          </div>
          <div>
            <Button
              variant="outline"
              className="bg-[#FFFFFF]"
              onClick={() => setWritingComment(true)}
            >
              Add Feedback
            </Button>
          </div>
        </div>
      )}
      <></>
      {((isAdmin || isObserver) && comments?.length === 0) || editingComment ? (
        <div
          className={cn(
            'mb-4',
            isSubmittingComment || isDeletingComment
              ? 'pointer-events-none opacity-50'
              : '',
          )}
        >
          {editingComment && (
            <div className="mb-3 p-2 bg-[#F4F4F5] rounded-lg text-sm text-[#71717A]">
              Editing comment by {editingComment.user?.firstName}{' '}
              {editingComment.user?.lastName}
            </div>
          )}
          {editingComment || writingComment ? (
            <div>
              <div className="relative px-1">
                <AutoSizeTextarea
                  value={commentContent}
                  onChange={(e) => setCommentContent(e.target.value)}
                  className="w-full border border-[#E4E4E7] min-h-[76px] rounded-[8px] overflow-visible"
                  placeholder={'Add your feedback here...'}
                ></AutoSizeTextarea>
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  variant="ghost"
                  className="mr-2"
                  onClick={() => {
                    setWritingComment(false);
                    setEditingComment(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSubmitComment}
                  disabled={
                    !commentContent.trim() ||
                    commentContent.length > 2000 ||
                    isSubmittingComment
                  }
                  className="items-center justify-center transition-all duration-300"
                >
                  {isSubmittingComment ? 'Saving...' : 'Save'}
                  {isSubmittingComment && (
                    <AnimatePresence>
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Loader2Icon className="animate-spin ml-2" />
                      </motion.div>
                    </AnimatePresence>
                  )}
                </Button>
              </div>
            </div>
          ) : null}
        </div>
      ) : null}
    </div>
  );
}
