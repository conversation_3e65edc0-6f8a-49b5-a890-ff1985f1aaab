import { CallStatsDto } from '@/lib/Call/types';
import { motion } from 'framer-motion';
import CallStatCard from './statsCard';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from '@/components/ui/chart';
import { Line<PERSON>hart, Line, XAxis, CartesianGrid, LabelList } from 'recharts';
import dayjs from 'dayjs';
import { ArrowRightIcon } from 'lucide-react';
import Link from 'next/link';
import useAgentCallerPastPerformance from '@/hooks/useAgentCallerPastPerformance';
import { getColorFromScore } from '../feedback';
import useUserSession from '@/hooks/useUserSession';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';

function formatDurationDisplay(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  const minutesDisplay = minutes >= 10 ? `${minutes}` : `0${minutes}`;
  const secondsDisplay =
    remainingSeconds >= 10 ? `${remainingSeconds}` : `0${remainingSeconds}`;

  return `${minutesDisplay}:${secondsDisplay}`;
}

interface IProps {
  isLoading: boolean;
  className?: string;
  stats?: CallStatsDto;
  agentId: number;
  callerId: number;
}

const chartConfig = {
  finalCallScore: {
    label: 'Score',
    color: 'var(--chart-1)',
  },
} satisfies ChartConfig;

export default function CallAnalytics({
  className,
  isLoading,
  stats,
  agentId,
  callerId,
}: IProps) {
  const { isLoggedIn } = useUserSession();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const isDemo = isLoggedIn ? false : true;
  const finalCallerId = isLoggedIn ? callerId : Number(hbDemoInboundForm?.id);
  const { data: chartData, isLoading: isLoadingChartData } =
    useAgentCallerPastPerformance(agentId, finalCallerId, isDemo);
  const talkListenRatio = `${Math.round(
    (stats?.talkListenRatio?.value || 0) * 100,
  )}%`;
  const longestMonologue = formatDurationDisplay(
    stats?.longestMonologue?.value || 0,
  );

  return (
    <div className={className}>
      <div className="grid grid-cols-2 gap-x-1 gap-y-1 mt-4">
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
          transition={{
            duration: 0.4,
            delay: 0,
          }}
          className={cn('p-3 rounded-lg', {
            'bg-[#F6FCFC]': stats?.talkListenRatio?.value
              ? stats?.talkListenRatio?.value >=
                  stats?.talkListenRatio?.recommendedRange?.[0] &&
                stats?.talkListenRatio?.value <=
                  stats?.talkListenRatio?.recommendedRange?.[1]
              : false,
            'bg-[#FCF5F5]': stats?.talkListenRatio?.value
              ? stats?.talkListenRatio?.value <
                  stats?.talkListenRatio?.recommendedRange?.[0] ||
                (stats?.talkListenRatio?.value &&
                  stats?.talkListenRatio?.value >
                    stats?.talkListenRatio?.recommendedRange?.[1])
              : true,
          })}
        >
          <CallStatCard
            title="Talk/Listen Ratio"
            description="How much you talked vs. how much you listened"
            isLoading={isLoading}
            value={talkListenRatio}
            rawValue={(stats?.talkListenRatio?.value || 0) * 100}
            recommendedRange={
              stats?.talkListenRatio?.recommendedRange?.map((r) =>
                Math.round(r * 100),
              ) || [0, 0]
            }
          />
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
          transition={{
            duration: 0.4,
            delay: 0.2,
          }}
          className={cn('p-3 rounded-lg', {
            'bg-[#F6FCFC]': stats?.fillerWords?.value
              ? stats?.fillerWords?.value >=
                  stats?.fillerWords?.recommendedRange?.[0] &&
                stats?.fillerWords?.value <=
                  stats?.fillerWords?.recommendedRange?.[1]
              : false,
            'bg-[#FCF5F5]': stats?.fillerWords?.value
              ? stats?.fillerWords?.value <
                  stats?.fillerWords?.recommendedRange?.[0] ||
                (stats?.fillerWords?.value &&
                  stats?.fillerWords?.value >
                    stats?.fillerWords?.recommendedRange?.[1])
              : true,
          })}
        >
          <CallStatCard
            title="Filler Words"
            description="How many filler words like 'um' and 'uh' you used"
            isLoading={isLoading}
            value={`${Number(stats?.fillerWords?.value || 0).toFixed(2)} wpm`}
            rawValue={stats?.fillerWords?.value || 0}
            recommendedRange={stats?.fillerWords?.recommendedRange || [0, 0]}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
          transition={{
            duration: 0.4,
            delay: 0.3,
          }}
          className={cn('p-3 rounded-lg', {
            'bg-[#F6FCFC]': stats?.talkSpeed?.value
              ? stats?.talkSpeed?.value >=
                  stats?.talkSpeed?.recommendedRange?.[0] &&
                stats?.talkSpeed?.value <=
                  stats?.talkSpeed?.recommendedRange?.[1]
              : false,
            'bg-[#FCF5F5]': stats?.talkSpeed?.value
              ? stats?.talkSpeed?.value <
                  stats?.talkSpeed?.recommendedRange?.[0] ||
                (stats?.talkSpeed?.value &&
                  stats?.talkSpeed?.value >
                    stats?.talkSpeed?.recommendedRange?.[1])
              : true,
          })}
        >
          <CallStatCard
            title="Talk Speed"
            description="How fast you spoke in words per minute"
            isLoading={isLoading}
            rawValue={stats?.talkSpeed?.value || 0}
            value={`${Math.round(stats?.talkSpeed?.value || 0)} wpm`}
            recommendedRange={stats?.talkSpeed?.recommendedRange || [0, 0]}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
          transition={{
            duration: 0.4,
            delay: 0.4,
          }}
          className={cn('p-3 rounded-lg', {
            'bg-[#F6FCFC]': stats?.longestMonologue?.value
              ? stats?.longestMonologue?.value >=
                  stats?.longestMonologue?.recommendedRange?.[0] &&
                stats?.longestMonologue?.value <=
                  stats?.longestMonologue?.recommendedRange?.[1]
              : false,
            'bg-[#FCF5F5]': stats?.longestMonologue?.value
              ? stats?.longestMonologue?.value <
                  stats?.longestMonologue?.recommendedRange?.[0] ||
                (stats?.longestMonologue?.value &&
                  stats?.longestMonologue?.value >
                    stats?.longestMonologue?.recommendedRange?.[1])
              : true,
          })}
        >
          <CallStatCard
            title="Longest Monologue"
            description="The longest stretch of time you spoke without interruption"
            isLoading={isLoading}
            value={`${longestMonologue}`}
            rawValue={stats?.longestMonologue?.value || 0}
            recommendedRange={
              stats?.longestMonologue?.recommendedRange || [0, 0]
            }
          />
        </motion.div>
      </div>
      {isLoadingChartData ? (
        <></>
      ) : (
        <Card className="border-none shadow-none mt-4">
          <CardHeader className="p-0 py-2">
            <CardTitle className="text-[#2E3035] leading-5 text-sm font-medium">
              Your last{' '}
              {chartData?.length === 1 ? 'call' : `${chartData?.length} calls`}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <ChartContainer
              config={chartConfig}
              className="min-h-[100px] w-full max-h-[200px]"
            >
              <LineChart
                accessibilityLayer
                data={chartData || []}
                margin={{
                  top: 32,
                  left: 32,
                  right: 32,
                  bottom: 16,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="createdAt"
                  interval={0}
                  tickLine={false}
                  axisLine={false}
                  tickMargin={16}
                  tick={({ x, y, payload }) => {
                    const date = dayjs(payload.value);
                    return (
                      <g transform={`translate(${x},${y})`}>
                        <text
                          textAnchor="middle"
                          fill="#888"
                          fontSize={12}
                          dy={0}
                        >
                          {date.format('MMM DD')}
                        </text>
                        <text
                          textAnchor="middle"
                          fill="#888"
                          fontSize={12}
                          dy={14}
                        >
                          {date.format('hh:mm A')}
                        </text>
                      </g>
                    );
                  }}
                />
                <ChartTooltip
                  cursor={false}
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length > 0) {
                      return (
                        <div className="bg-white border rounded-md p-2 shadow-sm text-sm text-muted-foreground">
                          <div className="flex items-center mb-1">
                            <div
                              className={`w-2 h-2 ${getColorFromScore(payload[0].value, true)} rounded-full mr-[6px]`}
                            ></div>
                            <div>
                              <div className="text-sm font-medium text-[#71717A]">
                                <span className="text-[#09090B] mr-[2px]">
                                  {payload[0].value}
                                </span>
                                / 100
                              </div>
                            </div>
                          </div>
                          <div className="mb-2 text-[#71717A] w-[178px]">
                            {`Date: ${dayjs(label).format('MMM DD, hh:mm A')}`}
                          </div>
                          <Link
                            href={`/calls/${payload[0].payload.vapiId}`}
                            target="_blank"
                            className="pointer-events-auto"
                          >
                            <div className="flex items-center text-[#71717A] cursor-pointer pointer-events-auto">
                              <div className="text-sm font-medium">
                                See more
                              </div>
                              <div className="ml-2">
                                <ArrowRightIcon className="w-4 h-4 text-[#71717A]" />
                              </div>
                            </div>
                          </Link>
                        </div>
                      );
                    }
                  }}
                />
                <Line
                  dataKey="finalCallScore"
                  type="natural"
                  stroke="var(--chart-1)"
                  strokeWidth={2}
                  dot={{
                    fill: 'var(--chart-1)',
                  }}
                  activeDot={{
                    r: 6,
                  }}
                >
                  <LabelList
                    position="top"
                    offset={12}
                    className="fill-foreground"
                    fontSize={12}
                  />
                </Line>
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
