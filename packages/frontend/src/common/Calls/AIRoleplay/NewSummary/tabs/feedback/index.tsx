import { Skeleton } from '@/components/ui/skeleton';
import {
  CallDto,
  CallScoringStatus,
  ExcludeFromAnalyticsReasonType,
} from '@/lib/Call/types';
import { AnimatePresence } from 'framer-motion';
import {
  BriefcaseBusiness,
  BugIcon,
  ChevronDownIcon,
  ChevronRight,
  CircleHelp,
  ExternalLink,
  Info,
  Loader2Icon,
  Pencil,
  TriangleAlert,
  XIcon,
} from 'lucide-react';
import { useState } from 'react';
import ScorecardSection, { ICriterion, ISection } from './scorecardSection';
import Link from 'next/link';
import LinksManager from '@/lib/linksManager';

import IncorrectlyScoredModal from './incorrectlyScoredModal';
import useUserSession from '@/hooks/useUserSession';
import CallService from '@/lib/Call';
import { cn } from '@/lib/utils';
import TroubleshootingGuide from '@/components/TroubleshootingGuide';
import CallScoreFrame from '@/common/Calls/Real/NewSummary/leftSideDetails/scoring/scoreFrame';
import { getLinearGradientColorList } from '@/common/Calls/Real/NewSummary/leftSideDetails/scoring';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import AgentAvatar from '@/components/Avatars/Agent';
import { getCallTypeLabelAndIcon } from '../../header';
import useRouting from '@/hooks/useRouting';
import ManagerFeedback from './managerFeedback';

interface IProps {
  isLoadingCall: boolean;
  className?: string;
  call?: CallDto;
  scorecardScrollContainerRef: React.RefObject<HTMLDivElement | null>;
  previousScorecardScrollRef: React.RefObject<number>;
  setShouldRestoreScroll: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface ISelectedCriteria {
  sectionIndex: number;
  criteriaIndex: number;
}

const getTitleFromScore = (score: number | undefined) => {
  let title = "Just the beginning, you'll get there!";
  if (score === undefined) {
    return 'No score for this call';
  } else {
    if (score === 100) {
      title = 'Awesome, this call was perfect!';
    } else if (score > 80 && score < 100) {
      title = "Great job, you're on track!";
    } else if (score > 60) {
      title = "Good job, you're almost there!";
    } else if (score > 40) {
      title = 'Uh oh, you need some work!';
    }
    return title;
  }
};

export const getColorFromScore = (
  score: number | undefined,
  tailwindFlag: boolean = false,
) => {
  let color = tailwindFlag ? 'bg-red-500' : 'rgb(239, 68, 68)';
  if (score === undefined) {
    return color;
  } else {
    if (score === 100) {
      color = tailwindFlag ? 'bg-green-500' : 'rgb(34, 197, 94)';
    } else if (score > 80 && score < 100) {
      color = tailwindFlag ? 'bg-green-500' : 'rgb(34, 197, 94)';
    } else if (score > 60) {
      color = tailwindFlag ? 'bg-yellow-500' : 'rgb(234, 179, 8)';
    } else if (score > 40) {
      color = tailwindFlag ? 'bg-orange-500' : 'rgb(249, 115, 22)';
    }
    return color;
  }
};

export default function CallSummaryFeedback({
  className,
  call,
  scorecardScrollContainerRef,
  previousScorecardScrollRef,
  setShouldRestoreScroll,
}: IProps) {
  const { setMultipleUrlParameters } = useRouting();
  const [showMicTroubleshootingGuide, setShowMicTroubleshootingGuide] =
    useState<boolean>(false);
  const callerId = call?.caller?.id || 0;
  const callId = call?.vapiId || '';

  const { isAdmin, dbOrg } = useUserSession();
  const [openScoreIncorrect, setOpenScoreIncorrect] = useState<boolean>(false);
  const [openExcludeDetails, setOpenExcludeDetails] = useState<boolean>(false);
  const [openBugDeails, setOpenBugDeails] = useState<boolean>(false);

  const toggleExcludeDetails = () => {
    setOpenExcludeDetails((s) => !s);
  };

  const toggleBugReportDetails = () => {
    setOpenBugDeails((s) => !s);
  };

  let scoringErrorMessage = '';
  let showMicInstructions = false;

  if (call?.scoringStatus == CallScoringStatus.ERROR) {
    if (call.vapiCallEndedReason) {
      const errorState = CallService.getVapiEndedReasonInfo(
        call.vapiCallEndedReason,
      );

      if (errorState.isError) {
        scoringErrorMessage = errorState.message;
        if (
          call.vapiCallEndedReason ==
          'customer-did-not-give-microphone-permission'
        ) {
          showMicInstructions = true;
        }
      }
    }
  }

  const handleCriteriaClick = (criteria: string) => {
    if (scorecardScrollContainerRef.current) {
      previousScorecardScrollRef.current =
        scorecardScrollContainerRef.current.scrollTop;
    }
    setShouldRestoreScroll(true);
    setMultipleUrlParameters({ tab: 'criteria', criteria: criteria });
  };

  const calculateScoreBreakdown = (criteria: ISection[]) => {
    let numeratorScore = 0;
    let denominatorScore = 0;
    criteria.forEach((c: ISection) => {
      numeratorScore += c.criteria.filter((c: ICriterion) => c.passed).length;
      denominatorScore += c.criteria.length;
    });
    return `${numeratorScore}/${denominatorScore}`;
  };

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  return (
    <AnimatePresence>
      <>
        <div className={className}>
          <div
            className={cn(
              'flex flex-col overflow-y-auto pb-3 pr-3 absolute left-0 right-0 top-0 bottom-0',
            )}
            ref={scorecardScrollContainerRef}
          >
            <>
              <div className="my-4 flex flex-col p-3 bg-[#FCFEFE] border rounded-2xl border-[#E4E4E7]">
                <div className="flex items-center mb-4">
                  <div className="flex items-center">
                    {call?.agent?.firstName ? (
                      <div className="flex gap-2 items-center">
                        <div className="flex items-center rounded-[100px] p-1 pr-2 border border-[rgba(63,197,209,0.2)] bg-[#FFFFFF] bg-gradient-to-b from-[#3dc3e6]/[0.05] via-[#49c8cf]/[0.05] to-[#36c4bf]/[0.05]">
                          <AgentAvatar
                            className="w-5 h-5 mr-[6px]"
                            agent={call?.agent}
                          />
                          <div className="font-medium text-sm leading-5 mr-[6px] max-w-[200px] truncate">
                            {call?.agent?.firstName} {call?.agent?.lastName}
                          </div>
                          <div className="flex items-center text-white text-[10px] w-[16px] h-[14px] rounded-[4px] px-[3px] mr-[2px] bg-[linear-gradient(180deg,_#3DC3E6_0%,_#49C8CF_33.33%,_#36C4BF_98.96%)] font-medium leading-[100%] tracking-[0%] ">
                            AI
                          </div>
                        </div>
                      </div>
                    ) : (
                      <Skeleton className="w-[150px] h-[30px] rounded-xl" />
                    )}
                    <div className="flex items-center mx-3">
                      {call?.agent?.jobTitle ? (
                        <div className="flex items-center">
                          <BriefcaseBusiness className="w-[14px] h-[14px] mr-2 text-[#71717A]" />
                          <div className="text-[#71717A] text-sm leading-5 max-w-[350px] truncate">
                            {call?.agent?.jobTitle} @ {call?.agent?.companyName}
                          </div>
                        </div>
                      ) : (
                        <Skeleton className="w-[330px] h-[30px] rounded-xl" />
                      )}
                    </div>

                    {call?.agent?.callType ? (
                      <div className="px-2 py-1 rounded-[100px] bg-[#F4F4F5] text-xs font-medium text-[#2E3035] leading-5">
                        <div>
                          {getCallTypeLabelAndIcon(call?.agent?.callType)}
                        </div>
                      </div>
                    ) : (
                      <Skeleton className="w-[100px] h-[30px] rounded-xl" />
                    )}
                  </div>
                </div>
                <div className="flex items-center">
                  {call?.scorecard?.criteria && (
                    <div className="relative flex flex-row justify-center z-[1] mr-4">
                      <>
                        <CallScoreFrame
                          width={64}
                          height={64}
                          gradientColorList={getLinearGradientColorList(
                            call?.scorecard?.finalCallScore === undefined
                              ? undefined
                              : call?.scorecard?.finalCallScore
                                ? Math.round(
                                    call?.scorecard?.finalCallScore * 100,
                                  )
                                : 0,
                          )}
                        />
                        <div className="absolute left-0 right-0 top-0 bottom-0 flex items-center justify-center">
                          <p
                            className={cn('font-bold text-white text-2xl', {
                              'text-[#B8B8BC]':
                                call?.scorecard?.finalCallScore === undefined,
                            })}
                          >
                            {call?.scorecard?.finalCallScore === undefined
                              ? '-'
                              : Math.round(
                                  call?.scorecard?.finalCallScore * 100,
                                )}
                          </p>
                        </div>
                      </>
                    </div>
                  )}
                  <div className="flex flex-col gap-1">
                    {call?.scorecard?.criteria ? (
                      <>
                        <div className="text-[14px] font-medium text-[#2E3035] leading-5">
                          {getTitleFromScore(
                            call?.scorecard?.finalCallScore === undefined
                              ? undefined
                              : Math.round(
                                  call?.scorecard?.finalCallScore * 100,
                                ),
                          )}
                        </div>
                        <div className="text-[14px] text-[#2E3035] leading-5">
                          You got{' '}
                          {
                            <span className="font-medium">
                              {calculateScoreBreakdown(
                                call?.scorecard?.criteria || [],
                              )}
                            </span>
                          }{' '}
                          criteria correct. For this call, the scorecard is the
                          best resource to understand what went right and what
                          went wrong. Dive into each criteria to check out
                          detailed feedback.
                        </div>
                      </>
                    ) : (
                      <div className="flex items-center ml-2 ">
                        <div className="mr-2">
                          <Loader2Icon className="animate-spin" size={18} />
                        </div>
                        <div className="text-sm">
                          {call?.scoringStatus == CallScoringStatus.SCORING
                            ? 'Scoring in progress'
                            : 'Loading...'}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {call?.relatedAgentCall && (
                <div className="my-4">
                  <Link
                    href={LinksManager.trainingCalls(
                      call?.relatedAgentCall?.vapiId,
                    )}
                    className="flex items-center font-semibold"
                  >
                    Open call with decision maker{' '}
                    <ExternalLink size={18} className="ml-2" />
                  </Link>
                </div>
              )}
              <div className="">
                {call?.buggedOut && (
                  <div className="border border-red-500 hover:border-red-300 rounded-lg p-2 mt-4">
                    <div
                      className="flex items-center cursor-pointer"
                      onClick={toggleBugReportDetails}
                    >
                      <div className="mr-3 text-red-400">
                        <BugIcon size={16} />
                      </div>
                      <div className="flex-1 text-sm">Bug Reported</div>
                      {openBugDeails ? (
                        <ChevronDownIcon className="w-4 h-4 " />
                      ) : (
                        <ChevronRight className="w-4 h-4 " />
                      )}
                    </div>
                    {openBugDeails && (
                      <div className="self-stretch px-6 flex-col justify-center items-start gap-2 flex-auto ml-2">
                        <div className="self-stretch text-muted-foreground text-sm font-normal ">
                          Reporter:{' '}
                          {`${call?.caller?.firstName} ${call?.caller?.lastName}`}
                        </div>
                        <div className="self-stretch text-muted-foreground text-sm font-normal ">
                          Description:{' '}
                          {call?.bugDescription || 'No description provided'}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
              {!dbOrg?.isCompetitionOrg && scoringErrorMessage && (
                <div className="border border-red-400 hover:border-red-300 rounded-lg p-2">
                  <div
                    className={cn('flex items-center cursor-pointer', {
                      'cursor-pointer': showMicInstructions,
                    })}
                    onClick={() => {
                      if (showMicInstructions) {
                        setShowMicTroubleshootingGuide(true);
                      }
                    }}
                  >
                    <div className="mr-3 text-red-400">
                      <TriangleAlert size={16} />
                    </div>
                    <div className="flex-1 text-sm">
                      {`${scoringErrorMessage}${showMicInstructions && '.'}`}{' '}
                      {showMicInstructions && 'Click here for more info'}
                    </div>
                    {showMicInstructions && (
                      <div className="ml-1">
                        <CircleHelp size={16} />
                      </div>
                    )}
                  </div>
                </div>
              )}
              {call?.excludeFromAnalytics && (
                <div className="border border-orange-500 hover:border-orange-300 rounded-lg p-2 my-4">
                  <div
                    className="flex items-center cursor-pointer"
                    onClick={toggleExcludeDetails}
                  >
                    <div className="mr-3 text-orange-400">
                      <XIcon size={16} />
                    </div>
                    <div className="flex-1 text-sm">
                      Excluded from analytics
                    </div>

                    {openExcludeDetails ? (
                      <ChevronDownIcon className="w-4 h-4 " />
                    ) : (
                      <ChevronRight className="w-4 h-4 " />
                    )}
                  </div>
                  {openExcludeDetails && (
                    <div className="self-stretch px-6 flex-col justify-center items-start gap-2 flex-auto ml-2">
                      <div className="self-stretch text-muted-foreground text-sm font-normal ">
                        By:{' '}
                        {call?.excludeFromAnalyticsReasonType ===
                        ExcludeFromAnalyticsReasonType.ANALYTICS_MINIMUM_REQUIREMENTS
                          ? 'Hyperbound'
                          : `${call?.caller?.firstName} ${call?.caller?.lastName}`}
                      </div>
                      <div className="self-stretch text-muted-foreground text-sm font-normal ">
                        Reason:{' '}
                        {call?.excludeFromAnalyticsReason ||
                          'No reason provided'}
                      </div>
                    </div>
                  )}
                </div>
              )}
              {call && <ManagerFeedback call={call} />}

              <div className="text-[#09090B] flex items-center gap-2 text-[14px] leading-[135%] font-medium mb-4">
                <div className="tracking-tight">Scorecard</div>
                {call?.scorecardConfigName && (
                  <div>
                    <TooltipProvider delayDuration={50}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="w-4 h-4 text-[#71717A]  hover:text-[#09090B] cursor-pointer" />
                        </TooltipTrigger>
                        <TooltipContent
                          side="bottom"
                          className="bg-white text-[#2E3035] rounded-[12px] border border-[#E4E4E7]"
                        >
                          <p className="text-[14px] leading-5 font-medium">
                            {' '}
                            {call?.scorecardConfigName}
                          </p>
                          {isAdmin && (
                            <Link
                              href={LinksManager.scorecards(
                                `?id=${call?.scorecard?.scorecardConfigId}`,
                              )}
                              target="_blank"
                              className="flex items-center gap-2 mt-2"
                            >
                              <div>
                                <Pencil className="w-4 h-4 text-[#71717A]" />
                              </div>
                              <div>
                                <p className="text-[14px] text-[#71717A] leading-5 font-medium">
                                  {' '}
                                  Edit
                                </p>
                              </div>
                            </Link>
                          )}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                )}
              </div>
              {!call?.scorecard?.criteria ? (
                <div className="grid grid-cols-2 items-stretch gap-4 pb-4 md:grid-cols-2 w-full">
                  <Skeleton className="w-full h-[200px] rounded-xl" />
                  <Skeleton className="w-full h-[200px] rounded-xl" />
                  <Skeleton className="w-full h-[200px] rounded-xl" />
                  <Skeleton className="w-full h-[200px] rounded-xl" />
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-4">
                  {call?.scorecard?.criteria?.map((sc, i) => {
                    return (
                      <ScorecardSection
                        key={i}
                        delayBy={i}
                        section={sc}
                        sectionIndex={i}
                        callerId={callerId}
                        callId={callId}
                        onCriteriaClick={handleCriteriaClick}
                      />
                    );
                  })}
                </div>
              )}
            </>
          </div>

          <IncorrectlyScoredModal
            open={openScoreIncorrect}
            setModalOpen={setOpenScoreIncorrect}
          />
          <TroubleshootingGuide
            open={showMicTroubleshootingGuide}
            onClose={() => {
              setShowMicTroubleshootingGuide(false);
            }}
          />
        </div>
      </>
    </AnimatePresence>
  );
}
