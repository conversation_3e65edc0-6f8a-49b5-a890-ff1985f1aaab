import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useUserSession from '@/hooks/useUserSession';
import CallService from '@/lib/Call';
import { RepsCanEditScoreResults } from '@/lib/Organization/types';
import { cn } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { Check, ChevronRight, SparklesIcon, X } from 'lucide-react';
import { useRef } from 'react';
import { Id, toast } from 'react-toastify';

export interface ISection{
  sectionTitle: string;
  criteria: ICriterion[];
  passedCriteriaCount: number;
  totalCriteriaCount: number;
  description: string;
}

export interface ICriterion{
  coaching: string;
  criterion: string;
  explanation: string;
  improvement: string;
  passed: boolean;
}

export interface ICriterionNavigation extends ICriterion{
  sectionTitle: string;
  sectionIndex: number;
  criteriaIndex: number;
}
interface IProps {
  section: ISection;
  sectionIndex: number;
  callId: string;
  callerId: number;
  delayBy?: number;
  onCriteriaClick?: (criteria: string) => void;
}
export default function ScorecardSection({
  section,
  sectionIndex,
  callId,
  callerId,
  delayBy,
  onCriteriaClick
}: IProps) {
  if (!delayBy) {
    delayBy = 0;
  }

  const { isAdmin, isLoggedIn, dbOrg, dbUser, aiCoachTextOverride } =
    useUserSession();
  const errorToastId = useRef<Id | null>(null);
  const queryClient = useQueryClient();
  let disableToggleCriteria = false;

  if (!isLoggedIn || dbOrg?.isCompetitionOrg) {
    disableToggleCriteria = true;
  } else {
    if (dbOrg?.repsCanEditScoreResults === RepsCanEditScoreResults.NO) {
      disableToggleCriteria = true;
    } else if (!isAdmin && dbUser?.id != callerId) {
      disableToggleCriteria = true;
    }
  }

  /***********************************/
  /*********** ACITONS ***************/
  /***********************************/

  const toggleScorecardCriteriaMutation = useMutation({
    mutationFn: CallService.toggleScorecardCriteriaForCall,
    onSuccess: (data, params) => {
      queryClient.invalidateQueries({
        queryKey: ['callScorecard', false, callId],
      });
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error toggling the scorecard criteria');
        errorToastId.current = toast.error(
          'There was an error toggling the scorecard criteria. Please try again.',
        );
      }
    },
  });

  const toggleDisputedScorecardCriteriaMutation = useMutation({
    mutationFn: CallService.toggleDisputedScorecardCriteriaForCall,
    onSuccess: (data, params) => {
      queryClient.invalidateQueries({
        queryKey: ['callScorecard', false, callId],
      });
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error toggling the scorecard criteria');
        errorToastId.current = toast.error(
          'There was an error toggling the scorecard criteria. Please try again.',
        );
      }
    },
  });

  

  /***********************************/
  /*********** RENDER ****************/
  /***********************************/

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ y: -50 }}
      transition={{
        duration: 0.4,
        delay: delayBy * 0.1,
      }}
      className={cn('rounded-xl px-1 pb-3 ', {
        'bg-[#FAFAFA]': section.passedCriteriaCount !== section.totalCriteriaCount,
        'bg-[#22C55D1A]': section.passedCriteriaCount === section.totalCriteriaCount,
        'border border-[#EDEDED]': section.passedCriteriaCount !== section.totalCriteriaCount
      })}
    >
      {/* HEADER */}
      <div className="flex items-center text-xs pt-3 mb-2 ml-2 font-medium">
        <div className="flex-1">{section.sectionTitle}</div>
        <div>
          <Badge variant={'secondary'} className={
            cn('rounded-[100px] font-medium mr-2',{
              'bg-[#EDEDED]': section.passedCriteriaCount !== section.totalCriteriaCount,
              'bg-[#22C55D]': section.passedCriteriaCount === section.totalCriteriaCount,
              'text-white': section.passedCriteriaCount === section.totalCriteriaCount,
              'hover:bg-[#EDEDED]':  section.passedCriteriaCount !== section.totalCriteriaCount,
              'hover:bg-[#22C55D]': section.passedCriteriaCount === section.totalCriteriaCount,

            })
          }
          >
            {section.passedCriteriaCount} / {section.totalCriteriaCount}
          </Badge>
        </div>
      </div>

      {/* BODY */}
      <div className="">
        {section.criteria.map((criterion: any, i: number) => {
          const passed = criterion.passed;
          let className =
            'flex items-center rounded-sm h-[12px] w-[12px] items-center justify-center';

          if (!disableToggleCriteria) {
            if (criterion.disputed) {
              className += ' cursor-pointer bg-yellow-600 hover:bg-yellow-400';
            } else if (passed) {
              className += ' cursor-pointer bg-green-600 hover:bg-green-400';
            } else {
              className += ' cursor-pointer bg-red-600 hover:bg-red-400';
            }
          } else {
            if (criterion.disputed) {
              className += ' bg-yellow-600';
            }
            if (passed) {
              className += ' bg-green-600';
            } else {
              className += ' bg-red-600';
            }
          }

          let show = 'disabled';

          if (!disableToggleCriteria) {
            if (!criterion.toggled && !criterion.disputed) {
              show = 'initial';
            } else if (criterion.toggled) {
              show = 'overwrite';
            }
            if (criterion.toggled || criterion.disputed) {
              show = 'overwrite';
            }
          }

          let sectionClass = 'cursor-pointer rounded-[8px] transition-all duration-300';
          sectionClass += section.passedCriteriaCount === section.totalCriteriaCount ? ' group hover:bg-[#22C55D33]' : ' group hover:bg-[#EDEDED]';

          return (
            <>
            <div
              key={'criterion-' + i}
              className={'m-0 flex ' + sectionClass}
              onClick={(e) => {
                onCriteriaClick && onCriteriaClick(criterion.criterion);
              }}
            >
              <div className="flex mr-3 items-center pl-2">
                {show == 'disabled' && (
                  <div className={className}>
                    {passed ? (
                      <Check className="text-white" size={12} />
                    ) : (
                      <X className=" text-white" size={12} />
                    )}
                  </div>
                )}

                {show == 'initial' && (
                  <AlertDialog>
                    <TooltipProvider delayDuration={50}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span tabIndex={0}>
                            <AlertDialogTrigger asChild>
                              <div className={className} onClick={(event) => {
                                event?.stopPropagation();
                              }}
                              data-alert-dialog
                              >
                                {passed ? (
                                  <Check className="text-white" size={12} />
                                ) : (
                                  <X className=" text-white" size={12} />
                                )}
                              </div>
                            </AlertDialogTrigger>
                          </span>
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          {dbOrg?.repsCanEditScoreResults ===
                            RepsCanEditScoreResults.YES || isAdmin ? (
                            <p>
                              Incorrectly scored? Click to change score
                              {!isAdmin && <> &amp; report</>}
                            </p>
                          ) : (
                            <p>Dispute score</p>
                          )}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        {dbOrg?.repsCanEditScoreResults ===
                          RepsCanEditScoreResults.YES || isAdmin ? (
                          <>
                            <AlertDialogTitle>
                              Are you absolutely sure?
                            </AlertDialogTitle>
                            {isAdmin ? (
                              <AlertDialogDescription>
                                You can always change this back.
                              </AlertDialogDescription>
                            ) : (
                              <AlertDialogDescription>
                                Your admins will be notified that you made this
                                change.
                              </AlertDialogDescription>
                            )}
                          </>
                        ) : (
                          <>
                            <AlertDialogTitle>
                              Do you want to despute this score?
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              Your admins will be notified that you made this
                              change.
                            </AlertDialogDescription>
                          </>
                        )}
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                        >Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={(e) => {
                            e.stopPropagation();
                            if (
                              isAdmin &&
                              dbOrg?.repsCanEditScoreResults ===
                                RepsCanEditScoreResults.DISPUTE_ONLY
                            ) {
                              toggleDisputedScorecardCriteriaMutation.mutate({
                                vapiId: callId,
                                sectionTitle: section.sectionTitle,
                                criterion: criterion.criterion,
                              });
                            } else {
                              toggleScorecardCriteriaMutation.mutate({
                                vapiId: callId,
                                sectionTitle: section.sectionTitle,
                                criterion: criterion.criterion,
                              });
                            }
                          }}
                        >
                          Change and report
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                {show == 'overwrite' && (
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span tabIndex={0}>
                          <div
                            className={className}
                            onClick={() => {
                              if (
                                isAdmin &&
                                dbOrg?.repsCanEditScoreResults ===
                                  RepsCanEditScoreResults.DISPUTE_ONLY
                              ) {
                                toggleDisputedScorecardCriteriaMutation.mutate({
                                  vapiId: callId,
                                  sectionTitle: section.sectionTitle,
                                  criterion: criterion.criterion,
                                });
                              } else if (
                                dbOrg?.repsCanEditScoreResults ===
                                RepsCanEditScoreResults.YES
                              ) {
                                //its either user or admin
                                toggleScorecardCriteriaMutation.mutate({
                                  vapiId: callId,
                                  sectionTitle: section.sectionTitle,
                                  criterion: criterion.criterion,
                                });
                              }
                            }}
                          >
                            {passed ? (
                              <Check className="text-white" size={12} />
                            ) : (
                              <X className=" text-white" size={12} />
                            )}
                          </div>
                        </span>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        {!(
                          isAdmin &&
                          dbOrg?.repsCanEditScoreResults ===
                            RepsCanEditScoreResults.DISPUTE_ONLY
                        ) &&
                        !(
                          dbOrg?.repsCanEditScoreResults ===
                          RepsCanEditScoreResults.YES
                        ) ? (
                          <p>Contact your admin to change this score</p>
                        ) : (
                          <p>Change score</p>
                        )}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
              <div
                className={'flex-1 font-medium py-2'}
              >
                <div className="flex items-center pr-3 rounded-lg">
                  <div className="flex-1">
                    <p className="text-xm leading-5 text-[#09090B]">
                      {criterion.criterion}
                    </p>
                  </div>
                  <div className="text-muted-foreground group-hover:text-black h-[13px] overflow-hidden">
                      <ChevronRight size={13} />
                    </div>
                </div>

                {passed && show == 'overwrite' && isAdmin && (
                  <div className="text-muted-foreground text-[10px] pt-0 pl-2 pb-2">
                    edited by rep
                  </div>
                )}
              </div>
            </div>
            {i < section.criteria.length - 1 && <hr className='border-t border-[#E4E4E7] my-2 mx-2' />}
            </>
          );
        })}
      </div>
    </motion.div>
  );
}
