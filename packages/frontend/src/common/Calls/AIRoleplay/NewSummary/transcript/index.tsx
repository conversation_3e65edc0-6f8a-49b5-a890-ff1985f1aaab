import { CallDto, VapiCallMessage } from '@/lib/Call/types';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  CopyCheckIcon,
  CopyIcon,
  DownloadIcon,
  Loader2Icon,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';
import useUserSession from '@/hooks/useUserSession';
import TroubleshootingGuide from '@/components/TroubleshootingGuide';
import { cn, formatTranscript } from '@/lib/utils';
import { AgentDto } from '@/lib/Agent/types';
import { RealCallNewSummaryTranscriptTab } from '@/common/Calls/Real/NewSummary/leftSideDetails/transcript';
import { RealCallsPublicPartiesDto } from '@/lib/Integrations/RealCalls/types';
import PlyrWrapper from './plyrWrapper';
import useMissingRecording from '@/hooks/useMissingRecording';
import { constructTranscriptFromCall } from './utils';
import { IHighlightPhrase } from '..';
import CallService from '@/lib/Call';

interface IProps {
  call?: CallDto;
  isLoadingCall: boolean;
  playerRef: React.RefObject<Plyr | null>;
  isAutoScrollEnabled: boolean;
  setIsAutoScrollEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  highlightPhrases: IHighlightPhrase[] | undefined;
}

export interface CallMetadata {
  artifact?: { videoRecordingUrl?: string };
  recordingUrl?: string;
  messages: VapiCallMessage[];
}

export default function CallSummaryTranscript({
  isLoadingCall,
  call,
  playerRef,
  highlightPhrases,
  isAutoScrollEnabled,
  setIsAutoScrollEnabled,
}: IProps) {
  /*****************************/
  /*********** CONSTs **********/
  /*****************************/
  const { isLoggedIn } = useUserSession();
  const [currentTime, setCurrentTime] = useState(0);
  const [recordingUrlObj, setRecordingUrlObj] = useState<
    { url: string; type: 'audio' | 'video' } | undefined
  >(undefined);
  const callMetadata = call?.providerMetadata
    ? call?.providerMetadata
    : (call?.vapiMetadata as CallMetadata);
  const callId = call?.providerCallId
    ? call?.providerCallId
    : call?.vapiId || '';
  const transcript: VapiCallMessage[] = callMetadata?.messages || [];
  const agent: AgentDto | undefined = call?.agent;

  const { data: missingRecording } = useMissingRecording(
    callId,
    !!callId &&
      !(call?.mediaUrls?.audio?.url || call?.mediaUrls?.video?.url) &&
      call?.createdAt &&
      new Date(call.createdAt) < new Date(Date.now() - 24 * 60 * 60 * 1000),
  );

  /*****************************/
  /*********** STATE ***********/
  /*****************************/

  const { isAdmin, onlyAdminsCanExportCalls, canViewMedia } = useUserSession();
  const [micProblemsModalOpen, setMicProblemsModalOpen] =
    useState<boolean>(false);
  const [isCopied, setIsCopied] = useState(false);
  /*****************************/
  /*********** INIT ************/
  /*****************************/

  const formattedTranscript = formatTranscript(
    transcript || [],
    agent ? `${agent.firstName} ${agent.lastName}` : '',
    callId,
  );

  /*****************************/
  /*********** ACTIONS *********/
  /*****************************/

  const onDownloadAudioClick = async () => {
    const callId = call?.providerCallId ? call?.providerCallId : call?.vapiId;
    if (!callId) {
      return;
    }
    if (recordingUrlObj?.url) {
      const audioRecodingBlob = await CallService.getAudioCall(callId);
      if (audioRecodingBlob) {
        const url = URL.createObjectURL(audioRecodingBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `audio-${callId}.wav`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    }
  };

  //transcript
  const onDownloadClick = async () => {
    const blob = new Blob([formattedTranscript], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    const callId = call?.providerCallId ? call?.providerCallId : call?.vapiId;
    a.download = `transcript-${callId}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const onCopyClick = async () => {
    try {
      // Use navigator.clipboard.writeText to copy text to clipboard

      await navigator.clipboard.writeText(formattedTranscript);

      // Update state to indicate that the text has been copied
      setIsCopied(true);

      // Reset the copied state after a short delay
      setTimeout(() => {
        setIsCopied(false);
      }, 1500);
    } catch (err) {
      console.error('Unable to copy to clipboard:', err);
    }
  };

  /*****************************/
  /********* EFFECTS ***********/
  /*****************************/

  // Construct parties from the the transcript
  const constructPartiesFromCall = () => {
    // Add a flag to control for multi-party
    const parties: RealCallsPublicPartiesDto[] = [
      {
        id: 1,
        name: `${call?.caller?.firstName} ${call?.caller?.lastName}`,
        email: call?.caller?.email || '',
        affiliation: 'INTERNAL',
        user: {
          id: 2,
          firstName: isLoggedIn
            ? call?.caller?.firstName || ''
            : call?.demoInboundFormResponse?.name?.split(' ')[0] || '',
          lastName: isLoggedIn
            ? call?.caller?.lastName || ''
            : call?.demoInboundFormResponse?.name.split(' ')[1] || '',
          email: isLoggedIn
            ? call?.caller?.email || ''
            : call?.demoInboundFormResponse?.email || '',
          avatar: isLoggedIn
            ? call?.caller?.avatar || ''
            : call?.demoInboundFormResponse?.avatar || '',
        },
      },
      {
        id: 2,
        name: `${call?.agent?.firstName} ${call?.agent?.lastName}`,
        email: '',
        affiliation: 'agent',
        user: {
          id: 1,
          firstName: call?.agent?.firstName || '',
          lastName: call?.agent?.lastName || '',
          email: '',
          avatar: call?.agent?.avatarUrl || '',
        },
      },
    ];
    // Add supporting agents as parties
    if (
      call?.agent?.supportingAgentInfo &&
      call?.agent?.supportingAgentInfo.length > 0
    ) {
      call?.agent?.supportingAgentInfo.forEach((agent, index) => {
        parties.push({
          id: index + 3,
          name: `${agent.firstName} ${agent.lastName}`,
          email: '',
          affiliation: 'agent',
          user: {
            id: index + 3,
            firstName: agent.firstName || '',
            lastName: agent.lastName || '',
            email: '',
            avatar: agent.avatarUrl || '',
          },
        });
      });
    }
    return parties;
  };

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  useEffect(() => {
    if (missingRecording && !recordingUrlObj?.url) {
      setRecordingUrlObj(
        missingRecording.video?.url
          ? { url: missingRecording.video?.url, type: 'video' }
          : missingRecording.audio?.url
            ? { url: missingRecording.audio?.url, type: 'audio' }
            : undefined,
      );
    }
  }, [missingRecording]);

  useEffect(() => {
    if (!recordingUrlObj?.url) {
      if (call?.mediaUrls?.audio?.url || call?.mediaUrls?.video?.url) {
        setRecordingUrlObj(
          call?.mediaUrls?.video?.url
            ? { url: call?.mediaUrls?.video?.url, type: 'video' }
            : call?.mediaUrls?.audio?.url
              ? { url: call?.mediaUrls?.audio?.url, type: 'audio' }
              : undefined,
        );
      }
    }
  }, [call]);

  return (
    <div className="w-full h-full md:flex md:flex-col">
      {canViewMedia && (
        <div className="w-full relative z-[1]">
          {/*********************************/}
          {/***** AUDIO/VIDEO (header) ******/}
          {/*********************************/}

          {/*********************************/}
          {/***** AUDIO/VIDEO (player) ******/}
          {/*********************************/}
          <div className="bg-[#FFFFFF] rounded-[12px] border border-[#E4E4E7] shadow-[0px_2px_10px_0px_#09090B0D]">
            {recordingUrlObj?.url ? (
              <div className="flex items-center justify-center relative">
                <>
                  <PlyrWrapper
                    url={recordingUrlObj.url}
                    setCurrentTime={setCurrentTime}
                    playerRef={playerRef}
                    recordingUrlType={recordingUrlObj.type}
                  />
                  {(!onlyAdminsCanExportCalls || isAdmin) && (
                    <TooltipProvider
                      delayDuration={50}
                      disableHoverableContent={isLoadingCall}
                    >
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={'ghost'}
                            disabled={isLoadingCall}
                            onClick={onDownloadAudioClick}
                            className={cn(
                              'rounded-full text-muted-foreground p-2',
                              {
                                'mr-3': recordingUrlObj?.type === 'audio',
                                'absolute text-white bottom-[9px] hover:bg-[#f3f4f6] right-3 hover:text-[#1f2937]':
                                  recordingUrlObj?.type === 'video',
                              },
                            )}
                          >
                            <DownloadIcon className="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Download recording</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </>
              </div>
            ) : (
              <div className="flex items-center p-3 h-[54px]">
                <div className="mr-2">
                  <Loader2Icon className="animate-spin" />
                </div>
                <div className="text-sm leading-5">Loading recording...</div>
              </div>
            )}
          </div>

          {/*********************************/}
          {/********* TROUBLESHOOTING *******/}
          {/*********************************/}
          {/* <div className="mt-6">
            <div
              className="flex items-center p-2 border rounded-lg border-yellow-400 hover:border-yellow-300 cursor-pointer"
              onClick={() => {
                setMicProblemsModalOpen(true);
              }}
            >
              <div className="mr-3 text-yellow-400">
                <MicOff size={16} />
              </div>
              <div className="flex-1">
                <h3 className="text-xs">Can&apos;t hear your voice?</h3>
              </div>
              <div>
                <ChevronRight size={16} />
              </div>
            </div>
          </div> */}
        </div>
      )}

      {/*********************************/}
      {/***** TRANSCRIPT (header) *******/}
      {/*********************************/}
      <div className="w-full mt-3 md:flex-1 md:relative bg-[#FFFFFF] rounded-[12px] border border-[#E4E4E7] shadow-[0px_2px_10px_0px_#09090B0D]">
        <div className="w-full md:absolute md:left-0 md:right-0 md:top-0 md:bottom-0 md:flex md:flex-col">
          <div className="flex justify-between items-start px-3 pt-3">
            <div className="flex items-center font-semibold h-full">
              Transcript
            </div>
            {transcript?.length > 0 && (
              <div className="flex items-center space-x-0">
                <TooltipProvider
                  delayDuration={50}
                  disableHoverableContent={isLoadingCall}
                >
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={'ghost'}
                        disabled={isLoadingCall}
                        onClick={onCopyClick}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        {isCopied ? (
                          <CopyCheckIcon className="w-4 h-4 text-green-600" />
                        ) : (
                          <CopyIcon className="w-4 h-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Copy to clipboard</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider
                  delayDuration={50}
                  disableHoverableContent={isLoadingCall}
                >
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={'ghost'}
                        disabled={isLoadingCall}
                        onClick={onDownloadClick}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        <DownloadIcon className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Download transcript</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            )}
          </div>

          {/*********************************/}
          {/********* TRANSCRIPT ************/}
          {/*********************************/}

          <div className="mt-4 md:flex-1">
            <RealCallNewSummaryTranscriptTab
              parties={constructPartiesFromCall()}
              transcript={constructTranscriptFromCall(call)}
              mediaTime={currentTime}
              simCallFlag={true}
              playerRef={playerRef}
              highlightPhrases={highlightPhrases}
              isAutoScrollEnabled={isAutoScrollEnabled}
              setIsAutoScrollEnabled={setIsAutoScrollEnabled}
            />
          </div>
        </div>
      </div>

      <TroubleshootingGuide
        open={micProblemsModalOpen}
        onClose={() => {
          setMicProblemsModalOpen(false);
        }}
      />
    </div>
  );
}
