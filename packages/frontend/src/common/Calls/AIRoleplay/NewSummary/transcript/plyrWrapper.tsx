'use client';
// import Plyr, { APITypes } from 'plyr-react';
import Plyr from 'plyr';
import React, { useEffect, useRef } from 'react';
import 'plyr/dist/plyr.css';

interface IPlyrWrapperProps {
  url: string | undefined;
  recordingUrlType: 'audio' | 'video' | undefined;
  setCurrentTime: React.Dispatch<React.SetStateAction<number>>;
  playerRef: React.RefObject<Plyr | null>;
}

function PlyrWrapperComponent({
  url,
  recordingUrlType,
  setCurrentTime,
  playerRef,
}: IPlyrWrapperProps) {
  const audioContainerRef = useRef<HTMLAudioElement | null>(null);
  const videoContainerRef = useRef<HTMLVideoElement | null>(null);
  useEffect(() => {
    const mediaElement =
      recordingUrlType === 'audio'
        ? audioContainerRef.current
        : videoContainerRef.current;
    if (!mediaElement) return;

    if (!playerRef.current) {
      playerRef.current = new Plyr(mediaElement, {
        controls: ['play', 'progress', 'current-time', 'mute'],
      });
    }

    const player = playerRef.current;

    const onTimeUpdate = () =>
      setCurrentTime(Math.ceil(player.currentTime * 100) / 100);
    player.on('timeupdate', onTimeUpdate);
  }, [url, setCurrentTime]);
  return (
    <div className="w-full rounded-[12px]">
      {recordingUrlType === 'audio' ? (
        <audio ref={audioContainerRef} src={url} crossOrigin="anonymous" />
      ) : (
        <div className="video-plyr-wrapper">
          <video
            ref={videoContainerRef}
            src={url}
            crossOrigin="anonymous"
            className="w-full aspect-video object-contain"
          />
        </div>
      )}
    </div>
  );
}

// Use React.memo to prevent re-renders unless `url` actually changes
const PlyrWrapper = React.memo(PlyrWrapperComponent);

export default PlyrWrapper;
