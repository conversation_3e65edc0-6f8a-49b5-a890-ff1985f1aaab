import { ITranscriptProp } from '@/common/Calls/Real/NewSummary/leftSideDetails/transcript';
import { CallDto, VapiCallMessage } from '@/lib/Call/types';

export const processTranscriptMessage = (
  call: CallDto | undefined,
  message: VapiCallMessage,
  nameList: string[],
) => {
  const role = message.role as 'bot' | 'user';
  const secondsFromStart = message.secondsFromStart;
  if (message.role === 'user') {
    return {
      role: role,
      message: message.message,
      userName: `${call?.caller?.firstName} ${call?.caller?.lastName}`,
      secondsFromStart: secondsFromStart,
      time: message.time,
    };
  } else {
    // Extract matches
    const regex = /<([^/>]+)>/g;
    let match;
    const names: string[] = [];
    while ((match = regex.exec(message.message)) !== null) {
      names.push(match[1]);
    }
    if (names.length === 0) {
      return {
        role: role,
        message: message.message,
        userName: `${call?.agent?.firstName} ${call?.agent?.lastName}`,
        secondsFromStart: secondsFromStart,
        time: message.time,
      };
    } else {
      const matchingNames = nameList.filter((name) => {
        const firstName = name.split(' ')[0];
        return names.includes(firstName);
      });
      if (names.length === 1) {
        return {
          role: role,
          message: message.message.replace(/<\/?[^>]+>/g, ''),
          userName: matchingNames.join(', '),
          secondsFromStart: secondsFromStart,
          time: message.time,
        };
      } else {
        return {
          role: role,
          message: message.message,
          userName: matchingNames.join(', '),
          secondsFromStart: secondsFromStart,
          time: message.time,
        };
      }
    }
  }
};

export const constructTranscriptFromCall = (call: CallDto | undefined) => {
  const callMetadata = call?.providerMetadata
    ? call?.providerMetadata
    : call?.vapiMetadata;
  const nameList = [`${call?.agent?.firstName} ${call?.agent?.lastName}`];
  if (call?.agent?.supportingAgentInfo) {
    call?.agent.supportingAgentInfo.forEach((agent) => {
      nameList.push(`${agent.firstName} ${agent.lastName}`);
    });
  }
  // Process the message to expand multi-speaker messages
  const processedTranscript: ITranscriptProp[] = [];
  callMetadata?.messages?.forEach((message: VapiCallMessage) => {
    // Expand messages with multiple speakers
    const expandedMessageList = parseMultiSpeakerMessage(message.message);
    if (expandedMessageList.length > 0) {
      expandedMessageList.forEach((expandedMessage) => {
        // Find the matching full name
        const matchingName = nameList.find((name) =>
          name.toLowerCase().includes(expandedMessage.speaker.toLowerCase()),
        );

        const processedMessage = {
          role: message.role as 'bot' | 'user',
          message: expandedMessage.message,
          time: message.time,
          secondsFromStart: message.secondsFromStart,
          userName: matchingName || '',
        };
        processedTranscript.push(processedMessage);
      });
    } else {
      const processedMessage = processTranscriptMessage(
        call,
        message,
        nameList,
      );
      processedTranscript.push(processedMessage);
    }
  });
  return processedTranscript;
};

export const formatSecondsToCitationTimestamp = (seconds: number) => {
  const totalSeconds = Math.floor(seconds);
  const hrs = Math.floor(totalSeconds / 3600);
  const mins = Math.floor((totalSeconds % 3600) / 60);
  const secs = totalSeconds % 60;
  const padded = (n: number) => String(n).padStart(2, '0');
  if (hrs > 0) {
    return `${hrs}:${padded(mins)}:${padded(secs)}`;
  } else {
    return `${mins}:${padded(secs)}`;
  }
};

export const formatMatch = (match: string) => {
  const timeStr = match.slice(1, -1); // remove ()
  const parts = timeStr.split(':').map(Number);
  if (parts.length === 3) {
    const [, minutes, seconds] = parts;
    return `(${minutes}:${String(seconds).padStart(2, '0')})`;
  } else if (parts.length === 2) {
    const [minutes, seconds] = parts;
    return `(${minutes}:${String(seconds).padStart(2, '0')})`;
  }
  return match; // fallback
};

export const processTimestamp = (timestamp: string) => {
  const parts = timestamp.split(':').map(Number);
  if (parts.length === 3 && parts[0] === 0) {
    // Format: hh:mm:ss → Convert to mm:ss
    return `${parts[1]}:${parts[2].toString().padStart(2, '0')}`;
  }
  return timestamp;
};

export const parseMultiSpeakerMessage = (rawMessage: string) => {
  const regex = /<([^>]+)>([\s\S]*?)<\/\1>/g; // match <tag>...</tag> pairs
  const results: { speaker: string; message: string }[] = [];
  let match;
  while ((match = regex.exec(rawMessage)) !== null) {
    results.push({
      speaker: match[1].trim(),
      message: match[2].trim(),
    });
  }
  return results;
};
