import CallWithProblemsModal from '@/components/CallWithProblemsModal';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import DemoWelcomeModal from '@/components/DemoWelcomeModal';
import useFixedWidthPanel from '@/hooks/ui/useFixedWidthPanel';
import useCall from '@/hooks/useCall';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useUserSession from '@/hooks/useUserSession';
import { AgentDto } from '@/lib/Agent/types';
import CallService from '@/lib/Call';
import { cn } from '@/lib/utils';
import _ from 'lodash';
import { RefObject, useEffect, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import CallSummaryHeader from './header';
import CallSummaryTabs from './tabs';
import CallSummaryTranscript from './transcript';
import { CallScoringStatus } from '@/lib/Call/types';
import { useQueryClient } from '@tanstack/react-query';
import { TaskAndAttempts } from '@/lib/LearningModule/types';
import { RealCallMediaControllerRefType } from '../../Real/NewSummary/RealCallMediaPlayer';
import { redirect } from 'next/navigation';
interface IProps {
  vapiId: string;
  openTab?: string;
  minimal?: boolean;
  onlyScorecard?: boolean;
  showLeaderboardDateFilter?: boolean;
  taskAndAttempts?: TaskAndAttempts;
}

export default function CallSummary({
  vapiId,
  openTab,
  minimal,
  onlyScorecard,
  showLeaderboardDateFilter = false,
  taskAndAttempts,
}: IProps) {
  const firstTimeCallFetchCheck = useRef<boolean>(false);
  const { isLoggedIn, userId, dbOrg, isMember, isHyperboundUser } = useUserSession();
  const mediaControllerRef = useRef<RealCallMediaControllerRefType>(
    null,
  ) as RefObject<RealCallMediaControllerRefType>;
  const [demoWelcomeModalOpen, setDemoWelcomeModalOpen] =
    useState<boolean>(false);
  const errorToastId = useRef<Id | null>(null);
  const [showConfimrRescoringModal, setShowConfimrRescoringModal] =
    useState<boolean>(false);
  const rescoringCall = useRef<boolean>(false);
  const queryClient = useQueryClient();

  /*****************************/
  /********* ACTIONS ***********/
  /*****************************/

  const rescoreCall = async (hideSuccessMessage = false) => {
    rescoringCall.current = true;
    setShowConfimrRescoringModal(false);
    if (call?.vapiId) {
      const r = await CallService.rescoreCall(call?.vapiId);
      if (r && !hideSuccessMessage) {
        errorToastId.current = toast.success(
          'Call rescore started. It may take a few seconds.',
        );
      }
      queryClient.invalidateQueries({
        queryKey: ['call', !isLoggedIn, vapiId],
      });
    }
  };

  const startCallRescore = () => {
    setShowConfimrRescoringModal(true);
  };

  /*****************************/
  /*********** INIT ************/
  /*****************************/

  const {
    data: call,
    isLoading: isLoadingCall,
    refetch: refetchCall,
  } = useCall(vapiId, !isLoggedIn);

  useEffect(() => {
    if (call) {
      if(!firstTimeCallFetchCheck.current){
        firstTimeCallFetchCheck.current = true
      }
      if (call?.scoringStatus == CallScoringStatus.SCORING) {
        startAutoRefresh();
      } else if (timeoutSearchRes.current) {
        clearTimeout(timeoutSearchRes.current);
      }
    }

    return () => {
      if (timeoutSearchRes.current) {
        clearTimeout(timeoutSearchRes.current);
      }
    };
  }, [call, isLoadingCall]);

  useEffect(() => {
    if(isLoggedIn && firstTimeCallFetchCheck.current
      && dbOrg?.onlyAdminCanViewAllCalls &&
      isMember && !isHyperboundUser){
        if(call?.callerId && call?.callerId !== userId){
          redirect('/404');
        }
      }
  }, [call?.callerId, isLoggedIn, dbOrg?.onlyAdminCanViewAllCalls, isMember, isHyperboundUser, userId]);

  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const agent = call?.agent as AgentDto;
  const callMetadata = call?.vapiMetadata;

  let _showProblemsModal = false;
  if (call) {
    if (
      !callMetadata ||
      !callMetadata?.transcript ||
      callMetadata.status == 'queued'
    ) {
      _showProblemsModal = true;
      if (!rescoringCall.current) {
        rescoreCall(true);
      }
    }
  }

  const [showProblemsModal] = useState<boolean>(_showProblemsModal);

  useEffect(() => {
    if (!isLoggedIn && _.isEmpty(hbDemoInboundForm) && !demoWelcomeModalOpen) {
      setDemoWelcomeModalOpen(true);
    }
  }, [hbDemoInboundForm, isLoggedIn]);

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);

  const startAutoRefresh = () => {
    timeoutSearchRes.current = setTimeout(() => {
      refetchCall();
    }, 2000);
  };

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  const { panelRef } = useFixedWidthPanel();

  const modals = (
    <>
      <DemoWelcomeModal
        agent={agent}
        modalOpen={demoWelcomeModalOpen}
        setModalOpen={setDemoWelcomeModalOpen}
        onSubmit={() => {
          setDemoWelcomeModalOpen(false);
        }}
        isClosable={false}
        submitText="Let's go, see scorecard"
      />
      <CallWithProblemsModal
        open={showProblemsModal}
        vapiId={call?.vapiId || ''}
      />
      <DeleteConfirmationModal
        open={showConfimrRescoringModal}
        onCancel={() => {
          setShowConfimrRescoringModal(false);
        }}
        onConfirm={rescoreCall}
        title={'Rescore your call?'}
        description={
          'Are you sure you want to rescore the AI review? This action will generate a new set of scores and erase all corrections that you made.'
        }
        cancelLabel={'Cancel'}
        confirmLabel={'Confirm'}
        useBtnBlack={true}
      />
    </>
  );

  if (minimal) {
    return (
      <div ref={panelRef}>
        <CallSummaryHeader
          isLoadingCall={isLoadingCall}
          call={call}
          minimal={minimal}
        />
        <div className="flex flex-col md:flex-row space-x-0 md:space-x-10 justify-between px-4 md:pr-0 md:pl-8">
          <div
            className={cn('w-full mt-5', {
              'md:w-[64%]': !minimal,
              'pr-8': minimal,
            })}
          >
            <CallSummaryTabs
              isLoadingCall={isLoadingCall}
              call={call}
              rescoreCall={startCallRescore}
              openTab={openTab}
              taskAndAttempts={taskAndAttempts}
            />
          </div>
          {!minimal && (
            <div className="flex w-full md:w-[36%] border-0 md:border-l px-2 min-h-screen">
              <CallSummaryTranscript
                isLoadingCall={isLoadingCall}
                call={call}
              />
            </div>
          )}
        </div>
        {modals}
      </div>
    );
  } else {
    return (
      <div
        ref={panelRef}
        className={'md:h-full flex flex-col md:overflow-y-hidden'}
      >
        <CallSummaryHeader
          isLoadingCall={isLoadingCall}
          call={call}
          minimal={minimal}
        />
        <div className="relative w-full md:flex-1">
          <div
            className={cn(
              'flex flex-col md:flex-row space-x-0 md:space-x-0 md:justify-between px-4 md:pr-0 md:pl-8',
              {
                'md:absolute md:left-0 md:right-0 md:top-0 md:bottom-0':
                  !minimal,
              },
            )}
          >
            <div
              className={cn('w-full mt-5 md:h-full md:overflow-y-auto', {
                'md:w-[67%]': !minimal,
                'pr-8': minimal,
              })}
            >
              <div className="md:pr-12 md:h-full">
                <CallSummaryTabs
                  isLoadingCall={isLoadingCall}
                  call={call}
                  rescoreCall={startCallRescore}
                  openTab={openTab}
                  onlyScorecard={onlyScorecard}
                  showLeaderboardDateFilter={showLeaderboardDateFilter}
                />
              </div>
            </div>
            {!minimal && (
              <div className="flex w-full md:w-[36%] border-0 md:border-l px-2 md:h-full overflow-y-auto md:overflow-y-auto">
                <CallSummaryTranscript
                  isLoadingCall={isLoadingCall}
                  call={call}
                />
              </div>
            )}
          </div>
        </div>
        {modals}
      </div>
    );
  }
}
