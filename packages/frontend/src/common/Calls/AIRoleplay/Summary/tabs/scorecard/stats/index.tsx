import { CallStatsDto } from '@/lib/Call/types';
import { motion } from 'framer-motion';
import CallStatCard from './statsCard';

function formatDurationDisplay(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  const minutesDisplay = minutes >= 10 ? `${minutes}` : `0${minutes}`;
  const secondsDisplay =
    remainingSeconds >= 10 ? `${remainingSeconds}` : `0${remainingSeconds}`;

  return `${minutesDisplay}:${secondsDisplay}`;
}

interface IProps {
  isLoading: boolean;
  className?: string;
  stats?: CallStatsDto;
}

export default function CallStats({ className, isLoading, stats }: IProps) {
  const talkListenRatio = `${Math.round(
    (stats?.talkListenRatio?.value || 0) * 100,
  )}%`;
  const longestMonologue = formatDurationDisplay(
    stats?.longestMonologue?.value || 0,
  );

  return (
    <div className={className}>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ y: -50 }}
        transition={{
          duration: 0.4,
          delay: 0,
        }}
        className="grid grid-cols-4 border rounded-lg"
      >
        <div className="p-3 border-r">
          <CallStatCard
            title="Talk/Listen Ratio"
            description="How much you talked vs. how much you listened"
            isLoading={isLoading}
            value={talkListenRatio}
            rawValue={(stats?.talkListenRatio?.value || 0) * 100}
            recommendedRange={
              stats?.talkListenRatio?.recommendedRange?.map((r) =>
                Math.round(r * 100),
              ) || [0, 0]
            }
          />
        </div>
        <div className="p-3 border-r">
          <CallStatCard
            title="Filler Words"
            description="How many filler words like 'um' and 'uh' you used"
            isLoading={isLoading}
            value={`${Number(stats?.fillerWords?.value || 0).toFixed(2)} wpm`}
            rawValue={stats?.fillerWords?.value || 0}
            recommendedRange={stats?.fillerWords?.recommendedRange || [0, 0]}
          />
        </div>

        <div className="p-3 border-r">
          <CallStatCard
            title="Talk Speed"
            description="How fast you spoke in words per minute"
            isLoading={isLoading}
            rawValue={stats?.talkSpeed?.value || 0}
            value={`${Math.round(stats?.talkSpeed?.value || 0)} wpm`}
            recommendedRange={stats?.talkSpeed?.recommendedRange || [0, 0]}
          />
        </div>

        <div className="p-3 ">
          <CallStatCard
            title="Longest Monologue"
            description="The longest stretch of time you spoke without interruption"
            isLoading={isLoading}
            value={`${longestMonologue}`}
            rawValue={stats?.longestMonologue?.value || 0}
            recommendedRange={
              stats?.longestMonologue?.recommendedRange || [0, 0]
            }
          />
        </div>
      </motion.div>
    </div>
  );
}
