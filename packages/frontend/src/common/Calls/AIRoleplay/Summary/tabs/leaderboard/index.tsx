import { Skeleton } from '@/components/ui/skeleton';
import useOrgLeaderboard from '@/hooks/useOrgLeaderboard';
import { useState } from 'react';
import Podium from './podium';
import List from './list';
import { AnimatePresence, motion } from 'framer-motion';
import DatesFilter from '@/common/Analytics/DashboardTab/Filters/DateFilter';
import {
  AnalyticsFilterDateRange,
  DateFilterType,
} from '@/lib/Analytics/types';
import AnalyticsService from '@/lib/Analytics';
import dayjs from 'dayjs';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  agentId?: number;
  className?: string;
  showLeaderboardDateFilter?: boolean;
}

const defaultDateRange = AnalyticsService.getDatesRange({
  fromDate: dayjs().subtract(14, 'day').toDate(),
  toDate: new Date(),
  range: AnalyticsFilterDateRange.LAST_TWO_WEEKS,
});

export default function Leaderboard({
  agentId,
  className,
  showLeaderboardDateFilter = false,
}: IProps) {
  const { defaultLeaderboardDateRange } = useUserSession();

  const [filterState, setFilterState] = useState<DateFilterType>({
    fromDate: defaultDateRange.from,
    toDate: defaultDateRange.to,
    range: defaultLeaderboardDateRange,
  });

  const { data: leaderboard, isLoading: isLoadingLeaderboard } =
    useOrgLeaderboard(filterState.fromDate, filterState.toDate, agentId);

  const onDatesUpdated = (dates: DateFilterType) => {
    const { from, to } = AnalyticsService.getDatesRange(dates);
    setFilterState({
      range: dates.range,
      fromDate: from,
      toDate: to,
    });
  };

  return (
    <AnimatePresence>
      <div className={className}>
        {showLeaderboardDateFilter && (
          <div className="mb-6">
            <div className="text-sm ml-1 mb-1 font-semibold">Date range:</div>
            <DatesFilter
              current={filterState}
              onDatesUpdated={onDatesUpdated}
            />
          </div>
        )}

        {isLoadingLeaderboard ? (
          <>
            <Skeleton className="w-full h-[200px] mb-3" />
            <Skeleton className="w-full h-[600px] mb-3" />
          </>
        ) : leaderboard?.leaderboard.length === 0 ? (
          <div className="w-full flex items-center justify-center">
            <div>No calls have been made.</div>
          </div>
        ) : (
          <>
            <Podium leaderboard={leaderboard} />
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.4,
                delay: 0,
              }}
              className="mt-8"
            >
              <List leaderboard={leaderboard} />
            </motion.div>
          </>
        )}
      </div>
    </AnimatePresence>
  );
}
