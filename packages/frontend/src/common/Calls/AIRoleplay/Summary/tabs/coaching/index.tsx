import { Skeleton } from '@/components/ui/skeleton';
import useCallCoaching, { useDemoCallCoaching } from '@/hooks/useCallCoaching';
import { CallDto } from '@/lib/Call/types';
import {
  Bot,
  Check,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Link2,
  LinkIcon,
  Phone,
  Sparkles,
  SparklesIcon,
  X,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import Link from 'next/link';
import LinksManager from '@/lib/linksManager';
import Markdown from 'react-markdown';
import rehypeRaw from 'rehype-raw'; //to enable HTML in markdown
import remarkGfm from 'remark-gfm'; //to enable the extensions to markdown that GitHub adds with GFM
import animationStyles from './animation.module.css';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import useUserSession from '@/hooks/useUserSession';
import AgentAvatar from '@/components/Avatars/Agent';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  call?: CallDto;
  openCriterion: string;
  scorecard?: any;
}

export default function Coaching({ call, openCriterion, scorecard }: IProps) {
  const {
    isAdmin,
    isCompetitionOrg,
    aiCoachTextOverride,
    isLoggedIn,
    hideSimilarCalls,
    hideLearningMaterials,
    canAccess
  } = useUserSession();

  const { data: coachingInfoDb, isLoading: isLoadingCoachingInfoDb } =
    useCallCoaching(call?.vapiId || '', isLoggedIn);
  const { data: coachingInfoDemo, isLoading: isLoadingCoachingInfoDemo } =
    useDemoCallCoaching(call?.vapiId || '', !isLoggedIn);

  const coachingInfo = isLoggedIn ? coachingInfoDb : coachingInfoDemo;
  const isLoadingCoachingInfo = isLoggedIn
    ? isLoadingCoachingInfoDb
    : isLoadingCoachingInfoDemo;

  const [currentOpenCriteria, setCurrentOpenCriteria] = useState<string>('');
  const toggleDetails = (criteria: string) => {
    if (criteria === currentOpenCriteria) {
      setCurrentOpenCriteria('');
    } else {
      // if a criteria is pressed while another is open
      if (currentOpenCriteria) {
        setTimeout(() => {
          const target = document.getElementById(currentOpenCriteria);
          target?.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          });
        }, 0);
      }
      setCurrentOpenCriteria(criteria);
    }
  };

  const str_pad_left = (string: any, pad: any, length: any) => {
    return (new Array(length + 1).join(pad) + string).slice(-length);
  };

  useEffect(() => {
    setCurrentOpenCriteria(openCriterion);
    setTimeout(() => {
      const target = document.getElementById(openCriterion);
      if (target) {
        if (target.getBoundingClientRect().bottom > window.innerHeight) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          });
        }

        if (target.getBoundingClientRect().top < 0) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          });
        }
      }
    }, 500);
  }, [openCriterion]);

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  if (isLoadingCoachingInfo) {
    return (
      <div className="mt-6">
        <Skeleton className="w-full h-[40px] mb-3" />
        <Skeleton className="w-full h-[40px] mb-3" />
        <Skeleton className="w-full h-[40px] mb-3" />
        <Skeleton className="w-full h-[40px] mb-3" />
        <Skeleton className="w-full h-[40px] mb-3" />
        <Skeleton className="w-full h-[40px] mb-3" />
        <Skeleton className="w-full h-[40px] mb-3" />
      </div>
    );
  } else if (!coachingInfo) {
    return <div className="mt-3"></div>;
  } else {
    let currentSection = '';
    return (
      <AnimatePresence>
        <div className="mt-6">
          {coachingInfo.map((c: any, i: number) => {
            const passed = c.passed;
            let className =
              'flex items-center rounded-sm h-[12px] w-[12px] mr-3';
            if (passed) {
              className += ' cursor-pointer bg-green-600 hover:bg-green-400';
            } else {
              className += ' cursor-pointer bg-red-600 hover:bg-red-400';
            }
            let open = false;
            if (currentOpenCriteria === c.criteria) {
              open = true;
            }

            if (isLoggedIn && !c.coaching && c.similarCalls.length == 0) {
              return;
            }

            let printSectionHeader = false;
            if (currentSection !== c.sectionTitle) {
              currentSection = c.sectionTitle;
              printSectionHeader = true;
            }

            return (
              <>
                {printSectionHeader && (
                  <div className="text-muted-foreground mb-4 text-xs font-semibold">
                    {currentSection}
                  </div>
                )}
                <motion.div
                  initial={{ opacity: 0, y: -50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ y: -50 }}
                  transition={{
                    duration: 0.4,
                    delay: i * 0.1,
                  }}
                  key={'ci-' + i}
                  id={c.criteria}
                  className="border rounded-lg mb-3 py-3"
                >
                  {/*******************************/}
                  {/*********** HEADER ************/}
                  {/*******************************/}
                  <div
                    className="group cursor-pointer mx-3"
                    onClick={() => {
                      toggleDetails(c.criteria);
                    }}
                  >
                    <div className="flex items-center">
                      <div className={className}>
                        {passed ? (
                          <Check className="text-white" size={12} />
                        ) : (
                          <X className=" text-white" size={12} />
                        )}
                      </div>
                      <div className="text-sm flex-1">
                        <div>{c.criteria}</div>
                      </div>
                      <motion.div
                        animate={{
                          rotate: !open ? -180 : 0,
                        }}
                        transition={{
                          ease: 'easeOut',
                          duration: 0.4,
                          delay: 0,
                        }}
                        className="group-hover:text-black text-muted-foreground"
                      >
                        <ChevronUp size={16} />
                      </motion.div>
                    </div>
                    <div
                      className={
                        'flex flex-row text-xs space-x-1 items-center ml-6 mt-3'
                      }
                    >
                      <div className="text-muted-foreground">
                        {c.sectionTitle}
                      </div>
                      {c.coaching ? (
                        <span className="flex flex-row items-center space-x-1">
                          &bull;{' '}
                          <SparklesIcon className="w-2 h-2 ml-1 text-teal-500" />
                          <span className="text-teal-500">
                            AI Coaching Enabled
                          </span>
                        </span>
                      ) : (
                        <span className="flex flex-row items-center space-x-1">
                          &bull;{' '}
                          <SparklesIcon className="w-2 h-2 ml-1 text-red-500" />{' '}
                          <span className="text-red-500">
                            AI Coaching Disabled
                          </span>
                        </span>
                      )}
                    </div>
                  </div>

                  {/*******************************/}
                  {/********** DETAILS ************/}
                  {/*******************************/}
                  <motion.div
                    initial={false}
                    animate={{
                      height: open ? 'auto' : 0,
                    }}
                    transition={{
                      ease: 'easeOut',
                      duration: 0.4,
                      delay: 0,
                    }}
                    className={'overflow-hidden'}
                  >
                    {/********** AI FEEDBACK ********/}
                    <div
                      className="my-3 p-2 overflow-hidden "
                      style={{ zIndex: 0 }}
                    >
                      <div
                        className={
                          'p-1 rounded-lg ' +
                          (open &&
                            !!c.coaching &&
                            animationStyles.animationContainer)
                        }
                      >
                        <div
                          className="p-3 border rounded-lg "
                          style={{
                            background: c.coaching ? '#f7ffff' : '#ffdbdb',
                            zIndex: 10,
                          }}
                        >
                          <div className="text-xs flex items-center text-teal-500">
                            <div>
                              <Sparkles
                                size={14}
                                className={cn({
                                  'text-red-500': !c.coaching,
                                })}
                              />
                            </div>
                            <div
                              className={cn('ml-2', {
                                'text-red-500': !c.coaching,
                              })}
                            >
                              {aiCoachTextOverride || 'Hyperbound AI'}
                              {!c.coaching ? ' Disabled' : ''}
                            </div>
                          </div>
                          <div className="mt-4">
                            <div className="text-sm font-semibold">
                              Why were you scored this way?
                            </div>
                            <Markdown
                              remarkPlugins={[remarkGfm]}
                              rehypePlugins={[rehypeRaw]}
                              className="prose mt-3 text-sm leading-snug"
                            >
                              {c.coaching ||
                                (canAccess(AppPermissions.MANAGE_SCORECARDS)
                                  ? 'Edit scorecard to enable personalized AI coaching feedback for this criteria'
                                  : 'Ask your admin to enable personalized AI coaching feedback for this criteria.')}
                            </Markdown>
                          </div>

                          {c.improvement && (
                            <div className="mt-4">
                              <div className="text-sm font-semibold">
                                What could you do differently next time?
                              </div>
                              <Markdown
                                remarkPlugins={[remarkGfm]}
                                rehypePlugins={[rehypeRaw]}
                                className="prose mt-3 text-sm leading-snug"
                              >
                                {c.improvement}
                              </Markdown>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {/********** SIMILAR CALLS ************/}
                    {c.similarCalls.length > 0 &&
                      !isCompetitionOrg &&
                      !hideSimilarCalls && (
                        <div className="mt-3 mx-3">
                          <div className="text-sm">
                            Similar calls to listen to:
                          </div>
                          <div>
                            {c.similarCalls.map((sc: any, j: number) => {
                              const callLength = dayjs(sc.callEndedAt).diff(
                                dayjs(sc.callStartedAt),
                                'second',
                              );

                              const minutes = Math.floor(callLength / 60);
                              const seconds = callLength % 60;

                              const finalTime =
                                str_pad_left(minutes, '0', 2) +
                                ':' +
                                str_pad_left(seconds, '0', 2);

                              return (
                                <Link
                                  key={'sc-' + j}
                                  className="mt-2 flex items-center p-3 border rounded-lg text-muted-foreground"
                                  href={LinksManager.trainingCalls(
                                    `${sc.callVapiId}`,
                                  )}
                                  target="_blank"
                                >
                                  <div className="mr-3">
                                    <Phone size={16} />
                                  </div>
                                  <div>
                                    <AgentAvatar
                                      className="w-7 h-7"
                                      agent={sc}
                                    />
                                  </div>
                                  <div className="text-normal text-black ml-1">
                                    {sc?.firstName} {sc?.lastName}
                                  </div>
                                  <div className="text-muted-foreground ml-4 flex-1">
                                    {finalTime}
                                  </div>
                                  <div>
                                    {dayjs(sc.callStartedAt).format('MMM D')}
                                  </div>
                                  <div className="ml-2">
                                    <ExternalLink size={18} />
                                  </div>
                                </Link>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    {/********** LEARNING MATERIALS ************/}
                    {!hideLearningMaterials && (
                      <div className="mt-3 mx-3">
                        <div className="text-sm">Learning materials:</div>
                        {!(
                          scorecard?.scorecardConfigId === 494 &&
                          c.criteria === 'Permission based opener?'
                        ) ? (
                          <div className="border rounded-lg flex flex-col items-center justify-center mt-2 py-10">
                            <div className="bg-muted p-2 rounded-full text-muted-foreground">
                              <LinkIcon size={16} />
                            </div>
                            <div className="text-sm font-medium mt-6">
                              Add useful links and resources
                            </div>
                            <div className="text-xs mt-2 text-muted-foreground">
                              {isAdmin
                                ? 'Add valuable links and resources as a guide to get this question right next time.'
                                : 'Ask your admin to add valuable links and resources as a guide to get this question right next time.'}
                            </div>
                            {canAccess(AppPermissions.MANAGE_BOTS) && (
                              <div className="mt-6">
                                <Button variant={'outline'}>
                                  <Bot size={16} className="mr-2" />
                                  Edit AI Buyer
                                </Button>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="flex flex-col mt-2">
                            <iframe
                              width="300"
                              height="200"
                              src="https://www.youtube.com/embed/6gnNdezoHTY?si=C_1ue5YjaM51tgOP"
                              title="YouTube video player"
                              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                              referrerPolicy="strict-origin-when-cross-origin"
                              className="rounded-lg"
                              allowFullScreen
                            ></iframe>
                          </div>
                        )}
                      </div>
                    )}
                  </motion.div>
                </motion.div>
              </>
            );
          })}
        </div>
      </AnimatePresence>
    );
  }
}
