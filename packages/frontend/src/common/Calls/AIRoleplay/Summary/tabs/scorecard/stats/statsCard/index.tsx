import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { Info } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface IProps {
  recommendedRange: number[];
  title: string;
  isLoading: boolean;
  value: string;
  rawValue: number;
  description: string;
}

export default function CallStatCard({
  isLoading,
  title,
  recommendedRange,
  description,
  value,
  rawValue,
}: IProps) {
  const [lowerBound, upperBound] = recommendedRange || [0, 0];
  const aboveRecommendedRange = rawValue > upperBound;
  const belowRecommendedRange = rawValue < lowerBound;

  return (
    <div>
      <div className="flex items-center">
        <div className="flex-1 text-xs text-muted-foreground">{title}</div>
        <div className="text-muted-foreground">
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger>
                <Info className="w-4 h-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent className="max-w-72">
                <p>
                  {description}. The recommended range is between {lowerBound}{' '}
                  and {upperBound}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
      <div className="font-semibold mt-2">
        {isLoading ? <Skeleton className="w-[50%] h-[20px]" /> : value}
      </div>
      <div className="flex items-center space-x-2 mt-6">
        <div
          className={cn('rounded-full p-1 w-2 h-2', {
            'bg-green-500': !aboveRecommendedRange && !belowRecommendedRange,
            'bg-red-500': aboveRecommendedRange || belowRecommendedRange,
          })}
        />
        <p className="text-xs text-muted-foreground">
          {aboveRecommendedRange
            ? 'Above average'
            : belowRecommendedRange
              ? 'Below average'
              : 'Good'}
        </p>
      </div>
    </div>
  );
}
