import EditUserTeams from '@/common/Members/Teams/EditUserTeams';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import useTeams from '@/hooks/useTeams';
import useUserSession from '@/hooks/useUserSession';
import LinksManager from '@/lib/linksManager';
import { OrgLeaderboardDto } from '@/lib/Scorecard/types';
import { cn } from '@/lib/utils';
import { ExternalLink, Star } from 'lucide-react';
import Link from 'next/link';
import { useMemo } from 'react';

interface IProps {
  leaderboard?: OrgLeaderboardDto;
}

export default function List({ leaderboard }: IProps) {
  //console.log(leaderboard);
  const { showTeamsInLeaderboard, dbOrg } = useUserSession();

  const first = leaderboard?.leaderboard[0];
  const allSections: any[] = first?.criteria || [];
  const { data: teams } = useTeams(0, 500);
  const numUsersOfTeams = useMemo(() => {
    if (!teams) {
      return {};
    }
    return Object.fromEntries(teams.map((t) => [t.id, t.numberOfUsers || 0]));
  }, [teams]);
  const specificTeamsForUserId = useMemo(() => {
    if (leaderboard) {
      return Object.fromEntries(
        leaderboard.leaderboard.map((l) => {
          let specificTeam = (l.teams || [])[0];
          for (const team of l.teams || []) {
            const specificTeamUserCount = numUsersOfTeams[specificTeam.id] || 0;
            const currentTeamUserCount = numUsersOfTeams[team.id] || 0;
            if (currentTeamUserCount <= specificTeamUserCount) {
              specificTeam = team;
            }
          }
          return [l.userId, [specificTeam].filter((t) => !!t)];
        }),
      );
    }
    return {};
  }, [leaderboard, numUsersOfTeams]);
  console.log('onlyAdminCanViewAllCalls', dbOrg?.onlyAdminCanViewAllCalls);
  return (
    <div className="border rounded-lg bg-white">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="border-r">&nbsp;</TableHead>
            {allSections.map((section, index) => {
              return (
                <TableHead key={index} className="border-r text-primary">
                  {section.sectionTitle}
                </TableHead>
              );
            })}
            {dbOrg?.onlyAdminCanViewAllCalls === true ? (
              <></>
            ) : (
              <TableHead>&nbsp;</TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {leaderboard?.leaderboard.map((item, index) => {
            const teamDtos = specificTeamsForUserId[item?.userId || -1];
            return (
              <TableRow key={index}>
                <TableCell className="border-r ">
                  <div className="flex">
                    <div className="mr-2 text-muted-foreground">
                      {index + 1}
                    </div>
                    <Avatar className="w-4 h-4 mt-0.5">
                      {item?.avatar && <AvatarImage src={item.avatar} />}
                      <AvatarFallback className="text-sm text-muted-foreground">
                        {item?.firstName?.charAt(0) || ''}
                        {item?.lastName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>
                    <div className="ml-2 flex flex-col">
                      <p>
                        {item?.firstName} {item?.lastName}
                      </p>
                      {!!teamDtos.length && showTeamsInLeaderboard && (
                        <div className="mt-1">
                          <EditUserTeams
                            currentTeams={teamDtos}
                            userId={item?.userId}
                          />
                        </div>
                      )}
                    </div>
                    {index == 0 && (
                      <Star size={16} className="mt-0.5 ml-2 text-yellow-500" />
                    )}
                  </div>
                </TableCell>
                {allSections.map((section, index) => {
                  let cr: any;
                  for (const c of item?.criteria || []) {
                    if (c.sectionTitle === section.sectionTitle) {
                      cr = c;
                      break;
                    }
                  }
                  if (cr) {
                    let score = 0;
                    if (
                      cr.passedCriteriaCount > 0 &&
                      cr.totalCriteriaCount > 0
                    ) {
                      score = Math.round(
                        (cr.passedCriteriaCount / cr.totalCriteriaCount) * 100,
                      );
                    }

                    return (
                      <TableHead
                        key={index}
                        className={cn(
                          'border-r text-center text-black bg-red-300',
                          {
                            'bg-green-300': score >= 80,
                            'bg-orange-300': score >= 60 && score < 80,
                            'bg-yellow-300': score >= 40 && score < 60,
                          },
                        )}
                      >
                        {score}
                      </TableHead>
                    );
                  } else {
                    return (
                      <TableHead key={index} className="border-r">
                        &nbsp;
                      </TableHead>
                    );
                  }
                })}
                {dbOrg?.onlyAdminCanViewAllCalls === true ? (
                  <></>
                ) : (
                  <TableCell>
                    <Link
                      className="text-muted-foreground"
                      href={LinksManager.trainingCalls(`${item.callVapiId}`)}
                      target="_blank"
                    >
                      <ExternalLink size={18} />
                    </Link>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
