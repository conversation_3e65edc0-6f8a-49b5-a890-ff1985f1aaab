import { Skeleton } from '@/components/ui/skeleton';
import {
  CallDto,
  CallScorecardDto,
  CallScoringStatus,
  CallStatsDto,
  ExcludeFromAnalyticsReasonType,
} from '@/lib/Call/types';
import { AnimatePresence } from 'framer-motion';
import {
  BugIcon,
  ChevronDownIcon,
  ChevronRight,
  CircleHelp,
  ExternalLink,
  Lightbulb,
  Lock,
  RefreshCcw,
  TriangleAlert,
  XIcon,
} from 'lucide-react';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import CallStats from './stats';
import ScorecardSection from './scorecardSection';
import Link from 'next/link';
import LinksManager from '@/lib/linksManager';

import IncorrectlyScoredModal from './incorrectlyScoredModal';
import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useUserSession from '@/hooks/useUserSession';
import CallService from '@/lib/Call';
import { cn } from '@/lib/utils';
import TroubleshootingGuide from '@/components/TroubleshootingGuide';

interface IProps {
  isLoading: boolean;
  className?: string;
  scorecard?: CallScorecardDto;
  stats?: CallStatsDto;
  rescoreCall: () => void;
  call?: CallDto;
  goToCoaching?: (criteria: string) => void;
}

export default function CallSummaryScorecard({
  className,
  scorecard,
  isLoading,
  stats,
  rescoreCall,
  call,
  goToCoaching,
}: IProps) {
  const [showMicTroubleshootingGuide, setShowMicTroubleshootingGuide] =
    useState<boolean>(false);

  const callerId = call?.caller?.id || 0;
  const callId = call?.vapiId || '';

  const { dbOrg, canRescoreCalls } = useUserSession();

  const [currentTab, setCurrentTab] = useState<string>('ai');

  const onTabChange = (t: string) => {
    setCurrentTab(t);
  };

  const [openScoreIncorrect, setOpenScoreIncorrect] = useState<boolean>(false);
  const [openExcludeDetails, setOpenExcludeDetails] = useState<boolean>(false);
  const [openBugDeails, setOpenBugDeails] = useState<boolean>(false);

  const toggleScoreIncorrectInfoPanel = () => {
    setOpenScoreIncorrect(!openScoreIncorrect);
  };

  const toggleExcludeDetails = () => {
    setOpenExcludeDetails((s) => !s);
  };

  const toggleBugReportDetails = () => {
    setOpenBugDeails((s) => !s);
  };

  let scoringErrorMessage = '';
  let showMicInstructions = false;

  if (call?.scoringStatus == CallScoringStatus.ERROR) {
    if (call.vapiCallEndedReason) {
      const errorState = CallService.getVapiEndedReasonInfo(
        call.vapiCallEndedReason,
      );

      if (errorState.isError) {
        scoringErrorMessage = errorState.message;
        if (
          call.vapiCallEndedReason ==
          'customer-did-not-give-microphone-permission'
        ) {
          showMicInstructions = true;
        }
      }
    }
  }

  /*****************************/
  /********* RENDER ************/
  /*****************************/
  return (
    <AnimatePresence>
      <div className={className}>
        {call?.relatedAgentCall && (
          <div className="mb-6">
            <Link
              href={LinksManager.trainingCalls(call?.relatedAgentCall?.vapiId)}
              className="flex items-center font-semibold"
            >
              Open buyer call stats <ExternalLink size={18} className="ml-2" />
            </Link>
          </div>
        )}

        <div className="mb-4">
          {call?.buggedOut && (
            <div className="border border-red-500 hover:border-red-300 rounded-lg p-2">
              <div
                className="flex items-center cursor-pointer"
                onClick={toggleBugReportDetails}
              >
                <div className="mr-3 text-red-400">
                  <BugIcon size={16} />
                </div>
                <div className="flex-1 text-sm">Bug Reported</div>
                {openBugDeails ? (
                  <ChevronDownIcon className="w-4 h-4 " />
                ) : (
                  <ChevronRight className="w-4 h-4 " />
                )}
              </div>
              {openBugDeails && (
                <div className="self-stretch px-6 flex-col justify-center items-start gap-2 flex-auto ml-2">
                  <div className="self-stretch text-muted-foreground text-sm font-normal ">
                    Reporter:{' '}
                    {`${call?.caller?.firstName} ${call?.caller?.lastName}`}
                  </div>
                  <div className="self-stretch text-muted-foreground text-sm font-normal ">
                    Description:{' '}
                    {call?.bugDescription || 'No description provided'}
                  </div>
                </div>
              )}
            </div>
          )}

          {call?.excludeFromAnalytics && (
            <div className="border border-orange-500 hover:border-orange-300 rounded-lg p-2 mt-4">
              <div
                className="flex items-center cursor-pointer"
                onClick={toggleExcludeDetails}
              >
                <div className="mr-3 text-orange-400">
                  <XIcon size={16} />
                </div>
                <div className="flex-1 text-sm">Excluded from analytics</div>

                {openExcludeDetails ? (
                  <ChevronDownIcon className="w-4 h-4 " />
                ) : (
                  <ChevronRight className="w-4 h-4 " />
                )}
              </div>
              {openExcludeDetails && (
                <div className="self-stretch px-6 flex-col justify-center items-start gap-2 flex-auto ml-2">
                  <div className="self-stretch text-muted-foreground text-sm font-normal ">
                    By:{' '}
                    {call?.excludeFromAnalyticsReasonType ===
                    ExcludeFromAnalyticsReasonType.ANALYTICS_MINIMUM_REQUIREMENTS
                      ? 'Hyperbound'
                      : `${call?.caller?.firstName} ${call?.caller?.lastName}`}
                  </div>
                  <div className="self-stretch text-muted-foreground text-sm font-normal ">
                    Reason:{' '}
                    {call?.excludeFromAnalyticsReason || 'No reason provided'}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {!dbOrg?.isCompetitionOrg && !scoringErrorMessage && (
          <div className="border border-yellow-400 hover:border-yellow-300 rounded-lg p-2">
            <div
              className="flex items-center cursor-pointer"
              onClick={toggleScoreIncorrectInfoPanel}
            >
              <div className="mr-3 text-yellow-400">
                <Lightbulb size={16} />
              </div>
              <div className="flex-1 text-sm">Incorrectly scored?</div>
              <div className="ml-1">
                <ChevronRight size={16} />
              </div>
            </div>
          </div>
        )}

        {!dbOrg?.isCompetitionOrg && scoringErrorMessage && (
          <div className="border border-red-400 hover:border-red-300 rounded-lg p-2">
            <div
              className={cn('flex items-center cursor-pointer', {
                'cursor-pointer': showMicInstructions,
              })}
              onClick={() => {
                if (showMicInstructions) {
                  setShowMicTroubleshootingGuide(true);
                }
              }}
            >
              <div className="mr-3 text-red-400">
                <TriangleAlert size={16} />
              </div>
              <div className="flex-1 text-sm">
                {`${scoringErrorMessage}${showMicInstructions && '.'}`}{' '}
                {showMicInstructions && 'Click here for more info'}
              </div>
              {showMicInstructions && (
                <div className="ml-1">
                  <CircleHelp size={16} />
                </div>
              )}
            </div>
          </div>
        )}

        <div className="mt-6">
          <CallStats isLoading={isLoading} stats={stats} />
        </div>
        <Tabs defaultValue={currentTab} value={currentTab} className="w-full">
          <div className="flex items-center mt-6">
            <div className="flex-1">
              <TabsList>
                <TabsTrigger value="ai" onClick={() => onTabChange('ai')}>
                  AI Review
                </TabsTrigger>

                <TabsTrigger value="dummy">
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger asChild className="opacity-[0.5]">
                        <div className="flex items-center">
                          Manual Review <Lock size={16} className="ml-2" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        Not enabled for your organization.
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TabsTrigger>
              </TabsList>
            </div>
            {call?.scoringStatus != CallScoringStatus.SCORING &&
              canRescoreCalls && (
                <div className="flex items-center">
                  <Button variant={'ghost'} onClick={rescoreCall}>
                    <>
                      <RefreshCcw size={16} className="mr-2" />
                      Rescore
                    </>
                  </Button>
                </div>
              )}
          </div>

          <TabsContent value="ai">
            <div className="pt-4 pb-6">
              {isLoading ? (
                <div className="grid grid-cols-2 items-stretch gap-4 pb-4 md:grid-cols-2 w-full">
                  <Skeleton className="w-full h-[250px] rounded-xl" />
                  <Skeleton className="w-full h-[250px] rounded-xl" />
                  <Skeleton className="w-full h-[250px] rounded-xl" />
                  <Skeleton className="w-full h-[250px] rounded-xl" />
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-4">
                  {scorecard?.scorecards.map((sc, i) => {
                    return (
                      <ScorecardSection
                        key={i}
                        delayBy={i}
                        section={sc}
                        callerId={callerId}
                        callId={callId}
                        goToCoaching={goToCoaching}
                      />
                    );
                  })}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
        <IncorrectlyScoredModal
          open={openScoreIncorrect}
          setModalOpen={setOpenScoreIncorrect}
        />
        <TroubleshootingGuide
          open={showMicTroubleshootingGuide}
          onClose={() => {
            setShowMicTroubleshootingGuide(false);
          }}
        />
      </div>
    </AnimatePresence>
  );
}
