import { OrgLeaderboardDto } from '@/lib/Scorecard/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AnimatePresence, motion } from 'framer-motion';

interface IProps {
  leaderboard?: OrgLeaderboardDto;
}

export default function Podium({ leaderboard }: IProps) {
  const first = leaderboard?.leaderboard[0];
  const second = leaderboard?.leaderboard[1];
  const third = leaderboard?.leaderboard[2];

  return (
    <AnimatePresence>
      <div className="w-full border rounded-lg h-[180px] flex items-stretch bg-white">
        <div className="flex-1 ml-3 flex flex-col justify-end">
          {second && (
            <div className="flex flex-col items-center mb-4">
              <Avatar className="w-[40px] h-[40px] mb-2">
                {second?.avatar && <AvatarImage src={second.avatar} />}
                <AvatarFallback className="text-sm text-muted-foreground">
                  {second?.firstName?.charAt(0) || ''}
                  {second?.lastName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
              <div className="">
                {second?.firstName} {second?.lastName}
              </div>
            </div>
          )}
          <div className="w-full">
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: '50px' }}
              transition={{
                duration: 0.4,
                delay: 0.3,
              }}
              style={{ backgroundColor: '#A9B0B8' }}
            >
              <div className="flex items-center justify-center rounded-t-lg"></div>
            </motion.div>
          </div>
        </div>
        <div className="flex-1 mx-3 flex flex-col justify-end">
          {first && (
            <div className="flex flex-col items-center mb-4">
              <Avatar className="w-[40px] h-[40px] mb-2">
                {first?.avatar && <AvatarImage src={first.avatar} />}
                <AvatarFallback className="text-sm text-muted-foreground">
                  {first?.firstName?.charAt(0) || ''}
                  {first?.lastName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
              <div>
                {first?.firstName} {first?.lastName}
              </div>
            </div>
          )}
          <div className="w-full">
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: '70px' }}
              transition={{
                duration: 0.4,
                delay: 0.3,
              }}
              style={{ backgroundColor: '#DAB969' }}
              className="flex items-center justify-center rounded-t-lg"
            >
              <div className="text-white mt-1 text-lg ">1</div>
            </motion.div>
          </div>
        </div>
        <div className="flex-1 mr-3 flex flex-col justify-end">
          {third && (
            <div className="flex flex-col items-center mb-4">
              <Avatar className="w-[40px] h-[40px] mb-2">
                {third?.avatar && <AvatarImage src={third.avatar} />}
                <AvatarFallback className="text-sm text-muted-foreground">
                  {third?.firstName?.charAt(0) || ''}
                  {third?.lastName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
              <div>
                {third?.firstName} {third?.lastName}
              </div>
            </div>
          )}
          <div className="w-full">
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: '30px' }}
              transition={{
                duration: 0.4,
                delay: 0.3,
              }}
              style={{ backgroundColor: '#D48833' }}
              className="flex items-center justify-center rounded-t-lg"
            >
              <div className="text-white mt-1 text-lg ">3</div>
            </motion.div>
          </div>
        </div>
      </div>
    </AnimatePresence>
  );
}
