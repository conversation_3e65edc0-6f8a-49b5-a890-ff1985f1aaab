import { useAggregateScoreBreakdownForSingleCallModal } from '@/common/AggregateScoreBreakdownModal';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useCallScorecard from '@/hooks/useCallScorecard';
import useCallScoreInfo from '@/hooks/useCallScoreInfo';
import useCallStats from '@/hooks/useCallStats';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import { CallDto, CallScoringStatus } from '@/lib/Call/types';
import { cn } from '@/lib/utils';
import {
  AlignLeft,
  BookMarked,
  Loader2Icon,
  Lock,
  LockIcon,
  Sparkles,
  Target,
  Trophy,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import Coaching from './coaching';
import Leaderboard from './leaderboard';
import CallSummaryQuestionsAndObjections from './questionsAndObjections';
import CallSummaryScorecard from './scorecard';
import Link from 'next/link';
import { useCompetitionInfoForCall } from '@/hooks/useLearningModules';
import ViewLearningMaterials from '@/common/Scorecards/LearningMaterials/View';
import { useIsInIframe } from '@/hooks/useIsInIframe';
import { TaskAndAttempts } from '@/lib/LearningModule/types';
import { useParams } from 'next/navigation';
import { ScorecardConfigMaterialScope } from '@/lib/ScorecardConfig/types';
import useScorecardConfigById from '@/hooks/useScorecardConfigById';

interface IProps {
  isLoadingCall: boolean;
  vapiId?: string;
  call?: CallDto;
  rescoreCall: () => void;
  openTab?: string;
  onlyScorecard?: boolean;
  showLeaderboardDateFilter?: boolean;
  taskAndAttempts?: TaskAndAttempts;
}

export default function CallSummaryTabs({
  isLoadingCall,
  vapiId: _vapiId,
  call,
  rescoreCall,
  openTab,
  onlyScorecard = false,
  showLeaderboardDateFilter = false,
  taskAndAttempts,
}: IProps) {
  const params = useParams();
  const vapiId = _vapiId || (params.id as string);
  const {
    isLoggedIn,
    isCompetitionOrg,
    competitionTag,
    blurLeaderboard,
    canAccessScorecardsMaterials,
  } = useUserSession();
  const { modal, setModalOpen } =
    useAggregateScoreBreakdownForSingleCallModal(vapiId);

  const { setUrlParameter } = useRouting();
  const isInIframe = useIsInIframe();
  const [currentTab, setCurrentTab] = useState<string>('scorecard');

  useEffect(() => {
    if (openTab && openTab !== '') {
      setCurrentTab(openTab);
    } else {
      setCurrentTab('scorecard');
    }
  }, [openTab]);

  /*****************************/
  /*********** INIT ************/
  /*****************************/

  const {
    data: internalCompetitionsInfo,
    isLoading: isLoadingInternalCompInfo,
  } = useCompetitionInfoForCall(vapiId || '');

  let showInternalCompLabel = false;
  if (!isLoadingInternalCompInfo && internalCompetitionsInfo) {
    if (internalCompetitionsInfo.length > 0) {
      showInternalCompLabel = true;
    }
  }

  const {
    data: stats,
    isLoading: isLoadingStats,
    isFetched: isStatsFetched,
  } = useCallStats(vapiId || '', !isLoggedIn, !isLoadingCall);

  const { data: scorecard, isLoading: isLoadingScorecard } = useCallScorecard(
    vapiId || '',
    !isLoggedIn,
    isStatsFetched && !isLoadingCall,
  );
  const scorecardConfigId = scorecard?.scorecardConfigId || 0;
  const { data: scorecardConfig } = useScorecardConfigById(
    scorecardConfigId,
    !!scorecardConfigId,
  );

  useEffect(() => {
    if (scorecard && !isLoadingScorecard && isInIframe) {
      const scormParams: Record<string, string> = {};

      const minScore = 0;
      const maxScore = 100;
      const rawScore = Number(
        (scorecard?.passedScore / scorecard?.totalScore) * 100 || 0,
      );

      scormParams['cmi.score.min'] = minScore.toString();
      scormParams['cmi.score.max'] = maxScore.toString();
      scormParams['cmi.score.raw'] = `${Number(
        (scorecard?.passedScore / scorecard?.totalScore) * 100 || 0,
      )}`;
      scormParams['cmi.score.scaled'] = `${Number(
        scorecard?.passedScore / scorecard?.totalScore || 0,
      )}`;

      if (taskAndAttempts) {
        const isCurrentAttemptApproved =
          rawScore >= Number(taskAndAttempts.task.minScorecardScore) ||
          taskAndAttempts.task.minScorecardScore === 0;
        const necessarySuccessfulAttempts =
          taskAndAttempts.task.minNumberOfAttempts || 1;

        const attempts = taskAndAttempts.attempts || [];
        const successfulAttempts = attempts.filter((a) => a.passed).length;
        const isApproved =
          successfulAttempts + (isCurrentAttemptApproved ? 1 : 0) >=
          necessarySuccessfulAttempts;

        const isCompleted = taskAndAttempts.task.maxNumberOfAttempts
          ? attempts.length + 1 >= taskAndAttempts.task.maxNumberOfAttempts
          : isApproved;

        const completionStatus = isCompleted ? 'completed' : 'incomplete';
        const successStatus = isApproved ? 'passed' : 'failed';

        scormParams['cmi.completion_status'] = completionStatus;

        if (isCompleted) {
          scormParams['cmi.success_status'] = successStatus;
        }
      } else {
        scormParams['cmi.completion_status'] = 'completed';
        scormParams['cmi.success_status'] =
          rawScore >= 80 ? 'passed' : 'failed';
      }

      window.parent?.postMessage(
        {
          type: 'SCORECARD_LOADED',
          scormParams,
        },
        '*',
      );
    }
  }, [scorecard, isLoadingScorecard, isInIframe, taskAndAttempts]);

  const { callScoreTitle, callScore, callScoreColor } =
    useCallScoreInfo(scorecard);

  /*****************************/
  /********* ACTIONS ***********/
  /*****************************/

  const [highlightCriteria, setHighlightCriteria] = useState<string>('');

  const goToCoaching = (criteria: string) => {
    setHighlightCriteria(criteria);
    onTabChange('coaching', false);
  };

  const onTabChange = (t: string, deleteHighlightCriteria = true) => {
    setCurrentTab(t);
    setUrlParameter('tab', t);
    if (deleteHighlightCriteria) {
      setHighlightCriteria('');
    }
  };

  const triggersOtherThanScorecards = (
    <>
      <TabsTrigger value="coaching" onClick={() => onTabChange('coaching')}>
        <Sparkles size={12} className="mr-1" />
        AI Coaching
      </TabsTrigger>

      {isCompetitionOrg ? (
        <Link
          href={`/competitions/${competitionTag}/leaderboard?tab=${call?.agentId}`}
        >
          {isLoggedIn && (
            <TabsTrigger
              value="leaderboard"
              onClick={() => onTabChange('leaderboard')}
            >
              <Trophy size={12} className="mr-1" />
              Leaderboard
            </TabsTrigger>
          )}
        </Link>
      ) : (
        <>
          {isLoggedIn && (
            <TabsTrigger
              value="leaderboard"
              disabled={blurLeaderboard}
              onClick={
                blurLeaderboard ? undefined : () => onTabChange('leaderboard')
              }
            >
              {blurLeaderboard ? (
                <LockIcon size={12} className="mr-1" />
              ) : (
                <Trophy size={12} className="mr-1" />
              )}
              Leaderboard
            </TabsTrigger>
          )}
        </>
      )}
      {!isLoggedIn && (
        <TabsTrigger value="dummy2">
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger asChild className="opacity-[0.5]">
                <div className="flex items-center">
                  <Trophy size={12} className="mr-1" />
                  Leaderboard <Lock size={12} className="ml-2" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                Book a demo to access leaderboard
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </TabsTrigger>
      )}

      <TabsTrigger value="objections" onClick={() => onTabChange('objections')}>
        <AlignLeft size={12} className="mr-1" />
        Objections
      </TabsTrigger>

      {call?.relatedGatekeeperCall && (
        <TabsTrigger
          value="gatekeeper"
          onClick={() => onTabChange('gatekeeper')}
        >
          Gatekeeper
        </TabsTrigger>
      )}
      {canAccessScorecardsMaterials && (
        <TabsTrigger
          value="learning-materials"
          onClick={() => onTabChange('learning-materials')}
        >
          <BookMarked size={12} className="mr-1" />
          Resources
        </TabsTrigger>
      )}
    </>
  );

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  return (
    <Tabs defaultValue={currentTab} value={currentTab} className="w-full">
      <div className="flex items-center">
        <div className="flex-1">
          <TabsList>
            <TabsTrigger
              value="scorecard"
              onClick={() => onTabChange('scorecard')}
            >
              <Target size={12} className="mr-1" />
              Scorecard
            </TabsTrigger>

            {!onlyScorecard && triggersOtherThanScorecards}
          </TabsList>
        </div>

        {call?.scoringStatus == CallScoringStatus.SCORING && (
          <div className="flex items-center ml-2 ">
            <div className="text-xs mr-2">Scoring in progress</div>
            <div>
              <Loader2Icon className="animate-spin" size={18} />
            </div>
          </div>
        )}

        {call?.scoringStatus != CallScoringStatus.SCORING && (
          <div className="flex items-center ml-2">
            <div className="text-xs mr-2">
              {showInternalCompLabel ? (
                <div className="text-xs mr-2">
                  <div>
                    You scored <b>{internalCompetitionsInfo[0].rank}</b> on the
                    leaderboard.
                  </div>
                  <div className="text-muted-foreground">
                    Filler words scoring was used as a tie breaker.
                  </div>
                </div>
              ) : (
                <div className="text-xs mr-2">{callScoreTitle}</div>
              )}
            </div>

            <div
              className={cn(
                'min-h-[48px] min-w-[48px] rounded-full flex items-center justify-center text-white font-semibold text-lg',
                {
                  'cursor-pointer': isCompetitionOrg,
                },
              )}
              style={{ backgroundColor: callScoreColor }}
              {...(isCompetitionOrg
                ? {
                    onClick: () => {
                      setModalOpen(true);
                    },
                  }
                : {})}
            >
              {callScore}
            </div>
          </div>
        )}
      </div>

      <TabsContent value="scorecard">
        <CallSummaryScorecard
          className="pt-3"
          scorecard={scorecard}
          isLoading={isLoadingScorecard || isLoadingStats}
          stats={stats}
          rescoreCall={rescoreCall}
          call={call}
          goToCoaching={onlyScorecard ? undefined : goToCoaching}
        />
      </TabsContent>
      <TabsContent value="objections">
        <CallSummaryQuestionsAndObjections call={call} className="mt-4" />
      </TabsContent>
      <TabsContent value="coaching">
        <Coaching
          openCriterion={highlightCriteria}
          scorecard={scorecard}
          scorecardConfig={scorecardConfig}
        />
      </TabsContent>
      <TabsContent value="leaderboard">
        <Leaderboard
          agentId={call?.agent?.id}
          className="mt-5"
          showLeaderboardDateFilter={showLeaderboardDateFilter}
        />
      </TabsContent>
      <TabsContent value="learning-materials">
        {scorecard && scorecard?.scorecardConfigId && (
          <ViewLearningMaterials
            scorecardConfigId={scorecard?.scorecardConfigId}
            scope={ScorecardConfigMaterialScope.SCORECARD}
          />
        )}
      </TabsContent>
      <div className="pb-24" />
      {modal}
    </Tabs>
  );
}
