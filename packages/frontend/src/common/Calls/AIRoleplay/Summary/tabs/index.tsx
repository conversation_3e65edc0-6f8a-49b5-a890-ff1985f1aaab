import { useAggregateScoreBreakdownForSingleCallModal } from '@/common/AggregateScoreBreakdownModal';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useCallScorecard from '@/hooks/useCallScorecard';
import useCallScoreInfo from '@/hooks/useCallScoreInfo';
import useCallStats from '@/hooks/useCallStats';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import { CallDto, CallScoringStatus } from '@/lib/Call/types';
import { cn } from '@/lib/utils';
import {
  AlignLeft,
  Loader2Icon,
  Lock,
  LockIcon,
  Sparkles,
  Target,
  Trophy,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import Coaching from './coaching';
import Leaderboard from './leaderboard';
import CallSummaryQuestionsAndObjections from './questionsAndObjections';
import CallSummaryScorecard from './scorecard';
import Link from 'next/link';
import { useCompetitionInfoForCall } from '@/hooks/useLearningModules';
import { AppPermissions } from '@/lib/permissions';
interface IProps {
  isLoadingCall: boolean;
  call?: CallDto;
  rescoreCall: () => void;
  openTab?: string;
  onlyScorecard?: boolean;
  showLeaderboardDateFilter?: boolean;
}

export default function CallSummaryTabs({
  isLoadingCall,
  call,
  rescoreCall,
  openTab,
  onlyScorecard = false,
  showLeaderboardDateFilter = false,
}: IProps) {
  const {
    isLoggedIn,
    isCompetitionOrg,
    competitionTag,
    blurLeaderboard,
    canAccess,
  } = useUserSession();
  const { modal, setModalOpen } =
    useAggregateScoreBreakdownForSingleCallModal(call?.vapiId);

  const { setUrlParameter } = useRouting();

  const [currentTab, setCurrentTab] = useState<string>('scorecard');

  useEffect(() => {
    if (openTab && openTab !== '') {
      setCurrentTab(openTab);
    } else {
      setCurrentTab('scorecard');
    }
  }, [openTab]);

  /*****************************/
  /*********** INIT ************/
  /*****************************/

  const {
    data: internalCompetitionsInfo,
    isLoading: isLoadingInternalCompInfo,
  } = useCompetitionInfoForCall(call?.vapiId || '');

  let showInternalCompLabel = false;
  if (!isLoadingInternalCompInfo && internalCompetitionsInfo) {
    if (internalCompetitionsInfo.length > 0) {
      showInternalCompLabel = true;
    }
  }

  const {
    data: stats,
    isLoading: isLoadingStats,
    isFetched: isStatsFetched,
  } = useCallStats(call?.vapiId || '', !isLoggedIn, !isLoadingCall);

  const { data: scorecard, isLoading: isLoadingScorecard } = useCallScorecard(
    call?.vapiId || '',
    !isLoggedIn,
    isStatsFetched && !isLoadingCall,
  );

  const { callScoreTitle, callScore, callScoreColor } =
    useCallScoreInfo(scorecard);

  /*****************************/
  /********* ACTIONS ***********/
  /*****************************/

  const [highlightCriteria, setHighlightCriteria] = useState<string>('');

  const goToCoaching = (criteria: string) => {
    setHighlightCriteria(criteria);
    onTabChange('coaching', false);
  };

  const onTabChange = (t: string, deleteHighlightCriteria = true) => {
    setCurrentTab(t);
    setUrlParameter('tab', t);
    if (deleteHighlightCriteria) {
      setHighlightCriteria('');
    }
  };

  const triggersOtherThanScorecards = (
    <>
      <TabsTrigger value="coaching" onClick={() => onTabChange('coaching')}>
        <Sparkles size={12} className="mr-1" />
        AI Coaching
      </TabsTrigger>

      {isCompetitionOrg ? (
        <Link
          href={`/competitions/${competitionTag}/leaderboard?tab=${call?.agentId}`}
        >
          {isLoggedIn && (
            <TabsTrigger
              value="leaderboard"
              onClick={() => onTabChange('leaderboard')}
            >
              <Trophy size={12} className="mr-1" />
              Leaderboard
            </TabsTrigger>
          )}
        </Link>
      ) : (
        <>
          {isLoggedIn && (
            <TabsTrigger
              value="leaderboard"
              disabled={
                blurLeaderboard &&
                !canAccess(AppPermissions.VIEW_LEADERBOARD)
              }
              onClick={
                blurLeaderboard && !canAccess(AppPermissions.VIEW_LEADERBOARD)
                  ? undefined
                  : () => onTabChange('leaderboard')
              }
            >
              {blurLeaderboard && !canAccess(AppPermissions.VIEW_LEADERBOARD) ? (
                <LockIcon size={12} className="mr-1" />
              ) : (
                <Trophy size={12} className="mr-1" />
              )}
              Leaderboard
            </TabsTrigger>
          )}
        </>
      )}
      {!isLoggedIn && (
        <TabsTrigger value="dummy2">
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger asChild className="opacity-[0.5]">
                <div className="flex items-center">
                  <Trophy size={12} className="mr-1" />
                  Leaderboard <Lock size={12} className="ml-2" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                Book a demo to access leaderboard
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </TabsTrigger>
      )}

      <TabsTrigger value="objections" onClick={() => onTabChange('objections')}>
        <AlignLeft size={12} className="mr-1" />
        Objections
      </TabsTrigger>

      {call?.relatedGatekeeperCall && (
        <TabsTrigger
          value="gatekeeper"
          onClick={() => onTabChange('gatekeeper')}
        >
          Gatekeeper
        </TabsTrigger>
      )}
    </>
  );

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  return (
    <Tabs defaultValue={currentTab} value={currentTab} className="w-full">
      <div className="flex items-center">
        <div className="flex-1">
          <TabsList>
            <TabsTrigger
              value="scorecard"
              onClick={() => onTabChange('scorecard')}
            >
              <Target size={12} className="mr-1" />
              Scorecard
            </TabsTrigger>

            {!onlyScorecard && triggersOtherThanScorecards}
          </TabsList>
        </div>

        {call?.scoringStatus == CallScoringStatus.SCORING && (
          <div className="flex items-center ml-2 ">
            <div className="text-xs mr-2">Scoring in progress</div>
            <div>
              <Loader2Icon className="animate-spin" size={18} />
            </div>
          </div>
        )}

        {call?.scoringStatus != CallScoringStatus.SCORING && (
          <div className="flex items-center ml-2">
            <div className="text-xs mr-2">
              {showInternalCompLabel ? (
                <div className="text-xs mr-2">
                  <div>
                    You scored <b>{internalCompetitionsInfo[0].rank}</b> on the
                    leaderboard.
                  </div>
                  <div className="text-muted-foreground">
                    Filler words scoring was used as a tie breaker.
                  </div>
                </div>
              ) : (
                <div className="text-xs mr-2">{callScoreTitle}</div>
              )}
            </div>

            <div
              className={cn(
                'min-h-[48px] min-w-[48px] rounded-full flex items-center justify-center text-white font-semibold text-lg',
                {
                  'cursor-pointer': isCompetitionOrg,
                },
              )}
              style={{ backgroundColor: callScoreColor }}
              {...(isCompetitionOrg
                ? {
                    onClick: () => {
                      setModalOpen(true);
                    },
                  }
                : {})}
            >
              {callScore}
            </div>
          </div>
        )}
      </div>

      <TabsContent value="scorecard">
        <CallSummaryScorecard
          className="pt-3"
          scorecard={scorecard}
          isLoading={isLoadingScorecard || isLoadingStats}
          stats={stats}
          rescoreCall={rescoreCall}
          call={call}
          goToCoaching={onlyScorecard ? undefined : goToCoaching}
        />
      </TabsContent>
      <TabsContent value="objections">
        <CallSummaryQuestionsAndObjections call={call} className="mt-4" />
      </TabsContent>
      <TabsContent value="coaching">
        <Coaching
          call={call}
          openCriterion={highlightCriteria}
          scorecard={scorecard}
        />
      </TabsContent>
      <TabsContent value="leaderboard">
        <Leaderboard
          agentId={call?.agent?.id}
          className="mt-5"
          showLeaderboardDateFilter={showLeaderboardDateFilter}
        />
      </TabsContent>
      <div className="pb-24" />
      {modal}
    </Tabs>
  );
}
