import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useUserSession from '@/hooks/useUserSession';
import CallService from '@/lib/Call';
import { RepsCanEditScoreResults } from '@/lib/Organization/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { Check, ChevronRight, SparklesIcon, X } from 'lucide-react';
import { useRef } from 'react';
import { Id, toast } from 'react-toastify';

interface IProps {
  section: any;
  callId: string;
  callerId: number;
  delayBy?: number;
  goToCoaching?: (criterion: string) => void;
}
export default function ScorecardSection({
  section,
  callId,
  callerId,
  delayBy,
  goToCoaching,
}: IProps) {
  if (!delayBy) {
    delayBy = 0;
  }

  const { isAdmin, isLoggedIn, dbOrg, dbUser, aiCoachTextOverride } =
    useUserSession();
  const errorToastId = useRef<Id | null>(null);
  const queryClient = useQueryClient();
  let disableToggleCriteria = false;

  if (!isLoggedIn || dbOrg?.isCompetitionOrg) {
    disableToggleCriteria = true;
  } else {
    if (dbOrg?.repsCanEditScoreResults === RepsCanEditScoreResults.NO) {
      disableToggleCriteria = true;
    } else if (!isAdmin && dbUser?.id != callerId) {
      disableToggleCriteria = true;
    }
  }

  /***********************************/
  /*********** ACITONS ***************/
  /***********************************/

  const toggleScorecardCriteriaMutation = useMutation({
    mutationFn: CallService.toggleScorecardCriteriaForCall,
    onSuccess: (data, params) => {
      queryClient.invalidateQueries({
        queryKey: ['callScorecard', false, callId],
      });
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error toggling the scorecard criteria');
        errorToastId.current = toast.error(
          'There was an error toggling the scorecard criteria. Please try again.',
        );
      }
    },
  });

  const toggleDisputedScorecardCriteriaMutation = useMutation({
    mutationFn: CallService.toggleDisputedScorecardCriteriaForCall,
    onSuccess: (data, params) => {
      queryClient.invalidateQueries({
        queryKey: ['callScorecard', false, callId],
      });
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error toggling the scorecard criteria');
        errorToastId.current = toast.error(
          'There was an error toggling the scorecard criteria. Please try again.',
        );
      }
    },
  });

  const openCriterionDetails = (criterion: string) => {
    goToCoaching?.(criterion);
  };

  /***********************************/
  /*********** RENDER ****************/
  /***********************************/

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ y: -50 }}
      transition={{
        duration: 0.4,
        delay: delayBy * 0.1,
      }}
      className="rounded-lg border bg-white"
    >
      {/* HEADER */}
      <div className="flex items-center text-xs p-3">
        <div className="font-semibold flex-1">{section.sectionTitle}</div>
        <div>
          <Badge variant={'secondary'}>
            {section.passedCriteriaCount} / {section.totalCriteriaCount}
          </Badge>
        </div>
      </div>

      {/* BODY */}
      <div>
        {section.criteria.map((criterion: any, i: number) => {
          let border = 'border-t';
          if (i === 0) {
            border = '';
          }
          const passed = criterion.passed;
          let className =
            'flex items-center rounded-sm h-[12px] w-[12px] mt-3 ml-3 mr-1';

          if (!disableToggleCriteria) {
            if (criterion.disputed) {
              className += ' cursor-pointer bg-yellow-600 hover:bg-yellow-400';
            } else if (passed) {
              className += ' cursor-pointer bg-green-600 hover:bg-green-400';
            } else {
              className += ' cursor-pointer bg-red-600 hover:bg-red-400';
            }
          } else {
            if (criterion.disputed) {
              className += ' bg-yellow-600';
            }
            if (passed) {
              className += ' bg-green-600';
            } else {
              className += ' bg-red-600';
            }
          }

          let show = 'disabled';

          if (!disableToggleCriteria) {
            if (!criterion.toggled && !criterion.disputed) {
              show = 'initial';
            } else if (criterion.toggled) {
              show = 'overwrite';
            }
            if (criterion.toggled || criterion.disputed) {
              show = 'overwrite';
            }
          }

          let sectionClass = ' ';
          if (!passed) {
            sectionClass = ' group';
            if (goToCoaching) {
              sectionClass += ' hover:bg-muted cursor-pointer';
            }
          }

          return (
            <div
              key={'criterion-' + i}
              className={'m-0 flex ' + border + sectionClass}
            >
              <div>
                {show == 'disabled' && (
                  <div className={className}>
                    {passed ? (
                      <Check className="text-white" size={12} />
                    ) : (
                      <X className=" text-white" size={12} />
                    )}
                  </div>
                )}

                {show == 'initial' && (
                  <AlertDialog>
                    <TooltipProvider delayDuration={50}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span tabIndex={0}>
                            <AlertDialogTrigger asChild>
                              <div className={className}>
                                {passed ? (
                                  <Check className="text-white" size={12} />
                                ) : (
                                  <X className=" text-white" size={12} />
                                )}
                              </div>
                            </AlertDialogTrigger>
                          </span>
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          {dbOrg?.repsCanEditScoreResults ===
                            RepsCanEditScoreResults.YES || isAdmin ? (
                            <p>
                              Incorrectly scored? Click to change score
                              {!isAdmin && <> &amp; report</>}
                            </p>
                          ) : (
                            <p>Dispute score</p>
                          )}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        {dbOrg?.repsCanEditScoreResults ===
                          RepsCanEditScoreResults.YES || isAdmin ? (
                          <>
                            <AlertDialogTitle>
                              Are you absolutely sure?
                            </AlertDialogTitle>
                            {isAdmin ? (
                              <AlertDialogDescription>
                                You can always change this back.
                              </AlertDialogDescription>
                            ) : (
                              <AlertDialogDescription>
                                Your admins will be notified that you made this
                                change.
                              </AlertDialogDescription>
                            )}
                          </>
                        ) : (
                          <>
                            <AlertDialogTitle>
                              Do you want to despute this score?
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              Your admins will be notified that you made this
                              change.
                            </AlertDialogDescription>
                          </>
                        )}
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => {
                            if (
                              isAdmin &&
                              dbOrg?.repsCanEditScoreResults ===
                                RepsCanEditScoreResults.DISPUTE_ONLY
                            ) {
                              toggleDisputedScorecardCriteriaMutation.mutate({
                                callId: callId,
                                sectionTitle: section.sectionTitle,
                                criterion: criterion.criterion,
                              });
                            } else {
                              toggleScorecardCriteriaMutation.mutate({
                                callId: callId,
                                sectionTitle: section.sectionTitle,
                                criterion: criterion.criterion,
                              });
                            }
                          }}
                        >
                          Change and report
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}

                {show == 'overwrite' && (
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span tabIndex={0}>
                          <div
                            className={className}
                            onClick={() => {
                              if (
                                isAdmin &&
                                dbOrg?.repsCanEditScoreResults ===
                                  RepsCanEditScoreResults.DISPUTE_ONLY
                              ) {
                                toggleDisputedScorecardCriteriaMutation.mutate({
                                  callId: callId,
                                  sectionTitle: section.sectionTitle,
                                  criterion: criterion.criterion,
                                });
                              } else if (
                                dbOrg?.repsCanEditScoreResults ===
                                RepsCanEditScoreResults.YES
                              ) {
                                //its either user or admin
                                toggleScorecardCriteriaMutation.mutate({
                                  callId: callId,
                                  sectionTitle: section.sectionTitle,
                                  criterion: criterion.criterion,
                                });
                              }
                            }}
                          >
                            {passed ? (
                              <Check className="text-white" size={12} />
                            ) : (
                              <X className=" text-white" size={12} />
                            )}
                          </div>
                        </span>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        {!(
                          isAdmin &&
                          dbOrg?.repsCanEditScoreResults ===
                            RepsCanEditScoreResults.DISPUTE_ONLY
                        ) &&
                        !(
                          dbOrg?.repsCanEditScoreResults ===
                          RepsCanEditScoreResults.YES
                        ) ? (
                          <p>Contact your admin to change this score</p>
                        ) : (
                          <p>Change score</p>
                        )}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
              <div
                className={'flex-1 mt-1 '}
                onClick={() => {
                  openCriterionDetails(criterion.criterion);
                }}
              >
                <div className="flex items-center py-2 pr-3 pl-2">
                  <div className="flex-1">
                    <p className="text-xs leading-none">
                      {criterion.criterion}
                    </p>
                  </div>
                  {!passed && (
                    <div className="text-muted-foreground group-hover:text-black h-[13px] overflow-hidden">
                      <ChevronRight size={13} />
                    </div>
                  )}
                </div>
                {!passed && (
                  <div className="flex space-x-1 text-muted-foreground text-[10px] pt-0 pl-2 pb-2">
                    <span>
                      Learn from {aiCoachTextOverride || 'Hyperbound AI'}{' '}
                    </span>
                    {show == 'overwrite' && <>&bull; edited by rep</>}{' '}
                    {criterion.coaching ? (
                      <span className="flex flex-row items-center space-x-1">
                        &bull;{' '}
                        <SparklesIcon className="w-2 h-2 ml-1 text-teal-500" />
                        <span className="text-teal-500">
                          AI Coaching Enabled
                        </span>
                      </span>
                    ) : (
                      <span className="flex flex-row items-center space-x-1">
                        &bull;{' '}
                        <SparklesIcon className="w-2 h-2 ml-1 text-red-500" />{' '}
                        <span className="text-red-500">
                          AI Coaching Disabled
                        </span>
                      </span>
                    )}
                  </div>
                )}

                {passed && show == 'overwrite' && isAdmin && (
                  <div className="text-muted-foreground text-[10px] pt-0 pl-2 pb-2">
                    edited by rep
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </motion.div>
  );
}
