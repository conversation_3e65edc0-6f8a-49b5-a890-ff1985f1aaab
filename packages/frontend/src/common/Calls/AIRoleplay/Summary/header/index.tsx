import DashboardNavbar from '@/common/DashboardNavbar';
import useUserSession from '@/hooks/useUserSession';
import { AgentCallType, AgentDto } from '@/lib/Agent/types';
import LinksManager from '@/lib/linksManager';
import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  BrainIcon,
  BriefcaseIcon,
  CalendarClockIcon,
  PhoneIcon,
  UserIcon,
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useRouter, usePathname } from 'next/navigation';
import dayjs from 'dayjs';
import Link from 'next/link';
import CallActionsDropdown from '@/components/CallActionsDropdown';
import { CallReportBugPopoverButton } from '@/components/CallReportBugPopoverButton';
import { CallDto } from '@/lib/Call/types';
import '@/app/globals.css';

interface IProps {
  isLoadingCall: boolean;
  call?: CallDto;
  minimal?: boolean;
}

export default function CallSummaryHeader({
  isLoadingCall,
  call,
  minimal,
}: IProps) {
  const { isLoggedIn, isPilotEnded, isInIframe, dbUser } = useUserSession();
  const router = useRouter();
  const pathname = usePathname();

  const caller = call?.caller;
  const agent = call?.agent as AgentDto;
  const callMetadata = call?.vapiMetadata;
  const demoInboundFormResponse = call?.demoInboundFormResponse;

  const callerFirstName =
    caller?.firstName || demoInboundFormResponse?.name?.split(' ')[0] || '';
  const callerLastName =
    caller?.lastName || demoInboundFormResponse?.name?.split(' ')[1] || '';
  const fallbackText =
    `${callerFirstName?.charAt(0) || ''}${callerLastName?.charAt(0) || ''}` ||
    'U';

  const getCallTypeLabelAndIcon = (callType: AgentCallType) => {
    const callTypeObj = CALL_TYPE_OPTIONS.find(
      (item) => item.value === callType,
    );
    const Icon = callTypeObj?.Icon;
    const label = callTypeObj?.label;
    return (
      <div className="flex items-center">
        {Icon && <Icon className="w-4 h-4 mr-1" />}
        {label}
      </div>
    );
  };
  return (
    <>
      <DashboardNavbar
        breadcrumbs={[
          {
            title: isLoggedIn ? 'Call History' : 'Org Call History',
            href: isInIframe ? undefined : LinksManager.trainingCalls(),
          },
          {
            title: agent
              ? `${
                  CALL_TYPE_OPTIONS.find(
                    (item) => item.value === agent.callType,
                  )?.label ||
                  (agent.callType === 'focus' ? 'Focus' : agent.callType)
                } with ${agent.firstName}`
              : '',
          },
        ]}
        titleRight={
          <div className="flex items-center space-x-2">
            {!isLoggedIn && <Badge>FREE DEMO</Badge>}{' '}
          </div>
        }
        agent={minimal ? undefined : agent}
        subContent={
          <div className="text-muted-foreground">
            {isLoadingCall ? (
              <div className="flex flex-col md:flex-row space-x-0 space-y-4 md:space-x-4 md:space-y-0 mt-1">
                <Skeleton className="w-32 h-6" />
                <Skeleton className="w-40 h-6" />
                <Skeleton className="w-16 h-6" />
                <Skeleton className="w-16 h-6" />
                <Skeleton className="w-32 h-6" />
              </div>
            ) : (
              <div className="flex flex-col md:flex-row space-x-0 space-y-4 md:space-x-4 md:space-y-0">
                <div className="flex items-center space-x-2">
                  <CalendarClockIcon className="w-4 h-4" />
                  <p className="text-sm">
                    {dayjs(callMetadata?.startedAt).format(
                      dayjs(callMetadata?.startedAt).year() === dayjs().year()
                        ? 'MMM D h:mm A'
                        : 'MMM D, YYYY h:mm A',
                    )}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <BriefcaseIcon className="w-4 h-4" />
                  <p className="text-sm max-w-[240px] truncate">
                    {agent?.jobTitle || ''} @ {agent?.companyName || ''}
                  </p>
                </div>
                {agent?.emotionalState && !isInIframe && !minimal && (
                  <div className="flex items-center space-x-2">
                    <BrainIcon className="w-4 h-4" />
                    <Badge variant="outline">
                      {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                        (item) => item.value === agent?.emotionalState,
                      )?.label ||
                        agent?.emotionalState ||
                        ''}
                    </Badge>
                  </div>
                )}
                {!isInIframe && !minimal && (
                  <div className="flex items-center space-x-2">
                    <UserIcon className="w-4 h-4" />
                    <Button
                      variant={'outline'}
                      onClick={() => {
                        if (isLoggedIn) {
                          router.push(LinksManager.members(String(caller?.id)));
                        }
                      }}
                      disabled={isInIframe}
                      className="cursor-pointer space-x-2 text-black pl-2 pr-3 py-1 rounded-full hover:bg-muted/80 hover:transition-all duration-300 disabled:opacity-1"
                    >
                      <Avatar className="w-6 h-6">
                        {caller?.avatar && <AvatarImage src={caller?.avatar} />}
                        <AvatarFallback className="text-sm capitalize">
                          {callerFirstName?.charAt(0) || ''}
                          {callerLastName?.charAt(0) || ''}
                        </AvatarFallback>
                      </Avatar>
                      <div className="capitalize">
                        {callerFirstName} {callerLastName}
                      </div>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        }
        rightContent={
          <div className="flex">
            {agent && !minimal && (
              <div className="flex flex-wrap space-x-2">
                {/* {
                isLoggedIn && (
                  <Link
                    href={LinksManager.trainingCalls()}
                    className="h-min"
                  >
                    <Button variant={"outline"}>
                      <LayoutListIcon className="w-4 h-4 mr-2" />
                      View org calls
                    </Button>
                  </Link>
                )
              } */}
                <Link
                  href={
                    isLoggedIn
                      ? `/buyers/${agent.vapiId}`
                      : pathname.includes('/embed')
                        ? `/embed/buyers/${agent.vapiId}`
                        : `/buyers/${agent.vapiId}`
                  }
                  className="h-min"
                >
                  <Button variant={'default'} disabled={isPilotEnded}>
                    <PhoneIcon className="w-4 h-4 mr-2" />
                    Start new call
                  </Button>
                </Link>
                {isLoggedIn && call?.callerId === dbUser?.id && (
                  <CallReportBugPopoverButton call={call!} />
                )}
                {!isPilotEnded && (
                  <CallActionsDropdown
                    call={call!}
                    location="individual_call_page"
                  />
                )}
              </div>
            )}
          </div>
        }
      />
    </>
  );
}
