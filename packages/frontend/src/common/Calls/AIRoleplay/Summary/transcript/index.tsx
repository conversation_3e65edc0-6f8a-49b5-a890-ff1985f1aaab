import { CallDto, VapiCallMessage } from '@/lib/Call/types';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  ArrowUp,
  ChevronRight,
  CopyCheckIcon,
  CopyIcon,
  DownloadIcon,
  Loader2Icon,
  MessageCircleIcon,
  MicOff,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useEffect, useMemo, useRef, useState } from 'react';
import useUserSession from '@/hooks/useUserSession';
import TroubleshootingGuide from '@/components/TroubleshootingGuide';
import { cn, formatTime, formatTranscript } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';
import { DemoInboundFormResponseDto } from '@/lib/Demo/types';
import AgentAvatar from '@/components/Avatars/Agent';
import CallService from '@/lib/Call';

interface IProps {
  call?: CallDto;
  isLoadingCall: boolean;
}

export interface CallMetadata {
  artifact?: { videoRecordingUrl?: string };
  recordingUrl?: string;
  messages: VapiCallMessage[];
}

export default function CallSummaryTranscript({ isLoadingCall, call }: IProps) {
  /*****************************/
  /*********** CONSTs **********/
  /*****************************/
  const callMetadata = call?.providerMetadata
    ? call?.providerMetadata
    : (call?.vapiMetadata as CallMetadata);
  const callId = call?.providerCallId
    ? call?.providerCallId
    : call?.vapiId || '';
  const transcript: VapiCallMessage[] = callMetadata?.messages || [];
  const agent: AgentDto | undefined = call?.agent;
  const caller: UserDto | undefined = call?.caller;
  const demoInboundFormResponse: DemoInboundFormResponseDto | undefined =
    call?.demoInboundFormResponse;
  const callerFirstName =
    caller?.firstName || demoInboundFormResponse?.name?.split(' ')[0] || '';
  const callerLastName =
    caller?.lastName || demoInboundFormResponse?.name?.split(' ')[1] || '';

  /*****************************/
  /*********** STATE ***********/
  /*****************************/

  const { isAdmin, onlyAdminsCanExportCalls, canViewMedia } = useUserSession();

  const [mediaTime, setMediaTime] = useState(0);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [videoRecordingUrl, setVideoRecordingUrl] = useState<
    string | undefined
  >(call?.mediaUrls?.video?.url);
  const [recordingUrl, setRecordingUrl] = useState<string | undefined>(
    call?.mediaUrls?.audio?.url,
  );

  useEffect(() => {
    if (call?.mediaUrls?.video?.url) {
      setVideoRecordingUrl(call?.mediaUrls?.video?.url);
    }
    if (call?.mediaUrls?.audio?.url) {
      setRecordingUrl(call?.mediaUrls?.audio?.url);
    }
  }, [call?.mediaUrls]);

  const transcriptWithAdditionalInfo = useMemo(() => {
    const t = [...(transcript || [])]
      .sort((o1, o2) => o1.secondsFromStart - o2.secondsFromStart)
      .map((t) => ({
        ...t,
        endSecondsFromStart: t.secondsFromStart,
      }));
    for (let i = t.length - 2; i >= 0; i--) {
      t[i].endSecondsFromStart = t[i + 1].secondsFromStart;
    }
    if (t.length > 0) {
      t[t.length - 1].endSecondsFromStart = Infinity;
    }
    return t;
  }, [transcript]);

  const speakingTranscriptIdx = useMemo(() => {
    if (!transcriptWithAdditionalInfo.length) {
      return -1;
    }
    const adjustedMediaTime = mediaTime + 1;
    return transcriptWithAdditionalInfo.findIndex(
      (t) =>
        t.secondsFromStart <= adjustedMediaTime &&
        adjustedMediaTime < t.endSecondsFromStart,
    );
  }, [transcriptWithAdditionalInfo, mediaTime]);

  const speakingTranscriptRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    if (isAutoScrollEnabled && speakingTranscriptRef.current) {
      const parentContainer = speakingTranscriptRef.current.parentElement;
      const grandParentContainer = parentContainer?.parentElement;
      if (!parentContainer || !grandParentContainer) {
        return;
      }
      const scrollOffset =
        parentContainer.offsetTop - grandParentContainer.offsetTop;
      grandParentContainer.scrollTo({
        top: scrollOffset,
        behavior: 'smooth',
      });
    }
  }, [isAutoScrollEnabled, speakingTranscriptIdx]);

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const isAudioPlaying = useRef<boolean>(false);
  const [micProblemsModalOpen, setMicProblemsModalOpen] =
    useState<boolean>(false);
  const [isCopied, setIsCopied] = useState(false);
  /*****************************/
  /*********** INIT ************/
  /*****************************/

  const getMissingRecording = async () => {
    const res = await CallService.getMissingRecording(callId);
    if (res) {
      setVideoRecordingUrl(res.video?.url);
      setRecordingUrl(res.audio?.url);
    }
  };

  useEffect(() => {
    if (
      !!callId &&
      !recordingUrl &&
      !videoRecordingUrl &&
      call?.createdAt &&
      new Date(call.createdAt) < new Date(Date.now() - 24 * 60 * 60 * 1000)
    ) {
      getMissingRecording();
    }
  }, [callId]);

  const formattedTranscript = formatTranscript(
    transcript || [],
    agent ? `${agent.firstName} ${agent.lastName}` : '',
    callId,
  );

  /**************************************/
  /************* AUDIO/VIDEO ************/
  /**************************************/

  const handlePlayPause = (e: Event) => {
    if (e.type == 'play') {
      isAudioPlaying.current = true;
    } else if (e.type == 'pause') {
      isAudioPlaying.current = false;
    }
  };

  /**************************************/
  /******** TRANSCRIPT/PLAY SYNC ********/
  /**************************************/

  function forwardAudio(startTime: number) {
    if (audioRef?.current) {
      audioRef.current.currentTime = startTime;
      audioRef?.current?.play();
    } else if (videoRef?.current) {
      videoRef.current.currentTime = startTime;
      videoRef?.current?.play();
    }
  }

  /*****************************/
  /*********** ACTIONS *********/
  /*****************************/

  const onDownloadAudioClick = async () => {
    const callId = call?.providerCallId ? call?.providerCallId : call?.vapiId;
    if (!callId) {
      return;
    }
    if (recordingUrl) {
      const audioRecodingBlob = await CallService.getAudioCall(callId);
      if (audioRecodingBlob) {
        const url = URL.createObjectURL(audioRecodingBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `audio-${callId}.wav`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    }
  };

  //transcript
  const onDownloadClick = async () => {
    const blob = new Blob([formattedTranscript], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-${call?.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const onCopyClick = async () => {
    try {
      // Use navigator.clipboard.writeText to copy text to clipboard

      await navigator.clipboard.writeText(formattedTranscript);

      // Update state to indicate that the text has been copied
      setIsCopied(true);

      // Reset the copied state after a short delay
      setTimeout(() => {
        setIsCopied(false);
      }, 1500);
    } catch (err) {
      console.error('Unable to copy to clipboard:', err);
    }
  };

  /*****************************/
  /********* EFFECTS ***********/
  /*****************************/

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current?.addEventListener('play', handlePlayPause);
      audioRef.current?.addEventListener('pause', handlePlayPause);
      return () => {
        audioRef.current?.removeEventListener('play', handlePlayPause);
        audioRef.current?.removeEventListener('pause', handlePlayPause);
      };
    }
  }, [audioRef.current]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current?.addEventListener('play', handlePlayPause);
      videoRef.current?.addEventListener('pause', handlePlayPause);
      return () => {
        videoRef.current?.removeEventListener('play', handlePlayPause);
        videoRef.current?.removeEventListener('pause', handlePlayPause);
      };
    }
  }, [videoRef.current]);

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  const renderMessage = (message: VapiCallMessage, i: number) => {
    const isSpeaking = i === speakingTranscriptIdx;

    return (
      <motion.div
        key={i}
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ y: -50 }}
        transition={{
          duration: 0.4,
          delay: i * 0.1,
        }}
      >
        <div
          onClick={() => forwardAudio(message.secondsFromStart)}
          ref={isSpeaking ? speakingTranscriptRef : undefined}
          className={cn(
            'flex items-center space-x-4 mt-1 cursor-pointer hover:shadow-inner hover:shadow-gray-300 rounded-lg py-3 px-2',
            {
              'justify-end': message.role === 'user',
              'justify-start': message.role === 'bot',
              'shadow-inner shadow-gray-300': isSpeaking,
            },
          )}
        >
          {message.role === 'bot' &&
            (i === 0 || transcript[i - 1].role !== message.role) && (
              <AgentAvatar className="w-8 h-8" agent={agent} />
            )}
          <div
            className={cn('flex flex-col', {
              'ml-10': i > 0 && transcript[i - 1].role !== message.role,
              'items-end': message.role === 'user',
              'items-start': message.role === 'bot',
            })}
          >
            <span className="text-xs text-muted-foreground">
              {formatTime(message.secondsFromStart * 1000)}
            </span>
            <div
              className={cn('rounded-xl text-xs py-3 px-4 max-w-[250px] mt-1', {
                'bg-gray-100 rounded-tl-md': message.role === 'bot',
                'bg-blue-500 text-white rounded-br-md': message.role === 'user',
              })}
            >
              {message.message}
            </div>
          </div>
          {message.role === 'user' &&
            (i === 0 || transcript[i - 1].role !== message.role) && (
              <Avatar className="w-8 h-8">
                {caller?.avatar && <AvatarImage src={caller.avatar} />}
                <AvatarFallback className="text-sm text-muted-foreground">
                  {callerFirstName?.charAt(0) || ''}
                  {callerLastName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
            )}
        </div>
      </motion.div>
    );
  };

  return (
    <div className="w-full px-4 py-4 md:flex md:flex-col">
      {canViewMedia && (
        <div className="w-full">
          {/*********************************/}
          {/***** AUDIO/VIDEO (header) ******/}
          {/*********************************/}
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-semibold">Recording</h3>
            </div>
            {recordingUrl && (!onlyAdminsCanExportCalls || isAdmin) && (
              <TooltipProvider
                delayDuration={50}
                disableHoverableContent={isLoadingCall}
              >
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={'ghost'}
                      disabled={isLoadingCall}
                      onClick={onDownloadAudioClick}
                      className="rounded-full p-2 text-muted-foreground"
                    >
                      <DownloadIcon className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Download recording</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          {/*********************************/}
          {/***** AUDIO/VIDEO (player) ******/}
          {/*********************************/}
          <div className="mt-3">
            {videoRecordingUrl ? (
              <video
                controls
                className="rounded-lg w-full"
                ref={videoRef}
                onTimeUpdate={() => {
                  setMediaTime(videoRef.current?.currentTime || 0);
                }}
              >
                <source src={videoRecordingUrl}></source>
              </video>
            ) : (
              <div className="relative">
                {!recordingUrl && (
                  <div className="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center z-50 rounded-full bg-muted">
                    <Loader2Icon className="animate-spin" />
                  </div>
                )}
                <audio
                  controls
                  controlsList="nodownload"
                  className="w-full"
                  ref={audioRef}
                  onTimeUpdate={() => {
                    setMediaTime(audioRef.current?.currentTime || 0);
                  }}
                >
                  {recordingUrl && (
                    <source type="audio/wav" src={recordingUrl} />
                  )}
                </audio>
              </div>
            )}
          </div>

          {/*********************************/}
          {/********* TROUBLESHOOTING *******/}
          {/*********************************/}
          <div className="my-6">
            <div
              className="flex items-center p-2 border rounded-lg border-yellow-400 hover:border-yellow-300 cursor-pointer"
              onClick={() => {
                setMicProblemsModalOpen(true);
              }}
            >
              <div className="mr-3 text-yellow-400">
                <MicOff size={16} />
              </div>
              <div className="flex-1">
                <h3 className="text-xs">Can&apos;t hear your voice?</h3>
              </div>
              <div>
                <ChevronRight size={16} />
              </div>
            </div>
          </div>
        </div>
      )}

      {/*********************************/}
      {/***** TRANSCRIPT (header) *******/}
      {/*********************************/}
      <div className="w-full mt-6 md:flex-1 md:relative">
        <div className="w-full md:absolute md:left-0 md:right-0 md:top-0 md:bottom-0 md:flex md:flex-col">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold">Transcript</h3>
              <p className="text-muted-foreground mt-1 text-xs">
                Click on messages to skip to that part of the audio
              </p>
            </div>
            {transcript?.length > 0 && (
              <div className="flex items-center space-x-0">
                <TooltipProvider
                  delayDuration={50}
                  disableHoverableContent={isLoadingCall}
                >
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={'ghost'}
                        disabled={isLoadingCall}
                        onClick={onDownloadClick}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        <DownloadIcon className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Download transcript</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider
                  delayDuration={50}
                  disableHoverableContent={isLoadingCall}
                >
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={'ghost'}
                        disabled={isLoadingCall}
                        onClick={onCopyClick}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        {isCopied ? (
                          <CopyCheckIcon className="w-4 h-4 text-green-600" />
                        ) : (
                          <CopyIcon className="w-4 h-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Copy to clipboard</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            )}
          </div>

          {/*********************************/}
          {/********* TRANSCRIPT ************/}
          {/*********************************/}
          <div
            className="max-h-[480px] overflow-y-auto pb-16 mt-4 md:flex-1"
            style={{ paddingRight: '4px' }}
            onWheel={() => {
              setIsAutoScrollEnabled(false);
            }}
          >
            <AnimatePresence>
              {transcript?.length > 0 ? (
                transcript.map(renderMessage)
              ) : (
                <div className="w-full h-[150px] flex justify-center items-center">
                  <div className="flex flex-col space-y-2 text-muted-foreground text-sm items-center">
                    {isLoadingCall ? (
                      <Loader2Icon className="animate-spin" />
                    ) : (
                      <MessageCircleIcon />
                    )}
                    <p>
                      {isLoadingCall
                        ? 'Loading transcript...'
                        : 'No transcript found'}
                    </p>
                  </div>
                </div>
              )}
            </AnimatePresence>
          </div>
          <div
            className={
              'w-full flex justify-center ' +
              (!isAutoScrollEnabled ? '' : 'invisible')
            }
            style={{ position: 'absolute', bottom: '20px' }}
          >
            <Button
              onClick={() => {
                setIsAutoScrollEnabled(true);
              }}
              style={{ fontSize: '12px' }}
            >
              <ArrowUp style={{ height: '16px' }} />
              &nbsp;&nbsp;Resume Transcript Auto-Scroll
            </Button>
          </div>
        </div>
      </div>

      <TroubleshootingGuide
        open={micProblemsModalOpen}
        onClose={() => {
          setMicProblemsModalOpen(false);
        }}
      />
    </div>
  );
}
