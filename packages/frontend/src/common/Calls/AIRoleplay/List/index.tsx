import { useEffect, useState } from 'react';
import { FilterType, TableState } from './common';
import FiltersPanel from './filtersControls';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import dayjs from 'dayjs';
import { AgentCallType } from '@/lib/Agent/types';
import PlaylistsFilter from './playlists';
import CallsTable from './table';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  showFiltersPanel: boolean;
  onFiltersPanelClose: () => void;
  onTableStateUpdated: (ts: TableState) => void;
  teams: number[];
}

export interface FilterState {
  dates: {
    fromDate: Date | undefined;
    toDate: Date | undefined;
  };
  buyers: number[];
  reps: number[];
  playlists: string[];
  callTypes: AgentCallType[];
  tags: string[];
  callblitz: number[];
  teams: number[];
  status: string[];
  roles: string[];
  scorecards: number[];
  from: number;
  numberOfResults: number;
  minDuration: number | undefined;
  maxDuration: number | undefined;
  scorecardSections: string[];
  scorecardCriterions: string[];
  scorecardCriterionsStatus: string[];
  objections: string[];
  languages: string[];
  search: string | undefined;
}

export default function RoleplayCallsList({
  showFiltersPanel,
  onFiltersPanelClose,
  onTableStateUpdated,
  teams,
}: IProps) {
  const router = useRouter();
  const params = useSearchParams();
  const { isCallHistoryLimitedUi } = useUserSession();
  const curSearchParams = new URLSearchParams(params);
  const pathname = usePathname();

  /***********************************/
  /************* INIT *************/
  /***********************************/

  let fromDate: dayjs.Dayjs | undefined; // Explicitly define the type
  let toDate: dayjs.Dayjs | undefined; // Explicitly define the type

  let buyers: number[] = [];
  let reps: number[] = [];
  let playlists: number[] = [];
  let callTypes: AgentCallType[] = [];
  let tags: number[] = [];
  let callBlitz: number[] = [];
  let scorecards: number[] = [];
  let languages: string[] = [];
  let scorecardSections: string[] = [];
  let scorecardCriterions: string[] = [];
  let objections: string[] = [];
  let scorecardCriterionsStatus: string[] = [];
  let from = 0;
  let numberOfResults = 20;

  const shownFilters: FilterType[] = [
    FilterType.DATE,
    FilterType.REPS,
    FilterType.TEAMS,
    FilterType.BUYERS,
    FilterType.TAGS,
    FilterType.CALL_TYPES,
    FilterType.PLAYLISTS,
  ];
  // console.log('curSearchParams', curSearchParams.get('buyers'));
  const [tableState, setTableState] = useState<TableState>(
    new TableState(
      fromDate?.toDate(), //
      toDate?.toDate(), //
      buyers,
      reps,
      playlists,
      callTypes,
      tags,
      callBlitz,
      from,
      numberOfResults,
      teams,
      languages,
      scorecards,
      scorecardSections,
      scorecardCriterions,
      scorecardCriterionsStatus,
      objections,
    ),
  );

  // useEffect to initialize table state using url on first component load
  useEffect(() => {
    if (curSearchParams.get('fromDate') && curSearchParams.get('toDate')) {
      fromDate = dayjs(curSearchParams.get('fromDate') as string);
      toDate = dayjs(curSearchParams.get('toDate') as string);
      if (
        tableState.filterBy.dates.fromDate == undefined &&
        tableState.filterBy.dates.toDate == undefined
      ) {
        setTableState((s) => {
          return {
            ...s,
            filterBy: {
              ...s.filterBy,
              dates: { fromDate: fromDate?.toDate(), toDate: toDate?.toDate() },
            },
          };
        });
      }
    }
    if (curSearchParams.get('buyers')) {
      buyers = curSearchParams.get('buyers')?.split(',').map(Number) || [];
      if (shownFilters.indexOf(FilterType.BUYERS) < 0) {
        shownFilters.push(FilterType.BUYERS);
      }
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, buyers },
        };
      });
    }
    if (curSearchParams.get('reps')) {
      reps = curSearchParams.get('reps')?.split(',').map(Number) || [];
      if (shownFilters.indexOf(FilterType.REPS) < 0) {
        shownFilters.push(FilterType.REPS);
      }
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, reps },
        };
      });
    }
    if (curSearchParams.get('pl')) {
      playlists = curSearchParams.get('pl')?.split(',').map(Number) || [];
      if (shownFilters.indexOf(FilterType.PLAYLISTS) < 0) {
        shownFilters.push(FilterType.PLAYLISTS);
      }
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, playlists: playlists.map(String) },
        };
      });
    }
    if (curSearchParams.get('callTypes')) {
      callTypes =
        (curSearchParams.get('callTypes')?.split(',') as AgentCallType[]) || [];
      if (shownFilters.indexOf(FilterType.CALL_TYPES) < 0) {
        shownFilters.push(FilterType.CALL_TYPES);
      }
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, callTypes },
        };
      });
    }
    if (curSearchParams.get('tags')) {
      tags = curSearchParams.get('tags')?.split(',').map(Number) || [];
      if (shownFilters.indexOf(FilterType.TAGS) < 0) {
        shownFilters.push(FilterType.TAGS);
      }
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, tags: tags.map(String) },
        };
      });
    }
    if (curSearchParams.get('callblitz')) {
      callBlitz =
        curSearchParams.get('callblitz')?.split(',').map(Number) || [];
      if (shownFilters.indexOf(FilterType.CALL_BLITZ) < 0) {
        shownFilters.push(FilterType.CALL_BLITZ);
      }
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, callblitz: callBlitz },
        };
      });
    }
    if (curSearchParams.get('teams')) {
      teams = curSearchParams.get('teams')?.split(',').map(Number) || [];
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, teams },
        };
      });
    }

    if (curSearchParams.get('scorecards')) {
      scorecards =
        curSearchParams.get('scorecards')?.split(',').map(Number) || [];
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, scorecards },
        };
      });
    }

    if (curSearchParams.get('from')) {
      from = Number(curSearchParams.get('from'));
      setTableState((s) => {
        return {
          ...s,
          from,
        };
      });
    }

    if (curSearchParams.get('numberOfResults')) {
      numberOfResults = Number(curSearchParams.get('numberOfResults'));
      setTableState((s) => {
        return {
          ...s,
          numberOfResults,
        };
      });
    }

    if (curSearchParams.get('languages')) {
      languages =
        curSearchParams.get('languages')?.split(',').map(decodeURIComponent) ||
        [];

      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, languages: languages },
        };
      });
    }

    if (curSearchParams.get('scorecardSections')) {
      scorecardSections =
        curSearchParams
          .get('scorecardSections')
          ?.split(',')
          .map(decodeURIComponent) || [];

      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, scorecardSections: scorecardSections },
        };
      });
    }

    if (curSearchParams.get('scorecardCriterions')) {
      scorecardCriterions =
        curSearchParams
          .get('scorecardCriterions')
          ?.split(',')
          .map(decodeURIComponent) || [];

      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, scorecardCriterions: scorecardCriterions },
        };
      });
    }

    if (curSearchParams.get('objections')) {
      objections =
        curSearchParams.get('objections')?.split(',').map(decodeURIComponent) ||
        [];
      setTableState((s) => {
        return {
          ...s,
          filterBy: { ...s.filterBy, objections },
        };
      });
    }

    if (curSearchParams.get('scorecardCriterionStatus')) {
      scorecardCriterionsStatus =
        curSearchParams
          .get('scorecardCriterionStatus')
          ?.split(',')
          .map(decodeURIComponent) || [];

      setTableState((s) => {
        return {
          ...s,
          filterBy: {
            ...s.filterBy,
            scorecardCriterionsStatus: scorecardCriterionsStatus,
          },
        };
      });
    }
  }, []);

  /***********************************/
  /************* ACTIONS *************/
  /***********************************/

  const updatePagination = (from: number, numberOfResults: number) => {
    //update url:
    if (from && from > 0) {
      curSearchParams.set('from', from.toString());
    } else {
      curSearchParams.delete('from');
    }

    if (numberOfResults && numberOfResults > 0) {
      curSearchParams.set('numberOfResults', numberOfResults.toString());
    } else {
      curSearchParams.delete('numberOfResults');
    }

    router.replace(`${pathname}?${curSearchParams.toString()}`);

    tableState.from = from;
    tableState.numberOfResults = numberOfResults;

    setTableState({ ...tableState });
  };

  const updateFilters = (filterBy: FilterState) => {
    // Ensure all keys exist by merging with current state
    const updatedFilterBy = {
      ...tableState.filterBy, // Ensures required keys exist
      ...filterBy, // Overwrites with new values
    };

    tableState.filterBy = updatedFilterBy;
    // Date Filter
    if (updatedFilterBy[FilterType.DATE]?.fromDate) {
      curSearchParams.set(
        'fromDate',
        dayjs(updatedFilterBy[FilterType.DATE]?.fromDate).format('YYYY-MM-DD'),
      );
    } else {
      curSearchParams.delete('fromDate');
    }
    if (updatedFilterBy[FilterType.DATE]?.toDate) {
      curSearchParams.set(
        'toDate',
        dayjs(updatedFilterBy[FilterType.DATE]?.toDate).format('YYYY-MM-DD'),
      );
    } else {
      curSearchParams.delete('toDate');
    }
    // Buyers Filter
    if (updatedFilterBy[FilterType.BUYERS]?.length > 0) {
      curSearchParams.set(
        'buyers',
        updatedFilterBy[FilterType.BUYERS]?.join(','),
      );
    } else {
      curSearchParams.delete('buyers');
    }

    // Reps Filter
    if (updatedFilterBy[FilterType.REPS]?.length > 0) {
      curSearchParams.set('reps', updatedFilterBy[FilterType.REPS]?.join(','));
    } else {
      curSearchParams.delete('reps');
    }

    // Playlists Filter
    if (updatedFilterBy[FilterType.PLAYLISTS]?.length > 0) {
      curSearchParams.set(
        'pl',
        updatedFilterBy[FilterType.PLAYLISTS]?.join(','),
      );
    } else {
      curSearchParams.delete('pl');
    }

    // Call Types Filter
    if (updatedFilterBy[FilterType.CALL_TYPES]?.length > 0) {
      curSearchParams.set(
        'callTypes',
        updatedFilterBy[FilterType.CALL_TYPES]?.join(','),
      );
    } else {
      curSearchParams.delete('callTypes');
    }

    // Tags Filter
    if (updatedFilterBy[FilterType.TAGS]?.length > 0) {
      curSearchParams.set('tags', updatedFilterBy[FilterType.TAGS]?.join(','));
    } else {
      curSearchParams.delete('tags');
    }

    // Call Blitz Filter
    if (updatedFilterBy[FilterType.CALL_BLITZ]?.length > 0) {
      curSearchParams.set(
        'callblitz',
        updatedFilterBy[FilterType.CALL_BLITZ]?.join(','),
      );
    } else {
      curSearchParams.delete('callblitz');
    }

    // Teams Filter
    if (updatedFilterBy[FilterType.TEAMS]?.length > 0) {
      curSearchParams.set(
        'teams',
        updatedFilterBy[FilterType.TEAMS]?.join(','),
      );
    } else {
      curSearchParams.delete('teams');
    }

    // Scorecards Filter
    if (updatedFilterBy[FilterType.SCORECARDS]?.length > 0) {
      curSearchParams.set(
        'scorecards',
        updatedFilterBy[FilterType.SCORECARDS]?.join(','),
      );
    } else {
      curSearchParams.delete('scorecards');
    }

    // Langs Filter
    if (updatedFilterBy[FilterType.LANGUAGES]?.length > 0) {
      curSearchParams.set(
        'languages',
        updatedFilterBy[FilterType.LANGUAGES]?.join(','),
      );
    } else {
      curSearchParams.delete('languages');
    }

    // Sections Filter
    if (updatedFilterBy[FilterType.SCORECARDS_SECTIONS]?.length > 0) {
      curSearchParams.set(
        'scorecardSections',
        updatedFilterBy[FilterType.SCORECARDS_SECTIONS]?.join(','),
      );
    } else {
      curSearchParams.delete('scorecardSections');
    }

    // Criterions Filter
    if (updatedFilterBy[FilterType.SCORECARDS_CRITERIONS]?.length > 0) {
      curSearchParams.set(
        'scorecardCriterions',
        updatedFilterBy[FilterType.SCORECARDS_CRITERIONS]?.join(','),
      );
    } else {
      curSearchParams.delete('scorecardCriterions');
    }

    // Criterions Status Filter
    if (updatedFilterBy[FilterType.SCORECARDS_CRITERIONS_STATUS]?.length > 0) {
      curSearchParams.set(
        'scorecardCriterionStatus',
        updatedFilterBy[FilterType.SCORECARDS_CRITERIONS_STATUS]?.join(','),
      );
    } else {
      curSearchParams.delete('scorecardCriterionStatus');
    }

    // Objections Filter
    if (updatedFilterBy[FilterType.OBJECTIONS]?.length > 0) {
      curSearchParams.set(
        'objections',
        updatedFilterBy[FilterType.OBJECTIONS]?.join(','),
      );
    } else {
      curSearchParams.delete('objections');
    }

    // Min Duration Filter
    if (updatedFilterBy[FilterType.MIN_DURATION]) {
      curSearchParams.set(
        'minDuration',
        updatedFilterBy[FilterType.MIN_DURATION]?.toString(),
      );
    } else {
      curSearchParams.delete('minDuration');
    }

    // Max Duration Filter
    if (updatedFilterBy[FilterType.MAX_DURATION]) {
      curSearchParams.set(
        'maxDuration',
        updatedFilterBy[FilterType.MAX_DURATION]?.toString(),
      );
    } else {
      curSearchParams.delete('maxDuration');
    }

    router.replace(`${pathname}?${curSearchParams.toString()}`);

    // Restart pagination
    updatePagination(0, tableState.numberOfResults);

    // Update state
    setTableState({ ...tableState });

    // Ensure `filterBy` is fully populated before passing to the callback
    onTableStateUpdated({ ...tableState, filterBy: updatedFilterBy });
  };

  /***********************************/
  /************* RENDER *************/
  /***********************************/
  return (
    <div>
      <FiltersPanel
        open={showFiltersPanel}
        onFiltersPanelClose={onFiltersPanelClose}
        filtersState={tableState.filterBy}
        onFiltersUpdated={updateFilters}
      />

      {!isCallHistoryLimitedUi && (
        <div className="mt-3">
          <PlaylistsFilter
            filtersState={tableState.filterBy}
            onFiltersUpdated={updateFilters}
          />
        </div>
      )}

      <div className="mt-5">
        <CallsTable
          tableState={tableState}
          updateTableState={setTableState}
          updatePagination={updatePagination}
        />
      </div>
    </div>
  );
}
