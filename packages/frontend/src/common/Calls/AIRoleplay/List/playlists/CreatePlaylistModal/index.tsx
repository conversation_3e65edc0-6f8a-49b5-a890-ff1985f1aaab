import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import CreatePlaylistForm from '../CreatePlaylistForm';
import { UpdatePlaylistDto } from '@/lib/Playlist/types';

interface ICreatePlaylistModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  onSubmit: () => void;
  defaultPlaylistValues?: UpdatePlaylistDto;
  defaultPlaylistId?: number;
  addCallId?: number;
  hideShare?: boolean;
}

export function CreatePlaylistModal({
  modalOpen,
  setModalOpen,
  onSubmit,
  defaultPlaylistValues,
  defaultPlaylistId,
  addCallId,
  hideShare,
}: ICreatePlaylistModalProps) {
  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent
        className="close-btn"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {defaultPlaylistValues?.name
              ? 'Edit playlist'
              : 'Create new playlist'}
          </DialogTitle>
          <DialogDescription className="">
            Use playlists to organize and quickly access calls.
          </DialogDescription>
        </DialogHeader>
        <CreatePlaylistForm
          onSubmit={onSubmit}
          defaultPlaylistValues={defaultPlaylistValues}
          defaultPlaylistId={defaultPlaylistId}
          addCallId={addCallId}
          hideShare={hideShare}
        />
      </DialogContent>
    </Dialog>
  );
}
