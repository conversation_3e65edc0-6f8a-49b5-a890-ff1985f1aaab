/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from '@/components/ui/button';
import usePlaylists from '@/hooks/usePlaylists';
import {
  ChevronDown,
  ListVideo,
  LockIcon,
  Plus,
  Trash2,
  X,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { PlaylistDto } from '@/lib/Playlist/types';
import useUserSession from '@/hooks/useUserSession';
import { useEffect, useRef, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { CreatePlaylistModal } from './CreatePlaylistModal';
import { FilterType } from '../common';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import PlaylistService from '@/lib/Playlist';
import { Id, toast } from 'react-toastify';
import { FilterState } from '..';

interface IProps {
  filtersState: FilterState;
  onFiltersUpdated: (filters: FilterState) => void;
}
export default function PlaylistsFilter({
  filtersState,
  onFiltersUpdated,
}: IProps) {
  const playlistsFilter = filtersState[FilterType.PLAYLISTS] || [];

  const { isLoggedIn, isAdmin, onlyAdminsCanCreatePlaylists } =
    useUserSession();
  const { data: allPlaylists, isLoading } = usePlaylists();
  const [playlists, setPlaylists] = useState<PlaylistDto[]>([]);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [deletingPlaylistId, setDeletingPlaylistId] = useState<number | null>(
    null,
  );
  const [deletingPlaylistName, setDeletingPlaylistName] = useState<string>('');
  const [showDeletConfrimationModal, setShowDeletConfrimationModal] =
    useState<boolean>(false);
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);

  const deletePlaylistByIdMutation = useMutation({
    mutationFn: PlaylistService.deletePlaylistById,
    onSuccess: async () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.success('Playlist successfully deleted.');
      }
      queryClient.invalidateQueries({ queryKey: ['playlists'] });
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
    },
    onError: (error) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error deleting your playlist. Please try again.',
        );
      }
      console.log('ERROR saving playlist', error);
    },
  });

  const activePlaylistId = 0;

  useEffect(() => {
    if (!isLoading && allPlaylists) {
      setPlaylists(allPlaylists);
    }
  }, [isLoading, allPlaylists]);

  const updatePlaylistFilter = (playlists: string[]) => {
    filtersState[FilterType.PLAYLISTS] = playlists;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const toggle = (id: number) => {
    if (playlistsFilter?.includes(id.toString())) {
      const r = playlistsFilter.filter((plId: string) => Number(plId) !== id);
      updatePlaylistFilter([...r]);
    } else {
      updatePlaylistFilter([...playlistsFilter, id.toString()]);
    }
  };

  const startDeletePlaylist = (id: number, plName: string) => {
    setDeletingPlaylistId(id);
    setDeletingPlaylistName(plName);
    setShowDeletConfrimationModal(true);
  };

  const doDeletePlaylist = () => {
    setShowDeletConfrimationModal(false);

    if (deletingPlaylistId) {
      deletePlaylistByIdMutation.mutate({
        id: deletingPlaylistId,
      });
    }
  };

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  if (isLoading) {
    return (
      <div>
        <Skeleton className="h-10 w-[100%]" />
      </div>
    );
  }

  return (
    <div className="flex items-center">
      {(!onlyAdminsCanCreatePlaylists || isAdmin) && (
        <>
          {playlists?.length == 0 && isLoggedIn && (
            <Button
              variant={'outline'}
              className="w-[200px]"
              disabled={!isLoggedIn}
              onClick={() => {
                setModalOpen(true);
              }}
            >
              <Plus size={16} className="mr-1" /> Create Playlist
            </Button>
          )}

          {playlists?.length == 0 && !isLoggedIn && (
            <TooltipProvider delayDuration={50}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    className="border-dashed opacity-45"
                  >
                    <Button
                      variant={'outline'}
                      className="w-[200px]"
                      disabled={!isLoggedIn}
                      onClick={() => {
                        setModalOpen(true);
                      }}
                    >
                      <Plus size={16} className="mr-1" /> Create Playlist{' '}
                      <LockIcon className="w-4 h-4 ml-2" />
                    </Button>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Book a demo to access playlists</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </>
      )}

      <div className="flex-1 overflow-hidden flex items-center flex-nowrap">
        {playlists?.map((pl: PlaylistDto, i: number) => {
          if (i < 4) {
            const selected = playlistsFilter?.includes(pl.id.toString());
            return (
              <div
                key={'pl-' + pl.id}
                className={cn(
                  'flex items-center border p-2 rounded-lg mr-2 cursor-pointer w-[25%] overflow-hidden ',
                  {
                    'bg-black text-white hover:bg-black/80': selected,
                    'hover:bg-gray-100': !selected,
                  },
                )}
                onClick={() => {
                  toggle(pl.id);
                }}
              >
                <div className="mr-2">
                  <ListVideo size={16} />
                </div>
                <div className="flex-1 mr-3 text-nowrap">{pl.name}</div>
                {selected ? (
                  <div className="text-muted-foreground text-sm text-nowrap text-white">
                    <X size={16} />
                  </div>
                ) : (
                  <div className="text-muted-foreground text-sm text-nowrap">
                    {pl.calls.length} call{pl.calls.length != 1 && 's'}
                  </div>
                )}
              </div>
            );
          }
        })}
      </div>

      {!isLoggedIn && (
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                className="border-dashed opacity-45"
              >
                <Button
                  variant={'outline'}
                  className="w-[200px]"
                  disabled={!isLoggedIn}
                  onClick={() => {
                    setModalOpen(true);
                  }}
                >
                  <LockIcon className="w-4 h-4 mr-2" /> All playlists{' '}
                  <ChevronDown size={16} className="ml-2" />
                </Button>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Book a demo to access playlists</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {isLoggedIn && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant={'outline'} disabled={!isLoggedIn}>
              All playlists <ChevronDown size={16} className="ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {playlists?.map((pl: PlaylistDto) => {
              const selected = playlistsFilter?.includes(pl.id.toString());
              return (
                <DropdownMenuItem
                  disabled={!isLoggedIn}
                  className={''}
                  onClick={() => {}}
                  key={'pl-' + pl.id}
                >
                  <div
                    key={'pl-' + pl.id}
                    className="flex items-center w-full"
                    onClick={() => {
                      toggle(pl.id);
                    }}
                  >
                    {/* <div className="mr-2"><GripVertical size={16} /></div> */}
                    <div className="flex-1 mr-3">{pl.name}</div>
                    {selected ? (
                      <div className="text-muted-foreground text-sm mr-2">
                        <X size={16} />
                      </div>
                    ) : (
                      <>
                        <div className="text-muted-foreground text-sm mr-2">
                          {pl.calls.length} call{pl.calls.length != 1 && 's'}
                        </div>
                        {isAdmin && (
                          <div
                            className="text-muted-foreground text-sm ml-2 cursor-pointer hover:text-black"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              startDeletePlaylist(pl.id, pl.name);
                            }}
                          >
                            <Trash2 size={16} />
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </DropdownMenuItem>
              );
            })}
            {isAdmin && (
              <>
                {playlists?.length >= 4 && <DropdownMenuSeparator />}
                <DropdownMenuItem
                  disabled={!isLoggedIn}
                  className={''}
                  onClick={() => {
                    setModalOpen(true);
                  }}
                >
                  <div className="flex items-center">
                    <Plus size={16} className="mr-2" />
                    Create a new playlist
                  </div>
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      <CreatePlaylistModal
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        onSubmit={() => {
          setModalOpen(false);
        }}
        hideShare={false}
        defaultPlaylistId={activePlaylistId}
        defaultPlaylistValues={
          activePlaylistId
            ? playlists?.find((p) => p.id === activePlaylistId)
            : undefined
        }
      />
      <DeleteConfirmationModal
        open={showDeletConfrimationModal}
        onCancel={() => {
          setShowDeletConfrimationModal(false);
          setDeletingPlaylistId(null);
        }}
        onConfirm={doDeletePlaylist}
        title={'Delete ' + deletingPlaylistName}
        description="Are you sure you want to delete this playlist?"
      />
    </div>
  );
}
