import useDemoCalls from '@/hooks/useDemoCalls';
import {
  CallsSortingParam,
  SortDirection,
  SortTypes,
  TableState,
} from '../common';
import useDemoCallsCount from '@/hooks/useDemoCallsCount';
import useOrgCalls from '@/hooks/useOrgCalls';
import useOrgCallsCount from '@/hooks/useOrgCallsCount';
import useUserSession from '@/hooks/useUserSession';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import {
  CaretDownIcon,
  CaretSortIcon,
  CaretUpIcon,
} from '@radix-ui/react-icons';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';
import { cn, formatDuration, timeAgo } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AgentCallType, AgentDto } from '@/lib/Agent/types';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { Badge } from '@/components/ui/badge';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  BotIcon,
  ChevronDown,
  Columns2,
  LayoutListIcon,
  ListVideo,
  Loader2Icon,
  MicOff,
  PhoneCall,
  PhoneMissed,
  Plus,
  Trash2Icon,
  TriangleAlert,
  X,
} from 'lucide-react';
import CallActionsDropdown from './CallActionsDropdown';
import { useCallback, useMemo, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import CallService from '@/lib/Call';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import PaginationControls from './paginationControls';
import usePlaylists from '@/hooks/usePlaylists';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { PlaylistDto } from '@/lib/Playlist/types';
import PlaylistService from '@/lib/Playlist';
import CallPreviewPanel from './callPreviewPanel';
import { CreatePlaylistModal } from '../playlists/CreatePlaylistModal';
import Analytics from '@/system/Analytics';
import { CallEvents } from '@/system/Analytics/events/CallEvents';
import ConfirmationModal from '@/components/ConfirmationModal';
import { CallDto, CallScoringStatus } from '@/lib/Call/types';
import AgentAvatar from '@/components/Avatars/Agent';
import Checkbox from '@/components/ui/Hyperbound/checkbox';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  tableState: TableState;
  updateTableState: (ts: TableState) => void;
  updatePagination: (from: number, numberOfResults: number) => void;
}

const COLUMNS = [
  {
    label: '',
    prop: 'multiSelect',
  },
  {
    label: 'Started At',
    prop: 'createdAt',
  },
  {
    label: 'Duration',
    prop: 'duration',
  },
  {
    label: 'Rep',
    prop: 'caller',
  },
  {
    label: 'Buyer',
    prop: 'agent',
  },
  {
    label: 'Type',
    prop: 'callType',
  },
  {
    label: 'Score / 100',
    prop: 'score',
  },
  {
    label: 'Playlist',
    prop: 'playlist',
  },
  {
    label: '',
    prop: 'sys-status-col',
  },
  {
    label: '',
    prop: 'actions-col',
  },
];

export default function CallsTable({
  tableState,
  updateTableState,
  updatePagination,
}: IProps) {
  const {
    isLoggedIn,
    isPilotEnded,
    isCompetitionOrg,
    canAccess,
    userId,
    onlyAdminsCanDeleteUserCalls,
  } = useUserSession();
  const router = useRouter();
  const errorToastId = useRef<Id | null>(null);
  const [recoveringCalls, setRecoveringCalls] = useState<string[]>([]);
  const queryClient = useQueryClient();
  const [showCallPreview, setShowCallPreview] = useState(false);
  const [previewCallId, setPreviewCallId] = useState<string>('');
  const [newPLaylistModalOpen, setNewPlaylistModalOpen] = useState(false);
  const [focusOnCallId, setFocusOnCallId] = useState<number>();
  const [checkedCallIds, setCheckedCallIds] = useState<Set<number>>(new Set());

  const toggleCallCheck = useCallback(
    (callId: number) => {
      if (checkedCallIds.has(callId)) {
        checkedCallIds.delete(callId);
      } else {
        checkedCallIds.add(callId);
      }
      setCheckedCallIds(new Set(checkedCallIds));
    },
    [checkedCallIds],
  );

  /***********************************/
  /************* INIT ****************/
  /***********************************/

  const { data: allPlaylists } = usePlaylists();

  const {
    data: demoCalls,
    isLoading: demoCallsLoading,
    refetch: refetchDemoCalls,
  } = useDemoCalls(
    !isLoggedIn,
    tableState.from,
    tableState.numberOfResults,
    tableState.sortBy,
    tableState.filterBy,
  );
  let { data: demoCallsCount } = useDemoCallsCount(tableState.filterBy);

  const { data: orgCalls, isLoading: orgCallsLoading } = useOrgCalls(
    tableState.from,
    tableState.numberOfResults,
    tableState.sortBy,
    tableState.filterBy,
  );
  let { data: orgCallsCount } = useOrgCallsCount(tableState.filterBy);

  //react-query is returning object instead of number when count is zero
  if (typeof orgCallsCount == 'object') {
    orgCallsCount = 0;
  }

  if (typeof demoCallsCount == 'object') {
    demoCallsCount = 0;
  }

  const callsCount = isLoggedIn ? orgCallsCount : demoCallsCount; //0; //
  const calls = isLoggedIn ? orgCalls : demoCalls; //[]; //

  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const callIdToCalls = useMemo(() => {
    return calls ? Object.fromEntries(calls.map((c) => [c.id, c])) : {};
  }, [calls]);

  const multiDeleteEnabled = useMemo(() => {
    const hasAdminRights = isLoggedIn
      ? canAccess(AppPermissions.DELETE_CALLS)
      : hbDemoInboundForm?.isAdmin;
    return (
      hasAdminRights ||
      Array.from(checkedCallIds).every((callId) => {
        const call = callIdToCalls[callId];
        return isLoggedIn
          ? !onlyAdminsCanDeleteUserCalls && userId === call.callerId
          : hbDemoInboundForm?.id === call?.demoInboundFormResponseId;
      })
    );
  }, [callIdToCalls, checkedCallIds, hbDemoInboundForm]);

  const isTableLoading = isLoggedIn ? orgCallsLoading : demoCallsLoading;

  /***********************************/
  /************* ACTIONS *************/
  /***********************************/

  const toggleSorting = (c: string) => {
    //this function loops as: DEC -> ASC -> remove sort -> DESC ....
    let isNew = true;
    let remove = false;
    tableState.sortBy.map((s) => {
      if (s.type == c) {
        if (s.direction == SortDirection.DESC) {
          s.direction = SortDirection.ASC;
        } else {
          remove = true;
        }
        isNew = false;
      }
    });

    if (isNew) {
      const sb = new CallsSortingParam();
      if (c == SortTypes.CREATED_AT) {
        sb.direction = SortDirection.ASC;
      } else {
        sb.direction = SortDirection.DESC;
      }
      sb.type = c as SortTypes;
      tableState.sortBy.push(sb);
    }

    if (remove) {
      tableState.sortBy = tableState.sortBy.filter((s) => s.type != c);
    }

    updateTableState({ ...tableState });
  };

  const recoverCall = async (vapiId: string) => {
    if (vapiId) {
      setRecoveringCalls((old) => {
        return [...old, vapiId];
      });
      const r = await CallService.rescoreCall(vapiId);
      if (r) {
        errorToastId.current = toast.success(
          'Call recovering started. It may take a few seconds.',
        );
      }

      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      }, 5000);
    }
  };

  const openCallPreview = (vapiId: string) => {
    setPreviewCallId(vapiId);
    setShowCallPreview(true);
  };

  /////////////////////////////////////
  //////// PLAYLISTS ACTIONS //////////
  /////////////////////////////////////

  const addCallToPlaylistMutation = useMutation({
    mutationFn: PlaylistService.addCallToPlaylist,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['playlists'] });
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      errorToastId.current = toast.success(`Added call to ${data.name}`, {
        position: 'bottom-center',
      });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error adding the call to the playlist');
        errorToastId.current = toast.error(
          'There was an error adding the call to the playlist. Please try again.',
        );
      }
    },
  });

  const removeCallFromPlaylistMutation = useMutation({
    mutationFn: PlaylistService.removeCallFromPlaylist,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['playlists'] });
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      errorToastId.current = toast.success(`Removed call from ${data.name}`, {
        position: 'bottom-center',
      });
    },
    onError: () => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error removing the call from the playlist');
        errorToastId.current = toast.error(
          'There was an error removing the call from the playlist. Please try again.',
        );
      }
    },
  });
  ////////////// end playlist actions

  /***********************************/
  /************* RENDER **************/
  /***********************************/

  const renderLoading = () => {
    const allRows = [];

    let i = 0;
    while (i < tableState.numberOfResults) {
      const allCells = [];
      for (const c of COLUMNS) {
        allCells.push(
          <TableCell>
            <Skeleton className="w-3/4 h-[26px] rounded" />
          </TableCell>,
        );
      }

      allRows.push(
        <TableRow className="cursor-not-allowed">{allCells}</TableRow>,
      );
      i++;
    }

    return allRows;
  };

  const renderRow = (call: CallDto, inactive: boolean = false) => {
    const allCells = [];

    for (const c of COLUMNS) {
      if (c.prop === 'multiSelect') {
        allCells.push(
          <TableCell key={c.prop + call.id} className="w-[50px] pl-4">
            <Checkbox
              checked={checkedCallIds.has(call.id)}
              onToggle={() => {
                toggleCallCheck(call.id);
              }}
            />
          </TableCell>,
        );
      } else if (c.prop === 'createdAt') {
        let content = '';
        content = dayjs(call.createdAt).format('MMM D, h:mm A');
        allCells.push(
          <TableCell key={c.prop + call.id}>
            <div>{timeAgo(new Date(call.createdAt))}</div>
            <div className="text-muted-foreground">{content}</div>
          </TableCell>,
        );
        /************************************************* */
      } else if (c.prop == 'duration') {
        let content = '';
        content = formatDuration(
          new Date(call.endedAt).getTime() - new Date(call.startedAt).getTime(),
        );
        if (inactive) {
          allCells.push(
            <TableCell key={c.prop + call.id}>In Progress</TableCell>,
          );
        } else {
          allCells.push(
            <TableCell key={c.prop + call.id} className="">
              {content}
            </TableCell>,
          );
        }

        /************************************************* */
      } else if (c.prop == 'caller') {
        const caller = isLoggedIn ? call.caller : call.demoInboundFormResponse;

        const firstName =
          isLoggedIn && caller
            ? caller.firstName
            : (caller as any)?.name?.split(' ')[0] || '';
        const lastName =
          isLoggedIn && caller
            ? caller.lastName
            : (caller as any)?.name?.split(' ')[1] || '';

        if (inactive) {
          const content = (
            <div className="flex items-center space-x-2 pl-2 pr-3 py-1 rounded-full hover:bg-muted/80 hover:transition-all duration-300">
              <Avatar className="w-6 h-6">
                {caller?.avatar && <AvatarImage src={caller?.avatar} />}
                <AvatarFallback className="text-sm capitalize">
                  {firstName?.charAt(0)}
                  {lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="capitalize">
                {firstName} {lastName}
              </div>
            </div>
          );
          allCells.push(
            <TableCell key={c.prop + call.id}>{content}</TableCell>,
          );
        } else {
          const content = (
            <Button
              variant={'outline'}
              onClick={(e) => {
                e.stopPropagation();
                if (isLoggedIn) {
                  router.push(LinksManager.members(`${caller?.id}`));
                }
              }}
              className="space-x-2 pl-2 pr-3 py-1 rounded-full hover:bg-muted/80 hover:transition-all duration-300"
            >
              <Avatar className="w-6 h-6">
                {caller?.avatar && <AvatarImage src={caller?.avatar} />}
                <AvatarFallback className="text-sm capitalize">
                  {firstName?.charAt(0)}
                  {lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="capitalize">
                {firstName} {lastName}
              </div>
            </Button>
          );
          allCells.push(
            <TableCell key={c.prop + call.id}>{content}</TableCell>,
          );
        }

        /************************************************* */
      } else if (c.prop == 'agent') {
        const agent = call.agent;

        if (inactive) {
          const content = (
            <div className="flex items-center space-x-2 pl-2 pr-3 py-1 rounded-full hover:bg-muted/80 hover:transition-all duration-300">
              <AgentAvatar className="w-6 h-6" agent={agent} />
              <div className="capitalize">
                {agent?.firstName || ''} {agent?.lastName || ''}
              </div>
            </div>
          );
          allCells.push(
            <TableCell key={c.prop + call.id}>{content}</TableCell>,
          );
        } else {
          const content = (
            <Button
              variant={'outline'}
              onClick={(e) => {
                e.stopPropagation();
                router.push(
                  isLoggedIn
                    ? `/buyers/${agent?.vapiId || ''}`
                    : `/buyers?id=${agent?.vapiId || ''}`,
                );
              }}
              className="space-x-2 pl-2 pr-3 py-1 rounded-full hover:bg-muted/80 hover:transition-all duration-300"
            >
              <AgentAvatar className="w-6 h-6" agent={agent} />
              <div className="capitalize">
                {agent?.firstName || ''} {agent?.lastName || ''}
              </div>
            </Button>
          );
          allCells.push(
            <TableCell key={c.prop + call.id}>{content}</TableCell>,
          );
        }

        /************************************************* */
      } else if (c.prop === 'callType') {
        const agent = call.agent as AgentDto;
        let callType = agent?.callType;

        if (agent.vapiId === '6031ae80-fc31-48f8-9a76-8ee1dc982241') {
          callType = AgentCallType.MANAGER_ONE_ON_ONE;
        }
        const Icon =
          CALL_TYPE_TO_ICON?.[callType as keyof typeof CALL_TYPE_TO_ICON]?.Icon;
        const content = (
          <div className="mr-2 rounded-full bg-gray-100 items-center py-[6px] px-3 inline-flex ">
            {Icon && <Icon className="mr-1 h-4 w-4" />}
            <p className="max-w-26">
              {CALL_TYPE_OPTIONS.find((option) => option.value === callType)
                ?.label || ''}
            </p>
          </div>
        );
        allCells.push(
          <TableCell key={c.prop + call.id} className="whitespace-nowrap">
            {content}
          </TableCell>,
        );
        /************************************************* */
      } else if (c.prop == 'score') {
        let content = '';
        if (!call.scorecard) {
          content = 'N/A';
        } else {
          content = isCompetitionOrg
            ? Number(
                (call.scorecard?.aggregateScore || 0) * 100,
              ).toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 1,
              })
            : Number(
                (call.scorecard?.passedScore / call.scorecard?.totalScore) *
                  100 || 0,
              ).toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
              });
        }

        if (inactive) {
          content = '';
        }
        allCells.push(<TableCell key={c.prop + call.id}>{content}</TableCell>);
        /************************************************* */
      } else if (c.prop == 'playlist') {
        const content = [];
        const playlists: any[] = call.playlists || [];

        const filteredPlaylists = tableState.filterBy.playlists || [];

        let counter = 0;
        for (const p of playlists) {
          if (
            filteredPlaylists.length == 0 ||
            (filteredPlaylists.length > 0 && filteredPlaylists.includes(p.id))
          ) {
            content.push(
              <div
                key={p.id}
                className="mr-2 rounded-full bg-black text-white items-center  py-[6px] px-3 inline-flex"
              >
                <ListVideo size={16} className="mr-1 text-white" />
                <p className="max-w-24 truncate">{p?.name || ''}</p>
                {canAccess(AppPermissions.MANAGE_PLAYLISTS) && (
                  <X
                    size={16}
                    className="ml-1 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeCallFromPlaylistMutation.mutate({
                        callId: call.id,
                        id: p.id,
                      });
                    }}
                  />
                )}
              </div>,
            );
            counter++;
          }

          if (counter > 1) {
            break;
          }
        }

        if (isLoggedIn && canAccess(AppPermissions.MANAGE_PLAYLISTS)) {
          content.push(
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                {counter == 0 ? (
                  <div className="rounded-full border border-dashed  items-center  py-[6px] px-3 inline-flex hover:bg-gray-100 cursor-pointer text-muted-foreground">
                    <Plus size={16} className="mr-1" />
                    Add to playlist
                  </div>
                ) : (
                  <div className="rounded-full border  items-center  py-[6px] px-[6px] inline-flex hover:bg-gray-100 cursor-pointer">
                    <Plus size={16} />
                  </div>
                )}
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {allPlaylists?.map((pl: PlaylistDto, i: number) => {
                  const callInPlaylist = !!pl.calls?.find(
                    (c) => c.callId === call.id,
                  );

                  return (
                    <DropdownMenuItem
                      disabled={!isLoggedIn}
                      className={''}
                      onClick={() => {}}
                      key={'pl-' + pl.id}
                    >
                      <div
                        key={'pl-' + pl.id}
                        className="flex items-center w-full"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (callInPlaylist) {
                            removeCallFromPlaylistMutation.mutate({
                              callId: call.id,
                              id: pl.id,
                            });
                          } else {
                            addCallToPlaylistMutation.mutate({
                              callId: call.id,
                              id: pl.id,
                            });
                          }
                        }}
                      >
                        <div className="mr-2">
                          <Checkbox
                            className="w-4 h-4 ml-auto"
                            checked={callInPlaylist}
                            onToggle={() => {
                              if (callInPlaylist) {
                                removeCallFromPlaylistMutation.mutate({
                                  callId: call.id,
                                  id: pl.id,
                                });
                              } else {
                                addCallToPlaylistMutation.mutate({
                                  callId: call.id,
                                  id: pl.id,
                                });
                              }
                            }}
                          />
                        </div>
                        <div className="flex-1 mr-3">{pl.name}</div>
                        <div className="text-muted-foreground text-sm mr-2">
                          {pl.calls.length} calls
                        </div>
                      </div>
                    </DropdownMenuItem>
                  );
                })}
                {allPlaylists?.length && allPlaylists.length > 0 && (
                  <DropdownMenuSeparator />
                )}
                <DropdownMenuItem
                  disabled={!isLoggedIn}
                  className={''}
                  onClick={(e) => {
                    e.stopPropagation();
                    setNewPlaylistModalOpen(true);
                  }}
                >
                  <div className="flex items-center">
                    <Plus size={16} className="mr-2" />
                    Create a new playlist
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>,
          );
        }

        allCells.push(<TableCell key={c.prop + call.id}>{content}</TableCell>);
        /************************************************* */
      } else if (c.prop == 'sys-status-col') {
        let isEmpty = true;

        if (call.scoringStatus == CallScoringStatus.SCORING) {
          isEmpty = false;
          allCells.push(
            <TableCell key={c.prop + call.id}>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <div className="p-2 hover:bg-gray-100 rounded-full">
                      <Loader2Icon className="w-4 h-4 text-primary animate-spin" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>Scoring in progress</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TableCell>,
          );
        } else if (call.vapiCallEndedReason) {
          const vapiState = CallService.getVapiEndedReasonInfo(
            call.vapiCallEndedReason,
          );
          if (vapiState.isError) {
            isEmpty = false;

            allCells.push(
              <TableCell key={c.prop + call.id}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <div className="p-2 hover:bg-gray-100 rounded-full">
                        <TriangleAlert className="w-4 h-4 text-red-400" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>{vapiState.message}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>,
            );
          }
        }

        if (isEmpty) {
          allCells.push(<TableCell key={c.prop + call.id}>&nbsp;</TableCell>);
        }
      } else if (c.prop == 'actions-col') {
        if (inactive) {
          let showRecoverBtn = false;
          let showCallFailed = false;
          const m = dayjs().diff(dayjs(call.createdAt), 'minutes');
          if (m > 15) {
            showRecoverBtn = true;
          }
          if (m > 60 && call.status.toLowerCase() == 'in-progress') {
            showRecoverBtn = false;
            showCallFailed = true;
          }
          if (showRecoverBtn) {
            allCells.push(
              <TableCell key={c.prop + call.id}>
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        className="text-primary text-xs"
                        onClick={() => {
                          recoverCall(call.vapiId);
                        }}
                      >
                        {recoveringCalls.indexOf(call.vapiId) > -1 ? (
                          <Loader2Icon size={18} className="animate-spin" />
                        ) : (
                          <>
                            <PhoneMissed size={14} className="mr-2" />
                            Recover
                          </>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      Call has been inactive for more than 15 minutes. Click to
                      recover.
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>,
            );
          } else if (showCallFailed) {
            allCells.push(
              <TableCell key={c.prop + call.id}>
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <PhoneMissed size={20} />
                    </TooltipTrigger>
                    <TooltipContent side="bottom">Call failed</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>,
            );
          } else {
            allCells.push(
              <TableCell key={c.prop + call.id}>
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <PhoneCall size={20} />
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      Call in progress
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>,
            );
          }
        } else {
          if (!isPilotEnded) {
            allCells.push(
              <TableCell
                key={c.prop + call.id}
                className={cn(
                  'flex items-center space-x-1 text-muted-foreground',
                  {
                    visible: focusOnCallId == call.id,
                    'group-hover:visible invisible': focusOnCallId != call.id,
                  },
                )}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="p-0 rounded-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    openCallPreview(call.vapiId);
                  }}
                >
                  <Columns2 size={18} />
                </Button>
                <CallActionsDropdown
                  call={call}
                  location="individual_call_page"
                  onOpenChange={(isOpen) => {
                    setFocusOnCallId(isOpen ? call.id : undefined);
                  }}
                />
              </TableCell>,
            );
          }
        }
      }
    }

    return allCells;
  };

  const renderTable = () => {
    const allRows = [];

    if (calls && calls.length > 0) {
      for (const call of calls) {
        let inactive = false;
        if (
          call.status.toLowerCase() == 'queued' ||
          call.status.toLowerCase() == 'in-progress'
        ) {
          inactive = true;
        }

        const cells = renderRow(call, inactive);

        if (inactive) {
          allRows.push(
            <TableRow key={call.id} className="bg-muted cursor-not-allowed">
              {cells}
            </TableRow>,
          );
        } else {
          allRows.push(
            <TableRow
              key={call.id}
              className="cursor-pointer hover:bg-gray-50 group"
              onClick={() => {
                router.push(LinksManager.trainingCalls(call.vapiId));
              }}
            >
              {cells}
            </TableRow>,
          );
        }
      }
    } else {
      allRows.push(
        <TableRow hideBorder={true}>
          <TableCell colSpan={COLUMNS.length} className="text-center pt-20">
            <div className="flex flex-col items-center justify-center">
              <div className="rounded-full bg-gray-100 p-2 text-muted-foreground">
                <LayoutListIcon size={16} />
              </div>
              <div className="mt-6 font-medium text-base">No calls found</div>
              <div className="mt-2 text-muted-foreground text-base w-[360px] text-wrap">
                Check your filters or start your first call, then return to this
                page to review your performance.
              </div>
              <Button
                variant="outline"
                className="mt-6 text-base"
                onClick={() =>
                  router.push(
                    isLoggedIn ? '/ai-roleplay' : '/buyers?callType=cold',
                  )
                }
              >
                <BotIcon size={18} className="mr-3" />
                Open AI Roleplay
              </Button>
            </div>
          </TableCell>
        </TableRow>,
      );
    }

    return allRows;
  };

  const [isMultiDeleteInProgress, setIsMultiDeleteInProgress] = useState(false);
  const deleteDemoCallsMutation = useMutation({
    mutationFn: CallService.deleteDemoCalls,
    onSuccess: (data, params) => {
      Analytics.track(CallEvents.BULK_DELETE_SUCCESS, {
        ...params,
        from: location,
      });
      queryClient.invalidateQueries({ queryKey: ['demoCalls'] });
      setMultiDeleteModalOpen(false);
      setIsMultiDeleteInProgress(false);
      setCheckedCallIds(new Set());
      refetchDemoCalls();
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error deleting the calls');
        errorToastId.current = toast.error(
          'There was an error deleting the calls. Please try again.',
        );
      }
      Analytics.track(CallEvents.BULK_DELETE_ERROR, {
        ...params,
        from: location,
      });
      setMultiDeleteModalOpen(false);
      setIsMultiDeleteInProgress(false);
      setCheckedCallIds(new Set());
      refetchDemoCalls();
    },
  });

  const deleteCallsMutation = useMutation({
    mutationFn: CallService.deleteCalls,
    onSuccess: (data, params) => {
      Analytics.track(CallEvents.BULK_DELETE_SUCCESS, {
        ...params,
        from: location,
      });
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
      setMultiDeleteModalOpen(false);
      setIsMultiDeleteInProgress(false);
      setCheckedCallIds(new Set());
      refetchDemoCalls();
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        console.log('There was an error deleting the call');
        errorToastId.current = toast.error(
          'There was an error deleting the call. Please try again.',
        );
      }
      Analytics.track(CallEvents.BULK_DELETE_ERROR, {
        ...params,
        from: location,
      });
      setMultiDeleteModalOpen(false);
      setIsMultiDeleteInProgress(false);
      setCheckedCallIds(new Set());
      refetchDemoCalls();
    },
  });

  const multiDelete = async () => {
    const callIds = Array.from(checkedCallIds);
    Analytics.track(CallEvents.BULK_DELETE_CLICKED, {
      callIds,
      from: location,
    });
    setIsMultiDeleteInProgress(true);
    if (isLoggedIn) {
      deleteCallsMutation.mutate({
        callIds,
      });
    } else {
      deleteDemoCallsMutation.mutate({
        demoInboundFormResponseId: hbDemoInboundForm?.id || -1,
        callIds,
      });
    }
  };

  const [multiDeleteModalOpen, setMultiDeleteModalOpen] = useState(false);
  const onPressDelete = () => {
    setMultiDeleteModalOpen(true);
  };

  const deleteItem = (
    <DropdownMenuItem
      className={cn('text-red-500', {
        'cursor-pointer': multiDeleteEnabled,
        'cursor-default': !multiDeleteEnabled,
      })}
      disabled={!multiDeleteEnabled}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onPressDelete();
      }}
    >
      <Trash2Icon className="w-4 h-4 mr-2 " />
      <span>Delete</span>
    </DropdownMenuItem>
  );

  return (
    <>
      {checkedCallIds.size > 0 && (
        <div className="mb-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant={'outline'} disabled={!isLoggedIn}>
                {checkedCallIds.size} call{checkedCallIds.size > 1 ? 's' : ''}{' '}
                selected <ChevronDown size={16} className="ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {multiDeleteEnabled ? (
                deleteItem
              ) : (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>{deleteItem}</TooltipTrigger>
                    <TooltipContent>
                      You can only delete your own calls if you are not an
                      admin.
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
      <Table>
        <TableContent>
          <TableRow className="sticky top-0 z-50">
            {COLUMNS.map((c: any, i) => {
              if (c.prop == 'createdAt' || c.prop == 'score') {
                let icon = <CaretDownIcon className="ml-2 h-4 w-4" />;

                if (c.prop == 'createdAt') {
                  //for start date default is desc (see BE)
                  icon = <CaretDownIcon className="ml-2 h-4 w-4" />;

                  tableState.sortBy.map((s) => {
                    if (s.type == c.prop) {
                      if (s.direction == SortDirection.DESC) {
                        icon = <CaretDownIcon className="ml-2 h-4 w-4" />;
                      } else {
                        icon = <CaretUpIcon className="ml-2 h-4 w-4" />;
                      }
                    }
                  });
                } else if (c.prop == 'score') {
                  icon = <CaretSortIcon className="ml-2 h-4 w-4" />;

                  tableState.sortBy.map((s) => {
                    if (s.type == c.prop) {
                      if (s.direction == SortDirection.DESC) {
                        icon = <CaretDownIcon className="ml-2 h-4 w-4" />;
                      } else {
                        icon = <CaretUpIcon className="ml-2 h-4 w-4" />;
                      }
                    }
                  });
                }

                return (
                  <TableCellHead
                    key={c.prop}
                    className="group cursor-pointer"
                    onClick={() => toggleSorting(c.prop)}
                  >
                    <div className="flex items-center">
                      <div className="mr-2">{c.label}</div>
                      <div className="group-hover:text-black">{icon}</div>
                    </div>
                  </TableCellHead>
                );
              } else {
                return <TableCellHead key={c.prop}>{c.label}</TableCellHead>;
              }
            })}
          </TableRow>
          {isTableLoading ? renderLoading() : renderTable()}
        </TableContent>
        <TableFooter>
          <PaginationControls
            from={tableState.from}
            numberOfResults={tableState.numberOfResults}
            totNumberOfRows={callsCount || 0}
            updatePagination={updatePagination}
          />
        </TableFooter>
      </Table>
      <CallPreviewPanel
        open={showCallPreview}
        onPanelClose={() => {
          setShowCallPreview(false);
        }}
        callVapiId={previewCallId}
        showLeaderboardDateFilter
      />
      <CreatePlaylistModal
        modalOpen={newPLaylistModalOpen}
        setModalOpen={setNewPlaylistModalOpen}
        onSubmit={() => {
          setNewPlaylistModalOpen(false);
        }}
        hideShare={true}
      />
      <ConfirmationModal
        open={multiDeleteModalOpen}
        onCancel={() => {
          setMultiDeleteModalOpen(false);
        }}
        onConfirm={() => {
          multiDelete();
        }}
        cancelLabel="Cancel"
        confirmLabel="Delete"
        title={`Do you want to delete ${checkedCallIds.size} calls?`}
        description="This action is permanent."
        isLoading={isMultiDeleteInProgress}
      />
    </>
  );
}
