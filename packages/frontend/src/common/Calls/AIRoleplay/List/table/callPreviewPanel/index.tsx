import {
  Sheet,
  SheetHeader,
  SheetTitle,
  SheetContentLight,
  SheetDescription,
} from '@/components/ui/sheet';
import CallSummary from '../../../Summary';
import { ScrollArea } from '@/components/ui/scroll-area';

interface IProps {
  open: boolean;
  onPanelClose: () => void;
  callVapiId: string;
  showLeaderboardDateFilter?: boolean;
}

export default function CallPreviewPanel({
  open,
  onPanelClose,
  callVapiId,
  showLeaderboardDateFilter = false,
}: IProps) {
  return (
    <Sheet open={open} onOpenChange={onPanelClose}>
      <SheetContentLight side={'rightFull'} className="p-0">
        <ScrollArea className="h-screen text-sm">
          <CallSummary
            vapiId={callVapiId}
            minimal={true}
            showLeaderboardDateFilter={showLeaderboardDateFilter}
          />
        </ScrollArea>
      </SheetContentLight>
    </Sheet>
  );
}
