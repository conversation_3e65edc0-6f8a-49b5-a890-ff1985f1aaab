import { AgentCallType } from '@/lib/Agent/types';
import { FilterState } from '.';

export enum FilterType {
  BUYERS = 'buyers',
  REPS = 'reps',
  PLAYLISTS = 'playlists',
  MIN_DURATION = 'minDuration',
  MAX_DURATION = 'maxDuration',
  DATE = 'dates',
  CALL_TYPES = 'callTypes',
  TAGS = 'tags',
  CALL_BLITZ = 'callblitz',
  TEAMS = 'teams',
  STATUS = 'status',
  ROLES = 'roles',
  LANGUAGES = 'languages',
  SCORECARDS = 'scorecards',
  SCORECARDS_SECTIONS = 'scorecardSections',
  SCORECARDS_CRITERIONS = 'scorecardCriterions',
  SCORECARDS_CRITERIONS_STATUS = 'scorecardCriterionsStatus',
  OBJECTIONS = 'objections',
  SEARCH = 'search',
  FROM = 'from',
  NUM_RESULTS = 'numberOfResults',
}

//match column's names to simplify code
export enum SortTypes {
  CREATED_AT = 'createdAt',
  SCORE = 'score',
}

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}
export class CallsSortingParam {
  type: SortTypes | undefined;
  direction: SortDirection | undefined;
}

export class TableState {
  from: number = 0;
  numberOfResults: number = 20;
  sortBy: CallsSortingParam[] = [];
  filterBy: FilterState = {
    [FilterType.BUYERS]: [],
    [FilterType.REPS]: [],
    [FilterType.PLAYLISTS]: [],
    [FilterType.DATE]: {
      fromDate: undefined,
      toDate: undefined,
    },
    [FilterType.CALL_TYPES]: [],
    [FilterType.TAGS]: [],
    [FilterType.CALL_BLITZ]: [],
    [FilterType.TEAMS]: [],
    [FilterType.STATUS]: [],
    [FilterType.ROLES]: [],
    [FilterType.LANGUAGES]: [],
    [FilterType.SCORECARDS]: [],
    [FilterType.SCORECARDS_SECTIONS]: [],
    [FilterType.SCORECARDS_CRITERIONS]: [],
    [FilterType.SCORECARDS_CRITERIONS_STATUS]: [],
    [FilterType.OBJECTIONS]: [],
    [FilterType.SEARCH]: undefined,
    [FilterType.MIN_DURATION]: undefined,
    [FilterType.MAX_DURATION]: undefined,
    [FilterType.FROM]: 0,
    [FilterType.NUM_RESULTS]: 20,
  };

  constructor(
    fromDate?: Date,
    toDate?: Date,
    buyers?: number[],
    reps?: number[],
    playlists?: number[],
    callTypes?: AgentCallType[],
    tags?: number[],
    callBlitz?: number[],
    from?: number,
    numberOfResults?: number,
    teams?: number[],
    languages?: string[],
    scorecards?: number[],
    scorecardSections?: string[],
    scorecardCriterions?: string[],
    scorecardCriterionsStatus?: string[],
    objections?: string[],
    search?: string,
    minDuration?: number,
    maxDuration?: number,
  ) {
    this.filterBy[FilterType.DATE] = {
      fromDate,
      toDate,
    };

    if (search) {
      this.filterBy[FilterType.SEARCH] = search;
    }

    if (buyers && buyers.length > 0) {
      this.filterBy[FilterType.BUYERS] = buyers;
    }

    if (reps && reps.length > 0) {
      this.filterBy[FilterType.REPS] = reps;
    }

    if (playlists && playlists.length > 0) {
      this.filterBy[FilterType.PLAYLISTS] = playlists.map(String);
    }

    if (callTypes && callTypes.length > 0) {
      this.filterBy[FilterType.CALL_TYPES] = callTypes;
    }

    if (tags && tags.length > 0) {
      this.filterBy[FilterType.TAGS] = tags.map(String);
    }

    if (callBlitz && callBlitz.length > 0) {
      this.filterBy[FilterType.CALL_BLITZ] = callBlitz;
    }

    if (from) {
      this.from = from;
    }

    if (numberOfResults) {
      this.numberOfResults = numberOfResults;
    }

    if (teams && teams.length > 0) {
      this.filterBy[FilterType.TEAMS] = teams;
    }
    if (languages && languages.length > 0) {
      this.filterBy[FilterType.LANGUAGES] = languages;
    }
    if (scorecards && scorecards.length > 0) {
      this.filterBy[FilterType.SCORECARDS] = scorecards;
    }
    if (scorecardSections && scorecardSections.length > 0) {
      this.filterBy[FilterType.SCORECARDS_SECTIONS] = scorecardSections;
    }
    if (scorecardCriterions && scorecardCriterions.length > 0) {
      this.filterBy[FilterType.SCORECARDS_CRITERIONS] = scorecardCriterions;
    }
    if (scorecardCriterionsStatus && scorecardCriterionsStatus.length > 0) {
      this.filterBy[FilterType.SCORECARDS_CRITERIONS_STATUS] =
        scorecardCriterionsStatus;
    }
    if (objections && objections.length > 0) {
      this.filterBy[FilterType.OBJECTIONS] = objections;
    }
    if (minDuration) {
      this.filterBy[FilterType.MIN_DURATION] = minDuration;
    }
    if (maxDuration) {
      this.filterBy[FilterType.MAX_DURATION] = maxDuration;
    }
  }
}
