import { useState, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { BotIcon, CheckIcon, Loader2Icon, FilterX } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { CommandList } from 'cmdk';
import useOrgAgents from '@/hooks/useOrgAgents';
import useOrgAgentsById from '@/hooks/useOrgAgentsById';
import { AgentDto, AnyAgentDto, PublicAgentDto } from '@/lib/Agent/types';
import useDemoAgents from '@/hooks/useDemoAgents';
import { useAuthInfo } from '@propelauth/react';
import AgentAvatar from '@/components/Avatars/Agent';

interface IBuyerFilterProps {
  current: number[];
  onBuyersUpdated: (buyers: string[]) => void;
}

export default function BuyerFilter({
  current,
  onBuyersUpdated,
}: IBuyerFilterProps) {
  const authInfo = useAuthInfo();

  const defaultNumberOfResults = 10;
  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );
  const [search, setSearch] = useState<string>('');

  const [open, setOpen] = useState(false);

  if (!current) {
    current = [];
  }

  const { data: orgAgents, isLoading: isLoadingAgents } = useOrgAgents(
    undefined,
    true,
    0,
    numberOfResults,
    search,
  );
  let { data: currentlySelectedAgents } = useOrgAgentsById(current.map(Number));

  let { data: demoAgents } = useDemoAgents(!authInfo?.isLoggedIn);

  if (!authInfo?.isLoggedIn && search && search != '') {
    demoAgents = demoAgents?.filter((a) =>
      `${a.firstName} ${a.lastName}`
        .toLowerCase()
        .includes(search.trim().toLowerCase()),
    );
  }

  const tmpAgents = authInfo?.isLoggedIn ? orgAgents : demoAgents;

  let noMoreAgents = false;
  const presentInList = useRef<any>({});

  if (tmpAgents) {
    if (tmpAgents.length > 0) {
      if (tmpAgents.length < numberOfResults) {
        noMoreAgents = true;
      }

      tmpAgents.map((a) => {
        presentInList.current[a.id] = true;
      });
    }
  }

  const agents = tmpAgents || [];

  if (!currentlySelectedAgents) {
    currentlySelectedAgents = [];
  }

  const [selected, setSelected] = useState<AgentDto[] | PublicAgentDto[]>(
    currentlySelectedAgents,
  );

  const toggleAgent = (selectedId: string) => {
    const id = parseInt(selectedId);

    let newSelection: AgentDto[] | PublicAgentDto[] = [];

    if (selected.find((val) => val.id === id)) {
      newSelection = selected.filter((val) => val.id !== id) as
        | AgentDto[]
        | PublicAgentDto[];
    } else {
      let selectedAgent: AnyAgentDto | null = null;
      agents.map((a) => {
        if (a.id == id) {
          selectedAgent = a;
          return;
        }
      });

      if (selectedAgent) {
        newSelection = [...selected, selectedAgent] as
          | AgentDto[]
          | PublicAgentDto[];
      }
    }

    if (onBuyersUpdated) {
      onBuyersUpdated(newSelection.map((a) => String(a.id)));
    }
    setSelected([...newSelection] as AgentDto[] | PublicAgentDto[]);
  };

  const clearAll = () => {
    setSelected([]);
    if (onBuyersUpdated) {
      onBuyersUpdated([]);
    }
  };

  const updateFilter = (open: boolean) => {
    setOpen(open);
    //moved to toggleAgent
    // if (!open && onBuyersUpdated) {
    //   onBuyersUpdated(selected.map(a => String(a.id)));
    // }
  };

  const [searchLabel, setSearchLabel] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const resetAgentsForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetAgentsForSearch.current = true;
      presentInList.current = {};
      setNumberOfResults(defaultNumberOfResults);
      setSearch(s);
    }, 200);

    setSearchLabel(s);
  };

  const loadMore = () => {
    setNumberOfResults(numberOfResults + 15);
  };

  return (
    <Popover open={open} onOpenChange={updateFilter}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border w-full"
        >
          <BotIcon className="mr-2 h-4 w-4" />
          Buyers
          {selected.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {selected.length} selected
                </Badge>
              </div>
            </>
          )}
          <div className="flex-1"></div>
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[200px] p-0">
        <Command filter={(value, search) => 1}>
          <CommandInput
            placeholder="Search buyers..."
            className="h-9"
            value={searchLabel}
            onValueChange={filterResults}
          />
          <CommandList>
            {selected.filter((a) => !presentInList.current[a.id]).length >
              0 && (
              <CommandGroup heading="Selected">
                {selected
                  .filter((a) => !presentInList.current[a.id])
                  .map((agent) => (
                    <CommandItem
                      key={agent.id}
                      value={String(agent.id)}
                      onSelect={toggleAgent}
                    >
                      <div
                        className={cn(
                          'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                          selected?.find((val) => val.id === agent?.id)
                            ? 'bg-primary text-primary-foreground'
                            : 'opacity-50 [&_svg]:invisible',
                        )}
                      >
                        <CheckIcon className={cn('h-4 w-4')} />
                      </div>
                      <div className="flex space-x-2 items-center">
                        <AgentAvatar className="w-6 h-6" agent={agent} />
                        <div className="capitalize">
                          {agent?.firstName || ''} {agent?.lastName || ''}
                        </div>
                      </div>
                    </CommandItem>
                  ))}
              </CommandGroup>
            )}
            <CommandGroup
              heading="Buyers"
              className="max-h-[50vh] overflow-y-scroll"
            >
              {agents.map((agent) => (
                <CommandItem
                  key={agent.id}
                  value={String(agent.id)}
                  onSelect={toggleAgent}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      selected?.find((val) => val.id === agent?.id)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex space-x-2 items-center">
                    <AgentAvatar className="w-6 h-6" agent={agent} />
                    <div className="capitalize">
                      {agent?.firstName || ''} {agent?.lastName || ''}
                    </div>
                  </div>
                </CommandItem>
              ))}
              {isLoadingAgents ? (
                <CommandItem className="justify-center text-center">
                  <Loader2Icon className="animate-spin" />
                </CommandItem>
              ) : (
                <>
                  {agents.length > 0 ? (
                    <>
                      {!noMoreAgents && (
                        <CommandItem
                          onSelect={loadMore}
                          className="justify-center text-center"
                        >
                          More...
                        </CommandItem>
                      )}
                    </>
                  ) : (
                    <CommandItem className="justify-center text-center">
                      No buyer found
                    </CommandItem>
                  )}
                </>
              )}
            </CommandGroup>
          </CommandList>
          {selected?.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}
