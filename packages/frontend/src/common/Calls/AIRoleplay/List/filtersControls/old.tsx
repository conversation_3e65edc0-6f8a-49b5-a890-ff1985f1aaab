import { FilterIcon } from 'lucide-react';
import DatesFilter from './DatesFilter';
import { FilterType } from '../common';
import BuyerFilter from './BuyerFilter';
import RepsFilter from './RepsFilter';
import PlaylistsFilter from './PlaylistsFilter';
import CallTypeFilter from './CallTypeFilter';
import { AgentCallType } from '@/lib/Agent/types';
import TagsFilter from '@/common/Buyers/ProdSite/filters/tags';
import CallBlitzFilter from './CallBlitz';
import useUserSession from '@/hooks/useUserSession';
import TeamsFilter from '@/common/Analytics/DashboardTab/Filters/TeamsFilter';
import ScorecardsFilter from '@/common/Analytics/DashboardTab/Filters/ScorecardFilter';
import ScorecardsSectionsFilter from '@/common/Analytics/DashboardTab/Filters/ScorecardsSectionsFilter';
import ScorecardsCriterionsFilter from '@/common/Analytics/DashboardTab/Filters/ScorecardsCriterionsFilter';
import ScorecardCriterionsStatusFilter from '@/common/Analytics/DashboardTab/Filters/ScorecardCriterionsStatusFilter';
import LanguagesFilter from '@/common/Analytics/DashboardTab/Filters/LanguagesFilter';
import { motion } from 'framer-motion';
// import DF from "@/common/Analytics/DashboardTab/Filters/DateFilter";
// import { AnalyticsFilterDateRange, DateFilterType } from "@/lib/Analytics/types";

interface IFiltersControlsProps {
  filtersState: Record<FilterType, any>;
  onFiltersUpdated: (filters: Record<FilterType, any>) => void;
  showFilters: FilterType[];
}
export default function FiltersControls({
  filtersState,
  onFiltersUpdated,
  showFilters,
}: IFiltersControlsProps) {
  const sessionUser = useUserSession();

  const dateFilter = filtersState[FilterType.DATE];
  const buyerFilter = filtersState[FilterType.BUYERS];
  const repsFilter = filtersState[FilterType.REPS];
  const playlistsFilter = filtersState[FilterType.PLAYLISTS];
  const callTypesFilter = filtersState[FilterType.CALL_TYPES];
  const tagsFilter = filtersState[FilterType.TAGS];
  const callBlitzFilter = filtersState[FilterType.CALL_BLITZ];
  const teamsFilter = filtersState[FilterType.TEAMS];

  const updateDatesFilter = (from: Date, to: Date) => {
    dateFilter.fromDate = from;
    dateFilter.toDate = to;

    filtersState[FilterType.DATE] = dateFilter;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateBuyersFilter = (buyers: string[]) => {
    filtersState[FilterType.BUYERS] = buyers;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateRepsFilter = (reps: string[]) => {
    filtersState[FilterType.REPS] = reps;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updatePlaylistFilter = (playlists: string[]) => {
    filtersState[FilterType.PLAYLISTS] = playlists;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateCallTypesFilter = (callTypes: AgentCallType[]) => {
    filtersState[FilterType.CALL_TYPES] = callTypes;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateTagsFilter = (tags: string[]) => {
    filtersState[FilterType.TAGS] = tags;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateCallBlitzFilter = (sessions: string[]) => {
    filtersState[FilterType.CALL_BLITZ] = sessions;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateTeamsFilter = (teams: number[]) => {
    filtersState[FilterType.TEAMS] = teams;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecards = (scorecards: number[]) => {
    filtersState[FilterType.SCORECARDS] = scorecards;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecardsSections = (sections: string[]) => {
    filtersState[FilterType.SCORECARDS_SECTIONS] = sections;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateLanguages = (langs: string[]) => {
    filtersState[FilterType.LANGUAGES] = langs;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecardsCriterions = (criterions: string[]) => {
    filtersState[FilterType.SCORECARDS_CRITERIONS] = criterions;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecardCriterionsStatus = (criterionStatuses: string[]) => {
    filtersState[FilterType.SCORECARDS_CRITERIONS_STATUS] = criterionStatuses;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  return (
    <div>
      <motion.div
        initial={false}
        animate={{
          height: showFilters.length > 0 ? 'auto' : 0,
        }}
        transition={{
          ease: 'easeInOut',
          duration: 0.4,
          delay: 0.1,
        }}
        className={
          'flex flex-wrap items-center space-x-2 space-y-2 overflow-hidden mb-4'
        }
      >
        {showFilters.length > 0 && (
          <FilterIcon className="text-muted-foreground mt-2" size={16} />
        )}

        {showFilters.map((f: FilterType) => {
          if (f === FilterType.DATE) {
            return (
              <DatesFilter
                key={'f-' + f}
                from={dateFilter.fromDate}
                to={dateFilter.toDate}
                onDatesUpdated={updateDatesFilter}
              />
            );
          } else if (f === FilterType.BUYERS) {
            return (
              <BuyerFilter
                key={'f-' + f}
                current={buyerFilter}
                onBuyersUpdated={updateBuyersFilter}
              />
            );
          } else if (f === FilterType.REPS && !sessionUser.isTemp) {
            return (
              <RepsFilter
                key={'f-' + f}
                current={repsFilter}
                onRepsUpdated={updateRepsFilter}
              />
            );
          } else if (f === FilterType.CALL_TYPES) {
            return (
              <CallTypeFilter
                key={'f-' + f}
                current={callTypesFilter}
                onCallTypesUpdated={updateCallTypesFilter}
              />
            );
          } else if (f === FilterType.PLAYLISTS) {
            return (
              <PlaylistsFilter
                key={'f-' + f}
                current={playlistsFilter}
                onPlaylistsUpdated={updatePlaylistFilter}
              />
            );
          } else if (f === FilterType.CALL_BLITZ) {
            return (
              <CallBlitzFilter
                key={'f-' + f}
                current={callBlitzFilter}
                onFiltersUpdated={updateCallBlitzFilter}
              />
            );
          } else if (f === FilterType.TAGS) {
            return (
              <TagsFilter
                key={'f-' + f}
                current={tagsFilter?.map((tid: number) => String(tid))}
                onFiltersUpdated={updateTagsFilter}
              />
            );
          } else if (f === FilterType.TEAMS) {
            return (
              <div key={'f-' + f} className="max-w-[300px]">
                <TeamsFilter
                  current={teamsFilter}
                  onFiltersUpdated={updateTeamsFilter}
                />
              </div>
            );
          } else if (f === FilterType.LANGUAGES) {
            return (
              <div key={'f-' + f} className="max-w-[300px]">
                <LanguagesFilter
                  current={filtersState[FilterType.LANGUAGES]}
                  onFiltersUpdated={updateLanguages}
                />
              </div>
            );
          } else if (f === FilterType.SCORECARDS) {
            return (
              <div key={'f-' + f} className="max-w-[300px]">
                <ScorecardsFilter
                  current={filtersState[FilterType.SCORECARDS]}
                  onFiltersUpdated={updateScorecards}
                />
              </div>
            );
          } else if (f === FilterType.SCORECARDS_SECTIONS) {
            return (
              <div key={'f-' + f} className="max-w-[300px]">
                <ScorecardsSectionsFilter
                  current={filtersState[FilterType.SCORECARDS_SECTIONS]}
                  onSectionsUpdated={updateScorecardsSections}
                  scorecards={filtersState[FilterType.SCORECARDS]}
                />
              </div>
            );
          } else if (f === FilterType.SCORECARDS_CRITERIONS) {
            return (
              <div key={'f-' + f} className="max-w-[300px]">
                <ScorecardsCriterionsFilter
                  current={filtersState[FilterType.SCORECARDS_CRITERIONS]}
                  onCriterionsUpdated={updateScorecardsCriterions}
                  sections={filtersState[FilterType.SCORECARDS_SECTIONS]}
                />
              </div>
            );
          } else if (f === FilterType.SCORECARDS_CRITERIONS_STATUS) {
            return (
              <div key={'f-' + f} className="max-w-[300px]">
                <ScorecardCriterionsStatusFilter
                  current={
                    filtersState[FilterType.SCORECARDS_CRITERIONS_STATUS]
                  }
                  onFilterUpdated={updateScorecardCriterionsStatus}
                />
              </div>
            );
          }
        })}
      </motion.div>
    </div>
  );
}
