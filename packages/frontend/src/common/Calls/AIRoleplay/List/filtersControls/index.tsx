import {
  Sheet,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON>itle,
  SheetContentLight,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Filter } from 'lucide-react';
import { FilterType } from '../common';
import useUserSession from '@/hooks/useUserSession';
import DatesFilter from './DatesFilter';
import BuyerFilter from './BuyerFilter';
import RepsFilter from './RepsFilter';
import CallTypeFilter from './CallTypeFilter';
import CallBlitzFilter from './CallBlitz';
import TagsFilter from '@/common/Buyers/ProdSite/filters/tags';
import { AgentCallType } from '@/lib/Agent/types';
import ScorecardsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ScorecardFilter';
import ScorecardsSectionsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ScorecardsSectionsFilter';
import ScorecardsCriterionsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ScorecardsCriterionsFilter';
import ScorecardCriterionsStatusFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ScorecardCriterionsStatusFilter';
import LanguagesFilter from '@/common/AnalyticsOld/DashboardTab/Filters/LanguagesFilter';
import DurationFilter from './DurationFilter';
import { FilterState } from '..';
import ObjectionsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ObjectionsFilter';

interface IProps {
  open: boolean;
  onFiltersPanelClose: () => void;
  filtersState: FilterState;
  onFiltersUpdated: (filters: FilterState) => void;
}

export default function FiltersPanel({
  open,
  onFiltersPanelClose,
  filtersState,
  onFiltersUpdated,
}: IProps) {
  const { isTemp, isLoggedIn } = useUserSession();

  const dateFilter = filtersState[FilterType.DATE];
  const buyerFilter = filtersState[FilterType.BUYERS];
  const repsFilter = filtersState[FilterType.REPS];
  const callTypesFilter = filtersState[FilterType.CALL_TYPES];
  const tagsFilter = filtersState[FilterType.TAGS];
  const callBlitzFilter = filtersState[FilterType.CALL_BLITZ];
  const minDurationFilter = filtersState[FilterType.MIN_DURATION];
  const maxDurationFilter = filtersState[FilterType.MAX_DURATION];

  const updateDatesFilter = (from: Date | undefined, to: Date | undefined) => {
    dateFilter.fromDate = from;
    dateFilter.toDate = to;

    filtersState[FilterType.DATE] = dateFilter;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateBuyersFilter = (buyers: string[]) => {
    filtersState[FilterType.BUYERS] = buyers.map((buyer) => Number(buyer));

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateRepsFilter = (reps: string[]) => {
    filtersState[FilterType.REPS] = reps.map((rep) => Number(rep));

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateCallTypesFilter = (callTypes: AgentCallType[]) => {
    filtersState[FilterType.CALL_TYPES] = callTypes;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateTagsFilter = (tags: string[]) => {
    filtersState[FilterType.TAGS] = tags;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateCallBlitzFilter = (sessions: string[]) => {
    filtersState[FilterType.CALL_BLITZ] = sessions.map((session) =>
      Number(session),
    );

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecards = (scorecards: number[]) => {
    filtersState[FilterType.SCORECARDS] = scorecards;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecardsSections = (sections: string[]) => {
    filtersState[FilterType.SCORECARDS_SECTIONS] = sections;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateLanguages = (langs: string[]) => {
    filtersState[FilterType.LANGUAGES] = langs;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecardsCriterions = (criterions: string[]) => {
    filtersState[FilterType.SCORECARDS_CRITERIONS] = criterions;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateObjections = (objections: string[]) => {
    filtersState[FilterType.OBJECTIONS] = objections;
    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecardCriterionsStatus = (criterionStatuses: string[]) => {
    filtersState[FilterType.SCORECARDS_CRITERIONS_STATUS] = criterionStatuses;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateDurationFilter = (
    min: number | undefined,
    max: number | undefined,
  ) => {
    if (min) {
      filtersState[FilterType.MIN_DURATION] = min;
    }
    if (max) {
      filtersState[FilterType.MAX_DURATION] = max;
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onFiltersPanelClose}>
      <SheetContentLight className="flex flex-col pr-0">
        <SheetHeader>
          <SheetTitle className="flex items-center">
            <Filter className="mr-2" size={16} /> Filters
          </SheetTitle>
        </SheetHeader>
        <ScrollArea className="grow pr-8">
          <div>
            <div className="text-xs font-semibold">Date range:</div>
            <DatesFilter
              from={dateFilter.fromDate}
              to={dateFilter.toDate}
              onDatesUpdated={updateDatesFilter}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Duration:</div>
            <DurationFilter
              min={minDurationFilter}
              max={maxDurationFilter}
              onDurationUpdated={updateDurationFilter}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Call types:</div>
            <CallTypeFilter
              current={callTypesFilter}
              onCallTypesUpdated={updateCallTypesFilter}
            />
          </div>
          {!isTemp && (
            <div className="mt-3">
              <div className="text-xs font-semibold">Reps:</div>
              <RepsFilter
                current={repsFilter}
                onRepsUpdated={updateRepsFilter}
              />
            </div>
          )}
          <div className="mt-3">
            <div className="text-xs font-semibold">Buyers:</div>
            <BuyerFilter
              current={buyerFilter}
              onBuyersUpdated={updateBuyersFilter}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Call blitz:</div>
            <CallBlitzFilter
              current={callBlitzFilter}
              onFiltersUpdated={updateCallBlitzFilter}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Tags:</div>
            <TagsFilter
              current={tagsFilter}
              onFiltersUpdated={updateTagsFilter}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Languages:</div>
            <LanguagesFilter
              current={filtersState[FilterType.LANGUAGES]}
              onFiltersUpdated={updateLanguages}
              locked={!isLoggedIn}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Scorecards:</div>
            <ScorecardsFilter
              current={filtersState[FilterType.SCORECARDS]}
              onFiltersUpdated={updateScorecards}
              locked={!isLoggedIn}
              lockedMessage={'Book a demo to access scorecards'}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Scorecard sections:</div>
            <ScorecardsSectionsFilter
              current={filtersState[FilterType.SCORECARDS_SECTIONS]}
              onSectionsUpdated={updateScorecardsSections}
              scorecards={filtersState[FilterType.SCORECARDS]}
              locked={!isLoggedIn}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Scorecard criterions:</div>
            <ScorecardsCriterionsFilter
              current={filtersState[FilterType.SCORECARDS_CRITERIONS]}
              onCriterionsUpdated={updateScorecardsCriterions}
              sections={filtersState[FilterType.SCORECARDS_SECTIONS]}
              locked={!isLoggedIn}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">
              Scorecard criterions status:
            </div>
            <ScorecardCriterionsStatusFilter
              current={filtersState[FilterType.SCORECARDS_CRITERIONS_STATUS]}
              onFilterUpdated={updateScorecardCriterionsStatus}
              locked={!isLoggedIn}
            />
          </div>
          <div className="mt-3">
            <div className="text-xs font-semibold">Objections:</div>
            <ObjectionsFilter
              current={filtersState[FilterType.OBJECTIONS]}
              onObjectionsUpdated={updateObjections}
              locked={!isLoggedIn}
            />
          </div>
        </ScrollArea>
      </SheetContentLight>
    </Sheet>
  );
}
