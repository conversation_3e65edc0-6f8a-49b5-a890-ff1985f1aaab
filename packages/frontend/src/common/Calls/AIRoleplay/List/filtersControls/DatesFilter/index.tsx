import { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import dayjs from 'dayjs';
import { CalendarIcon, CaretSortIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';

interface IDatesFilterProps {
  from: Date;
  to: Date;
  onDatesUpdated: (from: Date, to: Date) => void;
}

export default function DatesFilter({
  from,
  to,
  onDatesUpdated,
}: IDatesFilterProps) {
  const [dateRange, setDateRange] = useState<DateRange>({ from, to });

  const udpateDates = (d: DateRange) => {
    setDateRange({ ...d });
    if (onDatesUpdated) {
      d.from = dayjs(d.from).startOf('day').toDate();
      d.to = dayjs(d.to).endOf('day').toDate();
      onDatesUpdated(d.from, d.to);
    }
  };

  return (
    <div className="grid gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="dateRange"
            variant={'outline'}
            className={cn(
              'border justify-start text-left font-normal',
              !dateRange.from && !dateRange.to && 'text-muted-foreground',
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange.from ? (
              dateRange.to ? (
                <>
                  {format(dateRange.from, 'LLL dd, y')} -{' '}
                  {format(dateRange.to, 'LLL dd, y')}
                </>
              ) : (
                format(dateRange.from, 'LLL dd, y')
              )
            ) : (
              <span>Pick a date range</span>
            )}
            <div className="flex-1"></div>
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange.from}
            selected={dateRange}
            onSelect={async (data) => {
              udpateDates(data || { from: undefined });
            }}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
