import { Input } from '@/components/ui/input';
import { useState, useRef } from 'react';

interface DurationRange {
  min: number | undefined;
  max: number | undefined;
}

interface IDurationFilterProps {
  min: number | undefined;
  max: number | undefined;
  onDurationUpdated: (min: number | undefined, max: number | undefined) => void;
}

export default function DatesFilter({
  min,
  max,
  onDurationUpdated,
}: IDurationFilterProps) {
  const [duration, setDuration] = useState<DurationRange>({ min, max });

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const updateDuration = (d: DurationRange) => {
    setDuration({ ...d });

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (onDurationUpdated) {
        onDurationUpdated(d.min, d.max);
      }
    }, 400);
  };

  return (
    <div className="grid gap-2">
      <div>
        <span className="text-xs text-muted-foreground mb-[2px]">
          Min. duration
        </span>
        <Input
          className="bg-white mb-1"
          type="number"
          placeholder="seconds"
          value={duration.min}
          onChange={(e) => {
            updateDuration({
              ...duration,
              min: parseInt(e.target.value),
            });
          }}
        />
      </div>

      <div>
        <span className="text-xs text-muted-foreground mb-[2px]">
          Max. duration
        </span>
        <Input
          className="bg-white mb-1"
          type="number"
          placeholder="seconds"
          value={duration.max}
          onChange={(e) => {
            updateDuration({
              ...duration,
              max: parseInt(e.target.value),
            });
          }}
        />
      </div>
    </div>
  );
}
