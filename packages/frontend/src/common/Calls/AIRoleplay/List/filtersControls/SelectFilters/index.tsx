import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  BotIcon,
  CalendarIcon,
  ChevronDown,
  Earth,
  FilterIcon,
  ListChecks,
  ListVideoIcon,
  PhoneIcon,
  PilcrowIcon,
  Ruler,
  TagIcon,
  TagsIcon,
  Target,
  Users,
  Users2Icon,
} from 'lucide-react';
import { FilterType } from '../../common';
import Checkbox from '@/components/ui/Hyperbound/checkbox-with-label';
import { LightningBoltIcon } from '@radix-ui/react-icons';
import useUserSession from '@/hooks/useUserSession';
import { RepsCanEditScoreResults } from '@/lib/Organization/types';

interface IProps {
  current: FilterType[];
  onFiltersUpdated: (filtersState: FilterType[]) => void;
}

export default function SelectFilters({ current, onFiltersUpdated }: IProps) {
  const [open, setOpen] = useState(false);
  const { dbOrg } = useUserSession();

  let lableFilterCriterionsStatus = 'Criterions Status/Overwritten';
  if (dbOrg?.repsCanEditScoreResults == RepsCanEditScoreResults.DISPUTE_ONLY) {
    lableFilterCriterionsStatus = 'Criterions Status/Disputed';
  }
  const FILTERS: any[] = [
    { id: FilterType.DATE, label: 'Dates', icon: CalendarIcon },
    { id: FilterType.BUYERS, label: 'Buyers', icon: BotIcon },
    { id: FilterType.TAGS, label: 'Tags', icon: TagsIcon },
    { id: FilterType.REPS, label: 'Reps', icon: Users2Icon },
    { id: FilterType.TEAMS, label: 'Teams', icon: Users },
    { id: FilterType.CALL_TYPES, label: 'Call Types', icon: PhoneIcon },
    { id: FilterType.CALL_BLITZ, label: 'Call Blitz', icon: LightningBoltIcon },
    { id: FilterType.PLAYLISTS, label: 'Playlists', icon: ListVideoIcon },
    { id: FilterType.LANGUAGES, label: 'Buyer Languages', icon: Earth },
    { id: FilterType.SCORECARDS, label: 'Scorecards', icon: Target },
    {
      id: FilterType.SCORECARDS_SECTIONS,
      label: 'Scorecards Sections',
      icon: PilcrowIcon,
    },
    {
      id: FilterType.SCORECARDS_CRITERIONS,
      label: 'Scorecards Criterions',
      icon: Ruler,
    },
    {
      id: FilterType.SCORECARDS_CRITERIONS_STATUS,
      label: lableFilterCriterionsStatus,
      icon: ListChecks,
    },
  ];

  const toggleFilter = (f: any) => {
    const index = current.indexOf(f.id);
    if (index > -1) {
      current.splice(index, 1);
    } else {
      current.push(f.id);
    }
    onFiltersUpdated([...current]);
  };

  return (
    <Popover
      open={open}
      onOpenChange={(o: boolean) => {
        setOpen(o);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border"
        >
          <FilterIcon className="mr-2 h-4 w-4" />
          Filters
          {/* <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" /> */}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[280px] p-1">
        {FILTERS.map((f: any) => {
          return (
            <Checkbox
              key={f.id}
              checked={current.indexOf(f.id) > -1}
              onToggle={() => {
                toggleFilter(f);
              }}
              hover={true}
              className="p-1"
            >
              <f.icon className="mr-2 h-4 w-4" />
              {f.label}
            </Checkbox>
          );
        })}
      </PopoverContent>
    </Popover>
  );
}
