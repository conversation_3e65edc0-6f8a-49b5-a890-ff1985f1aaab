import { useState, useRef, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { LockIcon, CheckIcon, Loader2Icon, FilterX } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { CommandList } from 'cmdk';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useCallBlizSearch, {
  useCallBlitzByIds,
} from '@/hooks/useCallBlitzSearch';
import { UserDto } from '@/lib/User/types';
import { useAuthInfo } from '@propelauth/react';
import useDemoUsers from '@/hooks/useDemoUsers';
import { DemoInboundFormResponseDto } from '@/lib/Demo/types';
import { CallBlitzDto } from '@/lib/CallBlitz/types';
import { LightningBoltIcon } from '@radix-ui/react-icons';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ICallBlitzFilterProps {
  current: number[];
  onFiltersUpdated: (callBlitz: string[]) => void;
}

export default function CallBlitzFilter({
  current,
  onFiltersUpdated,
}: ICallBlitzFilterProps) {
  const authInfo = useAuthInfo();

  const defaultNumberOfResults = 10;
  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );
  const [search, setSearch] = useState<string>('');

  const [open, setOpen] = useState(false);

  if (!current) {
    current = [];
  }

  const { data: callBlitzSessions, isLoading: isLoadingSessions } =
    useCallBlizSearch(0, numberOfResults, search);
  const { data: currentlySelectedSessions, isLoading: isLoadingCallBlitz } =
    useCallBlitzByIds(current);

  let noMoreEntries = false;
  const presentInList = useRef<any>({});

  if (callBlitzSessions) {
    if (callBlitzSessions.length > 0) {
      if (callBlitzSessions.length < numberOfResults) {
        noMoreEntries = true;
      }

      callBlitzSessions.map((a: CallBlitzDto) => {
        presentInList.current[a.id || 0] = true;
      });
    }
  }

  useEffect(() => {
    if (!isLoadingCallBlitz && currentlySelectedSessions) {
      setSelected(currentlySelectedSessions);
    }
  }, [isLoadingCallBlitz]);

  const [selected, setSelected] = useState<CallBlitzDto[]>([]);

  const toggleItem = (selectedId: string) => {
    const id = parseInt(selectedId);

    let newSelection: CallBlitzDto[] = [];

    if (selected.find((val) => val.id === id)) {
      newSelection = selected.filter((val) => val.id !== id) as CallBlitzDto[];
    } else {
      let selectedItem: CallBlitzDto | null = null;
      callBlitzSessions?.map((a) => {
        if (a.id == id) {
          selectedItem = a;
          return;
        }
      });

      if (selectedItem) {
        newSelection = [...selected, selectedItem] as CallBlitzDto[];
      }
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(newSelection.map((a) => String(a.id)));
    }
    setSelected([...newSelection] as CallBlitzDto[]);
  };

  const clearAll = () => {
    setSelected([]);
    if (onFiltersUpdated) {
      onFiltersUpdated([]);
    }
  };

  const updateFilter = (open: boolean) => {
    setOpen(open);
  };

  const [searchLabel, setSearchLabel] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout>>();
  const resetUsersForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetUsersForSearch.current = true;
      presentInList.current = {};
      setNumberOfResults(defaultNumberOfResults);
      setSearch(s);
    }, 200);

    setSearchLabel(s);
  };

  const loadMore = () => {
    setNumberOfResults(numberOfResults + 15);
  };

  if (authInfo.isLoggedIn) {
    return (
      <Popover open={open} onOpenChange={updateFilter}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="border w-full"
          >
            <LightningBoltIcon className="mr-2 h-4 w-4" />
            Call Blitz
            {selected.length > 0 && (
              <>
                <Separator orientation="vertical" className="mx-2 h-4" />
                <div className="space-x-1 lg:flex">
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1 font-normal"
                  >
                    {selected.length} selected
                  </Badge>
                </div>
              </>
            )}
            <div className="flex-1"></div>
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent align="start" className="w-[200px] p-0">
          <Command filter={(value, search) => 1}>
            <CommandInput
              placeholder="Search call blitz..."
              className="h-9"
              value={searchLabel}
              onValueChange={filterResults}
            />
            <CommandList>
              {selected.filter((a) => !presentInList.current[a.id || 0])
                .length > 0 && (
                <CommandGroup heading="Selected">
                  {selected
                    .filter((a) => !presentInList.current[a.id || 0])
                    .map((item) => (
                      <CommandItem
                        key={item.id}
                        value={String(item.id)}
                        onSelect={toggleItem}
                      >
                        <div
                          className={cn(
                            'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                            selected?.find((val) => val.id === item?.id)
                              ? 'bg-primary text-primary-foreground'
                              : 'opacity-50 [&_svg]:invisible',
                          )}
                        >
                          <CheckIcon className={cn('h-4 w-4')} />
                        </div>
                        <div className="flex space-x-2 items-center">
                          {item.name}
                        </div>
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}
              <CommandGroup
                heading="Call Blitz Sessions"
                className="max-h-[50vh] overflow-y-scroll"
              >
                {callBlitzSessions?.map((item) => (
                  <CommandItem
                    key={item.id}
                    value={String(item.id)}
                    onSelect={toggleItem}
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        selected?.find((val) => val.id === item?.id)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    <div className="flex space-x-2 items-center">
                      {item.name}
                    </div>
                  </CommandItem>
                ))}
                {isLoadingSessions ? (
                  <CommandItem className="justify-center text-center">
                    <Loader2Icon className="animate-spin" />
                  </CommandItem>
                ) : (
                  <>
                    {(callBlitzSessions?.length || 0) > 0 ? (
                      <>
                        {!noMoreEntries && (
                          <CommandItem
                            onSelect={loadMore}
                            className="justify-center text-center"
                          >
                            More...
                          </CommandItem>
                        )}
                      </>
                    ) : (
                      <CommandItem className="justify-center text-center">
                        No call blitz found
                      </CommandItem>
                    )}
                  </>
                )}
              </CommandGroup>
            </CommandList>
            {selected?.length > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={clearAll}
                    className="justify-center text-center"
                  >
                    <FilterX size="14" />
                    &nbsp;Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </Command>
        </PopoverContent>
      </Popover>
    );
  } else {
    return (
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              className="border-dashed opacity-45"
            >
              <LightningBoltIcon className="mr-2 h-4 w-4" />
              Call Blitz
              <Separator orientation="vertical" className="mx-2 h-4" />
              <LockIcon className="w-4 h-4 text-muted-foreground" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Book a demo to access Call Blitz</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
}
