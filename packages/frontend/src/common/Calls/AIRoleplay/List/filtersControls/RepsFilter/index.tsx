import { useState, useRef, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Users2Icon, CheckIcon, Loader2Icon, FilterX } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { CommandList } from 'cmdk';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useOrgUsers from '@/hooks/useOrgUsers';
import useOrgUsersByIds from '@/hooks/useOrgUsersByIds';
import { UserDto } from '@/lib/User/types';
import { useAuthInfo } from '@propelauth/react';
import useDemoUsers from '@/hooks/useDemoUsers';
import { DemoInboundFormResponseDto } from '@/lib/Demo/types';

interface IRepFilterProps {
  maxResults?: number;
  label?: string;
  current: number[];
  onRepsUpdated: (reps: string[]) => void;
}

export default function RepsFilter({
  maxResults,
  label,
  current,
  onRepsUpdated,
}: IRepFilterProps) {
  const authInfo = useAuthInfo();

  const defaultNumberOfResults = 10;
  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );
  const [search, setSearch] = useState<string>('');

  const [open, setOpen] = useState(false);

  if (!current) {
    current = [];
  }

  const { data: result, isLoading: isLoadingOrgUsers } = useOrgUsers(
    true,
    0,
    numberOfResults,
    search,
  );

  const orgUsers = result?.data || [];

  let { data: currentlySelectedUsers } = useOrgUsersByIds(current.map(Number));

  const { data: demoUsersConst, isLoading: isLoadingDemoUsers } = useDemoUsers(
    !authInfo?.isLoggedIn,
  );
  let demoUsers = demoUsersConst;

  if (!authInfo?.isLoggedIn && search && search != '') {
    demoUsers = demoUsers?.filter((u) =>
      u.name.toLowerCase().includes(search.trim().toLowerCase()),
    );
  }

  const tmpUsers = authInfo?.isLoggedIn ? orgUsers : demoUsers;

  let noMoreUsers = false;
  const presentInList = useRef<any>({});

  if (tmpUsers) {
    if (tmpUsers.length > 0) {
      if (tmpUsers.length < numberOfResults) {
        noMoreUsers = true;
      }

      tmpUsers.map((a) => {
        presentInList.current[a.id] = true;
      });
    }
  }

  if (!authInfo?.isLoggedIn) {
    tmpUsers?.forEach((u) => {
      if ('name' in u) {
        const n = u.name.split(' ');
        u.firstName = n[0];
        u.lastName = n[1];
      }
    });
  }

  const users = tmpUsers || [];

  if (!currentlySelectedUsers) {
    currentlySelectedUsers = [];
  }
  const [selected, setSelected] = useState<
    UserDto[] | DemoInboundFormResponseDto[]
  >(currentlySelectedUsers);

  const toggleUser = (selectedId: string) => {
    const id = parseInt(selectedId);

    let newSelection: UserDto[] | DemoInboundFormResponseDto[] = [];

    if (selected.find((val) => val.id === id)) {
      newSelection = selected.filter((val) => val.id !== id) as
        | UserDto[]
        | DemoInboundFormResponseDto[];
    } else {
      let selectedUser: UserDto | DemoInboundFormResponseDto | null = null;
      users.map((a) => {
        if (a.id == id) {
          selectedUser = a;
          return;
        }
      });

      if (selectedUser) {
        newSelection = [...selected, selectedUser] as
          | UserDto[]
          | DemoInboundFormResponseDto[];
      }
    }

    if (maxResults && newSelection.length > maxResults) {
      newSelection = newSelection.slice(1, maxResults + 1);
    }

    if (onRepsUpdated) {
      onRepsUpdated(newSelection.map((a) => String(a.id)));
    }
    setSelected([...newSelection] as UserDto[] | DemoInboundFormResponseDto[]);
  };

  const clearAll = () => {
    setSelected([]);
    if (onRepsUpdated) {
      onRepsUpdated([]);
    }
  };

  const updateFilter = (open: boolean) => {
    setOpen(open);
    //moved to toggleUser:
    // if (!open) {
    //   onRepsUpdated(selected.map(a => String(a.id)));
    // }
  };

  //console.log("users", users);

  const [searchLabel, setSearchLabel] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const resetUsersForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetUsersForSearch.current = true;
      presentInList.current = {};
      setNumberOfResults(defaultNumberOfResults);
      setSearch(s);
    }, 200);

    setSearchLabel(s);
  };

  const loadMore = () => {
    setNumberOfResults(numberOfResults + 15);
  };

  return (
    <Popover open={open} onOpenChange={updateFilter}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border w-full"
        >
          <Users2Icon className="mr-2 h-4 w-4" />
          {label ?? 'Reps'}
          {selected.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {maxResults && maxResults == 1 && (
                    <span>
                      {selected[0].firstName} {selected[0].lastName}
                    </span>
                  )}
                  {maxResults && maxResults > 1 && (
                    <span>{selected.length} selected</span>
                  )}
                  {!maxResults && <span>{selected.length} selected</span>}
                </Badge>
              </div>
            </>
          )}
          <div className="flex-1"></div>
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[200px] p-0">
        <Command filter={(value, search) => 1}>
          <CommandInput
            placeholder="Search reps..."
            className="h-9"
            value={searchLabel}
            onValueChange={filterResults}
          />
          <CommandList>
            {selected.filter((a) => !presentInList.current[a.id]).length >
              0 && (
              <CommandGroup heading="Selected">
                {selected
                  .filter((a) => !presentInList.current[a.id])
                  .map((user) => (
                    <CommandItem
                      key={user.id}
                      value={String(user.id)}
                      onSelect={toggleUser}
                    >
                      {(maxResults ?? 2) > 1 && (
                        <div
                          className={cn(
                            'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                            selected?.find((val) => val.id === user?.id)
                              ? 'bg-primary text-primary-foreground'
                              : 'opacity-50 [&_svg]:invisible',
                          )}
                        >
                          <CheckIcon className={cn('h-4 w-4')} />
                        </div>
                      )}
                      <div className="flex space-x-2 items-center">
                        <Avatar className="w-6 h-6">
                          {user?.avatar && (
                            <AvatarImage src={user.avatar} alt="Avatar" />
                          )}
                          <AvatarFallback className="text-sm">
                            {user?.firstName?.charAt(0) || ''}
                            {user?.lastName?.charAt(0) || ''}
                          </AvatarFallback>
                        </Avatar>
                        <div className="capitalize">
                          {user?.firstName || ''} {user?.lastName || ''}
                        </div>
                      </div>
                    </CommandItem>
                  ))}
              </CommandGroup>
            )}
            <CommandGroup
              heading={label ?? 'Reps'}
              className="max-h-[50vh] overflow-y-scroll "
            >
              {users.map((user) => (
                <CommandItem
                  key={user.id}
                  value={String(user.id)}
                  onSelect={toggleUser}
                  className={`${selected?.find((val) => val.id === user?.id) ? 'bg-gray-100' : ''}`}
                >
                  {(maxResults ?? 2) > 1 && (
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        selected?.find((val) => val.id === user?.id)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                  )}
                  <div className="flex space-x-2 items-center">
                    <Avatar className="w-6 h-6">
                      {user?.avatar && (
                        <AvatarImage src={user.avatar} alt="Avatar" />
                      )}
                      <AvatarFallback className="text-sm">
                        {user?.firstName?.charAt(0) || ''}
                        {user?.lastName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>
                    <div className="capitalize">
                      {user?.firstName || ''} {user?.lastName || ''}
                    </div>
                  </div>
                </CommandItem>
              ))}
              {isLoadingOrgUsers || isLoadingDemoUsers ? (
                <CommandItem className="justify-center text-center">
                  <Loader2Icon className="animate-spin" />
                </CommandItem>
              ) : (
                <>
                  {users.length > 0 ? (
                    <>
                      {!noMoreUsers && (
                        <CommandItem
                          onSelect={loadMore}
                          className="justify-center text-center"
                        >
                          More...
                        </CommandItem>
                      )}
                    </>
                  ) : (
                    <CommandItem className="justify-center text-center">
                      No reps found
                    </CommandItem>
                  )}
                </>
              )}
            </CommandGroup>
          </CommandList>
          {selected?.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}
