'use client';

import { CaretSortIcon, CheckIcon } from '@radix-ui/react-icons';
import * as React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import usePlaylists from '@/hooks/usePlaylists';
import { cn } from '@/lib/utils';
import { ListVideoIcon, LockIcon } from 'lucide-react';
import { useAuthInfo } from '@propelauth/react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { update } from 'lodash';

interface FilterOption {
  label: string;
  value: string;
}

interface IRepFilterProps {
  current: number[];
  onPlaylistsUpdated: (playlist: string[]) => void;
}

export default function PlaylistsFilter({
  current,
  onPlaylistsUpdated,
}: IRepFilterProps) {
  const authInfo = useAuthInfo();

  const { data: playlists, isLoading } = usePlaylists();
  const [open, setOpen] = React.useState(false);
  const [values, setValues] = React.useState<FilterOption[]>([]);

  React.useEffect(() => {
    let newValues: FilterOption[] = [];
    if (playlists && playlists.length > 0 && current && current.length > 0) {
      newValues = playlists
        ?.filter((playlist) => current.includes(playlist.id))
        .map((playlist) => {
          return {
            label: playlist.name,
            value: String(playlist.id),
          };
        });
    }

    setValues(newValues);
  }, [playlists?.length]);

  const updateFilter = (open: boolean) => {
    setOpen(open);
    // if (!open) {
    //   onPlaylistsUpdated(values.map(a => String(a.value)));
    // }
  };

  const updateSelected = (newValues: FilterOption[]) => {
    setValues(newValues);

    if (onPlaylistsUpdated) {
      onPlaylistsUpdated(newValues.map((a) => String(a.value)));
    }
  };

  if (authInfo.isLoggedIn) {
    return (
      <Popover open={open} onOpenChange={updateFilter}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="border w-full"
          >
            <ListVideoIcon className="mr-2 h-4 w-4" />
            Playlists
            {values.length > 0 && (
              <>
                <Separator orientation="vertical" className="mx-2 h-4" />
                <div className="space-x-1 lg:flex">
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1 font-normal"
                  >
                    {values.length} selected
                  </Badge>
                </div>
              </>
            )}
            <div className="flex-1"></div>
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent align="start" className="w-[200px] p-0">
          <Command
            filter={(value, search) => {
              const playlist = playlists?.find(
                (playlist) => playlist.id === Number(value),
              );
              const name = playlist?.name.toLowerCase() || '';
              if (name.includes(search.toLowerCase())) return 1;
              return 0;
            }}
          >
            <CommandInput placeholder="Search playlists..." className="h-9" />
            <CommandEmpty>No playlist found</CommandEmpty>
            <CommandGroup>
              {(playlists || []).map((item) => (
                <CommandItem
                  key={item.id}
                  value={String(item.id)}
                  onSelect={(currentValue) => {
                    const newValues = values.find(
                      (val) => val.value === currentValue,
                    )
                      ? values.filter((val) => val.value !== currentValue)
                      : [
                          ...values,
                          {
                            label: item.name,
                            value: currentValue,
                          },
                        ];
                    updateSelected(newValues);
                  }}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      values.find((val) => val.value === String(item.id))
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  {item.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    );
  } else {
    return (
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              className="border-dashed opacity-45"
            >
              <ListVideoIcon className="mr-2 h-4 w-4" />
              Playlists
              <Separator orientation="vertical" className="mx-2 h-4" />
              <LockIcon className="w-4 h-4 text-muted-foreground" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Book a demo to access playlists</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
}
