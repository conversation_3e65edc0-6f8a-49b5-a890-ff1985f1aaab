import { AgentCallType } from '@/lib/Agent/types';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useState, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { PhoneIcon, CheckIcon, Loader2Icon, FilterX } from 'lucide-react';
import {
  Command,
  CommandList,
  CommandItem,
  CommandSeparator,
  CommandGroup,
} from '@/components/ui/command';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';

interface ICallTypeFilterProps {
  disabled?: boolean;
  current: AgentCallType[];
  onCallTypesUpdated: (callTypes: AgentCallType[]) => void;
}

export default function CallTypeFilter({
  disabled,
  current,
  onCallTypesUpdated,
}: ICallTypeFilterProps) {
  const [open, setOpen] = useState(false);

  if (!current) {
    current = [];
  }

  const updateFilter = (open: boolean) => {
    if (!disabled) {
      setOpen(open);
    }
  };

  const toggleCallType = (callType: AgentCallType) => {
    const tmp = [];
    if (current.find((val) => val === callType)) {
      tmp.push(...current.filter((val) => val !== callType));
    } else {
      tmp.push(...current);
      tmp.push(callType);
    }

    if (onCallTypesUpdated) {
      onCallTypesUpdated(tmp);
    }
  };

  const clearAll = () => {
    if (onCallTypesUpdated) {
      onCallTypesUpdated([]);
    }

    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={updateFilter}>
      <PopoverTrigger asChild>
        <Button
          disabled={disabled}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border w-full"
        >
          <PhoneIcon className="mr-2 h-4 w-4" />
          Call Type
          {current.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {current.length} selected
                </Badge>
              </div>
            </>
          )}
          <div className="flex-1"></div>
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[200px] p-0">
        <Command>
          <CommandList>
            {CALL_TYPE_OPTIONS.map((ct) => {
              const Icon =
                CALL_TYPE_TO_ICON?.[ct.value as keyof typeof CALL_TYPE_TO_ICON]
                  ?.Icon;
              return (
                <CommandItem
                  key={ct.value}
                  value={ct.value}
                  onSelect={() => toggleCallType(ct.value)}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      current?.find((val) => val === ct?.value)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex items-center">
                    {Icon && <Icon className="mr-2 h-4 w-4" />}
                    <div className="capitalize">{ct.label}</div>
                  </div>
                </CommandItem>
              );
            })}
          </CommandList>
          {current?.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}
