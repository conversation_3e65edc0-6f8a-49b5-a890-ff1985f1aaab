import PageHeader from '@/components/PageHeader';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import CallService from '@/lib/Call';
import {
  CloudDownload,
  FileSpreadsheet,
  FilterIcon,
  Loader2Icon,
  Lock,
  LockIcon,
  VideoIcon,
} from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import RoleplayCallsList from './AIRoleplay/List';
import { TableState } from './AIRoleplay/List/common';
import RealCalls from './Real/List';
import ImportRealCallsSelect from './Real/List/Import';
import ImportTasksStatus from './Real/List/Import/ImportTasksStatus';
import { AddRecorderToMeeting } from './Real/AddRecorderToMeetingDialog';
import { useIntegrations } from '@/hooks/useIntegrations';
import {
  IntegrationServiceType,
  IntegrationStatus,
} from '@/lib/Integrations/types';

interface IProps {
  openTab?: string;
}

export default function CallsTable({ openTab }: IProps) {
  const { setUrlParameter } = useRouting();
  const [currentTab, setCurrentTab] = useState<string>('ai-roleplay');
  const errorToastId = useRef<Id | null>(null);
  const {
    isLoggedIn,
    canAccessRealCallsScoring,
    onlyAdminsCanFilterCalls,
    isCallHistoryLimitedUi,
    isAdmin,
  } = useUserSession();
  const { data: integrations } = useIntegrations();
  const { zoomIntegration, googleMeetIntegration, microsoftTeamsIntegration } =
    useMemo(() => {
      const realCallIntegrations = (integrations || []).filter(
        (i) =>
          i.status === IntegrationStatus.ACTIVE &&
          i.type === IntegrationServiceType.REAL_CALL_SCORING,
      );
      const zoomIntegration = realCallIntegrations.find(
        (i) => i.provider?.companyName?.toLowerCase() === 'zoom',
      );
      const googleMeetIntegration = realCallIntegrations.find(
        (i) => i.provider?.companyName?.toLowerCase() === 'google',
      );
      const microsoftTeamsIntegration = realCallIntegrations.find(
        (i) => i.provider?.companyName?.toLowerCase() === 'microsoft',
      );
      return {
        zoomIntegration,
        googleMeetIntegration,
        microsoftTeamsIntegration,
      };
    }, [integrations]);
  const [showFilters, setShowFilters] = useState<boolean>(false);

  /*****************************/
  /********* INIT ***********/
  /*****************************/

  useEffect(() => {
    if (openTab && openTab !== '') {
      setCurrentTab(openTab);
    } else {
      setCurrentTab('ai-roleplay');
    }
  }, [openTab]);

  /*****************************/
  /********* ACTIONS ***********/
  /*****************************/

  const [tableState, setTableState] = useState<TableState>(new TableState());

  const [addRecorderDialogOpen, setAddRecorderDialogOpen] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const exportAsCsv = async () => {
    errorToastId.current = toast.success(
      'Export started. It may take a few seconds.',
    );
    setDownloading(true);
    const data = await CallService.downloadOrgCalls(tableState.filterBy);

    const blob = new Blob([data], {
      type: 'text/plain',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hyperbound-calls.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    setDownloading(false);
  };

  const onTabChange = (t: string) => {
    setTimeout(() => {
      setUrlParameter('openTab', t);
    });

    setCurrentTab(t);
  };

  const toggleFiltersPanel = () => {
    setShowFilters((o) => !o);
  };

  /****************************/
  /********** EVENTS **********/
  /****************************/

  const onTableStateUpdated = (ts: TableState) => {
    setTableState(ts);
  };

  const now = useMemo(() => new Date(), []);

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  return (
    <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6">
      <PageHeader title="Call History" />
      <Tabs defaultValue={currentTab} value={currentTab}>
        <div className="flex items-center mt-6">
          <div className="flex-1">
            <TabsList>
              <TabsTrigger
                value="ai-roleplay"
                onClick={() => onTabChange('ai-roleplay')}
              >
                Roleplay Calls
              </TabsTrigger>
              {canAccessRealCallsScoring ? (
                <TabsTrigger
                  value="real-calls"
                  onClick={() => onTabChange('real-calls')}
                >
                  Real Buyer Calls
                </TabsTrigger>
              ) : (
                <TabsTrigger value="dummy">
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger asChild className="opacity-[0.5]">
                        <div className="flex items-center">
                          Real Buyer Calls <Lock size={12} className="ml-2" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        {isLoggedIn
                          ? 'For real calls scoring, contact Hyperbound for access'
                          : 'Book a demo to access real calls scoring'}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TabsTrigger>
              )}
            </TabsList>
          </div>

          <div className="flex items-center">
            {currentTab == 'ai-roleplay' && (
              <>
                {(!onlyAdminsCanFilterCalls || isAdmin) &&
                  !isCallHistoryLimitedUi && (
                    <Button variant="outline" onClick={toggleFiltersPanel}>
                      <FilterIcon className="mr-2 h-4 w-4" />
                      Filters
                    </Button>
                  )}
                {isCallHistoryLimitedUi ? (
                  <></>
                ) : isLoggedIn ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        className="ml-2 flex items-center"
                        disabled={!isLoggedIn || downloading}
                      >
                        {downloading ? (
                          <Loader2Icon
                            size={18}
                            className="mr-2 animate-spin"
                          />
                        ) : (
                          <CloudDownload size={18} className="mr-2" />
                        )}
                        Export
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="center">
                      <DropdownMenuItem
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          exportAsCsv();
                        }}
                      >
                        <FileSpreadsheet className="w-4 h-4 mr-2 text-muted-foreground" />
                        <span>CSV</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          className="ml-2 flex items-center"
                          disabled={!isLoggedIn}
                        >
                          <CloudDownload size={18} className="mr-2" />
                          Export
                          <LockIcon className="ml-2 w-4 h-4 text-muted-foreground" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p>Book a demo to access calls export</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </>
            )}

            {currentTab == 'real-calls' && (
              <>
                {(!!zoomIntegration ||
                  !!googleMeetIntegration ||
                  !!microsoftTeamsIntegration) && (
                  <Button
                    variant="default"
                    onClick={() => setAddRecorderDialogOpen(true)}
                    className="mr-2"
                  >
                    <VideoIcon className="mr-2 h-4 w-4" />
                    Add Recorder to Meeting
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={toggleFiltersPanel}
                  className="mr-2"
                >
                  <FilterIcon className="mr-2 h-4 w-4" />
                  Filters
                </Button>
                <ImportRealCallsSelect />
                <ImportTasksStatus className="ml-2" />
              </>
            )}
          </div>
        </div>

        <TabsContent value="ai-roleplay">
          <RoleplayCallsList
            onTableStateUpdated={onTableStateUpdated}
            showFiltersPanel={showFilters}
            onFiltersPanelClose={() => {
              setShowFilters(false);
            }}
          />
        </TabsContent>

        <TabsContent value="real-calls">
          <RealCalls
            showFiltersPanel={showFilters}
            onFiltersPanelClose={() => {
              setShowFilters(false);
            }}
            now={now}
          />
        </TabsContent>
      </Tabs>
      <AddRecorderToMeeting
        open={addRecorderDialogOpen}
        onClose={() => setAddRecorderDialogOpen(false)}
        zoomIntegration={zoomIntegration}
        googleMeetIntegration={googleMeetIntegration}
        microsoftTeamsIntegration={microsoftTeamsIntegration}
      />
    </ScrollablePage>
  );
}
