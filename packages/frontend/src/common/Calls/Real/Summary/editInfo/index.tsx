import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { useEffect, useState } from 'react';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import { Loader2Icon } from 'lucide-react';
import RepsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/RepsFilter';
import { useQueryClient } from '@tanstack/react-query';
import { RealCallsService } from '@/lib/Integrations';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';

interface IProps {
  open: boolean;
  onClose: () => void;
  call: {
    id: number;
    callerId?: number;
    callType: string;
    scorecard?: {
      scorecardConfigId?: number;
    };
  };
}

export default function EditCallInfo({ open, onClose, call }: IProps) {
  const queryClient = useQueryClient();
  const { callTypeOptions } = useCallTypeOptions();
  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);
  const [isLoadingScorecards, setIsLoadingScorecards] =
    useState<boolean>(false);
  const [showSaveAndScore, setShowSaveAndScore] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [savingNotScoring, setSavingNotScoring] = useState<boolean>(false);
  const [savingScoring, setSavingScoring] = useState<boolean>(false);

  const [formState, setFormState] = useState<{
    callerId?: number;
    callType: string;
    scorecardId?: number;
  }>({
    callerId: call.callerId,
    callType: call.callType,
    scorecardId: call.scorecard?.scorecardConfigId,
  });

  /******************************/
  /************ INIT ************/
  /******************************/

  const fetchData = async () => {
    setIsLoadingScorecards(true);

    const scs = await ScorecardConfigService.getAllScorecardConfigsForOrg();

    setAllScorecards(scs);

    setIsLoadingScorecards(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (call) {
      setFormState({
        callerId: call.callerId,
        callType: call.callType,
        scorecardId: call.scorecard?.scorecardConfigId,
      });
    }
  }, [call]);

  /******************************/
  /********* ACTIONS ************/
  /******************************/

  const updateCallType = (ct: string) => {
    setFormState({
      ...formState,
      callType: ct,
    });
  };

  const updateScorecard = (scid: number) => {
    if (scid === formState.scorecardId) {
      setShowSaveAndScore(false);
    } else {
      setShowSaveAndScore(true);
    }
    setFormState({
      ...formState,
      scorecardId: scid,
    });
  };

  const updateCaller = (reps: string[]) => {
    if (reps.length > 0) {
      setFormState({
        ...formState,
        callerId: parseInt(reps[0]),
      });
    }
  };

  const saveAndScore = () => {
    setSavingScoring(true);
    save(true);
  };

  const save = async (rescore = false) => {
    if (!rescore) {
      setSavingNotScoring(true);
    }
    setIsSaving(true);

    try {
      await RealCallsService.updateCallInfo(
        call.id,
        formState.callType,
        formState.callerId,
        formState.scorecardId,
        rescore,
      );
    } catch (e) {
      console.log(e);
    }

    queryClient.invalidateQueries({ queryKey: ['real-call-details', call.id] });
    queryClient.invalidateQueries({
      queryKey: ['real-call-public-details', call.id],
    });
    queryClient.invalidateQueries({
      queryKey: ['real-call-scorecard-new', call.id],
    });
    onClose();
  };

  /******************************/
  /********** RENDER ************/
  /******************************/

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Edit call info
          </DialogTitle>
        </DialogHeader>
        <div>
          <div className="flex items-center text-sm mb-3 mt-6">
            <div className="font-semibold w-[83px]">Host</div>
            <div className="flex-1">
              <RepsFilter
                current={[formState.callerId || 0]}
                onRepsUpdated={updateCaller}
                isRadioSelect={true}
                displaySelectedName={true}
                hideClearBtn={true}
              />
            </div>
          </div>
          <div className="flex items-center text-sm mb-3">
            <div className="font-semibold w-[83px]">Call type</div>
            <div className="flex-1">
              <Select
                onValueChange={(value: string) => {
                  updateCallType(value);
                }}
                value={formState.callType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="" />
                </SelectTrigger>
                <SelectContent>
                  {callTypeOptions.map((ct) => {
                    const Icon =
                      CALL_TYPE_TO_ICON[
                        ct.value as keyof typeof CALL_TYPE_TO_ICON
                      ].Icon;

                    return (
                      <SelectItem key={ct.value} value={ct.value}>
                        <div className="flex items-center">
                          <div className="mr-1">
                            <Icon size={12} />
                          </div>
                          <div>{ct.label}</div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex items-center text-sm mb-3">
            <div className="font-semibold w-[83px]">Scorecard</div>
            {isLoadingScorecards && (
              <div className="ml-1">
                <Loader2Icon className="animate-spin" />
              </div>
            )}
            <div className="flex-1">
              {!isLoadingScorecards && (
                <Select
                  onValueChange={(value: string) => {
                    updateScorecard(parseInt(value));
                  }}
                  value={String(formState.scorecardId)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a scorecard" />
                  </SelectTrigger>
                  <SelectContent>
                    {allScorecards && (
                      <>
                        {allScorecards.map((option) => {
                          let show = true;
                          if (option.callTypes) {
                            for (const ct of option.callTypes) {
                              if (ct.callType === formState.callType) {
                                show = true;
                                break;
                              } else {
                                show = false;
                              }
                            }
                          }
                          if (show) {
                            return (
                              <SelectItem
                                key={option.id}
                                value={String(option.id)}
                              >
                                {option.tag}
                              </SelectItem>
                            );
                          }
                        })}
                      </>
                    )}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
        </div>
        <DialogFooter>
          {!showSaveAndScore && (
            <Button
              disabled={isSaving}
              onClick={() => {
                save();
              }}
              className="ml-2"
              variant={showSaveAndScore ? 'outline' : 'default'}
            >
              {savingNotScoring ? (
                <>
                  <Loader2Icon className="animate-spin mr-2" size={16} />
                  Saving
                </>
              ) : (
                'Save'
              )}
            </Button>
          )}

          {showSaveAndScore && (
            <Button onClick={saveAndScore} disabled={isSaving}>
              {savingScoring ? (
                <>
                  <Loader2Icon className="animate-spin mr-2" size={16} />
                  Saving
                </>
              ) : (
                'Save and rescore'
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
