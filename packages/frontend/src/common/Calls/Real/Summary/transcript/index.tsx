import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export interface TranscriptPartTyp {
  userName: string;
  secondsFromStart: number;
  message: string;
  role: 'user' | 'bot';
}

interface IProps {
  transcript?: TranscriptPartTyp[];
  usersAvatars: any;
}

export default function Transcript({ transcript, usersAvatars }: IProps) {
  const str_pad_left = (a: string, pad: string, length: number) => {
    return (new Array(length + 1).join(pad) + a).slice(-length);
  };

  // console.log(transcript);

  return (
    <div className="h-full w-[25vw] px-2 py-2">
      <div>
        <h3 className="font-semibold mb-2">Transcript</h3>
      </div>
      {transcript &&
        transcript.map((t, i: number) => {
          const avatar = usersAvatars[t.userName];
          let time = '0:00';
          const role = t.role;

          const duration = Math.floor(t.secondsFromStart);
          if (duration) {
            const minutes = Math.floor(duration / 60);
            const seconds = duration - minutes * 60;

            time =
              str_pad_left(String(minutes), '', 2) +
              ':' +
              str_pad_left(String(seconds), '0', 2);
          }

          return (
            <div
              key={`s-${i}`}
              className={cn(
                'flex items-center space-x-4 mt-1 rounded-lg py-3 px-2',
                {
                  'justify-end': role === 'user',
                  'justify-start': role === 'bot',
                },
              )}
            >
              {role === 'bot' && (
                <Avatar className="w-8 h-8">
                  <AvatarImage src={avatar?.url ? avatar.url : ''} />
                  <AvatarFallback className="text-sm">
                    {avatar?.initials}
                  </AvatarFallback>
                </Avatar>
              )}

              <div
                className={cn('flex flex-col', {
                  'ml-10': role === 'user',
                  '': role === 'bot',
                })}
              >
                <div className="text-xs text-muted-foreground flex items-center">
                  {role === 'user' && (
                    <div className="flex-1">{t.userName}</div>
                  )}
                  <div>{time}</div>
                  {role === 'bot' && (
                    <>
                      <div className="flex-1 min-w-[10px]"></div>
                      <div>{t.userName}</div>
                    </>
                  )}
                </div>
                <div
                  className={cn(
                    'rounded-xl text-xs py-3 px-4 max-w-[250px] mt-1',
                    {
                      'bg-gray-100 rounded-tl-md': role === 'bot',
                      'bg-blue-500 text-white rounded-br-md': role === 'user',
                    },
                  )}
                >
                  {t.message}
                </div>
              </div>

              {role === 'user' && (
                <Avatar className="w-8 h-8">
                  <AvatarImage src={avatar?.url ? avatar.url : ''} />
                  <AvatarFallback className="text-sm">
                    {avatar?.initials}-{avatar?.url}
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          );
        })}
    </div>
  );
}
