import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useCall } from '@/hooks/useRealCalls';
import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import LinksManager from '@/lib/linksManager';
import RealCall, {
  RealCallScoringStatus,
  RealCallStatus,
} from '@/lib/Integrations/RealCalls/types';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import ScorecardView from './scorecardView';
import Transcript, { TranscriptPartTyp } from './transcript';
import { CalendarClockIcon, Pencil } from 'lucide-react';
import dayjs from 'dayjs';
import { ScorecardDto } from '@/lib/Scorecard/types';
import { Button } from '@/components/ui/button';
import EditCallInfo from './editInfo';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';

interface IProps {
  callId: number;
}

export default function RealCallSummary({ callId }: IProps) {
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
    { title: 'Real Calls', href: LinksManager.realCalls() },
  ]);
  const {
    data: callDb,
    isLoading: isLoadingCallDetails,
    refetch: refetchCall,
    isRefetching: isRefetchingCallDetails,
  } = useCall(callId);
  const [call, setCall] = useState<RealCall>();
  const [caller, setCaller] = useState<any>();
  const [particpants, setParticpants] = useState<string>('');
  const [usersAvatars, setUsersAvatars] = useState<any>({});
  const [scorecard, setScorecard] = useState<ScorecardDto>();
  const [transcript, setTranscript] = useState<TranscriptPartTyp[]>([]);

  /********************************/
  /************* INIT *************/
  /********************************/

  useEffect(() => {
    return () => {
      stopRescoringMonitoring();
    };
  }, []);

  useEffect(() => {
    if (!isLoadingCallDetails && callDb && !isRefetchingCallDetails) {
      setCall(callDb);
      setScorecard(callDb.scorecard);
      setTranscript(callDb.transcript);
      setBreadcrumbs([
        { title: 'Real Calls', href: LinksManager.realCalls() },
        { title: callDb.title },
      ]);

      setCaller(callDb.caller);

      let _particpants = '';
      const _usersAvatars: any = {};

      callDb.parties?.map((p: any) => {
        if (_particpants == '') {
          _particpants = p.name;
        } else {
          _particpants += ', ' + p.name;
        }
        let avatar = '';
        if (p.user) {
          avatar = p.user.avatar;
        }
        _usersAvatars[p.name] = {
          url: avatar,
          initials: p.name?.charAt(0) + p.name?.charAt(1),
        };
      });

      if (callDb.status == RealCallStatus.SCORING) {
        startRescoringMonitoring();
      }

      setParticpants(_particpants);
      setUsersAvatars(_usersAvatars);
    }
  }, [isLoadingCallDetails, callDb, isRefetchingCallDetails]);

  /********************************/
  /*********** ACTIONS ************/
  /********************************/

  const [editCallOpen, setEditCallOpen] = useState<boolean>(false);
  const startEditCallInfos = () => {
    setEditCallOpen(true);
  };

  //------- monitor rescoring progress

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const startRescoringMonitoring = () => {
    timeoutRef.current = setTimeout(async () => {
      if (refetchCall) {
        refetchCall();
      }
    }, 5000);
  };

  const stopRescoringMonitoring = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  /********************************/
  /*********** RENDER *************/
  /********************************/

  const containerPanel = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    function updateSize() {
      if (containerPanel.current) {
        const rect = containerPanel.current.getBoundingClientRect();
        const h = window.innerHeight - rect.top;
        containerPanel.current.style.height = `${h}px`;
      }
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return (
    <div ref={containerPanel} className="overflow-hidden">
      <DashboardNavbar
        breadcrumbs={breadcrumbs}
        subContent={
          <div className="text-muted-foreground">
            {isLoadingCallDetails ? (
              <div className="flex flex-col md:flex-row space-x-0 space-y-4 md:space-x-4 md:space-y-0 mt-1">
                <Skeleton className="w-32 h-6" />
                <Skeleton className="w-40 h-6" />
                <Skeleton className="w-16 h-6" />
                <Skeleton className="w-16 h-6" />
                <Skeleton className="w-32 h-6" />
              </div>
            ) : (
              <div className="flex flex-col md:flex-row space-x-0 space-y-4 md:space-x-4 md:space-y-0 mt-2">
                <div className="flex items-center space-x-2">
                  <CalendarClockIcon className="w-4 h-4" />
                  <p className="text-sm">
                    {dayjs(call?.createdAt).format('YYYY-MM-DD')}
                  </p>
                </div>

                {caller && (
                  <div className="flex items-center space-x-2">
                    <Avatar className="w-6 h-6">
                      {caller?.avatar && <AvatarImage src={caller?.avatar} />}
                      <AvatarFallback className="text-sm capitalize">
                        {caller?.firstName?.charAt(0) || ''}
                        {caller?.lastName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>
                    <p className="text-sm max-w-[240px] truncate">
                      {caller?.firstName || ''} {caller?.lastName || ''}
                    </p>
                  </div>
                )}

                {particpants && (
                  <div
                    className="flex items-center max-w-[300px] truncate"
                    title={particpants}
                  >
                    with: {particpants}
                  </div>
                )}
              </div>
            )}
          </div>
        }
        rightContent={
          <div>
            <Button variant={'outline'} onClick={startEditCallInfos}>
              <Pencil size={16} className="mr-2" />
              Edit Info
            </Button>
          </div>
        }
      />
      <div className="flex items-stretch">
        <ScrollablePage className="flex-1 p-4 h-full overflow-auto">
          <ScorecardView
            scorecard={scorecard}
            isLoading={isLoadingCallDetails}
            callerId={caller?.id}
            callId={callId}
            callStatus={call?.status || RealCallStatus.SCORED}
            scoringStatus={
              call?.scoringStatus || RealCallScoringStatus.SCORED_SUCCESS
            }
            openRescoringPanel={startEditCallInfos}
          />
        </ScrollablePage>
        <ScrollablePage className="p-4 border-l overflow-auto">
          <Transcript transcript={transcript} usersAvatars={usersAvatars} />
        </ScrollablePage>
      </div>
      {call && editCallOpen && (
        <EditCallInfo
          call={call}
          open={editCallOpen}
          onClose={() => {
            setEditCallOpen(false);
          }}
        />
      )}
    </div>
  );
}
