import { ScorecardDto } from '@/lib/Scorecard/types';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { useState } from 'react';
import useCallScoreInfo from '@/hooks/useCallScoreInfo';
import { cn } from '@/lib/utils';
import {
  AlignLeft,
  Loader2Icon,
  Lock,
  RefreshCcw,
  Sparkles,
  Target,
} from 'lucide-react';
import ScorecardTab from './scorecard';
import QuestionsAndObjectionsTab from './questionsAndObjections';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  RealCallScoringStatus,
  RealCallStatus,
} from '@/lib/Integrations/RealCalls/types';
import { RealCallsService } from '@/lib/Integrations';
import { Button } from '@/components/ui/button';
import RealCallCoaching from './coaching';

interface IProps {
  scorecard?: ScorecardDto;
  isLoading: boolean;
  callerId: number;
  callId: number;
  callStatus: RealCallStatus;
  scoringStatus: RealCallScoringStatus;
  openRescoringPanel: () => void;
}

export default function ScorecardView({
  isLoading,
  scorecard,
  callerId,
  callId,
  callStatus,
  scoringStatus,
  openRescoringPanel,
}: IProps) {
  // console.log(scorecard);

  const [currentTab, setCurrentTab] = useState('scorecard');
  const onTabChange = (tabValue: string, deleteHighlightCriteria = true) => {
    setCurrentTab(tabValue);
    if (deleteHighlightCriteria) {
      setHighlightCriteria('');
    }
  };

  let ts = 0;
  let ps = 0;
  if (scorecard && scorecard.totalScore) {
    ts = scorecard.totalScore;
  }
  if (scorecard && scorecard.passedScore) {
    ps = scorecard.passedScore;
  }
  const { callScoreTitle, callScore, callScoreColor } = useCallScoreInfo({
    scorecards: [],
    totalScore: ts,
    passedScore: ps,
    aggregateScore: 0,
  });

  /*****************************/
  /********* ACTIONS ***********/
  /*****************************/

  const [highlightCriteria, setHighlightCriteria] = useState<string>('');

  const goToCoaching = (criteria: string) => {
    setHighlightCriteria(criteria);
    onTabChange('coaching', false);
  };

  let isRescoring = false;
  if (callStatus == RealCallStatus.SCORING) {
    isRescoring = true;
  }

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  if (scoringStatus === RealCallScoringStatus.SCORED_ERROR) {
    return (
      <div className="h-[300px] flex flex-col items-center justify-center">
        <div></div>
        <div className="font-semibold text-base">
          We could not score this call.
        </div>
        <div className="text-muted-foreground">
          Try rescoring the call or contact us support for support.
        </div>
        <div className="mt-4">
          <Button onClick={openRescoringPanel} disabled={isRescoring}>
            {isRescoring ? (
              <>
                <Loader2Icon size={16} className="mr-2 animate-spin" />
                Scoring
              </>
            ) : (
              <>
                <RefreshCcw size={16} className="mr-2" />
                Rescore
              </>
            )}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Tabs defaultValue={currentTab} value={currentTab} className="w-full">
      <div className="flex items-center">
        <TabsList>
          <TabsTrigger
            value="scorecard"
            onClick={() => onTabChange('scorecard')}
          >
            <Target size={12} className="mr-1" />
            Scorecard
          </TabsTrigger>
          <TabsTrigger value="coaching" onClick={() => onTabChange('coaching')}>
            <Sparkles size={12} className="mr-1" />
            AI Coaching
          </TabsTrigger>
          <TabsTrigger
            value="objections"
            onClick={() => onTabChange('objections')}
          >
            <AlignLeft size={12} className="mr-1" />
            Objections
          </TabsTrigger>
        </TabsList>
        <div className="flex-1" />
        <div className="flex items-center ml-2">
          <div className="text-xs mr-2">{callScoreTitle}</div>
          <div
            className={cn(
              'min-h-[48px] min-w-[48px] rounded-full flex items-center justify-center text-white font-semibold text-lg',
            )}
            style={{ backgroundColor: callScoreColor }}
          >
            {callScore == 'NaN' ? '0' : callScore}
          </div>
        </div>
      </div>

      <TabsContent value="scorecard">
        <ScorecardTab
          scorecard={scorecard}
          isLoading={isLoading}
          callerId={callerId}
          callId={callId}
          goToCoaching={goToCoaching}
          callStatus={callStatus}
        />
      </TabsContent>
      <TabsContent value="coaching">
        <RealCallCoaching
          callId={callId}
          openCriterion={highlightCriteria}
          scorecardConfigId={scorecard?.scorecardConfigId || 0}
        />
      </TabsContent>
      <TabsContent value="objections">
        <QuestionsAndObjectionsTab
          scorecard={scorecard}
          isLoading={isLoading}
        />
      </TabsContent>
    </Tabs>
  );
}
