import CallStats from '@/common/Calls/AIRoleplay/Summary/tabs/scorecard/stats';
import { ScorecardDto } from '@/lib/Scorecard/types';
import { AnimatePresence } from 'framer-motion';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Loader2Icon, Lock, RefreshCcw } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import ScorecardSection from '@/common/Calls/AIRoleplay/Summary/tabs/scorecard/scorecardSection';
import { Button } from '@/components/ui/button';
import { RealCallStatus } from '@/lib/Integrations/RealCalls/types';
import { RealCallsService } from '@/lib/Integrations';
import { useQueryClient } from '@tanstack/react-query';

interface IProps {
  scorecard?: ScorecardDto;
  isLoading: boolean;
  callerId: number;
  callId: number;
  goToCoaching: (criterion: string) => void;
  callStatus: RealCallStatus;
}

export default function ScorecardTab({
  isLoading,
  scorecard,
  callerId,
  callId,
  goToCoaching,
  callStatus,
}: IProps) {
  const queryClient = useQueryClient();
  const [currentTab, setCurrentTab] = useState<string>('ai');

  const [isRescoring, setIsRescoring] = useState<boolean>(false);

  useEffect(() => {
    if (callStatus == RealCallStatus.SCORING) {
      setIsRescoring(true);
    } else {
      setIsRescoring(false);
    }
  }, [callStatus]);

  const recommendedRanges = {
    talkListenRatio: [0.2, 0.4],
    fillerWords: [0.6, 3],
    talkSpeed: [110, 160],
    longestMonologue: [60, 150],
  };

  const stats = {
    talkListenRatio: {
      value: 0,
      recommendedRange: recommendedRanges.talkListenRatio,
    },
    fillerWords: {
      value: 0,
      recommendedRange: recommendedRanges.fillerWords,
    },
    talkSpeed: {
      value: 0,
      recommendedRange: recommendedRanges.talkSpeed,
    },
    longestMonologue: {
      value: 0,
      recommendedRange: recommendedRanges.longestMonologue,
    },
  };

  if (scorecard) {
    stats.talkListenRatio.value = scorecard.talkListenRatio;
    stats.fillerWords.value = scorecard.fillerWords;
    stats.talkSpeed.value = scorecard.talkSpeed;
    stats.longestMonologue.value = scorecard.longestMonologue;
  }

  /*****************************/
  /********* ACTIONS ***********/
  /*****************************/

  const onTabChange = (t: string) => {
    setCurrentTab(t);
  };

  const startRescoreCall = async () => {
    if (callId) {
      setIsRescoring(true);
      let ok = true;
      try {
        //do not wait
        RealCallsService.rescoreCall(callId);
      } catch (err) {
        ok = false;
        console.log(err);
      }

      if (ok) {
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ['real-call-details', callId],
          });
        }, 1000);
      }
    }
  };

  /*****************************/
  /********* RENDER ************/
  /*****************************/

  return (
    <AnimatePresence>
      <div className="mt-6">
        <CallStats isLoading={isLoading} stats={stats} />
      </div>

      <Tabs defaultValue={currentTab} value={currentTab} className="w-full">
        <div className="flex items-center mt-6">
          <div className="flex-1">
            <TabsList>
              <TabsTrigger value="ai" onClick={() => onTabChange('ai')}>
                AI Review
              </TabsTrigger>

              <TabsTrigger value="dummy">
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild className="opacity-[0.5]">
                      <div className="flex items-center">
                        Manual Review <Lock size={16} className="ml-2" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      Not enabled for your organization.
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TabsTrigger>
            </TabsList>
          </div>
          <div className="flex items-center">
            <Button
              variant={'ghost'}
              onClick={startRescoreCall}
              disabled={isRescoring}
            >
              {isRescoring ? (
                <>
                  <Loader2Icon size={16} className="mr-2 animate-spin" />
                  Scoring
                </>
              ) : (
                <>
                  <RefreshCcw size={16} className="mr-2" />
                  Rescore
                </>
              )}
            </Button>
          </div>
        </div>

        <TabsContent value="ai">
          <div className="pt-4 pb-6">
            {isLoading ? (
              <div className="grid grid-cols-2 items-stretch gap-4 pb-4 md:grid-cols-2 w-full">
                <Skeleton className="w-full h-[250px] rounded-xl" />
                <Skeleton className="w-full h-[250px] rounded-xl" />
                <Skeleton className="w-full h-[250px] rounded-xl" />
                <Skeleton className="w-full h-[250px] rounded-xl" />
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                {scorecard?.criteria.map((sc, i) => {
                  return (
                    <ScorecardSection
                      key={i}
                      delayBy={i}
                      section={sc}
                      callerId={callerId}
                      callId={String(callId)}
                      goToCoaching={goToCoaching}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </AnimatePresence>
  );
}
