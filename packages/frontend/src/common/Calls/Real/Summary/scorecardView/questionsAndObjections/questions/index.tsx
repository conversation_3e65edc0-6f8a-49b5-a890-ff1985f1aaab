import { ScorecardDto } from '@/lib/Scorecard/types';
import { AnimatePresence, motion } from 'framer-motion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoIcon, User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn, formatDuration } from '@/lib/utils';

interface IProps {
  scorecard: ScorecardDto;
}

export default function Questions({ scorecard }: IProps) {
  return (
    <AnimatePresence>
      <div className="mt-4">
        {(scorecard.questions || [])?.length > 0 ? (
          <>
            <h3 className="text-base">Rep asked...</h3>
            <div className="mt-4 space-y-2">
              {scorecard.questions?.map((question, i: number) => {
                return (
                  <motion.div
                    key={question.question}
                    initial={{ opacity: 0, y: -50 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ y: -50 }}
                    transition={{
                      duration: 0.4,
                      delay: i * 0.1,
                    }}
                  >
                    <Alert>
                      <AlertDescription className="flex items-center space-x-2">
                        <User className="w-4 h-4 text-muted-foreground" />
                        <Badge variant={'secondary'}>
                          {formatDuration(question.secondsFromStart * 1000)}
                        </Badge>
                        <p>{question.question}</p>
                      </AlertDescription>
                    </Alert>
                  </motion.div>
                );
              })}
            </div>
          </>
        ) : (
          <h3 className="text-muted-foreground">No questions identified</h3>
        )}
      </div>
    </AnimatePresence>
  );
}
