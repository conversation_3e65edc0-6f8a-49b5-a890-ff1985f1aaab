import { Skeleton } from '@/components/ui/skeleton';
import { ScorecardDto } from '@/lib/Scorecard/types';
import { AnimatePresence, motion } from 'framer-motion';
import Objections from './objections';
import Questions from './questions';

interface IProps {
  scorecard?: ScorecardDto;
  isLoading: boolean;
  className?: string;
}

export default function QuestionsAndObjectionsTab({
  className,
  scorecard,
  isLoading,
}: IProps) {
  return (
    <AnimatePresence>
      <div className={className}>
        {/********************************/}
        {/********* OBJECTIONS ***********/}
        {/********************************/}
        <div className="pt-2">
          <h3 className="text-normal font-semibold">
            Buyer objections and rep responses...
          </h3>

          {isLoading ? (
            <div className="mt-2">
              <Skeleton className="w-full h-[40px] mb-2" />
              <Skeleton className="w-full h-[40px] mb-2" />
              <Skeleton className="w-full h-[40px] mb-2" />
            </div>
          ) : (
            scorecard && <Objections scorecard={scorecard} />
          )}
        </div>

        {/********************************/}
        {/********* QUESTIONS ************/}
        {/********************************/}
        <div className="pt-8 mb-10">
          <h3 className="text-normal font-semibold">Rep asked...</h3>

          {isLoading ? (
            <div className="mt-2">
              <Skeleton className="w-full h-[40px] mb-2" />
              <Skeleton className="w-full h-[40px] mb-2" />
              <Skeleton className="w-full h-[40px] mb-2" />
            </div>
          ) : (
            scorecard && <Questions scorecard={scorecard} />
          )}
        </div>
      </div>
    </AnimatePresence>
  );
}
