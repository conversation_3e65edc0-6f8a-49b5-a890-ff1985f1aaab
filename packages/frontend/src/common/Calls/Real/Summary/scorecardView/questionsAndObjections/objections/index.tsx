import { ScorecardDto } from '@/lib/Scorecard/types';
import { AnimatePresence, motion } from 'framer-motion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoIcon, User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn, formatDuration } from '@/lib/utils';

interface IProps {
  scorecard: ScorecardDto;
}

export default function Objections({ scorecard }: IProps) {
  return (
    <div className="mt-4">
      {(scorecard.objections || [])?.length > 0 ? (
        <>
          <h3 className="text-base">Buyer objections and rep responses...</h3>
          <div className="mt-4 space-y-8">
            {scorecard.objections?.map((objection, i: number) => {
              return (
                <div key={objection.objection}>
                  <motion.div
                    initial={{ opacity: 0, y: -50 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ y: -50 }}
                    transition={{
                      duration: 0.4,
                      delay: i * 0.1,
                    }}
                  >
                    <Alert>
                      <AlertDescription className="flex items-center space-x-2">
                        <User className="w-4 h-4 text-muted-foreground" />
                        <Badge variant={'secondary'}>
                          {formatDuration(objection.secondsFromStart * 1000)}
                        </Badge>
                        <p>{objection.objection}</p>
                      </AlertDescription>
                    </Alert>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: -50 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ y: -50 }}
                    transition={{
                      duration: 0.4,
                      delay: i * 0.2,
                    }}
                  >
                    <Alert className="mt-2 ml-10">
                      <AlertDescription className="flex items-center space-x-2">
                        <User className="w-4 h-4 text-muted-foreground" />
                        <Badge variant={'secondary'}>
                          {formatDuration(objection.secondsFromStart * 1000)}
                        </Badge>
                        <p>{objection.response}</p>
                      </AlertDescription>
                    </Alert>
                  </motion.div>
                </div>
              );
            })}
          </div>
        </>
      ) : (
        <h3 className="text-muted-foreground">No objections identified</h3>
      )}
    </div>
  );
}
