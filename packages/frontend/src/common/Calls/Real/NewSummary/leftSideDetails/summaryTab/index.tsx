import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import useUserSession from '@/hooks/useUserSession';
import {
  RealCallsActionItemsDto,
  RealCallScoringStatus,
  RealCallsPublicPartiesDto,
} from '@/lib/Integrations/RealCalls/types';
import LinksManager from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import {
  Check,
  CopyCheckIcon,
  CopyIcon,
  SparklesIcon,
  Trash2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useMemo, useRef, useState } from 'react';
import Markdown from 'react-markdown';
import { Id, toast, ToastContainer } from 'react-toastify';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

export const RealCallNewSummarySummaryTab = (props: {
  isPublicVersion?: boolean;
  parties: RealCallsPublicPartiesDto[];
  actionItems: RealCallsActionItemsDto[];
  onActionItemDelete: (actionItemId: number) => Promise<unknown>;
  onActionItemToggle: (
    actionItemId: number,
    isCompleted: boolean,
  ) => Promise<unknown>;
  summary: string;
  notes: string;
  scoringStatus: RealCallScoringStatus;
}) => {
  const { isLoggedIn } = useUserSession();
  const router = useRouter();
  const toastId = useRef<Id | null>(null);
  const partyIdToParty = useMemo(() => {
    return Object.fromEntries(props.parties.map((p) => [p.id, p]));
  }, [props.parties]);

  const actionItemsSorted = useMemo(() => {
    return [...props.actionItems].sort((a1, a2) => a1.id - a2.id);
  }, [props.actionItems]);

  const [actionItemIdBeingDeleted, setActionItemIdBeingDeleted] = useState<
    number | null
  >(null);
  const [actionItemIdBeingToggled, setActionItemIdBeingToggled] = useState<
    number | null
  >(null);
  const [actionItemIdBeingHovered, setActionItemIdBeingHovered] = useState<
    number | null
  >(null);

  const [copiedState, setCopiedState] = useState<{
    'AI summary': boolean;
    notes: boolean;
  }>({ 'AI summary': false, notes: false });

  const onActionItemToggleInternal = async (
    actionItemId: number,
    isCompleted: boolean,
  ) => {
    if (
      !actionItemIdBeingToggled &&
      !actionItemIdBeingDeleted &&
      !props.isPublicVersion
    ) {
      setActionItemIdBeingToggled(actionItemId);
      await props.onActionItemToggle(actionItemId, isCompleted);
      setActionItemIdBeingToggled(null);
    }
  };

  const onActionItemDelete = async (actionItemId: number) => {
    if (
      !actionItemIdBeingDeleted &&
      !actionItemIdBeingToggled &&
      !props.isPublicVersion
    ) {
      setActionItemIdBeingDeleted(actionItemId);
      await props.onActionItemDelete(actionItemId);
      setActionItemIdBeingDeleted(null);
    }
  };

  const titleWithPoweredBy = (
    title: string,
    copyText?: string,
    className?: string,
    type?: 'AI summary' | 'notes',
  ) => {
    return (
      <div
        className={cn(
          `flex flex-row justify-between items-center w-full relative`,
          className,
        )}
      >
        <div className="flex flex-row items-center">
          <p className="font-medium">{title}</p>
          <div className="h-[10px] w-[2.5px] bg-[#E4E3E7] rounded-full mx-3 self-center" />
          <span className="text-muted-foreground text-xs">powered by</span>
          <span className="flex flex-row items-center space-x-1">
            <SparklesIcon className="w-3 h-3 ml-1 text-teal-500" />
            <span className="text-teal-500 text-xs">Hyperbound AI</span>
          </span>
        </div>
        {!!copyText && (
          <Button
            variant="ghost"
            size={'icon'}
            className="w-6 h-6 flex flex-row justify-center items-center cursor-pointer pointer-events-auto"
            onClick={(e) => {
              e.stopPropagation();
              navigator.clipboard.writeText(copyText);
              if (type) {
                if (!toast.isActive(toastId.current as Id)) {
                  toastId.current = toast.success(`Copied ${type}!`, {
                    className: 'text-primary',
                  });
                } else {
                  toast.dismiss(toastId.current as Id);
                  toastId.current = toast.success(`Copied ${type}!`, {
                    className: 'text-primary',
                  });
                }
                setCopiedState((prevState) => ({
                  ...prevState,
                  [type]: true,
                }));
                setTimeout(() => {
                  setCopiedState((prevState) => ({
                    ...prevState,
                    [type]: false,
                  }));
                }, 2000);
              }
            }}
          >
            {copiedState[type || 'AI summary'] ? (
              <CopyCheckIcon className="w-4 h-4 text-green-600" />
            ) : (
              <CopyIcon className="w-4 h-4" />
            )}
          </Button>
        )}
      </div>
    );
  };

  const completedActionItemsCount = actionItemsSorted.filter(
    (a) => a.isCompleted,
  ).length;
  const totalActionItemsCount = actionItemsSorted.length;

  return (
    <Accordion
      type="multiple"
      defaultValue={['action-items', 'ai-summary', 'notes']}
      className="flex flex-col w-full"
    >
      <AccordionItem value="action-items">
        <AccordionTrigger className="hover:no-underline hover:bg-gray-50 px-4">
          {titleWithPoweredBy(
            `Action Items${
              totalActionItemsCount > 0
                ? ` (${completedActionItemsCount} / ${totalActionItemsCount} completed)`
                : ''
            }`,
          )}
        </AccordionTrigger>
        <AccordionContent
          className="flex flex-col space-y-2 px-4 mt-2"
          style={{
            background:
              'linear-gradient(180deg, rgba(54, 196, 191, 0.05) 0%, rgba(73, 200, 207, 0.05) 46.5%, rgba(255, 255, 255, 0.05) 100%)',
          }}
        >
          {!actionItemsSorted?.length && (
            <div className="bg-muted flex flex-col justify-center items-center h-[144px] rounded-xl">
              <p className="text-sm text-muted-foreground font-medium">
                {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'Save only call' : 'No action items'}
              </p>
              <p className="text-sm text-muted-foreground">
              {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'This call is configured to not be scored currently' : 'If you have any questions, <NAME_EMAIL>.'}
              </p>
            </div>
          )}
          {actionItemsSorted.map((a) => {
            const party = partyIdToParty[a.realCallPartyId];
            const avatar = party?.user?.avatar || '';
            const name = party?.user
              ? `${party.user.firstName} ${party.user.lastName}`
              : party?.name;
            const isCompleted =
              a.id === actionItemIdBeingToggled
                ? !a.isCompleted
                : a.isCompleted;
            return (
              <div
                key={a.id}
                className={cn(
                  'rounded-lg flex flex-row border p-3 cursor-default bg-[#FBFBFB] shadow-sm relative',
                  {
                    'bg-[#22C55D0D]': isCompleted,
                    'hover:bg-[#22C55D06]':
                      !isCompleted &&
                      !actionItemIdBeingToggled &&
                      !actionItemIdBeingDeleted,
                    'cursor-pointer':
                      !actionItemIdBeingToggled &&
                      !actionItemIdBeingDeleted &&
                      !props.isPublicVersion,
                  },
                )}
                onClick={() => {
                  onActionItemToggleInternal(a.id, !a.isCompleted);
                }}
                onMouseOver={() => {
                  setActionItemIdBeingHovered(a.id);
                }}
                onMouseOut={() => {
                  setActionItemIdBeingHovered(null);
                }}
              >
                {actionItemIdBeingHovered === a.id && (
                  <div className="absolute top-0 bottom-0 right-0 flex flex-col justify-center">
                    <div
                      className="p-4"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onActionItemDelete(a.id);
                      }}
                    >
                      <Trash2
                        className={cn('w-4 h-4 text-muted-foreground', {
                          ' hover:text-black':
                            !actionItemIdBeingToggled &&
                            !actionItemIdBeingDeleted &&
                            !props.isPublicVersion,
                        })}
                      />
                    </div>
                  </div>
                )}
                <div
                  className={cn(
                    'rounded-full w-4 h-4 flex flex-row items-center justify-center mt-0.5',
                    {
                      'bg-green-600': isCompleted,
                      'border bg-white': !isCompleted,
                    },
                  )}
                >
                  {isCompleted && <Check className="text-white w-4 h-4" />}
                </div>
                <div className="flex-1 flex flex-col pl-3 items-start">
                  <Markdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                    className={cn('prose text-sm', {
                      'line-through': isCompleted,
                    })}
                  >
                    {a.action}
                  </Markdown>
                  <Button
                    variant={'outline'}
                    onClick={(e) => {
                      e.stopPropagation();
                      const userId = party?.user?.id;
                      if (isLoggedIn && !!userId) {
                        router.push(LinksManager.members(String(userId)));
                      }
                    }}
                    className="mt-3 space-x-[6px] pl-1 border pr-3 h-7 rounded-full bg-white hover:bg-muted/80 hover:transition-all duration-300"
                  >
                    <Avatar className="w-5 h-5">
                      {avatar && <AvatarImage src={avatar} />}
                      <AvatarFallback className="text-sm capitalize">
                        {name?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="capitalize text-xs">{name}</span>
                  </Button>
                </div>
              </div>
            );
          })}
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="ai-summary" className="w-full">
        <AccordionTrigger className="hover:no-underline hover:bg-gray-50 px-4">
          {titleWithPoweredBy(
            'AI Summary',
            props.summary,
            undefined,
            'AI summary',
          )}
        </AccordionTrigger>
        <AccordionContent className="flex flex-col space-y-2 mt-2 px-4">
          {!props.summary ? (
            <div className="bg-muted flex flex-col justify-center items-center h-[144px] rounded-xl">
              <p className="text-sm text-muted-foreground font-medium">
              {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'Save only call' : ' No AI summary'}
               
              </p>
              <p className="text-sm text-muted-foreground">
              {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'This call is configured to not be scored currently' : ' If you have any questions, <NAME_EMAIL>.'}
                
              </p>
            </div>
          ) : (
            <Markdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              className="prose text-sm w-full"
            >
              {props.summary}
            </Markdown>
          )}
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="notes" className="border-b-0 w-full">
        <AccordionTrigger className="hover:no-underline hover:bg-gray-50 px-4 pointer-events-none no-chevron">
          {titleWithPoweredBy('Notes', props.notes, undefined, 'notes')}
        </AccordionTrigger>
        <AccordionContent className="flex flex-col space-y-2 mt-2 px-4 pointer-events-none">
          {!props.notes ? (
            <div className="bg-muted flex flex-col justify-center items-center h-[144px] rounded-xl">
              <p className="text-sm text-muted-foreground font-medium">
              {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'Save only call' : ' No notes to show'}
                
              </p>
              <p className="text-sm text-muted-foreground">
              {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'This call is configured to not be scored currently' 
                : 'If you have any questions, <NAME_EMAIL>.'}
                
              </p>
            </div>
          ) : (
            <Markdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              className="prose text-sm w-full"
            >
              {props.notes}
            </Markdown>
          )}
        </AccordionContent>
      </AccordionItem>
      <ToastContainer position="bottom-center" />
    </Accordion>
  );
};
