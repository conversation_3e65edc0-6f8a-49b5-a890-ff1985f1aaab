import { ITranscriptProp } from '.';

export const sortTranscript = (
  transcript: ITranscriptProp[],
  simCallFlag = false,
  duration = 0,
) => {
  const t = [...(transcript || [])]
    .sort((o1, o2) => o1.secondsFromStart - o2.secondsFromStart)
    .map((t) => ({
      ...t,
      endSecondsFromStart: t.secondsFromStart,
      messageL: t.message?.toLowerCase(),
    }))
    .filter((t) => t.messageL);
  for (let i = t.length - 2; i >= 0; i--) {
    t[i].endSecondsFromStart = t[i + 1].secondsFromStart;
  }
  // Handle the endSecondsFromStart for the last item
  if (simCallFlag && duration) {
    if (t[t.length - 1]) {
      t[t.length - 1].endSecondsFromStart = duration;
    }
  }
  return t;
};
