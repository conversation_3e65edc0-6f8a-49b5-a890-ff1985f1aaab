import CallStats from '@/common/Calls/AIRoleplay/Summary/tabs/scorecard/stats';
import { Button } from '@/components/ui/button';
import useCallScoreInfo from '@/hooks/useCallScoreInfo';
import {
  CalendarCheckIcon,
  LockIcon,
  SparklesIcon,
  Target,
} from 'lucide-react';
import Image from 'next/image';
import RealCallCoaching from '../../../Summary/scorecardView/coaching';
import { useEffect, useState } from 'react';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useQueryClient } from '@tanstack/react-query';
import { RealCallsService } from '@/lib/Integrations';
import useUserSession from '@/hooks/useUserSession';
import { useTooltip } from '@/hooks/ui/useTooltip';

export const RealCallNewSummaryScoringTab = (props: {
  callType: string;
  callId: number;
  scorecard: any;
  isPublicVersion?: boolean;
}) => {
  const { canRescoreCalls } = useUserSession();
  const [rescoringMessage, setRescoringMessage] = useState('');
  const [selectedScorecardId, setSelectedScorecardId] = useState(
    props.scorecard?.scorecardConfigId,
  );
  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);
  const [isLoadingScorecards, setIsLoadingScorecards] =
    useState<boolean>(false);
  const [isRescoring, setIsRescoring] = useState<boolean>(false);
  const fetchData = async () => {
    setIsLoadingScorecards(true);

    const scs = await ScorecardConfigService.getAllScorecardConfigsForOrg();

    setAllScorecards(scs);

    setIsLoadingScorecards(false);
  };

  useEffect(() => {
    fetchData();
  }, []);
  const { callScore, callScoreColor, callScoreTitle } = useCallScoreInfo(
    props.scorecard,
  );
  const recommendedRanges = {
    talkListenRatio: [0.2, 0.4],
    fillerWords: [0.6, 3],
    talkSpeed: [110, 160],
    longestMonologue: [60, 150],
  };

  const stats = {
    talkListenRatio: {
      value: 0,
      recommendedRange: recommendedRanges.talkListenRatio,
    },
    fillerWords: {
      value: 0,
      recommendedRange: recommendedRanges.fillerWords,
    },
    talkSpeed: {
      value: 0,
      recommendedRange: recommendedRanges.talkSpeed,
    },
    longestMonologue: {
      value: 0,
      recommendedRange: recommendedRanges.longestMonologue,
    },
  };

  if (props.scorecard) {
    stats.talkListenRatio.value = props.scorecard.talkListenRatio;
    stats.fillerWords.value = props.scorecard.fillerWords;
    stats.talkSpeed.value = props.scorecard.talkSpeed;
    stats.longestMonologue.value = props.scorecard.longestMonologue;
  }

  const queryClient = useQueryClient();

  useEffect(() => {
    if (!rescoringMessage) return;
    const timeout = setTimeout(() => {
      setRescoringMessage('');
    }, 5000);
    return () => clearTimeout(timeout);
  }, [rescoringMessage]);

  const rescoreCall = async () => {
    setIsRescoring(true);
    let ok = true;
    try {
      RealCallsService.rescoreCall(props.callId, selectedScorecardId);
    } catch (err) {
      ok = false;
      console.log(err);
    }

    if (ok) {
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: ['real-call-details', props.callId],
        });
        queryClient.invalidateQueries({
          queryKey: ['real-call-public-details', props.callId],
        });
        queryClient.invalidateQueries({
          queryKey: ['real-call-scorecard-new', props.callId],
        });
      }, 2000);
    }
    setIsRescoring(false);
    setRescoringMessage(
      'Your call is being rescored. Please check back in a few minutes.',
    );
  };

  return (
    <div className="mt-4 flex flex-col">
      {props.isPublicVersion && (
        <div className="mt-16 flex flex-col items-center">
          <LockIcon className="w-16 h-16 text-muted-foreground" />
          <p className="my-3 whitespace-pre-line text-center">
            Score your own calls, build roleplay bots{'\n'}and more with
            Hyperbound!
          </p>
          <Button
            size={'lg'}
            className="text-[15px]"
            variant={'secondary'}
            onClick={() => {
              window.open(
                'https://calendly.com/d/cppn-cqx-39h',
                '_blank',
                'noopener',
              );
            }}
          >
            <CalendarCheckIcon className="w-4 h-4 mr-2" />
            Book a demo!
          </Button>
        </div>
      )}
      {!props.isPublicVersion && (
        <div>
          <div className="flex flex-col">
            <div className="flex-1">
              <div className="px-4">
                <div className="border rounded-lg px-3 pt-4 pb-3 flex flex-col bg-[#FCFEFE]">
                  <p className="mb-3 font-medium">Scorecard selection</p>
                  <div className="flex flex-row">
                    {useTooltip(
                      <Select
                        onValueChange={(value: string) => {
                          setSelectedScorecardId(parseInt(value));
                        }}
                        value={String(selectedScorecardId)}
                        disabled={!canRescoreCalls}
                      >
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Choose a scorecard" />
                        </SelectTrigger>
                        <SelectContent>
                          {allScorecards && (
                            <>
                              {allScorecards.map((option) => {
                                return (
                                  <SelectItem
                                    key={option.id}
                                    value={String(option.id)}
                                  >
                                    <div className="flex flex-row items-center">
                                      <Target className="w-4 h-4 mr-2" />
                                      <p>{option.tag}</p>
                                    </div>
                                  </SelectItem>
                                );
                              })}
                            </>
                          )}
                        </SelectContent>
                      </Select>,
                      {
                        message: canRescoreCalls
                          ? ''
                          : 'Only admins can rescore calls in your organisation',
                        containerClassName: 'flex-1',
                      },
                    )}
                    {useTooltip(
                      <Button
                        variant="default"
                        className="ml-3"
                        disabled={isRescoring || !canRescoreCalls}
                        onClick={() => {
                          rescoreCall();
                        }}
                      >
                        Re-score
                      </Button>,
                      {
                        message: canRescoreCalls
                          ? ''
                          : 'Only admins can rescore calls in your organisation',
                      },
                    )}
                  </div>
                  {!!rescoringMessage && (
                    <p className="mt-2">{rescoringMessage}</p>
                  )}
                </div>
              </div>
            </div>
            <div className="relative flex flex-col">
              {parseInt(callScore) > 60 && (
                <img
                  src="/images/icons/score-bg.svg"
                  alt="Score bg"
                  className="left-0 right-0 top-0 bottom-0 absolute"
                />
              )}
              <div className="relative flex flex-row justify-center mt-2 z-[1]">
                <Image
                  src="/images/icons/score-frame.svg"
                  alt="Score Frame"
                  width={64}
                  height={64}
                />
                <div className="absolute left-0 right-0 top-0 bottom-0 flex items-center justify-center">
                  <p className="font-bold text-white text-2xl">
                    {callScore || 0}
                  </p>
                </div>
              </div>
              <p className="text-center mt-2 z-[1]">{callScoreTitle}</p>
            </div>
            <CallStats className="mx-4 mt-6" stats={stats} isLoading={false} />
            <div className="px-4">
              <RealCallCoaching
                callId={props.callId}
                scorecardConfigId={props.scorecard.scorecardConfigId}
                openCriterion=""
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
