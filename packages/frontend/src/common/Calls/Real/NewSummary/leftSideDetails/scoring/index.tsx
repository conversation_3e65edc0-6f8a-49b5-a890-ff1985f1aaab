import CallStats from '@/common/Calls/AIRoleplay/Summary/tabs/scorecard/stats';
import { Button } from '@/components/ui/button';
import useCallScoreInfo from '@/hooks/useCallScoreInfo';
import { CalendarCheckIcon, LockIcon, Target } from 'lucide-react';
import RealCallCoaching from '../../../Summary/scorecardView/coaching';
import { useEffect, useState } from 'react';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useQueryClient } from '@tanstack/react-query';
import { RealCallsService } from '@/lib/Integrations';
import useUserSession from '@/hooks/useUserSession';
import { useTooltip } from '@/hooks/ui/useTooltip';
import CallScoreFrame from './scoreFrame';
import { cn } from '@/lib/utils';
import { RealCallScoringStatus } from '@/lib/Integrations/RealCalls/types';

export const getLinearGradientColorList = (score: number | undefined) => {
  if (score === undefined) {
    return ['#F4F4F5', '#F4F4F5', '#F4F4F5'];
  } else if (score > 80) {
    return ['#3DC3E6', '#49C8CF', '#36C4BF'];
  } else if (score > 40) {
    return ['#D2A61E', '#A7820F', '#A7820F'];
  } else if (score >= 0) {
    return ['#9D1C1D', '#C92526', '#F4F4F5'];
  }
};

export const RealCallNewSummaryScoringTab = (props: {
  callType: string;
  callId: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  scorecard: any;
  scoringStatus: RealCallScoringStatus;
  isPublicVersion?: boolean;
}) => {
  const { canRescoreCalls } = useUserSession();
  const [rescoringMessage, setRescoringMessage] = useState('');
  const [selectedScorecardId, setSelectedScorecardId] = useState(
    props.scorecard?.scorecardConfigId,
  );
  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);
  // TODO: We should have a loading state for scorecards
  // const [isLoadingScorecards, setIsLoadingScorecards] =
  //   useState<boolean>(false);
  const [isRescoring, setIsRescoring] = useState<boolean>(false);
  const fetchData = async () => {
    // setIsLoadingScorecards(true);

    const scs = await ScorecardConfigService.getAllScorecardConfigsForOrg();

    setAllScorecards(scs);

    // setIsLoadingScorecards(false);
  };

  useEffect(() => {
    fetchData();
  }, []);
  const { callScore, callScoreTitle } = useCallScoreInfo(props.scorecard);
  const recommendedRanges = {
    talkListenRatio: [0.2, 0.4],
    fillerWords: [0.6, 3],
    talkSpeed: [110, 160],
    longestMonologue: [60, 150],
  };

  const stats = {
    talkListenRatio: {
      value: 0,
      recommendedRange: recommendedRanges.talkListenRatio,
    },
    fillerWords: {
      value: 0,
      recommendedRange: recommendedRanges.fillerWords,
    },
    talkSpeed: {
      value: 0,
      recommendedRange: recommendedRanges.talkSpeed,
    },
    longestMonologue: {
      value: 0,
      recommendedRange: recommendedRanges.longestMonologue,
    },
  };

  if (props.scorecard) {
    stats.talkListenRatio.value = props.scorecard.talkListenRatio;
    stats.fillerWords.value = props.scorecard.fillerWords;
    stats.talkSpeed.value = props.scorecard.talkSpeed;
    stats.longestMonologue.value = props.scorecard.longestMonologue;
  }

  const queryClient = useQueryClient();

  useEffect(() => {
    if (!rescoringMessage) return;
    const timeout = setTimeout(() => {
      setRescoringMessage('');
    }, 5000);
    return () => clearTimeout(timeout);
  }, [rescoringMessage]);

  const rescoreCall = async () => {
    setIsRescoring(true);
    let ok = true;
    try {
      RealCallsService.rescoreCall(props.callId, selectedScorecardId);
    } catch (err) {
      ok = false;
      console.log(err);
    }

    if (ok) {
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: ['real-call-details', props.callId],
        });
        queryClient.invalidateQueries({
          queryKey: ['real-call-public-details', props.callId],
        });
        queryClient.invalidateQueries({
          queryKey: ['real-call-scorecard-new', props.callId],
        });
      }, 2000);
    }
    setIsRescoring(false);
    setRescoringMessage(
      'Your call is being rescored. Please check back in a few minutes.',
    );
  };

  return (
    <div className="mt-4 flex flex-col">
      {props.isPublicVersion && (
        <div className="mt-16 flex flex-col items-center">
          <LockIcon className="w-16 h-16 text-muted-foreground" />
          <p className="my-3 whitespace-pre-line text-center">
            Score your own calls, build roleplay bots{'\n'}and more with
            Hyperbound!
          </p>
          <Button
            size={'lg'}
            className="text-[15px]"
            variant={'secondary'}
            onClick={() => {
              window.open(
                'https://forms.default.com/422737',
                '_blank',
                'noopener',
              );
            }}
          >
            <CalendarCheckIcon className="w-4 h-4 mr-2" />
            Book a demo!
          </Button>
        </div>
      )}
      {!props.isPublicVersion && (
        <div>
          <div className="flex flex-col">
            <div className="flex-1">
              <div className="px-4">
                {[
                  'SCORING_SKIPPED_DUE_TO_PRIVACY_RULE',
                  'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE',
                ].includes(props.scoringStatus) ? (
                  <div className="flex flex-col items-center justify-center mb-4 pt-4 pb-8">
                    <div className="relative">
                      <CallScoreFrame
                        width={64}
                        height={64}
                        gradientColorList={getLinearGradientColorList(
                          undefined,
                        )}
                      />
                      <div className="text-[#B8B8BC] absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center font-semibold text-[24px] leading-[100%]">
                        -
                      </div>
                    </div>
                    <div className="font-medium text-[12px] leading-[16px] text-[#71717A] mt-4">
                      This call is configured to not be scored
                    </div>
                    <div className="text-[12px] leading-[16px] text-[#71717A] mt-1">
                      This call was only saved. Select a scorecard below and
                      re-score to see coaching feedback
                    </div>
                  </div>
                ) : (
                  <></>
                )}
                <div className="border rounded-lg px-3 pt-4 pb-3 flex flex-col bg-[#FCFEFE]">
                  <p className="mb-3 font-medium">Scorecard selection</p>
                  <div className="flex flex-row">
                    {useTooltip(
                      <Select
                        onValueChange={(value: string) => {
                          setSelectedScorecardId(parseInt(value));
                        }}
                        value={String(selectedScorecardId)}
                        disabled={!canRescoreCalls}
                      >
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Choose a scorecard" />
                        </SelectTrigger>
                        <SelectContent>
                          {allScorecards && (
                            <>
                              {allScorecards.map((option) => {
                                return (
                                  <SelectItem
                                    key={option.id}
                                    value={String(option.id)}
                                  >
                                    <div className="flex flex-row items-center">
                                      <Target className="w-4 h-4 mr-2" />
                                      <p>{option.tag}</p>
                                    </div>
                                  </SelectItem>
                                );
                              })}
                            </>
                          )}
                        </SelectContent>
                      </Select>,
                      {
                        message: canRescoreCalls
                          ? ''
                          : 'Only admins can rescore calls in your organization',
                        containerClassName: 'flex-1',
                      },
                    )}
                    {useTooltip(
                      <Button
                        variant="default"
                        className="ml-3"
                        disabled={isRescoring || !canRescoreCalls}
                        onClick={() => {
                          rescoreCall();
                        }}
                      >
                        Re-score
                      </Button>,
                      {
                        message: canRescoreCalls
                          ? ''
                          : 'Only admins can rescore calls in your organization',
                      },
                    )}
                  </div>
                  {!!rescoringMessage && (
                    <p className="mt-2">{rescoringMessage}</p>
                  )}
                </div>
              </div>
            </div>
            <div className="relative flex flex-col">
              {parseInt(callScore) > 60 && (
                <img
                  src="/images/icons/score-bg.svg"
                  alt="Score bg"
                  className="left-0 right-0 top-0 bottom-0 absolute"
                />
              )}
              <div className="relative flex flex-row justify-center mt-2 z-[1]">
                <CallScoreFrame
                  width={64}
                  height={64}
                  gradientColorList={getLinearGradientColorList(
                    callScore === undefined ? undefined : parseInt(callScore),
                  )}
                />
                <div className="absolute left-0 right-0 top-0 bottom-0 flex items-center justify-center">
                  <p
                    className={cn('font-bold text-white text-2xl', {
                      'text-[#B8B8BC]': callScore === undefined,
                    })}
                  >
                    {callScore === undefined ? '-' : callScore}
                  </p>
                </div>
              </div>
              <p className="text-center mt-2 z-[1]">{callScoreTitle}</p>
            </div>
            <CallStats className="mx-4 mt-6" stats={stats} isLoading={false} />
            <div className="px-4">
              <RealCallCoaching callId={props.callId} openCriterion="" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
