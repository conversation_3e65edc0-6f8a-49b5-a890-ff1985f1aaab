import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { RealCallsPublicDetailsDto } from '@/lib/Integrations/RealCalls/types';
import { SparklesIcon } from 'lucide-react';
import { MutableRefObject, useState } from 'react';
import { RealCallNewSummarySummaryTab } from './summaryTab';
import { RealCallsService } from '@/lib/Integrations';
import { RealCallNewSummaryKeyPointsTab } from './keyPoints';
import { RealCallNewSummaryTranscriptTab } from './transcript';
import { RealCallNewSummaryScoringTab } from './scoring';
import { RealCallNewSummaryDetailsTab } from './details';
import { RealCallMediaControllerRefType } from '../RealCallMediaPlayer';

interface IProps {
  callId: number;
  mediaTime: number;
  mediaControllerRef?: MutableRefObject<RealCallMediaControllerRefType>;
  refetchPublicDetails: () => Promise<unknown>;
  publicDetailsDto: RealCallsPublicDetailsDto;
  className?: string;
  scorecard?: any;
  isPublicVersion?: boolean;
  pwd?: string;
}

export default function RealCallNewSummaryLeftSideDetails(props: IProps) {
  const [currentTab, setCurrentTab] = useState('summary');
  const onTabChange = (newTab: string) => {
    setCurrentTab(newTab);
  };

  const onActionItemToggle = async (
    actionItemId: number,
    isCompleted: boolean,
  ) => {
    await RealCallsService.updateActionItem(actionItemId, { isCompleted });
    await props.refetchPublicDetails();
  };

  const onActionItemDelete = async (actionItemId: number) => {
    await RealCallsService.deleteActionItem(actionItemId);
    await props.refetchPublicDetails();
  };

  return (
    <div
      className={`border bg-white rounded-md pt-3 h-full ${
        props.className || ''
      }`}
    >
      <Tabs
        className="flex flex-col h-full"
        defaultValue={currentTab}
        value={currentTab}
      >
        <div className="flex items-center w-full px-4 pb-2">
          <TabsList className="w-full">
            <TabsTrigger
              className="flex-1"
              value="summary"
              onClick={() => onTabChange('summary')}
            >
              Summary
            </TabsTrigger>
            <TabsTrigger
              className="flex-1"
              value="key-points"
              onClick={() => onTabChange('key-points')}
            >
              Key Points
            </TabsTrigger>
            {/* <TabsTrigger
              className="flex-1"
              value="transcript"
              onClick={() => onTabChange("transcript")}
            >
              Transcript
            </TabsTrigger> */}
            <TabsTrigger
              className="flex-1"
              value="scoring"
              onClick={() => onTabChange('scoring')}
            >
              Scoring
            </TabsTrigger>
            <TabsTrigger
              className="flex-1"
              value="details"
              onClick={() => onTabChange('details')}
            >
              Details
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 overflow-y-auto pb-3">
          <TabsContent value="summary">
            <RealCallNewSummarySummaryTab
              isPublicVersion={props.isPublicVersion}
              parties={props.publicDetailsDto.parties}
              actionItems={props.publicDetailsDto.actionItems}
              onActionItemToggle={onActionItemToggle}
              onActionItemDelete={onActionItemDelete}
              notes={props.publicDetailsDto.scorecard?.notes || ''}
              summary={props.publicDetailsDto.scorecard?.summary || ''}
            />
          </TabsContent>
          <TabsContent value="key-points">
            <RealCallNewSummaryKeyPointsTab
              decisionMakerMentions={
                props.publicDetailsDto.decisionMakerMentions
              }
              mediaTime={props.mediaTime}
              mediaControllerRef={props.mediaControllerRef}
              objections={props.publicDetailsDto.scorecard?.objections || []}
              questions={props.publicDetailsDto.scorecard?.questions || []}
            />
          </TabsContent>
          {/* <TabsContent value="transcript" className="h-full mt-0">
            <RealCallNewSummaryTranscriptTab
              parties={props.publicDetailsDto.parties}
              transcript={props.publicDetailsDto.transcript}
              mediaTime={props.mediaTime}
              mediaControllerRef={props.mediaControllerRef}
            />
          </TabsContent> */}
          <TabsContent value="scoring">
            <RealCallNewSummaryScoringTab
              callType={props.publicDetailsDto.callType}
              callId={props.callId}
              isPublicVersion={props.isPublicVersion}
              scorecard={props.scorecard}
            />
          </TabsContent>
          <TabsContent value="details">
            <RealCallNewSummaryDetailsTab
              publicDetailsDto={props.publicDetailsDto}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
