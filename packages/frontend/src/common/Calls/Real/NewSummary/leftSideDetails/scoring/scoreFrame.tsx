import * as React from 'react';
const CallScoreFrame = ({
  width,
  height,
  gradientColorList,
}: {
  width?: number;
  height?: number;
  gradientColorList?: string[] | undefined;
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width ? width : 64}
    height={height ? height : 70}
    fill="none"
    viewBox="0 0 64 70"
  >
    <path
      fill="url(#a)"
      stroke="#fff"
      strokeWidth={4}
      d="M54.713 13.804 39 4.732a14 14 0 0 0-14 0L9.287 13.804a14 14 0 0 0-7 12.124v18.144a14 14 0 0 0 7 12.124L25 65.268a14 14 0 0 0 14 0l15.713-9.072a14 14 0 0 0 7-12.124V25.928a14 14 0 0 0-7-12.124Z"
    />
    <path
      stroke="#000"
      strokeOpacity={0.2}
      strokeWidth={4}
      d="m37 8.196 15.713 9.072a10 10 0 0 1 5 8.66v18.144a10 10 0 0 1-5 8.66L37 61.804a10 10 0 0 1-10 0l-15.713-9.072a10 10 0 0 1-5-8.66V25.928a10 10 0 0 1 5-8.66L27 8.196a10 10 0 0 1 10 0Z"
    />
    <defs>
      <linearGradient
        id="a"
        x1={32}
        x2={32}
        y1={3}
        y2={67}
        gradientUnits="userSpaceOnUse"
      >
        <stop
          stopColor={
            gradientColorList && gradientColorList[0]
              ? gradientColorList[0]
              : '#3DC3E6'
          }
        />
        <stop
          offset={0.333}
          stopColor={
            gradientColorList && gradientColorList[1]
              ? gradientColorList[1]
              : '#49C8CF'
          }
        />
        <stop
          offset={0.99}
          stopColor={
            gradientColorList && gradientColorList[2]
              ? gradientColorList[2]
              : '#36C4BF'
          }
        />
      </linearGradient>
    </defs>
  </svg>
);
export default CallScoreFrame;
