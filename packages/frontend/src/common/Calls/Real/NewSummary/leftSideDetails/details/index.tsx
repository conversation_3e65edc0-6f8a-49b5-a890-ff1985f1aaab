import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import useUserSession from '@/hooks/useUserSession';
import IntegrationService from '@/lib/Integrations';
import { RealCallsPublicDetailsDto } from '@/lib/Integrations/RealCalls/types';
import LinksManager from '@/lib/linksManager';
import dayjs from 'dayjs';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export const RealCallNewSummaryDetailsTab = (props: {
  publicDetailsDto: RealCallsPublicDetailsDto;
}) => {
  const caller = props.publicDetailsDto.caller;
  const name = `${caller?.firstName || ''} ${caller?.lastName || ''}`.trim();
  const avatar = caller?.avatar;
  const minutes = Math.floor(props.publicDetailsDto.duration / 60);
  const seconds = Math.floor(props.publicDetailsDto.duration - 60 * minutes)
    .toString()
    .padStart(2, '0');
  const { isLoggedIn } = useUserSession();
  const router = useRouter();
  return (
    <div className="mt-4 px-4">
      <p className="text-muted-foreground">Host</p>
      <Button
        variant={'outline'}
        onClick={(e) => {
          e.stopPropagation();
          if (isLoggedIn) {
            router.push(LinksManager.members(String(caller?.id)));
          }
        }}
        className="mt-2 space-x-2 pl-1.5 pr-3 py-1 rounded-full bg-white hover:bg-muted/80 hover:transition-all duration-300"
      >
        <Avatar className="w-6 h-6">
          {avatar && <AvatarImage src={avatar} />}
          <AvatarFallback className="text-sm capitalize">
            {name?.charAt(0)}
          </AvatarFallback>
        </Avatar>
        <div className="capitalize">{name}</div>
      </Button>
      <p className="text-muted-foreground mt-4">Duration</p>
      <p className="mt-2">
        {minutes}:{seconds}
      </p>
      <p className="text-muted-foreground mt-4">Date</p>
      <p className="mt-2">
        {dayjs(props.publicDetailsDto.callDate).format(
          'hh:mm A, DD MMMM, YYYY Z',
        )}
      </p>
      <p className="text-muted-foreground mt-4">Integration</p>
      <div className="flex items-center">
        <Image
          src={IntegrationService.getProviderLogoUrl(
            props.publicDetailsDto.integration.provider.logoUrl,
          )}
          alt={`${props.publicDetailsDto.integration.provider.companyName} Logo`}
          width={20}
          height={20}
          className="mr-1"
        />
        <div>{props.publicDetailsDto.integration.provider.companyName}</div>
      </div>
    </div>
  );
};
