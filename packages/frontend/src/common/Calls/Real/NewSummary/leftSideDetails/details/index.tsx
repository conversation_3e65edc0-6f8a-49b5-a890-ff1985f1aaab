import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import useUserSession from '@/hooks/useUserSession';
import IntegrationService from '@/lib/Integrations';
import { RealCallsPublicDetailsDto } from '@/lib/Integrations/RealCalls/types';
import LinksManager from '@/lib/linksManager';
import dayjs from 'dayjs';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useState } from 'react';
import { Accordion, AccordionContent } from '@/components/ui/accordion';
import { AccordionItem } from '@/components/ui/accordion';
import AIBotCreatorFormAccordionTrigger from '@/common/CreateBuyerForm/Main/AIBotCreatorForm/AIBotCreatorFormAccordionTrigger';
import { API } from '@/lib/Client';

enum RealCallAdditionalInfoKey {
  NOTES = 'notes',
}

type FormValues = {
  [key in RealCallAdditionalInfoKey]?: string;
};

const formSchema = z.object({
  [RealCallAdditionalInfoKey.NOTES]: z.string().optional(),
});

export const RealCallNewSummaryDetailsTab = (props: {
  publicDetailsDto: RealCallsPublicDetailsDto;
}) => {
  const caller = props.publicDetailsDto.caller;
  const name = `${caller?.firstName || ''} ${caller?.lastName || ''}`.trim();
  const avatar = caller?.avatar;
  const minutes = Math.floor(props.publicDetailsDto.duration / 60);
  const seconds = Math.floor(props.publicDetailsDto.duration - 60 * minutes)
    .toString()
    .padStart(2, '0');
  const { isLoggedIn } = useUserSession();
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues:
      props.publicDetailsDto.additionalInfo?.reduce(
        (acc, info) => ({
          ...acc,
          [info.key]: info.value,
        }),
        {},
      ) || {},
  });

  const onSubmit = async (data: FormValues) => {
    try {
      setIsSaving(true);
      setError(null);
      await API.patch(
        `/integrations/real-calls/${props.publicDetailsDto.id}/additional-info`,
        (props.publicDetailsDto.additionalInfo || []).map((info) => ({
          id: info.id,
          key: info.key,
          value: data[info.key] || info.value,
        })),
      );
    } catch (error) {
      console.error('Error updating additional info:', error);
      setError('Failed to update additional information');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="mt-4 px-4">
      <p className="text-muted-foreground">Host</p>
      <Button
        variant={'outline'}
        onClick={(e) => {
          e.stopPropagation();
          if (isLoggedIn) {
            router.push(LinksManager.members(String(caller?.id)));
          }
        }}
        className="mt-2 space-x-2 pl-1.5 pr-3 py-1 rounded-full bg-white hover:bg-muted/80 hover:transition-all duration-300"
      >
        <Avatar className="w-6 h-6">
          {avatar && <AvatarImage src={avatar} />}
          <AvatarFallback className="text-sm capitalize">
            {name?.charAt(0)}
          </AvatarFallback>
        </Avatar>
        <div className="capitalize">{name}</div>
      </Button>
      <p className="text-muted-foreground mt-4">Duration</p>
      <p className="mt-2">
        {minutes}:{seconds}
      </p>
      <p className="text-muted-foreground mt-4">Date</p>
      <p className="mt-2">
        {dayjs(props.publicDetailsDto.callDate).format(
          'hh:mm A, DD MMMM, YYYY Z',
        )}
      </p>
      <p className="text-muted-foreground mt-4">Integration</p>
      <div className="flex items-center">
        <Image
          src={IntegrationService.getProviderLogoUrl(
            props.publicDetailsDto.integration.provider.logoUrl,
          )}
          alt={`${props.publicDetailsDto.integration.provider.companyName} Logo`}
          width={20}
          height={20}
          className="mr-1"
        />
        <div>{props.publicDetailsDto.integration.provider.companyName}</div>
      </div>
      {props.publicDetailsDto.additionalInfo &&
        props.publicDetailsDto.additionalInfo.length > 0 && (
          <Accordion
            type="single"
            collapsible
            className="-ml-2 mt-4"
            defaultValue="Additional Information"
          >
            <AccordionItem
              value="Additional Information"
              className="border border-none"
            >
              <AIBotCreatorFormAccordionTrigger
                heading="Additional Information"
                headingClassName="text-muted-foreground"
              />
              <AccordionContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)}>
                    <div className="flex flex-col space-y-4">
                      {props.publicDetailsDto.additionalInfo.map(
                        (info, idx) => (
                          <FormField
                            key={idx}
                            control={form.control}
                            name={info.key as RealCallAdditionalInfoKey}
                            render={({ field }) => (
                              <FormItem className="w-full">
                                <FormLabel className="text-[#2e3035] text-sm font-medium leading-tight">
                                  {info.key.charAt(0).toUpperCase() +
                                    info.key.slice(1)}
                                </FormLabel>
                                <FormControl>
                                  <Textarea
                                    {...field}
                                    className="p-2 bg-white rounded-lg border border-zinc-200 text-[#2e3035] font-normal text-sm leading-tight resize-none min-h-24"
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        ),
                      )}
                    </div>
                    {error && (
                      <p className="text-red-500 text-sm mt-2">{error}</p>
                    )}
                    <div className="mt-4 flex justify-end">
                      <Button
                        type="submit"
                        disabled={isSaving || !form.formState.isDirty}
                        className="bg-primary text-white hover:bg-primary/90"
                      >
                        {isSaving ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </div>
                  </form>
                </Form>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}
    </div>
  );
};
