import {
  RealCallPartyAffiliation,
  RealCallScoringStatus,
  RealCallsDecisionMakerDto,
} from '@/lib/Integrations/RealCalls/types';
import { cn } from '@/lib/utils';
import { InfoIcon } from 'lucide-react';
import { Fragment, MutableRefObject, useMemo } from 'react';
import { RealCallMediaControllerRefType } from '../../RealCallMediaPlayer';

export const RealCallNewSummaryKeyPointsTab = (props: {
  decisionMakerMentions: RealCallsDecisionMakerDto[];
  mediaTime: number;
  mediaControllerRef?: MutableRefObject<RealCallMediaControllerRefType>;
  objections: {
    objection: string;
    response: string;
    secondsFromStart: number;
  }[];
  questions: {
    question: string;
    secondsFromStart: number;
  }[];
  scoringStatus: RealCallScoringStatus;
}) => {
  // const sortedDecisionMakerMentions = useMemo(() => {
  //   return [...props.decisionMakerMentions].sort(
  //     (d1, d2) => d2.numTimesMentioned - d1.numTimesMentioned,
  //   );
  // }, [props.decisionMakerMentions]);

  const sortedObjections = useMemo(() => {
    return [...(props.objections || [])]
      .sort((o1, o2) => o1.secondsFromStart - o2.secondsFromStart)
      .flatMap((o) => {
        return [
          {
            secondsFromStart: o.secondsFromStart,
            message: o.objection,
            type: 'objection',
          },
          {
            secondsFromStart: o.secondsFromStart,
            message: o.response,
            type: 'response',
          },
        ] as const;
      });
  }, [props.objections]);

  const sortedQuestions = useMemo(() => {
    return [...(props.questions || [])].sort(
      (o1, o2) => o1.secondsFromStart - o2.secondsFromStart,
    );
  }, [props.questions]);

  const getMessageBubble = (
    message: string,
    affiliation: RealCallPartyAffiliation,
    secondsFromStart: number,
    onClick?: () => unknown,
  ) => {
    const isInternalParty = affiliation === RealCallPartyAffiliation.INTERNAL;
    const minutes = Math.floor(secondsFromStart / 60);
    const seconds = Math.floor(secondsFromStart - 60 * minutes)
      .toString()
      .padStart(2, '0');
    return (
      <div
        className={cn('flex flex-row max-w-[90%]', {
          'self-start mt-4': !isInternalParty,
          'self-end mt-2': isInternalParty,
          'cursor-pointer': !!onClick,
        })}
        onClick={() => {
          onClick?.();
        }}
      >
        <div className="rounded-xl bg-[#FBFBFB] hover:bg-[#F6FCFD] border flex flex-col pl-3 pr-4 py-2.5">
          <div className="flex flex-row">
            <div className={cn('w-[3px] py-1 rounded-full', {})}>
              <div
                className={cn('h-full w-full rounded-full', {
                  'bg-blue-500': !isInternalParty,
                  'bg-[#FF7F62]': isInternalParty,
                })}
              />
            </div>
            <span className="font-medium text-xs ml-2 flex items-center">
              {isInternalParty ? 'Rep' : 'Prospect'}
            </span>
            <div className="flex-1 min-w-4" />
            <span className="text-muted-foreground">
              {minutes}:{seconds}
            </span>
          </div>
          <p className="mt-1.5">{message}</p>
        </div>
      </div>
    );
  };

  return (
    <div className="mt-6 flex flex-col px-4">
      {/* <p className="font-medium">Decision Maker Mentions</p>
		<div className="mt-3 flex flex-row flex-wrap space-x-2">
			{
				sortedDecisionMakerMentions.map((dm, idx) => {
					return <div key={idx} className="rounded-lg px-1.5 border bg-[#FBFBFB]">
							<span>{dm.decisionMaker}</span>
							<span className="text-muted-foreground ml-2">{dm.numTimesMentioned}</span>
					</div>;
				})
			}
			{
				!sortedDecisionMakerMentions.length && <p>None found.</p>
			}
		</div> */}
      <div className="mb-2 flex flex-row justify-between">
        <p className="font-medium">Objections and responses</p>
        <p className="text-muted-foreground">
          <InfoIcon className="w-4 h-4 inline-flex mb-0.5 mr-1" /> Mistakes
          might be present. Will improve over time.
        </p>
      </div>
      <div className="flex flex-col">
        {!sortedObjections?.length && (
          <div className="bg-muted flex flex-col justify-center items-center h-[144px] rounded-xl">
            <p className="text-sm text-muted-foreground font-medium">
            {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'Save only call' : ' No objections to show'}
              
            </p>
            <p className="text-sm text-muted-foreground">
            {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'This call is configured to not be scored' 
                : 'If you have any questions, <NAME_EMAIL>.'}
            </p>
          </div>
        )}
        {sortedObjections.map((obj, idx) => {
          return (
            <Fragment key={idx}>
              {getMessageBubble(
                obj.message,
                obj.type === 'objection'
                  ? RealCallPartyAffiliation.EXTERNAL
                  : RealCallPartyAffiliation.INTERNAL,
                obj.secondsFromStart,
                () => {
                  const mediaController = props.mediaControllerRef?.current;
                  if (mediaController) {
                    mediaController.seekTo(obj.secondsFromStart);
                    mediaController.play();
                  }
                },
              )}
            </Fragment>
          );
        })}
      </div>
      <p className="font-medium mt-6 mb-2">Questions</p>
      <div className="flex flex-col">
        {!sortedQuestions?.length && (
          <div className="bg-muted flex flex-col justify-center items-center h-[144px] rounded-xl">
            <p className="text-sm text-muted-foreground font-medium">
              {
                ['SCORING_SKIPPED_DUE_TO_PRIVACY_RULE', 'SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE'].includes(
                  props.scoringStatus
                ) ? 'Save only call' : '  No questions to show'}
             
            </p>
            <p className="text-sm text-muted-foreground">
              If you have any concerns, <NAME_EMAIL>.
            </p>
          </div>
        )}
        {sortedQuestions.map((obj, idx) => {
          return (
            <Fragment key={idx}>
              {getMessageBubble(
                obj.question,
                RealCallPartyAffiliation.INTERNAL,
                obj.secondsFromStart,
                () => {
                  const mediaController = props.mediaControllerRef?.current;
                  if (mediaController) {
                    mediaController.seekTo(obj.secondsFromStart);
                    mediaController.play();
                  }
                },
              )}
            </Fragment>
          );
        })}
      </div>
    </div>
  );
};
