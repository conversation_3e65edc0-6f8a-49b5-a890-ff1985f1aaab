import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  RealCallPartyAffiliation,
  RealCallsPublicPartiesDto,
} from '@/lib/Integrations/RealCalls/types';
import { cn, escapeStrForRegex } from '@/lib/utils';
import { CopyCheckIcon, CopyIcon } from 'lucide-react';
import { ReactNode, useMemo, useState } from 'react';
import { getPartyColorForIdx } from '../../constants';

export interface IHighlightPhrase {
  citationNumber: number;
  highlightPhrase: string;
}
export interface IMessageBubble {
  parties: RealCallsPublicPartiesDto[];
  idx: number;
  message: string;
  messageL: string;
  party: RealCallsPublicPartiesDto;
  secondsFromStart: number;
  role: 'bot' | 'user';
  nameFromTranscript: string;
  onClick?: () => unknown;
  cursorRef: React.RefObject<HTMLDivElement | null>;
  canPlayMedia: boolean;
  speakingTranscriptIdx: number;
  speakingTranscriptRef: React.RefObject<HTMLDivElement | null>;
  searchTerm: string;
  searchMatches: number[];
  searchCursor: number;
  highlightPhrases?: IHighlightPhrase[];
  simCallFlag: boolean | undefined;
}
export function MessageBubble({
  parties,
  idx,
  message,
  messageL,
  party,
  secondsFromStart,
  role,
  nameFromTranscript,
  onClick,
  cursorRef,
  canPlayMedia,
  speakingTranscriptIdx,
  speakingTranscriptRef,
  searchTerm,
  searchMatches,
  searchCursor,
  highlightPhrases,
  simCallFlag,
}: IMessageBubble) {
  const [hover, setHover] = useState(false);
  const [copied, setCopied] = useState(false);
  const partyIdToIdx = useMemo(() => {
    return Object.fromEntries(parties.map((p, idx) => [p.id, idx]));
  }, [parties]);
  const searchTermL = useMemo(
    () => searchTerm.toLowerCase().trim(),
    [searchTerm],
  );
  const isInternalParty = party
    ? party.affiliation === RealCallPartyAffiliation.INTERNAL
    : role === 'user';
  const minutes = Math.floor(secondsFromStart / 60);
  const seconds = Math.floor(secondsFromStart - 60 * minutes)
    .toString()
    .padStart(2, '0');
  const avatar = party?.user?.avatar || '';
  const name = party?.user
    ? `${party.user.firstName} ${party.user.lastName}`
    : party?.name || nameFromTranscript;
  const isCursor =
    searchMatches.length > 0 && searchMatches[searchCursor] === idx;
  const messageNode = useMemo(() => {
    if (searchMatches.length > 0 && messageL.includes(searchTermL)) {
      const matchedIndices = new Set(
        Array.from(
          messageL.matchAll(new RegExp(escapeStrForRegex(searchTermL), 'gi')),
        ).map((a) => a.index),
      );
      const newMessageNode: ReactNode[] = [];
      let i = 0;
      while (i < message.length) {
        if (matchedIndices.has(i)) {
          newMessageNode.push(
            <span
              className={cn('rounded-sm', {
                'bg-[#FFD35F] rounded-[4px] px-[3px]': isCursor,
                'bg-[#FFEAB3] rounded-[4px] px-[3px]': !isCursor,
              })}
            >
              {message.substring(i, i + searchTerm.length)}
            </span>,
          );
          i += searchTerm.length;
        } else {
          newMessageNode.push(message[i]);
          i++;
        }
      }
      return newMessageNode;
    } else if (highlightPhrases && highlightPhrases.length > 0) {
      const newMessageNode: ReactNode[] = [];
      const matches: { start: number; end: number; citationNumber: number }[] =
        [];

      for (const phrase of highlightPhrases) {
        const index = message.indexOf(phrase.highlightPhrase);
        if (index !== -1) {
          matches.push({
            start: index,
            end: index + phrase.highlightPhrase.length,
            citationNumber: phrase.citationNumber,
          });
        }
      }

      matches.sort((a, b) => a.start - b.start);

      // Remove overlapping matches
      const nonOverlapping: typeof matches = [];
      let lastEnd = 0;
      for (const match of matches) {
        if (match.start >= lastEnd) {
          nonOverlapping.push(match);
          lastEnd = match.end;
        }
      }

      let cursor = 0;
      for (const match of nonOverlapping) {
        if (cursor < match.start) {
          newMessageNode.push(message.slice(cursor, match.start));
        }

        newMessageNode.push(
          <span className="bg-[#1A829D] rounded-[4px] text-[#FFFFFF] px-[3px] ">
            <span className="bg-[white] text-[#1A829D] rounded-[3px] leading-[100%] h-3 w-3 align-middle px-[3px] mr-1 font-bold text-[10px] inline-flex items-center justify-center align-middle relative top-[-1px]">
              {match.citationNumber}
            </span>
            {message.slice(match.start, match.end)}
          </span>,
        );
        cursor = match.end;
      }
      if (cursor < message.length) {
        newMessageNode.push(message.slice(cursor));
      }
      return newMessageNode;
    } else {
      return message;
    }
  }, [
    idx,
    secondsFromStart,
    message,
    messageL,
    isCursor,
    searchMatches,
    searchTerm,
    highlightPhrases,
  ]);
  const onCopyClick = async (msg: string, idx: number) => {
    try {
      // Use navigator.clipboard.writeText to copy text to clipboard

      await navigator.clipboard.writeText(msg);

      // Update state to indicate that the text has been copied
      setCopied(true);

      // Reset the copied state after a short delay
      setTimeout(() => {
        setCopied(false);
      }, 1500);
    } catch (err) {
      console.error('Unable to copy to clipboard:', err);
    }
  };

  const avatarE = (
    <Avatar className="w-6 h-6">
      {avatar && <AvatarImage src={avatar} />}
      <AvatarFallback className="text-sm capitalize">
        {name?.charAt(0)}
      </AvatarFallback>
    </Avatar>
  );
  const refsToAdd = [
    isCursor ? cursorRef : null,
    canPlayMedia && speakingTranscriptIdx === idx
      ? speakingTranscriptRef
      : null,
  ].filter((r) => !!r);
  return (
    <div
      className={cn('flex flex-col h-fit', {
        'align-start mt-4': !isInternalParty && idx !== 0,
        'align-end mt-2': isInternalParty,
        'cursor-pointer': !!onClick,
      })}
      onMouseEnter={() => {
        setHover(true);
      }}
      onMouseLeave={() => {
        setHover(false);
      }}
    >
      <div
        ref={(e) => {
          refsToAdd.forEach((r) => {
            r.current = e || null;
          });
        }}
        className={cn(
          'relative flex flex-row items-end space-x-1.5 max-w-[80%]',
          {
            'self-start': !isInternalParty,
            'self-end': isInternalParty,
            'cursor-pointer': !!onClick,
          },
        )}
        onClick={() => {
          if (!window.getSelection()?.toString()) {
            onClick?.();
          }
        }}
      >
        {!isInternalParty && avatarE}
        {/* Reserve space for the icon even if it's not visible */}
        {isInternalParty && (
          <div className="w-12 absolute top-[calc(50%-12px)] left-[-32px]">
            {hover && (
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={'ghost'}
                      onClick={(e) => {
                        e.stopPropagation();
                        onCopyClick(message, idx);
                      }}
                      className="rounded-full p-2 text-muted-foreground"
                    >
                      {copied ? (
                        <CopyCheckIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <CopyIcon className="w-4 h-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy to clipboard</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        )}
        <div
          className={cn(
            'rounded-xl bg-[#FBFBFB] hover:bg-[#FBFBFB] hover:bg-gradient-to-b from-[#3dc3e6]/[0.05] via-[#49c8cf]/[0.05] to-[#36c4bf]/[0.05] border flex flex-col pl-3 pr-4 py-2.5',
            {
              'border border-teal-500':
                canPlayMedia && speakingTranscriptIdx === idx,
            },
            {
              'bg-[#F6FCFD]': canPlayMedia && speakingTranscriptIdx === idx,
            },
          )}
        >
          <div className="flex flex-row relative">
            <div className={cn('w-[3px] py-1 rounded-full', {})}>
              <div
                className={cn(`h-full w-full rounded-full`)}
                style={{
                  backgroundColor: getPartyColorForIdx(
                    party ? partyIdToIdx[party.id] : -1,
                  ),
                }}
              />
            </div>
            <div className="flex flex-row items-center">
              <span className="font-medium text-xs ml-2 flex items-center ">
                {name}
              </span>
              {simCallFlag && role === 'bot' ? (
                <span className="flex items-center w-[16px] h-[14px] items-center px-[3px] leading-[100%] font-medium text-[10px] ml-[6px] text-white rounded-[4px] bg-[linear-gradient(180deg,_#3DC3E6_0%,_#49C8CF_33.33%,_#36C4BF_98.96%)] font-medium">
                  AI
                </span>
              ) : (
                <></>
              )}
            </div>

            <div className="flex-1 min-w-4" />
            <span className="text-muted-foreground">
              {minutes}:{seconds}
            </span>
          </div>
          <p className="mt-1.5">{messageNode}</p>
        </div>
        {isInternalParty && avatarE}
        {!isInternalParty && (
          <div className="absolute top-[calc(50%-12px)] right-[-40px]">
            {hover && (
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={'ghost'}
                      onClick={(e) => {
                        e.stopPropagation();
                        onCopyClick(message, idx);
                      }}
                      className="rounded-full p-2 text-muted-foreground"
                    >
                      {copied ? (
                        <CopyCheckIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <CopyIcon className="w-4 h-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy to clipboard</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
