import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  RealCallPartyAffiliation,
  RealCallsPublicPartiesDto,
} from '@/lib/Integrations/RealCalls/types';
import { cn, escapeStrForRegex } from '@/lib/utils';
import {
  Fragment,
  MutableRefObject,
  ReactNode,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { getPartyColorForIdx } from '../../constants';
import { Input } from '@/components/ui/input';
import {
  ChevronLeft,
  ChevronRight,
  CopyCheckIcon,
  CopyIcon,
  SearchIcon,
  X,
} from 'lucide-react';
import { RealCallMediaControllerRefType } from '../../RealCallMediaPlayer';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';

export const RealCallNewSummaryTranscriptTab = (props: {
  parties: RealCallsPublicPartiesDto[];
  mediaTime: number;
  mediaControllerRef?: MutableRefObject<RealCallMediaControllerRefType>;
  transcript: {
    role: 'bot' | 'user';
    message: string;
    userName: string;
    secondsFromStart: number;
  }[];
  simCallFlag?: boolean;
  playerRef?: React.RefObject<Plyr | null>;
}) => {
  const [hoverMessageId, setHoverMessageId] = useState<number | null>(null);
  const [copiedMessageId, setCopiedMessageId] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const searchTermL = useMemo(
    () => searchTerm.toLowerCase().trim(),
    [searchTerm],
  );
  const [searchCursor, setSearchCursor] = useState(-1);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);

  const partyIdToIdx = useMemo(() => {
    return Object.fromEntries(props.parties.map((p, idx) => [p.id, idx]));
  }, [props.parties]);

  const nameToParty = useMemo(() => {
    if (!props.parties) {
      return {};
    }
    return Object.fromEntries(props.parties.map((p) => [p.name, p]));
  }, [props.parties]);

  const sortedTranscript = useMemo(() => {
    const t = [...(props.transcript || [])]
      .sort((o1, o2) => o1.secondsFromStart - o2.secondsFromStart)
      .map((t) => ({
        ...t,
        endSecondsFromStart: t.secondsFromStart,
        messageL: t.message.toLowerCase(),
      }));
    for (let i = t.length - 2; i >= 0; i--) {
      t[i].endSecondsFromStart = t[i + 1].secondsFromStart;
    }
    // Handle the endSecondsFromStart for the last item
    if (props.simCallFlag && props.playerRef?.current?.duration) {
      if (t[t.length - 1]) {
        t[t.length - 1].endSecondsFromStart =
          props.playerRef?.current?.duration;
      }
    }
    return t;
  }, [props.transcript]);

  const speakingTranscriptIdx = useMemo(() => {
    // Process media time to be in the range of the transcript
    const processedMediaTime = props.simCallFlag
      ? props.mediaTime !== 0
        ? sortedTranscript[0]?.secondsFromStart + props.mediaTime
        : props.mediaTime
      : props.mediaTime;
    return sortedTranscript.findIndex(
      (t) =>
        t.secondsFromStart <= processedMediaTime &&
        processedMediaTime < t.endSecondsFromStart,
    );
  }, [sortedTranscript, props.mediaTime]);

  const speakingTranscriptRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    if (isAutoScrollEnabled && speakingTranscriptRef.current) {
      speakingTranscriptRef.current.scrollIntoView({
        block: 'start',
        behavior: 'smooth',
      });
    }
  }, [isAutoScrollEnabled, speakingTranscriptIdx]);

  const searchMatches = useMemo(() => {
    if (!searchTermL) {
      return [];
    }
    return sortedTranscript
      .map((t, idx) => {
        return {
          idx,
          found: t.messageL.includes(searchTermL),
        };
      })
      .filter((t) => t.found)
      .map((t) => t.idx);
  }, [sortedTranscript, searchTermL]);

  useEffect(() => {
    setSearchCursor(searchMatches.length > 0 ? 0 : -1);
  }, [searchMatches]);

  const goToNextSearchCursor = () => {
    if (searchMatches.length === 0) {
      setSearchCursor(-1);
      return;
    }
    setSearchCursor((searchCursor + 1) % searchMatches.length);
  };

  const goToPreviousSearchCursor = () => {
    if (searchMatches.length === 0) {
      setSearchCursor(-1);
      return;
    }
    setSearchCursor(
      searchCursor > 0 ? searchCursor - 1 : searchMatches.length - 1,
    );
  };

  const cursorRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    if (cursorRef.current) {
      setIsAutoScrollEnabled(false);
      cursorRef.current.scrollIntoView({
        block: 'start',
        behavior: 'smooth',
      });
    }
  }, [searchMatches, searchCursor]);

  const canPlayMedia = props.simCallFlag
    ? true
    : !!props.mediaControllerRef?.current?.canPlay;

  const onCopyClick = async (msg: string, idx: number) => {
    try {
      // Use navigator.clipboard.writeText to copy text to clipboard

      await navigator.clipboard.writeText(msg);

      // Update state to indicate that the text has been copied
      setCopiedMessageId(idx);

      // Reset the copied state after a short delay
      setTimeout(() => {
        setCopiedMessageId(null);
      }, 1500);
    } catch (err) {
      console.error('Unable to copy to clipboard:', err);
    }
  };

  const getMessageBubble = (
    idx: number,
    message: string,
    messageL: string,
    party: RealCallsPublicPartiesDto,
    secondsFromStart: number,
    role: 'bot' | 'user',
    nameFromTranscript: string,
    onClick?: () => unknown,
  ) => {
    const isInternalParty = party
      ? party.affiliation === RealCallPartyAffiliation.INTERNAL
      : role === 'user';
    const minutes = Math.floor(secondsFromStart / 60);
    const seconds = Math.floor(secondsFromStart - 60 * minutes)
      .toString()
      .padStart(2, '0');
    const avatar = party?.user?.avatar || '';
    const name = party?.user
      ? `${party.user.firstName} ${party.user.lastName}`
      : party?.name || nameFromTranscript;
    let messageNode: ReactNode | ReactNode[] = message;

    const isCursor =
      searchMatches.length > 0 && searchMatches[searchCursor] === idx;
    if (searchMatches.length > 0 && messageL.includes(searchTermL)) {
      const matchedIndices = new Set(
        Array.from(
          messageL.matchAll(new RegExp(escapeStrForRegex(searchTermL), 'gi')),
        ).map((a) => a.index),
      );
      const newMessageNode: ReactNode[] = [];
      let i = 0;
      while (i < message.length) {
        if (matchedIndices.has(i)) {
          newMessageNode.push(
            <span
              className={cn('rounded-sm', {
                'bg-teal-500': isCursor,
                'bg-teal-200': !isCursor,
              })}
            >
              {message.substring(i, i + searchTerm.length)}
            </span>,
          );
          i += searchTerm.length;
        } else {
          newMessageNode.push(message[i]);
          i++;
        }
      }
      messageNode = newMessageNode;
    }
    const avatarE = (
      <Avatar className="w-6 h-6">
        {avatar && <AvatarImage src={avatar} />}
        <AvatarFallback className="text-sm capitalize">
          {name?.charAt(0)}
        </AvatarFallback>
      </Avatar>
    );
    const refsToAdd = [
      isCursor ? cursorRef : null,
      canPlayMedia && speakingTranscriptIdx === idx
        ? speakingTranscriptRef
        : null,
    ].filter((r) => !!r);
    return (
      <div
        className={cn('flex flex-col h-fit', {
          'align-start mt-4': !isInternalParty && idx !== 0,
          'align-end mt-2': isInternalParty,
          'cursor-pointer': !!onClick,
        })}
        onMouseEnter={() => {
          setHoverMessageId(idx);
        }}
        onMouseLeave={() => {
          setHoverMessageId(null);
        }}
      >
        <div
          ref={(e) => {
            refsToAdd.forEach((r) => {
              r.current = e || null;
            });
          }}
          className={cn(
            'relative flex flex-row items-end space-x-1.5 max-w-[80%]',
            {
              'self-start': !isInternalParty,
              'self-end': isInternalParty,
              'cursor-pointer': !!onClick,
            },
          )}
          onClick={() => {
            if (!window.getSelection()?.toString()) {
              onClick?.();
            }
          }}
        >
          {!isInternalParty && avatarE}
          {/* Reserve space for the icon even if it's not visible */}
          {isInternalParty && (
            <div className="w-12 absolute top-[calc(50%-12px)] left-[-32px]">
              {hoverMessageId === idx && (
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={'ghost'}
                        onClick={(e) => {
                          e.stopPropagation();
                          onCopyClick(message, idx);
                        }}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        {copiedMessageId === idx ? (
                          <CopyCheckIcon className="w-4 h-4 text-green-600" />
                        ) : (
                          <CopyIcon className="w-4 h-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Copy to clipboard</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          )}
          <div
            className={cn(
              'rounded-xl bg-[#FBFBFB] hover:bg-[#FBFBFB] hover:bg-gradient-to-b from-[#3dc3e6]/[0.05] via-[#49c8cf]/[0.05] to-[#36c4bf]/[0.05] border flex flex-col pl-3 pr-4 py-2.5',
              {
                'border border-teal-500':
                  canPlayMedia && speakingTranscriptIdx === idx,
              },
              {
                'bg-[#F6FCFD]': canPlayMedia && speakingTranscriptIdx === idx,
              },
            )}
          >
            <div className="flex flex-row relative">
              <div className={cn('w-[3px] py-1 rounded-full', {})}>
                <div
                  className={cn(`h-full w-full rounded-full`)}
                  style={{
                    backgroundColor: getPartyColorForIdx(
                      party ? partyIdToIdx[party.id] : -1,
                    ),
                  }}
                />
              </div>
              <div className="flex flex-row items-center">
                <span className="font-medium text-xs ml-2 flex items-center ">
                  {name}
                </span>
                {props.simCallFlag && role === 'bot' ? (
                  <span className="flex items-center w-[16px] h-[14px] items-center px-[3px] leading-[100%] font-medium text-[10px] ml-[6px] text-white rounded-[4px] bg-[linear-gradient(180deg,_#3DC3E6_0%,_#49C8CF_33.33%,_#36C4BF_98.96%)] font-medium">
                    AI
                  </span>
                ) : (
                  <></>
                )}
              </div>

              <div className="flex-1 min-w-4" />
              <span className="text-muted-foreground">
                {minutes}:{seconds}
              </span>
            </div>
            <p className="mt-1.5">{messageNode}</p>
          </div>
          {isInternalParty && avatarE}
          {!isInternalParty && (
            <div className="absolute top-[calc(50%-12px)] right-[-40px]">
              {hoverMessageId === idx && (
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={'ghost'}
                        onClick={(e) => {
                          e.stopPropagation();
                          onCopyClick(message, idx);
                        }}
                        className="rounded-full p-2 text-muted-foreground"
                      >
                        {copiedMessageId === idx ? (
                          <CopyCheckIcon className="w-4 h-4 text-green-600" />
                        ) : (
                          <CopyIcon className="w-4 h-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Copy to clipboard</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col pl-4 h-full w-full">
      {
        <div
          className={cn(
            'flex flex-row border items-center space-x-1 mt-2 rounded-md',
            {
              'absolute top-0 left-0 right-[80px] left-[198px]':
                props.simCallFlag,
              'bg-[#FBFBFB]': !props.simCallFlag,
              'bg-[#FFFFFF]': props.simCallFlag,
              'rounded-[8px]': props.simCallFlag,
            },
          )}
        >
          <SearchIcon className="w-4 h-4 ml-2 text-muted-foreground" />
          <Input
            className="border-0 shadow-none focus-visible:ring-0 flex-1"
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                goToNextSearchCursor();
              } else if (e.key === 'Enter' && e.shiftKey) {
                goToPreviousSearchCursor();
              } else if (e.key === 'Escape') {
                setSearchTerm('');
              }
            }}
          />
          {!!searchTermL && (
            <div className="flex flex-row items-center">
              <div
                className="p-1 cursor-pointer"
                onClick={() => {
                  setSearchTerm('');
                }}
              >
                <X className="w-4 h-4" />
              </div>
              <div
                className="p-1 cursor-pointer"
                onClick={() => {
                  goToPreviousSearchCursor();
                }}
              >
                <ChevronLeft className="w-4 h-4" />
              </div>
              <p className="text-center w-10">
                {searchCursor + 1}/{searchMatches.length}
              </p>
              <div
                className="p-1 cursor-pointer"
                onClick={() => {
                  goToNextSearchCursor();
                }}
              >
                <ChevronRight className="w-4 h-4" />
              </div>
            </div>
          )}
        </div>
      }
      <div className="flex-1 relative w-full mt-2">
        <div
          className="flex flex-col absolute left-0 right-0 top-0 bottom-0 overflow-y-auto pb-4 pr-4"
          onWheel={() => {
            setIsAutoScrollEnabled(false);
          }}
        >
          {sortedTranscript.map((obj, idx) => {
            return (
              <Fragment key={idx}>
                {getMessageBubble(
                  idx,
                  obj.message,
                  obj.messageL,
                  nameToParty[obj.userName],
                  obj.secondsFromStart,
                  obj.role,
                  obj.userName,
                  () => {
                    if (!props.simCallFlag) {
                      const mediaController = props.mediaControllerRef?.current;
                      if (mediaController) {
                        mediaController.seekTo(obj.secondsFromStart);
                        mediaController.play();
                      }
                    } else {
                      if (props.playerRef?.current) {
                        props.playerRef.current.currentTime =
                          obj.secondsFromStart -
                          sortedTranscript[0].secondsFromStart;
                        if (props.playerRef.current.paused) {
                          props.playerRef.current.play();
                        }
                      }
                    }
                    setIsAutoScrollEnabled(true);
                  },
                )}
              </Fragment>
            );
          })}
        </div>
        {!isAutoScrollEnabled && canPlayMedia && props.mediaTime !== 0 && (
          <div className="absolute bottom-6 left-0 right-0 mx-auto flex flex-row justify-center w-fit h-fit">
            <div
              className="rounded-full px-3 py-1.5 bg-black cursor-pointer"
              onClick={() => {
                setIsAutoScrollEnabled(true);
              }}
            >
              <p className="text-white">
                Sync with{' '}
                {props.mediaControllerRef?.current?.mediaType || 'audio'} time
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
