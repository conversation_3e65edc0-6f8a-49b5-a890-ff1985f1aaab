import { RealCallsPublicPartiesDto } from '@/lib/Integrations/RealCalls/types';
import { cn } from '@/lib/utils';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { ChevronLeft, ChevronRight, SearchIcon, X } from 'lucide-react';
import { RealCallMediaControllerRefType } from '../../RealCallMediaPlayer';
import { sortTranscript } from './utils';
import { IHighlightPhrase } from '@/common/Calls/AIRoleplay/NewSummary';
import { MessageBubble } from './messageBubble';

export interface ITranscriptProp {
  role: 'bot' | 'user';
  message: string;
  userName: string;
  secondsFromStart: number;
}
export const RealCallNewSummaryTranscriptTab = (props: {
  parties: RealCallsPublicPartiesDto[];
  mediaTime: number;
  mediaControllerRef?: React.RefObject<RealCallMediaControllerRefType>;
  transcript: ITranscriptProp[];
  isAutoScrollEnabled: boolean;
  setIsAutoScrollEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  simCallFlag?: boolean;
  playerRef?: React.RefObject<Plyr | null>;
  highlightPhrases?: IHighlightPhrase[];
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const searchTermL = useMemo(
    () => searchTerm.toLowerCase().trim(),
    [searchTerm],
  );
  const [searchCursor, setSearchCursor] = useState(-1);

  const nameToParty = useMemo(() => {
    if (!props.parties) {
      return {};
    }
    return Object.fromEntries(props.parties.map((p) => [p.name, p]));
  }, [props.parties]);

  const sortedTranscript = useMemo(() => {
    return sortTranscript(
      props.transcript,
      props.simCallFlag,
      props.playerRef?.current?.duration,
    );
  }, [props.transcript]);

  const speakingTranscriptIdx = useMemo(() => {
    // Process media time to be in the range of the transcript
    const processedMediaTime = props.simCallFlag
      ? props.mediaTime !== 0
        ? sortedTranscript[0]?.secondsFromStart + props.mediaTime
        : props.mediaTime
      : props.mediaTime;
    return sortedTranscript.findIndex(
      (t) =>
        t.secondsFromStart <= processedMediaTime &&
        processedMediaTime < t.endSecondsFromStart,
    );
  }, [sortedTranscript, props.mediaTime]);

  const speakingTranscriptRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    if (props.isAutoScrollEnabled && speakingTranscriptRef.current) {
      speakingTranscriptRef.current.scrollIntoView({
        block: 'start',
        behavior: 'smooth',
      });
    }
  }, [props.isAutoScrollEnabled, speakingTranscriptIdx]);

  const searchMatches = useMemo(() => {
    if (!searchTermL) {
      return [];
    }
    return sortedTranscript
      .map((t, idx) => {
        return {
          idx,
          found: t.messageL.includes(searchTermL),
        };
      })
      .filter((t) => t.found)
      .map((t) => t.idx);
  }, [sortedTranscript, searchTermL]);

  useEffect(() => {
    setSearchCursor(searchMatches.length > 0 ? 0 : -1);
  }, [searchMatches]);

  const goToNextSearchCursor = () => {
    if (searchMatches.length === 0) {
      setSearchCursor(-1);
      return;
    }
    setSearchCursor((searchCursor + 1) % searchMatches.length);
  };

  const goToPreviousSearchCursor = () => {
    if (searchMatches.length === 0) {
      setSearchCursor(-1);
      return;
    }
    setSearchCursor(
      searchCursor > 0 ? searchCursor - 1 : searchMatches.length - 1,
    );
  };

  const cursorRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    if (cursorRef.current) {
      props.setIsAutoScrollEnabled(false);
      cursorRef.current.scrollIntoView({
        block: 'start',
        behavior: 'smooth',
      });
    }
  }, [searchMatches, searchCursor]);

  const canPlayMedia = props.simCallFlag
    ? true
    : !!props.mediaControllerRef?.current?.canPlay;

  return (
    <div className="flex flex-col pl-4 h-full w-full">
      {
        <div
          className={cn(
            'flex flex-row border items-center space-x-1 mt-2 rounded-md',
            {
              'absolute top-0 left-0 right-[80px] left-[198px]':
                props.simCallFlag,
              'bg-[#FBFBFB]': !props.simCallFlag,
              'bg-[#FFFFFF]': props.simCallFlag,
              'rounded-[8px]': props.simCallFlag,
            },
          )}
        >
          <SearchIcon className="w-4 h-4 ml-2 text-muted-foreground" />
          <Input
            className="border-0 shadow-none focus-visible:ring-0 flex-1"
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                goToNextSearchCursor();
              } else if (e.key === 'Enter' && e.shiftKey) {
                goToPreviousSearchCursor();
              } else if (e.key === 'Escape') {
                setSearchTerm('');
              }
            }}
          />
          {!!searchTermL && (
            <div className="flex flex-row items-center">
              <div
                className="p-1 cursor-pointer"
                onClick={() => {
                  setSearchTerm('');
                }}
              >
                <X className="w-4 h-4" />
              </div>
              <div
                className="p-1 cursor-pointer"
                onClick={() => {
                  goToPreviousSearchCursor();
                }}
              >
                <ChevronLeft className="w-4 h-4" />
              </div>
              <p className="text-center w-10">
                {searchCursor + 1}/{searchMatches.length}
              </p>
              <div
                className="p-1 cursor-pointer"
                onClick={() => {
                  goToNextSearchCursor();
                }}
              >
                <ChevronRight className="w-4 h-4" />
              </div>
            </div>
          )}
        </div>
      }
      <div className="flex-1 relative w-full mt-2">
        <div
          className="flex flex-col absolute left-0 right-0 top-0 bottom-0 overflow-y-auto pb-4 pr-4"
          onWheel={() => {
            props.setIsAutoScrollEnabled(false);
          }}
        >
          {sortedTranscript.map((obj, idx) => {
            return (
              <MessageBubble
                key={idx}
                parties={props.parties}
                idx={idx}
                message={obj.message}
                messageL={obj.messageL}
                party={nameToParty[obj.userName]}
                secondsFromStart={obj.secondsFromStart}
                role={obj.role}
                nameFromTranscript={obj.userName}
                cursorRef={cursorRef}
                canPlayMedia={canPlayMedia}
                speakingTranscriptIdx={speakingTranscriptIdx}
                speakingTranscriptRef={speakingTranscriptRef}
                searchTerm={searchTerm}
                searchMatches={searchMatches}
                searchCursor={searchCursor}
                simCallFlag={props.simCallFlag}
                onClick={() => {
                  if (!props.simCallFlag) {
                    const mediaController = props.mediaControllerRef?.current;
                    if (mediaController) {
                      mediaController.seekTo(obj.secondsFromStart);
                      mediaController.play();
                    }
                  } else {
                    if (props.playerRef?.current) {
                      props.playerRef.current.currentTime =
                        obj.secondsFromStart -
                        sortedTranscript[0].secondsFromStart;
                      if (props.playerRef.current.paused) {
                        props.playerRef.current.play();
                      }
                    }
                  }
                  props.setIsAutoScrollEnabled(true);
                }}
                highlightPhrases={props.highlightPhrases
                  ?.map((highlightPhraseObj: IHighlightPhrase, idx: number) => {
                    return {
                      ...highlightPhraseObj,
                      citationNumber: idx + 1,
                    };
                  })
                  .filter(
                    (highlightPhraseObj: IHighlightPhrase) =>
                      highlightPhraseObj.secondsFromStart ===
                      obj.secondsFromStart,
                  )}
              />
            );
          })}
        </div>
        {!props.isAutoScrollEnabled &&
          canPlayMedia &&
          props.mediaTime !== 0 && (
            <div className="absolute bottom-6 left-0 right-0 mx-auto flex flex-row justify-center w-fit h-fit">
              <div
                className="rounded-full px-3 py-1.5 bg-black cursor-pointer"
                onClick={() => {
                  props.setIsAutoScrollEnabled(true);
                }}
              >
                <p className="text-white">
                  Sync with{' '}
                  {props.mediaControllerRef?.current?.mediaType || 'audio'} time
                </p>
              </div>
            </div>
          )}
      </div>
    </div>
  );
};
