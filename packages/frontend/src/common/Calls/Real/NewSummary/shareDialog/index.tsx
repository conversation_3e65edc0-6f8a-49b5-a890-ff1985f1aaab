import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { RealCallsService } from '@/lib/Integrations';
import {
  RealCallPartyAffiliation,
  RealCallPublicUrlDetails,
  RealCallsPublicDetailsDto,
  RealCallsPublicPartiesDto,
} from '@/lib/Integrations/RealCalls/types';
import { cn, shortenEmail } from '@/lib/utils';
import { CopyIcon, GlobeIcon } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

export const RealCallShareDialog = ({
  callId,
  open,
  setOpen,
  publicCallDetails,
  isLoading = false,
}: {
  callId: number;
  open: boolean;
  setOpen: (o: boolean) => unknown;
  publicCallDetails: RealCallsPublicDetailsDto | undefined;
  isLoading?: boolean;
}) => {
  const [isLoadingI, setIsLoadingI] = useState(false);
  const isLoadingCombined = useMemo(
    () => isLoading || isLoadingI,
    [isLoadingI, isLoading],
  );

  const [publicUrlDetails, setPublicUrlDetails] =
    useState<RealCallPublicUrlDetails>({ url: '', isPublicEnabled: false });

  const refetchUrl = async () => {
    setIsLoadingI(true);
    const res = await RealCallsService.getPublicUrl(callId);
    if (res) {
      setPublicUrlDetails(res);
    }
    setIsLoadingI(false);
  };

  const toggleUrl = async (isPublicEnabled: boolean) => {
    setIsLoadingI(true);
    setPublicUrlDetails({ ...publicUrlDetails, isPublicEnabled });
    const res = await RealCallsService.togglePublicUrl(callId, isPublicEnabled);
    if (res) {
      setPublicUrlDetails(res);
    }
    setIsLoadingI(false);
  };

  useEffect(() => {
    if (callId) {
      refetchUrl();
    } else {
      setPublicUrlDetails({ url: '', isPublicEnabled: false });
    }
  }, [callId]);

  const parties = publicCallDetails?.parties || [];

  const internalUsers = useMemo(() => {
    return (publicCallDetails?.parties || []).filter(
      (p) => p.affiliation === RealCallPartyAffiliation.INTERNAL,
    );
  }, [parties]);

  const domainUsers = useMemo(() => {
    const externalUsers = parties.filter(
      (p) => p.affiliation === RealCallPartyAffiliation.EXTERNAL,
    );
    const externalUsersOfDomain: {
      [domain: string]: RealCallsPublicPartiesDto[];
    } = {};
    externalUsers.forEach((u) => {
      const domain = u.email?.substring(u.email?.indexOf('@') + 1) || '';
      if (!externalUsersOfDomain[domain]) {
        externalUsersOfDomain[domain] = [];
      }
      externalUsersOfDomain[domain].push(u);
    });
    return Object.entries(externalUsersOfDomain)
      .map(([domain, users]) => ({
        domain,
        users,
      }))
      .sort((d1, d2) => d2.domain.localeCompare(d1.domain));
  }, [parties]);

  const allParties = useMemo(
    () => [...internalUsers, ...domainUsers.map((d) => d.users).flat()],
    [internalUsers, domainUsers],
  );

  const allPartiesWithEmail = useMemo(() => {
    return allParties.filter((party) => party.email || party.user?.email);
  }, [allParties]);

  const [emailSentMessage, setEmailSentMessage] = useState('');

  useEffect(() => {
    if (emailSentMessage) {
      const timeout = setTimeout(() => {
        setEmailSentMessage('');
      }, 2500);
      return () => clearTimeout(timeout);
    }
  }, [emailSentMessage]);

  const [emailSending, setEmailSending] = useState(false);
  const [emailInput, setEmailInput] = useState('');

  const isEmailInputValid = useMemo(() => {
    return /^\S+@\S+\.\S+$/.test(emailInput);
  }, [emailInput]);

  const sendSummaryEmail = async (email?: string, realCallPartyId?: number) => {
    setEmailSending(true);
    await RealCallsService.sendSummaryEmail(callId, email, realCallPartyId);
    setEmailSentMessage('The email summary has been sent!');
    setEmailSending(false);
    if (email) {
      setEmailInput('');
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
    >
      <DialogContent className="max-w-xl close-btn">
        <DialogHeader>
          <DialogTitle>Share this call</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <div>
            <p className="text-sm">
              Public Share Link (Anyone with link can view)
            </p>
            <div
              className={cn(
                'border mt-3 rounded-lg flex flex-row items-center px-2 h-[34px] bg-gray-50 hover:bg-gray-100 transition-all hover:scale-[0.99] duration-200 w-full relative',
                {
                  'cursor-pointer': !isLoadingCombined,
                },
              )}
              onClick={() => {
                if (!isLoadingCombined && !!publicUrlDetails) {
                  navigator.clipboard.writeText(publicUrlDetails.url);
                }
              }}
            >
              <div className="flex-1 relative h-full">
                <div className="absolute left-0 right-0 top-0 bottom-0 flex flex-row items-center">
                  <p className="w-full break-words text-sm mr-4">
                    {publicUrlDetails.url || '...'}
                  </p>
                </div>
              </div>
              <CopyIcon className="w-4 h-4" />
            </div>
            <div className="border rounded-lg flex flex-row px-2 py-2 items-center mt-3 space-x-4">
              <GlobeIcon
                className="w-6 h-6 text-muted-foreground"
                strokeWidth={1}
              />
              <div className="flex flex-col text-sm flex-1">
                <p className="font-medium">Public Access</p>
                <p className="text-xs text-muted-foreground mt-1">
                  If enabled, everyone outside your org would be able to view
                  the call
                </p>
              </div>
              <Switch
                className="data-[state=checked]:bg-[#3C82F6] data-[state=unchecked]:bg-input"
                checked={publicUrlDetails.isPublicEnabled}
                onCheckedChange={(c) => {
                  toggleUrl(c);
                }}
                disabled={isLoadingCombined}
                color="#3C82F6"
              />
            </div>
          </div>
          <div className="mt-6">
            <p className="text-sm">Send over email</p>
            <p className="text-sm text-green-600 mt-2">{emailSentMessage}</p>
            <form className="flex flex-row items-center space-x-2 mt-3">
              <Input
                type="email"
                className="bg-gray-50"
                placeholder="Email address"
                value={emailInput}
                onChange={(e) => {
                  setEmailInput(e.currentTarget.value);
                }}
              />
              <Button
                variant="default"
                disabled={emailSending || !isEmailInputValid}
                onClick={() => {
                  sendSummaryEmail(emailInput);
                }}
              >
                Send
              </Button>
            </form>
            <div className="max-h-[258px] overflow-y-auto mt-3 pr-2">
              {allPartiesWithEmail?.map((p) => {
                const avatar = p.user?.avatar;
                const name = p.name;
                const email = p.user?.email || p.email;
                return (
                  <div
                    key={p.id}
                    className={cn(
                      'flex flex-row justify-between items-center py-2 text-sm',
                    )}
                  >
                    <div className="flex flex-row items-center">
                      <Avatar className="w-8 h-8">
                        {avatar && <AvatarImage src={avatar} />}
                        <AvatarFallback className="text-sm capitalize">
                          {name?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 flex flex-col pl-2">
                        <p>{name}</p>
                        <p className="text-muted-foreground mt-0.5">
                          {shortenEmail(email)}
                        </p>
                      </div>
                    </div>
                    {email ? (
                      <Button
                        variant="outline"
                        disabled={emailSending || !email}
                        onClick={() => {
                          sendSummaryEmail(
                            p.id ? undefined : p.email,
                            p.id ? p.id : undefined,
                          );
                        }}
                      >
                        Send
                      </Button>
                    ) : (
                      <p className="text-muted-foreground">No email found</p>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
