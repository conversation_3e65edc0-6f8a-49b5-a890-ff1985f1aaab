import { RealCallMediaUrls } from '@/lib/Integrations/RealCalls/types';
import { Loader2Icon } from 'lucide-react';
import { MutableRefObject, useEffect, useRef, useState } from 'react';

export type RealCallMediaControllerRefType = {
  canPlay: boolean;
  isLoading: boolean;
  isPlaying: boolean;
  mediaType: 'audio' | 'video';
  play: () => void;
  pause: () => void;
  seekTo: (time: number) => void;
};

export const RealCallMediaPlayer = ({
  media,
  className,
  onTimeUpdate,
  mediaControllerRef,
}: {
  media: RealCallMediaUrls;
  className?: string;
  onTimeUpdate?: (time: number) => unknown;
  mediaControllerRef?: MutableRefObject<RealCallMediaControllerRefType>;
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  useEffect(() => {
    if (mediaControllerRef) {
      const refToUse = media?.video && videoRef?.current ? videoRef : audioRef;
      mediaControllerRef.current = {
        canPlay: !!media?.video || !!media?.audio,
        isLoading,
        isPlaying,
        mediaType: media?.video ? 'video' : 'audio',
        pause: () => {
          if (refToUse.current) {
            refToUse.current.pause();
          }
        },
        play: () => {
          if (refToUse.current) {
            refToUse.current.play();
          }
        },
        seekTo: (time) => {
          if (refToUse.current) {
            refToUse.current.currentTime = time;
          }
        },
      };
    }
  }, [isPlaying, isLoading, media]);
  return (
    <div className={`${className || ''}`}>
      {media?.video?.url ? (
        <video
          controls
          className="rounded-lg w-full h-[180px] bg-black object-contain"
          ref={videoRef}
          onTimeUpdate={() => {
            onTimeUpdate?.(videoRef?.current?.currentTime || 0);
          }}
          onCanPlay={() => {
            setIsLoading(false);
          }}
          onPlay={() => {
            setIsPlaying(true);
          }}
          onPause={() => {
            setIsPlaying(false);
          }}
        >
          <source src={media.video.url}></source>
        </video>
      ) : media?.audio?.url ? (
        <div className="relative">
          {isLoading && (
            <div className="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center z-50 rounded-full bg-muted">
              <Loader2Icon className="animate-spin" />
            </div>
          )}
          <audio
            ref={audioRef}
            onTimeUpdate={() => {
              onTimeUpdate?.(audioRef?.current?.currentTime || 0);
            }}
            onCanPlay={() => {
              setIsLoading(false);
            }}
            onPlay={() => {
              setIsPlaying(true);
            }}
            onPause={() => {
              setIsPlaying(false);
            }}
            controls
            controlsList="nodownload"
            className="w-full"
          >
            <source src={media.audio.url} />
          </audio>
        </div>
      ) : (
        <></>
      )}
    </div>
  );
};
