import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import ConfirmationModal from '@/components/ConfirmationModal';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  useRealCallPublicDetails,
  useRealCallScorecardNew,
} from '@/hooks/useRealCalls';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import { RealCallsService } from '@/lib/Integrations';
import LinksManager from '@/lib/linksManager';
import { formatDuration, formatRealCallTranscript } from '@/lib/utils';
import { DotsVerticalIcon } from '@radix-ui/react-icons';
import { useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import {
  CalendarIcon,
  ClockIcon,
  CopyIcon,
  DownloadCloudIcon,
  DownloadIcon,
  LinkIcon,
  Loader2Icon,
  LockIcon,
  MailIcon,
  PencilIcon,
  SparklesIcon,
  Trash2,
  UserIcon,
  Users2Icon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { MutableRefObject, useEffect, useMemo, useRef, useState } from 'react';
import EditCallInfo from '../Summary/editInfo';
import FollowUpEmailModal from './followUpEmailModal';
import RealCallNewSummaryLeftSideDetails from './leftSideDetails';
import { RealCallMediaControllerRefType } from './RealCallMediaPlayer';
import RealCallNewSummaryRightSideDetails from './rightSideDetails';
import { RealCallShareDialog } from './shareDialog';
import { RealCallScoringStatus } from '@/lib/Integrations/RealCalls/types';
import { LockKeyholeIcon } from 'lucide-react';

interface IProps {
  callId: number;
  isPublicVersion?: boolean;
  pwd?: string;
}

export default function RealCallNewSummary({
  callId,
  pwd,
  isPublicVersion = false,
}: IProps) {
  const [isRescoring, setIsRescoring] = useState<boolean>(false);
  const [rescoringMessage, setRescoringMessage] = useState('');
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [followUpEmailOpen, setFollowUpEmailOpen] = useState(false);
  const { isLoggedIn } = useUserSession();
  const { goToPage } = useRouting();
  if (isPublicVersion && isLoggedIn) {
    goToPage(LinksManager.realCalls(callId));
  }
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
    { title: 'Real Calls', href: LinksManager.realCalls() },
  ]);
  const {
    data: publicDetails,
    isLoading: isLoading,
    refetch: refetchPublicDetails,
  } = useRealCallPublicDetails(callId, pwd);
  const { data: scorecard } = isPublicVersion
    ? {
        data: null,
      }
    : useRealCallScorecardNew(callId);
  const [editCallOpen, setEditCallOpen] = useState<boolean>(false);

  useEffect(() => {
    if (publicDetails) {
      setBreadcrumbs([
        { title: 'Real Calls', href: LinksManager.realCalls() },
        { title: publicDetails.title },
      ]);
    }
  }, [publicDetails]);

  const editCallDetails = useMemo(() => {
    if (publicDetails) {
      return {
        id: callId,
        callerId: publicDetails.caller.id,
        callType: publicDetails.callType,
        scorecard: {
          scorecardConfigId: scorecard?.scorecardConfigId,
        },
      };
    }
  }, [scorecard, publicDetails]);

  const mediaControllerRef = useRef<RealCallMediaControllerRefType>(
    null,
  ) as MutableRefObject<RealCallMediaControllerRefType>;
  const [mediaTime, setMediaTime] = useState(0);

  const [showDeleteConfirmation, setShowDeleteConfirmation] =
    useState<boolean>(false);

  const startDeleteCall = () => {
    setShowDeleteConfirmation(true);
  };

  const cancelDelete = () => {
    setShowDeleteConfirmation(false);
  };

  const queryClient = useQueryClient();
  const router = useRouter();

  const confirmDelete = async () => {
    try {
      await RealCallsService.deleteCall(callId);
    } catch (e) {
      console.error(e);
    }
    queryClient.invalidateQueries({ queryKey: ['list-real-calls'] });
    setShowDeleteConfirmation(false);
    router.replace(LinksManager.realCalls());
  };

  const getFormattedTranscript = () =>
    formatRealCallTranscript(
      publicDetails?.transcript,
      callId,
      publicDetails?.integration?.provider?.companyName || 'Unknown',
    );

  const [transcriptCopied, setTranscriptCopied] = useState(false);

  useEffect(() => {
    if (transcriptCopied) {
      const timeout = setTimeout(() => {
        setTranscriptCopied(false);
      }, 1500);
      return () => clearTimeout(timeout);
    }
  }, [transcriptCopied]);

  const copyTranscript = () => {
    const formattedTranscript = getFormattedTranscript();
    navigator.clipboard.writeText(formattedTranscript);
    setTranscriptCopied(true);
  };

  const downloadTranscript = () => {
    const formattedTranscript = getFormattedTranscript();

    const blob = new Blob([formattedTranscript], {
      type: 'text/plain',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-real-${callId}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const rescoreCall = async () => {
    setIsRescoring(true);
    let ok = true;
    try {
      RealCallsService.rescoreCall(callId, scorecard?.scorecardConfigId);
    } catch (err) {
      ok = false;
      console.log(err);
    }

    if (ok) {
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: ['real-call-details', callId],
        });
        queryClient.invalidateQueries({
          queryKey: ['real-call-public-details', callId],
        });
        queryClient.invalidateQueries({
          queryKey: ['real-call-scorecard-new', callId],
        });
      }, 2000);
    }
    setIsRescoring(false);
    setRescoringMessage(
      'Your call is being rescored. Please check back in a few minutes.',
    );
  };

  return (
    <div className="bg-[#FBFBFB] w-full h-full flex flex-col">
      <DashboardNavbar
        className="bg-transparent border-b-0"
        breadcrumbs={breadcrumbs}
        subContent={
          <div className="text-muted-foreground">
            {isLoading ? (
              <div className="flex flex-col md:flex-row space-x-0 space-y-4 md:space-x-4 md:space-y-0 mt-1">
                <Skeleton className="w-32 h-6" />
                <Skeleton className="w-40 h-6" />
                <Skeleton className="w-16 h-6" />
                <Skeleton className="w-16 h-6" />
                <Skeleton className="w-32 h-6" />
              </div>
            ) : (
              <div className="flex flex-col md:flex-row space-x-0 space-y-4 md:space-x-4 md:space-y-0 ml-24 mt-1">
                <div className="flex items-center space-x-2">
                  <CalendarIcon className="w-4 h-4" />
                  <p className="text-sm">
                    {dayjs(publicDetails?.callDate).format(
                      dayjs(publicDetails?.callDate).year() === dayjs().year()
                        ? 'MMM D, h:mm A'
                        : 'MMM D, YYYY h:mm A',
                    )}
                  </p>
                </div>
                <div className="h-[10px] w-[2.5px] bg-[#E4E3E7] rounded-full mx-3 self-center" />
                <div className="flex items-center space-x-2">
                  <Users2Icon className="w-4 h-4" />
                  <p className="text-sm">{publicDetails?.parties?.length}</p>
                </div>
                <div className="h-[10px] w-[2.5px] bg-[#E4E3E7] rounded-full mx-3 self-center" />
                <div className="flex items-center space-x-2">
                  <ClockIcon className="w-4 h-4" />
                  <p className="text-sm">
                    {formatDuration((publicDetails?.duration || 0) * 1000)}
                  </p>
                </div>
                <div className="h-[10px] w-[2.5px] bg-[#E4E3E7] rounded-full mx-3 self-center" />
                <div className="flex items-center space-x-2">
                  <UserIcon className="w-4 h-4" />
                  <span>Host</span>
                  <Button
                    variant={'outline'}
                    onClick={() => {
                      if (isLoggedIn) {
                        goToPage(
                          LinksManager.members(
                            String(publicDetails?.caller?.id),
                          ),
                        );
                      }
                    }}
                    className="cursor-pointer space-x-[6px] pl-1 border pr-3 h-7 rounded-full text-primary hover:bg-muted/80 hover:transition-all duration-300"
                  >
                    <Avatar className="w-5 h-5">
                      {publicDetails?.caller?.avatar && (
                        <AvatarImage src={publicDetails?.caller?.avatar} />
                      )}
                      <AvatarFallback className="text-xs capitalize">
                        {publicDetails?.caller?.firstName?.charAt(0) || ''}
                        {publicDetails?.caller?.lastName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>
                    <div className="capitalize text-xs">
                      {publicDetails?.caller?.firstName || ''}{' '}
                      {publicDetails?.caller?.lastName || ''}
                    </div>
                  </Button>
                </div>
                {/* <div className="flex items-center space-x-2">
				  <BriefcaseIcon className="w-4 h-4" />
				  <p className="text-sm max-w-[240px] truncate">
					{agent?.jobTitle || ""} @ {agent?.companyName || ""}
				  </p>
				</div>
				{agent?.emotionalState && !isInIframe && (
				  <div className="flex items-center space-x-2">
					<BrainIcon className="w-4 h-4" />
					<Badge variant="outline">
					  {AGENT_EMOTIONAL_STATE_OPTIONS.find(
						(item) => item.value === agent?.emotionalState
					  )?.label ||
						agent?.emotionalState ||
						""}
					</Badge>
				  </div>
				)} */}
              </div>
            )}
          </div>
        }
        rightContent={
          <div className="flex flex-row">
            {!isPublicVersion && (
              <>
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span tabIndex={0}>
                        <Button
                          variant={'outline'}
                          onClick={() => setEditCallOpen(true)}
                          className="mr-2 shadow-md pointer-events-none"
                        >
                          <SparklesIcon
                            size={16}
                            className="mr-2 text-teal-500"
                          />
                          Ask Hyperbound AI
                          <LockIcon className="w-4 h-4 ml-2 text-muted-foreground" />
                        </Button>
                      </span>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      Ask Hyperbound AI anything about your call, coming soon!
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <div className="h-[10px] w-[2.5px] bg-[#E4E3E7] rounded-full self-center" />
                <Button
                  variant={'outline'}
                  onClick={() => setFollowUpEmailOpen(true)}
                  className="mr-2 ml-2 shadow-md"
                >
                  <MailIcon size={16} className="mr-2 text-teal-500" />
                  Follow up email
                </Button>
                <Button
                  variant={'default'}
                  onClick={() => setShareDialogOpen(true)}
                  className="text-white bg-gradient-to-b mr-2 from-[#3DC3E6] via-[#49C8CF] via-33% to-[#36C4BF] to-99% hover:scale-[0.98] hover:opacity-90 transition-all duration-200"
                >
                  <LinkIcon size={16} className="mr-2" />
                  Share
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <span className="sr-only">Open menu</span>
                      <DotsVerticalIcon className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => setEditCallOpen(true)}
                    >
                      <PencilIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                      <span>Edit call info</span>
                    </DropdownMenuItem>
                    {!!publicDetails?.mediaUrls?.audio?.url && (
                      <DropdownMenuItem>
                        <a
                          download
                          href={publicDetails.mediaUrls.audio.url}
                          className="flex flex-row"
                        >
                          <DownloadCloudIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                          <span>Download audio</span>
                        </a>
                      </DropdownMenuItem>
                    )}
                    {!!publicDetails?.mediaUrls?.video?.url && (
                      <DropdownMenuItem>
                        <a
                          download
                          href={publicDetails.mediaUrls.video.url}
                          className="flex flex-row"
                        >
                          <DownloadCloudIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                          <span>Download video</span>
                        </a>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => {
                        downloadTranscript();
                      }}
                    >
                      <DownloadIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                      <span>Download transcript</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => {
                        copyTranscript();
                      }}
                    >
                      <CopyIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                      <span>Copy transcript</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="cursor-pointer text-red-500"
                      onClick={() => {
                        startDeleteCall();
                      }}
                    >
                      <Trash2 className="w-4 h-4 mr-2 text-red-500" />
                      <span>Delete Call</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        }
      />
      {!!publicDetails && (
        <div className="w-full flex-1 relative">
          <div className="flex flex-row pt-4 pb-6 mx-4 space-x-4 absolute left-0 right-0 top-0 bottom-0">
            {publicDetails.scoringStatus == RealCallScoringStatus.SCORING_SKIPPED_DUE_TO_PRIVACY_RULE && ( 
              <div className="flex flex-col items-center justify-center bg-gray-100 w-full h-full rounded-lg border border-gray">
                <LockKeyholeIcon className="w-8 h-8 mb-3"/>
                <p className="text-center text-lg font-medium">Scoring skipped due to a privacy rule.</p>

                <div className="mt-10">
                  {!isRescoring && (
                    <Button 
                      variant="outline"
                      className="mb-2 bg-white hover:bg-white"
                      onClick={() => {
                        rescoreCall();
                      }}
                    >
                      Score Call - Skip Privacy
                    </Button>
                  )}
                  {isRescoring && (
                    <div className="flex flex-row items-center justify-center text-muted-foreground">
                      <Loader2Icon className="animate-spin w-4 h-4 mr-2"/> <p className="text-center">{rescoringMessage} Rescoring call in progress...</p>
                    </div>
                  )}
                </div>
              </div>
            )}
            {!(publicDetails.scoringStatus == RealCallScoringStatus.SCORING_SKIPPED_DUE_TO_PRIVACY_RULE) && (
              <RealCallNewSummaryLeftSideDetails
                className="w-[58%]"
                callId={callId}
                refetchPublicDetails={refetchPublicDetails}
                mediaTime={mediaTime}
                isPublicVersion={isPublicVersion}
                publicDetailsDto={publicDetails}
                scorecard={scorecard}
                mediaControllerRef={mediaControllerRef}
              />
            )}
            <RealCallNewSummaryRightSideDetails
              className="w-[42%]"
              isPublicVersion={isPublicVersion}
              publicDetailsDto={publicDetails}
              mediaTime={mediaTime}
              onTimeUpdate={(time) => setMediaTime(time)}
              mediaControllerRef={mediaControllerRef}
            />
          </div>
        </div>
      )}
      {!isPublicVersion && !!editCallDetails && editCallOpen && (
        <EditCallInfo
          call={editCallDetails}
          open={editCallOpen}
          onClose={() => {
            setEditCallOpen(false);
          }}
        />
      )}
      {!isPublicVersion && (
        <RealCallShareDialog
          callId={callId}
          open={shareDialogOpen}
          setOpen={setShareDialogOpen}
          publicCallDetails={publicDetails}
        />
      )}
      <ConfirmationModal
        open={showDeleteConfirmation}
        onCancel={cancelDelete}
        onConfirm={confirmDelete}
        title={`Delete call`}
        description={`Do you want to delete: ${publicDetails?.title || ''}`}
      />
      <FollowUpEmailModal
        open={followUpEmailOpen}
        onClose={() => setFollowUpEmailOpen(false)}
        callId={callId}
        publicDetails={publicDetails}
      />
    </div>
  );
}
