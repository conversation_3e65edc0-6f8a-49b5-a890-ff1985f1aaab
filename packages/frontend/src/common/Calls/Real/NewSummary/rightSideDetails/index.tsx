import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import useUserSession from '@/hooks/useUserSession';
import {
  RealCallPartyAffiliation,
  RealCallsPublicDetailsDto,
  RealCallsPublicPartiesDto,
} from '@/lib/Integrations/RealCalls/types';
import { cn } from '@/lib/utils';
import { LockIcon, PlayIcon } from 'lucide-react';
import { Fragment, MutableRefObject, useMemo, useState } from 'react';
import { getPartyColorForIdx } from '../constants';
import {
  RealCallMediaControllerRefType,
  RealCallMediaPlayer,
} from '../RealCallMediaPlayer';
import { RealCallNewSummaryTranscriptTab } from '../leftSideDetails/transcript';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

type SpeechDetails = {
  durationSeconds: number;
  durationStr: string;
  percentageOfTotalDuration: number;
  speechParts: {
    fromSeconds: number;
    toSeconds: number;
  }[];
};

interface ExtendedPartiesDto extends RealCallsPublicPartiesDto {
  speechDetails: SpeechDetails;
}

interface IProps {
  publicDetailsDto: RealCallsPublicDetailsDto;
  className?: string;
  isPublicVersion?: boolean;
  pwd?: string;
  mediaControllerRef?: MutableRefObject<RealCallMediaControllerRefType>;
  mediaTime: number;
  onTimeUpdate?: (time: number) => unknown;
}
interface TranscriptPart {
  role: 'bot' | 'user';
  message: string;
  userName: string;
  secondsFromStart: number;
}

export default function RealCallNewSummaryRightSideDetails(props: IProps) {
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const partyIdToIdx = useMemo(() => {
    return Object.fromEntries(
      props.publicDetailsDto.parties.map((p, idx) => [p.id, idx]),
    );
  }, [props.publicDetailsDto.parties]);
  const extendedParties: ExtendedPartiesDto[] = useMemo(() => {
    const speechPartsOfNames: {
      [name: string]: {
        fromSeconds: number;
        toSeconds: number;
      }[];
    } = {};
    let prevSecondsFromStart = -1;
    ([...props.publicDetailsDto.transcript] as TranscriptPart[])
      .sort((t1, t2) => t2.secondsFromStart - t1.secondsFromStart)
      .forEach((t) => {
        if (!speechPartsOfNames[t.userName]) {
          speechPartsOfNames[t.userName] = [];
        }
        speechPartsOfNames[t.userName].push({
          fromSeconds: t.secondsFromStart,
          toSeconds:
            prevSecondsFromStart === -1
              ? t.secondsFromStart
              : prevSecondsFromStart,
        });
        prevSecondsFromStart = t.secondsFromStart;
      });
    const extParties = props.publicDetailsDto.parties.map((p) => {
      const speechParts = speechPartsOfNames[p.name] || [];
      const durationSeconds = speechParts
        .map((p) => p.toSeconds - p.fromSeconds)
        .reduce((prev, curr) => prev + curr, 0);
      const durationPercentage = Math.floor(
        (100 * durationSeconds) / props.publicDetailsDto.duration,
      );
      const minutes = Math.floor(durationSeconds / 60);
      const seconds = Math.floor(durationSeconds - 60 * minutes)
        .toString()
        .padStart(2, '0');
      const durationStr = `${minutes}:${seconds}`;
      return {
        ...p,
        speechDetails: {
          speechParts,
          durationSeconds,
          percentageOfTotalDuration: durationPercentage,
          durationStr,
        },
      };
    });
    return extParties;
  }, [props.publicDetailsDto.parties, props.publicDetailsDto.transcript]);

  const globalSpeechParts = useMemo(() => {
    return extendedParties.flatMap((p) => {
      return p.speechDetails.speechParts
        .map((sp) => ({
          ...sp,
          partyId: p.id,
        }))
        .sort((p1, p2) => p1.fromSeconds - p2.fromSeconds);
    });
  }, [extendedParties]);
  const activeHighlightedPartyId = useMemo(() => {
    return globalSpeechParts.find(
      (p) => p.fromSeconds <= props.mediaTime && props.mediaTime < p.toSeconds,
    )?.partyId;
  }, [globalSpeechParts, props.mediaTime]);

  const partySort = (p1: ExtendedPartiesDto, p2: ExtendedPartiesDto) =>
    p2.speechDetails.durationSeconds - p1.speechDetails.durationSeconds;

  const internalUsers = useMemo(() => {
    return extendedParties
      .filter((p) => p.affiliation === RealCallPartyAffiliation.INTERNAL)
      .sort(partySort);
  }, [extendedParties]);

  const domainUsers = useMemo(() => {
    const externalUsers = extendedParties.filter(
      (p) => p.affiliation === RealCallPartyAffiliation.EXTERNAL,
    );
    const externalUsersOfDomain: {
      [domain: string]: ExtendedPartiesDto[];
    } = {};
    externalUsers.forEach((u) => {
      const domain = u.email?.substring(u.email?.indexOf('@') + 1) || '';
      if (!externalUsersOfDomain[domain]) {
        externalUsersOfDomain[domain] = [];
      }
      externalUsersOfDomain[domain].push(u);
    });
    Object.values(externalUsersOfDomain).forEach((uArr) => {
      uArr.sort(partySort);
    });
    return Object.entries(externalUsersOfDomain)
      .map(([domain, users]) => ({
        domain,
        users,
      }))
      .sort((d1, d2) => d2.domain.localeCompare(d1.domain));
  }, [extendedParties]);

  const generateOrgContainer = (
    orgTitle: string,
    orgLogo: string,
    parties: ExtendedPartiesDto[],
    accordianIdentifierValue: number | string,
  ) => {
    return (
      <AccordionItem
        value={`item-${accordianIdentifierValue}`}
        className={cn('flex flex-col')}
      >
        <AccordionTrigger className="flex flex-row items-center hover:no-underline hover:bg-gray-50 px-4">
          {orgLogo && (
            <img
              className="rounded-md flex-shrink-0"
              src={orgLogo}
              alt={orgTitle || 'Logo'}
              style={{
                width: '23px',
                height: '23px',
              }}
            />
          )}
          <span className="pl-2">
            {orgTitle || 'Unknown Org'} ({parties?.length || 0})
          </span>
        </AccordionTrigger>
        <AccordionContent>
          {parties.map((p, idx) => {
            const avatar = p.user?.avatar;
            const name = p.name;
            const email = p.user ? p.user.email : p.email;
            const color = getPartyColorForIdx(partyIdToIdx[p.id]);
            const isHighlightedParty = activeHighlightedPartyId === p.id;
            return (
              <div
                key={p.id}
                className={cn('flex flex-col py-3 mx-3', {
                  'border-b': idx !== parties.length - 1,
                })}
              >
                <div className="flex flex-row">
                  <Avatar className="w-5 h-5">
                    {avatar && <AvatarImage src={avatar} />}
                    <AvatarFallback className="text-sm capitalize">
                      {name?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 flex flex-col pl-2">
                    <p>{name}</p>
                    <p className="text-muted-foreground mt-0.5">{email}</p>
                  </div>
                  <p className="w-16 text-right text-muted-foreground">
                    {p.speechDetails.percentageOfTotalDuration}%
                  </p>
                  <p className="w-16 text-right text-muted-foreground">
                    {p.speechDetails.durationStr}
                  </p>
                </div>
                <div
                  className={cn(
                    'bg-[#F4F4F5] rounded-full  w-full h-3 mt-4 relative',
                    {
                      'cursor-pointer':
                        props.mediaControllerRef?.current?.canPlay,
                    },
                  )}
                  onClick={(e) => {
                    const rect = e.currentTarget.getBoundingClientRect();
                    const percClick = (e.clientX - rect.left) / rect.width;
                    const durationToJumpTo = Math.floor(
                      props.publicDetailsDto.duration * percClick,
                    );
                    props.mediaControllerRef?.current?.seekTo?.(
                      Math.min(
                        Math.max(0, durationToJumpTo),
                        props.publicDetailsDto.duration,
                      ),
                    );
                  }}
                >
                  {p.speechDetails.speechParts.map((sp, idx) => {
                    const durationSeconds = props.publicDetailsDto.duration;
                    return (
                      <div
                        key={idx}
                        style={{
                          backgroundColor: color,
                          left: `${(100 * sp.fromSeconds) / durationSeconds}%`,
                          width: `${
                            (100 * (sp.toSeconds - sp.fromSeconds)) /
                            durationSeconds
                          }%`,
                        }}
                        className="absolute top-0 bottom-0 rounded-[2px]"
                      />
                    );
                  })}
                  <div
                    className="absolute top-0 bottom-0 rounded-full w-[4px] border border-white"
                    style={{
                      backgroundColor: isHighlightedParty ? color : '#EDEDED',
                      transform: 'scale(2.5)',
                      left: `${
                        (100 * props.mediaTime) /
                        props.publicDetailsDto.duration
                      }%`,
                    }}
                  />
                </div>
              </div>
            );
          })}
        </AccordionContent>
      </AccordionItem>
    );
  };

  const { org } = useUserSession();

  return (
    <div
      className={`border bg-white rounded-md h-full flex flex-col ${
        props.className || ''
      }`}
    >
      <div className="w-full  my-4 px-3 flex flex-col cursor-default">
        {!!props.publicDetailsDto.mediaUrls && (
          <RealCallMediaPlayer
            media={props.publicDetailsDto.mediaUrls}
            onTimeUpdate={props.onTimeUpdate}
            mediaControllerRef={props.mediaControllerRef}
          />
        )}
        {!props.publicDetailsDto.mediaUrls && (
          <>
            <div className="rounded-lg border w-full flex flex-row py-2 items-center ">
              <PlayIcon className="w-4 h-4 ml-3 text-muted-foreground" />
              <div className="h-[1px] flex-1 mx-2 bg-muted-foreground" />
              <p className="mr-2">0:00</p>
            </div>
            <p className="ml-2 mt-1 text-xs text-muted-foreground">
              Call Recording not available for this call!
            </p>
          </>
        )}
      </div>
      <div className="px-3 overflow-hidden flex flex-col flex-grow h-full">
        <Tabs defaultValue="Transcript" className="flex flex-col h-full">
          <TabsList className="w-full">
            <TabsTrigger value="Transcript" className="flex-1">
              Transcript
            </TabsTrigger>
            <TabsTrigger value="Participants" className="flex-1">
              Participants{' '}
              {internalUsers.length > 0 &&
                `(${props.publicDetailsDto.parties.length})`}
            </TabsTrigger>
            <TooltipProvider delayDuration={50}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex-1 flex flex-row items-center justify-center">
                    <TabsTrigger
                      disabled
                      value="Chat Replay"
                      className="flex flex-row items-center justify-center space-x-2"
                    >
                      <p>Chat History</p>
                      <LockIcon size={12} className="ml-2" />
                    </TabsTrigger>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Coming soon!</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TabsList>
          <TabsContent value="Participants" className="flex-1 overflow-y-auto">
            <Accordion
              type="multiple"
              defaultValue={Array.from(
                { length: (domainUsers?.length || 0) + 1 },
                (_, index) => `item-${index}`,
              )}
            >
              {generateOrgContainer(
                props.publicDetailsDto.org?.name,
                org?.orgMetadata?.logo,
                internalUsers,
                0,
              )}
              {domainUsers.map((d) => {
                return (
                  <Fragment key={d.domain}>
                    {generateOrgContainer(d.domain, '', d.users, d.domain)}
                  </Fragment>
                );
              })}
            </Accordion>
          </TabsContent>
          <TabsContent
            value="Transcript"
            className="flex-1 overflow-y-auto h-full"
          >
            <div className="h-full flex flex-col">
              <RealCallNewSummaryTranscriptTab
                parties={props.publicDetailsDto.parties}
                transcript={props.publicDetailsDto.transcript}
                mediaTime={props.mediaTime}
                mediaControllerRef={props.mediaControllerRef}
                isAutoScrollEnabled={isAutoScrollEnabled}
                setIsAutoScrollEnabled={setIsAutoScrollEnabled}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
