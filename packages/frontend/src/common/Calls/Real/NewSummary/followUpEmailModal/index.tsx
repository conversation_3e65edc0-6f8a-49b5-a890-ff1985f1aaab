import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { RealCallsService } from '@/lib/Integrations';
import {
  FollowUpEmailResponse,
  RealCallsPublicDetailsDto,
} from '@/lib/Integrations/RealCalls/types';
import { ClipboardIcon, SparklesIcon, MailIcon } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

interface IProps {
  open: boolean;
  onClose: () => void;
  callId: number;
  publicDetails?: RealCallsPublicDetailsDto;
}

export default function FollowUpEmailModal({
  open,
  onClose,
  callId,
  publicDetails,
}: IProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [refinement, setRefinement] = useState('');
  const [followUpEmail, setFollowUpEmail] =
    useState<FollowUpEmailResponse | null>(null);
  const [selectedSender, setSelectedSender] = useState<string>('');
  const followUpEmailRecipient = useMemo(() => {
    if (!followUpEmail?.toUserName || !publicDetails?.parties) {
      return null;
    }

    return (
      publicDetails.parties.find(
        (party) =>
          party.name?.toLowerCase() === followUpEmail.toUserName?.toLowerCase(),
      ) || null
    );
  }, [publicDetails, followUpEmail]);

  // Get internal participants (excluding external/customer participants)
  const internalParticipants = useMemo(
    () =>
      publicDetails?.parties?.filter(
        (party) => party.affiliation === 'INTERNAL',
      ) || [],
    [publicDetails],
  );

  const internalParticipantsUniqueNames = useMemo(() => {
    const names = new Set<string>();
    internalParticipants.forEach((participant) => {
      if (participant != null && participant.name != null) names.add(participant.name);
    });
    return Array.from(names);
  }, [internalParticipants]);

  // Set initial sender to the call host when modal opens
  useEffect(() => {
    if (selectedSender) {
      return;
    }
    if (publicDetails?.caller) {
      setSelectedSender(
        `${publicDetails.caller.firstName} ${publicDetails.caller.lastName}`,
      );
    } else {
      setSelectedSender(internalParticipants?.[0]?.name || '');
    }
  }, [selectedSender, publicDetails, internalParticipants]);

  const generateFollowUpEmail = async (refinementText?: string) => {
    try {
      setIsGenerating(true);
      const result = await RealCallsService.generateFollowUpEmail(
        callId,
        selectedSender,
        refinementText,
      );
      setFollowUpEmail(result);
    } catch (error) {
      console.error('Failed to generate follow-up email:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClose = () => {
    setFollowUpEmail(null);
    setRefinement('');
    onClose();
  };

  const mailtoLink = useMemo(() => {
    if (!followUpEmail) return '';

    const recipient =
      followUpEmailRecipient?.email ||
      followUpEmailRecipient?.user?.email ||
      '';
    return `mailto:${recipient}?subject=${encodeURIComponent(followUpEmail.title)}&body=${encodeURIComponent(followUpEmail.body)}`;
  }, [followUpEmail, followUpEmailRecipient]);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl mt-16">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Follow-up Email</h2>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Select
                  value={selectedSender}
                  onValueChange={setSelectedSender}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select sender">
                      {selectedSender}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {internalParticipantsUniqueNames.length > 0 && internalParticipantsUniqueNames.map((participant) => (
                      <SelectItem key={participant} value={participant}>
                        <span className="capitalize">{participant}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-[2]">
                <Input
                  value={refinement}
                  onChange={(e) => setRefinement(e.target.value)}
                  placeholder="Optional: Make it formal, add details about X, etc."
                />
              </div>
            </div>

            <Button
              onClick={() => generateFollowUpEmail(refinement)}
              disabled={isGenerating || !selectedSender}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <SparklesIcon className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <SparklesIcon className="w-4 h-4 mr-2" />
                  {followUpEmail ? 'Regenerate Email' : 'Generate Email'}
                </>
              )}
            </Button>

            {followUpEmail && (
              <>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Subject</label>
                  <Input
                    value={followUpEmail.title}
                    readOnly
                    className="w-full"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Message</label>
                  <Textarea
                    value={followUpEmail.body}
                    readOnly
                    className="min-h-[200px]"
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={handleClose}>
                    Close
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        `${followUpEmail.title}\n\n${followUpEmail.body}`,
                      );
                    }}
                  >
                    <ClipboardIcon className="w-4 h-4 mr-2" />
                    Copy to Clipboard
                  </Button>
                  <Button asChild variant="default">
                    <a href={mailtoLink}>
                      <MailIcon className="w-4 h-4 mr-2" />
                      Send Email
                    </a>
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
