import CallTypeFilter from '@/common/AnalyticsOld/DashboardTab/Filters/CallTypeFilter';
import DatesFilter from '@/common/AnalyticsOld/DashboardTab/Filters/DateFilter';
import RepsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/RepsFilter';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sheet,
  SheetContentLight,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Switch } from '@/components/ui/switch';
import { useIntegrations } from '@/hooks/useIntegrations';
import { AgentCallType } from '@/lib/Agent/types';
import { DateFilterType } from '@/lib/AnalyticsOld/types';
import IntegrationService from '@/lib/Integrations';
import { Integration, IntegrationStatus } from '@/lib/Integrations/types';
import { Filter } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { FilterState } from '../index';
import CustomCallTypesFilter, {
  CustomCallTypesIds,
} from '@/common/AnalyticsOld/DashboardTab/Filters/CustomCallTypesFilter';
import useUserSession from '@/hooks/useUserSession';
import DurationFilter from '@/common/Calls/AIRoleplay/List/filtersControls/DurationFilter';
import { FilterType } from '@/common/Calls/AIRoleplay/List/common';
import ScorecardsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ScorecardFilter';
import ScorecardsSectionsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ScorecardsSectionsFilter';
import ScorecardCriterionsStatusFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ScorecardCriterionsStatusFilter';
import ScorecardsCriterionsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/ScorecardsCriterionsFilter';

interface IProps {
  open: boolean;
  onFiltersPanelClose: () => void;
  filtersState: FilterState;
  onFiltersUpdated: (sf: FilterState) => void;
}
export default function FiltersPanel({
  open,
  onFiltersPanelClose,
  filtersState,
  onFiltersUpdated,
}: IProps) {
  const { isAdmin, isLoggedIn, dbOrg } = useUserSession();

  const { data: dbIntegrations, isLoading: isLoadingIntegrations } =
    useIntegrations();

  const [allIntegrations, setAllIntegrations] = useState<Integration[]>([]);

  const [selectedIntegartion, setSelectedIntegration] = useState<number>();

  /******************************/
  /************ INIT **********/
  /******************************/

  useEffect(() => {
    if (!isLoadingIntegrations && dbIntegrations) {
      setAllIntegrations(dbIntegrations);
    }
  }, [dbIntegrations, isLoadingIntegrations]);

  useEffect(() => {
    if (filtersState) {
      setSelectedIntegration(filtersState.integrationId);
    }
  }, [filtersState]);

  /******************************/
  /************ ACTIONS *********/
  /******************************/

  const updateCustomCallTypes = (customCallTypesIds: CustomCallTypesIds) => {
    if (onFiltersUpdated) {
      onFiltersUpdated({
        ...filtersState,
        organizationCustomCallTypesIds: customCallTypesIds.organization,
        teamsCustomCallTypesIds: customCallTypesIds.teams,
        usersCustomCallTypesIds: customCallTypesIds.users,
      });
    }
  };

  const updateDatesFilter = (n: DateFilterType) => {
    filtersState.dateFilter = n;

    if (onFiltersUpdated) {
      onFiltersUpdated({ ...filtersState });
    }
  };

  const updateReps = (reps: string[]) => {
    filtersState.reps = reps.map((r) => parseInt(r));
    if (onFiltersUpdated) {
      onFiltersUpdated({ ...filtersState });
    }
  };

  const updateCallTypes = (callTypes: AgentCallType[]) => {
    if (onFiltersUpdated) {
      onFiltersUpdated({ ...filtersState, callTypes });
    }
  };

  const updateScorecards = (scorecards: number[]) => {
    filtersState[FilterType.SCORECARDS] = scorecards;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateIntegration = (integrationId?: number) => {
    if (onFiltersUpdated) {
      onFiltersUpdated({ ...filtersState, integrationId });
    }
  };

  const updateDuration = (min: number | undefined, max: number | undefined) => {
    if (onFiltersUpdated) {
      onFiltersUpdated({ ...filtersState, minDuration: min, maxDuration: max });
    }
  };

  const updateScorecardsSections = (sections: string[]) => {
    filtersState[FilterType.SCORECARDS_SECTIONS] = sections;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecardsCriterions = (criterions: string[]) => {
    filtersState[FilterType.SCORECARDS_CRITERIONS] = criterions;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateScorecardCriterionsStatus = (criterionStatuses: string[]) => {
    filtersState[FilterType.SCORECARDS_CRITERIONS_STATUS] = criterionStatuses;

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  /******************************/
  /************ RENDER **********/
  /******************************/

  return (
    <Sheet open={open} onOpenChange={onFiltersPanelClose}>
      <SheetContentLight className="flex flex-col pr-0">
        <SheetHeader>
          <SheetTitle className="flex items-center">
            <Filter className="mr-2" size={16} /> Filters
          </SheetTitle>
        </SheetHeader>
        <ScrollArea className="grow pr-8">
          <div>
            <div className="text-xs font-semibold mb-1">Date range</div>
            <DatesFilter
              current={filtersState.dateFilter}
              onDatesUpdated={updateDatesFilter}
            />
          </div>
          <div>
            <DurationFilter
              min={filtersState.minDuration}
              max={filtersState.maxDuration}
              onDurationUpdated={updateDuration}
            />
          </div>
          {(!dbOrg?.onlyAdminCanViewAllCalls || isAdmin) && (
            <div className="mt-4">
              <div className="text-xs font-semibold mb-1">Reps</div>
              <RepsFilter
                current={filtersState.reps}
                onRepsUpdated={updateReps}
              />
            </div>
          )}
          <div className="mt-4">
            <div className="text-xs font-semibold mb-1">Call Type</div>
            <CallTypeFilter
              current={filtersState.callTypes}
              onCallTypesUpdated={updateCallTypes}
            />
          </div>
          {isAdmin && (
            <div className="mt-4">
              <div className="text-xs font-semibold mb-1">Custom Call Type</div>
              <CustomCallTypesFilter
                current={{
                  organization:
                    filtersState.organizationCustomCallTypesIds || [],
                  teams: filtersState.teamsCustomCallTypesIds || [],
                  users: filtersState.usersCustomCallTypesIds || [],
                }}
                onCallTypesUpdated={updateCustomCallTypes}
              />
            </div>
          )}
          <div className="mt-4">
            <div className="text-xs font-semibold">Scorecards:</div>
            <ScorecardsFilter
              current={filtersState[FilterType.SCORECARDS]}
              onFiltersUpdated={updateScorecards}
              locked={!isLoggedIn}
              lockedMessage={'Book a demo to access scorecards'}
            />
          </div>
          <div className="mt-4">
            <div className="text-xs font-semibold">Scorecard sections:</div>
            <ScorecardsSectionsFilter
              current={filtersState[FilterType.SCORECARDS_SECTIONS]}
              onSectionsUpdated={updateScorecardsSections}
              scorecards={filtersState[FilterType.SCORECARDS]}
              locked={!isLoggedIn}
            />
          </div>
          <div className="mt-4">
            <div className="text-xs font-semibold">Scorecard criterions:</div>
            <ScorecardsCriterionsFilter
              current={filtersState[FilterType.SCORECARDS_CRITERIONS]}
              onCriterionsUpdated={updateScorecardsCriterions}
              sections={filtersState[FilterType.SCORECARDS_SECTIONS]}
              locked={!isLoggedIn}
            />
          </div>
          <div className="mt-4">
            <div className="text-xs font-semibold">
              Scorecard criterions status:
            </div>
            <ScorecardCriterionsStatusFilter
              current={filtersState[FilterType.SCORECARDS_CRITERIONS_STATUS]}
              onFilterUpdated={updateScorecardCriterionsStatus}
              locked={!isLoggedIn}
            />
          </div>
          <div className="mt-4">
            <div className="text-xs font-semibold mb-1">Source</div>
            <Select
              onValueChange={(v: string) => {
                if (v == 'clear') {
                  updateIntegration();
                } else {
                  updateIntegration(parseInt(v));
                }
              }}
              value={String(selectedIntegartion)}
            >
              <SelectTrigger className="py-0 h-[34px]">
                <SelectValue placeholder="" />
              </SelectTrigger>
              <SelectContent>
                {allIntegrations?.map((i: Integration) => {
                  const provider = i.provider;
                  if (provider && i.status == IntegrationStatus.ACTIVE) {
                    let isRealCallsService = false;
                    provider.services?.map((s: any) => {
                      if (s.type == 'REAL_CALL_SCORING') {
                        isRealCallsService = true;
                      }
                    });
                    if (isRealCallsService) {
                      return (
                        <SelectItem key={'int-' + i.id} value={String(i.id)}>
                          <div className="flex items-center">
                            <Image
                              src={IntegrationService.getProviderLogoUrl(
                                provider.logoUrl,
                              )}
                              alt={`${provider.companyName} Logo`}
                              width={20}
                              height={20}
                              className="mr-1"
                            />
                            <div>{i.name}</div>
                          </div>
                        </SelectItem>
                      );
                    }
                  }
                })}
                <SelectItem value={'clear'}>-- Clear Filter--</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4">
            <div className="text-xs font-semibold mb-1">Private calls only</div>
            <Switch
              checked={filtersState.isPrivate}
              onCheckedChange={(value) => {
                onFiltersUpdated({ ...filtersState, isPrivate: value });
              }}
            />
          </div>
        </ScrollArea>
      </SheetContentLight>
    </Sheet>
  );
}
