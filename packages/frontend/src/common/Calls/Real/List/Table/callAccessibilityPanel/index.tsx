import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UserDto } from '@/lib/User/types';
import { Switch } from '@/components/ui/switch';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Globe, GlobeLock, Loader2Icon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AnimatePresence, motion } from 'framer-motion';
import RepsFilter from '@/common/Analytics/DashboardTab/Filters/RepsFilter';
import { RealCallsService } from '@/lib/Integrations';

interface IProps {
  callInfo: {
    id: number;
    title: string;
    sharedWith: UserDto[];
    isPrivate: boolean;
  };
  modalOpen: boolean;
  setModalOpen: (open: boolean) => void;
}

export default function CallAccessibilityPanel({
  callInfo,
  modalOpen,
  setModalOpen,
}: IProps) {
  const queryClient = useQueryClient();

  const [callState, setCallState] = useState<{
    id: number;
    title: string;
    sharedWith: UserDto[];
    isPrivate: boolean;
  }>(callInfo);
  const [currentSharedWith, setCurrentSharedWith] = useState<number[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    setCurrentSharedWith(callInfo.sharedWith.map((u: UserDto) => u.id));
  }, [callInfo]);

  const editCallPrivacy = async () => {
    setIsSaving(true);

    try {
      await RealCallsService.updateCallPrivacy(
        callState.id,
        callState.isPrivate,
        currentSharedWith,
      );
    } catch (e) {
      console.error(e);
    }

    queryClient.invalidateQueries({ queryKey: ['list-real-calls'] });

    setIsSaving(false);
    setModalOpen(false);
  };

  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Edit accessibility for {callInfo.title}
          </DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="flex items-center mt-4">
          <div className="mr-4 flex items-center">
            <div className="mr-1 text-muted-foreground">
              {callState.isPrivate ? (
                <GlobeLock size={16} />
              ) : (
                <Globe size={16} />
              )}
            </div>
            <div>Private</div>
          </div>

          <Switch
            checked={callState.isPrivate}
            onCheckedChange={(value) => {
              setCallState({ ...callState, isPrivate: value });
            }}
          />
        </div>
        <AnimatePresence>
          {callState.isPrivate && (
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: 'auto' }}
              exit={{ height: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4 mb-4"
            >
              <div>Share this call with:</div>
              <div className="mt-2">
                <RepsFilter
                  current={currentSharedWith}
                  onRepsUpdated={(reps: string[]) => {
                    setCurrentSharedWith(reps.map((v: string) => parseInt(v)));
                  }}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <DialogFooter>
          <Button onClick={editCallPrivacy} disabled={isSaving}>
            {isSaving ? <Loader2Icon className="animate-spin" /> : 'Save'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
