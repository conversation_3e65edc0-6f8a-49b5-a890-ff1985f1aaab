import RealCall, { RealCallStatus } from '@/lib/Integrations/RealCalls/types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  CopyIcon,
  DownloadCloudIcon,
  DownloadIcon,
  Globe,
  GlobeLock,
  Loader2,
  LockIcon,
  Share2,
  Trash2Icon,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import useUserSession from '@/hooks/useUserSession';
import { UserDto } from '@/lib/User/types';
import { ForwardedRef, forwardRef } from 'react';

interface IProps {
  call: RealCall;
  doDeleteCall: (callId: number, callTitle: string) => void;
  doEditPrivateCall: (
    callId: number,
    callTitle: string,
    sharedWith: UserDto[],
    isPrivate: boolean,
  ) => void;
  downloadTranscript: (call: RealCall) => void;
  copyTranscript: (call: RealCall) => void;
  openShareDialog: (callId: number) => void;
  downloadingTranscript: boolean;
  copyingTranscript: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const DropDownMenu = forwardRef<any, IProps>((props, ref) => {
  const { isAdmin, userId } = useUserSession();
  const canEdit = props.call.callerId === userId || isAdmin;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild ref={ref}>
        <Button variant="ghost" size="icon" className="p-0 rounded-full">
          <span className="sr-only">Open menu</span>
          <DotsHorizontalIcon className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {canEdit && (
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              props.openShareDialog(props.call.id);
            }}
          >
            <Share2 className="w-4 h-4 mr-2 text-muted-foreground" />
            <span>Share</span>
          </DropdownMenuItem>
        )}
        {!!props.call?.mediaUrls?.audio?.url && (
          <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
            <a
              download
              href={props.call.mediaUrls.audio.url}
              className="flex flex-row"
            >
              <DownloadCloudIcon className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>Download audio</span>
            </a>
          </DropdownMenuItem>
        )}
        {!!props.call?.mediaUrls?.video?.url && (
          <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
            <a
              download
              href={props.call.mediaUrls.video.url}
              className="flex flex-row"
            >
              <DownloadCloudIcon className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>Download video</span>
            </a>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem
          className="cursor-pointer"
          disabled={props.copyingTranscript}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            props.copyTranscript(props.call);
          }}
        >
          {props.copyingTranscript ? (
            <Loader2 className="w-4 h-4 mr-2 text-muted-foreground animate-spin" />
          ) : (
            <CopyIcon className="w-4 h-4 mr-2 text-muted-foreground" />
          )}
          <span>Copy transcript</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          className="cursor-pointer"
          disabled={props.downloadingTranscript}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            props.downloadTranscript(props.call);
          }}
        >
          {props.downloadingTranscript ? (
            <Loader2 className="w-4 h-4 mr-2 text-muted-foreground animate-spin" />
          ) : (
            <>
              <DownloadIcon className="w-4 h-4 mr-2 text-muted-foreground" />
            </>
          )}
          <span>Download transcript</span>
        </DropdownMenuItem>
        {canEdit && (
          <>
            <DropdownMenuItem
              className="cursor-pointer "
              onClick={(e) => {
                e.stopPropagation();
                props.doEditPrivateCall(
                  props.call.id,
                  props.call.title,
                  props.call.sharedWith || [],
                  props.call.isPrivate,
                );
              }}
            >
              {props.call.isPrivate && (
                <>
                  <Globe className="text-muted-foreground w-4 h-4 mr-2 " />
                  <span>Share...</span>
                </>
              )}

              {!props.call.isPrivate && (
                <>
                  <GlobeLock className="text-muted-foreground w-4 h-4 mr-2 " />
                  <span>Mark as private</span>
                </>
              )}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="cursor-pointer text-red-500"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                props.doDeleteCall(props.call.id, props.call.title);
              }}
            >
              <Trash2Icon className="w-4 h-4 mr-2 " />
              <span>Delete call</span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
});
DropDownMenu.displayName = 'DropDownMenu';
export default DropDownMenu;
