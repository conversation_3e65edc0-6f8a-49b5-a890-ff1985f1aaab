import { CustomCallTypesIds } from '@/common/AnalyticsOld/DashboardTab/Filters/CustomCallTypesFilter';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import CustomCallTypesFilter from '@/common/AnalyticsOld/DashboardTab/Filters/CustomCallTypesFilter';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import RealCallsService from '@/lib/Integrations/RealCalls';
import { CategoryType } from '@/lib/Integrations/RealCalls/types';

interface IProps {
  open: boolean;
  onClose: () => void;
  callId: number;
  currentCategoryType: CategoryType;
  currentCustomCallTypeId: number;
}

export default function ChangeCallTypeModal({
  open,
  onClose,
  callId,
  currentCategoryType,
  currentCustomCallTypeId,
}: IProps) {
  const [customCallType, setCustomCallType] = useState<CustomCallTypesIds>({
    organization:
      currentCategoryType === CategoryType.ORGANIZATION
        ? [currentCustomCallTypeId]
        : [],
    teams:
      currentCategoryType === CategoryType.TEAM
        ? [currentCustomCallTypeId]
        : [],
    users:
      currentCategoryType === CategoryType.USER
        ? [currentCustomCallTypeId]
        : [],
  });

  const changeCallType = () => {
    if (!customCallType) return;

    if (customCallType?.organization.length === 1) {
      if (
        currentCategoryType === CategoryType.ORGANIZATION &&
        currentCustomCallTypeId === customCallType.organization[0]
      ) {
        return;
      }

      RealCallsService.updateCallType(
        callId,
        customCallType.organization[0],
        CategoryType.ORGANIZATION,
      );
    } else if (customCallType?.teams.length === 1) {
      if (
        currentCategoryType === CategoryType.TEAM &&
        currentCustomCallTypeId === customCallType.teams[0]
      ) {
        return;
      }

      RealCallsService.updateCallType(
        callId,
        customCallType.teams[0],
        CategoryType.TEAM,
      );
    } else if (customCallType?.users.length === 1) {
      if (
        currentCategoryType === CategoryType.USER &&
        currentCustomCallTypeId === customCallType.users[0]
      ) {
        return;
      }

      RealCallsService.updateCallType(
        callId,
        customCallType.users[0],
        CategoryType.USER,
      );
    }
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={cn('close-btn mt-16 flex flex-col')}>
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Change call type
          </DialogTitle>
        </DialogHeader>

        <div>
          <CustomCallTypesFilter
            current={{
              organization: customCallType.organization,
              teams: customCallType.teams,
              users: customCallType.users,
            }}
            onCallTypesUpdated={(n: CustomCallTypesIds) => {
              setCustomCallType(n);
            }}
            multiple={false}
          />
        </div>

        <DialogFooter>
          <div className="flex flex-row items-center justify-end w-full gap-2">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>

            <Button onClick={changeCallType} disabled={!customCallType}>
              Change call type
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
