import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import ConfirmationModal from '@/components/ConfirmationModal';
import Table, {
  TableCell,
  TableCellHead,
  TableContent,
  TableFooter,
  TableRow,
} from '@/components/ui/Hyperbound/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useListRealCalls } from '@/hooks/useRealCalls';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import { RealCallsService } from '@/lib/Integrations';
import RealCall, {
  RealCallScoringStatus,
  RealCallsPublicDetailsDto,
  RealCallStatus,
} from '@/lib/Integrations/RealCalls/types';
import { UserDto } from '@/lib/User/types';
import LinksManager from '@/lib/linksManager';
import { cn, formatRealCallTranscript, timeAgo } from '@/lib/utils';
import { useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { GlobeLock, Info, LayoutListIcon, Loader2Icon, LockIcon, SkipForward } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { FilterState } from '..';
import { RealCallShareDialog } from '../../NewSummary/shareDialog';
import DropDownMenu from './DropDownMenu';
import CallAccessibilityPanel from './callAccessibilityPanel';

import PaginationControls from './paginationControls';
import { CallsSortingParam, SortDirection, SortTypes } from '@/common/Calls/AIRoleplay/List/common';
import { CaretSortIcon, CaretUpIcon } from '@radix-ui/react-icons';
import { CaretDownIcon } from '@radix-ui/react-icons';

const COLUMNS = [
  {
    label: 'Started At',
    prop: 'createdAt',
  },
  {
    label: 'Duration',
    prop: 'duration',
  },
  {
    label: 'Title',
    prop: 'title',
  },
  {
    label: 'Rep',
    prop: 'caller',
  },
  {
    label: 'Call Type',
    prop: 'callType',
  },
  {
    label: 'Participants',
    prop: 'participants',
  },
  {
    label: 'Source',
    prop: 'integration',
  },
  {
    label: 'Score / 100',
    prop: 'score',
  },
  {
    label: '',
    prop: 'actions-col',
  },
];

interface IProps {
  filters: FilterState;
  updateFilters: (fs: FilterState) => void;
}

export default function RealCallsTable({ filters, updateFilters }: IProps) {
  // console.log(filters);
  const { userId } = useUserSession();
  const [sortBy, setSortBy] = useState<CallsSortingParam[]>([]);
  const {
    data: callsDb,
    isLoading: isLoadingCalls,
    refetch: refetchCalls,
    isRefetching: isRefetchingCalls,
  } = useListRealCalls(filters, true, sortBy);
  const [totNumberOfResults, setTotNumberOfResults] = useState<number>(0);
  const [calls, setCalls] = useState<RealCall[]>([]);
  const dropdownMenuRef = useRef<HTMLButtonElement>(null);
  const { goToPage } = useRouting();
  const queryClient = useQueryClient();
  useEffect(() => {
    if (!isLoadingCalls) {
      if (callsDb) {
        setTotNumberOfResults(callsDb.count);
        setCalls(callsDb.calls);

        if (
          !isRefetchingCalls &&
          (callsDb.calls as any[]).some(
            (c) => c.status === RealCallStatus.SCORING,
          )
        ) {
          startRescoringMonitoring();
          return () => {
            stopRescoringMonitoring();
          };
        }
      }
    }
  }, [isLoadingCalls, callsDb, isRefetchingCalls]);

  /***********************************/
  /************* ACTIONS **************/
  /***********************************/

  const updatePagination = (from: number, numberOfResults: number) => {
    //update url:

    filters.from = from;
    filters.numberOfResults = numberOfResults;

    updateFilters({ ...filters });
  };

  const openCallPreview = (call: RealCall) => {
    if (
      call.isPrivate &&
      call.callerId != userId &&
      !call.sharedWith?.find((u) => u.id == userId)
    ) {
      //restrict access to private calls
      return;
    }
    goToPage(LinksManager.realCalls(call.id));
  };

  //------- monitor rescoring progress

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const startRescoringMonitoring = () => {
    timeoutRef.current = setTimeout(async () => {
      if (refetchCalls) {
        refetchCalls();
      }
    }, 5000);
  };

  const stopRescoringMonitoring = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  //--------- transcription
  const [copyingTranscript, setCopyingTranscript] = useState<boolean>(false);
  const [downloadingTranscript, setDownloadingTranscript] =
    useState<boolean>(false);
  const [transcriptions, setTranscriptions] = useState<Record<number, any>>({});

  const fetchTranscription = async (callId: number) => {
    const transcription = await RealCallsService.getTranscription(callId);
    setTranscriptions((prev) => ({ ...prev, [callId]: transcription }));
    return transcription;
  };

  //--------- delete call
  const [callToDeleteId, setCallToDeleteId] = useState<number | null>(null);
  const [callToDeleteTitle, setCallToDeleteTitle] = useState<string>('');
  const [showDeleteConfirmation, setShowDeleteConfirmation] =
    useState<boolean>(false);
  const [shareDialogDetails, setShareDialogDetails] = useState<{
    callId: number;
    publicCallDetails: RealCallsPublicDetailsDto;
  } | null>(null);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);

  const startDeleteCall = (callId: number, callTitle: string) => {
    setCallToDeleteId(callId);
    setCallToDeleteTitle(callTitle);
    setShowDeleteConfirmation(true);
  };

  const cancelDelete = () => {
    setShowDeleteConfirmation(false);
  };

  const confirmDelete = async () => {
    if (callToDeleteId) {
      try {
        await RealCallsService.deleteCall(callToDeleteId);
      } catch (e) {
        console.error(e);
      }
    }
    queryClient.invalidateQueries({ queryKey: ['list-real-calls'] });
    setShowDeleteConfirmation(false);
  };

  //--------- call accessibility

  const [showCallAccessibilityPanel, setShowCallAccessibilityPanel] =
    useState<boolean>(false);
  const [callAccessibilityEdit, setCallAccessibilityEdit] = useState<{
    id: number;
    title: string;
    sharedWith: UserDto[];
    isPrivate: boolean;
  }>();
  const startEditCallAccessibility = (
    callId: number,
    callTitle: string,
    sharedWith: UserDto[],
    isPrivate: boolean,
  ) => {
    setCallAccessibilityEdit({
      id: callId,
      title: callTitle,
      sharedWith,
      isPrivate,
    });
    setShowCallAccessibilityPanel(true);
  };

  /***********************************/
  /************* RENDER **************/
  /***********************************/

  const renderLoading = () => {
    const allRows = [];

    let i = 0;
    while (i < filters.numberOfResults) {
      const allCells = [];
      for (const c of COLUMNS) {
        allCells.push(
          <TableCell>
            <Skeleton className="w-3/4 h-[26px] rounded" />
          </TableCell>,
        );
      }

      allRows.push(
        <TableRow className="cursor-not-allowed">{allCells}</TableRow>,
      );
      i++;
    }

    return allRows;
  };

  const str_pad_left = (a: string, pad: string, length: number) => {
    return (new Array(length + 1).join(pad) + a).slice(-length);
  };

  const getFormattedTranscript = (call: RealCall, transcript?: any) =>
    formatRealCallTranscript(
      transcript || transcriptions[call.id],
      call?.id,
      call?.integration?.provider?.companyName || 'Unknown',
    );

  const [transcriptCopied, setTranscriptCopied] = useState(false);

  useEffect(() => {
    if (transcriptCopied) {
      const timeout = setTimeout(() => {
        setTranscriptCopied(false);
      }, 1500);
      return () => clearTimeout(timeout);
    }
  }, [transcriptCopied]);

  const copyTranscript = async (call: RealCall) => {
    let transcript = transcriptions[call.id];
    if (!transcript) {
      setCopyingTranscript(true);
      transcript = await fetchTranscription(call.id);
    }
    const formattedTranscript = getFormattedTranscript(call, transcript);
    navigator.clipboard.writeText(formattedTranscript);
    setTranscriptCopied(true);
    setCopyingTranscript(false);
    dropdownMenuRef.current?.dispatchEvent(
      new KeyboardEvent('keydown', { key: 'Escape' }),
    );
  };

  const downloadTranscript = async (call: RealCall) => {
    let transcript = transcriptions[call.id];
    if (!transcript) {
      setDownloadingTranscript(true);
      transcript = await fetchTranscription(call.id);
    }
    const formattedTranscript = getFormattedTranscript(call, transcript);

    const blob = new Blob([formattedTranscript], {
      type: 'text/plain',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-real-${call.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    setDownloadingTranscript(false);
    dropdownMenuRef.current?.dispatchEvent(
      new KeyboardEvent('keydown', { key: 'Escape' }),
    );
  };

  const openShareDialog = async (callId: number) => {
    setShareDialogDetails(null);
    setShareDialogOpen(true);
    const publicDetails = await RealCallsService.getPublicDetails(callId);
    if (!publicDetails) {
      setShareDialogOpen(false);
      return;
    }
    setShareDialogDetails({
      callId,
      publicCallDetails: publicDetails,
    });
  };

  const renderRow = (call: RealCall, inactive: boolean = false) => {
    let categoryName = '';
    let categoryIsFallback = false;
    const explanation = call.categoryExplanation;
    if (call.orgCategory) {
      categoryName = call.orgCategory.label;
    } else if (call.teamCategory) {
      categoryName = call.teamCategory.label;
    } else if (call.userCategory) {
      categoryName = call.userCategory.label;
    }
    if (!categoryName) {
      categoryName = 'None'
      categoryIsFallback = true;
    }

    const allCells = [];

    let blur = false;
    if (
      call.isPrivate &&
      call.callerId != userId &&
      !call.sharedWith?.find((u) => u.id == userId)
    ) {
      blur = true;
    }

    for (const c of COLUMNS) {
      if (c.prop == 'createdAt') {
        let content = '';
        content = dayjs(call.callDate).format('MMM D, h:mm A');
        allCells.push(
          <TableCell key={c.prop + call.id}>
            <div>{timeAgo(new Date(call.callDate))}</div>
            <div className="text-muted-foreground">{content}</div>
          </TableCell>,
        );
        /************************************************* */
      } else if (c.prop == 'duration') {
        let duration = '00:00';
        if (call.duration) {
          const minutes = Math.floor(call.duration / 60);
          const seconds = call.duration - minutes * 60;

          duration =
            str_pad_left(String(minutes), '', 2) +
            ':' +
            str_pad_left(String(seconds), '0', 2);
        }
        allCells.push(<TableCell key={c.prop + call.id}>{duration}</TableCell>);
        /************************************************* */
      } else if (c.prop == 'title') {
        allCells.push(
          <TableCell
            key={c.prop + call.id}
            className={cn('max-w-[260px] truncate ', {
              blur: blur,
            })}
            title={blur ? '' : call.title}
          >
            {blur
              ? 'Hi Sai, how are you? hope everything is great! :)'
              : call.title}
          </TableCell>,
        );
        /************************************************* */
      } else if (c.prop == 'caller') {
        const caller: any | undefined = call.caller;
        if (caller) {
          const firstName = caller.firstName;
          const lastName = caller.lastName;

          const content = (
            <Button
              variant={'outline'}
              onClick={(e) => {
                e.stopPropagation();
                goToPage(LinksManager.members(`${caller?.id}`));
              }}
              className="space-x-2 pl-2 pr-3 py-1 rounded-full hover:bg-muted/80 hover:transition-all duration-300"
            >
              <Avatar className="w-6 h-6">
                {caller?.avatar && <AvatarImage src={caller?.avatar} />}
                <AvatarFallback className="text-sm capitalize">
                  {firstName?.charAt(0)}
                  {lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="capitalize">
                {firstName} {lastName}
              </div>
            </Button>
          );

          allCells.push(
            <TableCell key={c.prop + call.id}>{content}</TableCell>,
          );
        } else {
          allCells.push(<TableCell key={c.prop + call.id}>&nbsp;</TableCell>);
        }

        /************************************************* */
      } else if (c.prop == 'participants') {
        let particpants = '';
        call.parties?.map((p: any) => {
          if (particpants == '') {
            particpants = p.name || p.email;
          } else {
            particpants += ', ' + p.name || p.email;
          }
        });
        allCells.push(
          <TableCell
            key={c.prop + call.id}
            className={cn('max-w-[200px] truncate ', { blur: blur })}
            title={blur ? '' : particpants} 
          >
            {blur ? 'I need a snack...urgently!' : particpants}
          </TableCell>,
        );
        /************************************************* */
      } else if (c.prop == 'score') {
        if (call.scoringStatus == RealCallScoringStatus.SCORING_SKIPPED_DUE_TO_PRIVACY_RULE) {
          allCells.push(
            <TableCell
              key={c.prop + call.id}
              className={cn({ blur: blur })}
            >
              <TooltipProvider> 
                <Tooltip>
                  <TooltipTrigger className="cursor-help underline decoration-dotted">
                    <div className="flex flex-row gap-1 bg-yellow-500 text-xs text-white rounded-full px-2 py-1">
                      <LockIcon className="w-4 h-4" />
                      Privacy Protected
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      { call.privacyRuleExplanation || 'No explanation provided, please contact support.'}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TableCell>,
          );
        } else if(call.scoringStatus == RealCallScoringStatus.SCORING_SKIPPED_DUE_TO_CATEGORIZATION_RULE) {
          allCells.push(
            <TableCell
              key={c.prop + call.id}
              className={cn({ blur: blur })}
            >
              <TooltipProvider> 
                <Tooltip>
                  <TooltipTrigger>
                  <div className="flex flex-row gap-1 text-xs text-muted-foreground">
                    <SkipForward className="w-4 h-4" />
                    Skipped
                  </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      Scoring skipped because the call type '{categoryName}' has the behavior 'Save Only'.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TableCell>,
          );
        } else if (
          call.status == RealCallStatus.SCORING && 
          !call.scorecard &&
          !blur
        ) {
          allCells.push(
            <TableCell
              key={c.prop + call.id}
              className={cn('text-xs text-muted-foreground', { blur: blur })}
            >
              <Loader2Icon className="animate-spin" size={16} />
            </TableCell>,
          );
        } else if (call.status == RealCallStatus.SKIPPED && categoryIsFallback) {
          allCells.push(
            <TableCell
              key={c.prop + call.id}
              className={cn({ blur: blur })}
            >
              <TooltipProvider> 
                <Tooltip>
                  <TooltipTrigger>
                  <div className="text-xs text-muted-foreground">
                    Not Scored
                  </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      Scoring skipped because no call type could be determined. This is a fallback behavior.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TableCell>,
          );
        } else {
          const sc = call.scorecard;
          let score = '0';
          if (sc) {
            if (sc.scorecard) {
              score = call.scorecard?.finalScore?.toString() || '0';
            }
          }
          allCells.push(
            <TableCell key={c.prop + call.id} className={cn({ blur: blur })}>
              <TooltipProvider> 
                <Tooltip>
                  <TooltipTrigger>
                    { blur ? '100%' : score }
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {
                        categoryIsFallback 
                          ? `Scored because no custom call type '${categoryName}' has been set. This is a fallback behavior.`
                          : `Scored because the call type '${categoryName}' has the behavior 'Save and Score'.`
                      }
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TableCell>,
          );
        }

        /************************************************* */
      } else if (c.prop == 'integration') {
        allCells.push(
          <TableCell key={c.prop + call.id} className="">
            {call.integration.name}
          </TableCell>,
        );
      } else if (c.prop == 'callType') {
        if (call.callType) {
          const Icon =
            CALL_TYPE_TO_ICON[call.callType as keyof typeof CALL_TYPE_TO_ICON]
              .Icon;

          
          let content;
          if (explanation) {
            content = (
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className={cn(
                        'mr-2 rounded-full bg-gray-100 items-center py-[6px] px-3 inline-flex ',
                        { blur: blur },
                      )}
                    >
                      {categoryName != 'None' && Icon && <Icon className="mr-1 h-4 w-4" />}
                      <p className="max-w-26 flex items-center">
                        {categoryName}
                        <Info className="ml-1 h-4 w-4" />
                      </p>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="max-w-[200px] text-wrap"
                  >
                    {explanation}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            );
          } else {
            content = (
              <div
                className={cn(
                  'mr-2 rounded-full bg-gray-100 items-center py-[6px] px-3 inline-flex ',
                  { blur: blur },
                )}
              >
                {Icon && <Icon className="mr-1 h-4 w-4" />}
                <p className="max-w-26">{categoryName}</p>
              </div>
            );
          }
          allCells.push(
            <TableCell key={c.prop + call.id} className="whitespace-nowrap">
              {content}
            </TableCell>,
          );
        } else {
          allCells.push(
            <TableCell key={c.prop + call.id} className="">
              &nbsp;
            </TableCell>,
          );
        }
      } else if (c.prop == 'actions-col') {
        allCells.push(
          <TableCell
            key={c.prop + call.id}
            className="flex items-center space-x-1"
          >
            <div>
              {call.isPrivate && (
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger>
                      <div>
                        <GlobeLock
                          size={16}
                          className="text-muted-foreground mr-2"
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">Private</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <div className="group-hover:visible invisible">
              <DropDownMenu
                ref={dropdownMenuRef}
                call={call}
                doDeleteCall={startDeleteCall}
                doEditPrivateCall={startEditCallAccessibility}
                downloadTranscript={downloadTranscript}
                copyTranscript={copyTranscript}
                openShareDialog={openShareDialog}
                downloadingTranscript={downloadingTranscript}
                copyingTranscript={copyingTranscript}
              />
            </div>
          </TableCell>,
        );
      }
    }

    return allCells;
  };

  const renderTable = () => {
    const allRows = [];

    if (calls && calls.length > 0) {
      for (const call of calls) {
        const cells = renderRow(call);

        allRows.push(
          <TableRow
            key={call.id}
            className="cursor-pointer hover:bg-gray-50 group"
            onClick={() => {
              openCallPreview(call);
            }}
          >
            {cells}
          </TableRow>,
        );
      }
    } else {
      allRows.push(
        <TableRow hideBorder={true}>
          <TableCell colSpan={COLUMNS.length} className="text-center pt-20">
            <div className="flex flex-col items-center justify-center">
              <div className="rounded-full bg-gray-100 p-2 text-muted-foreground">
                <LayoutListIcon size={16} />
              </div>
              <div className="mt-6 font-medium text-base">No calls found</div>
              <div className="mt-2 text-muted-foreground text-base w-[360px] text-wrap">
                Check your filters or import calls, then return to this page to
                review your performance.
              </div>
            </div>
          </TableCell>
        </TableRow>,
      );
    }

    return allRows;
  };

  const getSortIcon = (prop: string) => {
    const fieldSort = sortBy.find((s) => s.type === prop);
    if(prop == 'createdAt'){
      if(fieldSort){
        if(fieldSort.direction == SortDirection.DESC){
          return <CaretDownIcon className="ml-2 h-4 w-4" />;
        } else {
          return <CaretUpIcon className="ml-2 h-4 w-4" />;
        }
      } else{
        return <CaretDownIcon className="ml-2 h-4 w-4" />;
      }
    } else if(prop == 'score'){
      if(fieldSort){
        if(fieldSort.direction == SortDirection.DESC){
          return <CaretDownIcon className="ml-2 h-4 w-4" />;
        } else {
          return <CaretUpIcon className="ml-2 h-4 w-4" />;
        }
      } else{
        return <CaretSortIcon className="ml-2 h-4 w-4" />;
      }
    }
    return <CaretDownIcon className="ml-2 h-4 w-4" />;
  }

  const toggleSorting = (prop: string) => {
      setSortBy(
        (prev) => {
          const newSortBy = [...prev];
          const fieldSortIndex = prev.findIndex((s) => s.type === prop);
          if(fieldSortIndex === -1){
            newSortBy.push({
              type: prop as SortTypes,
              direction: SortDirection.ASC,
            })
          } else{
            const newFieldSort = {
              type: prop as SortTypes,
              direction: prev[fieldSortIndex]?.direction === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC,
            }
            newSortBy[fieldSortIndex] = newFieldSort;
          } 
          return newSortBy
        }
      )
  }

  return (
    <>
      <Table>
        <TableContent>
          <TableRow className="sticky top-0 z-50">
            {COLUMNS.map((c: any) => {
               if(c.prop == 'createdAt' || c.prop == 'score'){
                return   <TableCellHead
                key={c.prop}
                className="group cursor-pointer"
                onClick={() => toggleSorting(c.prop)}
              >
                <div className="flex items-center">
                  <div className="mr-2">{c.label}</div>
                  <div className="group-hover:text-black">{
                    getSortIcon(c.prop)
                    }</div>
                </div>
              </TableCellHead>
              } else{
                return <TableCellHead key={c.prop}>{c.label}</TableCellHead>;
              }
            })}
          </TableRow>
          {isLoadingCalls ? renderLoading() : renderTable()}
        </TableContent>
        <TableFooter>
          <PaginationControls
            from={filters.from}
            numberOfResults={filters.numberOfResults}
            totNumberOfRows={totNumberOfResults || 0}
            updatePagination={updatePagination}
          />
        </TableFooter>
      </Table>

      {showDeleteConfirmation && (
        <ConfirmationModal
          open={showDeleteConfirmation}
          onCancel={cancelDelete}
          onConfirm={confirmDelete}
          title={`Delete call`}
          description={`Do you want to delete: ${callToDeleteTitle}`}
        />
      )}
      {showCallAccessibilityPanel && callAccessibilityEdit && (
        <CallAccessibilityPanel
          callInfo={callAccessibilityEdit}
          modalOpen={showCallAccessibilityPanel}
          setModalOpen={setShowCallAccessibilityPanel}
        />
      )}
      <RealCallShareDialog
        callId={shareDialogDetails?.callId || 0}
        open={shareDialogOpen}
        setOpen={setShareDialogOpen}
        publicCallDetails={shareDialogDetails?.publicCallDetails}
        isLoading={!shareDialogDetails}
      />
    </>
  );
}
