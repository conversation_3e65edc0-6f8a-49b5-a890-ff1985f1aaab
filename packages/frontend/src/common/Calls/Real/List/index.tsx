import Splash from './Splash';
import { useEffect, useRef, useState } from 'react';
import useUserSession from '@/hooks/useUserSession';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import {
  AnalyticsFilterDateRange,
  DateFilterType,
} from '@/lib/AnalyticsOld/types';
import dayjs from 'dayjs';
import FiltersPanel from './Filters';
import RealCallsTable from './Table';
import { AgentCallType } from '@/lib/Agent/types';
import { Input } from '@/components/ui/input';
import { useTotalIntegrationsCount } from '@/hooks/useRealCalls';

export class FilterState {
  dateFilter: DateFilterType;
  minDuration: number | undefined;
  maxDuration: number | undefined;
  integrationId: number | undefined;
  from: number = 0;
  numberOfResults: number = 20;
  reps: number[] = [];
  callTypes: AgentCallType[] = [];
  search: string | undefined = '';
  isPrivate: boolean = false;
  organizationCustomCallTypesIds: number[] = [];
  teamsCustomCallTypesIds: number[] = [];
  usersCustomCallTypesIds: number[] = [];
  scorecards: number[] = [];
  scorecardSections: string[] = [];
  scorecardCriterions: string[] = [];
  scorecardCriterionsStatus: string[] = [];

  constructor(
    fromDate: Date,
    toDate: Date,
    minDuration: number | undefined,
    maxDuration: number | undefined,
    range: AnalyticsFilterDateRange,
    from: number,
    integrationId: number | undefined = undefined,
    reps: number[] = [],
    callTypes: AgentCallType[] = [],
    search: string | undefined = undefined,
    isPrivate: boolean = false,
    organizationCustomCallTypesIds: number[] = [],
    teamsCustomCallTypesIds: number[] = [],
    usersCustomCallTypesIds: number[] = [],
    scorecards: number[] = [],
    scorecardSections: string[] = [],
    scorecardCriterions: string[] = [],
    scorecardCriterionsStatus: string[] = [],
  ) {
    this.dateFilter = { fromDate, toDate, range };
    this.minDuration = minDuration;
    this.maxDuration = maxDuration;
    this.integrationId = integrationId;
    this.from = from;
    this.reps = reps;
    this.callTypes = callTypes;
    this.search = search;
    this.isPrivate = isPrivate;
    this.organizationCustomCallTypesIds = organizationCustomCallTypesIds;
    this.teamsCustomCallTypesIds = teamsCustomCallTypesIds;
    this.usersCustomCallTypesIds = usersCustomCallTypesIds;
    this.scorecards = scorecards;
    this.scorecardSections = scorecardSections;
    this.scorecardCriterions = scorecardCriterions;
    this.scorecardCriterionsStatus = scorecardCriterionsStatus;
  }
}

interface IProps {
  now?: Date;
  showFiltersPanel: boolean;
  onFiltersPanelClose: () => void;
  onFiltersUpdated: (sf: FilterState | undefined) => void;
}

export default function RealCalls({
  now,
  showFiltersPanel,
  onFiltersPanelClose,
  onFiltersUpdated: _onFiltersUpdated,
}: IProps) {
  const { canAccessIntegrations } = useUserSession();

  const router = useRouter();
  const params = useSearchParams();
  const curSearchParams = new URLSearchParams(params);
  const pathname = usePathname();
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);

  /************************************/
  /********* URL FILTERS **************/
  /************************************/

  let toDate = dayjs(now || new Date());
  // Subtract 4 months from the current date
  let fromDate = toDate.subtract(1, 'months');
  let range = AnalyticsFilterDateRange.LAST_TWO_WEEKS;
  let minDuration: number | undefined = undefined;
  let maxDuration: number | undefined = undefined;
  let reps: number[] = [];
  let callTypes: AgentCallType[] = [];
  let integrationId: number | undefined = undefined;
  let privateCalls: boolean = false;
  let organizationCustomCallTypesIds: number[] = [];
  let teamsCustomCallTypesIds: number[] = [];
  let usersCustomCallTypesIds: number[] = [];
  let scorecards: number[] = [];
  let scorecardSections: string[] = [];
  let scorecardCriterions: string[] = [];
  let scorecardCriterionsStatus: string[] = [];
  const [search, setSearch] = useState<string | undefined>(
    curSearchParams.get('q') || '',
  );

  if (curSearchParams.get('dateRange')) {
    range = curSearchParams.get('dateRange') as AnalyticsFilterDateRange;
  } else if (curSearchParams.get('fromDate') && curSearchParams.get('toDate')) {
    fromDate = dayjs(curSearchParams.get('fromDate') as string);
    toDate = dayjs(curSearchParams.get('toDate') as string);
    range = AnalyticsFilterDateRange.CUSTOM;
  } else {
    curSearchParams.set('dateRange', range);
    router.replace(`${pathname}?${curSearchParams.toString()}`);
  }

  if (curSearchParams.get('reps')) {
    reps = curSearchParams.get('reps')?.split(',').map(Number) || [];
  }

  if (curSearchParams.get('callTypes')) {
    callTypes =
      (curSearchParams.get('callTypes')?.split(',') as AgentCallType[]) || [];
  }

  if (curSearchParams.get('source')) {
    const sourceParam = curSearchParams.get('source');
    integrationId = sourceParam ? parseInt(sourceParam) : undefined;
  }

  if (curSearchParams.get('private')) {
    const p = curSearchParams.get('private');
    privateCalls = p === 'yes';
  }

  if (curSearchParams.get('organizationCustomCallTypesIds')) {
    const organizationCustomCallTypesIdsParam =
      curSearchParams
        .get('organizationCustomCallTypesIds')
        ?.split(',')
        .map(Number) || [];
    organizationCustomCallTypesIds = organizationCustomCallTypesIdsParam.filter(
      (id) => id !== undefined,
    );
  }

  if (curSearchParams.get('teamsCustomCallTypesIds')) {
    const teamsCustomCallTypesIdsParam =
      curSearchParams.get('teamsCustomCallTypesIds')?.split(',').map(Number) ||
      [];
    teamsCustomCallTypesIds = teamsCustomCallTypesIdsParam.filter(
      (id) => id !== undefined,
    );
  }

  if (curSearchParams.get('usersCustomCallTypesIds')) {
    const usersCustomCallTypesIdsParam =
      curSearchParams.get('usersCustomCallTypesIds')?.split(',').map(Number) ||
      [];
    usersCustomCallTypesIds = usersCustomCallTypesIdsParam.filter(
      (id) => id !== undefined,
    );
  }

  if (curSearchParams.get('scorecards')) {
    scorecards =
      curSearchParams.get('scorecards')?.split(',').map(Number) || [];
  }

  if (curSearchParams.get('minDuration')) {
    minDuration = parseInt(curSearchParams.get('minDuration') as string);
  }

  if (curSearchParams.get('maxDuration')) {
    maxDuration = parseInt(curSearchParams.get('maxDuration') as string);
  }

  if (curSearchParams.get('scorecardSections')) {
    scorecardSections =
      curSearchParams.get('scorecardSections')?.split(',') || [];
  }

  if (curSearchParams.get('scorecardCriterions')) {
    scorecardCriterions =
      curSearchParams.get('scorecardCriterions')?.split(',') || [];
  }

  if (curSearchParams.get('scorecardCriterionsStatus')) {
    scorecardCriterionsStatus =
      curSearchParams.get('scorecardCriterionsStatus')?.split(',') || [];
  }

  /************************************/
  /************** INIT ****************/
  /************************************/

  const {
    data: totalIntegrationsCount,
    isLoading: isLoadingTotalIntegrationsCount,
  } = useTotalIntegrationsCount(canAccessIntegrations);
  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(
      fromDate.toDate(),
      toDate.toDate(),
      minDuration,
      maxDuration,
      range,
      0,
      integrationId,
      reps,
      callTypes,
      search,
      privateCalls,
      organizationCustomCallTypesIds,
      teamsCustomCallTypesIds,
      usersCustomCallTypesIds,
      scorecards,
      scorecardSections,
      scorecardCriterions,
      scorecardCriterionsStatus,
    ),
  );

  const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchFilter = e.target.value;
    setSearch(searchFilter);

    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      onFiltersUpdated({
        ...filterState,
        search: searchFilter,
      });
    }, 400);
  };

  const onFiltersUpdated = (sf: FilterState) => {
    const searchFilter = sf.search?.trim();

    if (searchFilter) {
      curSearchParams.set('q', searchFilter);
    } else {
      curSearchParams.delete('q');
    }

    const dateFilter = sf.dateFilter;

    if (dateFilter.range == AnalyticsFilterDateRange.CUSTOM) {
      curSearchParams.delete('dateRange');
      curSearchParams.set(
        'fromDate',
        dayjs(dateFilter.fromDate).format('YYYY-MM-DD'),
      );
      curSearchParams.set(
        'toDate',
        dayjs(dateFilter.toDate).format('YYYY-MM-DD'),
      );
    } else {
      curSearchParams.delete('fromDate');
      curSearchParams.delete('toDate');
      curSearchParams.set('dateRange', dateFilter.range);
    }

    if (sf.minDuration) {
      curSearchParams.set('minDuration', String(sf.minDuration));
    } else {
      curSearchParams.delete('minDuration');
    }

    if (sf.maxDuration) {
      curSearchParams.set('maxDuration', String(sf.maxDuration));
    } else {
      curSearchParams.delete('maxDuration');
    }

    const repsFilter = sf.reps;

    if (repsFilter && repsFilter.length > 0) {
      curSearchParams.set('reps', repsFilter.join(','));
    } else {
      curSearchParams.delete('reps');
    }

    const callTypes = sf.callTypes;

    if (callTypes && callTypes.length > 0) {
      curSearchParams.set('callTypes', callTypes.join(','));
    } else {
      curSearchParams.delete('callTypes');
    }

    if (
      sf.organizationCustomCallTypesIds &&
      sf.organizationCustomCallTypesIds.length > 0
    ) {
      curSearchParams.set(
        'organizationCustomCallTypesIds',
        sf.organizationCustomCallTypesIds.join(','),
      );
    } else {
      curSearchParams.delete('organizationCustomCallTypesIds');
    }

    if (sf.teamsCustomCallTypesIds && sf.teamsCustomCallTypesIds.length > 0) {
      curSearchParams.set(
        'teamsCustomCallTypesIds',
        sf.teamsCustomCallTypesIds.join(','),
      );
    } else {
      curSearchParams.delete('teamsCustomCallTypesIds');
    }

    if (sf.usersCustomCallTypesIds && sf.usersCustomCallTypesIds.length > 0) {
      curSearchParams.set(
        'usersCustomCallTypesIds',
        sf.usersCustomCallTypesIds.join(','),
      );
    } else {
      curSearchParams.delete('usersCustomCallTypesIds');
    }

    const integrationId = sf.integrationId;

    if (integrationId && integrationId > 0) {
      curSearchParams.set('source', String(integrationId));
    } else {
      curSearchParams.delete('source');
    }

    if (sf.isPrivate) {
      curSearchParams.set('private', 'yes');
    } else {
      curSearchParams.delete('private');
    }

    if (sf.scorecards && sf.scorecards.length > 0) {
      curSearchParams.set('scorecards', sf.scorecards.join(','));
    } else {
      curSearchParams.delete('scorecards');
    }

    if (sf.scorecardSections && sf.scorecardSections.length > 0) {
      curSearchParams.set('scorecardSections', sf.scorecardSections.join(','));
    } else {
      curSearchParams.delete('scorecardSections');
    }

    if (sf.scorecardCriterions && sf.scorecardCriterions.length > 0) {
      curSearchParams.set(
        'scorecardCriterions',
        sf.scorecardCriterions.join(','),
      );
    } else {
      curSearchParams.delete('scorecardCriterions');
    }

    if (
      sf.scorecardCriterionsStatus &&
      sf.scorecardCriterionsStatus.length > 0
    ) {
      curSearchParams.set(
        'scorecardCriterionsStatus',
        sf.scorecardCriterionsStatus.join(','),
      );
    } else {
      curSearchParams.delete('scorecardCriterionsStatus');
    }

    router.replace(`${pathname}?${curSearchParams.toString()}`);

    const newFilters = {
      ...sf,
      dateFilter,
      minDuration: sf.minDuration,
      maxDuration: sf.maxDuration,
      integrationId: sf.integrationId,
      reps: sf.reps,
      callTypes: sf.callTypes,
      search: searchFilter,
      scorecards: sf.scorecards,
      scorecardSections: sf.scorecardSections,
      scorecardCriterions: sf.scorecardCriterions,
      scorecardCriterionsStatus: sf.scorecardCriterionsStatus,
    };

    setFilterState(newFilters);
  };

  useEffect(() => {
    _onFiltersUpdated(filterState);
  }, [filterState]);

  /************************************/
  /************ RENDERING *************/
  /************************************/

  if (
    totalIntegrationsCount === 0 &&
    !isLoadingTotalIntegrationsCount &&
    canAccessIntegrations
  ) {
    return <Splash />;
  }

  return (
    <div>
      {/* CALLS */}
      <div className="mt-4">
        <Input
          placeholder="Search"
          value={search}
          onChange={onSearchChange}
          className="w-[400px] h-[34px] mb-4"
        />
        <RealCallsTable filters={filterState} updateFilters={setFilterState} />
      </div>

      {/* FILTERS */}
      <FiltersPanel
        open={showFiltersPanel}
        onFiltersPanelClose={onFiltersPanelClose}
        onFiltersUpdated={onFiltersUpdated}
        filtersState={filterState}
      />
    </div>
  );
}
