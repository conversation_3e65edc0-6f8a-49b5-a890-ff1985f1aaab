import DashboardNavbar from '@/common/DashboardNavbar';
import { Button } from '@/components/ui/button';
import LinksManager from '@/lib/linksManager';
import { Headset } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function Splash() {
  const router = useRouter();

  return (
    <div className="">
      <div className="mt-[10%] flex items-center justify-center">
        <div className="min-h-[40vh] text-base w-[50vw] border p-4 rounded-lg flex flex-col">
          <div className="font-bold text-xl mb-6 flex items-center">
            <Headset size={20} className="mr-1" />
            Score Real Calls
          </div>
          <div className="mt-4">
            With Hyperbound, now you can score &quot;real&quot; sales calls —
            right within the product.
            <br />
            <br />
            To start scoring your sales calls, start by click the “Get Started”
            button in the bottom right-hand corner.
          </div>
          <div className="flex-1"></div>
          <div className="flex items-center mt-4">
            <div className="flex-1"></div>
            <div>
              <Button
                size={'lg'}
                variant={'default'}
                className={
                  'w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200'
                }
                style={{
                  backgroundImage:
                    'linear-gradient(to right, #000000, #5189CE, #A168A2)',
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(LinksManager.integrations());
                }}
              >
                Get started
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
