import { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { CloudUpload } from 'lucide-react';
import { vttToJson } from '@/lib/utils';

interface IProps {
  onError: (e: string) => void;
  clearError: () => void;
  onUpload: (transcript: any, plainText: string) => void;
}

export default function UploadTranscriptDnd({
  onError,
  clearError,
  onUpload,
}: IProps) {
  const [trasncriptContent, setTranscriptContent] = useState<string>('');

  const onDrop = useCallback(
    (files: File[]) => {
      clearError();
      if (files) {
        for (let i = 0; i < files.length; i++) {
          if (files[i].size > 1024 * 1024 * 2) {
            //2MB
            onError('File size must be less than 2MB');
            return;
          }

          const reader = new FileReader();
          reader.onload = () => {
            const text: string = reader.result as string;

            try {
              const json = vttToJson(text);
              onUpload(json, text);
              setTranscriptContent('');
            } catch (e: any) {
              console.log(e);
              onError(e.message);
            }
          };
          reader.readAsText(files[i]);
        }
      }
    },
    [clearError, onError, onUpload],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'text/vtt': ['.vtt'],
    },
    onDrop,
  });

  return (
    <>
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        <div className="bg-muted rounded-lg p-2 cursor-pointer">
          <div className="border border-dashed border-slate-300 p-3 flex flex-col items-center justify-center rounded-lg">
            {trasncriptContent == '' && (
              <div className="mb-2">
                <CloudUpload className="w-5 h-5 text-muted-foreground" />
              </div>
            )}
            {isDragActive ? (
              <>
                <div className="text-sm text-muted-foreground mb-2">
                  Drop file here...
                </div>
                <div className="text-xs text-muted-foreground">&nbsp;</div>
              </>
            ) : trasncriptContent != '' ? (
              <div>tr</div>
            ) : (
              <>
                <div className="text-sm text-muted-foreground mb-2">
                  Click to select or drag&apos;n&apos;drop a file here
                </div>
                <div className="text-xs text-muted-foreground">
                  You can upload only *.vtt files
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
