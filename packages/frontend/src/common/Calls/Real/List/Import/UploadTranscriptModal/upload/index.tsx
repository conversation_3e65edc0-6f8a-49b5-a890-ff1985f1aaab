import { extractTextFromFile } from '@/common/DragAndDropModal/utils';
import HorizontalDotsLoading from '@/components/ui/Hyperbound/horizontalDotsLoading';
import { Progress } from '@/components/ui/progress';
import useAnyTranscriptParser from '@/hooks/useAnyTranscriptParser';
import useTranscriptProgress from '@/hooks/useTranscriptProgress';
import {
  BLOCKED_FILE_TYPES,
  handleFileToJson,
  TranscriptFileExtensions,
  validateAndSanitizeAnyTranscript,
} from '@/lib/utils';
import { CloudUpload } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Id, toast } from 'react-toastify';

interface IProps {
  onError: (e: string) => void;
  clearError: () => void;
  onUpload: (transcript: any, plainText: string) => void;
}

interface FileProcessingResult {
  success: boolean;
  text?: string;
  error?: string;
}

const ACCEPTED_FILE_TYPES = {
  'application/json': ['.json'],
  'application/pdf': ['.pdf'],
  'application/rtf': ['.rtf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
  ],
  'text/csv': ['.csv'],
  'text/markdown': ['.md'],
  'text/plain': ['.txt'],
  'text/srt': ['.srt'],
  'text/vtt': ['.vtt'],
  'text/xml': ['.xml'],
  'text/yaml': ['.yaml', '.yml'],
};

const COMPLEX_FORMATS = ['pdf', 'docx', 'rtf'];
const FILE_SIZE_LIMIT = 2 * 1024 * 1024; // 2MB

export default function UploadTranscriptDnd({
  onError,
  clearError,
  onUpload,
}: IProps) {
  const [transcriptContent, setTranscriptContent] = useState<string>('');
  const [aIParsing, setAIParsing] = useState<boolean>(false);
  const [progressInterval, setProgressInterval] = useState<number>(0);
  const [queryText, setQueryText] = useState<string>('');
  const { fetchQuery, totalWords } = useAnyTranscriptParser(queryText);
  const errorToastId = useRef<Id | null>(null);
  const { numberOfIntervals, intervalDuration } =
    useTranscriptProgress(totalWords);

  const handleError = (error: Error) => {
    const errorMessage = error.message;
    errorToastId.current = toast.error(errorMessage);
    onError(errorMessage);
  };

  const processComplexFile = async (
    file: File,
  ): Promise<FileProcessingResult> => {
    const extracted = await extractTextFromFile(file);
    if (!extracted.success || !extracted.text) {
      throw new Error(extracted.error || 'Failed to extract text from file');
    }
    return { success: true, text: extracted.text };
  };

  const processSimpleFile = async (
    file: File,
  ): Promise<FileProcessingResult> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () =>
        resolve({ success: true, text: reader.result as string });
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const validateFile = (file: File, fileExtension: string) => {
    if (file.size > FILE_SIZE_LIMIT) {
      throw new Error('File size must be less than 2MB');
    }
    if (BLOCKED_FILE_TYPES.includes(file.type)) {
      throw new Error(
        'Unsupported file type. Please upload a text-based transcript.',
      );
    }
  };

  const processFileContent = async (
    rawText: string,
    fileExtension: TranscriptFileExtensions,
  ) => {
    const sanitizedText = validateAndSanitizeAnyTranscript(rawText);
    setQueryText(sanitizedText);

    try {
      const json = handleFileToJson(sanitizedText, fileExtension);
      onUpload(json, sanitizedText);
    } catch (e) {
      console.error('Parsing error:', e);
      setAIParsing(true);
    }
  };

  const getTranscriptWithAI = async () => {
    let progressBar = 0;
    const increment = 100 / numberOfIntervals;

    const intervalId = setInterval(() => {
      if (progressBar < 100) {
        progressBar += increment;
        setProgressInterval(Math.min(progressBar, 100));
      }
    }, intervalDuration);

    try {
      const response = await fetchQuery();
      onUpload(response?.data, queryText);
      return response?.data;
    } catch (e) {
      handleError(e as Error);
    } finally {
      clearInterval(intervalId);
      setTranscriptContent('');
      setAIParsing(false);
      setQueryText('');
    }
  };

  const onDrop = useCallback(
    async (files: File[]) => {
      clearError();
      if (!files?.length) return;

      const file = files[0];
      const fileExtension = file.name
        .split('.')
        .pop()
        ?.toLowerCase() as TranscriptFileExtensions;

      try {
        validateFile(file, fileExtension);

        const result = COMPLEX_FORMATS.includes(fileExtension)
          ? await processComplexFile(file)
          : await processSimpleFile(file);

        if (result.success && result.text) {
          await processFileContent(result.text, fileExtension);
        }
      } catch (error) {
        handleError(error as Error);
      }
    },
    [clearError, onError, onUpload],
  );

  useEffect(() => {
    if (queryText && aIParsing) {
      getTranscriptWithAI();
    }
  }, [aIParsing, queryText]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: ACCEPTED_FILE_TYPES,
    onDrop,
  });

  const renderContent = () => {
    if (aIParsing) {
      return (
        <>
          <div className="mt-4 text-center text-zinc-600">
            Processing{`: ${progressInterval.toFixed(0)}%`}
          </div>
          <Progress indicatorColor={'black'} value={progressInterval} />
          <HorizontalDotsLoading />
        </>
      );
    }

    if (transcriptContent) {
      return <div>tr</div>;
    }

    return (
      <>
        {!transcriptContent && (
          <div className="mb-2">
            <CloudUpload className="w-5 h-5 text-muted-foreground" />
          </div>
        )}
        {isDragActive ? (
          <>
            <div className="text-sm text-muted-foreground mb-2">
              Drop file here...
            </div>
            <div className="text-xs text-muted-foreground">&nbsp;</div>
          </>
        ) : (
          <>
            <div className="text-sm text-muted-foreground mb-2">
              Click to select or drag&apos;n&apos;drop a file here
            </div>
            <div className="text-xs text-muted-foreground">
              Please upload a text-based transcript file
            </div>
          </>
        )}
      </>
    );
  };

  return (
    <div {...getRootProps()}>
      <input {...getInputProps()} />
      <div className="bg-muted rounded-lg p-2 cursor-pointer">
        <div className="border border-dashed border-slate-300 p-3 flex flex-col items-center justify-center rounded-lg">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}
