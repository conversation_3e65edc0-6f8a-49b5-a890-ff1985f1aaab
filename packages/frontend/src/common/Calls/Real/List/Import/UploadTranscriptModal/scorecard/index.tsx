import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { Loader2Icon } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';

interface IProps {
  callDetails: any;
  updateDetails: (d: any) => void;
}

export default function SelectScorecard({
  callDetails,
  updateDetails,
}: IProps) {
  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);
  const [isLoadingScorecards, setIsLoadingScorecards] =
    useState<boolean>(false);
  const [selectedScorecard, setSelectedScorecard] = useState<string>('');
  const [selectedCallType, setSelectedCallTpe] = useState<string>('cold');

  const fetchData = async () => {
    setIsLoadingScorecards(true);

    const scs = await ScorecardConfigService.getAllScorecardConfigsForOrg();
    setAllScorecards(scs);

    setIsLoadingScorecards(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div>
      <div className="flex items-center mt-2 mb-2 mr-1 text-sm ">
        <div className="mr-2 font-semibold w-[80px]">Scorecard:</div>
        <div className="ml-1">
          {isLoadingScorecards && <Loader2Icon className="animate-spin" />}
        </div>
        <div className="flex-1">
          {!isLoadingScorecards && (
            <Select
              onValueChange={(value: string) => {
                setSelectedScorecard(value);
                updateDetails({
                  ...callDetails,
                  scorecardId: parseInt(value),
                  callType: selectedCallType,
                });
              }}
              value={selectedScorecard}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose a scorecard" />
              </SelectTrigger>
              <SelectContent>
                {allScorecards && (
                  <>
                    {allScorecards.map((option) => {
                      return (
                        <SelectItem key={option.id} value={String(option.id)}>
                          {option.tag}
                        </SelectItem>
                      );
                    })}
                  </>
                )}
              </SelectContent>
            </Select>
          )}
        </div>
      </div>

      <div className="flex items-center mb-2 mr-1 text-sm">
        <div className="mr-2 font-semibold w-[83px]">Call Type:</div>
        <div className="flex-1">
          <Select
            onValueChange={(value: string) => {
              setSelectedCallTpe(value);
              updateDetails({ ...callDetails, callType: value });
            }}
            value={selectedCallType}
          >
            <SelectTrigger>
              <SelectValue placeholder="" />
            </SelectTrigger>
            <SelectContent>
              {CALL_TYPE_OPTIONS.map((ct, index) => {
                const Icon =
                  CALL_TYPE_TO_ICON[ct.value as keyof typeof CALL_TYPE_TO_ICON]
                    .Icon;

                return (
                  <SelectItem key={ct.value} value={ct.value}>
                    <div className="flex items-center">
                      <div className="mr-1">
                        <Icon size={12} />
                      </div>
                      <div>{ct.label}</div>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
