import { useEffect, useState } from 'react';
import { FilterState } from '../../..';
import { AnalyticsFilterDateRange } from '@/lib/AnalyticsOld/types';
import {
  useExternalUsers,
  userListCallsFromProvider,
} from '@/hooks/useRealCalls';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
  TablePaginationFooter,
} from '@/components/ui/Hyperbound/table';
import dayjs from 'dayjs';
import { Loader2Icon } from 'lucide-react';
import Checkbox from '@/components/ui/Hyperbound/checkbox';
import { useScrollableContent } from '@/components/ui/Hyperbound/DialogFullScreen/useScrollableContent';

interface IProps {
  integrationId: number;
  selectedCalls: string[];
  updateSelectedCalls: (calls: string[]) => void;
  fillTillHtmlDivElementId?: string;
}

export default function SelectCalls({
  integrationId,
  selectedCalls,
  updateSelectedCalls,
  fillTillHtmlDivElementId,
}: IProps) {
  const [currentCursor, setCurrentCursor] = useState<string>('');
  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(
      new Date(),
      new Date(),
      undefined,
      undefined,
      AnalyticsFilterDateRange.LAST_TWO_WEEKS,
      0,
    ),
  );
  const { data: dbCallsResults, isLoading: isLoadingCalls } =
    userListCallsFromProvider(integrationId, filterState, currentCursor);
  const { data: dbUsers, isLoading: isLoadingUsers } =
    useExternalUsers(integrationId);
  const [allCalls, setAllCalls] = useState<any[]>([]);
  const [allUsers, setAllUsers] = useState<any>({});
  const [fromCalls, setFromCalls] = useState<number>(0);
  const [totNumberOfCalls, setTotNumberOfCalls] = useState<number>(0);
  const [paginationCursor, setPaginationCursor] = useState<string[]>(['']);
  const [currentPage, setCurrentPage] = useState<number>(0);

  /*************************************/
  /***************  INIT  **************/
  /*************************************/

  useEffect(() => {
    if (!isLoadingCalls && dbCallsResults) {
      setAllCalls(dbCallsResults.calls);
      setTotNumberOfCalls(dbCallsResults.records.totalRecords);
      if (paginationCursor.indexOf(dbCallsResults.records.cursor) == -1) {
        if (dbCallsResults.records.cursor) {
          setPaginationCursor([
            ...paginationCursor,
            dbCallsResults.records.cursor,
          ]);
        }
      }
    }
  }, [dbCallsResults, isLoadingCalls]);

  useEffect(() => {
    if (!isLoadingUsers && dbUsers) {
      const tmp: any = {};
      for (const u of dbUsers) {
        tmp[u.id] = {
          firstName: u.firstName,
          lastName: u.lastName,
          email: u.email,
        };
      }
      setAllUsers(tmp);
    }
  }, [dbUsers, isLoadingUsers]);

  const str_pad_left = (a: string, pad: string, length: number) => {
    return (new Array(length + 1).join(pad) + a).slice(-length);
  };

  /*************************************/
  /***************  ACTIONS ************/
  /*************************************/

  const toggleCalls = (id: string) => {
    const tmp = [];
    let add = true;
    for (const cid of selectedCalls) {
      if (cid == id) {
        add = false;
      } else {
        tmp.push(cid);
      }
    }
    if (add) {
      tmp.push(id);
    }
    updateSelectedCalls([...tmp]);
  };

  const movePagination = (from: number, numberOfResults: number) => {
    if (from > fromCalls) {
      const i = currentPage + 1;
      const cc = paginationCursor[i];
      setCurrentCursor(cc);
      setCurrentPage(i);
    } else {
      let i = currentPage - 1;
      if (i < 0) {
        i = 0;
      }
      const cc = paginationCursor[i];
      setCurrentCursor(cc);
      setCurrentPage(i);
    }
    setFromCalls(from);
  };

  /*************************************/
  /*************** RENDER **************/
  /*************************************/

  const scrollContainerRef = useScrollableContent();

  return (
    <div>
      {isLoadingCalls && (
        <div className="flex items-center justify-center mt-20">
          Loading calls, this may take a few seconds....
          <Loader2Icon size={32} className="animate-spin ml-4" />
        </div>
      )}

      {!isLoadingCalls && (
        <Table
          fillTillHtmlDivElementId={fillTillHtmlDivElementId}
          tableRef={scrollContainerRef}
          className=""
        >
          <TableContent>
            <TableRow>
              <TableCellHead>&nbsp;</TableCellHead>
              <TableCellHead>Started At</TableCellHead>
              <TableCellHead>Title</TableCellHead>
              <TableCellHead>Rep</TableCellHead>
              <TableCellHead>Buyers</TableCellHead>
              <TableCellHead>Duration</TableCellHead>
              <TableCellHead>Direction</TableCellHead>
            </TableRow>

            {!isLoadingUsers &&
              allCalls.map((c: any) => {
                const isSelected = selectedCalls.indexOf(c.id) > -1;
                const userInfo = allUsers[c.primaryUserId];
                let startedAt = '';
                if (c.started) {
                  startedAt = dayjs(c.started).format('MMM D YYYY, h:mm A');
                }
                let rep = '';
                let buyers = '';
                if (c.parties) {
                  for (const p of c.parties) {
                    const r = p.name
                      ? p.name
                      : p.phoneNumber
                        ? p.phoneNumber
                        : '';
                    if (r) {
                      if (p.affiliation.toLowerCase() === 'internal') {
                        if (rep) {
                          rep += '\n' + r;
                        } else {
                          rep += r;
                        }
                      } else {
                        if (buyers) {
                          buyers += '\n' + r;
                        } else {
                          buyers += r;
                        }
                      }
                    }
                  }
                } else if (c.primaryUserId) {
                  if (userInfo) {
                    rep = userInfo.firstName + ' ' + userInfo.lastName;
                  }
                }
                let duration = '00:00';
                if (c.duration) {
                  const minutes = Math.floor(c.duration / 60);
                  const seconds = c.duration - minutes * 60;

                  duration =
                    str_pad_left(String(minutes), '0', 2) +
                    ':' +
                    str_pad_left(String(seconds), '0', 2);
                }

                //onClick={() => { console.log({ ...c, user: userInfo }) }}
                return (
                  <TableRow
                    key={'int-' + c.id}
                    className="cursor-pointer"
                    onClick={() => {
                      toggleCalls(c.id);
                    }}
                  >
                    <TableCell>
                      <Checkbox checked={isSelected} onToggle={() => {}} />
                    </TableCell>
                    <TableCell>{startedAt}</TableCell>
                    <TableCell className="max-w-[200px] truncate">
                      {c.title}
                    </TableCell>
                    <TableCell
                      className="max-w-[200px] truncate whitespace-pre-line"
                      title={rep}
                    >
                      {rep}
                    </TableCell>
                    <TableCell
                      className="max-w-[200px] truncate whitespace-pre-line"
                      title={buyers}
                    >
                      {buyers}
                    </TableCell>
                    <TableCell>{duration}</TableCell>
                    <TableCell>{c.direction}</TableCell>
                  </TableRow>
                );
              })}
          </TableContent>
          <TableFooter>
            <TablePaginationFooter
              from={fromCalls}
              numberOfResults={100}
              totNumberOfRows={totNumberOfCalls}
              updatePagination={movePagination}
              hideFirstLastPageButtons={true}
              hideRowsPerPage={true}
            />
          </TableFooter>
        </Table>
      )}
    </div>
  );
}
