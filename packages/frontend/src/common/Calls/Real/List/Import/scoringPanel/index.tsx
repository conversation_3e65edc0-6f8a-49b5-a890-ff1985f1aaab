import { cn } from '@/lib/utils';
import { Loader2Icon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

function randomIntFromInterval(min: number, max: number): number {
  // min and max included
  return Math.floor(Math.random() * (max - min + 1) + min);
}

interface IProps {
  onDone: () => void;
  forceExit: boolean;
  minimal?: boolean;
}

export default function Scoring({ onDone, forceExit, minimal }: IProps) {
  const senteces = [
    'Initializing transcript analysis. Please wait...',
    'Uploading call transcript data...',
    'Preprocessing text for evaluation...',
    'Identifying key dialogue segments...',
    'Extracting keywords and phrases...',
    'Analyzing conversation flow and structure...',
    'Evaluating rapport building techniques...',
    'Analyzing tone and sentiment throughout the call...',
    'Analyzing objection handling skills...',
    'Measuring engagement and responsiveness...',
    'Tracking the duration of key call segments...',
    'Detecting any escalations or hand-offs during the call...',
    'Assessing overall call structure and coherence...',
    'Evaluating personalization and customer care...',
    'Reviewing empathy and active listening skills...',
    'Detecting use of persuasive language...',
    'Checking compliance with company guidelines...',
    'Measuring the clarity and conciseness of communication...',
    'Identifying instances of information overload...',
    'Assessing the balance between talking and listening...',
    'Detecting repetitive or redundant phrases...',
    'Evaluating the timing and pacing of the conversation...',
    'Analyzing the impact of pauses and hesitations...',
    'Detecting the use of positive reinforcement...',
    'Assessing the effectiveness of summarizing key points...',
    'Evaluating the clarity of the next steps provided...',
    'Compiling performance metrics...',
    'Calculating overall performance score...',
    'Finalizing evaluation results...',
  ];

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const counter = useRef<number>(0);
  const [sentence, setSentence] = useState<string>(senteces[counter.current]);

  const start = () => {
    timeoutRef.current = setTimeout(
      () => {
        counter.current++;
        if (counter.current < senteces.length) {
          setSentence(senteces[counter.current]);
          start();
        } else {
          onDone();
        }
      },
      randomIntFromInterval(1, 6) * 1000,
    );
  };

  useEffect(() => {
    start();

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (forceExit) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      counter.current = senteces.length - 2;
      start();
    }
  }, [forceExit]);

  return (
    <div>
      <div
        className={cn('flex items-center justify-center ', {
          'h-full w-full mt-10': !minimal,
        })}
      >
        <div className="mr-4">
          <div className="text-base">
            Scoring in progress, this may take a few seconds
          </div>
          <div className="text-xs text-muted-foreground">{sentence}</div>
        </div>

        <Loader2Icon className="animate-spin text-muted-foreground" size={28} />
      </div>
    </div>
  );
}
