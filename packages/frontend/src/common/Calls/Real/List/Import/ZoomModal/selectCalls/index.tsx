import { useEffect, useState } from 'react';
import { FilterState } from '../../..';
import { AnalyticsFilterDateRange } from '@/lib/AnalyticsOld/types';
import {
  useExternalUsers,
  userListCallsFromProvider,
} from '@/hooks/useRealCalls';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
  TablePaginationFooter,
} from '@/components/ui/Hyperbound/table';
import dayjs from 'dayjs';
import { Loader2Icon } from 'lucide-react';
import Checkbox from '@/components/ui/Hyperbound/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useScrollableContent } from '@/components/ui/Hyperbound/DialogFullScreen/useScrollableContent';

interface ZoomCall {
  uuid: string;
  primaryUserId: string;
  start_time?: string;
  topic?: string;
  duration?: number;
  participants?: Array<{
    name?: string;
  }>;
}

interface IProps {
  integrationId: number;
  selectedCalls: string[];
  updateSelectedCalls: (calls: string[]) => void;
  fillTillHtmlDivElementId?: string;
  filtersState: FilterState;
}

export default function SelectCalls({
  integrationId,
  selectedCalls,
  updateSelectedCalls,
  fillTillHtmlDivElementId,
  filtersState: _filtersState,
}: IProps) {
  const [currentCursor, setCurrentCursor] = useState<string>('');
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [filterState] = useState<FilterState>(_filtersState);
  const { data: dbCallsResults, isLoading: isLoadingCalls } =
    userListCallsFromProvider(
      integrationId,
      filterState,
      currentCursor,
      selectedUserId,
      selectedUserId != '',
    );
  const { data: dbUsers, isLoading: isLoadingUsers } =
    useExternalUsers(integrationId);
  const [allCalls, setAllCalls] = useState<ZoomCall[]>([]);
  const [fromCalls, setFromCalls] = useState<number>(0);
  const [totNumberOfCalls, setTotNumberOfCalls] = useState<number>(0);
  const [paginationCursor, setPaginationCursor] = useState<string[]>(['']);
  const [currentPage, setCurrentPage] = useState<number>(0);

  /*************************************/
  /***************  INIT  **************/
  /*************************************/

  useEffect(() => {
    if (!isLoadingCalls && dbCallsResults) {
      setAllCalls(dbCallsResults.calls);
      setTotNumberOfCalls(dbCallsResults.records.totalRecords);
      if (paginationCursor.indexOf(dbCallsResults.records.cursor) == -1) {
        if (dbCallsResults.records.cursor) {
          setPaginationCursor([
            ...paginationCursor,
            dbCallsResults.records.cursor,
          ]);
        }
      }
    }
  }, [dbCallsResults, isLoadingCalls]);

  useEffect(() => {
    if (!isLoadingUsers && dbUsers) {
      let f = true;
      for (const u of dbUsers) {
        if (f) {
          setSelectedUserId(u.userId);
          f = false;
        }
      }
    }
  }, [dbUsers, isLoadingUsers]);

  const str_pad_left = (a: string, pad: string, length: number) => {
    return (new Array(length + 1).join(pad) + a).slice(-length);
  };

  /*************************************/
  /***************  ACTIONS ************/
  /*************************************/

  const toggleCalls = (id: string) => {
    const tmp = [];
    let add = true;
    for (const cid of selectedCalls) {
      if (cid == id) {
        add = false;
      } else {
        tmp.push(cid);
      }
    }
    if (add) {
      tmp.push(id);
    }
    updateSelectedCalls([...tmp]);
  };

  const movePagination = (from: number) => {
    if (from > fromCalls) {
      const i = currentPage + 1;
      const cc = paginationCursor[i];
      setCurrentCursor(cc);
      setCurrentPage(i);
    } else {
      let i = currentPage - 1;
      if (i < 0) {
        i = 0;
      }
      const cc = paginationCursor[i];
      setCurrentCursor(cc);
      setCurrentPage(i);
    }
    setFromCalls(from);
  };

  /*************************************/
  /*************** RENDER **************/
  /*************************************/
  const scrollContainerRef = useScrollableContent();
  return (
    <div>
      {isLoadingCalls && (
        <div className="flex items-center justify-center mt-20">
          Loading calls, this may take a few seconds....
          <Loader2Icon size={32} className="animate-spin ml-4" />
        </div>
      )}

      {!isLoadingCalls && (
        <div>
          <div className="flex items-center text-sm mb-2">
            <div className="mr-2">Hosted by</div>
            <div>
              <Select
                onValueChange={(v: string) => {
                  setSelectedUserId(v);
                }}
                value={selectedUserId}
              >
                <SelectTrigger>
                  <SelectValue placeholder={'Select'} />
                </SelectTrigger>
                <SelectContent>
                  {dbUsers?.map((u: { userId: string; name: string }) => {
                    return (
                      <SelectItem key={u.userId} value={u.userId}>
                        {u.name}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>
          <Table
            fillTillHtmlDivElementId={fillTillHtmlDivElementId}
            tableRef={scrollContainerRef}
          >
            <TableContent>
              <TableRow>
                <TableCellHead>&nbsp;</TableCellHead>
                <TableCellHead>Started At</TableCellHead>
                <TableCellHead>Title</TableCellHead>
                <TableCellHead>Attendies</TableCellHead>
                <TableCellHead>Duration</TableCellHead>
              </TableRow>

              {!isLoadingUsers &&
                allCalls.map((c) => {
                  const isSelected = selectedCalls.indexOf(c.uuid) > -1;
                  let startedAt = '';
                  if (c.start_time) {
                    startedAt = dayjs(c.start_time).format(
                      'MMM D YYYY, h:mm A',
                    );
                  }
                  let attendies = '';
                  if (c.participants) {
                    for (const p of c.participants) {
                      if (p.name) {
                        if (attendies) {
                          attendies += ', ' + p.name;
                        } else {
                          attendies += p.name;
                        }
                      }
                    }
                  }
                  let duration = '00:00';
                  if (c.duration) {
                    const minutes = Math.floor(c.duration);
                    const seconds = 0;

                    duration =
                      str_pad_left(String(minutes), '0', 2) +
                      ':' +
                      str_pad_left(String(seconds), '0', 2);
                  }

                  //onClick={() => { console.log({ ...c, user: userInfo }) }}
                  return (
                    <TableRow
                      key={'int-' + c.uuid}
                      className="cursor-pointer"
                      onClick={() => {
                        toggleCalls(c.uuid);
                      }}
                    >
                      <TableCell>
                        <Checkbox
                          checked={isSelected}
                          onToggle={() => {
                            toggleCalls(c.uuid);
                          }}
                        />
                      </TableCell>
                      <TableCell>{startedAt}</TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {c.topic}
                      </TableCell>
                      <TableCell
                        className="max-w-[300px] truncate"
                        title={attendies}
                      >
                        {attendies}
                      </TableCell>
                      <TableCell>{duration}</TableCell>
                    </TableRow>
                  );
                })}
            </TableContent>
            <TableFooter>
              <TablePaginationFooter
                from={fromCalls}
                numberOfResults={100}
                totNumberOfRows={totNumberOfCalls}
                updatePagination={movePagination}
                hideFirstLastPageButtons={true}
                hideRowsPerPage={true}
              />
            </TableFooter>
          </Table>
        </div>
      )}
    </div>
  );
}
