import DialogFullScreen from '@/components/ui/Hyperbound/DialogFullScreen';
import Header from '@/components/ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '@/components/ui/Hyperbound/DialogFullScreen/ScrollableContent';
import { Integration } from '@/lib/Integrations/types';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import CallSettings from '../callSettings';
import { RealCallsService } from '@/lib/Integrations';
import {
  RealCallsImportTaskScope,
  RealCallsImportTaskScopeList,
} from '@/lib/Integrations/RealCalls/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2Icon } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';
import SelectCalls from './selectCalls';

interface IProps {
  open: boolean;
  onClose: () => void;
  integration: Integration;
}

export default function ImportFromZoomModal({
  open,
  onClose,
  integration,
}: IProps) {
  const queryClient = useQueryClient();

  const [importScope, setImportScope] = useState<RealCallsImportTaskScope>(
    RealCallsImportTaskScope.SELECTED_CALL_IDS,
  );

  const [selectedCalls, setSelectedCalls] = useState<string[]>([]);
  const [callSettings, setCallSettings] = useState<any>();

  const [show, setShow] = useState<string>('list-calls'); //import-method for zoom is always "select calls" => zoom does not allow to parse all calls without specifying a userId

  /*************************************/
  /*************** ACTIONS *************/
  /*************************************/

  const close = () => {
    onClose();
    // console.log("done");
  };

  const next = () => {
    if (show == 'import-method') {
      if (importScope == RealCallsImportTaskScope.SELECTED_CALL_IDS) {
        setShow('list-calls');
      } else {
        setShow('select-scorecard');
      }
    } else if (show == 'list-calls') {
      setShow('select-scorecard');
    } else if (show == 'select-scorecard') {
      setShow('importing');
    }
  };

  const startImport = useCallback(() => {
    setShow('importing');

    (async () => {
      try {
        await RealCallsService.ImportCalls(
          integration.id,
          importScope,
          callSettings?.scorecardConfigId,
          callSettings?.callType,
          selectedCalls,
        );
      } catch (e) {
        console.log(e);
      }

      queryClient.invalidateQueries({ queryKey: ['import-tasks-list'] });
      close();
    })();
  }, [callSettings, importScope, selectedCalls]);

  /*************************************/
  /*************** RENDER **************/
  /*************************************/

  if (show == 'import-method') {
    return (
      <DialogFullScreen
        open={open}
        onOpenChange={close}
        fitHeightToContent={true}
        width="400px"
      >
        <Header
          title={`Import calls from ${integration.provider?.companyName}`}
          onClose={close}
          className="p-6"
        />

        <div className="px-6 pb-4 text-sm flex-1 w-full relative">
          <div className="flex items-center my-2">
            <div className="mr-2">Which calls would you like to import?</div>
            <div className="w-[200px]">
              <Select
                onValueChange={(value: string) => {
                  setImportScope(value as RealCallsImportTaskScope);
                }}
                value={importScope}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a scorecard" />
                </SelectTrigger>
                <SelectContent>
                  {RealCallsImportTaskScopeList.map((option: any) => {
                    return (
                      <SelectItem
                        key={option.value}
                        value={String(option.value)}
                      >
                        {option.label}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div className="px-4 py-2 border-t flex items-center">
          <div className="text-muted-foreground text-xs">
            {selectedCalls.length > 0 && `${selectedCalls.length} selected`}
          </div>
          <div className="flex-1" />
          <Button onClick={next}>Next</Button>
        </div>
      </DialogFullScreen>
    );
  } else if (show == 'list-calls') {
    return (
      <DialogFullScreen
        open={open}
        onOpenChange={close}
        fitHeightToContent={false}
      >
        <Header
          title={`Import calls from ${integration.provider?.companyName}`}
          onClose={close}
          className="p-6"
        />

        <ScrollableContent className="px-6 pb-4 text-sm">
          {importScope == RealCallsImportTaskScope.SELECTED_CALL_IDS && (
            <SelectCalls
              updateSelectedCalls={setSelectedCalls}
              selectedCalls={selectedCalls}
              integrationId={integration.id}
              fillTillHtmlDivElementId={'footerDiv-' + integration.id}
            />
          )}
        </ScrollableContent>
        <div
          className="px-4 py-2 border-t flex items-center overflow-hidden"
          id={'footerDiv-' + integration.id}
        >
          <div className="text-muted-foreground text-xs">
            {selectedCalls.length > 0 && `${selectedCalls.length} selected`}
          </div>
          <div className="flex-1" />
          <Button
            onClick={next}
            disabled={
              importScope == RealCallsImportTaskScope.SELECTED_CALL_IDS &&
              selectedCalls.length == 0
            }
          >
            Next
          </Button>
        </div>
      </DialogFullScreen>
    );
  } else if (show == 'select-scorecard') {
    return (
      <DialogFullScreen
        open={open}
        onOpenChange={close}
        fitHeightToContent={true}
        width="400px"
      >
        <Header
          title={`Import calls from ${integration.provider?.companyName}`}
          onClose={close}
          className="p-6"
        />

        <ScrollableContent className="px-6 pb-4 text-sm">
          <CallSettings onSelect={setCallSettings} />
        </ScrollableContent>
        <div className="px-4 py-2 border-t flex items-center">
          <div className="text-muted-foreground text-xs">
            {selectedCalls.length > 0 && `${selectedCalls.length} selected`}
          </div>
          <div className="flex-1" />
          <Button onClick={startImport}>Import</Button>
        </div>
      </DialogFullScreen>
    );
  } else if (show == 'importing') {
    return (
      <DialogFullScreen
        open={open}
        onOpenChange={close}
        fitHeightToContent={true}
        width="400px"
      >
        <Header
          title={`Import calls from ${integration.provider?.companyName}`}
          onClose={close}
          className="p-6"
        />

        <ScrollableContent className="px-6 pb-8 text-sm">
          <div className="flex items-center">
            Creating task...
            <Loader2Icon className="animate-spin ml-2" size={18} />
          </div>
        </ScrollableContent>
      </DialogFullScreen>
    );
  }
}
