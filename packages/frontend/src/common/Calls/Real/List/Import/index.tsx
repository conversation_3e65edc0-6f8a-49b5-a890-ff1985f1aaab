import { useIntegrations } from '@/hooks/useIntegrations';
import useUserSession from '@/hooks/useUserSession';
import { Integration, IntegrationStatus } from '@/lib/Integrations/types';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectSeparator,
  SelectTrigger,
} from '@/components/ui/select';
import useRouting from '@/hooks/useRouting';
import { Blocks, CloudUpload } from 'lucide-react';
import Image from 'next/image';
import LinksManager from '@/lib/linksManager';
import UploadTranscriptModal from './UploadTranscriptModal';
import ImportFromGongModal from './GongModal';
import ImportFromZoomModal from './ZoomModal';
import IntegrationService from '@/lib/Integrations';
import { AppPermissions } from '@/lib/permissions';

export default function ImportRealCallsSelect() {
  const { canAccessIntegrations, canAccess } = useUserSession();
  const { goToPage } = useRouting();

  const { data: dbIntegrations, isLoading: isLoadingIntegrations } =
    useIntegrations(); //always load, even if canAccessIntegrations = false (they may have access only to upload)

  const [allIntegrations, setAllIntegrations] = useState<Integration[]>([]);

  useEffect(() => {
    if (!isLoadingIntegrations && dbIntegrations) {
      setAllIntegrations(dbIntegrations);
    }
  }, [dbIntegrations, isLoadingIntegrations]);

  /*************************************/
  /*************** IMPORT **************/
  /*************************************/

  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showGongModal, setShowGongModal] = useState(false);
  const [showZoomModal, setShowZoomModal] = useState(false);
  const [integration, setIntegration] = useState<Integration>();

  const openImportPanel = (integrationId: number) => {
    let selectedIntegration: Integration | undefined;
    for (const i of allIntegrations) {
      if (i.id == integrationId) {
        selectedIntegration = i;
      }
    }

    if (selectedIntegration) {
      setIntegration(selectedIntegration);

      switch (selectedIntegration.provider?.companyName?.toLowerCase?.()) {
        case 'gong':
          setShowGongModal(true);
          break;
        case 'manual call transcript upload':
          setShowUploadModal(true);
          break;
        case 'zoom':
          setShowZoomModal(true);
          break;
      }
    }
  };

  /*************************************/
  /*************** RENDER **************/
  /*************************************/

  return (
    <>
      <Select
        onValueChange={(v: string) => {
          if (
            v == 'manage-integrations' &&
            canAccess(AppPermissions.INTEGRATIONS)
          ) {
            goToPage(LinksManager.integrations());
          } else {
            openImportPanel(parseInt(v));
          }
        }}
        value=""
      >
        <SelectTrigger className="py-0 h-[34px]">
          <div className="flex items-center mr-2">
            <CloudUpload size={16} className="mr-2" /> Import Real Calls
          </div>
        </SelectTrigger>
        <SelectContent>
          {allIntegrations.map((i: Integration) => {
            const provider = i.provider;
            if (provider && i.status == IntegrationStatus.ACTIVE) {
              let isRealCallsService = false;
              provider.services.map((s: any) => {
                if (s.type == 'REAL_CALL_SCORING') {
                  isRealCallsService = true;
                }
              });
              if (isRealCallsService) {
                return (
                  <SelectItem key={'int-' + i.id} value={String(i.id)}>
                    <div className="flex items-center">
                      <Image
                        src={IntegrationService.getProviderLogoUrl(
                          provider.logoUrl,
                        )}
                        alt={`${provider.companyName} Logo`}
                        width={20}
                        height={20}
                        className="mr-1"
                      />
                      <div>{i.name || provider.companyName}</div>
                    </div>
                  </SelectItem>
                );
              }
            }
          })}

          {canAccess(AppPermissions.INTEGRATIONS) && canAccessIntegrations && (
            <>
              <SelectSeparator />
              <SelectItem value={'manage-integrations'}>
                <div className="flex items-center">
                  <Blocks size={16} className="mr-2" />
                  Manage Integrations
                </div>
              </SelectItem>
            </>
          )}
        </SelectContent>
      </Select>

      {integration && showUploadModal && (
        <UploadTranscriptModal
          open={showUploadModal}
          onClose={() => {
            setShowUploadModal(false);
          }}
          integration={integration}
        />
      )}
      {integration && showGongModal && (
        <ImportFromGongModal
          open={showGongModal}
          onClose={() => {
            setShowGongModal(false);
          }}
          integration={integration}
        />
      )}
      {integration && showZoomModal && (
        <ImportFromZoomModal
          open={showZoomModal}
          onClose={() => {
            setShowZoomModal(false);
          }}
          integration={integration}
        />
      )}
    </>
  );
}
