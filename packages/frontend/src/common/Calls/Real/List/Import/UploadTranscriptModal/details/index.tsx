import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import useOrgUsers from '@/hooks/useOrgUsers';
import { UserDto } from '@/lib/User/types';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';

interface IProps {
  onError: (e: string) => void;
  clearError: () => void;
  callDetails: any;
  updateDetails: (d: any) => void;
}

export default function UploadTranscriptModalDetails({
  onError,
  clearError,
  callDetails,
  updateDetails,
}: IProps) {
  const tmpActors: string[] = [];

  for (const msg of callDetails.transcript) {
    if (!tmpActors.includes(msg.userName)) {
      tmpActors.push(msg.userName);
    }
  }

  const { data: allDbUsers, isLoading: isLoadingUsers } = useOrgUsers();
  const [allUsers, setAllUsers] = useState<UserDto[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [actors, setActors] = useState<any[]>([]);
  const [title, setTitle] = useState<string>('');

  useEffect(() => {
    if (!isLoadingUsers && allDbUsers) {
      setAllUsers(allDbUsers.data);
      findUsers(allDbUsers.data);
    }
  }, [isLoadingUsers, allDbUsers]);

  const findUsers = (users: UserDto[]) => {
    const tmp: any[] = [];
    let _title = '';
    for (const a of tmpActors) {
      const p = a.split(' ');
      let user: UserDto | undefined = undefined;
      for (const u of users) {
        if (
          u.firstName?.toLowerCase().includes(p[0]?.toLowerCase()) ||
          u.firstName?.toLowerCase().includes(p[1]?.toLowerCase())
        ) {
          if (
            u.lastName?.toLowerCase().includes(p[0]?.toLowerCase()) ||
            u.lastName?.toLowerCase().includes(p[1]?.toLowerCase())
          ) {
            user = u;
            break;
          }
        }
      }
      if (user) {
        tmp.push({
          affiliation: 'INTERNAL',
          user: user,
          name: a,
        });
      } else {
        tmp.push({
          affiliation: 'EXTERNAL',
          name: a,
        });
        if (_title == '') {
          _title = 'Call with ' + a;
        }
      }
    }
    setTitle(_title);
    setActors(tmp);
    setIsLoading(false);
  };

  useEffect(() => {
    updateDetails({
      ...callDetails,
      title: title,
      actors: actors,
    });
  }, [title, actors]);

  if (isLoading) {
    return (
      <div>
        <Skeleton className="w-[600px] h-10 mb-2" />
        <Skeleton className="w-[600px] h-10 mb-2" />
        <Skeleton className="w-[600px] h-10 mb-2" />
        <Skeleton className="w-[600px] h-10 mb-2" />
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center">
        <Label className="mr-2">Title:</Label>
        <div className="mr-2">
          <Input
            value={title}
            onChange={(e) => {
              setTitle(e.target.value);
            }}
            className="w-56"
            placeholder=""
          />
        </div>
      </div>
      <div className="mt-4">
        <Label className="mr-2">Participants:</Label>
        <div className="mt-2 ml-2">
          {actors.map((a, i) => {
            const isExternal = a.affiliation == 'EXTERNAL';
            let selectedId = 'external';
            if (!isExternal && a.user) {
              selectedId = a.user.id;
            }
            return (
              <div key={'pt' + i} className="text-sm flex items-center mb-2">
                <div className="w-[200px]">{a.name}</div>
                <div>
                  <Select
                    onValueChange={(v: string) => {
                      setActors((prev: any[]) => {
                        if (v != 'external') {
                          const u = allUsers?.find((u) => u.id == parseInt(v));
                          prev[i].affiliation = 'INTERNAL';
                          prev[i].user = u;
                        } else {
                          prev[i].affiliation = 'EXTERNAL';
                          prev[i].user = undefined;
                        }
                        return [...prev];
                      });
                    }}
                    value={selectedId}
                  >
                    <SelectTrigger>
                      {isExternal
                        ? 'External'
                        : a.user?.firstName + ' ' + a.user?.lastName}
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={'external'} className="font-semibold">
                        External (Customer/Prospect)
                      </SelectItem>

                      {allUsers?.map((u) => {
                        return (
                          <SelectItem key={u.id} value={String(u.id)}>
                            {u.firstName} {u.lastName}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
