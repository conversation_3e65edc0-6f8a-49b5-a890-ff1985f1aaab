import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { RealCallsService } from '@/lib/Integrations';
import { Integration } from '@/lib/Integrations/types';
import { useQueryClient } from '@tanstack/react-query';
import { useRef, useState } from 'react';
import Scoring from '../scoringPanel';
import UploadTranscriptModalDetails from './details';
import SelectScorecard from './scorecard';
import UploadTranscriptDnd from './upload';

interface IProps {
  open: boolean;
  onClose: () => void;
  integration: Integration;
}

export default function UploadTranscriptModal({
  open,
  onClose,
  integration,
}: IProps) {
  const queryClient = useQueryClient();

  const [modalState, setModalState] = useState<string>('upload');
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [callDetails, setCallDetails] = useState<any>({});

  const showError = (e: string) => {
    setErrorMsg(e);
  };

  const onTranscriptUpload = (
    transcript: RealCallTranscriptMessage[],
    plainText: string,
  ) => {
    setCallDetails({
      transcript: transcript,
      plainText: plainText,
    });
    setModalState('details');
  };

  const back = () => {
    if (modalState === 'details') {
      setModalState('upload');
    } else if (modalState === 'scorecard') {
      setModalState('details');
    }
  };

  const next = () => {
    if (modalState === 'details') {
      setModalState('scorecard');
    }
  };

  const save = async () => {
    setModalState('scoring');

    const transcript = callDetails.transcript;
    const rolesByUser: any = {};
    let callerId: number | undefined = undefined;
    for (const a of callDetails.actors) {
      if (a.affiliation == 'INTERNAL') {
        if (!callerId) {
          callerId = a.user.id;
        }
      }
      rolesByUser[a.name] = a.affiliation;
    }

    if (!callerId) {
      setModalState('upload');
      setErrorMsg('We could not find any hyperbound user in the call');
    } else {
      for (const t of transcript) {
        if (rolesByUser[t.userName] == 'INTERNAL') {
          t.role = 'user';
        } else {
          t.role = 'bot';
        }
      }
      let success = true;
      try {
        await RealCallsService.addNewRealCall(
          callerId,
          callDetails.scorecardId,
          callDetails.callType,
          integration.id,
          transcript,
          callDetails.plainText,
          callDetails.title,
          callDetails.actors,
          true,
        );
      } catch (e) {
        success = false;
        setModalState('scorecard');
        setErrorMsg('Error saving call details');
      }

      if (success) {
        queryClient.invalidateQueries({ queryKey: ['list-real-calls'] });

        if (scoringMessagesCompleted.current) {
          close();
        } else {
          setForceLastScoringMessage(true);
        }
      }
    }
  };

  const scoringMessagesCompleted = useRef<boolean>(false);
  const [forceLastScoringMessage, setForceLastScoringMessage] =
    useState<boolean>(false);
  const onScoringMessagesCompleted = () => {
    scoringMessagesCompleted.current = true;
    if (forceLastScoringMessage) {
      close();
    }
  };

  const close = () => {
    setModalState('upload');
    setCallDetails({});
    setErrorMsg('');
    onClose();
    // console.log("done");
  };

  return (
    <Dialog open={open} onOpenChange={close}>
      <DialogContent className="close-btn">
        {modalState != 'scoring' && (
          <DialogHeader>
            <DialogTitle className="flex items-center">
              Upload call transcript
            </DialogTitle>
          </DialogHeader>
        )}

        <div className="overflow-hidden">
          {modalState == 'upload' && (
            <UploadTranscriptDnd
              onError={showError}
              clearError={() => {
                setErrorMsg('');
              }}
              onUpload={onTranscriptUpload}
            />
          )}
          {modalState == 'details' && (
            <UploadTranscriptModalDetails
              onError={showError}
              clearError={() => {
                setErrorMsg('');
              }}
              callDetails={callDetails}
              updateDetails={setCallDetails}
            />
          )}
          {modalState == 'scorecard' && (
            <SelectScorecard
              callDetails={callDetails}
              updateDetails={setCallDetails}
            />
          )}
          {modalState == 'scoring' && (
            <div className="mr-6">
              <Scoring
                onDone={onScoringMessagesCompleted}
                forceExit={forceLastScoringMessage}
                minimal={true}
              />
              {/* <Button className="ml-2 flex items-center" variant={"default"} onClick={() => { save() }} disabled={!callDetails.scorecardId}>
                  RESAVE
                </Button> */}
            </div>
          )}
        </div>
        {modalState != 'scoring' && (
          <DialogFooter className="flex items-center">
            <div className="text-red-500 text-sm mr-2">{errorMsg}</div>
            {modalState != 'upload' && (
              <Button
                className="ml-2 flex items-center"
                variant={'outline'}
                onClick={() => {
                  back();
                }}
              >
                Back
              </Button>
            )}
            {modalState == 'upload' && (
              <Button
                className="ml-2 flex items-center"
                variant={'default'}
                onClick={() => {
                  onClose();
                }}
              >
                Close
              </Button>
            )}
            {modalState == 'details' && (
              <Button
                className="ml-2 flex items-center"
                variant={'default'}
                onClick={() => {
                  next();
                }}
              >
                Next
              </Button>
            )}

            {modalState == 'scorecard' && (
              <Button
                className="ml-2 flex items-center"
                variant={'default'}
                onClick={() => {
                  save();
                }}
                disabled={!callDetails.scorecardId}
              >
                Done
              </Button>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
