import { useImportTasks } from '@/hooks/useRealCalls';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  RealCallsImportTaskScope,
  RealCallsImportTaskScopeList,
  RealCallsImportTaskStatus,
} from '@/lib/Integrations/RealCalls/types';
import dayjs from 'dayjs';
import { Check, CheckCircle, Loader2Icon } from 'lucide-react';
import { ProgressCircle } from '@tremor/react';
import { cn } from '@/lib/utils';
import { useEffect, useRef } from 'react';

interface IProps {
  className: string;
}

export default function ImportTasksStatus({ className }: IProps) {
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();

  const {
    data: importTasks,
    isLoading: isLoadingImportTasks,
    refetch: reloadTasks,
  } = useImportTasks();

  const pollTasks = () => {
    reloadTasks();
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      pollTasks();
    }, 5000);
  };

  useEffect(() => {
    if (!isLoadingImportTasks && importTasks && importTasks.length > 0) {
      let atLeastOneRunning = false;
      importTasks.map((task) => {
        if (
          task.status != RealCallsImportTaskStatus.COMPLETED &&
          task.status != RealCallsImportTaskStatus.ERROR
        ) {
          atLeastOneRunning = true;
        }
      });
      if (atLeastOneRunning) {
        timeoutRef.current = setTimeout(() => {
          pollTasks();
        }, 1000);
      }
    }
  }, [isLoadingImportTasks, importTasks]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  if (!isLoadingImportTasks) {
    if (importTasks && importTasks.length > 0) {
      let totImported = 0;
      let numberOfCalls = 0;
      let atLeastOneRunning = false;
      importTasks.map((task) => {
        if (task.status == RealCallsImportTaskStatus.RUNNING) {
          totImported += task.importedCalls;
          numberOfCalls += task.numberOfCalls;
        }
        if (
          task.status != RealCallsImportTaskStatus.COMPLETED &&
          task.status != RealCallsImportTaskStatus.ERROR
        ) {
          atLeastOneRunning = true;
        }
      });

      if (atLeastOneRunning) {
        const progress = Math.trunc((totImported / numberOfCalls) * 100);
        return (
          <Popover modal={true}>
            <PopoverTrigger asChild>
              <div className={cn('cursor-pointer', className)}>
                <ProgressCircle color={'blue'} value={progress} size="xs">
                  {importTasks.length}
                </ProgressCircle>
              </div>
            </PopoverTrigger>

            <PopoverContent align="end" className="p-1 w-[440px]">
              {importTasks.map((task) => {
                let name = '';
                RealCallsImportTaskScopeList.map((scope: any) => {
                  if (scope.value == task.importScope) {
                    if (
                      task.importScope ==
                      RealCallsImportTaskScope.SELECTED_CALL_IDS
                    ) {
                      name = 'Manually selected calls';
                    } else {
                      name = scope.label;
                    }
                  }
                });
                const date = dayjs(task.createdAt).format('MM/DD/YYYY HH:mm');
                return (
                  <div
                    key={'it-' + task.id}
                    className="flex items-center text-sm p-2"
                  >
                    <div className="flex-1">{name}</div>
                    <div className="mr-3 text-xs text-muted-foreground ">
                      {date}
                    </div>
                    <div className="mr-3 text-xs">
                      {task.importedCalls}/{task.numberOfCalls} imported
                    </div>
                    <div className="w-[20px]">
                      {task.status == RealCallsImportTaskStatus.RUNNING && (
                        <Loader2Icon className="animate-spin" size={18} />
                      )}
                      {task.status == RealCallsImportTaskStatus.COMPLETED && (
                        <Check size={18} className="text-green-500" />
                      )}
                    </div>
                  </div>
                );
              })}
            </PopoverContent>
          </Popover>
        );
      }
    }
  }
}
