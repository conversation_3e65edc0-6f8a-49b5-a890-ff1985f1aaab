import { useExternalUsers, useListRealCalls } from '@/hooks/useRealCalls';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { FilterState } from '../..';
import {
  ZoomMeetingDto,
  ZoomMeetingInternalDto,
} from '@/lib/Integrations/types';

interface IProps {
  integrationId: number;
  onLoadingUpdates: (isLoading: boolean) => void;
  filters: FilterState;
}
export default function ZoomCalls({
  integrationId,
  onLoadingUpdates,
  filters,
}: IProps) {
  // const { data: dbCalls, isLoading: isLoadingCalls } = useListRealCalls(integrationId, filters.dateFilter) as {
  //   data: ZoomMeetingInternalDto[],
  //   isLoading: boolean,
  // };
  const [allCalls, setAllCalls] = useState<ZoomMeetingInternalDto[]>([]);

  // useEffect(() => {
  //   if (!isLoadingCalls && dbCalls) {
  //     setAllCalls(dbCalls);
  //   }
  //   onLoadingUpdates(isLoadingCalls);
  // }, [dbCalls, isLoadingCalls]);

  const str_pad_left = (a: string, pad: string, length: number) => {
    return (new Array(length + 1).join(pad) + a).slice(-length);
  };

  const openCall = (call: any) => {
    console.log(call);
  };

  const bots = [
    'circleback',
    'fireflies',
    'otter.ai',
    'betafi',
    'meeting notes',
  ];
  return (
    <Table>
      <TableContent>
        <TableRow>
          <TableCellHead>Started At</TableCellHead>
          <TableCellHead>Title</TableCellHead>
          <TableCellHead>Rep</TableCellHead>
          <TableCellHead>Buyers</TableCellHead>
          <TableCellHead>Duration</TableCellHead>
        </TableRow>

        {allCalls.map((c) => {
          let startedAt = '';
          if (c.start_time) {
            startedAt = dayjs(c.start_time).format('MMM D YYYY, h:mm A');
          }
          let rep = '';
          let buyers = '';
          if (c.participants) {
            const participantsSorted = [...c.participants].sort((p1, p2) => {
              const isP1Bot = bots.some((b) =>
                p1.name.toLowerCase().includes(b),
              );
              const isP2Bot = bots.some((b) =>
                p2.name.toLowerCase().includes(b),
              );
              if (isP1Bot) {
                return 1;
              }
              if (isP2Bot) {
                return -1;
              }
              return 0;
            });
            for (const p of participantsSorted) {
              const r = p.name || p.user_email;
              if (r) {
                if (p.isInternal) {
                  if (rep) {
                    rep += ', ' + r;
                  } else {
                    rep += r;
                  }
                } else {
                  if (buyers) {
                    buyers += ', ' + r;
                  } else {
                    buyers += r;
                  }
                }
              }
            }
          }
          let duration = '00:00';
          if (c.duration) {
            // zoom's duration is in minutes
            const minutes = Math.floor(c.duration);
            const seconds = c.duration * 60 - minutes * 60;

            duration =
              str_pad_left(String(minutes), '0', 2) +
              ':' +
              str_pad_left(String(seconds), '0', 2);
          }

          return (
            <TableRow
              key={'int-' + c.id}
              className="cursor-pointer"
              onClick={() => {
                openCall({ ...c });
              }}
            >
              <TableCell>{startedAt}</TableCell>
              <TableCell className="max-w-[200px] truncate">
                {c.topic}
              </TableCell>
              <TableCell className="max-w-[300px] truncate" title={rep}>
                {rep}
              </TableCell>
              <TableCell className="max-w-[300px] truncate" title={buyers}>
                {buyers}
              </TableCell>
              <TableCell>{duration}</TableCell>
            </TableRow>
          );
        })}
      </TableContent>
      <TableFooter>
        <div></div>
      </TableFooter>
    </Table>
  );
}
