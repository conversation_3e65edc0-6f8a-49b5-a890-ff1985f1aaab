import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import RecallAiService from '@/lib/Integrations/RecallAi';
import { Integration } from '@/lib/Integrations/types';
import { Loader2Icon, VideoIcon } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

export const AddRecorderToMeeting = ({
  open,
  onClose,
  zoomIntegration,
  googleMeetIntegration,
  microsoftTeamsIntegration,
}: {
  open: boolean;
  onClose: () => unknown;
  zoomIntegration?: Integration;
  googleMeetIntegration?: Integration;
  microsoftTeamsIntegration?: Integration;
}) => {
  const [addedText, setAddedText] = useState(
    'The meeting recorder has been successfully initiated to join your meeting.',
  );
  const [error, setError] = useState('');

  useEffect(() => {
    if (addedText) {
      const timeout = setTimeout(() => {
        setAddedText('');
      }, 2500);
      return () => clearTimeout(timeout);
    }
  }, [addedText]);

  const [isAdding, setIsAdding] = useState(false);
  const [link, setLink] = useState('');
  const url = useMemo(() => {
    try {
      return link ? new URL(link) : null;
    } catch {
      return null;
    }
  }, [link]);
  const linkError = useMemo(() => {
    const defaultError = 'Please enter the correct link.';
    if (!link) {
      return '';
    }
    if (!url) {
      return defaultError;
    }
    let pathnameRegex: RegExp;
    if (url.host.endsWith('zoom.us')) {
      if (zoomIntegration) {
        pathnameRegex = /\/j\/\d+/;
      } else {
        return 'You have no Zoom integration configured';
      }
    } else if (url.host === 'meet.google.com') {
      if (googleMeetIntegration) {
        pathnameRegex = /\/[a-z]{3}-[a-z]{4}-[a-z]{3}/;
      } else {
        return 'You have no Google Meet integration configured';
      }
    } else if (
      url.host === 'teams.microsoft.com' ||
      url.host === 'teams.live.com'
    ) {
      if (microsoftTeamsIntegration) {
        pathnameRegex = /\/.+/;
      } else {
        return 'You have no Microsoft Teams integration configured';
      }
    } else {
      return defaultError;
    }
    return pathnameRegex.test(url.pathname.toLowerCase()) ? null : defaultError;
  }, [link, url]);

  useEffect(() => {
    setError(linkError || '');
  }, [linkError]);

  const integrationNames = useMemo(() => {
    return (
      [
        zoomIntegration ? 'Zoom' : '',
        googleMeetIntegration ? 'Google Meet' : '',
        microsoftTeamsIntegration ? 'Microsoft Teams' : '',
      ]
        .filter((i) => !!i)
        .join(', ') || 'None'
    );
  }, [zoomIntegration, googleMeetIntegration, microsoftTeamsIntegration]);

  const addRecorder = async () => {
    setIsAdding(true);
    const isSuccess = await RecallAiService.addBotToMeeting(link);
    if (isSuccess) {
      setAddedText(
        'The meeting recorder has been successfully initiated to join your meeting.',
      );
    } else {
      setError('There was an error in adding the recorder to your meeting.');
      setTimeout(() => {
        setError('');
      }, 2000);
    }
    setIsAdding(false);
    setLink('');
  };

  return (
    <Dialog
      open={open}
      onOpenChange={() => {
        onClose();
      }}
    >
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <VideoIcon className="mr-2 h-4 w-4" />
            Add Recorder to Meeting
          </DialogTitle>
          <DialogDescription className="pt-2">
            <p>
              You can add our Recorder to your meeting on-demand using the below
              form. Once your meeting ends, you can find the meeting in your
              &quot;Real Calls&quot; section within a few minutes.
            </p>
            <p>Integrations you have enabled: {integrationNames}</p>
          </DialogDescription>
        </DialogHeader>
        <div className="pb-4">
          <Label htmlFor="name" className="text-right">
            Meeting Link
          </Label>
          <Input
            id="name"
            value={link}
            className="col-span-3"
            onChange={(e) => {
              setLink(e.currentTarget.value);
            }}
          />
          <p className="mt-4 text-muted-foreground text-xs">
            This only works for Zoom, Google Meet and Microsoft Teams currently.
            <br />
            For Zoom, please copy and paste the link from Participants &gt;
            Invite &gt; Copy URL.
            <br />
            For Google Meet, please copy and paste the link from Meeting details
            (the i icon in the meeting).
            <br />
            For Microsoft Teams, please copy and paste the link from People &gt;
            Share invite &gt; Copy meeting link.
          </p>
        </div>
        <DialogFooter>
          <div className="flex flex-col w-full items-start">
            <Button
              onClick={() => {
                addRecorder();
              }}
              type="submit"
              disabled={!link || !!error || isAdding}
            >
              {isAdding ? (
                <Loader2Icon className="animate-spin mx-1" />
              ) : (
                <>Add to meeting</>
              )}
            </Button>
            {addedText && (
              <p className="text-green-600 text-sm mt-2">{addedText}</p>
            )}
            {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
