import LearningModule from '@/lib/LearningModule/types';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import AssigneesPreview from '../AssigneesPreview';
import { ProgressCircle } from '@tremor/react';
import dayjs from 'dayjs';
import { BadgeCheck, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import useRouting from '@/hooks/useRouting';
import LinksManager from '@/lib/linksManager';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  modules: LearningModule[];
  comingSoon?: boolean;
}

export default function ListView({ modules, comingSoon }: IProps) {
  const { isAdmin, dbUser } = useUserSession();
  const isDisabled = comingSoon && !isAdmin;
  const { goToPage } = useRouting();

  const edit = (lmId: string) => {
    goToPage(LinksManager.learningModules(lmId));
  };

  return (
    <Table className="w-full">
      <TableContent>
        <TableRow>
          <TableCellHead>Name</TableCellHead>
          <TableCellHead>
            {comingSoon ? 'Start Date' : 'Due Date'}
          </TableCellHead>
          <TableCellHead>Assignees</TableCellHead>
          <TableCellHead>Progress</TableCellHead>
          <TableCellHead className="w-[20px]">&nbsp;</TableCellHead>
        </TableRow>
        {modules.map((lm: LearningModule) => {
          let dueDate = dayjs(lm.dueDate);

          if (!isAdmin && dbUser) {
            if (lm.assigneesDueDate) {
              for (const add of lm.assigneesDueDate) {
                if (add.userId == dbUser.id) {
                  dueDate = dayjs(add.dueDate);
                  break;
                }
              }
            }
          }

          return (
            <TableRow
              key={lm.id}
              className={cn('hover:bg-gray-50 cursor-pointer group', {
                'opacity-50 cursor-default': isDisabled,
              })}
              onClick={() => {
                if (!isDisabled) {
                  edit(lm.id);
                }
              }}
            >
              <TableCell className="">
                <div className="">
                  <div
                    className={cn('text-sm font-medium flex items-center', {
                      'text-blue-500': lm.isCertification,
                    })}
                  >
                    {lm.name}
                    {lm.isCertification && (
                      <BadgeCheck
                        size={18}
                        className={cn('ml-1 text-blue-500')}
                      />
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1 text-wrap">
                    {lm.description}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {comingSoon
                  ? dayjs(lm.startDate).format('MMM D, YYYY')
                  : dueDate.format('MMM D, YYYY')}
              </TableCell>
              <TableCell className="w-[120px]">
                <AssigneesPreview assignees={lm.assignees} />
              </TableCell>
              <TableCell>
                <div className="flex items-start">
                  <ProgressCircle
                    color={'blue'}
                    value={lm.progress}
                    size="xs"
                  ></ProgressCircle>
                </div>
              </TableCell>
              <TableCell className="w-[20px]">
                <ChevronRight
                  className="text-muted-foreground group-hover:visible invisible"
                  size={16}
                />
              </TableCell>
            </TableRow>
          );
        })}
      </TableContent>
      <TableFooter className="invisible">
        <div>&nbsp;</div>
      </TableFooter>
    </Table>
  );
}
