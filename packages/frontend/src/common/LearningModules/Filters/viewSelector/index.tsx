import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { LayoutGrid, List } from 'lucide-react';

interface IProps {
  current: string;
  setView: (s: string) => void;
}

export default function ViewSelector({ current, setView }: IProps) {
  /*
  <Tabs defaultValue={'grid'} value={current}>
      <TabsList className="mx-3 h-[34px] rounded-[8px]">
        <TabsTrigger
          value="grid"
          onClick={() => {
            setView('grid');
          }}
          className="rounded-[8px] h-[30px] w-[30px]"
        >
          <LayoutGrid />
        </TabsTrigger>
        <TabsTrigger
          value="list"
          onClick={() => {
            setView('list');
          }}
          className="rounded-[8px] h-[30px] w-[30px]"
        >
          <List size={16} />
        </TabsTrigger>
      </TabsList>
    </Tabs>
  
  */

  return (
    <Tabs defaultValue={'grid'} value={current}>
      <TabsList className="mx-[8px] py-4">
        <TabsTrigger
          value="grid"
          onClick={() => {
            setView('grid');
          }}
          className="py-[7px] px-[7px]"
        >
          <LayoutGrid size={16} />
        </TabsTrigger>
        <TabsTrigger
          value="list"
          onClick={() => {
            setView('list');
          }}
          className="py-[7px] px-[7px]"
        >
          <List size={16} />
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
