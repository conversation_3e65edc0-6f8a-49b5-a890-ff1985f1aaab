import { Button } from '@/components/ui/button';
import { CalendarArrowDown, CalendarArrowUp } from 'lucide-react';

interface IProps {
  current: string;
  update: (s: string) => void;
}

export default function OrdeSelector({ current, update }: IProps) {
  if (current == 'asc') {
    return (
      <Button
        variant={'outline'}
        onClick={() => {
          update('desc');
        }}
        className="bg-white w-[240px]"
      >
        <CalendarArrowDown size={16} className="mr-2" />
        Sort by: Date (Ascending)
      </Button>
    );
  } else {
    return (
      <Button
        variant={'outline'}
        onClick={() => {
          update('asc');
        }}
        className="bg-white w-[240px]"
      >
        <CalendarArrowUp size={16} className="mr-2" />
        Sort by: Date (Descending)
      </Button>
    );
  }
}
