import LearningModule from '@/lib/LearningModule/types';
import LearningModuleCard from './Card';

interface IProps {
  modules: LearningModule[];
  comingSoon?: boolean;
}

export default function GridView({ modules, comingSoon }: IProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-3 2xl:grid-cols-4  w-full gap-3">
      {modules.map((lm: LearningModule) => {
        return (
          <LearningModuleCard
            key={lm.id}
            module={lm}
            isInGrid={true}
            comingSoon={comingSoon}
          />
        );
      })}
    </div>
  );
}
