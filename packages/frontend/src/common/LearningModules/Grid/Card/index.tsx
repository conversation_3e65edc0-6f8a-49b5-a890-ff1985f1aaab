import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import LearningModule, {
  LearningModuleStatus,
} from '@/lib/LearningModule/types';
import LinksManager from '@/lib/linksManager';
import { BG_COLORS } from '../../Edit/ModuleDetails/EditBackground';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { BadgeCheck, Info, TrendingDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { blurhashAsGradients } from 'blurhash-gradients';
import dayjs from 'dayjs';
import AssigneesPreview from '../../AssigneesPreview';
import { ProgressCircle } from '@tremor/react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface IProps {
  module: LearningModule;
  blockNavigation?: boolean;
  isInGrid?: boolean;
  comingSoon?: boolean;
}

export default function LearningModuleCard({
  module,
  blockNavigation,
  isInGrid,
  comingSoon,
}: IProps) {
  const { isAdmin, dbUser } = useUserSession();
  const isDisabled = comingSoon && !isAdmin;

  const lm = module;

  const { goToPage } = useRouting();

  const [isBehind, setIsBehind] = useState<boolean>(false);

  /******************************/
  /*********** SPLASH **********/
  /******************************/

  const loadImage = (imageUrl: string) => {
    const image = new Image();
    image.src = imageUrl;

    image.onload = () => {
      setSplashStyle({
        backgroundImage: `url('${imageUrl}')`,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        backgroundSize: 'cover',
        imageRendering: '-webkit-optimize-contrast',
      });
    };
  };

  const [spashStyle, setSplashStyle] = useState<any>({
    backgroundColor: BG_COLORS[0],
  });

  useEffect(() => {
    if (lm.splashImageInfo != '') {
      const splash = JSON.parse(lm.splashImageInfo);

      if (splash.type == 'image' && splash.blur_hash) {
        const css = blurhashAsGradients(splash.blur_hash);
        setSplashStyle(css);
        loadImage(splash.url);
      } else if (splash.type == 'image') {
        setSplashStyle({
          backgroundImage: `url('${splash.url}')`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundSize: 'cover',
        });
      } else {
        setSplashStyle({
          backgroundColor: splash.color,
        });
      }
    }

    if (lm) {
      let _isBehind = false;
      if (!isAdmin && dbUser) {
        let totUser = 0;
        let totOthers = 0;
        for (const sm of lm.subModules) {
          for (const as of sm.assigneesStats) {
            if (as.userId == dbUser.id) {
              totUser += as.numberOfTasksPassed;
            } else {
              totOthers = as.numberOfTasksPassed;
            }
          }
        }
        if (lm.assignees && lm.assignees.length > 0) {
          const avg = totOthers / lm.assignees.length;
          if (avg > totUser) {
            _isBehind = true;
          }
        }

        setIsBehind(_isBehind);
      }
    }
  }, []);

  /******************************/
  /*********** ACTIONS **********/
  /******************************/

  const openDetails = (lmId: string) => {
    if (!blockNavigation) {
      goToPage(LinksManager.learningModules(lmId));
    }
  };

  /******************************/
  /*********** RENDER ***********/
  /******************************/

  let dueDate = dayjs(lm.dueDate);

  let showAssigneesBreakdown = false;
  let dueDatePerAssignee: { [userId: number]: Date } = {};

  if (!isAdmin && dbUser) {
    if (lm.assigneesDueDate) {
      for (const add of lm.assigneesDueDate) {
        if (add.userId == dbUser.id) {
          dueDate = dayjs(add.dueDate);
          break;
        }
      }
    }
  } else if (
    !isInGrid &&
    isAdmin &&
    lm.assigneesDueDate &&
    lm.assigneesDueDate.length > 0 &&
    lm.status != LearningModuleStatus.COMPLETED
  ) {
    showAssigneesBreakdown = true;
    const tmp: { [userId: number]: Date } = {};
    for (const add of lm.assigneesDueDate) {
      let dd = add.dueDate;
      if (!dd) {
        dd = new Date();
      }
      tmp[add.userId] = dd;
    }
    dueDatePerAssignee = tmp;
  }

  return (
    <div
      onClick={() => {
        if (!isDisabled) {
          openDetails(lm.id);
        }
      }}
      className={cn('border rounded-lg overflow-hidden w-full bg-white ', {
        'hover:bg-gray-50 cursor-pointer': !blockNavigation,
        'opacity-50 cursor-default': isDisabled,
      })}
    >
      <div className="w-full h-[110px]" style={spashStyle}>
        <div className="flex items-center p-3">
          <div className="flex-1" />
          {lm.isCertification && (
            <div className="bg-black/20 rounded-full text-white px-2 py-1 font-medium flex items-center">
              <BadgeCheck size={18} className={cn('mr-1 ')} />
              Certificate
            </div>
          )}
        </div>
      </div>
      <div className="h-[160px] p-3 flex flex-col">
        <div className="flex items-start">
          <div className="flex-1">
            {isBehind && (
              <div className="text-xs text-muted-foreground flex items-center mb-1">
                <TrendingDown size={12} className="mr-2" />
                You are behind compared to your team
                <Info size={12} className="ml-1" />
              </div>
            )}
            <div
              className={cn('text-sm font-medium flex items-center', {
                'text-blue-500': lm.isCertification,
              })}
            >
              {lm.name}
              {lm.isCertification && (
                <BadgeCheck size={18} className={cn('ml-1 text-blue-500')} />
              )}
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              {lm.description}
            </div>
          </div>
          <div>
            {lm.status == LearningModuleStatus.IN_PROGRESS && (
              <ProgressCircle
                color={'blue'}
                value={lm.progress}
                size="xs"
              ></ProgressCircle>
            )}
            {lm.status == LearningModuleStatus.COMPLETED && (
              <div className="text-xs p-1 px-2 bg-blue-100 text-blue-600 rounded-full border">
                Completed
              </div>
            )}
            {lm.status == LearningModuleStatus.IN_PREPARATION && (
              <div className="text-xs text-muted-foreground">Draft</div>
            )}
          </div>
        </div>

        <div className="flex-1" />
        {!showAssigneesBreakdown && (
          <div className="flex items-center">
            <div className="text-muted-foreground">
              {lm.status == LearningModuleStatus.COMPLETED
                ? 'Completed:'
                : comingSoon
                  ? 'Start date:'
                  : 'Due date:'}
            </div>
            <div className="ml-2">
              {comingSoon
                ? dayjs(lm.startDate).format('MMM D, YYYY')
                : dueDate.format('MMM D, YYYY')}
            </div>
            <div className="flex-1" />
            <div>
              <AssigneesPreview assignees={lm.assignees} />
            </div>
          </div>
        )}

        {showAssigneesBreakdown && (
          <div className="mt-2 overflow-auto max-h-[80px]">
            {lm.assignees.map((u) => {
              let dd = dueDatePerAssignee[u.id];
              if (dd) {
                if (typeof dd == 'string') {
                  dd = new Date(dd);
                }
              }
              return (
                <div key={u.id} className="flex items-center mb-1">
                  <div className="flex items-center">
                    <Avatar className="w-[24px] h-[24px] border-white border-2">
                      <AvatarImage src={u?.avatar} />
                      <AvatarFallback className="text-sm">
                        {u?.firstName?.charAt(0) || ''}
                        {u?.lastName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      {u.firstName} {u.lastName}
                    </div>
                  </div>
                  <div className="flex-1" />
                  <div className="w-[130px]">
                    <span className="text-muted-foreground mr-1">due</span>
                    <span>{dayjs(dd).format('MMM D, YYYY')}</span>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
