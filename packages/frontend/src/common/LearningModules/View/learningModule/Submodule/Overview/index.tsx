import { SubModule, Task, TaskStats } from '@/lib/LearningModule/types';
import { UserDto } from '@/lib/User/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import TaskView from '../TaskView';
import { useEffect, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface IProps {
  sm: SubModule;
  assignees: UserDto[];
}

export default function SubModuleOverviewPanel({ sm, assignees }: IProps) {
  const [shownTask, setShownTask] = useState<Task>({ id: 'all' } as Task);
  const [orderedAssignees, setOrderedAssigees] = useState<UserDto[]>([]);

  const selectTask = (tid: string) => {
    if (tid == 'all') {
      setShownTask({ id: 'all' } as Task);
    } else {
      for (const t of sm.tasks) {
        if (t.id == tid) {
          setShownTask(t);
          break;
        }
      }
    }
  };

  useEffect(() => {
    setShownTask({ id: 'all' } as Task);
  }, [sm]);

  useEffect(() => {
    if (assignees) {
      setOrderedAssigees(
        assignees.sort((a, b) => a.lastName?.localeCompare(b.lastName)),
      );
    }
  }, [assignees]);

  const statsPerAssignee: any = {};
  for (const as of sm.assigneesStats) {
    if (shownTask.id == 'all') {
      statsPerAssignee[as.userId] = as.numberOfTasksPassed;
    } else {
      for (const us of as.tasks) {
        if (us.taskId == shownTask.id) {
          statsPerAssignee[as.userId] = us;
        }
      }
    }
  }

  return (
    <div>
      <div className="mb-4">
        <div className="text-xs mb-1">Select a task</div>
        <Select
          onValueChange={(v: string) => {
            selectTask(v);
          }}
          value={shownTask.id}
        >
          <SelectTrigger className="overflow-hidden h-full">
            {shownTask.id == 'all' ? (
              <div className="mt-4 mb-4">All tasks</div>
            ) : (
              <div>
                <TaskView task={shownTask} isPreview={true} />
              </div>
            )}
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={'all'}>
              <div className="mt-4 mb-4">All tasks</div>
            </SelectItem>
            {sm.tasks &&
              sm.tasks.length > 0 &&
              sm.tasks.map((t: Task) => {
                return (
                  <SelectItem key={t.id} value={t.id}>
                    <TaskView task={t} isPreview={true} />
                  </SelectItem>
                );
              })}
          </SelectContent>
        </Select>
      </div>
      <div className="mb-1">
        {orderedAssignees.map((u) => {
          let showCompleted = true;
          const totalCompleted = statsPerAssignee[u.id] | 0;
          const userStats = statsPerAssignee[u.id];
          if (shownTask.id != 'all') {
            showCompleted = false;
          }
          return (
            <div key={u.id} className="mb-2 flex items-center">
              <div className="">
                <div className="flex space-x-2 items-center z-50 m-2">
                  <Avatar className="w-6 h-6">
                    {u?.avatar && <AvatarImage src={u.avatar} alt="Avatar" />}
                    <AvatarFallback className="text-sm">
                      {u?.firstName?.charAt(0) || ''}
                      {u?.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="capitalize">
                    {u?.firstName || ''} {u?.lastName || ''}
                  </div>
                </div>
              </div>

              <div className="flex-1" />
              <div>
                {showCompleted && (
                  <div className="text-xs text-muted-foreground">
                    {totalCompleted}/{sm.tasks.length} completed
                  </div>
                )}
                {!showCompleted && (
                  <div className="text-xs text-muted-foreground">
                    {userStats ? (
                      userStats.passed ? (
                        <span className="text-blue-500">Completed</span>
                      ) : (
                        <span className="text-blue-500">
                          {userStats.attempts?.length} attempts
                        </span>
                      )
                    ) : (
                      <span className="text-red-400">0 attempts</span>
                    )}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
