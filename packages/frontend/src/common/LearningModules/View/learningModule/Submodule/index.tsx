import { SubModule } from '@/lib/LearningModule/types';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useEffect, useState } from 'react';
import useUserSession from '@/hooks/useUserSession';
import SubModuleDetailsPanel from './DetailsPanel';
import SubModuleOverviewPanel from './Overview';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';

interface IProps {
  sm: SubModule;
  onStartCall: (agent: AgentDto) => void;
  assignees: UserDto[];
}

export default function SubModuleDetails({
  sm,
  onStartCall,
  assignees,
}: IProps) {
  const { isAdmin } = useUserSession();
  const [view, setView] = useState('details');

  return (
    <div className="w-full border bg-white rounded-lg p-3 pb-0">
      <div className="flex items-center mb-4">
        <div className="font-semibold flex-1">{sm.name}</div>
        {isAdmin && (
          <div>
            <Tabs defaultValue={'details'} value={view}>
              <TabsList className="mx-3">
                <TabsTrigger
                  value="details"
                  onClick={() => {
                    setView('details');
                  }}
                >
                  Details
                </TabsTrigger>
                <TabsTrigger
                  value="overview"
                  onClick={() => {
                    setView('overview');
                  }}
                >
                  Overview
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        )}
      </div>

      <div>
        {view == 'details' && (
          <SubModuleDetailsPanel
            sm={sm}
            onStartCall={onStartCall}
            assignees={assignees}
          />
        )}

        {view == 'overview' && (
          <SubModuleOverviewPanel sm={sm} assignees={assignees} />
        )}
      </div>
    </div>
  );
}
