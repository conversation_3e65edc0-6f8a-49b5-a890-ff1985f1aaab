import { AssigneeStats, SubModule, Task } from '@/lib/LearningModule/types';
import TaskView from '../TaskView';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';
import useUserSession from '@/hooks/useUserSession';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface IProps {
  sm: SubModule;
  onStartCall: (agent: AgentDto) => void;
  assignees: UserDto[];
}

export default function SubModuleDetailsPanel({
  sm,
  onStartCall,
  assignees,
}: IProps) {
  const { isAdmin, dbUser } = useUserSession();

  const [shownAssignee, setShownAssignee] = useState<UserDto>({
    id: 0,
  } as UserDto);

  useEffect(() => {
    if (isAdmin) {
      setShownAssignee(assignees[0]);
    } else {
      if (dbUser) {
        setShownAssignee(dbUser);
      }
    }
  }, [isAdmin]);

  const selectAssignee = (uid: number) => {
    for (const a of assignees) {
      if (a.id == uid) {
        setShownAssignee(a);
        break;
      }
    }
  };

  const statsPerTask: any = {};
  for (const as of sm.assigneesStats) {
    if (as.userId == shownAssignee?.id) {
      for (const ts of as.tasks) {
        statsPerTask[ts.taskId] = ts;
      }
    }
  }

  return (
    <div>
      {isAdmin && (
        <div className="mb-3">
          <div className="text-xs mb-1">Select assignee</div>
          <div className="w-[300px]">
            <Select
              onValueChange={(v: string) => {
                selectAssignee(parseInt(v));
              }}
              value={String(shownAssignee?.id)}
            >
              <SelectTrigger>
                <div className="flex space-x-2 items-center">
                  <Avatar className="w-6 h-6">
                    {shownAssignee?.avatar && (
                      <AvatarImage src={shownAssignee.avatar} alt="Avatar" />
                    )}
                    <AvatarFallback className="text-sm">
                      {shownAssignee?.firstName?.charAt(0) || ''}
                      {shownAssignee?.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="capitalize">
                    {shownAssignee?.firstName || ''}{' '}
                    {shownAssignee?.lastName || ''}
                  </div>
                </div>
              </SelectTrigger>
              <SelectContent>
                {assignees?.map((v) => {
                  return (
                    <SelectItem key={v.id} value={String(v.id)}>
                      <div className="flex space-x-2 items-center">
                        <Avatar className="w-6 h-6">
                          {v?.avatar && (
                            <AvatarImage src={v.avatar} alt="Avatar" />
                          )}
                          <AvatarFallback className="text-sm">
                            {v?.firstName?.charAt(0) || ''}
                            {v?.lastName?.charAt(0) || ''}
                          </AvatarFallback>
                        </Avatar>
                        <div className="capitalize">
                          {v?.firstName || ''} {v?.lastName || ''}
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
      {sm.tasks &&
        sm.tasks.length > 0 &&
        sm.tasks.map((t: Task) => {
          const stats = statsPerTask[t.id];
          return (
            <TaskView
              task={t}
              key={t.id}
              onStartCall={onStartCall}
              userStats={stats}
            />
          );
        })}
    </div>
  );
}
