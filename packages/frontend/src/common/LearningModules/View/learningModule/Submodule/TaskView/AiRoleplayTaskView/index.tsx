import {
  AiRoleplayTask,
  LearningModuleTaskType,
  Task,
  TaskAttempt,
  TaskStats,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import useUserSession from '@/hooks/useUserSession';
import { But<PERSON> } from '@/components/ui/button';
import { ExternalLink, Phone, Trash2Icon } from 'lucide-react';
import { AgentDto } from '@/lib/Agent/types';
import { useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import dayjs from 'dayjs';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';
import AgentAvatar from '@/components/Avatars/Agent';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import { UserDto } from '@/lib/User/types';
import LearningModuleService from '@/lib/LearningModule';
import { Id, toast } from 'react-toastify';
import { AppPermissions } from '@/lib/permissions';
import { useQueryClient } from '@tanstack/react-query';
import { useCommonQueryKeys } from '@/hooks/useCommonQueryKeys';

interface IProps {
  task: Task;
  onStartCall?: (agent: AgentDto) => void;
  userStats?: TaskStats;
  isPreview?: boolean;
  currentAssignee?: UserDto;
  learningModuleId?: string;
}

export default function AiRoleplayTaskView({
  task,
  onStartCall,
  userStats,
  isPreview,
  currentAssignee,
  learningModuleId,
}: IProps) {
  const { canAccess, isAdmin } = useUserSession();
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();
  const router = useRouter();
  const [showAttemptsPanel, setShowAttemptsPanel] = useState<boolean>(true);

  const [deleteAttemptConfirmation, setDeleteAttemptConfirmation] =
    useState<boolean>(false);
  const [attemptToDelete, setAttemptToDelete] = useState<TaskAttempt | null>(
    null,
  );
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const toastId = useRef<Id | null>(null);

  if (task.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
    const t: AiRoleplayTask = task.info;

    const stats: string[] = [];
    for (const s of t.stats) {
      stats.push(String(s.name));
    }

    const startCall = () => {
      if (t.agent && onStartCall) {
        onStartCall(t.agent);
      }
    };

    const toggleAttemptsPanel = () => {
      if (userStats) {
        if (userStats.attempts?.length > 0) {
          setShowAttemptsPanel(!showAttemptsPanel);
        }
      }
    };

    const handleDeleteAttempt = async (attempt: TaskAttempt) => {
      if (!canAccess(AppPermissions.DELETE_ATTEMPTS)) {
        toastId.current = toast.error(
          'You are not authorized to delete attempts',
        );
        return;
      }
      setIsDeleting(true);
      try {
        await LearningModuleService.deleteAttempt(
          learningModuleId!,
          task.id,
          attempt.id,
        );
        queryClient.invalidateQueries({
          queryKey: [
            'learning-module',
            learningModuleId,
            isAdmin,
            ...commonQueryKeys,
          ],
        });
        if (!toast.isActive(toastId.current as Id)) {
          toastId.current = toast.success('Attempt deleted successfully');
        }
      } catch (error) {
        console.error('Error deleting attempt:', error);
        if (!toast.isActive(toastId.current as Id)) {
          toastId.current = toast.error('Error deleting attempt');
        }
      } finally {
        setIsDeleting(false);
        setDeleteAttemptConfirmation(false);
        setAttemptToDelete(null);
      }
    };

    const processMessage = (passed: boolean) => {
      if(passed){
        return {msg: 'Passed', color: 'text-green-500'}
      } else {
        return {msg: 'Failed', color: 'text-red-400'}
      }
      // let msg = r.replace('[RESCORED]', '');
      // msg = msg.replace('[past-due-date]', '')
      // if(msg.indexOf('(passed)') > -1){
      //   return {msg: msg.replace('(passed)', '').trim(), color: 'text-green-500'}
      // } else if (msg.indexOf('(failed)') > -1) {
      //   return {msg: msg.replace('(failed)', '').trim(), color: 'text-red-400'}
      // }

    }
    return (
      <div className={cn('rounded-lg', { 'border py-3 mb-3': !isPreview })}>
        <div className="flex items-center mb-4 px-3">
          <div className="border rounded-full flex items-center ">
            <AgentAvatar
              className="w-[26px] h-[26px] border-white border-2"
              agent={t?.agent}
            />
            <div className="capitalize mx-1 font-medium mr-2">
              {t.agent?.firstName || ''} {t.agent?.lastName || ''}
            </div>
          </div>
          {userStats?.attempts.length ? <div className='flex'>
            <div className='mx-2 text-[#EDEDED]'>
            |
              </div>
            <div className='text-[#71717A]'> {userStats?.attempts.length} attempts</div>
           
          </div> : <></>}
          <div className="flex-1" />
          <div>
            <Button size="sm" variant="outline" onClick={startCall}>
              <Phone size={16} className="mr-2" />
              Make a call
            </Button>
          </div>
        </div>

        <div className="flex items-end">
          <div className="flex-1">
            {t.minNumberOfAttempts && t.minNumberOfAttempts > 0 && (
              <div className="flex items-center text-xs mt-2 px-3">
                <div className="text-muted-foreground">
                  Required successful attempts:
                </div>
                <div className="ml-2">
                  {t.minNumberOfAttempts == 0
                    ? 'unlimited'
                    : t.minNumberOfAttempts}
                </div>
              </div>
            )}

            {t.minScorecardScore != undefined && t.minScorecardScore > 0 && (
              <div className="flex items-center text-xs mt-2 px-3">
                <div className="text-muted-foreground ">
                  Min scorecard score:
                </div>
                <div className="ml-2">
                  {t.minScorecardScore == 0 ? 'Any' : t.minScorecardScore}
                </div>
              </div>
            )}

            {t.criterions && t.criterions.length > 0 && (
              <div className="flex items-start text-xs mt-2 px-3">
                <div className="text-muted-foreground text-nowrap">
                  Pass criteria:
                </div>
                <div className="ml-2 max-w-[600px] pr-4">
                  {t.criterions.map((c: any, i: number) => {
                    return (
                      <span key={'cr' + i}>
                        {i > 0 && ', '}
                        {c.criterion}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {!isPreview && (
            <div className="flex items-center px-3">
              <div className="text-muted-foreground text-xs">AI Buyer Bot</div>
              <div className="border-l pl-2 text-xs ml-2">
                {userStats ? (
                  userStats.passed ? (
                    <span className="text-blue-500">Completed</span>
                  ) : (
                    <span
                      className={cn('text-blue-500', {
                        'hover:underline cursor-pointer':
                          userStats.attempts?.length > 0,
                      })}
                      onClick={toggleAttemptsPanel}
                    >
                      {userStats.attempts?.length} attempts
                    </span>
                  )
                ) : (
                  <span className="text-red-400">0 attempts</span>
                )}
              </div>
            </div>
          )}
        </div>

        {showAttemptsPanel && (
          <AnimatePresence>
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: 'auto' }}
              exit={{ height: 0 }}
              transition={{ duration: 0.2 }}
              
            >
               {userStats?.attempts?.length ?<div className='flex bg-[#FBFBFB] py-3 px-3 border-b border-t border-[#E4E4E7] font-medium mt-5'>
                  <div className='text-[#71717A] w-[160px]'>Attempt Date</div>
                  <div className='text-[#71717A]'>Result</div>
                </div>: <></>}
              <div className="text-xs overflow-auto max-h-[160px]">
               
                {userStats?.attempts?.map((a: TaskAttempt, i: number) => {
                  let results: string[] = [];
                  if (a.results && a.results !== '') {
                    results = a.results.split('\n');
                  }
                  return (
                    <div
                      key={i}
                      className="border-b py-2 px-3"
                    >
                      <div className="flex items-center">
                        <div className='w-[160px]'>{dayjs(a.date).format('dddd, MMMM D')}</div>
                        <div className={
                          cn('py-1 px-2 font-medium leading-[16px] border rounded-[100px] mr-[34px]',
                          {
                            'bg-[#3C82F61A]': a.passed,
                            'bg-[#EF44441A]': !a.passed,
                            'text-[#105AD4]': a.passed,
                            'text-[#EF4444]': !a.passed,
                            'border-[#3C82F633]': a.passed,
                            'border-[#EF44441A]': !a.passed

                          })
                        }>{
                          a.passed ? 'Passed' : 'Failed'}</div>
                        <div className="whitespace-pre text-muted-foreground text-xs w-[490px]">
                        <div className={ a.passed ? 'text-[#105AD4]' : 'text-primary'}>
                            {a.passed ? 'Good job on clearing the scorecard criteria and getting a good score!': 'You need to pass the mandatory scorecard criteria or clear the minimum score to pass'}
                          </div>
                          {/* {results.map((r: string, k: number) => {
                            console.log(results)
                            const processedResult = processMessage(a.passed)
                            return (
                              <div key={k} className={processedResult?.color}>
                                {processedResult?.msg}
                              </div>
                            );
                          })} */}
                        </div>
                        <div className="flex-1" />
                        <div className="text-xs text-muted-foreground p-[14px] cursor-pointer text-[#2E3035] hover:text-gray-500">
                          <ExternalLink size={16} onClick={() => {
                          router.push(LinksManager.trainingCalls(a.callVapiId));
                          }}/>
                        </div>
                        {canAccess(AppPermissions.DELETE_ATTEMPTS) && (
                          <div
                            className="ml-2 text-xs text-muted-foreground p-[14px]"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setDeleteAttemptConfirmation(true);
                              setAttemptToDelete(a);
                            }}
                          >
                            <Trash2Icon
                              className="text-red-400 hover:text-red-500 cursor-pointer"
                              size={16}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </motion.div>
          </AnimatePresence>
        )}
        <DeleteConfirmationModal
          open={deleteAttemptConfirmation}
          onCancel={() => {
            setDeleteAttemptConfirmation(false);
            setAttemptToDelete(null);
          }}
          onConfirm={() => {
            if (attemptToDelete) {
              handleDeleteAttempt(attemptToDelete);
            }
          }}
          isLoading={isDeleting}
          title={'Delete attempt'}
          description={
            <>
              Are you sure you want to delete{' '}
              <span className="font-bold">
                {currentAssignee?.firstName} {currentAssignee?.lastName}
              </span>{' '}
              attempt{' '}
              {attemptToDelete
                ? (userStats?.attempts?.indexOf(attemptToDelete) ?? 0) + 1
                : 0}{' '}
              on{' '}
              <span className="font-bold">
                {task.info.agent?.firstName} {task.info.agent?.lastName}
              </span>{' '}
              ?
            </>
          }
        />
      </div>
    );
  }
}
