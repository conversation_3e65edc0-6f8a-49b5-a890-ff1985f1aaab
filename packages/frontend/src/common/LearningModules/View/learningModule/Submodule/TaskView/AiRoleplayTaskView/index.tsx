import { STATS_OPTIONS } from '@/lib/common-types';
import {
  AiRoleplayTask,
  LearningModuleTaskType,
  Task,
  TaskStats,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useUserSession from '@/hooks/useUserSession';
import { Button } from '@/components/ui/button';
import { ExternalLink, Link, Phone, SquareArrowOutUpRight } from 'lucide-react';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';
import { ProgressCircle } from '@tremor/react';
import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import dayjs from 'dayjs';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';
import AgentAvatar from '@/components/Avatars/Agent';

interface IProps {
  task: Task;
  onStartCall?: (agent: AgentDto) => void;
  userStats?: TaskStats;
  isPreview?: boolean;
}

export default function AiRoleplayTaskView({
  task,
  onStartCall,
  userStats,
  isPreview,
}: IProps) {
  const { isAdmin } = useUserSession();
  const router = useRouter();
  const [showAttemptsPanel, setShowAttemptsPanel] = useState<boolean>(true);

  if (task.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
    const t: AiRoleplayTask = task.info;

    const stats: string[] = [];
    for (const s of t.stats) {
      stats.push(String(s.name));
    }

    const startCall = () => {
      if (t.agent && onStartCall) {
        onStartCall(t.agent);
      }
    };

    const toggleAttemptsPanel = () => {
      if (userStats) {
        if (userStats.attempts?.length > 0) {
          setShowAttemptsPanel(!showAttemptsPanel);
        }
      }
    };

    return (
      <div className={cn('rounded-lg ', { 'border p-3 mb-3': !isPreview })}>
        <div className="flex items-center mb-4">
          <div className="border rounded-full flex items-center ">
            <AgentAvatar
              className="w-[26px] h-[26px] border-white border-2"
              agent={t?.agent}
            />
            <div className="capitalize mx-1 font-medium mr-2">
              {t.agent?.firstName || ''} {t.agent?.lastName || ''}
            </div>
          </div>
          <div className="flex-1" />
          <div>
            <Button size="sm" variant="outline" onClick={startCall}>
              <Phone size={16} className="mr-2" />
              Make a call
            </Button>
          </div>
        </div>

        <div className="flex items-end">
          <div className="flex-1">
            {t.minNumberOfAttempts && t.minNumberOfAttempts > 0 && (
              <div className="flex items-center text-xs mt-2">
                <div className="text-muted-foreground ">
                  Required successful attempts:
                </div>
                <div className="ml-2">
                  {t.minNumberOfAttempts == 0
                    ? 'unlimited'
                    : t.minNumberOfAttempts}
                </div>
              </div>
            )}

            {t.minScorecardScore != undefined && t.minScorecardScore > 0 && (
              <div className="flex items-center text-xs mt-2">
                <div className="text-muted-foreground ">
                  Min scorecard score:
                </div>
                <div className="ml-2">
                  {t.minScorecardScore == 0 ? 'Any' : t.minScorecardScore}
                </div>
              </div>
            )}

            {stats.length > 0 && (
              <div className="flex items-center text-xs mt-2">
                <div className="text-muted-foreground ">Stats:</div>
                <div className="ml-2">
                  {stats.length == 0 && 'Any'}
                  {stats.map((s: any, i: number) => {
                    return STATS_OPTIONS.map((so: any) => {
                      if (so.id == s) {
                        return (
                          <span key={so.id}>
                            {i > 0 && ', '}
                            {so.label}
                          </span>
                        );
                      }
                    });
                  })}
                </div>
              </div>
            )}

            {t.criterions && t.criterions.length > 0 && (
              <div className="flex items-start text-xs mt-2">
                <div className="text-muted-foreground text-nowrap">
                  Pass this criteria:
                </div>
                <div className="ml-2 max-w-[600px] pr-4">
                  {t.criterions.map((c: any, i: number) => {
                    return (
                      <span key={'cr' + i}>
                        {i > 0 && ', '}
                        {c.criterion}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {!isPreview && (
            <div className="flex items-center">
              <div className="text-muted-foreground text-xs">AI Buyer Bot</div>
              <div className="border-l pl-2 text-xs ml-2">
                {userStats ? (
                  userStats.passed ? (
                    <span className="text-blue-500">Completed</span>
                  ) : (
                    <span
                      className={cn('text-blue-500', {
                        'hover:underline cursor-pointer':
                          userStats.attempts?.length > 0,
                      })}
                      onClick={toggleAttemptsPanel}
                    >
                      {userStats.attempts?.length} attempts
                    </span>
                  )
                ) : (
                  <span className="text-red-400">0 attempts</span>
                )}
              </div>
            </div>
          )}
        </div>

        {showAttemptsPanel && (
          <AnimatePresence>
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: 'auto' }}
              exit={{ height: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-auto max-h-[200px]"
            >
              <div className="text-xs mt-2">
                {userStats?.attempts?.map((a: any, i: number) => {
                  let results = [];
                  if (a.results && a.results != '') {
                    results = a.results.split('\n');
                  }
                  return (
                    <div
                      key={i}
                      className="border-b py-2 px-2 cursor-pointer hover:bg-gray-50"
                      onClick={() => {
                        router.push(LinksManager.trainingCalls(a.callVapiId));
                      }}
                    >
                      <div className="flex items-center">
                        <div className="text-muted-foreground text-xs w-[80px]">
                          Attempt {i + 1}
                        </div>
                        <div>{dayjs(a.date).format('dddd, MMMM D')}</div>
                        {/* <div
                          className={cn('text-xs ml-4', {
                            'text-red-400': !a.passed,
                            'text-blue-500': a.passed,
                          })}
                        >
                          {a.passed ? 'Passed' : 'Failed'}
                        </div> */}
                        <div className="ml-4 whitespace-pre text-muted-foreground text-xs">
                          {results.map((r: string, k: number) => {
                            let msg = r.replace('[RESCORED]', '');
                            let color = '';
                            if (msg.indexOf('[past-due-date])') > -1) {
                              color = 'text-red-400';
                              msg = 'Past due date attempt';
                            } else if (msg.indexOf('(passed)') > -1) {
                              color = 'text-green-500';
                              msg = msg.replace('(passed)', '').trim();
                            } else if (msg.indexOf('(failed)') > -1) {
                              color = 'text-red-400';
                              msg = msg.replace('(failed)', '').trim();
                            }
                            return (
                              <div key={k} className={color}>
                                {msg}
                              </div>
                            );
                          })}
                        </div>
                        <div className="flex-1" />
                        <div className="text-xs text-muted-foreground">
                          <ExternalLink size={18} />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </motion.div>
          </AnimatePresence>
        )}
      </div>
    );
  }
}
