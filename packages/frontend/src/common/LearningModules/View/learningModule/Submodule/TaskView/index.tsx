import {
  LearningModuleTaskType,
  Task,
  TaskStats,
} from '@/lib/LearningModule/types';
import AiRoleplayTaskView from './AiRoleplayTaskView';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';
import CallBlitzTaskView from './CallBlitzTaskView';

interface IProps {
  task: Task;
  onStartCall?: (agent: AgentDto) => void;
  userStats?: TaskStats;
  isPreview?: boolean;
  currentAssignee?: UserDto;
  learningModuleId?: string;
}

export default function TaskView({
  task,
  onStartCall,
  userStats,
  isPreview,
  currentAssignee,
  learningModuleId,
}: IProps) {
  console.log('task', task);
  if (task.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
    return (
      <AiRoleplayTaskView
        task={task}
        onStartCall={onStartCall}
        userStats={userStats}
        isPreview={isPreview}
        currentAssignee={currentAssignee}
        learningModuleId={learningModuleId}
      />
    );
  }

  if (task.type == LearningModuleTaskType.CALL_BLITZ) {
    return (
      <CallBlitzTaskView
        task={task}
        userStats={userStats}
        isPreview={isPreview}
        learningModuleId={learningModuleId}
      />
    );
  }
}
