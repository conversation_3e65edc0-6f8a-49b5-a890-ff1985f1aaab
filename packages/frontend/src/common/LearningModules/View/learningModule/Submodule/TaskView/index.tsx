import {
  LearningModuleTaskType,
  Task,
  TaskStats,
} from '@/lib/LearningModule/types';
import AiRoleplayTaskView from './AiRoleplayTaskView';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';

interface IProps {
  task: Task;
  onStartCall?: (agent: AgentDto) => void;
  userStats?: TaskStats;
  isPreview?: boolean;
}

export default function TaskView({
  task,
  onStartCall,
  userStats,
  isPreview,
}: IProps) {
  if (task.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
    return (
      <AiRoleplayTaskView
        task={task}
        onStartCall={onStartCall}
        userStats={userStats}
        isPreview={isPreview}
      />
    );
  }
}
