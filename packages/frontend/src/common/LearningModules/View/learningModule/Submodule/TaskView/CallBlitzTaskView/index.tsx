'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Trophy, PlayIcon } from 'lucide-react';
import {
  CallBlitzTask,
  LearningModuleTaskType,
  TaskAttempt,
  TaskStats,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import { useState, useRef } from 'react';
import { toast, Id } from 'react-toastify';
import LearningModuleService from '@/lib/LearningModule';
import CallBlitzPanel from '@/common/LearningModulesV2/View/callBlitzPanel';
import dayjs from 'dayjs';
import { AnimatePresence, motion } from 'framer-motion';

interface IProps {
  task: {
    id: string;
    type: LearningModuleTaskType.CALL_BLITZ;
    info: CallBlitzTask;
  };
  userStats?: TaskStats;
  isPreview?: boolean;
  learningModuleId?: string;
}

export default function CallBlitzTaskView({
  task,
  userStats,
  isPreview,
  learningModuleId,
}: IProps) {
  const [isCreatingSession, setIsCreatingSession] = useState<boolean>(false);
  const toastId = useRef<Id | null>(null);
  const [showCallBlitzPanel, setShowCallBlitzPanel] = useState(false);
  const [overlaySessionId, setOverlaySessionId] = useState<number | null>(null);
  const [showAttemptsPanel, setShowAttemptsPanel] = useState<boolean>(true);

  if (task.type == LearningModuleTaskType.CALL_BLITZ) {
    const t: CallBlitzTask = task.info;

    const handleStartCallBlitz = async () => {
      if (isCreatingSession) return;
      if (!learningModuleId) {
        toastId.current = toast.error('Learning module ID not available');
        return;
      }

      setIsCreatingSession(true);
      try {
        const response = await LearningModuleService.startCallBlitzForTask(
          learningModuleId,
          task.id,
        );
        if (response.sessionId) {
          setOverlaySessionId(response.sessionId);
          setShowCallBlitzPanel(true);
        }
      } catch (error) {
        console.error('Error creating call blitz session:', error);
        toastId.current = toast.error(
          'There was an error starting the call blitz session. Please try again.',
        );
      } finally {
        setIsCreatingSession(false);
      }
    };

    const userHasPassed = userStats?.passed || false;
    const userAttempts = userStats?.attempts || [];
    const lastAttempt = userAttempts[userAttempts.length - 1];

    return (
      <>
        <div className={cn('rounded-lg', { 'border py-3 mb-3': !isPreview })}>
          <div className="flex items-center mb-4 px-3">
            <div className="border rounded-full flex items-center p-0.5">
              <Trophy size={20} className="text-blue-600 mx-1" />
              <div className="capitalize mx-1 font-medium mr-2">
                {t.sessionName || 'Call Blitz'}
              </div>
            </div>
            <div className="flex-1" />
            {!isPreview && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleStartCallBlitz}
                disabled={
                  isCreatingSession ||
                  (userHasPassed &&
                    t.maxNumberOfCalls !== undefined &&
                    userAttempts.length >= t.maxNumberOfCalls)
                }
              >
                <PlayIcon className="h-4 w-4 mr-2" />
                {isCreatingSession
                  ? 'Creating Session...'
                  : userHasPassed
                    ? 'Start Another Call Blitz'
                    : 'Start Call Blitz'}
              </Button>
            )}
          </div>

          <div className="flex items-end">
            <div className="flex-1">
              {t.minNumberOfCalls !== undefined && t.minNumberOfCalls > 0 && (
                <div className="flex items-center text-xs mt-2 px-3">
                  <div className="text-muted-foreground">
                    Min calls required:
                  </div>
                  <div className="ml-2">{t.minNumberOfCalls}</div>
                </div>
              )}
              {t.maxNumberOfCalls !== undefined && t.maxNumberOfCalls > 0 && (
                <div className="flex items-center text-xs mt-2 px-3">
                  <div className="text-muted-foreground">
                    Max calls allowed:
                  </div>
                  <div className="ml-2">{t.maxNumberOfCalls}</div>
                </div>
              )}
              {/* {t.minScorecardScore !== undefined && t.minScorecardScore > 0 && (
                <div className="flex items-center text-xs mt-2 px-3">
                  <div className="text-muted-foreground">
                    Min average score:
                  </div>
                  <div className="ml-2">{t.minScorecardScore}</div>
                </div>
              )} */}
            </div>
            {!isPreview && (
              <div className="flex items-center px-3">
                <div className="text-muted-foreground text-xs">Call Blitz</div>
                <div className="border-l pl-2 text-xs ml-2">
                  {userStats?.passed ? (
                    <span className="text-blue-500">Completed</span>
                  ) : (
                    <span
                      className={cn('text-blue-500', {
                        'hover:underline cursor-pointer':
                          (userStats?.attempts?.length || 0) > 0,
                      })}
                      onClick={() => setShowAttemptsPanel(!showAttemptsPanel)}
                    >
                      {userStats?.attempts?.length || 0}/{t.minNumberOfCalls}{' '}
                      calls done
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {showAttemptsPanel && (
            <AnimatePresence>
              <motion.div
                initial={{ height: 0 }}
                animate={{ height: 'auto' }}
                exit={{ height: 0 }}
                transition={{ duration: 0.2 }}
              >
                {userStats?.attempts?.length ? (
                  <div className="flex bg-[#FBFBFB] py-3 px-3 border-b border-t border-[#E4E4E7] font-medium mt-5">
                    <div className="text-[#71717A] w-[160px]">Attempt Date</div>
                    <div className="text-[#71717A]">Result</div>
                  </div>
                ) : (
                  <></>
                )}
                <div className="text-xs overflow-auto max-h-[160px]">
                  {userStats?.attempts?.map((a: TaskAttempt, i: number) => {
                    return (
                      <div key={i} className="border-b py-2 px-3">
                        <div className="flex items-center">
                          <div className="w-[160px]">
                            {dayjs(a.date).format('dddd, MMMM D HH:mm')}
                          </div>
                          <div
                            className={cn(
                              'py-1 px-2 font-medium leading-[16px] border rounded-[100px] mr-[34px]',
                              {
                                'bg-[#3C82F61A]': a.passed,
                                'bg-[#EF44441A]': !a.passed,
                                'text-[#105AD4]': a.passed,
                                'text-[#EF4444]': !a.passed,
                                'border-[#3C82F633]': a.passed,
                                'border-[#EF44441A]': !a.passed,
                              },
                            )}
                          >
                            {a.passed ? 'Passed' : 'Failed'}
                          </div>
                          <div className="whitespace-pre text-muted-foreground text-xs w-[490px]">
                            <div
                              className={
                                a.passed ? 'text-[#105AD4]' : 'text-primary'
                              }
                            >
                              {a.passed
                                ? 'Good job on clearing the scorecard criteria and getting a good score!'
                                : 'You need to pass the mandatory scorecard criteria or clear the minimum score to pass'}
                            </div>
                            {/* {results.map((r: string, k: number) => {
                            console.log(results)
                            const processedResult = processMessage(a.passed)
                            return (
                              <div key={k} className={processedResult?.color}>
                                {processedResult?.msg}
                              </div>
                            );
                          })} */}
                          </div>
                          <div className="flex-1" />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </motion.div>
            </AnimatePresence>
          )}

          {!isPreview && userStats && (
            <div className="space-y-2 mt-4 px-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Status:</span>
                <Badge variant={userHasPassed ? 'default' : 'secondary'}>
                  {userHasPassed ? 'Completed' : 'In Progress'}
                </Badge>
              </div>
              {userAttempts.length > 0 && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Attempts:</span>{' '}
                  {userAttempts.length}
                </div>
              )}
              {lastAttempt && (
                <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                  <div className="font-medium">Last Result:</div>
                  <div className="whitespace-pre-wrap">
                    {lastAttempt.results}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {overlaySessionId && (
          <CallBlitzPanel
            open={showCallBlitzPanel}
            onOpenChange={setShowCallBlitzPanel}
            sessionId={overlaySessionId}
            sessionName={t.sessionName}
          />
        )}
      </>
    );
  }
  return null;
}
