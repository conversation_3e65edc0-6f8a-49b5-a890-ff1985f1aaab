import LearningModule, {
  AssigneeStats,
  SubModule,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import LearningModuleCard from '../../../Grid/Card';
import useUserSession from '@/hooks/useUserSession';
import { ProgressCircle } from '@tremor/react';
import { Separator } from '@/components/ui/separator';
import { ChevronRight, Logs } from 'lucide-react';

interface IProps {
  lm: LearningModule;
  selectSubmodule: (sm: SubModule | undefined) => void;
  openSubModuleId?: string;
  openOverallProgress: (sm: boolean) => void;
  isOverallProgressOpen: boolean;
}

export default function ModuleDetails({
  lm,
  openSubModuleId,
  selectSubmodule,
  openOverallProgress,
  isOverallProgressOpen,
}: IProps) {
  // console.log(lm);

  const { isAdmin, dbUser } = useUserSession();

  const lockProgress = lm.lockProgress;

  let freezeNextModule = false;

  return (
    <div className="min-w-[400px] max-w-[500px]">
      <LearningModuleCard module={lm} blockNavigation={true} />

      {isAdmin && (
        <>
          <div
            className={cn(
              'border bg-white p-3 cursor-pointer rounded-lg mb-3 mt-3',
              isOverallProgressOpen ? 'border-black' : '',
            )}
            onClick={() => {
              openOverallProgress(true);
              selectSubmodule(undefined);
            }}
          >
            <div className={cn('flex items-center')}>
              <div className="text-sm flex items-center">
                <Logs size={16} className="mr-2" />
                Overall progress and stats
              </div>
              <div className="flex-1" />
              <div className="text-xs px-2 text-muted-foreground">
                <ChevronRight size={18} />
              </div>
            </div>
          </div>
          <Separator />
        </>
      )}

      <div className="mt-3">
        {lm.subModules.map((sm) => {
          let isCompleted = sm.progress == 100;
          const isFrozen = freezeNextModule;

          if (!isAdmin) {
            if (dbUser) {
              for (const as of sm.assigneesStats) {
                if (as.userId == dbUser.id) {
                  if (as.numberOfTasksPassed == sm.tasks.length) {
                    isCompleted = true;
                  }
                  break;
                }
              }
            } else {
              isCompleted = false;
            }
            if (lockProgress && !isCompleted) {
              freezeNextModule = true;
            }
          }

          return (
            <div
              key={sm.id}
              className={cn(
                'border bg-white p-3 cursor-pointer rounded-lg mb-3',
                openSubModuleId == sm.id ? 'border-black' : '',
                isFrozen && 'bg-gray-100',
              )}
              onClick={() => {
                if (!isFrozen) {
                  selectSubmodule(sm);
                  openOverallProgress(false);
                }
              }}
            >
              <div className={cn('flex items-center')}>
                <div className="text-sm">{sm.name}</div>
                <div className="flex-1" />
                {!isAdmin && (
                  <div
                    className={cn(
                      'text-xs border rounded-full text-muted-foreground px-2 py-1',
                      {
                        'bg-blue-100 text-blue-500 border-blue-200':
                          !isFrozen && isCompleted,
                      },
                    )}
                  >
                    {isFrozen && !isCompleted && 'Locked'}
                    {!isFrozen && isCompleted && 'Completed'}
                    {!isFrozen && !isCompleted && 'In progress'}
                  </div>
                )}
                {isAdmin && (
                  <div className="text-xs px-2 text-muted-foreground">
                    <ProgressCircle
                      color={'blue'}
                      value={sm.progress}
                      size="xs"
                    ></ProgressCircle>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
