import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, FilterX, UserRoundIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import { RoleEnum } from '@/lib/User/types';

interface IProps {
  selectedRoles: RoleEnum[];
  onRoleSelect: (roles: RoleEnum[]) => void;
}

const RoleDisplayLabel: Record<RoleEnum, string | null> = {
  [RoleEnum.OWNER]: null,
  [RoleEnum.ADMIN]: 'Admin',
  [RoleEnum.OBSERVER]: 'Observer',
  [RoleEnum.TEMP]: null,
  [RoleEnum.CUSTOM]: null,
  [RoleEnum.MEMBER]: 'Member',
  [RoleEnum.MEMBER_PLUS]: null,
};

function RoleFilter({ onRoleSelect, selectedRoles }: IProps) {
  const roles = Object.values(RoleEnum)
    .filter((role) => !!RoleDisplayLabel[role])
    .map((role) => ({
      label: RoleDisplayLabel[role]!,
      value: role,
    }));

  const [open, setOpen] = useState(false);

  const clearAll = () => {
    if (onRoleSelect) {
      onRoleSelect([]);
    }
    setOpen(false);
  };

  const toggleRole = (role: RoleEnum) => {
    const updatedRoles = selectedRoles.includes(role)
      ? selectedRoles.filter((s) => s !== role)
      : [...selectedRoles, role];
    onRoleSelect(updatedRoles);
  };

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
    >
      <PopoverTrigger asChild className="w-full">
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border w-full"
          style={{ justifyContent: 'start' }}
        >
          <UserRoundIcon className="mr-2 h-4 w-4" />
          Roles
          {selectedRoles.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {selectedRoles.length} selected
                </Badge>
              </div>
            </>
          )}
          <div className="flex-1"></div>
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="p-0 max-h-[50vh] overflow-y-auto"
      >
        {' '}
        <Command>
          <CommandList>
            <CommandGroup heading="Roles">
              {roles?.map((role) => (
                <CommandItem
                  key={role.value}
                  value={String(role.value)}
                  onSelect={() => toggleRole(role.value)}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      selectedRoles?.some((s) => s === role.value)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex space-x-2 items-center">
                    <div className="capitalize">{role.label}</div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
          {selectedRoles.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export default React.memo(RoleFilter);
