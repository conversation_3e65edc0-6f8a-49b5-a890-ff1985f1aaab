/* eslint-disable @typescript-eslint/no-explicit-any */
import { FilterType } from '@/common/Calls/AIRoleplay/List/common';
import useUserSession from '@/hooks/useUserSession';
import useLearningModuleFilters from './useLearningModuleFilters';
import LearningModule from '@/lib/LearningModule/types';
import TeamsFilter from './TeamsFilter';
import StatusFilter from './StatusFilter';
import RoleFilter from './RoleFilter';
import { UserStatus, RoleEnum } from '@/lib/User/types';
import useRouting from '@/hooks/useRouting';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetDescription,
} from '@/components/ui/sheet';

interface IProps {
  learningModule: LearningModule;
  filtersState: Record<FilterType, any>;
  onFiltersUpdated: (filtersState: Record<FilterType, any>) => void;
  open: boolean;
  onFiltersPanelClose: () => void;
}

export default function LearningModuleFilters({
  learningModule,
  filtersState,
  onFiltersUpdated,
  open,
  onFiltersPanelClose,
}: IProps) {
  const { isAdmin } = useUserSession();
  const { data: filters } = useLearningModuleFilters(learningModule.id);
  const { setUrlParameter, deleteUrlParameter } = useRouting();

  const updateTeamsFilter = (teams: number[]) => {
    filtersState[FilterType.TEAMS] = teams;

    if (teams && teams.length > 0) {
      setUrlParameter('teams', teams.join(','));
    } else {
      deleteUrlParameter('teams');
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateStatusFilter = (statuses: UserStatus[]) => {
    filtersState[FilterType.STATUS] = statuses;

    if (statuses && statuses.length > 0) {
      setUrlParameter('status', statuses.join(','));
    } else {
      deleteUrlParameter('status');
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  const updateRolesFilter = (roles: RoleEnum[]) => {
    filtersState[FilterType.ROLES] = roles;

    if (roles && roles.length > 0) {
      setUrlParameter('roles', roles.join(','));
    } else {
      deleteUrlParameter('roles');
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(filtersState);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onFiltersPanelClose}>
      <SheetContent>
        <SheetHeader>Filters</SheetHeader>
        <SheetDescription></SheetDescription>

        <div>
          {isAdmin && (
            <>
              <div className="mt-8">
                <div className="text-xs font-semibold">Teams:</div>
                <TeamsFilter
                  teams={filters?.teams || []}
                  current={filtersState[FilterType.TEAMS]}
                  onFiltersUpdated={updateTeamsFilter}
                />
              </div>

              <div className="mt-8">
                <div className="text-xs font-semibold">Roles:</div>
                <RoleFilter
                  selectedRoles={filtersState[FilterType.ROLES] || []}
                  onRoleSelect={updateRolesFilter}
                />
              </div>

              <div className="mt-8">
                <div className="text-xs font-semibold">Status:</div>
                <StatusFilter
                  selectedStatuses={filtersState[FilterType.STATUS] || []}
                  onStatusSelect={updateStatusFilter}
                />
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
