import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, FilterX, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import { UserStatus } from '@/lib/User/types';

interface IStatusFilterProps {
  selectedStatuses: UserStatus[];
  onStatusSelect: (statuses: UserStatus[]) => void;
}

const StatusDisplayLabel: Record<UserStatus, string | null> = {
  [UserStatus.ACTIVE]: 'Active',
  [UserStatus.DEPROVISIONED]: 'Inactive',
  [UserStatus.INVITED]: null,
};

function StatusFilter({
  onStatusSelect,
  selectedStatuses,
}: IStatusFilterProps) {
  const statuses = Object.values(UserStatus)
    .filter((r) => !!StatusDisplayLabel[r])
    .map((r) => ({
      label: StatusDisplayLabel[r],
      value: r,
    }));

  const [open, setOpen] = useState(false);

  const clearAll = () => {
    if (onStatusSelect) {
      onStatusSelect([]);
    }
    setOpen(false);
  };

  const toggleStatus = (status: UserStatus) => {
    const updatedStatuses = selectedStatuses.includes(status)
      ? selectedStatuses.filter((s) => s !== status)
      : [...selectedStatuses, status];
    onStatusSelect(updatedStatuses);
  };

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
    >
      <PopoverTrigger asChild className="w-full">
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border w-full"
          style={{ justifyContent: 'start' }}
        >
          <Lock className="mr-2 h-4 w-4" />
          Status
          {selectedStatuses.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {selectedStatuses.length} selected
                </Badge>
              </div>
            </>
          )}
          <div className="flex-1"></div>
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="p-0 max-h-[50vh] overflow-y-auto"
      >
        {' '}
        <Command>
          <CommandList>
            <CommandGroup heading="Status">
              {statuses?.map((status) => (
                <CommandItem
                  key={status.value}
                  value={String(status.value)}
                  onSelect={() => toggleStatus(status.value)}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      selectedStatuses?.some((s) => s === status.value)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex space-x-2 items-center">
                    <div className="capitalize">{status.label}</div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
          {selectedStatuses.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export default React.memo(StatusFilter);
