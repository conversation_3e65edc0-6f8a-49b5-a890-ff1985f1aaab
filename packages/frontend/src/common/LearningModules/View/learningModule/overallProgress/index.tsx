/* eslint-disable @typescript-eslint/no-explicit-any */
import LearningModule, { ReportPerUser } from '@/lib/LearningModule/types';
import { TeamRoleEnum, UserDto } from '@/lib/User/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ProgressCircle } from '@tremor/react';
import { useLearningModuleStats } from '@/hooks/useLearningModules';
import {
  ChevronDown,
  ChevronUp,
  FilterIcon,
  Loader2Icon,
  Plus,
  Shield,
} from 'lucide-react';
import { AnimatePresence } from 'framer-motion';
import Table, {
  TableCell,
  TableCellHead,
  TableContent,
  TableFooter,
  TablePaginationFooter,
  TableRow,
} from '@/components/ui/Hyperbound/table';
import { useEffect, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import LearningModuleFilters from './components/Filters';
import { FilterType } from '@/common/Calls/AIRoleplay/List/common';
import { Badge } from '@/components/ui/badge';

interface IProps {
  lm: LearningModule;
}

const sortStats = (stats: ReportPerUser[]) => {
  return stats.sort((a, b) => {
    const tasksDiff = b.passedTasks - a.passedTasks;
    if (tasksDiff !== 0) return tasksDiff;

    const aFullName = `${a.user.firstName} ${a.user.lastName}`.toLowerCase();
    const bFullName = `${b.user.firstName} ${b.user.lastName}`.toLowerCase();
    return aFullName.localeCompare(bFullName);
  });
};

export default function OverallProgress({ lm }: IProps) {
  const [filtersState, setFiltersState] = useState<Record<FilterType, any>>(
    {} as Record<FilterType, any>,
  );
  const [stats, setStats] = useState<ReportPerUser[]>([]);
  const [paginationState, setPaginationState] = useState<
    Record<
      string,
      {
        from: number;
        numberOfResults: number;
      }
    >
  >({
    completed: {
      from: 0,
      numberOfResults: 10,
    },
    inProgress: {
      from: 0,
      numberOfResults: 10,
    },
    notStarted: {
      from: 0,
      numberOfResults: 10,
    },
  });

  const [showFilters, setShowFilters] = useState<boolean>(false);

  const teamsPerAssignee: {
    [userId: number]: { role: TeamRoleEnum; name: string; id: number }[];
  } = useMemo(() => {
    const teamsPerAssignee: {
      [userId: number]: { role: TeamRoleEnum; name: string; id: number }[];
    } = {};
    for (const a of lm.assignees) {
      teamsPerAssignee[a.id] = a.teams.map((t: any) => ({
        role: t.role,
        name: t.team.name,
        id: t.team.id,
      }));
    }
    return teamsPerAssignee;
  }, [lm.assignees]);

  const filteredAssignees = useMemo(
    () =>
      lm.assignees.filter(
        (a) =>
          !filtersState[FilterType.TEAMS]?.length ||
          filtersState[FilterType.TEAMS]?.some((t: number) =>
            a.teams.some((t2: any) => t2.team.id === t),
          ),
      ),
    [filtersState[FilterType.TEAMS], lm.assignees],
  );

  const { data: dbStats, isLoading: isLoadingStats } = useLearningModuleStats(
    lm.id,
    true,
    filtersState,
  );

  useEffect(() => {
    if (!isLoadingStats && dbStats) {
      setStats(sortStats(dbStats));
    }
  }, [isLoadingStats]);

  const totalNumberOfTasks = useMemo(() => {
    return lm.subModules.reduce((acc, sb) => acc + sb.tasks.length, 0);
  }, [lm.subModules]);

  const completedUsers = useMemo(() => {
    return stats.filter(
      (stat) =>
        stat.passedTasks === totalNumberOfTasks &&
        filteredAssignees.some((a) => a.id === stat.user.id),
    );
  }, [stats, totalNumberOfTasks, filteredAssignees]);

  const inProgressUsers = useMemo(() => {
    return stats.filter(
      (stat) =>
        stat.passedTasks > 0 &&
        stat.passedTasks < totalNumberOfTasks &&
        filteredAssignees.some((a) => a.id === stat.user.id),
    );
  }, [stats, totalNumberOfTasks, filteredAssignees]);

  const notStartedUsers = useMemo(() => {
    const startedUserIds = new Set(stats.map((stat) => stat.user.id));
    return filteredAssignees.filter(
      (assignee) => !startedUserIds.has(assignee.id),
    );
  }, [stats, filteredAssignees]);

  const passedPerAssignee: { [userId: number]: number } = {};
  let totalCompleted = 0;
  let overallNumberOfTasks = 0; //=totalNumberOfTasks * # of assignees
  const numberOfTasksPerSubmodule: { [smId: string]: number } = {};
  for (const sb of lm.subModules) {
    numberOfTasksPerSubmodule[sb.id] = sb.tasks.length;
    for (const s of sb.assigneesStats) {
      if (!filteredAssignees.some((a) => a.id === s.userId)) {
        continue;
      }

      if (!passedPerAssignee[s.userId]) {
        passedPerAssignee[s.userId] = 0;
      }
      passedPerAssignee[s.userId] += s.numberOfTasksPassed;
      totalCompleted += s.numberOfTasksPassed;
    }
  }

  overallNumberOfTasks = totalNumberOfTasks * filteredAssignees.length;

  const ranks = useMemo(() => {
    const ranks: { user: UserDto; passed: number; avgScore: number }[] = [];

    for (const a of filteredAssignees) {
      const passed = passedPerAssignee[a.id] || 0;
      const userStat = stats.find((s) => s.user.id === a.id);
      const avgScore = userStat?.avgScore || 0;
      ranks.push({ user: a, passed, avgScore });
    }

    return ranks.sort((a, b) => {
      if (a.passed !== b.passed) {
        return b.passed - a.passed;
      }

      return b.avgScore - a.avgScore;
    });
  }, [filteredAssignees, passedPerAssignee, stats]);

  const { bestRep, worstRep } = useMemo(() => {
    return {
      bestRep: ranks[0],
      worstRep: ranks[ranks.length - 1],
    };
  }, [ranks, filteredAssignees]);

  /*******************************/
  /********** ACTIONS ************/
  /*******************************/

  const [sortingState, setSortingState] = useState<{ [key: string]: number }>({
    attempts: 0,
    avgScore: 0,
    completedTasks: 1,
  });

  const orderBy = (key: string) => {
    if (sortingState[key] == 0) {
      sortingState[key] = 2;
    }

    if (key == 'attempts') {
      sortingState.avgScore = 0;
      sortingState.completedTasks = 0;
    } else if (key == 'avgScore') {
      sortingState.attempts = 0;
      sortingState.completedTasks = 0;
    } else if (key == 'completedTasks') {
      sortingState.attempts = 0;
      sortingState.avgScore = 0;
    }

    if (sortingState[key] === 2) {
      sortingState[key] = 1;
    } else {
      sortingState[key] = 2;
    }

    const tmp = stats.sort((a, b) => {
      if (key == 'attempts') {
        return sortingState[key] === 2
          ? a.numberOfAttempts - b.numberOfAttempts
          : b.numberOfAttempts - a.numberOfAttempts;
      } else if (key == 'avgScore') {
        return sortingState[key] === 2
          ? a.avgScore - b.avgScore
          : b.avgScore - a.avgScore;
      } else if (key == 'completedTasks') {
        return sortingState[key] === 2
          ? a.passedTasks - b.passedTasks
          : b.passedTasks - a.passedTasks;
      }
      return 0;
    });

    setStats(sortStats([...tmp]));

    setSortingState({ ...sortingState });
  };

  const toggleFiltersPanel = () => {
    setShowFilters((o) => !o);
  };

  const createMailtoLink = (users: (UserDto | ReportPerUser)[]) => {
    const emails = users
      .map((user) => {
        const userId = 'user' in user ? user.user.id : user.id;
        const assignee = lm.assignees.find((a) => a.id === userId);
        return assignee?.email;
      })
      .filter(Boolean);
    const subject = encodeURIComponent(`Reminder: ${lm.name} Learning Module`);
    const body = encodeURIComponent(
      `Hi,\n\nThis is a reminder about the ${lm.name} learning module.\n\nBest regards,`,
    );
    return `mailto:${emails.join(',')}?subject=${subject}&body=${body}`;
  };

  /*******************************/
  /********** RENDER *************/
  /*******************************/

  return (
    <div className="w-full border bg-white rounded-lg p-3">
      <div className="flex flex-rowitems-center justify-between">
        <div className="font-semibold mb-6">Learning module progress</div>
        <Button variant="outline" onClick={toggleFiltersPanel}>
          <FilterIcon className="mr-2 h-4 w-4" />
          Filters
        </Button>
      </div>
      <div className="my-2 border rounded-lg flex items-stretch justify-stretch">
        <div className="flex-1 border-r p-2">
          <div className="flex items-center">
            <div className="flex-1">
              {totalCompleted}/{overallNumberOfTasks} completed
            </div>
            <div>
              <ProgressCircle
                color={'blue'}
                value={Math.trunc(
                  (totalCompleted / overallNumberOfTasks) * 100,
                )}
                size="xs"
              ></ProgressCircle>
            </div>
          </div>

          <div className="text-muted-foreground text-xs mt-6">
            Overall progress
          </div>
        </div>
        <div className="flex-1 border-r p-2">
          {bestRep && (
            <div className="flex items-center">
              <div className="flex items-center flex-1">
                <Avatar className="w-[24px] h-[24px] border-white border-2">
                  <AvatarImage src={bestRep.user.avatar} />
                  <AvatarFallback className="text-sm">
                    {bestRep.user.firstName?.charAt(0) || ''}
                    {bestRep.user.lastName?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-2">
                  {bestRep.user.firstName} {bestRep.user.lastName}
                </div>
              </div>
              <div>
                <ProgressCircle
                  color={'blue'}
                  value={Math.trunc(
                    (bestRep.passed / totalNumberOfTasks) * 100,
                  )}
                  size="xs"
                ></ProgressCircle>
              </div>
            </div>
          )}

          <div className="text-muted-foreground text-xs mt-6">
            Best performing rep
          </div>
        </div>
        <div className="flex-1 p-2">
          {worstRep && (
            <div className="flex items-center">
              <div className="flex items-center flex-1">
                <Avatar className="w-[24px] h-[24px] border-white border-2">
                  <AvatarImage src={worstRep.user.avatar} />
                  <AvatarFallback className="text-sm">
                    {worstRep.user.firstName?.charAt(0) || ''}
                    {worstRep.user.lastName?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-2">
                  {worstRep.user.firstName} {worstRep.user.lastName}
                </div>
              </div>
              <div>
                <ProgressCircle
                  color={'blue'}
                  value={Math.trunc(
                    (worstRep.passed / totalNumberOfTasks) * 100,
                  )}
                  size="xs"
                ></ProgressCircle>
              </div>
            </div>
          )}

          <div className="text-muted-foreground text-xs mt-6">
            Least performing rep
          </div>
        </div>
      </div>

      <div className="mt-4">
        {isLoadingStats && (
          <div className="flex justify-center m-20 text-muted-foreground text-sm">
            <Loader2Icon size={20} className="animate-spin mr-2" />
            Loading...
          </div>
        )}
        <AnimatePresence>
          {!isLoadingStats && (
            <>
              {completedUsers.length > 0 && (
                <div className="mb-8 mt-8">
                  <div className="flex items-center justify-between mb-4">
                    <div className="font-semibold">Completed</div>
                  </div>
                  <Table fitToContent>
                    <TableContent>
                      <TableRow>
                        <TableCellHead>&nbsp;</TableCellHead>
                        <TableCellHead className="w-full">&nbsp;</TableCellHead>
                        <TableCellHead
                          onClick={() => {
                            orderBy('attempts');
                          }}
                          className="cursor-pointer hover:underline"
                        >
                          <div className="flex items-center">
                            <div>Numb. of Attempts</div>
                            {sortingState.attempts > 0 ? (
                              <div>
                                {sortingState.attempts === 2 ? (
                                  <ChevronUp size={16} className="ml-2" />
                                ) : (
                                  <ChevronDown size={16} className="ml-2" />
                                )}
                              </div>
                            ) : (
                              <div className="w-[20px]" />
                            )}
                          </div>
                        </TableCellHead>
                        <TableCellHead
                          onClick={() => {
                            orderBy('avgScore');
                          }}
                          className="cursor-pointer hover:underline"
                        >
                          <div className="flex items-center">
                            <div>Avg Score</div>
                            {sortingState.avgScore > 0 ? (
                              <div>
                                {sortingState.avgScore === 2 ? (
                                  <ChevronUp size={16} className="ml-2" />
                                ) : (
                                  <ChevronDown size={16} className="ml-2" />
                                )}
                              </div>
                            ) : (
                              <div className="w-[20px]" />
                            )}
                          </div>
                        </TableCellHead>
                        <TableCellHead
                          onClick={() => {
                            orderBy('completedTasks');
                          }}
                          className="cursor-pointer hover:underline"
                        >
                          <div className="flex items-center">
                            <div>Completed Tasks</div>
                            {sortingState.completedTasks > 0 ? (
                              <div>
                                {sortingState.completedTasks === 2 ? (
                                  <ChevronUp size={16} className="ml-2" />
                                ) : (
                                  <ChevronDown size={16} className="ml-2" />
                                )}
                              </div>
                            ) : (
                              <div className="w-[20px]" />
                            )}
                          </div>
                        </TableCellHead>
                        <TableCellHead>Start Date</TableCellHead>
                        <TableCellHead>Teams</TableCellHead>
                        <TableCellHead>&nbsp;</TableCellHead>
                      </TableRow>
                      {completedUsers
                        .slice(
                          paginationState.completed.from,
                          paginationState.completed.from +
                            paginationState.completed.numberOfResults,
                        )
                        .map((r: ReportPerUser) => (
                          <TableRow key={r.user.id}>
                            <TableCell>
                              <div className="flex items-center">
                                <Avatar className="w-[24px] h-[24px] border-white border-2">
                                  <AvatarImage src={r.user.avatar} />
                                  <AvatarFallback className="text-sm">
                                    {r.user.firstName?.charAt(0) || ''}
                                    {r.user.lastName?.charAt(0) || ''}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="ml-2">
                                  {r.user.firstName} {r.user.lastName}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="w-full">&nbsp;</TableCell>
                            <TableCell>{r.numberOfAttempts}</TableCell>
                            <TableCell>{r.avgScore}</TableCell>
                            <TableCell>
                              {r.passedTasks}/{totalNumberOfTasks}
                            </TableCell>
                            <TableCell>
                              {r.firstAttemptDate
                                ? new Date(
                                    r.firstAttemptDate,
                                  ).toLocaleDateString()
                                : '-'}
                            </TableCell>
                            <TableCell>
                              {teamsPerAssignee[r.user.id].length == 0 ? (
                                <span className="text-[#71717A] text-sm">
                                  -
                                </span>
                              ) : (
                                <div className="flex items-center flex-wrap">
                                  {teamsPerAssignee[r.user.id].map((team) => {
                                    let icon = undefined;
                                    if (team.role == TeamRoleEnum.ADMIN) {
                                      icon = (
                                        <Shield
                                          size={16}
                                          className="text-muted-foreground ml-1"
                                        />
                                      );
                                    }
                                    return (
                                      <Badge
                                        key={team.id}
                                        variant={'secondary'}
                                        className="flex items-center mr-1 mb-1"
                                      >
                                        {team.name}
                                        {icon}
                                      </Badge>
                                    );
                                  })}
                                </div>
                              )}
                            </TableCell>
                            <TableCell>&nbsp;</TableCell>
                          </TableRow>
                        ))}
                    </TableContent>
                    <TableFooter>
                      <TablePaginationFooter
                        from={paginationState.completed.from}
                        numberOfResults={
                          paginationState.completed.numberOfResults
                        }
                        totNumberOfRows={completedUsers.length}
                        updatePagination={(
                          from: number,
                          numberOfResults: number,
                        ) => {
                          setPaginationState({
                            ...paginationState,
                            completed: { from, numberOfResults },
                          });
                        }}
                      />
                    </TableFooter>
                  </Table>
                </div>
              )}

              {inProgressUsers.length > 0 && (
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <div className="font-semibold">In progress</div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        (window.location.href =
                          createMailtoLink(inProgressUsers))
                      }
                    >
                      Remind All
                    </Button>
                  </div>
                  <Table fitToContent>
                    <TableContent>
                      <TableRow>
                        <TableCellHead>&nbsp;</TableCellHead>
                        <TableCellHead className="w-full">&nbsp;</TableCellHead>
                        <TableCellHead
                          onClick={() => {
                            orderBy('attempts');
                          }}
                          className="cursor-pointer hover:underline"
                        >
                          <div className="flex items-center">
                            <div>Numb. of Attempts</div>
                            {sortingState.attempts > 0 ? (
                              <div>
                                {sortingState.attempts === 2 ? (
                                  <ChevronUp size={16} className="ml-2" />
                                ) : (
                                  <ChevronDown size={16} className="ml-2" />
                                )}
                              </div>
                            ) : (
                              <div className="w-[20px]" />
                            )}
                          </div>
                        </TableCellHead>
                        <TableCellHead
                          onClick={() => {
                            orderBy('avgScore');
                          }}
                          className="cursor-pointer hover:underline"
                        >
                          <div className="flex items-center">
                            <div>Avg Score</div>
                            {sortingState.avgScore > 0 ? (
                              <div>
                                {sortingState.avgScore === 2 ? (
                                  <ChevronUp size={16} className="ml-2" />
                                ) : (
                                  <ChevronDown size={16} className="ml-2" />
                                )}
                              </div>
                            ) : (
                              <div className="w-[20px]" />
                            )}
                          </div>
                        </TableCellHead>
                        <TableCellHead
                          onClick={() => {
                            orderBy('completedTasks');
                          }}
                          className="cursor-pointer hover:underline"
                        >
                          <div className="flex items-center">
                            <div>Completed Tasks</div>
                            {sortingState.completedTasks > 0 ? (
                              <div>
                                {sortingState.completedTasks === 2 ? (
                                  <ChevronUp size={16} className="ml-2" />
                                ) : (
                                  <ChevronDown size={16} className="ml-2" />
                                )}
                              </div>
                            ) : (
                              <div className="w-[20px]" />
                            )}
                          </div>
                        </TableCellHead>
                        <TableCellHead>Start Date</TableCellHead>
                        <TableCellHead>Teams</TableCellHead>
                        <TableCellHead>&nbsp;</TableCellHead>
                      </TableRow>
                      {inProgressUsers
                        .slice(
                          paginationState.inProgress.from,
                          paginationState.inProgress.from +
                            paginationState.inProgress.numberOfResults,
                        )
                        .map((r: ReportPerUser) => (
                          <TableRow key={r.user.id}>
                            <TableCell>
                              <div className="flex items-center">
                                <Avatar className="w-[24px] h-[24px] border-white border-2">
                                  <AvatarImage src={r.user.avatar} />
                                  <AvatarFallback className="text-sm">
                                    {r.user.firstName?.charAt(0) || ''}
                                    {r.user.lastName?.charAt(0) || ''}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="ml-2">
                                  {r.user.firstName} {r.user.lastName}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="w-full">&nbsp;</TableCell>
                            <TableCell>{r.numberOfAttempts}</TableCell>
                            <TableCell>{r.avgScore}</TableCell>
                            <TableCell>
                              {r.passedTasks}/{totalNumberOfTasks}
                            </TableCell>
                            <TableCell>
                              {r.firstAttemptDate
                                ? new Date(
                                    r.firstAttemptDate,
                                  ).toLocaleDateString()
                                : '-'}
                            </TableCell>
                            <TableCell>
                              {teamsPerAssignee[r.user.id].length == 0 ? (
                                <span className="text-[#71717A] text-sm">
                                  -
                                </span>
                              ) : (
                                <div className="flex items-center flex-wrap">
                                  {teamsPerAssignee[r.user.id].map((team) => {
                                    let icon = undefined;
                                    if (team.role == TeamRoleEnum.ADMIN) {
                                      icon = (
                                        <Shield
                                          size={16}
                                          className="text-muted-foreground ml-1"
                                        />
                                      );
                                    }
                                    return (
                                      <Badge
                                        key={team.id}
                                        variant={'secondary'}
                                        className="flex items-center mr-1 mb-1"
                                      >
                                        {team.name}
                                        {icon}
                                      </Badge>
                                    );
                                  })}
                                </div>
                              )}
                            </TableCell>
                            <TableCell>&nbsp;</TableCell>
                          </TableRow>
                        ))}
                    </TableContent>
                    <TableFooter>
                      <TablePaginationFooter
                        from={paginationState.inProgress.from}
                        numberOfResults={
                          paginationState.inProgress.numberOfResults
                        }
                        totNumberOfRows={inProgressUsers.length}
                        updatePagination={(
                          from: number,
                          numberOfResults: number,
                        ) => {
                          setPaginationState({
                            ...paginationState,
                            inProgress: { from, numberOfResults },
                          });
                        }}
                      />
                    </TableFooter>
                  </Table>
                </div>
              )}

              {notStartedUsers.length > 0 && (
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <div className="font-semibold">Not started</div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        (window.location.href =
                          createMailtoLink(notStartedUsers))
                      }
                    >
                      Remind All
                    </Button>
                  </div>
                  <Table fitToContent>
                    <TableContent>
                      <TableRow>
                        <TableCellHead>&nbsp;</TableCellHead>
                        <TableCellHead className="w-full">&nbsp;</TableCellHead>
                        <TableCellHead>Status</TableCellHead>
                        <TableCellHead>Teams</TableCellHead>
                        <TableCellHead>&nbsp;</TableCellHead>
                      </TableRow>
                      {notStartedUsers
                        .slice(
                          paginationState.notStarted.from,
                          paginationState.notStarted.from +
                            paginationState.notStarted.numberOfResults,
                        )
                        .map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <div className="flex items-center">
                                <Avatar className="w-[24px] h-[24px] border-white border-2">
                                  <AvatarImage src={user.avatar} />
                                  <AvatarFallback className="text-sm">
                                    {user.firstName?.charAt(0) || ''}
                                    {user.lastName?.charAt(0) || ''}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="ml-2">
                                  {user.firstName} {user.lastName}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="w-full">&nbsp;</TableCell>
                            <TableCell>Not Started</TableCell>
                            <TableCell>
                              {teamsPerAssignee[user.id].length == 0 ? (
                                <Button
                                  variant={'ghost'}
                                  className="flex justify-center"
                                >
                                  <Plus
                                    size={'9.3px'}
                                    className="text-[#71717A] mr-1"
                                  />
                                  <span className="text-[#71717A] text-sm">
                                    Add to teams
                                  </span>
                                </Button>
                              ) : (
                                <div className="flex items-center flex-wrap">
                                  {teamsPerAssignee[user.id].map((team) => {
                                    let icon = undefined;
                                    if (team.role == TeamRoleEnum.ADMIN) {
                                      icon = (
                                        <Shield
                                          size={16}
                                          className="text-muted-foreground ml-1"
                                        />
                                      );
                                    }
                                    return (
                                      <Badge
                                        key={team.id}
                                        variant={'secondary'}
                                        className="flex items-center mr-1 mb-1"
                                      >
                                        {team.name}
                                        {icon}
                                      </Badge>
                                    );
                                  })}
                                </div>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableContent>
                    <TableFooter>
                      <TablePaginationFooter
                        from={paginationState.notStarted.from}
                        numberOfResults={
                          paginationState.notStarted.numberOfResults
                        }
                        totNumberOfRows={notStartedUsers.length}
                        updatePagination={(
                          from: number,
                          numberOfResults: number,
                        ) => {
                          setPaginationState({
                            ...paginationState,
                            notStarted: { from, numberOfResults },
                          });
                        }}
                      />
                    </TableFooter>
                  </Table>
                </div>
              )}
            </>
          )}
        </AnimatePresence>
      </div>
      <LearningModuleFilters
        learningModule={lm}
        filtersState={filtersState}
        onFiltersUpdated={setFiltersState}
        open={showFilters}
        onFiltersPanelClose={() => {
          setShowFilters(false);
        }}
      />
    </div>
  );
}
