import LearningModule, { ReportPerUser } from '@/lib/LearningModule/types';
import { UserDto } from '@/lib/User/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ProgressCircle } from '@tremor/react';
import { useLearningModuleStats } from '@/hooks/useLearningModules';
import { ChevronDown, ChevronUp, Loader2Icon } from 'lucide-react';
import { AnimatePresence } from 'framer-motion';
import Table, {
  TableCell,
  TableCellHead,
  TableContent,
  TableFooter,
  TableRow,
} from '@/components/ui/Hyperbound/table';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface IProps {
  lm: LearningModule;
}

export default function OverallProgress({ lm }: IProps) {
  const { data: dbStats, isLoading: isLoadingStats } = useLearningModuleStats(
    lm.id,
    true,
  );

  const [stats, setStats] = useState<ReportPerUser[]>([]);

  useEffect(() => {
    if (!isLoadingStats && dbStats) {
      setStats(
        dbStats.sort((a, b) => {
          return b.passedTasks - a.passedTasks;
        }),
      );
    }
  }, [isLoadingStats]);

  const passedPerAssignee: { [userId: number]: number } = {};
  let totalNumberOfTasks = 0;
  let totalCompleted = 0;
  let overallNumberOfTasks = 0; //=totalNumberOfTasks * # of assignees
  const numberOfTasksPerSubmodule: { [smId: string]: number } = {};
  for (const sb of lm.subModules) {
    numberOfTasksPerSubmodule[sb.id] = sb.tasks.length;
    totalNumberOfTasks += sb.tasks.length;
    for (const s of sb.assigneesStats) {
      if (!passedPerAssignee[s.userId]) {
        passedPerAssignee[s.userId] = 0;
      }
      passedPerAssignee[s.userId] += s.numberOfTasksPassed;
      totalCompleted += s.numberOfTasksPassed;
    }
  }

  overallNumberOfTasks = totalNumberOfTasks * lm.assignees.length;

  let ranks: { user: UserDto; passed: number }[] = [];

  for (const a of lm.assignees) {
    const passed = passedPerAssignee[a.id] || 0;
    ranks.push({ user: a, passed });
  }

  ranks = ranks.sort((a, b) => (a.passed > b.passed ? -1 : 1));

  const bestRep = ranks[0];
  const worstRep = ranks[ranks.length - 1];

  /*******************************/
  /********** ACTIONS ************/
  /*******************************/

  const [expandedUsers, setExpandedUsers] = useState<{
    [uid: number]: boolean;
  }>({});

  const toggleExpandUser = (userId: number) => {
    setExpandedUsers({
      ...expandedUsers,
      [userId]: !expandedUsers[userId],
    });
  };

  const [sortingState, setSortingState] = useState<{ [key: string]: number }>({
    attempts: 0,
    avgScore: 0,
    completedTasks: 1,
  });

  const orderBy = (key: string) => {
    if (sortingState[key] == 0) {
      sortingState[key] = 2;
    }

    if (key == 'attempts') {
      sortingState.avgScore = 0;
      sortingState.completedTasks = 0;
    } else if (key == 'avgScore') {
      sortingState.attempts = 0;
      sortingState.completedTasks = 0;
    } else if (key == 'completedTasks') {
      sortingState.attempts = 0;
      sortingState.avgScore = 0;
    }

    if (sortingState[key] === 2) {
      sortingState[key] = 1;
    } else {
      sortingState[key] = 2;
    }

    const tmp = stats.sort((a, b) => {
      if (key == 'attempts') {
        return sortingState[key] === 2
          ? a.numberOfAttempts - b.numberOfAttempts
          : b.numberOfAttempts - a.numberOfAttempts;
      } else if (key == 'avgScore') {
        return sortingState[key] === 2
          ? a.avgScore - b.avgScore
          : b.avgScore - a.avgScore;
      } else if (key == 'completedTasks') {
        return sortingState[key] === 2
          ? a.passedTasks - b.passedTasks
          : b.passedTasks - a.passedTasks;
      }
      return 0;
    });

    setStats([...tmp]);

    setSortingState({ ...sortingState });
  };

  /*******************************/
  /********** RENDER *************/
  /*******************************/

  return (
    <div className="w-full border bg-white rounded-lg p-3">
      <div className="font-semibold mb-6">Learning module progress</div>

      <div className="my-2 border rounded-lg flex items-stretch justify-stretch">
        <div className="flex-1 border-r p-2">
          <div className="flex items-center">
            <div className="flex-1">
              {totalCompleted}/{overallNumberOfTasks} completed
            </div>
            <div>
              <ProgressCircle
                color={'blue'}
                value={Math.trunc(
                  (totalCompleted / overallNumberOfTasks) * 100,
                )}
                size="xs"
              ></ProgressCircle>
            </div>
          </div>

          <div className="text-muted-foreground text-xs mt-6">
            Overall progress
          </div>
        </div>
        <div className="flex-1 border-r p-2">
          {bestRep && (
            <div className="flex items-center">
              <div className="flex items-center flex-1">
                <Avatar className="w-[24px] h-[24px] border-white border-2">
                  <AvatarImage src={bestRep.user.avatar} />
                  <AvatarFallback className="text-sm">
                    {bestRep.user.firstName?.charAt(0) || ''}
                    {bestRep.user.lastName?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-2">
                  {bestRep.user.firstName} {bestRep.user.lastName}
                </div>
              </div>
              <div>
                <ProgressCircle
                  color={'blue'}
                  value={Math.trunc(
                    (bestRep.passed / totalNumberOfTasks) * 100,
                  )}
                  size="xs"
                ></ProgressCircle>
              </div>
            </div>
          )}

          <div className="text-muted-foreground text-xs mt-6">
            Best performing rep
          </div>
        </div>
        <div className="flex-1 p-2">
          {worstRep && (
            <div className="flex items-center">
              <div className="flex items-center flex-1">
                <Avatar className="w-[24px] h-[24px] border-white border-2">
                  <AvatarImage src={worstRep.user.avatar} />
                  <AvatarFallback className="text-sm">
                    {worstRep.user.firstName?.charAt(0) || ''}
                    {worstRep.user.lastName?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-2">
                  {worstRep.user.firstName} {worstRep.user.lastName}
                </div>
              </div>
              <div>
                <ProgressCircle
                  color={'blue'}
                  value={Math.trunc(
                    (worstRep.passed / totalNumberOfTasks) * 100,
                  )}
                  size="xs"
                ></ProgressCircle>
              </div>
            </div>
          )}

          <div className="text-muted-foreground text-xs mt-6">
            Least performing rep
          </div>
        </div>
      </div>

      {/* <div className="mt-4">
        {ranks.map((r, i) => {
          return (
            <div key={r.user.id} className="flex items-center mb-1">
              <div className="flex items-center">
                <Avatar className="w-[24px] h-[24px] border-white border-2">
                  <AvatarImage src={r.user.avatar} />
                  <AvatarFallback className="text-sm">
                    {r.user.firstName?.charAt(0) || ''}
                    {r.user.lastName?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
                <div className="ml-2">
                  {r.user.firstName} {r.user.lastName}
                </div>
              </div>
              <div className="flex-1" />
              <div className="text-muted-foreground">
                {r.passed}/{totalNumberOfTasks} completed
              </div>
            </div>
          );
        })}
      </div> */}

      <div className="mt-4">
        {isLoadingStats && (
          <div className="flex justify-center m-20 text-muted-foreground text-sm">
            <Loader2Icon size={20} className="animate-spin mr-2" />
            Loading...
          </div>
        )}
        <AnimatePresence>
          {!isLoadingStats && (
            <Table hideFooter={true} fitToContent={true}>
              <TableContent>
                <TableRow>
                  <TableCellHead>&nbsp;</TableCellHead>
                  <TableCellHead className="w-full">&nbsp;</TableCellHead>
                  <TableCellHead
                    onClick={() => {
                      orderBy('attempts');
                    }}
                    className="cursor-pointer hover:underline"
                  >
                    <div className="flex items-center">
                      <div>Numb. of Attempts</div>
                      {sortingState.attempts > 0 ? (
                        <div>
                          {sortingState.attempts === 2 ? (
                            <ChevronUp size={16} className="ml-2" />
                          ) : (
                            <ChevronDown size={16} className="ml-2" />
                          )}
                        </div>
                      ) : (
                        <div className="w-[20px]" />
                      )}
                    </div>
                  </TableCellHead>
                  <TableCellHead
                    onClick={() => {
                      orderBy('avgScore');
                    }}
                    className="cursor-pointer hover:underline"
                  >
                    <div className="flex items-center">
                      <div>Avg Score</div>
                      {sortingState.avgScore > 0 ? (
                        <div>
                          {sortingState.avgScore === 2 ? (
                            <ChevronUp size={16} className="ml-2" />
                          ) : (
                            <ChevronDown size={16} className="ml-2" />
                          )}
                        </div>
                      ) : (
                        <div className="w-[20px]" />
                      )}
                    </div>
                  </TableCellHead>
                  <TableCellHead
                    onClick={() => {
                      orderBy('completedTasks');
                    }}
                    className="cursor-pointer hover:underline"
                  >
                    <div className="flex items-center">
                      <div>Completed Tasks</div>
                      {sortingState.completedTasks > 0 ? (
                        <div>
                          {sortingState.completedTasks === 2 ? (
                            <ChevronUp size={16} className="ml-2" />
                          ) : (
                            <ChevronDown size={16} className="ml-2" />
                          )}
                        </div>
                      ) : (
                        <div className="w-[20px]" />
                      )}
                    </div>
                  </TableCellHead>
                  <TableCellHead>&nbsp;</TableCellHead>
                </TableRow>
                {stats.map((r: ReportPerUser) => {
                  const expand = expandedUsers[r.user.id];

                  const cells = [
                    <TableRow
                      key={r.user.id}
                      className={cn('hover:bg-gray-50 cursor-pointer', {
                        'bg-gray-50': expand,
                      })}
                      onClick={() => {
                        toggleExpandUser(r.user.id);
                      }}
                    >
                      <TableCell>
                        <div className="flex items-center">
                          <Avatar className="w-[24px] h-[24px] border-white border-2">
                            <AvatarImage src={r.user.avatar} />
                            <AvatarFallback className="text-sm">
                              {r.user.firstName?.charAt(0) || ''}
                              {r.user.lastName?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <div className="ml-2">
                            {r.user.firstName} {r.user.lastName}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="w-full">&nbsp;</TableCell>
                      <TableCell>{r.numberOfAttempts}</TableCell>
                      <TableCell>{r.avgScore}</TableCell>
                      <TableCell>
                        {r.passedTasks}/{totalNumberOfTasks}
                      </TableCell>
                      <TableCell>
                        {expand ? (
                          <ChevronUp
                            size={16}
                            className="text-muted-foreground"
                          />
                        ) : (
                          <ChevronDown
                            size={16}
                            className="text-muted-foreground"
                          />
                        )}
                      </TableCell>
                    </TableRow>,
                  ];

                  if (expand && r.submodules) {
                    for (const sm of r.submodules) {
                      if (sm) {
                        cells.push(
                          <TableRow
                            key={r.user.id}
                            onClick={() => {
                              toggleExpandUser(r.user.id);
                            }}
                          >
                            <TableCell>
                              <div className="ml-[32px]">{sm.name}</div>
                            </TableCell>
                            <TableCell className="w-full">&nbsp;</TableCell>

                            <TableCell>{sm.numberOfAttempts}</TableCell>
                            <TableCell>{sm.avgScore}</TableCell>
                            <TableCell>
                              {sm.passedTasks}/
                              {numberOfTasksPerSubmodule[sm.id]}
                            </TableCell>
                            <TableCell>&nbsp;</TableCell>
                          </TableRow>,
                        );
                      }
                    }
                  }
                  return cells;
                })}
              </TableContent>
              <TableFooter className="">&nbsp;</TableFooter>
            </Table>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
