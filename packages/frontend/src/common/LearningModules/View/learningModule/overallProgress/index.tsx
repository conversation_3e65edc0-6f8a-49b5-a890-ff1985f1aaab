/* eslint-disable @typescript-eslint/no-explicit-any */
import LearningModule, { ReportPerUser } from '@/lib/LearningModule/types';
import { RoleEnum, TeamRoleEnum, UserDto, UserStatus } from '@/lib/User/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ProgressCircle } from '@tremor/react';
import { useLearningModuleStats } from '@/hooks/useLearningModules';
import {
  ChevronDown,
  ChevronUp,
  DownloadIcon,
  FilterIcon,
  Loader2Icon,
  MailIcon,
  Shield,
} from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import Table, {
  TableCell,
  TableCellHead,
  TableContent,
  TableFooter,
  TablePaginationFooter,
  TableRow,
} from '@/components/ui/Hyperbound/table';
import { useEffect, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import LearningModuleFilters from './components/Filters';
import { FilterType } from '@/common/Calls/AIRoleplay/List/common';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsTrigger, TabsList } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import SearchBox from '@/components/ui/Hyperbound/search-box';
import Papa from 'papaparse';
import useRouting from '@/hooks/useRouting';

interface IProps {
  lm: LearningModule;
}

const sortStats = (stats: ReportPerUser[]) => {
  return stats.sort((a, b) => {
    const tasksDiff = b.passedTasks - a.passedTasks;
    if (tasksDiff !== 0) return tasksDiff;

    const aFullName = `${a.user.firstName} ${a.user.lastName}`.toLowerCase();
    const bFullName = `${b.user.firstName} ${b.user.lastName}`.toLowerCase();
    return aFullName.localeCompare(bFullName);
  });
};

export default function OverallProgress({ lm }: IProps) {
  const { getUrlParameter } = useRouting();
  const [filtersState, setFiltersState] = useState<Record<FilterType, any>>(
    {} as Record<FilterType, any>,
  );
  const [stats, setStats] = useState<ReportPerUser[]>([]);
  const [paginationState, setPaginationState] = useState<
    Record<
      string,
      {
        from: number;
        numberOfResults: number;
      }
    >
  >({
    all: {
      from: 0,
      numberOfResults: 10,
    },
    completed: {
      from: 0,
      numberOfResults: 10,
    },
    inProgress: {
      from: 0,
      numberOfResults: 10,
    },
    notStarted: {
      from: 0,
      numberOfResults: 10,
    },
  });

  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [currentTab, setCurrentTab] = useState<string>('all');
  const [expandedUsers, setExpandedUsers] = useState<Set<number>>(new Set());
  const [search, setSearch] = useState<string>('');

  useEffect(() => {
    const teamsParam = getUrlParameter('teams');
    if (teamsParam) {
      const teams = teamsParam
        .split(',')
        .map(Number)
        .filter((id) => !isNaN(id));
      setFiltersState((prev) => ({
        ...prev,
        [FilterType.TEAMS]: teams,
      }));
    }

    const statusParam = getUrlParameter('status');
    if (statusParam) {
      const statuses = statusParam.split(',') as UserStatus[];
      setFiltersState((prev) => ({
        ...prev,
        [FilterType.STATUS]: statuses,
      }));
    }

    const rolesParam = getUrlParameter('roles');
    if (rolesParam) {
      const roles = rolesParam.split(',') as TeamRoleEnum[];
      setFiltersState((prev) => ({
        ...prev,
        [FilterType.ROLES]: roles,
      }));
    }
  }, []);

  const teamsPerAssignee: {
    [userId: number]: { role: TeamRoleEnum; name: string; id: number }[];
  } = useMemo(() => {
    const teamsPerAssignee: {
      [userId: number]: { role: TeamRoleEnum; name: string; id: number }[];
    } = {};
    for (const a of lm.assignees) {
      teamsPerAssignee[a.id] = a.teams.map((t: any) => ({
        role: t.role,
        name: t.team.name,
        id: t.team.id,
      }));
    }
    return teamsPerAssignee;
  }, [lm.assignees]);

  const filteredAssignees = useMemo(
    () =>
      lm.assignees.filter((a) => {
        const teamsFilter =
          !filtersState[FilterType.TEAMS]?.length ||
          filtersState[FilterType.TEAMS]?.some((t: number) =>
            a.teams.some((t2: any) => t2.team.id === t),
          );
        const statusFilter =
          !filtersState[FilterType.STATUS]?.length ||
          filtersState[FilterType.STATUS]?.some(
            (s: UserStatus) => a.status === s,
          );
        const rolesFilter =
          !filtersState[FilterType.ROLES]?.length ||
          filtersState[FilterType.ROLES]?.some((r: RoleEnum) => a.role === r);
        return teamsFilter && statusFilter && rolesFilter;
      }),
    [
      filtersState[FilterType.TEAMS],
      filtersState[FilterType.STATUS],
      filtersState[FilterType.ROLES],
      lm.assignees,
    ],
  );

  const { data: dbStats, isLoading: isLoadingStats } = useLearningModuleStats(
    lm.id,
    true,
    filtersState,
  );

  useEffect(() => {
    if (!isLoadingStats && dbStats) {
      setStats(sortStats(dbStats));
    }
  }, [isLoadingStats]);

  const totalNumberOfTasks = useMemo(() => {
    return lm.subModules.reduce((acc, sb) => acc + sb.tasks.length, 0);
  }, [lm.subModules]);

  const completedUsers = useMemo(() => {
    return stats.filter(
      (stat) =>
        stat.passedTasks === totalNumberOfTasks &&
        filteredAssignees.some((a) => a.id === stat.user.id),
    );
  }, [stats, totalNumberOfTasks, filteredAssignees]);

  const inProgressUsers = useMemo(() => {
    return stats.filter(
      (stat) =>
        stat.passedTasks > 0 &&
        stat.passedTasks < totalNumberOfTasks &&
        filteredAssignees.some((a) => a.id === stat.user.id),
    );
  }, [stats, totalNumberOfTasks, filteredAssignees]);

  const notStartedUsers = useMemo(() => {
    const startedUserIds = new Set([
      ...inProgressUsers.map((stat) => stat.user.id),
      ...completedUsers.map((stat) => stat.user.id),
    ]);
    return filteredAssignees
      .filter((assignee) => !startedUserIds.has(assignee.id))
      .map((user) => ({
        user: {
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
          id: user.id,
        },
        sumOfScores: 0,
        avgScore: 0,
        passedTasks: 0,
        numberOfAttempts: 0,
        submodules: [],
      }));
  }, [inProgressUsers, completedUsers, filteredAssignees]);

  const passedPerAssignee: { [userId: number]: number } = {};
  let totalCompleted = 0;
  let overallNumberOfTasks = 0; //=totalNumberOfTasks * # of assignees
  const numberOfTasksPerSubmodule: { [smId: string]: number } = {};
  for (const sb of lm.subModules) {
    numberOfTasksPerSubmodule[sb.id] = sb.tasks.length;
    for (const s of sb.assigneesStats) {
      if (!filteredAssignees.some((a) => a.id === s.userId)) {
        continue;
      }

      if (!passedPerAssignee[s.userId]) {
        passedPerAssignee[s.userId] = 0;
      }
      passedPerAssignee[s.userId] += s.numberOfTasksPassed;
      totalCompleted += s.numberOfTasksPassed;
    }
  }

  overallNumberOfTasks = totalNumberOfTasks * filteredAssignees.length;

  const ranks = useMemo(() => {
    const ranks: { user: UserDto; passed: number; avgScore: number }[] = [];

    for (const a of filteredAssignees) {
      const passed = passedPerAssignee[a.id] || 0;
      const userStat = stats.find((s) => s.user.id === a.id);
      const avgScore = userStat?.avgScore || 0;
      ranks.push({ user: a, passed, avgScore });
    }

    return ranks.sort((a, b) => {
      if (a.passed !== b.passed) {
        return b.passed - a.passed;
      }

      return b.avgScore - a.avgScore;
    });
  }, [filteredAssignees, passedPerAssignee, stats]);

  const { bestRep, worstRep } = useMemo(() => {
    return {
      bestRep: ranks[0],
      worstRep: ranks[ranks.length - 1],
    };
  }, [ranks, filteredAssignees]);

  /*******************************/
  /********** ACTIONS ************/
  /*******************************/

  const [sortingState, setSortingState] = useState<{ [key: string]: number }>({
    attempts: 0,
    avgScore: 0,
    completedTasks: 1,
  });

  const orderBy = (key: string) => {
    if (sortingState[key] == 0) {
      sortingState[key] = 2;
    }

    if (key == 'attempts') {
      sortingState.avgScore = 0;
      sortingState.completedTasks = 0;
    } else if (key == 'avgScore') {
      sortingState.attempts = 0;
      sortingState.completedTasks = 0;
    } else if (key == 'completedTasks') {
      sortingState.attempts = 0;
      sortingState.avgScore = 0;
    }

    if (sortingState[key] === 2) {
      sortingState[key] = 1;
    } else {
      sortingState[key] = 2;
    }

    const tmp = stats.sort((a, b) => {
      if (key == 'attempts') {
        return sortingState[key] === 2
          ? a.numberOfAttempts - b.numberOfAttempts
          : b.numberOfAttempts - a.numberOfAttempts;
      } else if (key == 'avgScore') {
        return sortingState[key] === 2
          ? a.avgScore - b.avgScore
          : b.avgScore - a.avgScore;
      } else if (key == 'completedTasks') {
        return sortingState[key] === 2
          ? a.passedTasks - b.passedTasks
          : b.passedTasks - a.passedTasks;
      }
      return 0;
    });

    setStats(sortStats([...tmp]));

    setSortingState({ ...sortingState });
  };

  const toggleFiltersPanel = () => {
    setShowFilters((o) => !o);
  };

  const createMailtoLink = (users: (UserDto | ReportPerUser)[]) => {
    const emails = users
      .map((user) => {
        const userId = 'user' in user ? user.user.id : user.id;
        const assignee = lm.assignees.find((a) => a.id === userId);
        return assignee?.email;
      })
      .filter(Boolean);
    const subject = encodeURIComponent(`Reminder: ${lm.name} Learning Module`);
    const body = encodeURIComponent(
      `Hi,\n\nThis is a reminder about the ${lm.name} learning module.\n\nBest regards,`,
    );
    return `mailto:${emails.join(',')}?subject=${subject}&body=${body}`;
  };

  const cleanUserReports = (userReports: ReportPerUser[]) => {
    return userReports.map((userReport) => ({
      ...userReport,
      user: {
        firstName: userReport.user.firstName,
        lastName: userReport.user.lastName,
      },
      submodules: userReport.submodules
        ?.filter((submodule) => submodule)
        .map((submodule) => ({
          name: submodule.name,
          avgScore: submodule.avgScore,
          sumOfScores: submodule.sumOfScores,
          passedTasks: submodule.passedTasks,
          tasks:
            submodule.tasks
              ?.filter((task) => task)
              .map((task) => ({
                passed: task.passed,
                numberOfAttempts: task.numberOfAttempts,
                agent: {
                  firstName: task.agent?.firstName || 'Unknown',
                  lastName: task.agent?.lastName || 'Agent',
                },
                attemptDate: task.attemptDate,
              })) || [],
        })),
    }));
  };

  const exportUserProgressToCSV = (userReports: ReportPerUser[]) => {
    const flattenedData: any[] = [];

    userReports.forEach((userReport) => {
      userReport.submodules
        ?.filter((submodule) => submodule)
        .forEach((submodule) => {
          submodule.tasks
            ?.filter((task) => task)
            .forEach((task) => {
              flattenedData.push({
                email: lm.assignees.find((a) => a.id === userReport.user.id)
                  ?.email,
                firstName: userReport.user.firstName,
                lastName: userReport.user.lastName,
                userFirstAttemptDate: userReport.firstAttemptDate,

                learningModuleName: lm.name,
                userAvgScore: userReport.avgScore,
                userSumOfScores: userReport.sumOfScores,
                userPassedTasks: userReport.passedTasks,
                totalAttempts: userReport.numberOfAttempts,
                learningModulePassed:
                  userReport.passedTasks === totalNumberOfTasks,

                submoduleName: submodule.name,
                submoduleAvgScore: submodule.avgScore,
                submoduleSumOfScores: submodule.sumOfScores,
                submodulePassedTasks: submodule.passedTasks,

                agentFirstName: task.agent?.firstName || 'Unknown',
                agentLastName: task.agent?.lastName || 'Agent',
                taskAvgScore: task.avgScore,
                taskSumOfScores: task.sumOfScores,
                taskAttemptDate: task.attemptDate,
                attempts: task.numberOfAttempts,
                taskPassed: task.passed,
              });
            });
        });
    });

    const csv = Papa.unparse(flattenedData);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `learning-module-progress-${lm.name}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const exportUserProgressToJSON = (userReports: ReportPerUser[]) => {
    const cleanedData = cleanUserReports(userReports);
    const json = JSON.stringify(cleanedData, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `learning-module-progress-${lm.name}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const onTabChange = (t: string) => {
    setCurrentTab(t);
  };

  const toggleUserAccordion = (userId: number) => {
    const newExpandedUsers = new Set(expandedUsers);
    if (newExpandedUsers.has(userId)) {
      newExpandedUsers.delete(userId);
    } else {
      newExpandedUsers.add(userId);
    }
    setExpandedUsers(newExpandedUsers);
  };

  /*******************************/
  /********** RENDER *************/
  /*******************************/

  const renderTable = () => {
    let users: ReportPerUser[] = [];
    if (currentTab === 'all') {
      users = [...completedUsers, ...inProgressUsers, ...notStartedUsers];
    } else if (currentTab === 'completed') {
      users = completedUsers;
    } else if (currentTab === 'inProgress') {
      users = inProgressUsers;
    } else if (currentTab === 'notStarted') {
      users = notStartedUsers;
    }

    users = users.filter((r) => {
      return `${r.user.firstName} ${r.user.lastName}`
        .toLowerCase()
        .includes(search.toLowerCase());
    });

    return (
      <>
        <div className="flex flex-row items-center gap-2">
          <Button
            className="mb-4"
            variant="outline"
            onClick={() => {
              const link = createMailtoLink(users);
              window.open(link, '_blank');
            }}
          >
            <MailIcon className="mr-2 h-4 w-4" />
            Remind
          </Button>
          <Button
            className="mb-4"
            variant="outline"
            onClick={() => {
              exportUserProgressToCSV(users);
            }}
          >
            <DownloadIcon className="mr-2 h-4 w-4" />
            Export to CSV
          </Button>
          <Button
            className="mb-4"
            variant="outline"
            onClick={() => {
              exportUserProgressToJSON(users);
            }}
          >
            <DownloadIcon className="mr-2 h-4 w-4" />
            Export to JSON
          </Button>
        </div>

        <Table fitToContent>
          <TableContent>
            <TableRow>
              <TableCellHead>Rep Profile</TableCellHead>
              <TableCellHead className="w-full">&nbsp;</TableCellHead>
              <TableCellHead
                onClick={() => {
                  orderBy('attempts');
                }}
                className="cursor-pointer hover:underline"
              >
                <div className="flex items-center">
                  <div># of Attempts</div>
                  {sortingState.attempts > 0 ? (
                    <div>
                      {sortingState.attempts === 2 ? (
                        <ChevronUp size={16} className="ml-2" />
                      ) : (
                        <ChevronDown size={16} className="ml-2" />
                      )}
                    </div>
                  ) : (
                    <div className="w-[20px]" />
                  )}
                </div>
              </TableCellHead>
              <TableCellHead
                onClick={() => {
                  orderBy('avgScore');
                }}
                className="cursor-pointer hover:underline"
              >
                <div className="flex items-center">
                  <div>Avg Score</div>
                  {sortingState.avgScore > 0 ? (
                    <div>
                      {sortingState.avgScore === 2 ? (
                        <ChevronUp size={16} className="ml-2" />
                      ) : (
                        <ChevronDown size={16} className="ml-2" />
                      )}
                    </div>
                  ) : (
                    <div className="w-[20px]" />
                  )}
                </div>
              </TableCellHead>
              <TableCellHead
                onClick={() => {
                  orderBy('completedTasks');
                }}
                className="cursor-pointer hover:underline"
              >
                <div className="flex items-center">
                  <div>Completed Tasks</div>
                  {sortingState.completedTasks > 0 ? (
                    <div>
                      {sortingState.completedTasks === 2 ? (
                        <ChevronUp size={16} className="ml-2" />
                      ) : (
                        <ChevronDown size={16} className="ml-2" />
                      )}
                    </div>
                  ) : (
                    <div className="w-[20px]" />
                  )}
                </div>
              </TableCellHead>
              <TableCellHead>Start Date</TableCellHead>
              <TableCellHead>Teams</TableCellHead>
              <TableCellHead>&nbsp;</TableCellHead>
            </TableRow>
            {users
              .slice(
                paginationState[currentTab].from,
                paginationState[currentTab].from +
                  paginationState[currentTab].numberOfResults,
              )
              .map((r: ReportPerUser) => {
                const isUserExpanded = expandedUsers.has(r.user.id);
                const submodules = lm.subModules.map((fullSubmodule) => {
                  const userSubmodule = r.submodules
                    ?.filter((sub) => !!sub)
                    .find((sub) => sub.id === fullSubmodule.id);
                  return {
                    id: fullSubmodule.id,
                    name: fullSubmodule.name,
                    passedTasks: userSubmodule?.passedTasks || 0,
                    tasks: fullSubmodule.tasks.map((task) => {
                      const userTask = userSubmodule?.tasks.find(
                        (t) => t.id === task.id,
                      );
                      const agent =
                        task.type === 'AI_BUYER_ROLEPLAY' && task.info?.agent
                          ? task.info.agent
                          : { firstName: 'Unknown', lastName: 'Agent' };
                      return {
                        id: task.id,
                        agent,
                        passed: userTask?.passed || false,
                      };
                    }),
                  };
                });

                return (
                  <>
                    <TableRow key={r.user.id}>
                      <TableCell>
                        <div className="flex flex-row items-center">
                          {isUserExpanded ? (
                            <ChevronUp
                              size={16}
                              className="mr-2 cursor-pointer text-muted-foreground"
                              onClick={() => toggleUserAccordion(r.user.id)}
                            />
                          ) : (
                            <ChevronDown
                              size={16}
                              className="mr-2 cursor-pointer text-muted-foreground"
                              onClick={() => toggleUserAccordion(r.user.id)}
                            />
                          )}

                          <div className="flex items-center border rounded-full p-[2px] pr-2">
                            <Avatar className="w-[24px] h-[24px] border-white border-2">
                              <AvatarImage src={r.user.avatar} />
                              <AvatarFallback className="text-sm">
                                {r.user.firstName?.charAt(0) || ''}
                                {r.user.lastName?.charAt(0) || ''}
                              </AvatarFallback>
                            </Avatar>
                            <div className="ml-2">
                              {r.user.firstName} {r.user.lastName}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="w-full">&nbsp;</TableCell>
                      <TableCell>{r.numberOfAttempts}</TableCell>
                      <TableCell>{r.avgScore}</TableCell>
                      <TableCell>
                        {r.passedTasks}/{totalNumberOfTasks}
                      </TableCell>
                      <TableCell>
                        {r.firstAttemptDate
                          ? new Date(r.firstAttemptDate).toLocaleDateString()
                          : '-'}
                      </TableCell>
                      <TableCell>
                        {teamsPerAssignee[r.user.id].length == 0 ? (
                          <span className="text-[#71717A] text-sm">-</span>
                        ) : (
                          <div className="flex items-center flex-wrap">
                            {teamsPerAssignee[r.user.id].map((team) => {
                              let icon = undefined;
                              if (team.role == TeamRoleEnum.ADMIN) {
                                icon = (
                                  <Shield
                                    size={16}
                                    className="text-muted-foreground ml-1"
                                  />
                                );
                              }
                              return (
                                <Badge
                                  key={team.id}
                                  variant={'secondary'}
                                  className="flex items-center mr-1 mb-1"
                                >
                                  {team.name}
                                  {icon}
                                </Badge>
                              );
                            })}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>&nbsp;</TableCell>
                    </TableRow>
                    <AnimatePresence>
                      {isUserExpanded && (
                        <TableRow>
                          <TableCell colSpan={10} className="p-0 border-0">
                            <motion.div
                              initial={{ height: 0 }}
                              animate={{ height: 'auto' }}
                              exit={{ height: 0 }}
                              transition={{ duration: 0.3, ease: 'easeInOut' }}
                              className="overflow-hidden"
                            >
                              <motion.div
                                className="p-2 bg-gray-100"
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                transition={{ duration: 0.2, delay: 0.1 }}
                              >
                                <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-2">
                                  {submodules.map((submodule, index) => {
                                    return (
                                      <motion.div
                                        key={submodule.id}
                                        className="rounded-lg bg-white border"
                                        initial={{ scale: 0.95, opacity: 0 }}
                                        animate={{ scale: 1, opacity: 1 }}
                                        exit={{ scale: 0.95, opacity: 0 }}
                                        transition={{
                                          duration: 0.2,
                                          delay: 0.15 + index * 0.05,
                                          ease: 'easeInOut',
                                        }}
                                      >
                                        <div className="bg-gray-100 rounded-t-lg px-4 py-2 flex flex-row items-center justify-between">
                                          <div className="text-sm font-medium text-wrap">
                                            {submodule.name}
                                          </div>
                                          <div className="flex items-center gap-2">
                                            <div className="text-sm text-muted-foreground">
                                              {submodule.passedTasks}/
                                              {
                                                numberOfTasksPerSubmodule[
                                                  submodule.id
                                                ]
                                              }
                                            </div>
                                            <ProgressCircle
                                              color={'blue'}
                                              value={Math.trunc(
                                                (submodule.passedTasks /
                                                  numberOfTasksPerSubmodule[
                                                    submodule.id
                                                  ]) *
                                                  100,
                                              )}
                                              size="xs"
                                            />
                                          </div>
                                        </div>
                                        <div className="px-4 py-2 border-t">
                                          {submodule.tasks.map((task) => {
                                            return (
                                              <div
                                                key={task.id}
                                                className="flex flex-row items-center justify-between text-sm my-2"
                                              >
                                                <div>
                                                  {task.agent.firstName}{' '}
                                                  {task.agent.lastName}
                                                </div>
                                                <div
                                                  className={cn(
                                                    task.passed
                                                      ? 'text-blue-600'
                                                      : 'text-muted-foreground',
                                                  )}
                                                >
                                                  {task.passed
                                                    ? 'Passed'
                                                    : 'In Progress'}
                                                </div>
                                              </div>
                                            );
                                          })}
                                        </div>
                                      </motion.div>
                                    );
                                  })}
                                </div>
                              </motion.div>
                            </motion.div>
                          </TableCell>
                        </TableRow>
                      )}
                    </AnimatePresence>
                  </>
                );
              })}
          </TableContent>
          <TableFooter>
            <TablePaginationFooter
              from={paginationState[currentTab].from}
              numberOfResults={paginationState[currentTab].numberOfResults}
              totNumberOfRows={users.length}
              updatePagination={(from: number, numberOfResults: number) => {
                setPaginationState({
                  ...paginationState,
                  [currentTab]: { from, numberOfResults },
                });
              }}
            />
          </TableFooter>
        </Table>
      </>
    );
  };

  return (
    <div className="w-full border bg-white rounded-lg p-3">
      <Tabs defaultValue={currentTab} value={currentTab}>
        <div className="flex flex-row items-center justify-between">
          <TabsList>
            <TabsTrigger value="all" onClick={() => onTabChange('all')}>
              All
            </TabsTrigger>
            <TabsTrigger
              value="completed"
              onClick={() => onTabChange('completed')}
            >
              Completed ({completedUsers.length}/{lm.assignees.length})
            </TabsTrigger>
            <TabsTrigger
              value="inProgress"
              onClick={() => onTabChange('inProgress')}
            >
              In Progress ({inProgressUsers.length}/{lm.assignees.length})
            </TabsTrigger>
            <TabsTrigger
              value="notStarted"
              onClick={() => onTabChange('notStarted')}
            >
              Not Started ({notStartedUsers.length}/{lm.assignees.length})
            </TabsTrigger>
          </TabsList>

          <div className="flex flex-row items-center gap-2">
            <Button variant="outline" onClick={toggleFiltersPanel}>
              <FilterIcon className="mr-2 h-4 w-4" />
              Filters
            </Button>

            <SearchBox
              placeholder="Search"
              value={search}
              onChange={setSearch}
            />
          </div>
        </div>

        <div className="mb-2 mt-4 border rounded-lg flex items-stretch justify-stretch">
          <div className="flex-1 border-r p-2">
            <div className="flex items-center">
              <div className="flex-1">
                {totalCompleted}/{overallNumberOfTasks} tasks completed
              </div>
              <div>
                <ProgressCircle
                  color={'blue'}
                  value={Math.trunc(
                    (totalCompleted / overallNumberOfTasks) * 100,
                  )}
                  size="xs"
                />
              </div>
            </div>

            <div className="text-muted-foreground text-xs mt-6">
              Overall task progress
            </div>
          </div>
          <div className="flex-1 border-r p-2">
            {bestRep && (
              <div className="flex items-center">
                <div className="flex items-center flex-1">
                  <Avatar className="w-[24px] h-[24px] border-white border-2">
                    <AvatarImage src={bestRep.user.avatar} />
                    <AvatarFallback className="text-sm">
                      {bestRep.user.firstName?.charAt(0) || ''}
                      {bestRep.user.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="ml-2">
                    {bestRep.user.firstName} {bestRep.user.lastName}
                  </div>
                </div>
                <div>
                  <ProgressCircle
                    color={'blue'}
                    value={Math.trunc(
                      (bestRep.passed / totalNumberOfTasks) * 100,
                    )}
                    size="xs"
                  ></ProgressCircle>
                </div>
              </div>
            )}

            <div className="text-muted-foreground text-xs mt-6">
              Best performing rep
            </div>
          </div>
          <div className="flex-1 p-2">
            {worstRep && (
              <div className="flex items-center">
                <div className="flex items-center flex-1">
                  <Avatar className="w-[24px] h-[24px] border-white border-2">
                    <AvatarImage src={worstRep.user.avatar} />
                    <AvatarFallback className="text-sm">
                      {worstRep.user.firstName?.charAt(0) || ''}
                      {worstRep.user.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="ml-2">
                    {worstRep.user.firstName} {worstRep.user.lastName}
                  </div>
                </div>
                <div>
                  <ProgressCircle
                    color={'blue'}
                    value={Math.trunc(
                      (worstRep.passed / totalNumberOfTasks) * 100,
                    )}
                    size="xs"
                  ></ProgressCircle>
                </div>
              </div>
            )}

            <div className="text-muted-foreground text-xs mt-6">
              Least performing rep
            </div>
          </div>
        </div>

        <div className="mt-4">
          {isLoadingStats && (
            <div className="flex justify-center m-20 text-muted-foreground text-sm">
              <Loader2Icon size={20} className="animate-spin mr-2" />
              Loading...
            </div>
          )}
          <AnimatePresence>
            {!isLoadingStats && (
              <>
                <div>{renderTable()}</div>
              </>
            )}
          </AnimatePresence>
        </div>
      </Tabs>
      <LearningModuleFilters
        learningModule={lm}
        filtersState={filtersState}
        onFiltersUpdated={setFiltersState}
        open={showFilters}
        onFiltersPanelClose={() => {
          setShowFilters(false);
        }}
      />
    </div>
  );
}
