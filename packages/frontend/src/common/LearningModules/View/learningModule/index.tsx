import PageHeader from '@/components/PageHeader';
import { Skeleton } from '@/components/ui/skeleton';
import { useLearningModule } from '@/hooks/useLearningModules';
import LinksManager from '@/lib/linksManager';
import ModuleDetails from './ModuleDetails';
import SubModuleDetails from './Submodule';
import { useEffect, useState } from 'react';
import { LearningModuleStatus, SubModule } from '@/lib/LearningModule/types';
import { AgentDto } from '@/lib/Agent/types';
import CallPanel from '../callPanel';
import { Button } from '@/components/ui/button';
import useUserSession from '@/hooks/useUserSession';
import useRouting from '@/hooks/useRouting';
import OverallProgress from './overallProgress';
import dayjs from 'dayjs';
import { TriangleAlert } from 'lucide-react';

interface IProps {
  id: string;
}

export default function ViewLearningModule({ id }: IProps) {
  const { isAdmin } = useUserSession();

  const { goToPage } = useRouting();

  const { data: lm, isLoading: isLoadingLearningModule } = useLearningModule(
    id,
    isAdmin,
  );

  const [openSubModule, setOpenSubModule] = useState<SubModule | undefined>(
    lm?.subModules[0] || undefined,
  );
  const [openOverallProgress, setOpenOverallProgress] =
    useState<boolean>(false);

  const [isOverduePast7Days, setIsOverduePast7Days] = useState<boolean>(false);

  useEffect(() => {
    if (lm) {
      // console.log(lm);
      if (lm.subModules && lm.subModules.length > 0) {
        setOpenSubModule(lm.subModules[0]);
      }
      const todayDAYJS = dayjs(new Date());
      const dueDateDAYJS = dayjs(lm.dueDate);
      if (
        todayDAYJS.diff(dueDateDAYJS, 'days') > 7 ||
        lm.status == LearningModuleStatus.COMPLETED
      ) {
        setIsOverduePast7Days(true);
      }
    }
  }, [lm]);

  /****************************************/
  /*************** ACTIONS ****************/
  /****************************************/

  const [isUserOnCall, setIsUserOnCall] = useState<boolean>(false);
  const [agentOnCall, setAgentOnCall] = useState<AgentDto>();

  const startCall = (agent: AgentDto) => {
    setAgentOnCall(agent);
    setIsUserOnCall(true);
  };

  const closeCallPanel = (o: boolean) => {
    setIsUserOnCall(o);
    setAgentOnCall(undefined);
    console.log('REFRESH');
  };

  const editModule = () => {
    goToPage(LinksManager.learningModulesEdit(id));
  };

  /****************************************/
  /*************** RENDER *****************/
  /****************************************/

  const isLoading = isLoadingLearningModule; //true; //

  if (isLoading || !lm) {
    return (
      <div className="bg-[#FBFBFB] px-6 py-4 h-[100vh] flex flex-col ">
        <div>
          <Skeleton className="w-full h-24 mt-2" />
        </div>
        <div className="flex items-stretch pt-1 flex-1">
          <div className="w-[35%] mr-6">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>

          <div className="flex-1">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#FBFBFB] px-6 py-4 min-h-[100vh]">
      <PageHeader
        title={[
          { title: 'Learning Modules', href: LinksManager.learningModules() },
          { title: lm?.name || '' },
        ]}
        rightComponent={
          <div className="flex items-center">
            {isAdmin && (
              <Button variant={'outline'} onClick={editModule}>
                Edit
              </Button>
            )}
          </div>
        }
      />

      <div className="flex items-start mt-6 space-x-3">
        <ModuleDetails
          lm={lm}
          selectSubmodule={setOpenSubModule}
          openSubModuleId={openSubModule?.id}
          openOverallProgress={setOpenOverallProgress}
          isOverallProgressOpen={openOverallProgress}
        />

        <div className="flex-1">
          {isOverduePast7Days && (
            <div className="border border-red-500 bg-red-100 text-red-500 p-4 rounded-lg mb-4">
              <div className="mb-4 flex items-center">
                <div className="mr-2">
                  <TriangleAlert size={22} />
                </div>
                <div className="font-semibold">Module Past Due Date</div>
              </div>
              <div>
                This learning module is completed or more than 1 week past its
                due date, any future calls will not be logged towards progress
                here.
              </div>
              <div>
                Please contact your admin to extend the due date of this
                learning module, or to create a new learning module with a later
                due date.
              </div>
            </div>
          )}
          {openOverallProgress && <OverallProgress lm={lm} />}

          {openSubModule && !openOverallProgress && (
            <SubModuleDetails
              sm={openSubModule}
              onStartCall={startCall}
              assignees={lm.assignees}
            />
          )}

          {/* CALL PANEL */}
          {agentOnCall && (
            <CallPanel
              key={'call-panel-' + agentOnCall?.id}
              open={isUserOnCall}
              agent={agentOnCall}
              onOpenChange={closeCallPanel}
            />
          )}
        </div>
      </div>
    </div>
  );
}
