import { Skeleton } from '@/components/ui/skeleton';
import { useLearningModule } from '@/hooks/useLearningModules';
import useUserSession from '@/hooks/useUserSession';
import ViewLearningModule from './learningModule';
import ViewCompetition from './competition';

interface IProps {
  id: string;
}

export default function ViewLearningModuleOrCompetition({ id }: IProps) {
  const { isAdmin } = useUserSession();

  const { data: lm, isLoading: isLoadingLearningModule } = useLearningModule(
    id,
    isAdmin,
  );

  /****************************************/
  /*************** RENDER *****************/
  /****************************************/

  const isLoading = isLoadingLearningModule; //true; //

  if (isLoading || !lm) {
    return (
      <div className="bg-[#FBFBFB] px-6 py-4 h-[100vh] flex flex-col ">
        <div>
          <Skeleton className="w-full h-20" />
        </div>
        <div className="flex items-stretch pt-3 flex-1">
          <div className="w-[35%] mr-6">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>

          <div className="flex-1">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>
        </div>
      </div>
    );
  }

  if (lm.isCompetition) {
    return <ViewCompetition id={id} />;
  } else {
    return <ViewLearningModule id={id} />;
  }
}
