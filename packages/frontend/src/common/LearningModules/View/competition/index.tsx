import PageHeader from '@/components/PageHeader';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  useCompetitionStats,
  useLearningModule,
} from '@/hooks/useLearningModules';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import LinksManager from '@/lib/linksManager';
import { useEffect, useRef, useState } from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import dayjs from 'dayjs';
import { AlarmClock, Phone, Search, ExternalLink } from 'lucide-react';
import Countdown from './Countdown';
import Podium from './Podium';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import Image from 'next/image';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import AgentAvatar from '@/components/Avatars/Agent';
import {
  CALL_TYPE_LABELS,
  CALL_TYPE_TO_ICON,
} from '@/common/CreateBuyerForm/constants';
import { ProgressCircle } from '@tremor/react';
import { AgentDto } from '@/lib/Agent/types';
import CallPanel from '../callPanel';

interface IProps {
  id: string;
}

export default function ViewCompetition({ id }: IProps) {
  const { isAdmin, userId } = useUserSession();

  const { goToPage } = useRouting();

  const { data: lm, isLoading: isLoadingLearningModule } = useLearningModule(
    id,
    isAdmin,
  );

  const [show, setShow] = useState<string>('leaderboard');
  // eslint-disable-next-line prefer-const
  let { data: stats, isLoading: isLoadingStats } = useCompetitionStats(id);
  // stats = DEV_STATS;

  const [leaderboard, setLeaderboard] = useState<any[]>([]);
  const [userRank, setUserRank] = useState<number>(0);
  const [userRankInfo, setUserRankInfo] = useState<any>();
  const [tasksInfo, setTasksInfo] = useState<{ [taskId: string]: any }>({});

  useEffect(() => {
    if (!isLoadingStats && stats && !isLoadingLearningModule && lm) {
      const tmp: { [taskId: string]: any } = {};
      for (const sm of lm.subModules) {
        for (const task of sm.tasks) {
          tmp[task.id] = task;
        }
      }
      setTasksInfo(tmp);

      const _lb = [];
      let i = 0;
      for (const r of stats.rankedUsers) {
        if (r.user.id === userId) {
          setUserRank(i + 1);
          setUserRankInfo(r);
        }
        i++;
      }
      if (stats.rankedUsers[0]) {
        _lb.push({
          ...stats.rankedUsers[0].user,
          score: stats.rankedUsers[0].totalScore,
        });
      }
      if (stats.rankedUsers[1]) {
        _lb.push({
          ...stats.rankedUsers[1].user,
          score: stats.rankedUsers[1].totalScore,
        });
      }
      if (stats.rankedUsers[2]) {
        _lb.push({
          ...stats.rankedUsers[2].user,
          score: stats.rankedUsers[2].totalScore,
        });
      }
      setLeaderboard(_lb);
    }
  }, [stats, isLoadingStats, isLoadingLearningModule, lm]);

  /****************************************/
  /*************** ACTIONS ****************/
  /****************************************/

  const editModule = () => {
    goToPage(LinksManager.learningModulesEdit(id));
  };

  //
  // SEARCH
  //

  const [searchString, setSearchString] = useState<string>('');

  const search = (s: string) => {
    setSearchString(s);
  };

  //
  // CALL BOT
  //

  const [agentOnCall, setAgentOnCall] = useState<AgentDto>();
  const [isUserOnCall, setIsUserOnCall] = useState<boolean>(false);

  const callBot = (agent: AgentDto) => {
    setAgentOnCall(agent);
    setIsUserOnCall(true);
  };

  const closeCallPanel = (o: boolean) => {
    setIsUserOnCall(o);
    setAgentOnCall(undefined);
    console.log('REFRESH');
  };

  /****************************************/
  /*************** RENDER *****************/
  /****************************************/

  const isLoading = isLoadingLearningModule || isLoadingStats;

  if (isLoading || !lm) {
    return (
      <div className="bg-[#FBFBFB] px-6 py-4 h-[100vh] flex flex-col ">
        <div>
          <Skeleton className="w-full h-24 mt-2" />
        </div>
        <div className="flex items-stretch pt-1 flex-1">
          <div className="w-[35%] mr-6">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>

          <div className="flex-1">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#FBFBFB] px-6 py-4 min-h-[100vh]">
      <PageHeader
        title={[
          {
            title: 'Competitions',
            href: LinksManager.learningModules('?show=competitions'),
          },
          { title: lm?.name || '' },
        ]}
        rightComponent={
          <div className="flex items-center">
            {isAdmin && (
              <Button variant={'outline'} onClick={editModule}>
                Edit
              </Button>
            )}
          </div>
        }
      />

      <div className="flex flex-col items-center mt-6">
        <div className="font-semibold text-base">{lm?.name}</div>
        <div className="text-muted-foreground w-[40%] mt-2 text-xs">
          {lm?.description}
        </div>
        <div className="mt-6">
          <Tabs
            defaultValue="leaderboard"
            onValueChange={(e) => {
              setShow(e);
            }}
          >
            <TabsList>
              <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
              <TabsTrigger value="bots">Bots to call</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {show == 'leaderboard' && (
          <div className="relative">
            <Podium leaderboard={leaderboard} className="mt-8" />
            <div className="absolute bottom-0 left-[44%]">
              <Countdown startDate={lm?.startDate} dueDate={lm?.dueDate} />
            </div>
          </div>
        )}
      </div>

      {show == 'leaderboard' && (
        <div className="mt-2">
          <div className="flex items-center">
            <div className="flex items-center">
              <div className="text-muted-foreground mr-2 text-sm">My rank:</div>
              <div className="text-sm">{userRank == 0 ? 'N.A.' : userRank}</div>
            </div>
            <div className="flex-1"></div>
            <div className="border rounded-[6px] p-[6px] text-sm flex items-center bg-white w-[20vw]">
              <div className="mr-2">
                <Search size={16} className="text-muted-foreground" />
              </div>
              <div className="flex-1 mr-2">
                <input
                  value={searchString}
                  onChange={(e) => {
                    search(e.target.value);
                  }}
                  className="outline-none w-full "
                  placeholder="Search"
                />
              </div>
            </div>
          </div>
          <div className="max-w-[92.25vw] overflow-x-auto">
            <Table hideFooter={true} fitToContent={true} className="mt-2">
              <TableContent>
                <TableRow>
                  <TableCellHead className="flex justify-center">
                    Rank
                  </TableCellHead>
                  <TableCellHead>Player</TableCellHead>
                  <TableCellHead>Total score</TableCellHead>

                  {stats?.allBots.map((bot: any) => {
                    return (
                      <TableCellHead key={bot.agent?.id}>
                        {bot.agent?.firstName} {bot.agent?.lastName}
                      </TableCellHead>
                    );
                  })}
                </TableRow>

                {stats?.rankedUsers
                  .filter((st: any) => {
                    if (searchString) {
                      const ss = searchString.toLowerCase();
                      const fn = st.user.firstName.toLowerCase();
                      const ln = st.user.lastName.toLowerCase();

                      if (!fn.includes(ss) && !ln.includes(ss)) {
                        return false;
                      }
                    }
                    return true;
                  })
                  .map((st: any, i: number) => {
                    let rank: any = '';
                    if (i < 3) {
                      let badge = 'first.svg';
                      if (i == 1) {
                        badge = 'second.svg';
                      } else if (i == 2) {
                        badge = 'third.svg';
                      }

                      rank = (
                        <Image
                          src={'/images/icons/' + badge}
                          width={28}
                          height={28}
                          alt="Badge position"
                        />
                      );
                    } else {
                      rank = i + 1;
                    }
                    let bg = '';
                    let showCallButtons = false;

                    if (st.user.id === userId) {
                      bg = 'bg-teal-50';
                      showCallButtons = true;
                    }

                    return (
                      <TableRow key={i} className={bg}>
                        <TableCell className="flex justify-center">
                          {rank}
                        </TableCell>
                        <TableCell>
                          <div className="border rounded-full p-1 bg-[#fbfbfb] inline-block">
                            <div className="flex items-center">
                              <div className="mr-1">
                                <Avatar className="w-[24px] h-[24px]">
                                  {st.user?.avatar && (
                                    <AvatarImage src={st.user.avatar} />
                                  )}
                                  <AvatarFallback className="text-sm text-muted-foreground">
                                    {st.user?.firstName?.charAt(0) || ''}
                                  </AvatarFallback>
                                </Avatar>
                              </div>
                              <div className="mr-1">
                                {st.user.firstName} {st.user.lastName}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <div>
                              <Image
                                src="/images/icons/shootingStars.svg"
                                width={20}
                                height={10}
                                alt="Sell Better Logo"
                              />
                            </div>
                            <div>{st.totalScore > 0 ? st.totalScore : '-'}</div>
                          </div>
                        </TableCell>
                        {stats.allBots.map((bot: any) => {
                          const tInfo = st.bots[bot.taskId];

                          return (
                            <TableCell key={bot.agent?.id + ' ' + i}>
                              {tInfo?.maxScore ||
                                (showCallButtons ? (
                                  <div className="inline-block">
                                    <div
                                      className="bg-sky-100 rounded-lg p-2 text-[#1A829D] flex items-center cursor-pointer hover:bg-sky-200"
                                      onClick={() => {
                                        callBot(bot.agent);
                                      }}
                                    >
                                      <div className="mr-2">
                                        <Phone size={16} />
                                      </div>
                                      <div>Call now</div>
                                    </div>
                                  </div>
                                ) : (
                                  '-'
                                ))}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    );
                  })}
              </TableContent>
              <TableFooter>&nbsp;</TableFooter>
            </Table>
          </div>
        </div>
      )}

      {show == 'bots' && (
        <div className="flex flex-col items-center mt-4">
          {stats.allBots.map((bot: any, i: number) => {
            // console.log(userRankInfo, bot.agent);
            const userTaskInfo = userRankInfo?.bots[bot.taskId];
            const taskInfo = tasksInfo[bot.taskId];

            // console.log(tasksInfo); //); //

            const Icon =
              CALL_TYPE_TO_ICON?.[
                bot.agent?.callType as keyof typeof CALL_TYPE_TO_ICON
              ]?.Icon;

            let attemptsLeft = 0;
            const numberOfAttempts = userTaskInfo?.numberOfAttempts || 0;
            if (taskInfo?.info?.maxNumberOfAttempts > numberOfAttempts) {
              attemptsLeft =
                taskInfo?.info?.maxNumberOfAttempts - numberOfAttempts;
            }
            return (
              <div
                key={bot.agent?.id + ' ' + i}
                className="border m-2 px-2 py-3 bg-white rounded-lg min-w-[40%]"
              >
                <div className="flex items-center">
                  <div className="border rounded-full p-1 bg-[#fbfbfb] inline-block mr-3">
                    <div className="flex items-center">
                      <div className="mr-1">
                        <AgentAvatar
                          className="w-[26px] h-[26px]"
                          agent={bot.agent}
                        />
                      </div>
                      <div className="mr-2 font-semibold">
                        {bot.agent.firstName} {bot.agent.lastName}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center text-muted-foreground text-xs bg-gray-100 p-2 rounded-full">
                    <div className="flex items-center">
                      <Icon size={16} className="mr-1" />
                      {
                        CALL_TYPE_LABELS[
                          bot.agent?.callType as keyof typeof CALL_TYPE_TO_ICON
                        ]
                      }
                    </div>
                  </div>
                  <div className="flex-1" />
                  <Button variant="outline" onClick={() => callBot(bot.agent)}>
                    <Phone size={16} className="mr-2" /> Make a call
                  </Button>
                </div>

                <div className="flex items-center mt-6 text-xs">
                  <div className="text-muted-foreground mr-1">
                    Filler words score:
                  </div>
                  <div>
                    {taskInfo?.info?.incorporateFillerWordsScore
                      ? 'Enabled'
                      : 'Disabled'}
                  </div>
                  <div className="flex-1" />
                  <div className="text-muted-foreground mr-3">
                    Attempts left:
                  </div>
                  <div className="mr-2">
                    <ProgressCircle
                      color={'blue'}
                      value={Math.trunc(
                        (100 * attemptsLeft) /
                          (taskInfo?.info?.maxNumberOfAttempts > 0
                            ? taskInfo?.info?.maxNumberOfAttempts
                            : 1),
                      )}
                      size="xs"
                    ></ProgressCircle>
                  </div>
                  <div>
                    {attemptsLeft} out of {taskInfo?.info?.maxNumberOfAttempts}
                  </div>
                </div>
                {stats?.rankedUsers
                  ?.find((ru: any) => ru.user.id === userId)
                  ?.bots[bot.taskId]?.attempts?.map(
                    (attempt: any, index: number) => (
                      <div
                        key={index}
                        className="flex items-center text-xs text-muted-foreground mt-2 border-t pt-2"
                      >
                        <div>Attempt {attempt.attemptNumber}</div>
                        <div className="flex-1" />
                        <div className="mr-2">Score: {attempt.callScore}</div>
                        <div>Filler words: {attempt.fillerWords}</div>
                        <div className="ml-2">
                          <a href={`/calls/${attempt.callId}`}>
                            <ExternalLink size={16} />
                          </a>
                        </div>
                      </div>
                    ),
                  )}
              </div>
            );
          })}
        </div>
      )}
      {agentOnCall && (
        <CallPanel
          key={'call-panel-' + agentOnCall?.id}
          open={isUserOnCall}
          agent={agentOnCall}
          onOpenChange={closeCallPanel}
        />
      )}
    </div>
  );
}
