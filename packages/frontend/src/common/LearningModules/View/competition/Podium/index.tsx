import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';

interface IProps {
  leaderboard: any[];
  className?: string;
}

export default function Podium({ leaderboard, className }: IProps) {
  const first = leaderboard[0];
  const second = leaderboard[1];
  const third = leaderboard[2];

  const renderPodium = (place: number) => {
    const item = place === 1 ? first : place === 2 ? second : third;
    const score = !item ? 0 : item.score;

    return (
      <motion.div
        key={place}
        className="flex flex-col items-center w-[200px] lg:w-[270px]"
        initial={{ translateY: '50px', opacity: 0 }}
        animate={{ translateY: '0px', opacity: 1 }}
        transition={{
          duration: 0.4,
          delay: 0.1 * (4 - place),
        }}
      >
        <div className="flex flex-col items-center w-full">
          <div className="relative mb-2">
            <Avatar className="w-16 h-16">
              {item?.avatar && <AvatarImage src={item.avatar} />}
              <AvatarFallback className="text-sm text-muted-foreground">
                {item?.firstName.charAt(0) || ''}
              </AvatarFallback>
            </Avatar>
            <div className="absolute bottom-0 left-0 right-0 flex flex-row justify-center -mb-4">
              <img
                src={`/images/competition/medals/medal-${place}.svg`}
                className="w-8 h-8"
              />
            </div>
          </div>
          <p className="text-l w-full text-center mt-2">
            {item?.firstName} {item?.lastName}
          </p>
        </div>
        <div className="relative -mt-2">
          <img
            src={`/images/competition/podium-place-${place}.svg`}
            className="w-full"
            style={{
              aspectRatio:
                place === 1 ? 300 / 265 : place === 2 ? 300 / 216 : 300 / 167,
            }}
          />
          <div className="absolute top-0 left-0 right-0">
            <div className="w-full flex flex-col items-center mt-12">
              <div className="flex flex-row items-start">
                <img
                  src={`/images/competition/stars-black.svg`}
                  className="mr-2 mt-2"
                />
                <p className="text-4xl font-bold">{score}</p>
              </div>
              <p className="uppercase text-muted-foreground font-medium">
                points
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  //
  return (
    <AnimatePresence>
      <div
        className={cn(
          'flex flex-row justify-center items-end space-x-3 relative',
          className,
        )}
      >
        <div className="absolute left-0 right-0 top-0 bottom-0">
          <img
            src="/images/competition/internal-comp-bg.png"
            className="h-full w-full"
          />
        </div>
        {renderPodium(2)}
        {renderPodium(1)}
        {renderPodium(3)}
      </div>
    </AnimatePresence>
  );
}
