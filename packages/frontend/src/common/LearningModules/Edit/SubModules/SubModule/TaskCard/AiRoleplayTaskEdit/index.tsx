import {
  AiRoleplayTask,
  LearningModuleTaskType,
  Task,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import { GripVertical, Pencil, Trash2 } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { STATS_OPTIONS } from '@/lib/common-types';
import { DndFunctions } from '../../..';
import { useEffect, useState } from 'react';
import AgentAvatar from '@/components/Avatars/Agent';

interface IProps {
  task: Task;
  doEdit: () => void;
  doDelete: () => void;
  dnd: DndFunctions;
  isCompetition?: boolean;
}

export default function AiRoleplayTaskEdit({
  task,
  doEdit,
  doDelete,
  dnd,
  isCompetition,
}: IProps) {
  const [isDragged, setIsDragger] = useState<boolean>(false);

  useEffect(() => {
    let done = false;
    if (dnd.draggedObject && task) {
      if (dnd.draggedObject.id == task.id && dnd.isActive) {
        setIsDragger(true);
        done = true;
      }
    }
    if (!done) {
      setIsDragger(false);
    }
  }, [dnd]);

  if (task.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
    const t: AiRoleplayTask = task.info;

    const stats: string[] = [];
    for (const s of t.stats) {
      stats.push(String(s.name));
    }

    return (
      <div
        className={cn('border-t', {
          'opacity-50 ': isDragged,
          'select-none': dnd.isActive,
        })}
        onMouseEnter={(e) => {
          dnd.onMouseEnter(task);
        }}
        onMouseLeave={() => {
          dnd.onMouseLeave();
        }}
      >
        <div className="flex items-center p-2">
          <div
            className={cn({
              'hover:cursor-grab': !dnd.isActive,
            })}
            onMouseDown={(e) => {
              dnd.setCurrentDraggedObject(task);
              dnd.onMouseDown(task, e);
            }}
          >
            <GripVertical size={16} className="text-muted-foreground" />
          </div>
          <div className="border rounded-full p-0 flex items-center ml-2">
            <AgentAvatar
              className="w-[26px] h-[26px] border-white border-2"
              agent={t?.agent}
            />
            <div className="capitalize mx-1 font-medium mr-2">
              {t.agent?.firstName || ''} {t.agent?.lastName || ''}
            </div>
          </div>
          <div className="flex-1" />
          <div
            className="rounded-lg hover:bg-gray-50 cursor-pointer p-2"
            onClick={doEdit}
          >
            <Pencil size={16} className="text-muted-foreground" />
          </div>
          <div
            className="rounded-lg hover:bg-gray-50 cursor-pointer p-2"
            onClick={doDelete}
          >
            <Trash2 size={16} className="text-muted-foreground" />
          </div>
        </div>

        {isCompetition && (
          <div>
            <div className="flex items-center ml-8 mb-2 text-xs">
              <div className="text-muted-foreground ">
                Maximum number of attempts:
              </div>
              <div className="ml-2">{t.maxNumberOfAttempts}</div>
            </div>
            <div className="flex items-center ml-8 mb-2 text-xs">
              <div className="text-muted-foreground ">Filler word score:</div>
              <div className="ml-2">
                {t.incorporateFillerWordsScore ? 'Enabled' : 'Disabled'}
              </div>
            </div>
          </div>
        )}

        {!isCompetition && (
          <>
            {t.minNumberOfAttempts != undefined &&
              t.minNumberOfAttempts > 0 && (
                <div className="flex items-center ml-8 mb-2 text-xs">
                  <div className="text-muted-foreground ">
                    Required successful attempts:
                  </div>
                  <div className="ml-2">
                    {t.minNumberOfAttempts == 0
                      ? 'unlimited'
                      : t.minNumberOfAttempts}
                  </div>
                </div>
              )}
            {t.minScorecardScore != undefined && t.minScorecardScore > 0 && (
              <div className="flex items-center ml-8 mb-2 text-xs">
                <div className="text-muted-foreground ">
                  Min scorecard score:
                </div>
                <div className="ml-2">
                  {t.minScorecardScore == 0 ? 'Any' : t.minScorecardScore}
                </div>
              </div>
            )}
            {stats.length > 0 && (
              <div className="flex items-center ml-8 mb-2 text-xs">
                <div className="text-muted-foreground ">Stats:</div>
                <div className="ml-2">
                  {stats.length == 0 && 'Any'}
                  {stats.map((s: any, i: number) => {
                    return STATS_OPTIONS.map((so: any) => {
                      if (so.id == s) {
                        return (
                          <span key={so.id}>
                            {i > 0 && ', '}
                            {so.label}
                          </span>
                        );
                      }
                    });
                  })}
                </div>
              </div>
            )}

            {t.criterions && t.criterions.length > 0 && (
              <div className="flex items-start ml-8 mb-2 text-xs">
                <div className="text-muted-foreground text-nowrap">
                  Pass this criteria:
                </div>
                <div className="ml-2">
                  {t.criterions.map((c: any, i: number) => {
                    return (
                      <span key={'cr' + i}>
                        {i > 0 && ', '}
                        {c.criterion}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    );
  }
}
