import { LearningModuleTaskType, Task } from '@/lib/LearningModule/types';
import AiRoleplayTaskEdit from './AiRoleplayTaskEdit';
import { DndFunctions } from '../..';

interface IProps {
  task: Task;
  doEdit: () => void;
  doDelete: () => void;
  dnd: DndFunctions;
  isCompetition?: boolean;
}

export default function TaskEdit({
  task,
  doEdit,
  doDelete,
  dnd,
  isCompetition,
}: IProps) {
  if (task.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
    return (
      <AiRoleplayTaskEdit
        task={task}
        doEdit={doEdit}
        doDelete={doDelete}
        dnd={dnd}
        isCompetition={isCompetition}
      />
    );
  }
}
