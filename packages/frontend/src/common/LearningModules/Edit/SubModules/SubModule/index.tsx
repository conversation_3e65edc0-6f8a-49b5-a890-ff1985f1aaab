import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  LearningModuleTaskType,
  SubModule,
  Task,
} from '@/lib/LearningModule/types';
import { GripVertical, Plus, Trash2 } from 'lucide-react';
import { DndFunctions } from '..';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';
import EditTask from './EditTaskDetails';
import TaskView from './TaskCard';
import { AgentDto } from '@/lib/Agent/types';
import ConfirmationModal from '@/components/ConfirmationModal';

interface IProps {
  sm: SubModule;
  setSM: (s: SubModule, saveToDb?: boolean) => void;
  onDelete: (smId: string) => void;
  dnd: DndFunctions;
  dndTasks: DndFunctions;
  generateTaskId: () => string;
  isCompetition?: boolean;
}

export default function Submodule({
  sm,
  setSM,
  onDelete,
  dnd,
  dndTasks,
  generateTaskId,
  isCompetition,
}: IProps) {
  const [isDragged, setIsDragger] = useState<boolean>(false);
  const [editingTask, setEditingTask] = useState<Task>({} as Task);
  const [openEditingTask, setOpenEditingTask] = useState<boolean>(false);
  const [showConfirmEditingForTask, setShowConfirmEditingForTask] =
    useState<boolean>(false);
  const [isNewTask, setIsNewTask] = useState<boolean>(false);

  useEffect(() => {
    let done = false;
    if (dnd.draggedObject && sm) {
      if (dnd.draggedObject.id == sm.id && dnd.isActive) {
        setIsDragger(true);
        done = true;
      }
    }
    if (!done) {
      setIsDragger(false);
    }
  }, [dnd]);

  /***********************************/
  /************ TASKS ***************/
  /***********************************/

  const startAddTask = () => {
    const taskId = generateTaskId();
    const t: Task = {
      id: taskId,
      type: LearningModuleTaskType.AI_BUYER_ROLEPLAY,
      info: {
        agent: undefined,
        minNumberOfAttempts: 1,
        maxNumberOfAttempts: isCompetition ? 10 : 0,
        minScorecardScore: 80,
        stats: [],
        criterions: [],
      },
    };
    setIsNewTask(true);
    setEditingTask({ ...t });
    setOpenEditingTask(true);
  };

  const saveTask = (t: Task, selectedAgents?: AgentDto[]) => {
    t.hasInfoUpdated = true;

    setIsNewTask(false);
    const tmp: Task[] = [];
    let found = false;
    for (const smt of sm.tasks) {
      if (smt.id == t.id) {
        tmp.push(t);
        found = true;
      } else {
        tmp.push(smt);
      }
    }

    if (!found) {
      tmp.push(t);
      //multi agent selection can happen only with new agents:
      if (selectedAgents) {
        if (
          selectedAgents.length > 0 &&
          t.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY &&
          t.info.agent
        ) {
          for (const agent of selectedAgents) {
            if (agent.id != t.info.agent.id) {
              const task = {
                ...t,
                id: generateTaskId(),
                info: { ...t.info, agent },
              };
              tmp.push(task);
            }
          }
        }
      }
    }

    setSM({ ...sm, tasks: tmp }, true);
    setOpenEditingTask(false);
  };

  const deleteTask = (t: Task) => {
    const tmp: Task[] = [];
    for (const smt of sm.tasks) {
      if (smt.id != t.id) {
        tmp.push(t);
      }
    }
    setSM({ ...sm, tasks: tmp }, true);
  };

  const startEdit = (t: Task) => {
    let hasAttempts = false;
    for (const stats of sm.assigneesStats) {
      for (const tsk of stats.tasks) {
        if (tsk.taskId == t.id) {
          hasAttempts = true;
          break;
        }
      }
      if (hasAttempts) {
        break;
      }
    }

    setEditingTask({ ...t, hasInfoUpdated: false });

    if (hasAttempts) {
      setShowConfirmEditingForTask(true);
    } else {
      doStartEdit();
    }
  };

  const doStartEdit = () => {
    setShowConfirmEditingForTask(false);
    setOpenEditingTask(true);
  };

  /***********************************/
  /************ RENDER ***************/
  /***********************************/

  return (
    <div
      className={cn('border bg-white rounded-lg w-[100%] mb-4', {
        'opacity-50 ': isDragged,
      })}
    >
      {isCompetition && (
        <div className="flex items-center p-2 bg-gray-50">
          <div className="flex-1" />
          <div className="ml-6 mr-2">
            <Button
              className="bg-white"
              variant={'outline'}
              onClick={startAddTask}
            >
              <Plus size={16} className="mr-2" /> Add task
            </Button>
          </div>
        </div>
      )}

      {!isCompetition && (
        <div
          className="flex items-center p-2 bg-gray-50"
          onMouseEnter={() => {
            dnd.onMouseEnter(sm);
            if (sm.tasks.length == 0) {
              if (dnd.onMouseEnterForTasks) {
                dnd.onMouseEnterForTasks(sm);
              }
            }
          }}
          onMouseLeave={() => {
            dnd.onMouseLeave();
            if (sm.tasks.length == 0) {
              if (dnd.onMouseLeaveForTasks) {
                dnd.onMouseLeaveForTasks();
              }
            }
          }}
        >
          <div
            className={cn({
              'hover:cursor-grab': !dnd.isActive,
            })}
            onMouseDown={(e) => {
              dnd.setCurrentDraggedObject(sm);
              dnd.onMouseDown(sm, e);
            }}
          >
            <GripVertical size={16} className="text-muted-foreground" />
          </div>

          <div className="flex-1 ml-2">
            <Input
              className={cn('text-sm h-8 bg-white', {
                'select-none': dnd.isActive,
              })}
              value={sm.name}
              onChange={(e) => {
                setSM({ ...sm, name: e.target.value });
              }}
              placeholder="Submodule name"
            />
          </div>
          <div className="ml-6 mr-2">
            <Button
              className="bg-white"
              variant={'outline'}
              onClick={startAddTask}
            >
              <Plus size={16} className="mr-2" /> Add task
            </Button>
          </div>
          <div
            className="rounded-lg hover:bg-gray-50 cursor-pointer p-2"
            onClick={() => {
              onDelete(sm.id);
            }}
          >
            <Trash2 size={16} className="text-muted-foreground" />
          </div>
        </div>
      )}

      {sm.tasks &&
        sm.tasks.length > 0 &&
        sm.tasks.map((t: Task) => {
          return (
            <TaskView
              task={t}
              key={t.id}
              doDelete={() => deleteTask(t)}
              doEdit={() => {
                startEdit(t);
              }}
              dnd={dndTasks}
              isCompetition={isCompetition}
            />
          );
        })}

      {openEditingTask && (
        <EditTask
          editingTask={editingTask}
          onUpdate={saveTask}
          open={true}
          onPanelClose={() => {
            setOpenEditingTask(false);
            setIsNewTask(false);
          }}
          isNewTask={isNewTask}
          isCompetition={isCompetition}
        />
      )}

      {showConfirmEditingForTask && (
        <ConfirmationModal
          open={showConfirmEditingForTask}
          onCancel={() => {
            setShowConfirmEditingForTask(false);
          }}
          onConfirm={() => {
            doStartEdit();
          }}
          title={'Important Warning!'}
          description={
            'Some users have already attempted to complete this task. Editing it will erase their progress, and this action cannot be undone. Are you sure you want to continue?'
          }
        />
      )}
    </div>
  );
}
