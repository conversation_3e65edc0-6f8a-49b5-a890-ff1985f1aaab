import { Sheet, SheetContentLight } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import LearningModuleTaskTypeFilter from '@/common/AnalyticsOld/DashboardTab/Filters/LearningModuleTaskTypeFilter';
import {
  EvalStats,
  LearningModuleTaskType,
  Task,
} from '@/lib/LearningModule/types';
import AgentService from '@/lib/Agent';
import { useEffect, useRef, useState } from 'react';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { cn } from '@/lib/utils';
import ScorecardSelectCriterions, {
  ScorecardSelectCriterionsRef,
  SelectedCriterion,
} from '@/common/Scorecards/scorecardPreview/selectCriterions';
import BuyersFilterWithFolders from '@/common/AnalyticsOld/DashboardTab/Filters/BuyersFilterWithFolders';
import { AgentDto } from '@/lib/Agent/types';
import Checkbox from '@/components/ui/Hyperbound/checkbox';

interface IProps {
  open: boolean;
  onPanelClose: () => void;
  editingTask: Task;
  onUpdate: (t: Task, selectedAgents?: AgentDto[]) => void;
  isNewTask: boolean;
  isCompetition?: boolean;
}

export default function EditTaskDetailsPanel({
  open,
  onPanelClose,
  onUpdate,
  editingTask,
  isNewTask,
  isCompetition,
}: IProps) {
  const [stats, setStats] = useState<string[]>([]);
  const [scorecard, setScorecard] = useState<ScorecardConfigDto>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isAgentSet, setIsAgentSet] = useState<boolean>(false);
  const [et, setEt] = useState<Task>({ ...editingTask });
  const [canSave, setCanSave] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showMultiTaskWillBeCreated, setShowMultiTaskWillBeCreated] =
    useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [taskCriteriaCheckboxes, setTaskCriteriaCheckboxes] = useState<any>({
    minScore: false,
    stats: false,
    scorecardCriteria: false,
  });

  useEffect(() => {
    if (editingTask) {
      if (editingTask.info) {
        if (editingTask.info.stats) {
          const tmp: string[] = [];
          for (const s of editingTask.info.stats) {
            tmp.push(s.name);
          }

          setStats(tmp);
        }

        if (editingTask.info.agent) {
          setIsAgentSet(true);
          loadScorecard();
        }
      }
    }
    validateForSaving({ ...editingTask });
    setEt({ ...editingTask });
  }, [editingTask]);

  useEffect(() => {
    const checkboxStatus = {
      minScore: false,
      stats: false,
      scorecardCriteria: false,
    };

    if (editingTask.info?.stats && editingTask.info.stats.length > 0) {
      checkboxStatus.stats = true;
    }

    if (et.info?.minScorecardScore && et.info.minScorecardScore > 0) {
      checkboxStatus.minScore = true;
    }

    if (et.info?.criterions && et.info?.criterions.length > 0) {
      checkboxStatus.scorecardCriteria = true;
    }
    setTaskCriteriaCheckboxes({ ...checkboxStatus });
    loadScorecard();
  }, [et]);

  /********************************/
  /************ MISC **************/
  /********************************/

  const validateForSaving = (edTask: Task) => {
    let errMsg = '';
    let s = false;

    if (edTask.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
      if (edTask.info) {
        if (edTask.info.agent) {
          s = true;
        }
        if (s) {
          if (
            !edTask.info.minNumberOfAttempts ||
            edTask.info.minNumberOfAttempts == 0
          ) {
            if (
              edTask.info.stats.length == 0 &&
              edTask.info.criterions.length == 0
            ) {
              errMsg =
                'Please select stats or criterias to mark this task as passed';
              s = false;
            }
          }
        }
      }
    }

    setErrorMessage(errMsg);
    setCanSave(s);
  };

  const loadScorecard = async () => {
    if (et.info) {
      if (et.info.agent && et.info.agent.scorecardConfigId) {
        if (
          !scorecard ||
          (scorecard && scorecard.id != et.info.agent.scorecardConfigId)
        ) {
          setIsLoading(true);
          const sc: ScorecardConfigDto | undefined =
            await ScorecardConfigService.getScorecardConfigById(
              et.info.agent.scorecardConfigId,
            ); //
          if (sc) {
            setScorecard(sc);
          }
          setIsLoading(false);
        }
      }
    }
  };

  const loadAgent = async (buyers: number[]) => {
    if (buyers.length > 0) {
      const agents = await AgentService.getOrgAgentsByIds([buyers[0]]);
      if (et.info && agents && agents.length > 0) {
        et.info.agent = agents[0];
        setIsAgentSet(true);
      }
    } else {
      if (et.info) {
        et.info.agent = undefined;
      }
    }
    validateForSaving({ ...et });

    if (et.info && et.info?.criterions) {
      et.info.criterions = [];
    }

    setEt({ ...et });
  };

  const [currentlySelectedAgents, setCurrentlySelectedAgents] = useState<
    number[]
  >([]);

  const loadMultipleAgents = async (buyers: number[]) => {
    setCurrentlySelectedAgents(buyers);

    loadAgent([buyers[0]]);

    if (buyers.length > 1) {
      setShowMultiTaskWillBeCreated(true);
    } else {
      setShowMultiTaskWillBeCreated(false);
    }
  };

  /***********************************/
  /************ ACTIONS **************/
  /***********************************/

  const updateTask = (prop: string, value: string) => {
    if (et.info) {
      if (prop == 'minNumberOfAttempts') {
        if (value) {
          if (parseInt(value) < 0) {
            et.info.minNumberOfAttempts = 0;
          } else {
            et.info.minNumberOfAttempts = parseInt(value);
          }
        } else {
          et.info.minNumberOfAttempts = undefined;
        }
      } else if (prop == 'maxNumberOfAttempts') {
        if (value) {
          if (parseInt(value) > 0) {
            et.info.maxNumberOfAttempts = parseInt(value);
          } else {
            et.info.maxNumberOfAttempts = 0;
          }
        } else {
          et.info.maxNumberOfAttempts = undefined;
        }
      } else if (prop == 'minScorecardScore') {
        if (value) {
          let v = parseInt(value);
          if (v > 100) {
            v = 100;
          }
          if (v > 0) {
            et.info.minScorecardScore = v;
          } else {
            et.info.minScorecardScore = 0;
          }
        } else {
          et.info.minScorecardScore = undefined;
        }
      } else if (prop == 'incorporateFillerWordsScore') {
        et.info.incorporateFillerWordsScore =
          !et.info.incorporateFillerWordsScore;
      }
    }

    validateForSaving(et);
    setEt({ ...et });
  };

  const updateStats = (s: string[]) => {
    const tmp: EvalStats[] = [];
    for (const st of s) {
      tmp.push({
        name: st,
        min: 0,
        max: 0,
      });
    }
    if (et.info) {
      if (et.info.stats) {
        et.info.stats = tmp;
      }
    }
    setEt({ ...et });
    setStats(s);
  };

  const updateCriterions = (s: SelectedCriterion[]) => {
    if (et.info) {
      et.info.criterions = s;
      setEt({ ...et });
    }
  };

  const toggleMinScoreCheckbox = () => {
    const nv = !taskCriteriaCheckboxes.minScore;

    if (!nv) {
      updateTask('minScorecardScore', '0');
    } else {
      setTaskCriteriaCheckboxes({ ...taskCriteriaCheckboxes, minScore: nv });
    }
  };

  // const toggleStats = () => {
  //   const nv = !taskCriteriaCheckboxes.stats;

  //   if (!nv) {
  //     updateStats([]);
  //   } else {
  //     setTaskCriteriaCheckboxes({ ...taskCriteriaCheckboxes, stats: nv });
  //   }
  // };

  const toggleScorecardCriteria = () => {
    if (refSelectCriterionsComponent.current) {
      refSelectCriterionsComponent.current?.toggleAll();
    }
    const nv = !taskCriteriaCheckboxes.scorecardCriteria;

    if (!nv) {
      updateCriterions([]);
    } else {
      setTaskCriteriaCheckboxes({
        ...taskCriteriaCheckboxes,
        scorecardCriteria: nv,
      });
    }
  };

  /***********************************/
  /************ SAVE ***************/
  /***********************************/

  const save = async () => {
    if (currentlySelectedAgents && currentlySelectedAgents.length > 0) {
      setIsSaving(true);
      let allSelectedAgents: AgentDto[] = [];
      try {
        allSelectedAgents = await AgentService.getOrgAgentsByIds(
          currentlySelectedAgents,
        );
      } catch (e) {
        console.log(e);
      }
      onUpdate({ ...et }, allSelectedAgents);
      setIsSaving(false);
    } else {
      onUpdate({ ...et });
    }
  };

  /***********************************/
  /************ RENDER ***************/
  /***********************************/

  const refSelectCriterionsComponent =
    useRef<ScorecardSelectCriterionsRef>(null);

  return (
    <Sheet open={open} onOpenChange={onPanelClose}>
      <SheetContentLight side={'rightFull'} className="p-0">
        <div className="h-screen min-w-[50vw] overflow-auto">
          <div className="flex flex-col h-full overflow-hidden">
            <div className="flex flex-col flex-1 overflow-auto">
              <div className="text-base font-semibold mx-1 p-3 ">
                {isNewTask ? 'Create new task' : 'Edit task'}
              </div>

              <div className="">
                {!isCompetition && (
                  <div className="p-3">
                    <div className="text-sm font-medium mx-1">Type</div>
                    <div className="mt-2 mx-1 w-[400px]">
                      <LearningModuleTaskTypeFilter
                        current={[et.type]}
                        onUpdate={(s: string[]) => {}}
                        isRadioSelect={true}
                      />
                    </div>
                  </div>
                )}

                <div className="p-4">
                  <div className="text-sm font-medium">Select bot</div>
                  {!isNewTask ? (
                    <div className="mt-2 flex items-center">
                      {/*<BuyersFilter
                        current={et.info?.agent ? [et.info?.agent.id] : []}
                        onBuyersUpdated={loadAgent}
                        isRadioSelect={true}
                        keepSelectionInPlace={true}
                        displaySelectedName={true}
                        hideClearBtn={true}
                        className="py-[18px] w-[400px]"
                      />
                      <div className="ml-2">
                        {isLoading && (
                          <Loader2Icon className="animate-spin" size={18} />
                        )}
                      </div>*/}
                      <BuyersFilterWithFolders
                        current={et.info?.agent ? [et.info?.agent.id] : []}
                        onBuyersUpdated={loadAgent}
                        isRadioSelect={false}
                        keepSelectionInPlace={true}
                        displaySelectedName={true}
                        hideClearBtn={true}
                        className="w-[400px]"
                        hideSearch={false}
                        selectLimit={1}
                      />
                    </div>
                  ) : (
                    <div className="mt-2 flex items-center">
                      <BuyersFilterWithFolders
                        current={currentlySelectedAgents}
                        onBuyersUpdated={loadMultipleAgents}
                        isRadioSelect={true}
                        keepSelectionInPlace={true}
                        displaySelectedName={true}
                        hideClearBtn={true}
                        className="w-[400px]"
                        hideSearch={false}
                      />
                    </div>
                  )}
                  {showMultiTaskWillBeCreated && (
                    <div className="mt-2 text-muted-foreground text-xs">
                      {currentlySelectedAgents.length} tasks will be created
                      (one per agent)
                    </div>
                  )}
                </div>
                {/* <div>with tags:</div>
                <div>
                  <BuyerFilterWithTags
                    current={currentlySelectedAgents}
                    onBuyersUpdated={loadMultipleAgents}
                    isRadioSelect={true}
                    keepSelectionInPlace={true}
                    displaySelectedName={true}
                    hideClearBtn={true}
                    className="w-[400px]"
                    hideSearch={true}
                  />
                </div> */}

                <div
                  className={cn('p-4', {
                    invisible: !isAgentSet,
                  })}
                >
                  {isCompetition && (
                    <div>
                      <div className="text-sm font-medium">
                        Max number of tries
                      </div>
                      <div className="w-[400px]">
                        <Input
                          className={'px-2'}
                          min={0}
                          type={'number'}
                          value={et.info?.maxNumberOfAttempts}
                          onChange={(e) => {
                            updateTask('maxNumberOfAttempts', e.target.value);
                          }}
                        />
                      </div>
                    </div>
                  )}

                  {isCompetition && (
                    <div className="mt-6 flex items-top">
                      <div className="mt-1">
                        <Checkbox
                          checked={
                            et.info?.incorporateFillerWordsScore || false
                          }
                          onToggle={() => {
                            updateTask('incorporateFillerWordsScore', 'toggle');
                          }}
                        />
                      </div>
                      <div className="text-sm mr-2 font-semibold">
                        <div>
                          Incorporate filler words score as a tie-breaker
                        </div>
                        <div className="text-xs text-muted-foreground">
                          We recommend using the amount of filler words used by
                          the rep as a tie-breaker in internal competitions
                        </div>
                      </div>
                    </div>
                  )}

                  {!isCompetition && (
                    <>
                      <div className="text-sm mb-2 flex items-center">
                        <div className="font-semibold ">
                          To pass this task, reps must meet the following
                          criteria at least{' '}
                        </div>
                        <div className="w-[60px] mx-2">
                          <Input
                            className={'px-2'}
                            min={0}
                            type={'number'}
                            value={et.info?.minNumberOfAttempts}
                            onChange={(e) => {
                              updateTask('minNumberOfAttempts', e.target.value);
                            }}
                          />
                        </div>
                        <div className="font-semibold ">
                          time
                          {et.info?.minNumberOfAttempts &&
                            et.info?.minNumberOfAttempts > 1 &&
                            's'}
                          :
                        </div>
                      </div>
                      <div className="mt-4 ml-4">
                        <div className="flex items-center">
                          <div>
                            <Checkbox
                              checked={taskCriteriaCheckboxes.minScore}
                              onToggle={toggleMinScoreCheckbox}
                            />
                          </div>
                          <div className="text-sm mr-2">
                            Hit the minimum score of
                          </div>
                          <div className="w-[80px]">
                            <Input
                              type={'number'}
                              value={et.info?.minScorecardScore}
                              onChange={(e) => {
                                updateTask('minScorecardScore', e.target.value);
                              }}
                            />
                          </div>
                        </div>
                      </div>
                      {/* <div className="mt-4 ml-4">
                        <div className="flex items-center">
                          <div>
                            <Checkbox
                              checked={taskCriteriaCheckboxes.stats}
                              onToggle={toggleStats}
                            />
                          </div>
                          <div className="text-sm mr-2">
                            Meet the minimum requirements for the following
                            stats:
                          </div>
                          <div className="w-[300px]">
                            <StatsFilter
                              current={stats}
                              onUpdate={(s: string[]) => {
                                updateStats(s);
                              }}
                              statsInfos={scorecard?.stats}
                            />
                          </div>
                        </div>
                      </div> */}

                      {currentlySelectedAgents.length > 1 ? (
                        <div className="mt-5 ml-4 text-muted-foreground text-xs">
                          <div className="flex items-top mb-4">
                            <div className="mt-1">
                              <Checkbox
                                checked={false}
                                onToggle={() => {}}
                                disabled={true}
                              />
                            </div>
                            <div className="text-sm mr-2">
                              <div>
                                Pass the following scorecard criteria (disabled)
                              </div>
                              <div className="text-xs text-muted-foreground">
                                You&apos;ll need to edit each task in order to
                                select scorecard criteria
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="mt-4 ml-4">
                          {scorecard && (
                            <ScorecardSelectCriterions
                              ref={refSelectCriterionsComponent}
                              scorecard={scorecard}
                              current={
                                et.info?.criterions as SelectedCriterion[]
                              }
                              onChange={updateCriterions}
                              hideQuery={true}
                              titleComponent={
                                <div className="flex items-center mb-4">
                                  <div>
                                    <Checkbox
                                      checked={
                                        taskCriteriaCheckboxes.scorecardCriteria
                                      }
                                      onToggle={toggleScorecardCriteria}
                                    />
                                  </div>
                                  <div className="text-sm mr-2">
                                    Pass the following scorecard criteria:
                                  </div>
                                </div>
                              }
                            />
                          )}
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* <div className={cn("p-4", {
                  'invisible': !isAgentSet
                })}>
                  <div className="text-sm font-medium">Required successful attempts</div>
                  <div className="text-muted-foreground text-xs mb-2">Min number of calls to pass this task</div>
                  <div className="mt-2 flex items-center">
                    <div className="w-[80px]"><Input min={0} type={"number"} value={et.info?.minNumberOfAttempts} onChange={(e) => { updateTask('minNumberOfAttempts', e.target.value) }} /></div>
                  </div>

                </div>
                <div className={cn("p-4", {
                  'invisible': !isAgentSet
                })}>
                  <div className="text-sm font-medium">Minimum scorecard score</div>
                  <div className="text-muted-foreground text-xs mt-0">You can set this to zero if you only want to evaluate reps on stats/criteria</div>
                  <div className="mt-2 w-[80px]"><Input type={"number"} value={et.info?.minScorecardScore} onChange={(e) => { updateTask('minScorecardScore', e.target.value) }} /></div>
                </div>
                <div className={cn("p-4", {
                  'invisible': !isAgentSet
                })}>
                  <div className="text-sm font-medium">Stats</div>
                  <div className="text-muted-foreground text-xs mt-0">Select the stats a reps must pass (optional)</div>
                  <div className="mt-2 w-[400px]">
                    <StatsFilter current={stats} onUpdate={(s: string[]) => { updateStats(s) }} statsInfos={scorecard?.stats} />
                  </div>
                </div> */}
              </div>

              {/* <div className={cn("p-4", {
                'invisible': !isAgentSet || !scorecard
              })}>

                <div className="mt-3 max-w-[1000px] text-sm">
                  {scorecard && <ScorecardSelectCriterions scorecard={scorecard} current={et.info?.criterions as SelectedCriterion[]} onChange={updateCriterions} hideQuery={true} titleComponent={
                    <div className="mb-4">
                      <div className="text-sm font-medium">Scorecard criteria to pass</div>
                      <div className="text-muted-foreground text-xs mt-0">Select the criteria a reps must pass (optional)</div>
                    </div>
                  } />}
                </div>
              </div> */}
            </div>
            <div className="flex items-center border-t px-4 py-4">
              <Button
                variant={'outline'}
                className="mr-2"
                onClick={onPanelClose}
              >
                Cancel
              </Button>
              <Button onClick={save} disabled={!canSave || isSaving}>
                Save
              </Button>
              <div className="text-red-500 ml-2 text-xs">{errorMessage}</div>
            </div>
          </div>
        </div>
      </SheetContentLight>
    </Sheet>
  );
}
