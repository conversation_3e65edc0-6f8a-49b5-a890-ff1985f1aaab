import { Button } from '@/components/ui/button';
import LearningModule, {
  LearningModuleTaskType,
  SubModule,
  Task,
} from '@/lib/LearningModule/types';
import { cn, generateShortId } from '@/lib/utils';
import { Plus } from 'lucide-react';
import Submodule from './SubModule';
import Checkbox from '@/components/ui/Hyperbound/checkbox-with-label';
import {
  useCallback,
  useState,
  MouseEvent as MouseEventReact,
  useEffect,
  useRef,
} from 'react';
import useDragAndDrop from '@/hooks/ui/useDragAndDrop';
import AgentAvatar from '@/components/Avatars/Agent';

export interface DndFunctions {
  onMouseDown: (movingObj: any, e: MouseEventReact) => void;
  onMouseEnter: (hoverObj: any) => void;
  onMouseLeave: () => void;
  setCurrentDraggedObject: (obj: any) => any;
  draggedObject: any;
  isActive: boolean;
  onMouseEnterForTasks?: (hoverObj: any) => void;
  onMouseLeaveForTasks?: () => void;
}

interface IProps {
  lm: LearningModule;
  setLM: (l: LearningModule) => void;
  forceSave: (l: LearningModule) => void;
}

export default function Submodules({ lm, setLM, forceSave }: IProps) {
  const [subModules, setSubModules] = useState<SubModule[]>([]);
  const isCompetition = lm.isCompetition;

  useEffect(() => {
    setSubModules(lm.subModules);
  }, [lm]);

  /*****************************************/
  /************  Task ACTIONS **************/
  /*****************************************/

  const generateTaskId = (): string => {
    //to be extra safe, task Id must be unique for a whole Learning Module
    let id = generateShortId();
    let isUnique = true;

    do {
      isUnique = true;
      lm.subModules.map((s: SubModule) => {
        s.tasks.map((t: Task) => {
          if (t.id == id) {
            isUnique = false;
          }
        });
      });
      if (!isUnique) {
        id = generateShortId();
      }
    } while (!isUnique);

    return id;
  };

  /**********************************************/
  /************  Submodule ACTIONS **************/
  /**********************************************/

  const addSubmodule = () => {
    let id = generateShortId();
    let isUnique = true;

    do {
      isUnique = true;
      lm.subModules.map((s: SubModule) => {
        if (s.id == id) {
          isUnique = false;
        }
      });
      if (!isUnique) {
        id = generateShortId();
      }
    } while (!isUnique);

    lm.subModules.push({
      id,
      name: '',
      description: '',
      progress: 0,
      tasks: [],
      assigneesStats: [],
    });
    setLM({ ...lm });
  };

  const deleteSubmodule = (smId: string) => {
    const tmp: SubModule[] = [];
    lm.subModules.map((s: SubModule) => {
      if (s.id != smId) {
        tmp.push(s);
      }
    });
    setLM({ ...lm, subModules: tmp });
  };

  const updateSubmodule = (sm: SubModule, saveToDb = false) => {
    const sms = lm.subModules.map((s: SubModule) => {
      if (s.id == sm.id) {
        return sm;
      } else {
        return s;
      }
    });
    setLM({ ...lm, subModules: sms });
    if (saveToDb) {
      forceSave({ ...lm, subModules: sms });
    }
  };

  /***************************************************/
  /********** DRAG AND DROP SUBMODULES ***************/
  /***************************************************/

  const [isDNDActive_SUB, setIsDNDActive_SUB] = useState<boolean>(false);
  const [currentDraggedSubModule, setCurrentDraggedSubModule] =
    useState<SubModule>();

  const onDrop_SUB = useCallback(
    (movingObj: SubModule, targetObj: SubModule | undefined) => {
      // if (targetObj) {
      //   setLM({ ...lm, subModules: subModules });
      // } else {
      //   setLM({ ...lm, subModules: lm.subModules });
      // }

      setLM({ ...lm, subModules: subModules });
    },
    [lm, setLM, subModules],
  );

  const onDragStart_SUB = useCallback(
    (obj: SubModule) => {
      setSubModules([...lm.subModules]);
    },
    [lm, setSubModules],
  );

  const onDraggingOverObj_SUB = useCallback(
    (sm: SubModule) => {
      if (currentDraggedSubModule && currentDraggedSubModule.id != sm.id) {
        setSubModules(
          rearrangeModules(subModules, currentDraggedSubModule.id, sm.id),
        );
      }
    },
    [subModules, currentDraggedSubModule, setSubModules],
  );

  const rearrangeModules = (
    list: SubModule[],
    movingId: string,
    hoverId: string,
  ): SubModule[] => {
    let mo: SubModule | undefined = undefined;
    let movingIndex = 0;
    let targetIndex = 0;
    let index = 0;
    for (const s of list) {
      if (s.id == movingId) {
        mo = s;
        movingIndex = index;
      } else if (s.id == hoverId) {
        targetIndex = index;
      }
      index++;
    }

    let tmp: SubModule[] = [];

    if (mo) {
      for (const s of list) {
        if (s.id != movingId) {
          if (s.id == hoverId) {
            if (movingIndex < targetIndex) {
              tmp.push(s);
              tmp.push(mo);
            } else {
              tmp.push(mo);
              tmp.push(s);
            }
          } else {
            tmp.push(s);
          }
        }
      }
    } else {
      tmp = list;
    }

    return tmp;
  };

  const {
    dragPanelRef: dragPanelRef_SUB,
    onMouseDown: onMouseDown_SUB,
    onMouseEnter: onMouseEnter_SUB,
    onMouseLeave: onMouseLeave_SUB,
  } = useDragAndDrop<SubModule, SubModule>(
    setIsDNDActive_SUB,
    onDrop_SUB,
    onDragStart_SUB,
    onDraggingOverObj_SUB,
  );

  /************************************************/
  /************ DRAG AND DROP TASKS ***************/
  /************************************************/

  const [isDNDActive_TASKS, setIsDNDActive_TASKS] = useState<boolean>(false);
  const [currentDraggedTask, setCurrentDraggedTask] = useState<Task>();

  const isSubModule = (object: any): object is SubModule => {
    return 'name' in object;
  };

  const isTask = (object: any): object is Task => {
    return 'type' in object;
  };

  const onDrop_TASKS = useCallback(
    (movingObj: Task, targetObj: Task | SubModule | undefined) => {
      if (targetObj) {
        setLM({ ...lm, subModules: subModules });
      } else {
        setLM({ ...lm, subModules: lm.subModules });
      }
    },
    [lm, setLM, subModules],
  );

  const onDragStart_TASKS = useCallback(
    (obj: Task) => {
      setSubModules([...lm.subModules]);
    },
    [lm, setSubModules],
  );

  const onDraggingOverObj_TASKS = useCallback(
    (t: Task | SubModule) => {
      if (currentDraggedTask && currentDraggedTask.id != t.id) {
        if (isSubModule(t)) {
          if (t.tasks && t.tasks.length == 0) {
            const tmp: SubModule[] = [];
            for (const sm of subModules) {
              if (sm.id == t.id) {
                tmp.push({ ...sm, tasks: [currentDraggedTask] });
              } else {
                const tmpTask: Task[] = [];
                for (const t of sm.tasks) {
                  if (t.id != currentDraggedTask.id) {
                    tmpTask.push(t);
                  }
                }
                tmp.push({ ...sm, tasks: tmpTask });
              }
            }
            setSubModules(tmp);
          }
        } else if (isTask(t)) {
          setSubModules(
            rearrangeTasks(subModules, currentDraggedTask.id, t.id),
          );
        }
      }
    },
    [subModules, currentDraggedTask, setSubModules],
  );

  const rearrangeTasks = (
    list: SubModule[],
    movingId: string,
    hoverId: string,
  ): SubModule[] => {
    let fromSubModuleId = '';
    let toSubModuleId = '';
    let isBefore = true;
    let fromTasks: Task[] = [];
    let toTasks: Task[] = [];
    let mt: Task | undefined = undefined;

    for (const sm of list) {
      for (const t of sm.tasks) {
        if (t.id == movingId) {
          mt = t;
          fromTasks = sm.tasks;
          fromSubModuleId = sm.id;
          if (toSubModuleId) {
            isBefore = false;
          }
        } else if (t.id == hoverId) {
          toTasks = sm.tasks;
          toSubModuleId = sm.id;
        }
        if (fromSubModuleId && toSubModuleId) {
          break;
        }
      }
      if (fromSubModuleId && toSubModuleId) {
        break;
      }
    }

    let tmp: SubModule[] = [];

    if (mt) {
      const tmpFromtasks: Task[] = [];
      const tmpTotasks: Task[] = [];
      for (const t of fromTasks) {
        if (t.id != movingId) {
          if (t.id == hoverId) {
            if (isBefore) {
              tmpFromtasks.push(t);
              tmpFromtasks.push(mt);
            } else {
              tmpFromtasks.push(mt);
              tmpFromtasks.push(t);
            }
          } else {
            tmpFromtasks.push(t);
          }
        }
      }
      if (fromSubModuleId != toSubModuleId) {
        for (const t of toTasks) {
          if (t.id == hoverId) {
            if (isBefore) {
              tmpTotasks.push(t);
              tmpTotasks.push(mt);
            } else {
              tmpTotasks.push(mt);
              tmpTotasks.push(t);
            }
          } else {
            tmpTotasks.push(t);
          }
        }
      }

      for (const sm of list) {
        if (sm.id == fromSubModuleId) {
          tmp.push({ ...sm, tasks: tmpFromtasks });
        } else if (sm.id == toSubModuleId && sm.id != fromSubModuleId) {
          tmp.push({ ...sm, tasks: tmpTotasks });
        } else {
          tmp.push(sm);
        }
      }
    } else {
      tmp = list;
    }

    return tmp;
  };

  const {
    dragPanelRef: dragPanelRef_TASKS,
    onMouseDown: onMouseDown_TASKS,
    onMouseEnter: onMouseEnter_TASKS,
    onMouseLeave: onMouseLeave_TASKS,
  } = useDragAndDrop<Task, Task | SubModule>(
    setIsDNDActive_TASKS,
    onDrop_TASKS,
    onDragStart_TASKS,
    onDraggingOverObj_TASKS,
  );

  /****************************************/
  /*************** RENDER *****************/
  /****************************************/

  return (
    <div
      className={cn('border bg-white rounded-lg w-[100%] p-3', {
        'select-none': isDNDActive_SUB || isDNDActive_TASKS,
        'cursor-grabbing': isDNDActive_SUB || isDNDActive_TASKS,
      })}
    >
      <div className="flex items-start">
        <div className="flex-1">
          <div className="text-base font-semibold mb-2">
            {isCompetition ? 'Bots for the competition' : 'Submodules'}
          </div>
          <div className="text-sm text-muted-foreground mb-4">
            {isCompetition
              ? 'These are the bots that reps will have to call in order to participate in the competition.'
              : 'These are the submodules that Reps will have to complete in order to pass the training.'}
          </div>
          {!isCompetition && (
            <div className="mb-4">
              <Checkbox
                checked={lm.lockProgress}
                onToggle={() => {
                  setLM({ ...lm, lockProgress: !lm.lockProgress });
                }}
                className="text-sm"
              >
                Lock the progression
              </Checkbox>
              <div className="text-xs text-muted-foreground ml-6 mt-2">
                If this is enabled, Reps will have to complete submodules in
                order
              </div>
            </div>
          )}
        </div>
        <div>
          {lm.subModules.length > 0 && !isCompetition && (
            <Button variant={'outline'} onClick={addSubmodule}>
              <Plus size={16} className="mr-2" />
              Create new
            </Button>
          )}
        </div>
      </div>

      {subModules.map((s: SubModule) => {
        return (
          <Submodule
            key={s.id}
            sm={s}
            setSM={updateSubmodule}
            onDelete={deleteSubmodule}
            generateTaskId={generateTaskId}
            isCompetition={isCompetition}
            dnd={{
              onMouseDown: onMouseDown_SUB,
              onMouseEnter: onMouseEnter_SUB,
              onMouseLeave: onMouseLeave_SUB,
              onMouseEnterForTasks: onMouseEnter_TASKS,
              onMouseLeaveForTasks: onMouseLeave_TASKS,
              setCurrentDraggedObject: setCurrentDraggedSubModule,
              draggedObject: currentDraggedSubModule,
              isActive: isDNDActive_SUB,
            }}
            dndTasks={{
              onMouseDown: onMouseDown_TASKS,
              onMouseEnter: onMouseEnter_TASKS,
              onMouseLeave: onMouseLeave_TASKS,
              setCurrentDraggedObject: setCurrentDraggedTask,
              draggedObject: currentDraggedTask,
              isActive: isDNDActive_TASKS,
            }}
          />
        );
      })}

      {!isCompetition && (
        <div className="bg-gray-50 rounded-lg border border-dashed border-gray-200 flex flex-col items-center justify-center py-10">
          <div className="text-sm font-medium">Create a new submodule</div>
          <div className="text-sm text-muted-foreground">
            Create a new submodule for reps to complete
          </div>
          <div className="mt-4">
            <Button onClick={addSubmodule}>
              <Plus size={16} className="mr-2" />
              Create new
            </Button>
          </div>
        </div>
      )}

      <div
        ref={dragPanelRef_SUB}
        className="absolute p-2 rounded-lg border bg-gray-100/70"
        style={{ display: 'none' }}
      >
        {currentDraggedSubModule?.name
          ? currentDraggedSubModule?.name
          : 'Submodule'}
      </div>
      <div
        ref={dragPanelRef_TASKS}
        className="absolute p-2 rounded-lg border bg-gray-100/70"
        style={{ display: 'none' }}
      >
        {currentDraggedTask &&
          currentDraggedTask.info &&
          currentDraggedTask?.type ==
            LearningModuleTaskType.AI_BUYER_ROLEPLAY && (
            <div className="border rounded-full p-0 flex items-center ml-2">
              <AgentAvatar
                className="w-[26px] h-[26px] border-white border-2"
                agent={currentDraggedTask.info?.agent}
              />

              <div className="capitalize mx-1 font-medium mr-2">
                {currentDraggedTask.info.agent?.firstName || ''}{' '}
                {currentDraggedTask.info.agent?.lastName || ''}
              </div>
            </div>
          )}
      </div>
    </div>
  );
}
