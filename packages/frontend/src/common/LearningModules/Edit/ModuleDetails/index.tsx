import LearningModule, {
  LearningModuleStatus,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import { useCallback, useEffect, useState } from 'react';
import { <PERSON>geCheck, Check, Info } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import TeamsAndRepsFilter from '@/common/Analytics/DashboardTab/Filters/TeamsAndReps';
import { UserDto } from '@/lib/User/types';
import DatePicker from '@/common/Analytics/DashboardTab/Filters/DatePicker';
import EditBackground, { BG_COLORS } from './EditBackground';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import RepsFilter from '@/common/Analytics/DashboardTab/Filters/RepsFilter';

interface IProps {
  lm: LearningModule;
  setLM: (l: LearningModule) => void;
}

export default function ModuleDetails({ lm, setLM }: IProps) {
  const isCompetition = lm.isCompetition;

  const [spashStyle, setSplashStyle] = useState<any>({
    backgroundColor: BG_COLORS[Math.floor(Math.random() * BG_COLORS.length)],
  });

  const [dueDatePerAssignee, setDueDatePerAssignee] = useState<{
    [userId: number]: Date;
  }>({});
  const [editDueDatePerAssignee, setEditDueDatePerAssignee] =
    useState<boolean>(false);

  useEffect(() => {
    if (lm) {
      if (lm.splashImageInfo != '') {
        const info = JSON.parse(lm.splashImageInfo);
        if (info.type == 'color') {
          setSplashStyle({
            backgroundColor: info.color,
          });
        } else {
          setSplashStyle({
            backgroundImage: `url('${info.url}')`,
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            backgroundSize: 'cover',
            imageRendering: '-webkit-optimize-contrast',
          });
        }
      }

      if (lm.assigneesDueDate) {
        if (lm.assigneesDueDate.length > 0) {
          setEditDueDatePerAssignee(true);
        }
        const tmp: { [userId: number]: Date } = {};
        for (const add of lm.assigneesDueDate) {
          let dd = add.dueDate;
          if (!dd) {
            dd = new Date();
          }
          tmp[add.userId] = dd;
        }
        setDueDatePerAssignee(tmp);
      }
    }
  }, [lm]);

  /*************************************/
  /************  ACTIONS ***************/
  /*************************************/

  const updateDueDatePerAssignee = useCallback(
    (userId: number, dueDate: Date) => {
      if (!lm.assigneesDueDate) {
        lm.assigneesDueDate = [];
      }

      const tmp = [];
      let found = false;
      let maxDate = dueDate;
      for (const add of lm.assigneesDueDate) {
        if (add.userId == userId) {
          found = true;
          add.dueDate = dueDate;
        }
        if (add.dueDate > maxDate) {
          maxDate = add.dueDate;
        }
        tmp.push(add);
      }
      if (!found) {
        tmp.push({
          userId: userId,
          dueDate: dueDate,
        });
      }

      setLM({ ...lm, assigneesDueDate: tmp, dueDate: maxDate });
    },
    [lm],
  );

  /****************************************/
  /************  BACKGROUND ***************/
  /****************************************/

  const [openEditBackground, setOpenEditBackground] = useState<boolean>(false);

  const startEditBG = async () => {
    setOpenEditBackground(true);
  };

  const updateBackground = (i: any) => {
    setLM({
      ...lm,
      splashImageInfo: JSON.stringify(i),
    } as LearningModule);

    if (i.type === 'image') {
      setSplashStyle({
        backgroundImage: `url('${i.url}')`,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        backgroundSize: 'cover',
      });
    } else {
      setSplashStyle({
        backgroundColor: i.color,
      });
    }
    setOpenEditBackground(false);
  };

  return (
    <div className="border bg-white rounded-lg w-[55%] p-3">
      {!isCompetition && (
        <>
          <div className="text-sm font-medium mb-3">Upload cover</div>
          <div
            className={cn(`rounded-lg p-3  h-[140px] mb-6 flex flex-col`)}
            style={spashStyle}
          >
            <div className="flex items-center">
              <div
                className="border bg-black/20 rounded-lg text-white px-2 py-1 hover:bg-black/10 cursor-pointer"
                onClick={startEditBG}
              >
                Edit background
              </div>
              <div className="flex-1" />
              {lm.isCertification && (
                <div className="bg-black/20 rounded-full text-white px-2 py-1 font-medium flex items-center">
                  <BadgeCheck size={18} className={cn('mr-1 ')} />
                  Certificate
                </div>
              )}
            </div>
            <div className="flex-1"></div>
          </div>
        </>
      )}

      <div className="text-sm font-medium mb-1">Enter name</div>
      <div>
        <Input
          value={lm.name}
          onChange={(e) => {
            setLM({ ...lm, name: e.target.value });
          }}
        />
      </div>

      <div className="text-sm font-medium mb-1 mt-6">Enter description</div>
      <div>
        <Textarea
          value={lm.description}
          onChange={(e) => {
            setLM({
              ...lm,
              description: e.target.value,
            });
          }}
        />
      </div>

      <div className="text-sm font-medium mb-1 mt-6">
        Add team &amp; assignees
      </div>
      <div>
        <TeamsAndRepsFilter
          placeholder="Select assignees"
          onUpdate={(s: UserDto[]) => {
            setLM({ ...lm, assignees: s });
          }}
          current={lm.assignees}
        />
      </div>

      {!isCompetition && (
        <>
          {lm.status === LearningModuleStatus.IN_PREPARATION && (
            <>
              <div className="text-sm font-medium mb-1 mt-6">
                Select start date
              </div>
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="startDateOption"
                    className="mr-2"
                    checked={!lm.startDate}
                    onChange={() => {
                      const { startDate, ...rest } = lm;
                      setLM(rest as LearningModule);
                    }}
                  />
                  <span className="text-sm">On publish</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    name="startDateOption"
                    className="mr-2"
                    checked={!!lm.startDate}
                    onChange={() => {
                      setLM({ ...lm, startDate: new Date() });
                    }}
                  />
                  <span className="text-sm">On a specific date</span>
                </label>
              </div>

              {!lm.startDate ? null : (
                <div className="mt-2">
                  <DatePicker
                    current={lm.startDate}
                    onUpdate={(d: Date) => {
                      setLM({ ...lm, startDate: d });
                    }}
                    minDate={new Date()}
                  />
                </div>
              )}
            </>
          )}

          <div className="text-sm font-medium mb-1 mt-6">Select due date</div>
          {!editDueDatePerAssignee && (
            <>
              <div>
                <DatePicker
                  current={lm.dueDate}
                  onUpdate={(d: Date) => {
                    setLM({ ...lm, dueDate: d });
                  }}
                  minDate={new Date()}
                />
              </div>
              {lm.assignees.length > 1 && (
                <div className="flex justify-end">
                  <div
                    className="text-xs text-muted-foreground underline mt-1 cursor-pointer hover:text-black"
                    onClick={() => {
                      setEditDueDatePerAssignee(true);
                    }}
                  >
                    Adjust due date per assignee
                  </div>
                </div>
              )}
            </>
          )}
        </>
      )}

      {isCompetition && (
        <div className="flex items-center mt-6">
          <div className="flex-1">
            <div className="text-sm font-medium">Start date</div>
            <div className="mt-1">
              <DatePicker
                current={lm.startDate || new Date()}
                onUpdate={(d: Date) => {
                  setLM({ ...lm, startDate: d });
                }}
                minDate={new Date()}
              />
            </div>
          </div>

          <div className="flex-1 ml-2">
            <div className="text-sm font-medium">End date</div>
            <div className="mt-1">
              <DatePicker
                current={lm.dueDate}
                onUpdate={(d: Date) => {
                  setLM({ ...lm, dueDate: d });
                }}
                minDate={new Date()}
              />
            </div>
          </div>
        </div>
      )}

      {editDueDatePerAssignee && (
        <div>
          <div className="mt-2">
            {lm.assignees.map((u) => {
              let dd = dueDatePerAssignee[u.id];
              if (dd) {
                if (typeof dd == 'string') {
                  dd = new Date(dd);
                }
              }
              return (
                <div key={u.id} className="flex items-center mb-1">
                  <div className="flex items-center">
                    <Avatar className="w-[24px] h-[24px] border-white border-2">
                      <AvatarImage src={u?.avatar} />
                      <AvatarFallback className="text-sm">
                        {u?.firstName?.charAt(0) || ''}
                        {u?.lastName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      {u.firstName} {u.lastName}
                    </div>
                  </div>
                  <div className="flex-1" />
                  <div className="w-[110px]">
                    <DatePicker
                      current={dd}
                      onUpdate={(d: Date) => {
                        updateDueDatePerAssignee(u.id, d);
                      }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {!isCompetition && (
        <div className="mt-6 flex items-start">
          <div
            className={cn(
              'w-[22px] h-[22px] rounded border-2 border-gray-200 flex items-center justify-center cursor-pointer hover:border-gray-300',
              {
                'bg-blue-500': lm.isCertification,
              },
            )}
            onClick={() => {
              setLM({ ...lm, isCertification: !lm.isCertification });
            }}
          >
            {lm.isCertification && <Check className="text-white" size={12} />}
          </div>
          <div className="flex-1 ml-2">
            <div className="flex items-center">
              <div className="text-sm font-medium">Mark as certificate</div>
              <div>
                <BadgeCheck size={18} className="ml-1 text-blue-700" />
              </div>
            </div>
            <div className="text-xs text-muted-foreground flex mt-2 items-center">
              This will indicate that this module is a certificate
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info size={16} className="ml-1" />
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p className="text-center">
                      A certificate is a recognition of completing a training
                      course, helping <br></br>salespeople improve their skills
                      and demonstrate their expertise.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      )}

      {!isCompetition && (
        <div>
          <div className="text-sm font-medium mb-1 mt-6">
            On completion notify
          </div>
          <div>
            <RepsFilter
              current={lm.notifyUsersOnCompletion || []}
              onRepsUpdated={(s: string[]) => {
                console.log(s);
                setLM({
                  ...lm,
                  notifyUsersOnCompletion: s.map((uid: string) => {
                    return parseInt(uid);
                  }),
                });
              }}
            />
          </div>
        </div>
      )}

      {openEditBackground && (
        <EditBackground
          open={openEditBackground}
          onCancel={() => {
            setOpenEditBackground(false);
          }}
          onSave={updateBackground}
        />
      )}
    </div>
  );
}
