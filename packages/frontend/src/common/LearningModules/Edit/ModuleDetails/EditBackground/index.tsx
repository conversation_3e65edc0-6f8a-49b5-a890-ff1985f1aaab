import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import UnsplashPickPhotoPanel from '@/components/UnsplashPickPhoto/UnsplashPickPhotoPanel';
import LearningModuleService from '@/lib/LearningModule';
import { cn } from '@/lib/utils';
import { Brush, Image, Loader2, Upload } from 'lucide-react';
import { useCallback, useState } from 'react';
import { HexColorPicker } from 'react-colorful';
import { useDropzone } from 'react-dropzone';
import { toast } from 'react-toastify';


interface IProps {
  lmId: string;
  open: boolean;
  onCancel: () => void;
  onSave: (i: any) => void;
}

export const BG_COLORS = [
  '#3CC5C4',
  '#2C836E',
  '#CB5A99',
  '#CD7524',
  '#4263CC',
  '#845EC5',
];

export default function EditBackground({ lmId, open, onCancel, onSave }: IProps) {
  const [tab, setTab] = useState<string>('photo');
  const [canSave, setCanSave] = useState<boolean>(false);
  const [color, setColor] = useState(BG_COLORS[0]);
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [background, setBackground] = useState<any>({
    type: 'image',
    url: '',
    blur_hash: '',
  });

  const selectImage = (imgUrl: string, blur_hash: string) => {
    setBackground({
      type: 'image',
      url: imgUrl,
      blur_hash: blur_hash,
    });
    setCanSave(true);
  };

  const pickColor = (c: string) => {
    setColor(c);
    setBackground({
      type: 'color',
      color: c,
    });
    setCanSave(true);
  };

  const loadFile = useCallback(async (files: File[]) => {
    let image: string = '';

    if (files) {
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > 1024 * 1024 * 5) {
          //2MB
          toast.error('File size must be less than 5MB');
          return;
        }
        image = URL.createObjectURL(files[i]);
      }
    }

    if (files[0]) {
      setFile(files[0]);
      setBackground({
        type: 'custom_image',
        file: files[0],
      });
      setCanSave(true);
    }
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: loadFile,
  });

  const uploadImage = async () => {
    setIsLoading(true);

    const res = await LearningModuleService.uploadSplashImage(file!);
    if (!res) {
      toast.error('Failed to upload splash image.');
      setIsLoading(false);
      return;
    }

    setIsLoading(false);
    onSave(res);
  }

  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="close-btn max-w-[1000px] overflow-hidden overflow-x-hidden p-0">
        <DialogHeader>
          <DialogTitle className="flex items-center p-4">
            Edit background
          </DialogTitle>
        </DialogHeader>
        <div className="h-[50vh]">
          <Tabs defaultValue={'photo'} value={tab}>
            <TabsList className="mx-3">
              <TabsTrigger
                value="photo"
                onClick={() => {
                  setTab('photo');
                }}
              >
                <Image size={16} className="mr-2" />
                Picture
              </TabsTrigger>
              <TabsTrigger
                value="color"
                onClick={() => {
                  setTab('color');
                }}
              >
                <Brush size={16} className="mr-2" />
                Color
              </TabsTrigger>
            </TabsList>
            <TabsContent value="photo">
              {!file && (<div className="overflow-auto block h-[44vh]">
                <div className="mx-3" {...getRootProps()}>
                  <input {...getInputProps()} />
                  <div className="flex items-center justify-center p-6 border border-dashed border-gray-300 bg-gray-100/50 rounded-lg text-sm text-muted-foreground cursor-pointer">
                    <Upload size={16} className="mr-2" />
                    Upload new
                  </div>
                </div>
                <UnsplashPickPhotoPanel
                  onSave={selectImage}
                  selectedUrl={background.url}
                />
              </div>)}
              {file && (
                <div className="mx-3">
                  <img
                    src={URL.createObjectURL(file)}
                    alt="Preview"
                    className="h-[210px] object-contain rounded-lg border-2 border-teal-400"
                  />
                </div>
              )}
            </TabsContent>
            <TabsContent value="color">
              <div className="h-[44vh] flex items-center">
                <div className="flex-1 ">
                  <div className="grid grid-cols-5 p-4 gap-4 ml-2">
                    {BG_COLORS.map((c: string) => {
                      let isSelected = false;

                      if (background) {
                        if (background.type == 'color') {
                          if (background.color == c) {
                            isSelected = true;
                          }
                        }
                      }
                      return (
                        <div
                          key={c}
                          className={cn(
                            'w-[60px] h-[60px] cursor-pointer rounded-lg border-2 border-white',
                            {
                              'border-2 border-black': isSelected,
                            },
                          )}
                          style={{ backgroundColor: c }}
                          onClick={() => pickColor(c)}
                        ></div>
                      );
                    })}
                  </div>
                  <div className="p-4">
                    <div className="w-full text-muted-foreground">Preview</div>
                    <div
                      className="h-[80px] w-full rounded-lg"
                      style={{ backgroundColor: color }}
                    ></div>
                  </div>
                </div>

                <div className="flex-1">
                  <HexColorPicker
                    color={color}
                    onChange={pickColor}
                    style={{ width: '400px', height: '300px' }}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        <DialogFooter className="px-3 pb-3">
          <Button variant={'outline'} onClick={onCancel}>
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (background.type === 'custom_image') {
                uploadImage();
              } else {
                onSave(background);
              }
            }}
            disabled={!canSave || isLoading}
          >
            {isLoading ? <div className="flex items-center"><Loader2 className="animate-spin mr-2" /> Uploading...</div> : 'Save'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
