import PageHeader from '@/components/PageHeader';
import { But<PERSON> } from '@/components/ui/button';
import { useLearningModule } from '@/hooks/useLearningModules';
import useRouting from '@/hooks/useRouting';
import LearningModule, {
  LearningModuleStatus,
  SubModule,
} from '@/lib/LearningModule/types';
import LinksManager from '@/lib/linksManager';
import { useEffect, useRef, useState } from 'react';
import ModuleDetails from './ModuleDetails';
import Submodules from './SubModules';
import LearningModuleService from '@/lib/LearningModule';
import { useQueryClient } from '@tanstack/react-query';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Archive,
  Copy,
  Loader2Icon,
  MoreVerticalIcon,
  SaveIcon,
  Send,
} from 'lucide-react';
import { BG_COLORS } from './ModuleDetails/EditBackground';
import useUserSession from '@/hooks/useUserSession';
import PublishConfirmationModal from './publishConfirmationModal';
import { Id, toast, ToastContainer } from 'react-toastify';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { generateShortId } from '@/lib/utils';

interface IProps {
  id?: string;
  isCompetition?: boolean; //its true only when creating a new competition
}

export default function EditLearningModule({ id, isCompetition }: IProps) {
  const errorToastId = useRef<Id | null>(null);

  const { isAdmin } = useUserSession();

  const queryClient = useQueryClient();

  const randomColor = (): string => {
    return BG_COLORS[Math.floor(Math.random() * BG_COLORS.length)];
  };

  const loadLearningModule = id ? true : false;

  const learningModuleId = id || '';

  const { goToPage, appendToUrl } = useRouting();

  const { data: lmDB, isLoading: isLoadingLearningModule } = useLearningModule(
    learningModuleId,
    isAdmin,
    loadLearningModule,
  );

  const [isNew, setIsNew] = useState<boolean>(true);
  const [lm, setLM] = useState<LearningModule>({
    id: '',
    orgId: 0,
    createdAt: new Date(),
    createdBy: 0,
    name: '',
    description: '',
    dueDate: new Date(),
    status: LearningModuleStatus.IN_PREPARATION,
    isCertification: false,
    isCompetition: isCompetition || false,
    archived: false,
    subModules: [],
    assignees: [],
    lockProgress: false,
    splashImageInfo: JSON.stringify({
      type: 'color',
      color: randomColor(),
    }),
    progress: 0,
  } as LearningModule);

  useEffect(() => {
    if (!isLoadingLearningModule) {
      if (lmDB) {
        // console.log(lmDB);
        setIsNew(false);
        setLM(lmDB);
      } else {
        const submodules = [];
        let startDate = undefined;
        if (isCompetition) {
          startDate = new Date();
          let idSM = generateShortId();
          let isUnique = true;

          do {
            isUnique = true;
            lm.subModules.map((s: SubModule) => {
              if (s.id == idSM) {
                isUnique = false;
              }
            });
            if (!isUnique) {
              idSM = generateShortId();
            }
          } while (!isUnique);

          //competitions have only 1 submodule hidden from the users
          submodules.push({
            id: idSM,
            name: '',
            description: '',
            progress: 0,
            tasks: [],
            assigneesStats: [],
          });
        }
        setIsNew(true);
        setLM({
          id: '',
          orgId: 0,
          createdAt: new Date(),
          createdBy: 0,
          name: '',
          description: '',
          dueDate: new Date(),
          startDate: startDate,
          status: LearningModuleStatus.IN_PREPARATION,
          isCertification: false,
          isCompetition: isCompetition || false,
          archived: false,
          subModules: submodules,
          assignees: [],
          lockProgress: false,
          splashImageInfo: JSON.stringify({
            type: 'color',
            color: randomColor(),
          }),
          progress: 0,
        } as LearningModule);
      }
    }
  }, [isLoadingLearningModule]);

  const update = (l: LearningModule) => {
    setLM(l);
    startAutosave(l);
  };

  /****************************************/
  /***************  SAVE ******************/
  /****************************************/

  ////// AUTOSAVE
  const [isAutosavingPending, setIsAutosavingPending] =
    useState<boolean>(false);

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const startAutosave = (l: LearningModule) => {
    setIsAutosavingPending(true);
    clearAutosave();

    const delay = 2000;

    timeoutRef.current = setTimeout(async () => {
      forceSave(l);
    }, delay);
  };

  const clearAutosave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  //----------- END AUTOSAVE

  const [isSaving, setIsSaving] = useState<boolean>(false);

  const forceSave = (updatedLearningModule: LearningModule) => {
    clearAutosave();
    doSave(updatedLearningModule);
  };

  const save = async () => {
    doSave(lm);
  };

  const doSave = async (tmpLM: LearningModule, updatingArchive = false) => {
    if (!tmpLM.archived || updatingArchive) {
      setIsAutosavingPending(false);
      setIsSaving(true);

      let nlm: LearningModule | undefined = undefined;
      try {
        nlm = await LearningModuleService.upsert(tmpLM);
      } catch (e) {
        console.log(e);
      }

      if (nlm) {
        if (isNew) {
          setLM(nlm);
          setIsNew(false);
          // goToPage(LinksManager.learningModulesEdit(nlm.id));
          appendToUrl(nlm.id);
        }
      }

      await queryClient.invalidateQueries({ queryKey: ['learning-modules'] });
      await queryClient.invalidateQueries({ queryKey: ['learning-module'] });
      setIsSaving(false);
    }
  };

  const [showPublishModal, setShowPublishModal] = useState<boolean>(false);

  const publish = () => {
    if (verifyLearningModuleBeforePublish()) {
      setShowPublishModal(true);
    }
  };

  const verifyLearningModuleBeforePublish = (): boolean => {
    let errorMessage =
      'Some fields are missing or invalid. Please check the form and try again.\n';
    let error = false;

    if (lm.name == '') {
      errorMessage += '\n  Name is required';
      error = true;
    }
    if (lm.assignees.length == 0) {
      errorMessage += '\n  Assignees are required';
      error = true;
    }
    if (lm.subModules.length == 0) {
      errorMessage += '\n  At least one submodule is required';
      error = true;
    } else {
      let submoduleError = false;
      let taskError = false;
      lm.subModules.forEach((sm) => {
        if (sm.name == '' && !submoduleError && !lm.isCompetition) {
          errorMessage += '\n  Submodule name is required';
          submoduleError = true;
        }
        if (sm.tasks.length == 0 && !taskError) {
          errorMessage += '\n  At least one task is required in each submodule';
          taskError = true;
        }
      });
      if (submoduleError || taskError) {
        error = true;
      }
    }
    if (error && !toast.isActive(errorToastId.current as Id)) {
      errorToastId.current = toast.error(errorMessage);
    }

    return !error;
  };

  const doPublish = async () => {
    setLM({
      ...lm,
      status: LearningModuleStatus.IN_PROGRESS,
      startDate: lm.startDate ?? new Date(),
    });
    await doSave({
      ...lm,
      status: LearningModuleStatus.IN_PROGRESS,
      startDate: lm.startDate ?? new Date(),
    });
  };

  const closePublishConfModalAndSave = async (publish: boolean) => {
    setShowPublishModal(false);
    if (publish) {
      await doPublish();
      goToPage(LinksManager.learningModules(lm.id));
    } else {
      doSave({ ...lm });
    }
  };

  const pause = () => {
    setLM({ ...lm, status: LearningModuleStatus.IN_PREPARATION });
    doSave({ ...lm, status: LearningModuleStatus.IN_PREPARATION });
  };

  const archive = async () => {
    setLM({ ...lm, archived: true });
    await doSave({ ...lm, archived: true }, true);
    history.go(-2);

  };

  const clone = async () => {
    setIsSaving(true);

    let nlm: LearningModule | undefined = undefined;
    try {
      nlm = await LearningModuleService.clone(lm.id);
    } catch (e) {
      console.log(e);
    }

    await queryClient.invalidateQueries({ queryKey: ['learning-modules'] });
    await queryClient.invalidateQueries({ queryKey: ['learning-module'] });

    if (nlm) {
      goToPage(LinksManager.learningModulesEdit(nlm.id));
    }

    setIsSaving(false);
  };

  /****************************************/
  /*************** RENDER *****************/
  /****************************************/

  // let isLoading = true; //isLoadingLearningModule

  if (isLoadingLearningModule) {
    return (
      <div className="bg-[#FBFBFB] px-6 py-4 h-[100vh] flex flex-col ">
        <div>
          <Skeleton className="w-full h-20" />
        </div>
        <div className="flex items-stretch pt-3 flex-1">
          <div className="w-[35%] mr-6">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>

          <div className="flex-1">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>
        </div>
      </div>
    );
  }

  /*
            {/* <Button variant={"secondary"} className="mr-2" onClick={() => { goToPage(LinksManager.learningModules()) }} disabled={isSaving}>Cancel</Button>
            {
              lm.status == LearningModuleStatus.IN_PREPARATION && !lm.archived && (
                <>
                  <Button variant={"secondary"} onClick={clone} disabled={isSaving} className="mr-2"><Copy size={16} className="mr-2" />Clone</Button>
                  <Button variant={"outline"} onClick={save} disabled={isSaving} className="mr-2"><Plus size={16} className="mr-2" />Save draft</Button>
                  <Button variant={"outline"} onClick={archive} disabled={isSaving} className="mr-2"><Archive size={16} className="mr-2" />Archive</Button>
                  <Button onClick={publish} disabled={isSaving}><Send size={16} className="mr-2" />Publish</Button>
                </>
              )
            }
            {
              lm.status != LearningModuleStatus.IN_PREPARATION && !lm.archived && (
                <>
                  <Button variant={"secondary"} onClick={clone} disabled={isSaving} className="mr-2"><Copy size={16} className="mr-2" />Clone</Button>
                  <Button variant={"outline"} onClick={pause} disabled={isSaving} className="mr-2"><Pause size={16} className="mr-2" />Pause</Button>
                  <Button variant={"outline"} onClick={archive} disabled={isSaving} className="mr-2"><Archive size={16} className="mr-2" />Archive</Button>
                  <Button onClick={save} disabled={isSaving}><Plus size={16} className="mr-2" />Save</Button>
                </>
              )
            }

  */
  return (
    <div className="bg-[#FBFBFB] px-6 py-4 min-h-[100vh]">
      <PageHeader
        title={[
          {
            title: lm.isCompetition ? 'Competitions' : 'Learning Modules',
            href: LinksManager.learningModules(),
          },
          { title: isNew ? 'Create new' : lm?.name || '' },
        ]}
        rightComponent={
          <div className="flex items-center">
            {(isSaving || isAutosavingPending) && (
              <div className="flex items-center mr-2 text-muted-foreground text-xs">
                Saving...{' '}
                <Loader2Icon className="animate-spin ml-2" size={16} />
              </div>
            )}
            {!lm.archived && (
              <>
                {lm.status == LearningModuleStatus.IN_PREPARATION && (
                  <>
                    <Button
                      variant={'outline'}
                      onClick={save}
                      disabled={isSaving || isAutosavingPending}
                      className="mr-2"
                    >
                      <SaveIcon size={16} className="mr-2" />
                      Save draft
                    </Button>
                    <Button
                      onClick={publish}
                      disabled={isSaving || isAutosavingPending}
                    >
                      <Send size={16} className="mr-2" />
                      Publish
                    </Button>
                  </>
                )}
                {lm.status != LearningModuleStatus.IN_PREPARATION && (
                  <>
                    {/* <Button variant={"outline"} onClick={pause} disabled={isSaving} className="mr-2"><Pause size={16} className="mr-2" />Pause</Button> */}
                    <Button onClick={save} disabled={isSaving}>
                      <SaveIcon size={16} className="mr-2" />
                      Save
                    </Button>
                  </>
                )}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div className="ml-2 hover:bg-muted py-2 px-1 rounded cursor-pointer">
                      <MoreVerticalIcon className="h-4 w-4" />
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={clone}
                      disabled={isSaving || isAutosavingPending}
                    >
                      <Copy size={16} className="mr-2" />
                      Clone
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={archive}
                      disabled={isSaving || isAutosavingPending}
                    >
                      <Archive size={16} className="mr-2" />
                      Archive
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        }
      />

      <div className="flex items-start mt-6 space-x-3">
        <ModuleDetails lm={lm} setLM={update} />

        <Submodules lm={lm} setLM={update} forceSave={forceSave} />
      </div>

      <PublishConfirmationModal
        modalOpen={showPublishModal}
        setModalOpen={setShowPublishModal}
        doSave={closePublishConfModalAndSave}
      />
      <ToastContainer />
    </div>
  );
}
