import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface IProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  doSave: (publish: boolean) => void;
}

export default function PublishConfirmationModal({
  modalOpen,
  setModalOpen,
  doSave,
}: IProps) {
  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn overflow-hidden overflow-x-scroll">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Publish Learning Module
          </DialogTitle>
          <DialogDescription>
            Once published, this module will be live and accessible to the
            entire organization. All assigned members will receive a
            notification via email, informing them about their new learning
            assignment.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4 flex flex-col">
          <Button
            className="mb-2"
            onClick={() => {
              doSave(true);
            }}
          >
            Publish
          </Button>
          <Button
            variant={'outline'}
            className="mb-2"
            onClick={() => {
              doSave(false);
            }}
          >
            Save as a draft
          </Button>
          <Button
            variant={'outline'}
            className="mb-2"
            onClick={() => {
              setModalOpen(false);
            }}
          >
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
