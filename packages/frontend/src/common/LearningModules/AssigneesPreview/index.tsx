import { UserDto } from '@/lib/User/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  assignees: UserDto[];
}

export default function AssigneesPreview({ assignees }: IProps) {
  const { isAdmin, firstName, lastName, avatar, userId } = useUserSession();

  if (isAdmin) {
    return (
      <div className="flex items-center border rounded-full px-1 py-[2px] h-[30px] pr-2">
        <div
          className="relative  h-[24px]"
          style={{
            width: `${14 + (assignees.length > 3 ? 3 * 16 : assignees.length * 16)}px`,
          }}
        >
          {assignees.map((u: UserDto, i: number) => {
            if (i == 2) {
              const remaining = assignees.length - 2;
              if (remaining > 0) {
                return (
                  <div
                    key={u.id}
                    className="absolute "
                    style={{ left: `${i * 16}px` }}
                  >
                    <div className="w-[24px] h-[24px] border-white border-2 rounded-full bg-gray-100 text-[10px] text-center ">
                      +{remaining}
                    </div>
                  </div>
                );
              }
            } else if (i < 2) {
              return (
                <div
                  key={u.id}
                  className="absolute "
                  style={{ left: `${i * 16}px` }}
                >
                  <Avatar className="w-[24px] h-[24px] border-white border-2">
                    <AvatarImage src={u?.avatar} />
                    <AvatarFallback className="text-sm">
                      {u?.firstName?.charAt(0) || ''}
                      {u?.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                </div>
              );
            } else {
              return;
            }
          })}
        </div>
        <div className="flex-1">
          {assignees.length + ` assignee${assignees.length == 1 ? '' : 's'}`}
        </div>
      </div>
    );
  } else {
    if (assignees.map((u) => u.id).indexOf(userId || 0) > -1) {
      return (
        <div className="flex items-center border rounded-full px-1 py-[2px] h-[30px] pr-2">
          <div className="h-[24px]">
            <div className="mr-1">
              <Avatar className="w-[24px] h-[24px] border-white border-2">
                <AvatarImage src={avatar} />
                <AvatarFallback className="text-sm">
                  {firstName?.charAt(0) || ''}
                  {lastName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
            </div>
          </div>
          <div className="flex-1">{`${firstName} ${lastName}`}</div>
        </div>
      );
    }
  }
}
