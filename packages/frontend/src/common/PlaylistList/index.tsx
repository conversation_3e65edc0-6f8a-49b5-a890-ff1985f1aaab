import { Ava<PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import useOrgCurrentUser from '@/hooks/useOrgCurrentUser';
import usePlaylists from '@/hooks/usePlaylists';
import PlaylistService from '@/lib/Playlist';
import { PlaylistDto } from '@/lib/Playlist/types';
import { cn } from '@/lib/utils';
import { useAuthInfo } from '@propelauth/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Edit2Icon,
  LayoutListIcon,
  Loader2Icon,
  MoreVerticalIcon,
  PlayCircleIcon,
  PlusIcon,
  Trash2Icon,
  UsersIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import { CreatePlaylistModal } from '../CreatePlaylist/CreatePlaylistModal';
import Link from 'next/link';
import LinksManager from '@/lib/linksManager';
import dayjs from 'dayjs';
import useOrg from '@/hooks/useOrg';

interface IPlaylistListProps {
  repId?: number;
}

function PlaylistList({ repId }: IPlaylistListProps) {
  const { data: curUser } = useOrgCurrentUser();
  const { data: playlists, isLoading } = usePlaylists();
  const { data: org } = useOrg();
  const router = useRouter();
  const [modalOpen, setModalOpen] = useState(false);
  const authInfo = useAuthInfo();
  const [activePlaylistId, setActivePlaylistId] = useState<number>(0);
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);

  const deletePlaylistByIdMutation = useMutation({
    mutationFn: PlaylistService.deletePlaylistById,
    onSuccess: async (playlist, params) => {
      // Analytics.track(AssignmentEvents.CREATED_SUCCESS, {
      //   ...params,
      // });
      queryClient.invalidateQueries({ queryKey: ['playlists'] });
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving your playlist. Please try again.',
        );
      }
      console.log('ERROR saving playlist', error);
      // Analytics.track(AssignmentEvents.CREATED_ERROR, {
      //   ...params,
      //   err: "Error creating assignment",
      // });
    },
  });

  const renderPlaylist = (playlist: PlaylistDto, i: number) => {
    return (
      <Link href={LinksManager.trainingCalls(`?pl=${playlist.id}`)}>
        <Button
          key={i}
          variant={'ghost'}
          className="flex flex-shrink-0 w-full text-left justify-start py-8 rounded-lg"
          // onClick={() => {
          //   router.push(`/buyers/${assignment?.agent?.vapiId}`);
          // }}
        >
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size={'sm'}
                className="w-8 h-8 rounded-full p-0"
              >
                <span className="sr-only">Open menu</span>
                <MoreVerticalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="center">
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setActivePlaylistId(playlist.id as number);
                  setModalOpen(true);
                  // router.push(`/buyers/${agent.vapiId}/edit/main`);
                }}
              >
                <Edit2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Edit</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <LayoutListIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>View calls</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();

                  deletePlaylistByIdMutation.mutate({
                    id: playlist.id,
                  });
                }}
              >
                <Trash2Icon className="w-4 h-4 mr-2 text-red-400" />
                <span>Delete playlist</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Avatar className="h-10 w-10 rounded-lg">
            <AvatarFallback className="text-muted-foreground rounded-lg">
              <PlayCircleIcon />
            </AvatarFallback>
          </Avatar>
          <div className="ml-4 space-y-1">
            <div className="flex">
              <p className="text-sm font-medium leading-none">
                {playlist.name}
              </p>
              {playlist.shared && (
                <UsersIcon className="w-4 h-4 ml-2 text-muted-foreground" />
              )}
            </div>
            <p className={cn('text-xs text-muted-foreground')}>
              {playlist.calls?.length} call
              {playlist.calls?.length !== 1 ? 's' : ''}
              {playlist.shared ? ' · Shared' : ''}
              {playlist.shared
                ? ` · Created by ${
                    playlist?.ownerId === curUser?.id
                      ? 'me'
                      : `${playlist.owner?.firstName} ${
                          playlist?.owner?.lastName?.charAt(0) || ''
                        }`
                  }`
                : ''}
            </p>
          </div>
        </Button>
      </Link>
    );
  };

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <div>
      <Card className="w-[450px]">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle>Playlists</CardTitle>
              <CardDescription className="mt-1 w-80">
                Organize your recorded calls into playlists. Shared playlists
                are accessible &amp; editable by anyone in your org.
              </CardDescription>
            </div>
            <Button
              onClick={() => {
                setActivePlaylistId(0);
                setModalOpen(true);
              }}
              className="flex items-center w-7 h-7 justify-center"
              variant="outline"
              size={'icon'}
              disabled={isPilotEnded}
            >
              <PlusIcon className="w-5 h-5 text-muted-foreground" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {(playlists || [])?.length > 0 ? (
            playlists?.map(renderPlaylist)
          ) : (
            <div className="flex flex-col space-y-4 items-center justify-center h-[200px] border rounded-lg border-dotted">
              {isLoading ? (
                <Loader2Icon className="animate-spin" />
              ) : (
                <p className="text-muted-foreground">No playlists yet</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      <CreatePlaylistModal
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        onSubmit={() => {
          setModalOpen(false);
        }}
        defaultPlaylistId={activePlaylistId}
        defaultPlaylistValues={
          activePlaylistId
            ? playlists?.find((p) => p.id === activePlaylistId)
            : undefined
        }
      />
    </div>
  );
}

export default PlaylistList;
