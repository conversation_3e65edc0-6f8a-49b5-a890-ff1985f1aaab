import { useState, useRef } from 'react';
import { Id, toast } from 'react-toastify';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2Icon } from 'lucide-react';
import AnalyticsService from '@/lib/Analytics';
import { useQueryClient } from '@tanstack/react-query';
import useUserSession from '@/hooks/useUserSession';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AppPermissions } from '@/lib/permissions';

interface INewDashboardProps {
  onClose: (id: number) => void;
}

export default function NewDashboard({ onClose }: INewDashboardProps) {
  const { org, canAccess } = useUserSession();
  const queryClient = useQueryClient();

  const errorToastId = useRef<Id | null>(null);

  const [name, setName] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  let accessibilityOptions = [
    'Shared with ' + org?.orgName,
    'Personal',
    'Admins only',
  ];
  let defaultAccess = 'Shared with ' + org?.orgName;
  if (!canAccess(AppPermissions.CREATE_PUBLIC_DASHBOARDS)) {
    accessibilityOptions = ['Personal'];
    defaultAccess = 'Personal';
  }
  const [accessibility, setAccessibility] = useState<string>(defaultAccess);
  const [isPersonal, setIsPersonal] = useState<boolean>(
    !canAccess(AppPermissions.CREATE_PUBLIC_DASHBOARDS),
  );
  const [isForAdminOnly, setIsForAdminOnly] = useState<boolean>(false);

  const changeAccessiblity = (value: string) => {
    setAccessibility(value);
    if (value == 'Personal') {
      setIsPersonal(true);
      setIsForAdminOnly(false);
    } else {
      setIsPersonal(false);
      if (value == 'Admins only') {
        setIsForAdminOnly(true);
      } else {
        setIsForAdminOnly(false);
      }
    }
  };

  const save = async () => {
    setIsLoading(true);
    let go = false;
    let idNewDashboard = 0;
    try {
      const newDashboard = await AnalyticsService.addDashboardTab(
        name,
        isPersonal,
        isForAdminOnly,
      );
      idNewDashboard = newDashboard.id;
      go = true;
    } catch (e) {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error. Please try again.',
        );
      }
    }

    if (go) {
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      onClose(idNewDashboard);
    }
    setIsLoading(false);
  };

  if (canAccess(AppPermissions.CREATE_PUBLIC_DASHBOARDS)) {
    return (
      <div className="w-[300px]">
        <div>
          <Label>Name</Label>
          <div className="mb-4">
            <Input
              value={name}
              onChange={(e) => {
                setName(e.target.value);
              }}
              className=""
              placeholder=""
            />
          </div>

          <>
            <Label>Accessibility</Label>
            <div>
              <Select value={accessibility} onValueChange={changeAccessiblity}>
                <SelectTrigger>
                  <SelectValue placeholder={'Select'} />
                </SelectTrigger>
                <SelectContent side="top">
                  {accessibilityOptions.map((ao) => (
                    <SelectItem key={ao.replace(' ', '-')} value={`${ao}`}>
                      {ao}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </>

          <div className="flex items-center justify-end mt-4">
            <Button
              onClick={save}
              variant={'default'}
              disabled={name == '' || isLoading}
            >
              {isLoading ? <Loader2Icon className="animate-spin" /> : 'Save'}
            </Button>
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div>
        <div className="">
          <Label>Name</Label>
          <div className="flex items-center">
            <div className="mr-2">
              <Input
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                }}
                className="w-56"
                placeholder=""
              />
            </div>
            <Button
              onClick={save}
              variant={'default'}
              disabled={name == '' || isLoading}
            >
              {isLoading ? <Loader2Icon className="animate-spin" /> : 'Save'}
            </Button>
          </div>
        </div>
      </div>
    );
  }
}
