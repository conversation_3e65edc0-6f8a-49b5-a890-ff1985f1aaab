import { useState } from 'react';
import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import { Lock } from 'lucide-react';
import useUserSession from '@/hooks/useUserSession';
import Image from 'next/image';
import PageHeader from '@/components/PageHeader';
import LightdashAnalytics from './LightdashAnalytics';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Insights from './Insights';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { cn } from '@/lib/utils';

export default function Analytics() {
  const [breadcrumbs] = useState<BreadcrumbItem[]>([{ title: 'Analytics' }]);
  const { blurSecondaryPages, isCompetitionOrg } = useUserSession();
  const router = useRouter();
  const params = useSearchParams();
  const pathname = usePathname();

  const initialTab = params?.get('tab') || 'analytics';
  const [currentTab, setCurrentTab] = useState<string>(initialTab);
  const [popoverActive, setPopoverActive] = useState(false);

  const onTabChange = (t: string) => {
    setCurrentTab(t);
    const newParams = new URLSearchParams(params);
    newParams.delete('tab');
    newParams.set('tab', t);
    router.replace(`${pathname}?${newParams.toString()}`);
  };

  const handlePopoverChange = (isOpen: boolean) => {
    setPopoverActive(isOpen);
  };

  if (blurSecondaryPages || isCompetitionOrg) {
    return (
      <div className="relative h-[100vh] block overflow-hidden bg-[#FBFBFB]">
        <div className="h-[100vh] overflow-hidden">
          <DashboardNavbar breadcrumbs={breadcrumbs} />
          <div className="w-full">
            <Image
              src={'/images/analytics.png'}
              width={1000}
              height={1000}
              className="mt-4 mx-3 h-full w-4/5"
              alt="coaching"
            />
          </div>
        </div>
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock the power of Analytics
            </div>
            <div className="text-base mt-2 text-muted-foreground max-w-[50%] text-center">
              Complete your onboarding to gain insights into your teams&apos;
              performance, including reps and more.
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn('bg-[#FBFBFB] h-[100vh] py-4 px-3.5', {
        'overflow-hidden': popoverActive,
        'overflow-auto': !popoverActive,
      })}
    >
      <div className="mx-3 mb-6">
        <PageHeader title="Analytics" />
      </div>

      <div className="pb-6">
        <Tabs defaultValue={currentTab} value={currentTab}>
          <div className="flex items-center mx-3">
            <TabsList>
              <TabsTrigger
                value="analytics"
                onClick={() => onTabChange('analytics')}
              >
                Analytics
              </TabsTrigger>
              <TabsTrigger
                value="insights"
                onClick={() => onTabChange('insights')}
              >
                Scorecard Insights
              </TabsTrigger>
            </TabsList>
          </div>

          {currentTab == 'analytics' && <LightdashAnalytics />}
          {currentTab == 'insights' && (
            <Insights onChatOpenChange={handlePopoverChange} />
          )}
        </Tabs>
      </div>
    </div>
  );
}
