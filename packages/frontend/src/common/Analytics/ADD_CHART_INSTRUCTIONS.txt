
STEPS: 

  1. add entry in the db, table: DashboardWidgetTemplate
  2. BE add function to fetch data in analytics/charts.service
  3. add logic to Widget/index.tsx (see switch) 
  4. add component to render (you can find a skeleton below)


Possible filters for a widget: 'all' or any combination of 'dates', 'reps', 'teams', 'buyers', 'tags', 'calltype', 'scorecards'

Possible variables in description: 

      {{ dates.short }} - eg "last month" or "10 days"
      {{ dates.fromdate }} - format MMM D, YYYY
      {{ dates.todate }} - format MMM D, YYYY

      {{ [reps | teams | buyers | tags | calltype | scorecards].count }} - counts how many have been selected in the filter


      {{ data.XXX }} to extract information from the data returned from the BE. 
        
            {{ data.list }} assumes that data is a simple array and concatenates all the values in a comma-separated string
        
            {{ data.[propName] }} if data contains an array of objects and for each object gets the data[propName] value
                                  if data is an object, gets the data[propName] value  


//------------------------------------------------
//---------- CHART COMPONENT SKELETON ------------
//------------------------------------------------


import { useEffect, useState } from "react";
import { AnalyticsFilterState, DashboardWidgetDto } from "@/lib/Analytics/types";
import { Loader2Icon } from "lucide-react";
import WidgetCard from "../WidgetCard";
import DefaultChartDropDownMenu from "../defaultChartDropDownMenu";
import useWidgetCard from "../WidgetCard/useWidgetCard";
import CHART_COLORS from "../colors";
import { LineChart } from "@tremor/react";

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?:boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function COMPONENT_NAME({ widget, isEditing, updateWidgetInfo, deleteWidget, hideFilters, overwriteFilters }: IGraphProps) {


  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu; //if you need to customize this, see below (CUSTOM WIDGET MENU EXAMPLE)

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(widget, updateWidgetInfo, overwriteFilters);
  const [currentData, setCurrentData] = useState<any>(data);

  useEffect(() => {
    if (!isLoading && data) {
      setCurrentData(data);
    }
  }, [isLoading, data]);


  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}>
      {
        (isLoading || !currentData) ? (
          <div className="w-full h-full flex items-center justify-center"><Loader2Icon size={40} className="animate-spin text-muted-foreground" /></div>
        ) : (
          //FOR NO DATA USE THIS:  <div className="w-full h-full flex items-center justify-center text-muted-foreground">No data</div>
          //DO WHATEVER YOU WANT
        )
      }
    </WidgetCard >
  )
}


//------------------------------------------------
// ------ CUSTOM WIDGET MENU EXAMPLE -------------
//------------------------------------------------


import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DashboardWidgetDto } from "@/lib/Analytics/types";
import { Settings, Trash2, MoreVerticalIcon, LineChartIcon, BarChart3Icon, Filter } from "lucide-react";


interface IChartDropDownMenuProps {
  widget: DashboardWidgetDto;
  startEditing: () => void;
  setAdditionalFeatures: (features: any) => void;
}


export default function ChartDropDownMenu({ widget, startEditing, setAdditionalFeatures }: IChartDropDownMenuProps) {

  const hasFilters = widget.customFilters && widget.customFilters != '';


  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <MoreVerticalIcon size={16} />
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <LineChartIcon className="w-4 h-4 mr-2 text-muted-foreground" />
            <span className="mr-2">Chart Type</span>
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent className="w-64">
              <DropdownMenuItem
                onClick={(e) => {
                  setAdditionalFeatures({ charType: "line" });
                }}
              >
                <LineChartIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Line</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  setAdditionalFeatures({ charType: "bar" });
                }}
              >
                <BarChart3Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Bar</span>
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>


        {
          hasFilters && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={startEditing}
            >
              <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
              <span>Filters</span>
            </DropdownMenuItem>
          )
        }

      </DropdownMenuContent>
    </DropdownMenu>
  )
}