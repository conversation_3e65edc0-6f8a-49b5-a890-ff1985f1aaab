import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { CloudDownload, Loader2Icon } from 'lucide-react';
import { useState, useRef } from 'react';
import AnalyticsService from '@/lib/Analytics';
import {
  AnalyticsFilterState,
  AnalyticsFilterType,
  DateFilterType,
} from '@/lib/Analytics/types';
import { Button } from '@/components/ui/button';
import Checkbox from '@/components/ui/Hyperbound/checkbox-with-label';
import DatesFilter from '../DashboardTab/Filters/DateFilter';

interface IProps {
  open: boolean;
  setModalOpen: (modalOpen: boolean) => void;
}

export default function ExportAnalyticsModal({ open, setModalOpen }: IProps) {
  const [downloading, setDownloading] = useState(false);
  const [includeGraphs, setIncludeGraphs] = useState<string | string[]>('all');
  const [filterState, setFilterState] = useState<AnalyticsFilterState>(
    new AnalyticsFilterState(),
  );

  const exportAsCsv = async () => {
    setDownloading(true);
    const data = await AnalyticsService.exportToCsv(includeGraphs, filterState);

    const blob = new Blob([data], {
      type: 'text/zip',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hyperbound-analytics.zip`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    setDownloading(false);
  };

  const charts = [
    {
      id: 'all',
      label: 'All',
    },
    {
      id: 'callsPerBot',
      label: 'Per buyer stats',
    },
    {
      id: 'callsPerTeam',
      label: 'Per team stats',
    },
    {
      id: 'callsPerRep',
      label: 'Per rep stats',
    },
  ];

  const isChartSelected = (id: string) => {
    if (includeGraphs === 'all') {
      return true;
    } else {
      return includeGraphs.includes(id);
    }
  };

  const toggleChart = (id: string) => {
    if (id === 'all') {
      if (Array.isArray(includeGraphs)) {
        setIncludeGraphs('all');
      } else {
        setIncludeGraphs([]);
      }
    } else {
      if (Array.isArray(includeGraphs)) {
        if (includeGraphs.includes(id)) {
          setIncludeGraphs(includeGraphs.filter((i) => i !== id));
        } else {
          setIncludeGraphs([...includeGraphs, id]);
        }
      } else {
        if (includeGraphs === 'all') {
          const tmp: string[] = [];
          charts.map((c) => {
            if (c.id !== 'all' && c.id !== id) {
              tmp.push(c.id);
            }
          });
          setIncludeGraphs([...tmp]);
        }
      }
    }
  };

  const updateDates = (n: DateFilterType) => {
    filterState[AnalyticsFilterType.DATE] = n;
    setFilterState({ ...filterState });
  };

  return (
    <Dialog open={open} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <CloudDownload className="mr-1" size={18} />
            Export Analytics
          </DialogTitle>
        </DialogHeader>
        <div className="mt-2">
          <div className="text-sm ml-1 mb-1 font-semibold">Select stats:</div>
          <div className="ml-4">
            {charts.map((c) => {
              return (
                <Checkbox
                  key={c.id}
                  checked={isChartSelected(c.id)}
                  onToggle={() => {
                    toggleChart(c.id);
                  }}
                  className="mt-2"
                >
                  {c.label}
                </Checkbox>
              );
            })}
          </div>
        </div>
        <div className="mt-1">
          <div className="mb-6">
            <div className="text-sm ml-1 mb-1 font-semibold">Date range:</div>
            <DatesFilter
              current={filterState[AnalyticsFilterType.DATE]}
              onDatesUpdated={updateDates}
            />
          </div>
        </div>
        <DialogFooter className="flex items-center">
          {downloading && (
            <div className="text-xs">
              Export started. It may take a few seconds.
            </div>
          )}
          <Button
            className="ml-2 flex items-center"
            variant={'default'}
            onClick={exportAsCsv}
            disabled={downloading}
          >
            {downloading ? (
              <Loader2Icon size={18} className="mr-2 animate-spin" />
            ) : (
              <CloudDownload size={18} className="mr-2" />
            )}
            Export
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
