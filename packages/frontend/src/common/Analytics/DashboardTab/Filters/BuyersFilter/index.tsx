import React, { useState, useEffect } from 'react';
import useOrgAgents from '@/hooks/useOrgAgents';
import useOrgAgentsById from '@/hooks/useOrgAgentsById';
import Filter, { FilterState, FilterItem } from '../Filter';
import { AgentDto } from '@/lib/Agent/types';
import { BotIcon } from 'lucide-react';
import AgentAvatar from '@/components/Avatars/Agent';

// Helper function to convert AgentDto to FilterItem
const agentToFilterItem = (agent: AgentDto): FilterItem => ({
  id: agent.id,
  firstName: agent.firstName,
  lastName: agent.lastName,
  avatar: agent.avatar,
  vapiId: agent.vapiId,
  [Symbol.iterator]: function* () {
    yield* Object.entries(this);
  },
});

interface IBuyersFilterProps {
  current: number[];
  onBuyersUpdated: (buyers: string[]) => void;
  isRadioSelect?: boolean;
  keepSelectionInPlace?: boolean;
  displaySelectedName?: boolean;
  className?: string;
  hideClearBtn?: boolean;
}

function BuyersFilter({
  current,
  onBuyersUpdated,
  isRadioSelect,
  keepSelectionInPlace,
  displaySelectedName,
  className,
  hideClearBtn,
}: IBuyersFilterProps) {
  if (!current) {
    current = [];
  }

  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(true, 10),
  );
  const [allBuyers, setAllBuyers] = useState<AgentDto[]>([]);
  const [selected, setSelected] = useState<AgentDto[]>([]);

  const { data: allBuyersDB, isLoading: isLoadingBuyers } = useOrgAgents(
    undefined,
    true,
    filterState.from,
    filterState.numberOfResults,
    filterState.search,
    undefined,
    true,
  );

  const {
    data: currentlySelectedBuyers,
    isLoading: isLoadingCurrentlySelectedBuyers,
  } = useOrgAgentsById(current);

  useEffect(() => {
    let hasMore = true;
    if (!isLoadingBuyers && allBuyersDB) {
      if (allBuyersDB.length < filterState.numberOfResults) {
        hasMore = false;
      }
      if (filterState.isSearching) {
        setAllBuyers([...allBuyersDB]);
      } else {
        setAllBuyers([...allBuyers, ...allBuyersDB]);
      }
      setFilterState({
        ...filterState,
        isLoadingItems: isLoadingBuyers,
        hasMore,
        isSearching: false,
      });
    }
  }, [isLoadingBuyers, allBuyersDB]);

  useEffect(() => {
    if (!isLoadingCurrentlySelectedBuyers && currentlySelectedBuyers) {
      setSelected(currentlySelectedBuyers);
    }
  }, [currentlySelectedBuyers, isLoadingCurrentlySelectedBuyers]);

  const onFilterUpdated = (ns: FilterItem[]) => {
    // Since we know these FilterItems were originally AgentDtos, this conversion is safe
    const agents = ns.map(
      (item) => allBuyers.find((agent) => agent.id === item.id)!,
    );
    setSelected(agents);

    if (onBuyersUpdated) {
      onBuyersUpdated(agents.map((a) => a.id.toString()));
    }
  };

  const printLabel = (item: FilterItem) => {
    // Find the original agent from our allBuyers array
    const agent = allBuyers.find((a) => a.id === item.id);
    if (!agent) return null;

    return (
      <div className="flex space-x-2 items-center">
        <AgentAvatar className="w-6 h-6" agent={agent} />
        <div className="capitalize">
          {agent.firstName || ''} {agent.lastName || ''}
        </div>
      </div>
    );
  };

  return (
    <Filter
      Icon={BotIcon}
      items={allBuyers.map(agentToFilterItem)}
      selected={selected.map(agentToFilterItem)}
      onStateUpdated={setFilterState}
      onFiltersUpdated={onFilterUpdated}
      printLabel={printLabel}
      filterState={filterState}
      filterName={isRadioSelect ? 'Buyer' : 'Buyers'}
      isRadioSelect={isRadioSelect}
      keepSelectionInPlace={keepSelectionInPlace}
      displaySelectedName={displaySelectedName}
      className={className}
      hideClearBtn={hideClearBtn}
    />
  );
}

export default React.memo(BuyersFilter);
