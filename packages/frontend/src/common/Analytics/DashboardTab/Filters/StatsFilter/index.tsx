import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  CheckIcon,
  ChevronDown,
  ChevronRight,
  ChevronsUpDown,
  Loader2Icon,
  Search,
  Square,
  SquareCheck,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { STATS_OPTIONS } from '@/lib/common-types';
import { useMemo, useState } from 'react';

export interface IProps {
  placeholder?: string;
  current: string[];
  onUpdate: (stats: string[]) => void;
  isRadioSelect?: boolean;
  statsInfos?: any;
}

export default function StatsFilter({
  placeholder,
  current,
  onUpdate,
  isRadioSelect,
  statsInfos,
}: IProps) {
  const [open, setOpen] = useState(false);

  /**************************************/
  /************** FE UTILS  *************/
  /**************************************/

  const [trigger, setTrigger] = useState<any>(null);
  const triggerBottom = useMemo(
    () => trigger?.offsetTop + trigger?.offsetHeight + 20,
    [trigger],
  );

  const toggle = (opt: string) => {
    const tmp: string[] = [];

    if (isRadioSelect) {
      if (current.indexOf(opt) < 0) {
        tmp.push(opt);
      }
    } else {
      let add = true;
      for (const o of current) {
        if (o == opt) {
          add = false;
        } else {
          tmp.push(o);
        }
      }
      if (add) {
        tmp.push(opt);
      }
    }

    if (onUpdate) {
      onUpdate(tmp);
    }
  };

  let addComma = false;
  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
      modal={true}
    >
      <PopoverTrigger asChild ref={setTrigger}>
        <div className="border border-gray-200 bg-white rounded-lg p-2 flex items-center cursor-pointer">
          <div className="flex-1  overflow-hidden">
            {current.length == 0 && (
              <div className="text-sm text-muted-foreground">
                {placeholder ? placeholder : 'Select stats'}
              </div>
            )}
            {current.length > 0 && (
              <div className="text-sm flex items-center text-nowrap">
                {STATS_OPTIONS.map((s: any, i: number) => {
                  const isChecked = current.indexOf(s.id) > -1;
                  if (isChecked) {
                    let comma = '';
                    if (addComma) {
                      comma = ', ';
                    } else {
                      addComma = true;
                    }
                    return (
                      <div key={s.id}>
                        {comma} {s.label}
                      </div>
                    );
                  }
                })}
              </div>
            )}
          </div>
          <div className="ml-2 mr-2">
            <ChevronsUpDown className="text-muted-foreground" size={16} />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="p-0"
        style={{
          maxHeight: `calc(100vh - ${triggerBottom || 0}px)`,
          width: 'var(--radix-popover-trigger-width)',
          overflow: 'auto',
        }}
      >
        <div className="p-1">
          {STATS_OPTIONS.map((s: any) => {
            const isChecked = current.indexOf(s.id) > -1;
            let statsInfodDetailsLabel = '';
            if (statsInfos) {
              if (statsInfos[s.id]) {
                let statsInfodDetails = statsInfos[s.id];
                if (statsInfodDetails) {
                  if (statsInfodDetails.talking) {
                    statsInfodDetails = statsInfodDetails.talking;
                  }
                  if (s.id == 'talkListenRatio') {
                    statsInfodDetailsLabel = `[${statsInfodDetails.min - 1}%,${statsInfodDetails.max - 1}%]`;
                  } else if (s.id == 'fillerWords') {
                    statsInfodDetailsLabel = `[${statsInfodDetails.min - 1},${statsInfodDetails.max - 1}] words per minute`;
                  } else if (s.id == 'talkSpeed') {
                    statsInfodDetailsLabel = `[${statsInfodDetails.min - 1},${statsInfodDetails.max - 1}] words per minute`;
                  } else if (s.id == 'longestMonologue') {
                    statsInfodDetailsLabel = `[${statsInfodDetails.min - 1},${statsInfodDetails.max - 1}] seconds`;
                  }
                }
              }
            }

            return (
              <div
                key={s.id}
                className="p-2 flex items-center cursor-pointer hover:bg-gray-50 rounded-lg"
                onClick={() => {
                  toggle(s.id);
                }}
              >
                <div
                  className={cn(
                    'mr-2 flex h-4 w-4 items-center justify-center  border border-primary',
                    isChecked
                      ? 'bg-primary text-primary-foreground'
                      : 'opacity-50 [&_svg]:invisible',
                    {
                      'rounded-full': isRadioSelect,
                      'rounded-sm': !isRadioSelect,
                    },
                  )}
                >
                  <CheckIcon className={cn('h-4 w-4')} />
                </div>
                <div className="flex-1 text-sm">
                  <div>{s.label}</div>
                  {statsInfodDetailsLabel && (
                    <div className="textxs text-muted-foreground">
                      Recommended Range: {statsInfodDetailsLabel}
                    </div>
                  )}
                </div>
              </div>

              // <div key={s.id} className="pb-3">
              //   <Checkbox checked={isChecked} onToggle={() => { toggle(s.id) }} className="text-sm">
              //     {s.label}
              //   </Checkbox>
              // </div>
            );
          })}
        </div>
      </PopoverContent>
    </Popover>
  );
}
