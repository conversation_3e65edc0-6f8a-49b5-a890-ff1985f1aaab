import { useState } from 'react';
import Filter, { FilterState } from '../Filter';
import useUserSession from '@/hooks/useUserSession';
import { RepsCanEditScoreResults } from '@/lib/Organization/types';
import { ListChecks } from 'lucide-react';

interface IProps {
  current: string[];
  onFilterUpdated: (status: string[]) => void;
  locked?: boolean;
}

export default function ScorecardCriterionsStatusFilter({
  current,
  onFilterUpdated,
  locked,
}: IProps) {
  const { dbOrg } = useUserSession();

  if (!current) {
    current = [];
  }

  const transformIntoObject = (s: string[]): any[] => {
    const tmp: any[] = [];
    for (const sec of s) {
      if (sec == 'passed') {
        tmp.push({ id: sec, name: 'Passed' });
      } else if (sec == 'failed') {
        tmp.push({ id: sec, name: 'Failed' });
      } else if (sec == 'toggled') {
        if (
          dbOrg?.repsCanEditScoreResults == RepsCanEditScoreResults.DISPUTE_ONLY
        ) {
          tmp.push({ id: sec, name: 'Dispute accepted' });
        } else {
          tmp.push({ id: sec, name: 'Score overwritten by rep' });
        }
      } else {
        tmp.push({ id: sec, name: 'Score disputed by Rep' });
      }
    }

    return tmp;
  };

  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(false, 10, false),
  );

  const _temp = ['passed', 'failed'];

  if (dbOrg?.repsCanEditScoreResults == RepsCanEditScoreResults.YES) {
    _temp.push('toggled');
  } else if (
    dbOrg?.repsCanEditScoreResults == RepsCanEditScoreResults.DISPUTE_ONLY
  ) {
    _temp.push('toggled');
    _temp.push('disputed');
  }

  const [allStatus, setAllStatus] = useState<any[]>(transformIntoObject(_temp));
  const [selected, setSelected] = useState<any[]>(transformIntoObject(current));

  /**************************************/
  /************** TOGGLE ****************/
  /**************************************/

  const _onFilterUpdated = (ns: any[]) => {
    setSelected(ns);

    if (onFilterUpdated) {
      onFilterUpdated(ns.map((a) => a.id.toString()));
    }
  };

  const printLabel = (sec: any) => {
    return <div className="flex space-x-2 items-center">{sec.name}</div>;
  };

  return (
    <Filter
      Icon={ListChecks}
      items={allStatus}
      selected={selected}
      onStateUpdated={setFilterState}
      onFiltersUpdated={_onFilterUpdated}
      printLabel={printLabel}
      filterState={filterState}
      useIdAsString={true}
      filterName={'Criterions Status'}
      hideSearch={true}
      locked={locked}
      lockedMessage={'Book a demo to access scorecards criterions'}
    />
  );
}
