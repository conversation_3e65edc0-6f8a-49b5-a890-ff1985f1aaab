import React, { useState, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContentWithoutPortal as PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, Loader2Icon, FilterX, TagsIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import { TagDto } from '@/lib/Agent/types';
import useTags from '@/hooks/useTags';

interface ITagsFilterProps {
  current: number[];
  onFiltersUpdated: (tagsIds: number[]) => void;
}

function TagsFilter({ current, onFiltersUpdated }: ITagsFilterProps) {
  if (!current) {
    current = [];
  }

  const defaultNumberOfResults = 10;

  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );
  const [searchString, setSearchString] = useState<string>('');

  const [open, setOpen] = useState(false);

  const { data: allTags, isLoading: isLoadingTags } = useTags(
    true,
    0,
    numberOfResults,
    searchString,
  );
  const tags = allTags || [];

  let noMoreTags = false;
  const currentlySelectedTags: TagDto[] = [];
  if (allTags) {
    if (allTags.length > 0) {
      if (allTags.length < numberOfResults) {
        noMoreTags = true;
      }
      allTags.map((t) => {
        if (current.indexOf(t.id) > -1) {
          currentlySelectedTags.push(t);
        }
      });
    }
  }

  const [selected, setSelected] = useState<TagDto[]>(currentlySelectedTags);
  const [prevSelected, setPrevSelected] = useState<TagDto[]>(
    currentlySelectedTags,
  );

  if (
    currentlySelectedTags.length != prevSelected.length ||
    JSON.stringify(currentlySelectedTags) != JSON.stringify(prevSelected)
  ) {
    setPrevSelected(currentlySelectedTags);
    setSelected(currentlySelectedTags);
  }

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout>>();
  const resetAgentsForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetAgentsForSearch.current = true;
      setNumberOfResults(defaultNumberOfResults);
    }, 200);

    setSearchString(s);
  };

  const clearAll = () => {
    setSelected([]);
    if (onFiltersUpdated) {
      onFiltersUpdated([]);
    }
    setOpen(false);
  };

  const toggleTag = (_id: string) => {
    const id = parseInt(_id);

    let newSelection: TagDto[] = [];

    if (selected.find((val) => val.id === id)) {
      newSelection = selected.filter((val) => val.id !== id) as TagDto[];
    } else {
      let selectedTagDto: TagDto | null = null;
      tags.map((a) => {
        if (a.id == id) {
          selectedTagDto = a;
          return;
        }
      });

      if (selectedTagDto) {
        newSelection = [...selected, selectedTagDto] as TagDto[];
      }
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(newSelection.map((a) => a.id));
    }
    setSelected([...newSelection] as TagDto[]);
  };

  const loadMore = () => {
    setNumberOfResults(numberOfResults + 15);
  };

  return (
    <>
      <Popover
        open={open}
        onOpenChange={(o) => {
          setOpen(o);
        }}
      >
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="border w-full"
            style={{ justifyContent: 'start' }}
          >
            <TagsIcon className="mr-2 h-4 w-4" />
            Tags
            {selected.length > 0 && (
              <>
                <Separator orientation="vertical" className="mx-2 h-4" />
                <div className="space-x-1 lg:flex">
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1 font-normal"
                  >
                    {selected.length} selected
                  </Badge>
                </div>
              </>
            )}
            <div className="flex-1"></div>
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          className="p-0 max-h-[50vh] overflow-y-auto"
        >
          <Command>
            <CommandInput
              placeholder="Search tags..."
              className="h-9"
              value={searchString}
              onValueChange={filterResults}
            />
            <CommandList>
              <CommandGroup heading="Tags">
                {tags.map((v) => (
                  <CommandItem
                    key={v.id}
                    value={String(v.id)}
                    onSelect={toggleTag}
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        selected?.find((val) => val.id === v.id)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    <div className="flex space-x-2 items-center">
                      <div className="capitalize">{v.name}</div>
                    </div>
                  </CommandItem>
                ))}

                {isLoadingTags ? (
                  <CommandItem className="justify-center text-center">
                    <Loader2Icon className="animate-spin" />
                  </CommandItem>
                ) : (
                  <>
                    {tags.length > 0 ? (
                      <>
                        {!noMoreTags && (
                          <CommandItem
                            onSelect={loadMore}
                            className="justify-center text-center"
                          >
                            More...
                          </CommandItem>
                        )}
                      </>
                    ) : (
                      <CommandItem className="justify-center text-center">
                        No tag found
                      </CommandItem>
                    )}
                  </>
                )}
              </CommandGroup>
            </CommandList>
            {selected?.length > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={clearAll}
                    className="justify-center text-center"
                  >
                    <FilterX size="14" />
                    &nbsp;Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}

export default React.memo(TagsFilter);
