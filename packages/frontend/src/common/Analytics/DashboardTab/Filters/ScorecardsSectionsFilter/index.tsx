import useScorecardsSections from '@/hooks/useScorecardsSections';
import { useEffect, useState, useRef } from 'react';
import Filter, { FilterState } from '../Filter';
import { PilcrowIcon } from 'lucide-react';

interface IScorecardsSectionsFilterProps {
  current: string[];
  onSectionsUpdated: (sections: string[]) => void;
  scorecards: number[];
  locked?: boolean;
}

export default function ScorecardsSectionsFilter({
  current,
  onSectionsUpdated,
  scorecards,
  locked,
}: IScorecardsSectionsFilterProps) {
  if (!scorecards) {
    scorecards = [];
  }

  if (!current) {
    current = [];
  }

  const transformIntoObject = (s: string[]): any[] => {
    const tmp: any[] = [];
    for (const sec of s) {
      tmp.push({ id: sec, name: sec });
    }

    return tmp;
  };

  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(true, 10),
  );
  const [allSections, setAllSections] = useState<any[]>([]);
  const [selected, setSelected] = useState<any[]>(transformIntoObject(current));

  const prevScorecards = useRef<string>('');

  const { data: sectionsDB, isLoading } = useScorecardsSections(
    true,
    scorecards,
    filterState.from,
    filterState.numberOfResults,
    filterState.search,
  );

  useEffect(() => {
    let hasMore = true;
    if (!isLoading && sectionsDB) {
      if (sectionsDB.length < filterState.numberOfResults) {
        hasMore = false;
      }

      let newScorecards = false;

      if (prevScorecards.current != JSON.stringify(scorecards)) {
        newScorecards = true;
        prevScorecards.current = JSON.stringify(scorecards);
      }

      if (filterState.isSearching || newScorecards) {
        setAllSections([...transformIntoObject(sectionsDB)]);
      } else {
        setAllSections([...allSections, ...transformIntoObject(sectionsDB)]);
      }
    }
    setFilterState({
      ...filterState,
      isLoadingItems: isLoading,
      hasMore,
      isSearching: false,
    });
  }, [sectionsDB, isLoading]);

  const onFilterUpdated = (ns: any[]) => {
    setSelected(ns);

    if (onSectionsUpdated) {
      onSectionsUpdated(ns.map((a) => a.id.toString()));
    }
  };

  const printLabel = (sec: any) => {
    return <div className="flex space-x-2 items-center">{sec.name}</div>;
  };

  return (
    <Filter
      Icon={PilcrowIcon}
      items={allSections}
      selected={selected}
      onStateUpdated={setFilterState}
      onFiltersUpdated={onFilterUpdated}
      printLabel={printLabel}
      filterState={filterState}
      useIdAsString={true}
      filterName={'Sections'}
      locked={locked}
      lockedMessage={'Book a demo to access scorecards sections'}
    />
  );
}
