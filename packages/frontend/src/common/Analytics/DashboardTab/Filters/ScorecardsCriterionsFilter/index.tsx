import useScorecardsCriterions from '@/hooks/useScorecardsCriterions';
import React, { useEffect, useState, useRef } from 'react';
import Filter, { FilterState } from '../Filter';
import { Ruler } from 'lucide-react';

interface IScorecardsCriterionsFilterProps {
  current: string[];
  onCriterionsUpdated: (criterions: string[]) => void;
  sections: string[];
  locked?: boolean;
}

function ScorecardsCriterionsFilter({
  current,
  onCriterionsUpdated,
  sections,
  locked,
}: IScorecardsCriterionsFilterProps) {
  if (!current) {
    current = [];
  }

  const transformIntoObject = (s: string[]): any[] => {
    const tmp: any[] = [];
    for (const sec of s) {
      tmp.push({ id: sec, name: sec });
    }

    return tmp;
  };
  const prevSections = useRef<string>('');
  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(true, 10),
  );
  const [allCriterions, setAllCriterions] = useState<any[]>([]);
  const [selected, setSelected] = useState<any[]>(transformIntoObject(current));

  const { data: criterionsDB, isLoading } = useScorecardsCriterions(
    true,
    sections,
    filterState.from,
    filterState.numberOfResults,
    filterState.search,
  );

  useEffect(() => {
    let hasMore = true;
    if (!isLoading && criterionsDB) {
      if (criterionsDB.length < filterState.numberOfResults) {
        hasMore = false;
      }

      let newSections = false;

      if (prevSections.current != JSON.stringify(sections)) {
        newSections = true;
        prevSections.current = JSON.stringify(sections);
      }

      if (filterState.isSearching || newSections) {
        setAllCriterions([...transformIntoObject(criterionsDB)]);
      } else {
        setAllCriterions([
          ...allCriterions,
          ...transformIntoObject(criterionsDB),
        ]);
      }
    }
    setFilterState({
      ...filterState,
      isLoadingItems: isLoading,
      hasMore,
      isSearching: false,
    });
  }, [criterionsDB, isLoading]);

  const onFilterUpdated = (ns: any[]) => {
    setSelected(ns);

    if (onCriterionsUpdated) {
      onCriterionsUpdated(ns.map((a) => a.id.toString()));
    }
  };

  const printLabel = (sec: any) => {
    return <div className="flex space-x-2 items-center">{sec.name}</div>;
  };

  return (
    <Filter
      Icon={Ruler}
      items={allCriterions}
      selected={selected}
      onStateUpdated={setFilterState}
      onFiltersUpdated={onFilterUpdated}
      printLabel={printLabel}
      filterState={filterState}
      useIdAsString={true}
      filterName={'Criterions'}
      locked={locked}
      lockedMessage={'Book a demo to access scorecards criterions'}
    />
  );
}

export default React.memo(ScorecardsCriterionsFilter);
