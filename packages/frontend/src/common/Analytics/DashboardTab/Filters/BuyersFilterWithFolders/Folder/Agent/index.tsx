import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AgentDto } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { CheckIcon, BrainIcon } from 'lucide-react';
import { AGENT_EMOTIONAL_STATE_OPTIONS, CALL_TYPE_LABELS } from '@/common/CreateBuyerForm/constants';
import AgentAvatar from '@/components/Avatars/Agent';
import EmotionBadge from '@/common/CreateBuyerForm/BotPreview/EmotionBadge';

interface IProps {
  agent: AgentDto;
  selectedAgentsIds: number[];
  isRadioSelect?: boolean;
  canSelectMultiple?: boolean;
  onToggleAgent: (agentId: number) => void;
}

export default function Agent({
  agent,
  selectedAgentsIds,
  isRadioSelect,
  canSelectMultiple,
  onToggleAgent,
}: IProps) {
  const a: AgentDto = agent;
  const Icon = CALL_TYPE_TO_ICON?.[a?.callType as keyof typeof CALL_TYPE_TO_ICON]?.Icon;
  return (
    <div
      className="hover:bg-gray-50 p-1 rounded-lg cursor-pointer"
      onClick={() => {
        onToggleAgent(agent.id);
      }}
    >
      <div className="text-sm flex-1 flex items-center">
      {canSelectMultiple && (<div>
            <div
              className={cn(
                'mr-2 flex h-4 w-4 items-center justify-center  border border-primary',
                  selectedAgentsIds.find((aid) => aid === a.id)
                    ? 'bg-primary text-primary-foreground'
                    : 'opacity-50 [&_svg]:invisible',
                {
                  'rounded-full': isRadioSelect,
                  'rounded-sm': !isRadioSelect,
                },
              )}
            >
              <CheckIcon className={cn('h-4 w-4')} />
            </div>
          </div>
        )}
        <div className="mr-2">
          <AgentAvatar className="w-[32px] h-[32px]" agent={a} />
        </div>
        <div className="flex-1">
          <div>
            <div className="flex flex-row items-end">
              <div>
                {a.firstName} {a.lastName}
              </div>
              <div className="ml-2 flex items-center text-[0.62rem] text-muted-foreground">
                {Icon && <Icon size={10} className="mr-[3px]"/>}
                {CALL_TYPE_LABELS[a?.callType as keyof typeof CALL_TYPE_TO_ICON]}
              </div>
            </div>
            <div className="text-[0.76rem] text-muted-foreground">
              {a.jobTitle}{' '}
              <span style={{ whiteSpace: 'wrap' }}>
                {'@  ' + a.companyName}
              </span>
            </div>
            <div className="flex flex-row items-center">
              {a.emotionalState && (
                  <div className="inline-flex items-center text-nowrap text-green-600 text-[0.62rem]">
                    <EmotionBadge 
                      emotionalState={a.emotionalState} 
                      size="small" 
                      border={false}
                    />
                  </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
