import {
  AnalyticsFilterDateRange,
  AnalyticsFilterState,
  AnalyticsFilterType,
  DateFilterType,
  GlobalFiltesType,
} from '@/lib/Analytics/types';
import CallTypeFilter from '../Filters/CallTypeFilter';
import { AgentCallType } from '@/lib/Agent/types';
import DatesFilterShort from '../Filters/DateFilterShort';
import TeamsFilter from '../Filters/TeamsFilter';

interface IProps {
  type: GlobalFiltesType;
  current: AnalyticsFilterState;
  updateFilters: (f: AnalyticsFilterState) => void;
}

export default function GlobalFilters({
  type,
  current,
  updateFilters,
}: IProps) {
  if (type === GlobalFiltesType.RepsAnalytics) {
    return (
      <div className="flex items-center mx-6 mb-6">
        <div className="w-[300px] mr-2">
          <CallTypeFilter
            current={current[AnalyticsFilterType.CALL_TYPES]}
            onCallTypesUpdated={(callTypes: AgentCallType[]) => {
              updateFilters({
                ...current,
                [AnalyticsFilterType.CALL_TYPES]: callTypes,
              });
            }}
          />
        </div>

        <DatesFilterShort
          current={current[AnalyticsFilterType.DATE]}
          onDatesUpdated={(dates: DateFilterType) => {
            updateFilters({ ...current, [AnalyticsFilterType.DATE]: dates });
          }}
        />
      </div>
    );
  } else if (type === GlobalFiltesType.ManagersAnalytics) {
    return (
      <div className="flex items-center mx-6 mb-6">
        <div className="w-[300px] mr-2">
          <CallTypeFilter
            current={current[AnalyticsFilterType.CALL_TYPES]}
            onCallTypesUpdated={(callTypes: AgentCallType[]) => {
              updateFilters({
                ...current,
                [AnalyticsFilterType.CALL_TYPES]: callTypes,
              });
            }}
          />
        </div>

        <div className="mr-2 w-[200px]">
          <TeamsFilter
            current={current[AnalyticsFilterType.TEAMS]}
            onFiltersUpdated={(n: number[]) => {
              updateFilters({ ...current, [AnalyticsFilterType.TEAMS]: n });
            }}
          />
        </div>

        <DatesFilterShort
          current={current[AnalyticsFilterType.DATE]}
          onDatesUpdated={(dates: DateFilterType) => {
            updateFilters({ ...current, [AnalyticsFilterType.DATE]: dates });
          }}
        />
      </div>
    );
  }
}
