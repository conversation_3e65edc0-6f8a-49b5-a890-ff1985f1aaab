import { But<PERSON> } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Copy, ListFilter, Loader2Icon, Plus, Trash2 } from 'lucide-react';

interface IEditingCommandsProps {
  onAddChart: () => void;
  onAddFilter: () => void;
  onCloneTab?: () => void;
  onDeleteTab: () => void;
  isCloning: boolean;
  isDeleting: boolean;
  hideCloneBtn?: boolean;
}

export default function EditingCommands({
  onAddChart,
  onAddFilter,
  onCloneTab,
  onDeleteTab,
  isCloning,
  isDeleting,
  hideCloneBtn,
}: IEditingCommandsProps) {
  const disableAllButtons = isCloning || isDeleting;

  return (
    <>
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <span tabIndex={0}>
              <Button
                onClick={onAddChart}
                variant="outline"
                size="icon"
                className="mr-2"
                disabled={disableAllButtons}
              >
                <Plus size={17} />
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Add charts</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      {/* <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <span tabIndex={0}>
              <Button
                onClick={onAddFilter}
                variant="outline"
                size="icon"
                className="mr-2"
                disabled={disableAllButtons}
              >
                <ListFilter size={17} />
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Add filter</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider> */}
      {!hideCloneBtn && (
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild>
              <span tabIndex={0}>
                <Button
                  onClick={onCloneTab}
                  variant="outline"
                  size="icon"
                  className="mr-2"
                  disabled={disableAllButtons}
                >
                  {isCloning ? (
                    <Loader2Icon
                      size={17}
                      className="animate-spin text-muted-foreground"
                    />
                  ) : (
                    <Copy size={17} />
                  )}
                </Button>
              </span>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Clone tab</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <span tabIndex={0}>
              <Button
                onClick={onDeleteTab}
                variant="destructive"
                size="icon"
                title={'Delete dashboard...'}
                className="mr-5"
                disabled={disableAllButtons}
              >
                {isDeleting ? (
                  <Loader2Icon
                    size={17}
                    className="animate-spin text-muted-foreground"
                  />
                ) : (
                  <Trash2 size={17} />
                )}
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Delete tab</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
}
