import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/Analytics/types';
import useDashboardChartData from '@/hooks/useDashboardChartData';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import AnalyticsService from '@/lib/Analytics';
import useWidgetCard from '../WidgetCard/useWidgetCard';

interface IProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function ScorecardsVolume({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IProps) {
  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>();

  useEffect(() => {
    if (!isLoading && data) {
      setCurrentData(data);
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || data == undefined ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : !currentData ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          No data
        </div>
      ) : (
        <div className="w-full h-full flex items-center justify-between">
          <div className="flex flex-col justify-center items-center">
            <div className="flex items-baseline">
              <p className="font-bold text-2xl">
                {currentData.numbOfQuestions}
              </p>
            </div>
            <div className="font-semibold text-muted-foreground text-xs">
              Questions
            </div>
          </div>
          <div className="flex flex-col justify-center items-center">
            <div className="flex items-baseline">
              <p className="font-bold text-2xl">
                {currentData.numbOfObjections}
              </p>
            </div>
            <div className="font-semibold text-muted-foreground text-xs">
              Objections
            </div>
          </div>
          <div className="flex flex-col justify-center items-center">
            <div className="flex items-baseline">
              <p className="font-bold text-2xl">
                {currentData.percentOfQuestionsReported}%
              </p>
            </div>
            <div className="font-semibold text-muted-foreground text-xs">
              Disputed
            </div>
          </div>
        </div>
      )}
    </WidgetCard>
  );
}
