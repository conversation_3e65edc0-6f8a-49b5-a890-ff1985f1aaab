// import {
//     DropdownMenu,
//     DropdownMenuContent,
//     DropdownMenuItem,
//     DropdownMenuTrigger,
//   } from '@/components/ui/dropdown-menu';
//   import { DashboardWidgetDto } from '@/lib/Analytics/types';
//   import { Settings, MoreVerticalIcon, Filter, Download } from 'lucide-react';
// import useWidgetCard from '../WidgetCard/useWidgetCard';
// import { useState } from 'react';

//   interface IChartDropDownMenuProps {
//     widget: DashboardWidgetDto;
//     startEditing: () => void;
//   }

//   export default function CallsGroupedMenu({
//     widget,
//     startEditing,
//   }: IChartDropDownMenuProps) {
//     const hasFilters = widget.customFilters && widget.customFilters != '';

//       const { updateFiltersState, description, isLoading, data } = useWidgetCard(
//         widget,
//         (widget: DashboardWidgetDto) => {},
//       );
//       const [currentData, setCurrentData] = useState<any>(data);

//       const exportToCSVAll = () => {

//         let csv: string = '';
//         csv += `Layer, Full Name, Call Type, Tag, Total Calls, Avg Score, Max Score\n`;
//         currentData.forEach((i: any) => {
//           csv += `${i.displayLayer}, ${i.fullName},${i.callType},${i.tag},${i.callCount},${Math.trunc(100 * i.avgScore)}%, ${Math.trunc(100 * i.maxScore)}%\n`;
//         });

//         // const csvContent = rows.join('\n');
//         const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
//         const link = document.createElement('a');
//         if (link.download !== undefined) {
//           const url = URL.createObjectURL(blob);
//           link.setAttribute('href', url);
//           link.setAttribute('download', 'calls_summary_all.csv');
//           link.click();
//         }
//       };

//     return (
//       <DropdownMenu>
//         <DropdownMenuTrigger asChild>
//           <MoreVerticalIcon size={16} />
//         </DropdownMenuTrigger>

//         <DropdownMenuContent align='end'>
//           {
//             hasFilters && (
//               <DropdownMenuItem
//                 className="cursor-pointer"
//                 onClick={startEditing}
//               >
//                 <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
//                 <span>Filters</span>
//               </DropdownMenuItem>

//             )
//           }
//             <DropdownMenuItem
//                 className="cursor-pointer"
//                 onClick={exportToCSVAll}
//               >
//                 <Download className="w-4 h-4 mr-2 text-muted-foreground" />
//                 <span>Export to CSV</span>
//               </DropdownMenuItem>
//         </DropdownMenuContent>
//       </DropdownMenu>
//     )

//   }

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DashboardWidgetDto } from '@/lib/Analytics/types';
import { Filter, Download, MoreVerticalIcon } from 'lucide-react';
import { useState, useEffect } from 'react';

interface IChartDropDownMenuProps {
  widget: DashboardWidgetDto;
  startEditing: () => void;
  menuComponentData: any;
}

export default function CallsGroupedMenu({
  widget,
  startEditing,
  menuComponentData,
}: IChartDropDownMenuProps) {
  const hasFilters = widget.customFilters && widget.customFilters !== '';

  const [currentData, setCurrentData] = useState<any>(menuComponentData);

  // Update currentData when widget or data changes
  useEffect(() => {
    setCurrentData(menuComponentData);
  }, [menuComponentData, widget]);

  const exportToCSVAll = () => {
    console.log('current_data', currentData);
    let csv = `Layer, Full Name, Call Type, Tag, Total Calls, Avg Score, Max Score\n`;

    currentData.forEach((i: any) => {
      csv += `${i.displayLayer}, ${i.fullName}, ${i.callType}, ${i.tag}, ${i.callCount}, ${(100 * i.avgScore).toFixed(2)}%, ${(100 * i.maxScore).toFixed(2)}%\n`;
    });

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'calls_summary_all.csv');
      link.click();
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <MoreVerticalIcon size={16} />
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end">
        {hasFilters && (
          <DropdownMenuItem className="cursor-pointer" onClick={startEditing}>
            <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
            <span>Filters</span>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem className="cursor-pointer" onClick={exportToCSVAll}>
          <Download className="w-4 h-4 mr-2 text-muted-foreground" />
          <span>Export to CSV</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
