import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/Analytics/types';
import { ExternalLink, Loader2Icon, Phone } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import { cn } from '@/lib/utils';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import dayjs from 'dayjs';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AgentDto } from '@/lib/Agent/types';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import useRouting from '@/hooks/useRouting';
import AgentAvatar from '@/components/Avatars/Agent';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function CriterionImprPerRep({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [formattedData, setFormattedData] = useState<any[]>([]);
  const [openCriterion, setOpenCriterion] = useState<string>('');
  const router = useRouter();
  const { goToPage } = useRouting();

  useEffect(() => {
    if (!isLoading && data && data.length > 0) {
      setOpenCriterion(data[0].criterion);
      setFormattedData(data);
    }
  }, [isLoading, data]);

  if (isEditing) {
    return (
      <WidgetCard
        widget={widget}
        isEditing={isEditing}
        updateWidgetInfo={updateWidgetInfo}
        deleteWidget={deleteWidget}
        updateFiltersState={updateFiltersState}
        description={description}
      >
        {isLoading ? (
          <div className="w-full h-full flex items-center justify-center">
            <Loader2Icon
              size={40}
              className="animate-spin text-muted-foreground"
            />
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center font-bold text-2xl">
            &nbsp;
          </div>
        )}
      </WidgetCard>
    );
  } else {
    return (
      <div
        className={
          'h-full w-full py-4 rounded-xl text-card-foreground flex flex-col '
        }
      >
        <div className="font-semibold">Criterion Improvements</div>
        <div className="flex-1 mt-4">
          {isLoading ? (
            <div className="w-full h-full flex items-center justify-center">
              <Loader2Icon
                size={40}
                className="animate-spin text-muted-foreground"
              />
            </div>
          ) : formattedData.length === 0 ? (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              No data available
            </div>
          ) : (
            <div className=" h-full flex items-start text-xs">
              <div className="min-w-[20%]">
                {formattedData.map((info, i) => {
                  const Icon =
                    CALL_TYPE_TO_ICON[
                      info?.callType as keyof typeof CALL_TYPE_TO_ICON
                    ]?.Icon;

                  return (
                    <div
                      key={i}
                      className={cn(
                        'border bg-card rounded-lg mb-1 p-4 cursor-pointer hover:bg-gray-100',
                        {
                          'border-black': openCriterion === info.criterion,
                        },
                      )}
                      onClick={() => {
                        setOpenCriterion(info.criterion);
                      }}
                    >
                      <div className="font-semibold">{info.criterion}</div>
                      <div className="flex items-center mt-2">
                        <div className="text-xs text-muted-foreground">
                          {info.teamName ? info.teamName : 'Team'} average:{' '}
                          {info.teamAverage ? `${info.teamAverage}%` : 'N.A.'}
                        </div>
                        <div className="ml-4">
                          <div className="rounded-full flex items-center bg-gray-100 py-1 px-2 text-nowrap text-xs">
                            {Icon && <Icon className="mr-1" size={14} />}
                            {CALL_TYPE_OPTIONS.find(
                              (item) => item.value === info?.callType,
                            )?.label ||
                              (info.callType === 'focus'
                                ? 'Focus Call'
                                : info.callType)}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
              <div className="flex-1 border bg-card h-full rounded-lg ml-2 p-4">
                <div className="font-semibold">Review your calls</div>
                <div className="text-muted-foreground mt-2">
                  These calls highlight areas where you didn&apos;t meet your
                  goals. Use them as opportunities to reflect, identify what can
                  be improved, and sharpen your skills. Each call is a step
                  toward becoming more confident and successful.
                </div>

                <div className="border rounded-lg mt-4">
                  <table className="border-collapse w-full">
                    <tr className="border-b">
                      <td className="text-muted-foreground p-2">
                        Calls where you under-performed
                      </td>
                      <td className="text-muted-foreground p-2">&nbsp;</td>
                      <td className=" p-2">&nbsp;</td>
                    </tr>
                    {formattedData.map((info, i) => {
                      if (info.criterion !== openCriterion) return null;

                      return info.pastCalls.map((call: any, j: number) => {
                        const a = call.agent;
                        const sc = call.scorecard;
                        let score = '0';
                        if (sc && sc.scorecard) {
                          score = Number(
                            (call.scorecard?.passedScore /
                              call.scorecard?.totalScore) *
                              100 || 0,
                          ).toLocaleString('en-US', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          });
                        }
                        const date = dayjs(call.createdAt).format('MMM DD');
                        // console.log(call);
                        return (
                          <tr
                            key={'pc-' + j}
                            className="border-b hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              router.push(
                                LinksManager.trainingCalls(call.vapiId),
                              );
                            }}
                          >
                            <td className="flex items-center p-2">
                              <AgentAvatar className="w-6 h-6 mr-1" agent={a} />
                              {a?.firstName} {a?.lastName}
                            </td>
                            <td className="p-2">&nbsp;</td>
                            <td className="text-muted-foreground ">
                              <div className="flex items-center justify-end mr-4">
                                {date}
                                <ExternalLink size={16} className="ml-2" />
                              </div>
                            </td>
                          </tr>
                        );
                      });
                    })}
                  </table>
                </div>

                <div className="font-semibold mt-4">Practice suggestions</div>
                <div className="text-muted-foreground mt-2">
                  Here are the best bots to call to practice this criterion
                </div>

                <div className="border rounded-lg mt-4">
                  <table className="border-collapse w-full">
                    {formattedData.map((info, i) => {
                      if (info.criterion !== openCriterion) return null;

                      return info.suggestedAgents.map(
                        (agent: AgentDto, j: number) => {
                          const a = agent;
                          const Icon =
                            CALL_TYPE_TO_ICON?.[
                              a?.callType as keyof typeof CALL_TYPE_TO_ICON
                            ]?.Icon;
                          return (
                            <tr
                              key={'pc-' + j}
                              className="border-b hover:bg-gray-100 cursor-pointer"
                              onClick={() => {
                                goToPage(`/buyers/${a.vapiId}`);
                              }}
                            >
                              <td className="flex items-center p-2">
                                <AgentAvatar
                                  className="w-6 h-6 mr-1"
                                  agent={a}
                                />
                                {a?.firstName} {a?.lastName}
                              </td>
                              <td className="p-2 w-[80px]">
                                <div className="rounded-full flex items-center bg-gray-100 py-1 px-2 text-nowrap text-xs">
                                  {Icon && <Icon className="mr-1" size={14} />}
                                  {CALL_TYPE_OPTIONS.find(
                                    (item) => item.value === a?.callType,
                                  )?.label ||
                                    (a.callType === 'focus'
                                      ? 'Focus Call'
                                      : a.callType)}
                                </div>
                              </td>
                              <td className="text-muted-foreground ">
                                <div className="flex items-center justify-end mr-4 text-green-600">
                                  <Phone size={16} className="mr-2" /> Call
                                </div>
                              </td>
                            </tr>
                          );
                        },
                      );
                    })}
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
}
