import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/Analytics/types';
import useDashboardChartData from '@/hooks/useDashboardChartData';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import AnalyticsService from '@/lib/Analytics';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import { formatDuration } from '@/lib/utils';

interface IProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function TopUser({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IProps) {
  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>();
  useEffect(() => {
    if (!isLoading && data) {
      setCurrentData(data);
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || data == undefined ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : !currentData ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          No data
        </div>
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          <div>
            <div className="font-bold mt-2 text-xl">
              {currentData?.firstName} {currentData?.lastName}
            </div>
            <div className="">
              {currentData?.numbOfCalls} calls -{' '}
              {`${formatDuration(
                currentData?.talkTimeInMinutes * 60 * 1000,
              )} mins`}
            </div>
          </div>
        </div>
      )}
    </WidgetCard>
  );
}
