import { useEffect, useRef, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/Analytics/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import CHART_COLORS from '../colors';
import { BarChart } from '@tremor/react';
import useUserSession from '@/hooks/useUserSession';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function ProficiencyComparison({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { dbOrg } = useUserSession();

  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>(data);
  const [categories, setCategory] = useState<string[]>([]);

  useEffect(() => {
    if (!isLoading && data) {
      const allSections: any = {};
      const cat = [];

      for (const d of data) {
        if (!allSections[d.section_title]) {
          allSections[d.section_title] = [];
        }
        allSections[d.section_title].push(d);

        if (d.group_name == 'user') {
          if (cat.indexOf(d.object_name) < 0) {
            cat.unshift(d.object_name);
          }
        } else {
          const on = d.object_name == `null` ? dbOrg?.name : d.object_name;
          if (cat.indexOf(on) < 0) {
            cat.push(on);
          }
        }
      }

      setCategory(cat);

      const tmp = [];
      for (const s of Object.keys(allSections)) {
        const group = allSections[s];
        const entry: any = {
          name: s,
        };
        for (const d of group) {
          const on = d.object_name == `null` ? dbOrg?.name : d.object_name;
          entry[on] = Math.round(100 * d.section_score);
        }

        tmp.push(entry);
      }

      setCurrentData(tmp);
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : //FOR NO DATA USE THIS:  <div className="w-full h-full flex items-center justify-center text-muted-foreground">No data</div>
      //DO WHATEVER YOU WANT

      currentData.length == 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          No data
        </div>
      ) : (
        <div className="mt-8">
          <BarChart
            data={currentData}
            index="name"
            categories={categories}
            yAxisWidth={30}
            showLegend={false}
            valueFormatter={(number: number) => `${number}%`}
          />
        </div>
      )}
    </WidgetCard>
  );
}
