import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DashboardWidgetDto } from '@/lib/Analytics/types';
import {
  Settings,
  Trash2,
  MoreVerticalIcon,
  LineChartIcon,
  BarChart3Icon,
  Filter,
} from 'lucide-react';

interface IChartDropDownMenuProps {
  widget: DashboardWidgetDto;
  startEditing: () => void;
  setAdditionalFeatures: (features: any) => void;
}

export default function ChartDropDownMenu({
  widget,
  startEditing,
  setAdditionalFeatures,
}: IChartDropDownMenuProps) {
  const hasFilters = widget.customFilters && widget.customFilters != '';

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <MoreVerticalIcon size={16} />
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <LineChartIcon className="w-4 h-4 mr-2 text-muted-foreground" />
            <span className="mr-2">Chart Type</span>
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent className="w-64">
              <DropdownMenuItem
                onClick={(e) => {
                  setAdditionalFeatures({ charType: 'line' });
                }}
              >
                <LineChartIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Line</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  setAdditionalFeatures({ charType: 'bar' });
                }}
              >
                <BarChart3Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span>Bar</span>
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>

        {hasFilters && (
          <DropdownMenuItem className="cursor-pointer" onClick={startEditing}>
            <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
            <span>Filters</span>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
