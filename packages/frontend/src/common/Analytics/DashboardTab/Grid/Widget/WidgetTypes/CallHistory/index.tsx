import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/Analytics/types';
import { ExternalLink, Link, Loader2Icon, Phone } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import { cn, formatDuration } from '@/lib/utils';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import dayjs from 'dayjs';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AgentDto } from '@/lib/Agent/types';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import useRouting from '@/hooks/useRouting';
import { TableState } from '@/common/Calls/AIRoleplay/List/common';
import CallsTable from '@/common/Calls/AIRoleplay/List/table';
import useUserSession from '@/hooks/useUserSession';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function SymCallCallHistory({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { userId } = useUserSession();

  let reps: number[] = [];
  if (userId) {
    reps = [userId];
  }

  const [tableState, setTableState] = useState<TableState>(
    new TableState(
      undefined,
      undefined, //
      [],
      reps,
    ),
  );

  return (
    <div
      className={
        'h-full w-full py-4 rounded-xl text-card-foreground flex flex-col '
      }
    >
      <div className="font-semibold mb-6">Call History</div>
      <div>
        <CallsTable
          tableState={tableState}
          updateTableState={setTableState}
          updatePagination={(from: number, numberOfResults: number) => {
            tableState.from = from;
            tableState.numberOfResults = numberOfResults;

            setTableState({ ...tableState });
          }}
        />
      </div>
    </div>
  );
}
