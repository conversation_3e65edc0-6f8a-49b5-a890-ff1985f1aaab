import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/Analytics/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import { formatDuration } from '@/lib/utils';
import useWidgetCard from '../WidgetCard/useWidgetCard';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function RepOverallPerformance({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  // console.log(widget);

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [s, setS] = useState<string>('s');

  useEffect(() => {
    if (!isLoading && (data || data == 0)) {
      if (data == 1) {
        setS('');
      } else {
        setS('s');
      }
    }
  }, [isLoading, data]);

  if (isEditing) {
    return (
      <WidgetCard
        widget={widget}
        isEditing={isEditing}
        updateWidgetInfo={updateWidgetInfo}
        deleteWidget={deleteWidget}
        updateFiltersState={updateFiltersState}
        description={description}
      >
        {isLoading ? (
          <div className="w-full h-full flex items-center justify-center">
            <Loader2Icon
              size={40}
              className="animate-spin text-muted-foreground"
            />
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center font-bold text-2xl">
            &nbsp;
          </div>
        )}
      </WidgetCard>
    );
  } else {
    return (
      <div
        className={
          'h-full w-full p-4 rounded-xl border bg-card text-card-foreground shadow-sm flex flex-col '
        }
      >
        <div className="text-muted-foreground">Overall performance</div>
      </div>
    );
  }
}
