import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/Analytics/types';
import useDashboardChartData from '@/hooks/useDashboardChartData';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import AnalyticsService from '@/lib/Analytics';
import useWidgetCard from '../WidgetCard/useWidgetCard';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function ScorecardsStats({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );

  const [min, setMin] = useState<string>('0');
  const [max, setMax] = useState<string>('0');
  const [avg, setAvg] = useState<string>('0');

  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  useEffect(() => {
    if (!isLoading && data) {
      if (data.length > 0) {
        let _min = 0;
        let _max = 0;
        let _sum = 0;
        let _count = 0;

        for (const d of data) {
          if (d.min == 0 && d.max == 0 && d.avg == 0) {
            //skip, never used
          } else {
            if (d.min < _min || _min == 0) {
              _min = d.min;
            }
            if (d.max > _max) {
              _max = d.max;
            }
            _sum += d.avg;
            _count++;
          }
        }

        setMin(String(_min));
        setMax(String(_max));
        if (_count <= 0) {
          setAvg(String(0));
        } else if (_sum <= 0) {
          setAvg(String(0));
        } else {
          setAvg(String(Math.round(_sum / _count)));
        }
      }
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : (
        <div className="w-full h-full flex items-center justify-between">
          <div className="flex flex-col justify-center items-center">
            <div className="flex items-baseline">
              <p className="font-bold text-2xl">{max}</p>
              <p className="font-bold text-xs">&nbsp;/&nbsp;100</p>
            </div>
            <div className="font-semibold text-muted-foreground text-xs">
              Max
            </div>
          </div>
          <div className="flex flex-col justify-center items-center">
            <div className="flex items-baseline">
              <p className="font-bold text-2xl">{min}</p>
              <p className="font-bold text-xs">&nbsp;/&nbsp;100</p>
            </div>
            <div className="font-semibold text-muted-foreground text-xs">
              Min
            </div>
          </div>
          <div className="flex flex-col justify-center items-center">
            <div className="flex items-baseline">
              <p className="font-bold text-2xl">{avg}</p>
              <p className="font-bold text-xs">&nbsp;/&nbsp;100</p>
            </div>
            <div className="font-semibold text-muted-foreground text-xs">
              Avg
            </div>
          </div>
        </div>
      )}
    </WidgetCard>
  );
}
