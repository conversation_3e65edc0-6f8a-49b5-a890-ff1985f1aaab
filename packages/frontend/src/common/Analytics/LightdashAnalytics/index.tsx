'use client';

/* eslint-disable react/no-unknown-property */
import { useEffect, useRef, useState, useMemo } from 'react';
import useDashboard, {
  useAvailableAnalyticsDashboards,
} from '@/hooks/useAnalyticsDashboard';
import { DashboardDto, EmbeddableDashboardDto } from '@/lib/Analytics/types';
import { usePathname, useSearchParams, useRouter } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import dynamic from 'next/dynamic';
import Lightdash from '@lightdash/sdk';
import { Button } from '@/components/ui/button';
import { ChevronLeftIcon } from 'lucide-react';

const LightdashDashboard = dynamic(
  () => import('@lightdash/sdk').then((mod) => mod.Dashboard),
  { ssr: false },
);

export default function LightdashAnalytics() {
  const params = useSearchParams();
  const curSearchParams = useMemo(() => new URLSearchParams(params), [params]);
  const router = useRouter();
  const pathname = usePathname();

  /*********************************/
  /************** INIT *************/
  /*********************************/

  const [currentDashboard, setCurrentDashboard] =
    useState<EmbeddableDashboardDto>();
  const { data: availableDashboards } = useAvailableAnalyticsDashboards();
  const selectedDashboard = useRef<DashboardDto | undefined>(undefined);
  const [isInitialized, setIsInitialized] = useState(false);
  const [savedChart, setSavedChart] = useState<any>(undefined);

  const { data: dashboardDb, isLoading: isLoadingDashboard } = useDashboard(
    true,
    curSearchParams.get('dashboardUuid') &&
      curSearchParams.get('providerProjectUuid')
      ? {
          name: 'Home',
          uuid: curSearchParams.get('dashboardUuid')!,
          providerProjectUuid: curSearchParams.get('providerProjectUuid')!,
        }
      : undefined,
  );

  useEffect(() => {
    if (!dashboardDb) return;
    setCurrentDashboard(dashboardDb);

    if (!isInitialized) {
      setIsInitialized(true);
      updateDashboard(dashboardDb);
    }
  }, [isLoadingDashboard, dashboardDb]);

  const updateDashboard = (dashboard: DashboardDto) => {
    if (!dashboard) return;
    selectedDashboard.current = dashboard;
    const newSearchParams = new URLSearchParams(curSearchParams);
    newSearchParams.set('dashboardUuid', dashboard.uuid);
    newSearchParams.set('providerProjectUuid', dashboard.providerProjectUuid);
    router.replace(`${pathname}?${newSearchParams.toString()}`);
  };

  /*********************************/
  /************* RENDER ************/
  /*********************************/

  return (
    <div>
      <div className="flex items-center px-3 my-3">
        <div>
          <Select
            onValueChange={(dashboardUuid: string) => {
              const dashboard = availableDashboards?.find(
                (d: DashboardDto) => d.uuid === dashboardUuid,
              );
              if (dashboard) updateDashboard(dashboard);
            }}
            value={selectedDashboard.current?.uuid}
          >
            <SelectTrigger className="py-0 h-[34px]">
              <SelectValue placeholder="" />
            </SelectTrigger>
            <SelectContent>
              {availableDashboards?.map((dashboard: DashboardDto) => {
                return (
                  <SelectItem key={dashboard.uuid} value={dashboard.uuid}>
                    <div>{dashboard.name}</div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="relative w-full">
        {savedChart && currentDashboard && (
          <div className="mx-3">
            <Button
              onClick={() => {
                setSavedChart(null);
              }}
              variant="outline"
              className="mb-4 w-10 h-10 p-0"
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </Button>
            <Lightdash.Explore
              instanceUrl={process.env.NEXT_PUBLIC_LIGHTDASH_BASE_URL!}
              token={currentDashboard.secret}
              exploreId={savedChart.tableName}
              savedChart={savedChart}
            />
          </div>
        )}
        {!isLoadingDashboard && currentDashboard && !savedChart && (
          <LightdashDashboard
            key={currentDashboard.uuid}
            instanceUrl={process.env.NEXT_PUBLIC_LIGHTDASH_BASE_URL!}
            token={currentDashboard.secret}
            onExplore={(chart) => {
              setSavedChart(chart.chart);
              console.log(chart.chart.tableName);
            }}
          />
        )}
        <style jsx global>{`
          .mantine-Paper-root {
            --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
              var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
            padding: 1rem;
            border-width: 1px;
            border-radius: 0.75rem;
            border-color: hsl(var(--border));
            color: hsl(var(--card-foreground));
            background-color: hsl(var(--card));
          }
          .mantine-Card-root.tile-base {
            --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
              var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
            padding: 1rem;
            border-width: 1px;
            border-radius: 0.75rem;
            border-color: hsl(var(--border));
            color: hsl(var(--card-foreground));
            background-color: hsl(var(--card));
          }
          .mantine-UnstyledButton-root {
            padding: 5px;
            background-color: transparent;
            border-width: 1px;
            border-radius: calc(var(--radius) - 2px);
            border-color: hsl(var(--border));
            --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
              var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
          }
          .mantine-Menu-item:disabled {
            --tw-bg-opacity: 1;
            background-color: rgb(243 244 246 / var(--tw-bg-opacity));
            color: inherit;
          }
          .mantine-Menu-dropdown {
            border-width: 1px;
            border-radius: calc(var(--radius) - 2px);
            border-color: hsl(var(--border));
            --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
              var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
          }
          .mantine-Menu-arrow {
            border-width: 1px;
            border-color: hsl(var(--border));
          }
          .mantine-Popover-dropdown {
            border-width: 1px;
            border-radius: calc(var(--radius) - 2px);
            border-color: hsl(var(--border));
            --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
              var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
          }
          .mantine-Input-input {
            border-width: 1px;
            border-radius: calc(var(--radius) - 2px);
            border-color: hsl(var(--border));
          }
          .mantine-Button-root {
            color: hsl(var(--foreground));
          }
          .mantine-Button-root:hover {
            --tw-bg-opacity: 1;
            background-color: rgb(243 244 246 / var(--tw-bg-opacity));
            color: inherit;
          }
          .mantine-Select-dropdown {
            border-width: 1px;
            border-radius: calc(var(--radius) - 2px);
            border-color: hsl(var(--border));
            --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
              var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
          }
          .mantine-Select-item[data-selected='true'] {
            --tw-bg-opacity: 1;
            background-color: rgb(243 244 246 / var(--tw-bg-opacity));
            color: inherit;
          }
          .mantine-Select-item[data-selected='true']:hover {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
          }
          .mantine-Input-input:focus,
          .mantine-Input-input:focus-within {
            border-color: hsl(var(--primary)) !important;
          }
          .mantine-Popover-dropdown .tabler-icon {
            stroke: hsl(var(--primary));
          }
          .loading_chart_overlay svg {
            stroke: #b1e3db;
          }
        `}</style>
      </div>
    </div>
  );
}
