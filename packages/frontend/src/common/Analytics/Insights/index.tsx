import { useState } from 'react';
import RepsOfConcern from './components/sections/RepsOfConcern';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  AnalyticsFilterDateRange,
  AnalyticsFilterState,
  AnalyticsFilterType,
  InsightsCallType,
} from '@/lib/Analytics/types';
import GlobalFilters from './components/globalFilters';
import dayjs from 'dayjs';
import CriterionByRep from './components/sections/CriterionByRep';
import ObjectionsByRep from './components/sections/ObjectionsByRep';
import InsightsChat from './components/sections/InsightsChat';
import { Button } from '@/components/ui/button';
import { SparklesIcon } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import MonthlyPerformanceSummary from './components/sections/MonthlyPerformanceSummary';

export default function Insights({ onChatOpenChange }: { onChatOpenChange?: (isOpen: boolean) => void }) {
  const [currentTab, setCurrentTab] = useState<InsightsCallType>(
    InsightsCallType.SIMULATED_CALLS,
  );
  const [isChatOpen, setIsChatOpen] = useState(false);

  const f: AnalyticsFilterState = {} as AnalyticsFilterState;

  const today = dayjs();

  f[AnalyticsFilterType.DATE] = {
    range: AnalyticsFilterDateRange.CUSTOM,
    fromDate: today.subtract(30, 'days').startOf('day').toDate(),
    toDate: today.toDate(),
  };

  const [globalFilters, setGlobalFilters] = useState<AnalyticsFilterState>(f);

  const onTabChange = (t: InsightsCallType) => {
    setCurrentTab(t);
  };

  const handleChatToggle = (isOpen: boolean) => {
    setIsChatOpen(isOpen);
    onChatOpenChange?.(isOpen);
  };

  return (
    <div>
      <Tabs defaultValue={currentTab} value={currentTab} className="mt-6">
        <div className="flex items-center mx-3">
          <TabsList>
            <TabsTrigger
              value={InsightsCallType.SIMULATED_CALLS}
              onClick={() => onTabChange(InsightsCallType.SIMULATED_CALLS)}
            >
              Simulated Calls
            </TabsTrigger>
            <TabsTrigger
              value={InsightsCallType.REAL_CALLS}
              onClick={() => onTabChange(InsightsCallType.REAL_CALLS)}
            >
              Real Calls
            </TabsTrigger>
          </TabsList>
        </div>
      </Tabs>

      <div className="mx-3 mt-6">
        <GlobalFilters
          type={currentTab}
          current={globalFilters}
          updateFilters={setGlobalFilters}
        />
      </div>

      <div
        className="flex flex-row items-center justify-between p-6 mt-6 rounded-xl mx-3"
        style={{
          background:
            'linear-gradient(180deg, #3DC3E6 0%, #49C8CF 33.33%, #36C4BF 98.96%)',
        }}
      >
        <div className="text-white text-lg font-medium flex flex-row items-center justify-start">
          <SparklesIcon className="w-6 h-6 mr-2" />
          <span className="mr-6">Chat with Kota</span>
          <Button
            variant="outline"
            onClick={() => handleChatToggle(!isChatOpen)}
            className="text-white bg-white/10 hover:bg-white/20 shadow-none hover:text-white transition-all duration-300"
          >
            Open Chat
          </Button>
        </div>
        <div className="flex flex-row items-center justify-end text-sm text-white">
          Let Hyperbound&apos;s AI help you analyze your data, speeding up issue
          resolution.
        </div>
      </div>
      <AnimatePresence>
        {isChatOpen && (
          <>
            <motion.div
              key="backdrop"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed z-[900] top-0 left-0 w-[100vw] h-[100vh] bg-gray-700/20 backdrop-blur-[4px]"
              onClick={() => handleChatToggle(false)}
            />
            <motion.div
              key="chatContainer"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'tween', duration: 0.2, ease: 'easeOut' }}
              className="fixed z-[1000] top-0 right-0 h-full w-full min-w-[500px] max-w-[800px] p-4"
            >
              <InsightsChat type={currentTab} filters={globalFilters} />
            </motion.div>
          </>
        )}
      </AnimatePresence>

      <div className="mt-6 mx-3">
        <MonthlyPerformanceSummary type={currentTab} />
      </div>

      <div className="bg-white border rounded-xl p-6 mt-6 mx-3 max-h-[500px] overflow-y-auto">
        <h2 className="font-bold">Reps of Concern</h2>
        <p className="text-xs font-medium text-muted-foreground mb-12">
          Identify reps who need coaching and their top 3 areas for improvement.
          Hyperbound&apos;s custom algorithm analyzes each rep&apos;s average
          score, call volume, and scorecard performance. You can compare a
          rep&apos;s performance to the team and review specific calls for
          deeper insights. If a rep is not showing up, it means they have too
          few calls to be evaluated.
        </p>
        <RepsOfConcern type={currentTab} filters={globalFilters} />
      </div>

      <div className="bg-white border rounded-xl mt-6 mx-3 pb-6 max-h-[500px] lg:max-h-[800px] overflow-y-auto">
        <div className="p-6">
          <h2 className="font-bold">Criterion By Rep</h2>
          <p className="text-xs font-medium text-muted-foreground mb-2">
            Evaluate rep performance on specific scorecard criteria to pinpoint
            areas where reps are excelling or facing challenges. Use examples
            from top performers to help elevate your team&apos;s performance.
            For each criterion, review handle rate and frequency for the entire
            team on the left side. Hover over any cell to see review a
            rep&apos;s individual performance.
          </p>
        </div>
        <CriterionByRep type={currentTab} filters={globalFilters} />
      </div>

      <div className="bg-white border rounded-xl pb-6 mt-6 mx-3 max-h-[500px] lg:max-h-[800px] overflow-y-auto">
        <div className="p-6">
          <h2 className="font-bold">Objections By Rep</h2>
          <p className="text-xs font-medium text-muted-foreground mb-2">
            Evaluate rep performance on specific objections to pinpoint areas
            where reps are excelling or facing challenges. Use examples from top
            performers to help elevate your team&apos;s performance. For each
            objection, review handle rate and frequency for the entire team on
            the left side. Hover over any cell to see review a rep&apos;s
            individual performance.
          </p>
        </div>
        <ObjectionsByRep type={currentTab} filters={globalFilters} />
      </div>
    </div>
  );
}
