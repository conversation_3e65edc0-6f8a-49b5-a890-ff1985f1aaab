import {
  AnalyticsFilterState,
  AnalyticsFilterType,
  DateFilterType,
  InsightsCallType,
} from '@/lib/Analytics/types';
import CallTypeFilter from '../filters/CallTypeFilter';
import { AgentCallType } from '@/lib/Agent/types';
import DatesFilterShort from '../filters/DateFilterShort';
import TeamsFilter from '../filters/TeamsFilter';
import CustomCallTypesFilter, {
  CustomCallTypesIds,
} from '../filters/CustomCallTypesFilter';

interface IProps {
  type: InsightsCallType;
  current: AnalyticsFilterState;
  updateFilters: (f: AnalyticsFilterState) => void;
}

export default function GlobalFilters({
  type,
  current,
  updateFilters,
}: IProps) {
  if (type === InsightsCallType.SIMULATED_CALLS) {
    return (
      <div className="flex items-center mb-6">
        <div className="w-[300px] mr-2">
          <CallTypeFilter
            current={current[AnalyticsFilterType.CALL_TYPES]}
            onCallTypesUpdated={(callTypes: AgentCallType[]) => {
              updateFilters({
                ...current,
                [AnalyticsFilterType.CALL_TYPES]: callTypes,
              });
            }}
          />
        </div>

        <div className="mr-2 w-[200px]">
          <TeamsFilter
            current={current[AnalyticsFilterType.TEAMS]}
            onFiltersUpdated={(n: number[]) => {
              updateFilters({ ...current, [AnalyticsFilterType.TEAMS]: n });
            }}
          />
        </div>

        <DatesFilterShort
          current={current[AnalyticsFilterType.DATE]}
          onDatesUpdated={(dates: DateFilterType) => {
            updateFilters({ ...current, [AnalyticsFilterType.DATE]: dates });
          }}
        />
      </div>
    );
  } else if (type === InsightsCallType.REAL_CALLS) {
    return (
      <div className="flex items-center mb-6">
        <div className="w-[300px] mr-2">
          <CallTypeFilter
            current={current[AnalyticsFilterType.CALL_TYPES]}
            onCallTypesUpdated={(callTypes: AgentCallType[]) => {
              updateFilters({
                ...current,
                [AnalyticsFilterType.CALL_TYPES]: callTypes,
              });
            }}
          />
        </div>
        <div className="w-[300px] mr-2">
          <CustomCallTypesFilter
            current={{
              organization:
                current[AnalyticsFilterType.CUSTOM_CALL_TYPES_ORG_IDS] || [],
              teams:
                current[AnalyticsFilterType.CUSTOM_CALL_TYPES_TEAM_IDS] || [],
              users:
                current[AnalyticsFilterType.CUSTOM_CALL_TYPES_USER_IDS] || [],
            }}
            onCallTypesUpdated={(n: CustomCallTypesIds) => {
              updateFilters({
                ...current,
                [AnalyticsFilterType.CUSTOM_CALL_TYPES_ORG_IDS]: n.organization,
                [AnalyticsFilterType.CUSTOM_CALL_TYPES_TEAM_IDS]: n.teams,
                [AnalyticsFilterType.CUSTOM_CALL_TYPES_USER_IDS]: n.users,
              });
            }}
          />
        </div>

        <div className="mr-2 w-[200px]">
          <TeamsFilter
            current={current[AnalyticsFilterType.TEAMS]}
            onFiltersUpdated={(n: number[]) => {
              updateFilters({ ...current, [AnalyticsFilterType.TEAMS]: n });
            }}
          />
        </div>

        <DatesFilterShort
          current={current[AnalyticsFilterType.DATE]}
          onDatesUpdated={(dates: DateFilterType) => {
            updateFilters({ ...current, [AnalyticsFilterType.DATE]: dates });
          }}
        />
      </div>
    );
  }
}
