import {
  AnalyticsFilterDateRange,
  DateFilterType,
} from '@/lib/Analytics/types';
import { cn } from '@/lib/utils';
import dayjs from 'dayjs';
import { useCallback } from 'react';

interface IDatesFilterProps {
  current: DateFilterType;
  onDatesUpdated: (n: DateFilterType) => void;
}

export default function DatesFilterShort({
  current,
  onDatesUpdated,
}: IDatesFilterProps) {
  const updateSelected = useCallback(
    (options: any[]) => {
      const selected = false;
      if (current) {
        const from = dayjs(current.fromDate);
        const to = dayjs(current.toDate);
        for (const o of options) {
          if (o.from.isSame(from, 'day')) {
            if (o.to.isSame(to, 'day')) {
              o.selected = true;
            }
          }
        }
      }
      return selected;
    },
    [current],
  );

  const today = dayjs();

  const options: any[] = [
    {
      label: '7D',
      from: today.subtract(7, 'days').startOf('day'),
      to: today,
      selected: false,
    },
    {
      label: '30D',
      from: today.subtract(30, 'days').startOf('day'),
      to: today,
      selected: false,
    },
    {
      label: '3M',
      from: today.subtract(90, 'days').startOf('day'),
      to: today,
      selected: false,
    },
    {
      label: '6M',
      from: today.subtract(180, 'days').startOf('day'),
      to: today,
      selected: false,
    },
  ];

  updateSelected(options);

  const updateFilters = (o: any) => {
    onDatesUpdated({
      range: AnalyticsFilterDateRange.CUSTOM,
      fromDate: o.from.toDate(),
      toDate: o.to.toDate(),
    });
  };

  return (
    <div className="flex items-center bg-gray-200 p-1 rounded-lg space-x-1">
      {options.map((o: any) => {
        return (
          <div
            key={o.label}
            className={cn('px-3 py-1  rounded-lg text-sm font-semibold ', {
              'bg-white text-black': o.selected,
              'cursor-pointer hover:bg-gray-300 hover:text-black text-muted-foreground':
                !o.selected,
            })}
            onClick={() => {
              updateFilters(o);
            }}
          >
            {o.label}
          </div>
        );
      })}
    </div>
  );
}
