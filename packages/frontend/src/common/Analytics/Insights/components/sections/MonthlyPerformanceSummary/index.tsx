import { InsightsCallType } from "@/lib/Analytics/types";
import { useQuery } from "@tanstack/react-query";
import InsightsService from "@/lib/Insights";
import { Loader2Icon, SparklesIcon } from "lucide-react";
import ReactMarkdown from "react-markdown";

export default function MonthlyPerformanceSummary({ type }: { type: InsightsCallType }) {
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
  const previousYear = currentMonth === 0 ? currentYear - 1 : currentYear;

  const { data: performance, isLoading } = useQuery({
    queryKey: ['monthly-performance', type],
    queryFn: () => InsightsService.getMonthlyPerformance(
      previousMonth,
      previousYear,
      type,
    ),
    refetchInterval: 1000 * 60 * 60 * 0.05, // 3 minutes
  });

  return (
    <div className="bg-white border rounded-xl p-6">
      <h2 className="font-bold mb-2">Monthly Performance Summary</h2>
      <div className="flex flex-row items-center text-[#33ABB2] font-medium text-sm">
        <SparklesIcon className="w-4 h-4 mr-2" /> Hyperbound AI - {currentMonth}/{currentYear}
      </div>

      {isLoading && (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground my-6"
          />
        </div>
      )}

      {!isLoading && (
        <>
          <h3 className="font-bold mb-2 mt-6">Summary</h3>
          <p className="text-xs font-medium text-muted-foreground mb-6">
            <ReactMarkdown>{performance?.summary}</ReactMarkdown>
          </p>

          <h3 className="font-bold mb-2">Significant Objections</h3>
          <p className="text-xs font-medium text-muted-foreground mb-6">
            <ReactMarkdown>{performance?.significantObjectionsSummary}</ReactMarkdown>
          </p>

          <h3 className="font-bold mb-2">Significant Competitors</h3>
          <p className="text-xs font-medium text-muted-foreground mb-6">
            <ReactMarkdown>{performance?.significantCompetitorsSummary}</ReactMarkdown>
          </p>

          <h3 className="font-bold mb-2">Top Blockers</h3>
          <p className="text-xs font-medium text-muted-foreground mb-6">
            <ReactMarkdown>{performance?.topBlockersSummary}</ReactMarkdown>
          </p>

          <h3 className="font-bold mb-2">Worst Performing Criteria</h3>
          <p className="text-xs font-medium text-muted-foreground mb-6">
            <ReactMarkdown>{performance?.worstPerformingCriterionsSummary}</ReactMarkdown>
          </p>

          <h3 className="font-bold mb-2">Worst Performing Objections</h3>
          <p className="text-xs font-medium text-muted-foreground mb-6">
            <ReactMarkdown>{performance?.worstPerformingObjectionsSummary}</ReactMarkdown>
          </p>

        </>
      )}
    </div>
  );
}