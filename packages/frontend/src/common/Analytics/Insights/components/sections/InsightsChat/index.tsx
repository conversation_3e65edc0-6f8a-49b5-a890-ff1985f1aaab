import { Button } from "@/components/ui/button";
import { useInsightsChat } from "@/hooks/useInsightsChat";
import { AnalyticsFilterState, InsightsCallType } from "@/lib/Analytics/types";
import { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";
import ReactMarkdown from "react-markdown";
import { Input } from "@/components/ui/input";
import { Loader2, LockIcon, SendIcon, TrashIcon } from "lucide-react";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import Image from "next/image";

export default function InsightsChat({ filters, type }: { filters: AnalyticsFilterState, type: InsightsCallType }) {
  const { messageHistory, isResponding, sendMessage, clearMessageHistory } = useInsightsChat();
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollAtBottom = useRef(true);

  useEffect(() => {
    scrollToBottom();
  }, [])

  useEffect(() => {
    const node = scrollContainerRef.current;
    if (!node) return;

    const handleScroll = () => {
      const atBottom = node.scrollHeight - node.scrollTop <= node.clientHeight + 5;
      scrollAtBottom.current = atBottom;
    };

    node.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => {
      node.removeEventListener('scroll', handleScroll);
    };
  }, [messageHistory.length]);

  useEffect(() => {
    if (scrollAtBottom.current) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messageHistory, isResponding]);

  function send() {
    if (message.trim() == '' || isResponding) return;
    sendMessage(message, filters, type);
    setMessage('');
    scrollToBottom();
  }

  function scrollToBottom() {
    const node = scrollContainerRef.current;
    if (!node) return;

    const atBottom = node.scrollHeight - node.scrollTop <= node.clientHeight + 5;
    scrollAtBottom.current = atBottom;
  }

  return (
    <div 
      className="relative h-full w-full bg-white rounded-[36px] border border-gray-200 overflow-hidden"
      style={{
        backgroundColor: '#ffffff',
        backgroundImage: 'url(/images/bg_chat.png)',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center center',
        backgroundSize: 'cover',
      }}
    >
      <div className="absolute z-10 top-0 w-full backdrop-blur-[16px] p-6 px-12 shadow-sm border-b border-gray-200">
        <h3 className="text-lg font-medium">Chat with Kota - Hyperbound AI</h3>
        <p className="text-xs text-muted-foreground">
          Let our AI analyze your scorecard data, speeding up the process of identifying issues and brainstorming effective solutions.
        </p>
        <p className="text-xs text-muted-foreground flex flex-row items-center mt-2">
          <LockIcon className="w-3 h-3 mr-1" /> Your chat history is private and is stored locally.
        </p>
      </div>

      {messageHistory.length > 0 && (<div ref={scrollContainerRef} className="relative w-full h-full overflow-y-auto px-6">
        <div className="h-48" />

        {messageHistory.map((message, index) => (
          <div
            key={`${message.createdAt ?? index}-${message.role}`}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <p
              className={cn(
                'prose mt-6 mx-2 text-sm',
                message.role === 'user'
                  ? '!text-white text-left shadow-sm rounded-2xl py-4 px-5'
                  : '!text-black text-left',
                message.error != null && '!border-red-200 !bg-red-50'
              )}
              style={{
                background:
                  message.role === 'user'
                    ? 'linear-gradient(180deg, #3DC3E6 0%, #49C8CF 33.33%, #36C4BF 98.96%)'
                    : '',
              }}
            >

              {message.toolCalls?.map((toolCall) => (
                <div key={toolCall.name} className="text-sm text-muted-foreground mb-2">
                  {toolCall.name == 'get_call_details' && (
                    <div className="flex flex-row items-center">
                      <MagnifyingGlassIcon className="w-4 h-4 mr-2" /> Fetched call details...
                    </div>
                  )}
                </div>
              ))}

              {isResponding && message.content.trim() == '' && index == messageHistory.length - 1 && (
                <div className="flex flex-row items-center text-sm justify-center text-muted-foreground">
                  <Loader2 className="w-4 h-4 animate-spin mr-2" /> Generating response...
                </div>
              )}

              {message.content.trim() != '' && (
                <ReactMarkdown>{message.content}</ReactMarkdown>
              )}
            </p>
            <span className="mt-6 text-sm text-red-500">
              {message.error != null && (
                "Error while responding, please try again."
              )}
            </span>
          </div>
        ))}

        <div ref={messagesEndRef} />

        <div className="h-[128px]" />
      </div>)}

      {messageHistory.length == 0 && (
        <div className="flex flex-col items-center justify-center h-full w-full text-center">
          <div className="relative px-4 mb-2">
            <Image
              width={96}
              height={96}
              src="/images/kota.png"
              className="mb-2 rounded-full border border-gray-200 shadow-sm"
              alt="Kota - the Hyperbound dog mascot"
            />
            <div className="absolute z-10 bottom-0 right-0 bg-[#36C4BF]/20 text-[#36C4BF] px-2 py-1 rounded-full">
              Beta
            </div>
          </div>
          <p className="text-lg font-medium mb-2">Hey I'm Kota, let me fetch some data! 🎾</p>
          <p className="text-xs text-muted-foreground">
            My knowledge base is your previously filtered scorecards.
          </p>

          {/* Examples */}
          <div className="grid grid-cols-2 items-center justify-center m-12 gap-6 text-xs text-muted-foreground text-nowrap">
            <div 
              className="col-span-1 p-2 bg-white rounded-full border border-gray-200 shadow-sm cursor-pointer hover:bg-[#36C4BF]/20 hover:text-[#36C4BF] hover:border-[#36C4BF] transition-all duration-300"
              onClick={() => {
                sendMessage("Who is the best performing seller?", filters, type);
              }}
            >
              Who is the best performing seller?
            </div>
            <div 
              className="col-span-1 p-2 bg-white rounded-full border border-gray-200 shadow-sm cursor-pointer hover:bg-[#36C4BF]/20 hover:text-[#36C4BF] hover:border-[#36C4BF] transition-all duration-300"
              onClick={() => {
                sendMessage("Analyze the best call of the month", filters, type);
              }}
            >
              Analyze the best call of the month
            </div>
            <div 
              className="col-span-1 p-2 bg-white rounded-full border border-gray-200 shadow-sm cursor-pointer hover:bg-[#36C4BF]/20 hover:text-[#36C4BF] hover:border-[#36C4BF] transition-all duration-300"
              onClick={() => {
                sendMessage("Create a coaching strategy based on best calls", filters, type);
              }}
            >
              Create a coaching strategy based on best calls
            </div>
            <div 
              className="col-span-1 p-2 bg-white rounded-full border border-gray-200 shadow-sm cursor-pointer hover:bg-[#36C4BF]/20 hover:text-[#36C4BF] hover:border-[#36C4BF] transition-all duration-300"
              onClick={() => {
                sendMessage("Which sellers are falling behind?", filters, type);
              }}
            >
              Which sellers are falling behind?
            </div>
          </div>

        </div>
      )}

      <div className="absolute bottom-0 left-0 right-0 m-6">
        {/* <p className="text-xs text-muted-foreground mb-2 text-center">
          Kota is in early beta, information may be inaccurate.
        </p>*/}
        <div
          className="px-2 py-1.5 border border-gray-200 rounded-full backdrop-blur-[16px] shadow-sm flex flex-row items-center justify-between"
        >
          <Button
            className="rounded-full bg-gray-200 shadow-sm text-gray-500 hover:bg-red-200 hover:text-red-500 transition-all duration-300"
            onClick={() => clearMessageHistory()}
            disabled={isResponding}
          >
            <TrashIcon className="w-4 h-4" />
          </Button>

          <Input
            className="border-none shadow-none focus-visible:ring-0 mr-6"
            placeholder="Ask me anything about your scorecards..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onSubmit={() => send()}
            onKeyDown={(e) => {
              if (e.key == 'Enter') {
                send()
              }
            }}
          />

          <Button
            className="rounded-full font-semibold drop-shadow-lg hover:opacity-90 transition-all duration-300"
            style={{
              background:
                'linear-gradient(180deg, #3DC3E6 0%, #49C8CF 33.33%, #36C4BF 98.96%)',
            }}
            onClick={() => send()}
            disabled={isResponding}
          >
            Send <SendIcon className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}
