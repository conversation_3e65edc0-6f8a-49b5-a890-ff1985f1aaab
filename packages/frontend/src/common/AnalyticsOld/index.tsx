import { useEffect, useRef, useState } from 'react';
import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import useDashboard from '@/hooks/useDashboard';
import { DashboardTabDto } from '@/lib/AnalyticsOld/types';
import DashboardTab from './DashboardTab';
import NewDashboard from './NewDashboard';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useRouter } from 'next/navigation';
import {
  Pencil,
  Plus,
  Loader2Icon,
  GlobeLock,
  CloudDownload,
  FileSpreadsheet,
  Lock,
  ChevronDown,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import AnalyticsService from '@/lib/AnalyticsOld';
import { Id, toast } from 'react-toastify';
import { useQueryClient } from '@tanstack/react-query';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import ExportAnalyticsModal from './ExportModal';
import useUserSession from '@/hooks/useUserSession';
import Image from 'next/image';
import PageHeader from '@/components/PageHeader';
import { AppPermissions } from '@/lib/permissions';

interface IAnalyticsProps {
  dashboardId?: number;
}

const SYSTEM_DASHBOARDS = [3, 4];

export default function AdminAnalytics({ dashboardId }: IAnalyticsProps) {
  const router = useRouter();
  const [dashboardTabs, setDashboardTabs] = useState<DashboardTabDto[]>([]);
  const [splitDahsboardTabs, setSplitDashboardTabs] = useState<boolean>(false); //we want to show some dashbaord templates on the left tabs: SYSTEM_DASHBOARDS

  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
    { title: 'Analytics' },
  ]);
  const [currentTab, setCurrentTab] = useState<string>();
  const [currentDashboard, setCurrentDashboard] = useState<DashboardTabDto>();
  const [currentDashboardCopy, setCurrentDashboardCopy] =
    useState<DashboardTabDto>(); //used for editing
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const errorToastId = useRef<Id | null>(null);
  const queryClient = useQueryClient();
  const {
    isAdmin,
    blurSecondaryPages,
    isCompetitionOrg,
    isHyperboundUser,
    canAccess,
  } = useUserSession();

  /*********************************/
  /************** INIT *************/
  /*********************************/

  const { data: dashboardDb, isLoading: isLoadingDashboard } = useDashboard();

  useEffect(() => {
    if (!isLoadingDashboard && dashboardDb) {
      if (dashboardDb.length > 0) {
        let title = dashboardDb[0].title;
        let tabId = 'tab' + String(dashboardDb[0].id);
        let noDefaultDashboards = true;
        dashboardDb.map((d) => {
          if (d.dashboardTemplateId) {
            if (SYSTEM_DASHBOARDS.indexOf(d.dashboardTemplateId) > -1) {
              if (noDefaultDashboards) {
                tabId = 'tab' + String(d.id);
                title = d.title;
                noDefaultDashboards = false;
                setCurrentDashboard(d);
                setCurrentDashboardCopy(JSON.parse(JSON.stringify(d)));
              }
              setSplitDashboardTabs(true);
            }
          }
        });
        if (dashboardId) {
          dashboardDb.map((d) => {
            if (d.id == dashboardId) {
              tabId = 'tab' + String(d.id);
              title = d.title;

              setCurrentDashboard(d);
              setCurrentDashboardCopy(JSON.parse(JSON.stringify(d)));
            }
          });
        } else if (dashboardDb.length > 0 && noDefaultDashboards) {
          setCurrentDashboard(dashboardDb[0]);
          setCurrentDashboardCopy(JSON.parse(JSON.stringify(dashboardDb[0])));
        }
        setCurrentTab(tabId);
        setBreadcrumbs([{ title: 'Analytics' }, { title: title }]);
      }
      setDashboardTabs(dashboardDb);
    }
  }, [isLoadingDashboard, dashboardDb]);

  /*********************************/
  /************** TABS *************/
  /*********************************/

  const newTabAdded = (idNewDashboard: number) => {
    router.push(`/analytics/${idNewDashboard}`);
  };

  const changeTab = (tab: string) => {
    setCurrentTab(tab);
    if (tab == 'AddTab') {
      setBreadcrumbs([{ title: 'Analytics' }, { title: 'New' }]);
    } else {
      const id = Number(tab.replace('tab', ''));
      router.push(`/analytics/${id}`);
    }
  };

  /*********************************/
  /************* EDITING ***********/
  /*********************************/

  const startEditing = () => {
    setIsEditing(true);
  };

  const cancelEditing = () => {
    setCurrentDashboard(currentDashboard);
    if (currentDashboard) {
      setCurrentDashboardCopy({ ...currentDashboard });
    }
    setIsEditing(false);
  };

  const updateDashboard = (d: DashboardTabDto) => {
    setCurrentDashboardCopy(d);
  };

  const startSave = async () => {
    setIsSaving(true);

    if (currentDashboardCopy) {
      try {
        await AnalyticsService.updateDashboardTab(
          currentDashboardCopy.id,
          currentDashboardCopy.title,
          currentDashboardCopy.filters,
        );

        const tmp = [];
        for (const w of currentDashboardCopy.widgets) {
          if (w.isDelete) {
            await AnalyticsService.deleteWidget(w.id);
          } else if (w.isNew) {
            tmp.push(
              await AnalyticsService.addWidgetToDashboard(
                currentDashboardCopy.id,
                w.widgetTemplateId,
                w.name,
                w.description,
                w.props,
                w.appliedFilters,
              ),
            );
          } else {
            tmp.push(
              await AnalyticsService.updateWidget(
                w.id,
                w.name,
                w.description,
                w.props,
                w.appliedFilters,
              ),
            );
          }
        }

        currentDashboardCopy.widgets = tmp;

        setIsEditing(false);
        setCurrentDashboardCopy(
          JSON.parse(JSON.stringify(currentDashboardCopy)),
        );
        setCurrentDashboard(JSON.parse(JSON.stringify(currentDashboardCopy)));
      } catch (e) {
        console.log(e);
        errorToastId.current = toast.error(
          'There was an error. Please try again.',
        );
      }
    }

    queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    setIsSaving(false);
  };

  /*********************************/
  /************* EXPORT ************/
  /*********************************/

  const [openExportModal, setOpenExportModal] = useState(false);

  const exportAsCsv = async () => {
    setOpenExportModal(true);
  };

  /******************************************************/
  /***************** DASHBOARD TEMPALTES ****************/
  /* THIS SHOULD BE ACCESSIBLE ONLY YO HYPERBOUND USERS */
  /******************************************************/
  const openHyperboundDashboardTemplates = () => {
    router.push('/hyperbound/analytics');
  };

  /*********************************/
  /************* RENDER ************/
  /*********************************/

  if (blurSecondaryPages || isCompetitionOrg) {
    return (
      <div className={'relative h-[100vh] block overflow-hidden bg-[#FBFBFB]'}>
        <div className="h-[100vh] overflow-hidden">
          <DashboardNavbar breadcrumbs={breadcrumbs} />
          <div className="w-full">
            <Image
              src={'/images/analytics.png'}
              width={1000}
              height={1000}
              className="mt-4 mx-3 h-full w-4/5"
              alt="coaching"
            />
          </div>
        </div>
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock the power of Analytics
            </div>
            <div className="text-base mt-2 text-muted-foreground max-w-[50%] text-center">
              Complete your onboarding to gain insights into your teams&apos;
              performance, including reps and more.
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="bg-[#FBFBFB] h-[100vh] py-4"
      style={{
        opacity: isLoadingDashboard ? 0 : 1,
        transition: 'opacity 0.2s ease-in-out',
      }}
    >
      <div className="flex items-center px-3 ">
        <PageHeader title="Analytics" />
        <div className="flex-1"></div>
        <div className="flex pr-4">
          {isHyperboundUser && (
            <Button
              variant={'ghost'}
              onClick={openHyperboundDashboardTemplates}
            >
              <Image
                src="/images/hyperbound-logo.svg"
                width={20}
                height={20}
                alt="Hyperbound logo"
                className="mr-1"
              />
              Dashboard templates
            </Button>
          )}
          {currentTab &&
            currentTab != 'AddTab' &&
            (isEditing ? (
              <>
                <Button
                  onClick={cancelEditing}
                  variant={'outline'}
                  className="mr-2"
                  disabled={isSaving}
                >
                  Cancel
                </Button>
                <Button
                  onClick={startSave}
                  variant={'default'}
                  disabled={isSaving}
                >
                  {isSaving ? <Loader2Icon className="animate-spin" /> : 'Save'}
                </Button>
              </>
            ) : (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      className="ml-2 flex items-center"
                      variant={'outline'}
                    >
                      <CloudDownload size={18} className="mr-2" />
                      Export
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="center">
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        exportAsCsv();
                      }}
                    >
                      <FileSpreadsheet className="w-4 h-4 mr-2 text-muted-foreground" />
                      <span>CSV</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                {SYSTEM_DASHBOARDS.indexOf(
                  currentDashboard?.dashboardTemplateId || 0,
                ) < 0 &&
                  (canAccess(AppPermissions.MANAGE_ANALYTICS) ||
                    currentDashboard?.isPersonal) && (
                    <Button
                      onClick={startEditing}
                      variant={'default'}
                      className="ml-2"
                    >
                      <Pencil size={16} className="mr-2" /> Edit
                    </Button>
                  )}
              </>
            ))}
        </div>
      </div>

      {dashboardTabs.length == 0 ? (
        <div className="mt-10 px-6 ">
          <div>
            <div className="flex items-center space-x-2">
              <p className="text-base font-semibold text-primary">
                Create your first dashboard...
              </p>
            </div>
          </div>
          <div className="mt-4">
            <NewDashboard onClose={() => {}} />
          </div>
        </div>
      ) : (
        <div>
          <div className="flex items-center">
            {splitDahsboardTabs && (
              <Tabs value={currentTab} onValueChange={changeTab}>
                <TabsList
                  style={{
                    display: isEditing ? 'none' : '',
                  }}
                  className=" mt-6 mx-6"
                >
                  {dashboardTabs.map((dashboard) => {
                    if (dashboard.dashboardTemplateId) {
                      if (
                        SYSTEM_DASHBOARDS.indexOf(
                          dashboard.dashboardTemplateId,
                        ) > -1
                      ) {
                        return (
                          <TabsTrigger
                            key={dashboard.id}
                            value={'tab' + String(dashboard.id)}
                          >
                            {dashboard.title}{' '}
                            {dashboard.isPersonal && (
                              <span title="Private Dashboard">
                                <GlobeLock size={16} className="ml-2" />
                              </span>
                            )}{' '}
                            {dashboard.isForAdminOnly && (
                              <span title="Admins Only Dashboard">
                                <GlobeLock size={16} className="ml-2" />
                              </span>
                            )}
                          </TabsTrigger>
                        );
                      }
                    }
                  })}
                </TabsList>
              </Tabs>
            )}

            {splitDahsboardTabs && <div className="flex-1" />}

            {currentDashboard && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild className=" mt-6 mx-6">
                  <div>
                    {(() => {
                      if (
                        currentDashboard.dashboardTemplateId &&
                        SYSTEM_DASHBOARDS.indexOf(
                          currentDashboard.dashboardTemplateId,
                        ) > -1
                      ) {
                        return (
                          <Button
                            className=""
                            variant={'outline'}
                            key={currentDashboard.id}
                          >
                            More dashboards
                            <ChevronDown size={16} className="ml-2" />
                          </Button>
                        );
                      } else {
                        return (
                          <Button
                            className=""
                            variant={'outline'}
                            key={currentDashboard.id}
                          >
                            {currentDashboard.title}{' '}
                            {currentDashboard.isPersonal && (
                              <span title="Private Dashboard">
                                <GlobeLock size={16} className="ml-2" />
                              </span>
                            )}{' '}
                            {currentDashboard.isForAdminOnly && (
                              <span title="Admins Only Dashboard">
                                <GlobeLock size={16} className="ml-2" />
                              </span>
                            )}
                            <ChevronDown size={16} className="ml-2" />
                          </Button>
                        );
                      }
                    })()}
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {dashboardTabs
                    .filter(
                      (d) => (d.isForAdminOnly && isAdmin) || !d.isForAdminOnly,
                    )
                    .map((dashboard) => {
                      return (
                        <DropdownMenuItem
                          className="cursor-pointer"
                          onClick={(e) => {
                            changeTab('tab' + dashboard.id);
                            e.stopPropagation();
                          }}
                          key={'btn' + dashboard.id}
                        >
                          <div className="flex items-center">
                            {dashboard.title}{' '}
                            {dashboard.isPersonal && (
                              <span title="Private Dashboard">
                                <GlobeLock size={16} className="ml-2" />
                              </span>
                            )}{' '}
                            {dashboard.isForAdminOnly && (
                              <span title="Admins Only Dashboard">
                                <GlobeLock size={16} className="ml-2" />
                              </span>
                            )}
                          </div>
                        </DropdownMenuItem>
                      );
                    })}
                  {dashboardTabs.length > 0 && <DropdownMenuSeparator />}
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={(e) => {
                      changeTab('AddTab');
                      e.stopPropagation();
                    }}
                  >
                    <div className="flex items-center">
                      <Plus size={18} />
                      &nbsp;New dashboard
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>

          <div>
            {currentTab && currentTab != 'AddTab' && currentDashboardCopy && (
              <DashboardTab
                dashboard={currentDashboardCopy}
                isEditing={isEditing}
                onDashboardUpdated={updateDashboard}
                hideWidgetFilters={currentDashboardCopy.hideWidgetsFilters}
              />
            )}

            {currentTab && currentTab == 'AddTab' && (
              <div className="ml-4">
                <div className="my-10 mx-4">
                  <div>
                    <div className="flex items-center space-x-2">
                      <p className="text-base font-semibold text-primary">
                        Add dashboard...
                      </p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <NewDashboard onClose={newTabAdded} />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      <ExportAnalyticsModal
        open={openExportModal}
        setModalOpen={setOpenExportModal}
      />
    </div>
  );
}
