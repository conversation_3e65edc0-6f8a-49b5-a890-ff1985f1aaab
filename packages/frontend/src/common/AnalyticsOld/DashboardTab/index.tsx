import { useState, useRef, useEffect } from 'react';
import {
  AnalyticsFilterDateRange,
  AnalyticsFilterState,
  AnalyticsFilterType,
  DashboardTabDto,
  DashboardWidgetDto,
  DashboardWidgetTemplateDto,
} from '@/lib/AnalyticsOld/types';
import { Id, toast } from 'react-toastify';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AnalyticsService from '@/lib/AnalyticsOld';
import { useQueryClient } from '@tanstack/react-query';
import EditingCommands from './EditingCommands';
import AddChart from './AddChart';
import Grid from './Grid';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import { useRouter } from 'next/navigation';
import { DashboardTemplateWidget } from '@/lib/AnalyticsOld/DashboardTemplates/types';
import GlobalFilters from './GlobalFilters';
import dayjs from 'dayjs';

interface IDashboardTabProps {
  dashboard: DashboardTabDto;
  isEditing?: boolean;
  onDashboardUpdated?: (d: DashboardTabDto) => void;
  paddingX?: number;
  doNotFitHeightToScreen?: boolean;
  hideWidgetFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function DashboardTab({
  dashboard,
  isEditing,
  onDashboardUpdated,
  paddingX,
  doNotFitHeightToScreen,
  hideWidgetFilters,
  overwriteFilters,
}: IDashboardTabProps) {
  // console.log(dashboard);

  if (isEditing == undefined) {
    isEditing = false;
  }

  if (isEditing && !onDashboardUpdated) {
    throw new Error('onDashboardUpdated is required when isEditing is true');
  }

  const [name, setName] = useState<string>(dashboard.title);
  const [isCloning, setIsCloning] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [counter, setCounter] = useState<number>(0); //used to force refresh
  const router = useRouter();
  const queryClient = useQueryClient();

  /*********************************/
  /********* GLOBAL FITLERS ********/
  /*********************************/
  let f: AnalyticsFilterState = {} as AnalyticsFilterState;

  const today = dayjs();

  f[AnalyticsFilterType.DATE] = {
    range: AnalyticsFilterDateRange.CUSTOM,
    fromDate: today.subtract(30, 'days').startOf('day').toDate(),
    toDate: today.toDate(),
  };
  if (overwriteFilters) {
    f = overwriteFilters;
  }

  const [globalFilters, setGlobalFilters] = useState<
    AnalyticsFilterState | undefined
  >(dashboard.globalFiltersType ? f : undefined);

  useEffect(() => {
    if (overwriteFilters) {
      setGlobalFilters(overwriteFilters);
    }
  }, [overwriteFilters]);

  /*********************************/
  /************ ADD WIDGET *********/
  /*********************************/

  const [openPickWidget, setOpenPickWidget] = useState<boolean>(false);

  const startAddChart = () => {
    setOpenPickWidget(true);
  };

  const endAddChart = () => {
    setOpenPickWidget(false);
  };

  const addWidget = async (widgetTemplate: DashboardWidgetTemplateDto) => {
    endAddChart();

    let wid = 0;
    let maxY = 0; //to position new widget at the bottom
    dashboard.widgets.map((widget) => {
      const id = parseInt(widget.props.i.replace('n', ''));
      const y = parseInt(widget.props.y);
      if (id > wid) {
        wid = id;
      }
      if (y > maxY) {
        maxY = y;
      }
    });
    wid++;

    dashboard.widgets.push({
      id: wid,
      isNew: true,
      name: widgetTemplate.name,
      description: widgetTemplate.description,
      userId: dashboard.userId,
      dashboardId: dashboard.id,
      props: {
        i: 'n' + wid,
        w: widgetTemplate.width,
        h: widgetTemplate.height,
        x: 0,
        y: maxY,
        minW: widgetTemplate.width,
        minH: widgetTemplate.height,
        resizeHandles: ['se'],
        isBounded: true,
      },
      appliedFilters: {},
      widgetTemplateId: widgetTemplate.id,
      type: widgetTemplate.type,
      customFilters: widgetTemplate.customFilters,
    });
    if (onDashboardUpdated) {
      onDashboardUpdated(JSON.parse(JSON.stringify(dashboard)));
    }
  };

  /*********************************/
  /************ ADD FILTERS ********/
  /*********************************/

  const startAddFilter = () => {};

  /*********************************/
  /************ CLONE **************/
  /*********************************/

  const startCloneTab = async () => {
    setIsCloning(true);
    let tabId = undefined;
    try {
      const newTab = await AnalyticsService.cloneDashboardTab(dashboard.id);
      tabId = newTab.id;
    } catch (e) {
      console.error(e);
      toast.error('There was an error. Please try again.');
    }
    setIsCloning(false);
    if (tabId) {
      router.push(`/analytics/${tabId}`);
    }
  };

  /*********************************/
  /************ DELETE *************/
  /*********************************/

  const [openDeleteConfirmation, setOpenDeleteConfirmation] =
    useState<boolean>(false);

  const startDeleteTab = () => {
    setOpenDeleteConfirmation(true);
  };

  const doDeleteDashboardTab = async () => {
    setIsDeleting(true);
    setOpenDeleteConfirmation(false);
    let ok = false;
    try {
      await AnalyticsService.deleteDashboardTab(dashboard.id);
      ok = true;
    } catch (e) {
      toast.error(
        'Only a dashboard creator or an admin can delete a dashboard. Try again or contact support.',
      );
    }
    setIsDeleting(false);

    if (ok) {
      //dashboard
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      router.push(`/analytics`);
    }
  };

  /*********************************/
  /*** SAVE WIDGET CHANGES ****/
  /*********************************/

  const updateWidgetsUpdated = (
    newWidgets: DashboardWidgetDto[] | DashboardTemplateWidget[],
  ) => {
    dashboard.widgets = newWidgets as DashboardWidgetDto[];
    if (onDashboardUpdated) {
      onDashboardUpdated(dashboard);
    }
  };

  const deleteWidget = (
    widget: DashboardWidgetDto | DashboardTemplateWidget,
  ) => {
    const tmp = dashboard.widgets.map((w) => {
      if (w.id == widget.id) {
        w.isDelete = true;
      }
      return w;
    });
    dashboard.widgets = tmp;
    if (onDashboardUpdated) {
      onDashboardUpdated(dashboard);
    }
    setCounter((c) => c + 1);
  };

  /*********************************/
  /************** RENDER ***********/
  /*********************************/

  return (
    <div className="mt-6">
      {isEditing && (
        <div className="flex items-center mx-6 mb-6">
          <Label className="mr-2">Name:</Label>
          <div className="mr-2">
            <Input
              value={name}
              onChange={(e) => {
                setName(e.target.value);
                dashboard.title = e.target.value;
                if (onDashboardUpdated) {
                  onDashboardUpdated(dashboard);
                }
              }}
              className="w-56"
              placeholder=""
            />
          </div>
          <div className="flex-1"></div>
          <EditingCommands
            onAddChart={startAddChart}
            onAddFilter={startAddFilter}
            onCloneTab={startCloneTab}
            onDeleteTab={startDeleteTab}
            isCloning={isCloning}
            isDeleting={isDeleting}
          />
        </div>
      )}

      {dashboard.globalFiltersType && globalFilters && (
        <GlobalFilters
          type={dashboard.globalFiltersType}
          current={globalFilters}
          updateFilters={setGlobalFilters}
        />
      )}


      <Grid
        key={
          'grid-' +
          dashboard.id +
          '-' +
          dashboard.widgets?.length +
          (isEditing ? '-e' : '')
        }
        widgets={dashboard.widgets}
        isEditing={isEditing}
        onWidgetsUpdated={updateWidgetsUpdated}
        onWidgetsDelete={deleteWidget}
        paddingX={paddingX}
        doNotFitHeightToScreen={doNotFitHeightToScreen}
        hideWidgetFilters={hideWidgetFilters}
        overwriteFilters={globalFilters}
      />
      <AddChart
        open={openPickWidget}
        onOpenChange={endAddChart}
        onWidgetClick={addWidget}
      />
      <DeleteConfirmationModal
        open={openDeleteConfirmation}
        onCancel={() => {
          setOpenDeleteConfirmation(false);
        }}
        onConfirm={doDeleteDashboardTab}
        title="Delete dashboard"
        description="Are you sure you want to delete this dashboard?"
      />
    </div>
  );
}
