import {
  AnalyticsFilterDateRange,
  AnalyticsFilterState,
  AnalyticsFilterType,
  DateFilterType,
  GlobalFiltersType,
} from '@/lib/AnalyticsOld/types';
import CallTypeFilter from '../Filters/CallTypeFilter';
import { AgentCallType } from '@/lib/Agent/types';
import DatesFilterShort from '../Filters/DateFilterShort';
import TeamsFilter from '../Filters/TeamsFilter';
import CustomCallTypesFilter, { CustomCallTypesIds } from '../Filters/CustomCallTypesFilter';

interface IProps {
  type: GlobalFiltersType;
  current: AnalyticsFilterState;
  updateFilters: (f: AnalyticsFilterState) => void;
}

export default function GlobalFilters({
  type,
  current,
  updateFilters,
}: IProps) {
  if (type === GlobalFiltersType.RepsAnalytics) {
    return (
      <div className="flex items-center mx-6 mb-6">
        <div className="w-[300px] mr-2">
          <CallTypeFilter
            current={current[AnalyticsFilterType.CALL_TYPES]}
            onCallTypesUpdated={(callTypes: AgentCallType[]) => {
              updateFilters({
                ...current,
                [AnalyticsFilterType.CALL_TYPES]: callTypes,
              });
            }}
          />
        </div>

        <DatesFilterShort
          current={current[AnalyticsFilterType.DATE]}
          onDatesUpdated={(dates: DateFilterType) => {
            updateFilters({ ...current, [AnalyticsFilterType.DATE]: dates });
          }}
        />
      </div>
    );
  } else if (type === GlobalFiltersType.ManagersAnalytics) {
    return (
      <div className="flex items-center mx-6 mb-6">
        <div className="w-[300px] mr-2">
          <CallTypeFilter
            current={current[AnalyticsFilterType.CALL_TYPES]}
            onCallTypesUpdated={(callTypes: AgentCallType[]) => {
              updateFilters({
                ...current,
                [AnalyticsFilterType.CALL_TYPES]: callTypes,
              });
            }}
          />
        </div>

        <div className="mr-2 w-[200px]">
          <TeamsFilter
            current={current[AnalyticsFilterType.TEAMS]}
            onFiltersUpdated={(n: number[]) => {
              updateFilters({ ...current, [AnalyticsFilterType.TEAMS]: n });
            }}
          />
        </div>

        <DatesFilterShort
          current={current[AnalyticsFilterType.DATE]}
          onDatesUpdated={(dates: DateFilterType) => {
            updateFilters({ ...current, [AnalyticsFilterType.DATE]: dates });
          }}
        />
      </div>
    );
  } else if (type === GlobalFiltersType.RepsAnalyticsRealCalls) {
    return (
      <div className="flex items-center mx-6 mb-6">
        <div className="w-[300px] mr-2">
          <CallTypeFilter
            current={current[AnalyticsFilterType.CALL_TYPES]}
            onCallTypesUpdated={(callTypes: AgentCallType[]) => {
              updateFilters({
                ...current,
                [AnalyticsFilterType.CALL_TYPES]: callTypes,
              });
            }}
          />
        </div>
        <div className="w-[300px] mr-2">
          <CustomCallTypesFilter
             current={{
              organization: current[AnalyticsFilterType.CUSTOM_CALL_TYPES_ORG_IDS] || [],
              teams: current[AnalyticsFilterType.CUSTOM_CALL_TYPES_TEAM_IDS] || [],
              users: current[AnalyticsFilterType.CUSTOM_CALL_TYPES_USER_IDS] || []
            }}
            onCallTypesUpdated={(n: CustomCallTypesIds)=>{
              updateFilters({
                ...current,
                [AnalyticsFilterType.CUSTOM_CALL_TYPES_ORG_IDS]: n.organization,
                [AnalyticsFilterType.CUSTOM_CALL_TYPES_TEAM_IDS]: n.teams,
                [AnalyticsFilterType.CUSTOM_CALL_TYPES_USER_IDS]: n.users,
              })
            }}
          />
        </div>

        <div className="mr-2 w-[200px]">
          <TeamsFilter
            current={current[AnalyticsFilterType.TEAMS]}
            onFiltersUpdated={(n: number[]) => {
              updateFilters({ ...current, [AnalyticsFilterType.TEAMS]: n });
            }}
          />
        </div>

        <DatesFilterShort
          current={current[AnalyticsFilterType.DATE]}
          onDatesUpdated={(dates: DateFilterType) => {
            updateFilters({ ...current, [AnalyticsFilterType.DATE]: dates });
          }}
        />
      </div>
    );
  }
}
