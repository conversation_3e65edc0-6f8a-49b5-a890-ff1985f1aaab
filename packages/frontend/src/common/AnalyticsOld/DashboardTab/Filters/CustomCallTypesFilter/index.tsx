import { AgentCallType } from '@/lib/Agent/types';
import {
  Popover,
  PopoverContentWithoutPortal as PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { PhoneIcon, CheckIcon, FilterX } from 'lucide-react';
import {
  Command,
  CommandList,
  CommandItem,
  CommandSeparator,
  CommandGroup,
} from '@/components/ui/command';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AllCategorizationRules, CustomCallType, RealCallOrganizationCategorizationRule, RealCallTeamCategorizationRule, RealCallUserCategorizationRule } from '@/lib/Integrations/RealCalls/types';
import { useCategorizationRules } from '@/hooks/useRealCalls';

export type CustomCallTypesIds = {
  organization: number[];
  teams: number[];
  users: number[];
}

interface ICustomCallTypesFilterProps {
  current: CustomCallTypesIds;
  onCallTypesUpdated: (customCallTypesIds: CustomCallTypesIds) => void;
}

export default function CustomCallTypesFilter({
  current,
  onCallTypesUpdated,
}: ICustomCallTypesFilterProps) {
  const { data: allCustomCallTypes } = useCategorizationRules();
  const [open, setOpen] = useState(false);

  if (!current) {
    current = {
      organization: [],
      teams: [],
      users: [],
    };
  }

  const updateFilter = (open: boolean) => {
    setOpen(open);
  };

  const toggleCallType = (
    userCustomCallType: RealCallUserCategorizationRule | undefined,
    teamCustomCallType: RealCallTeamCategorizationRule | undefined,
    organizationCustomCallType: RealCallOrganizationCategorizationRule | undefined
  ) => {
    const newSelection: CustomCallTypesIds = {
      organization: [...current.organization],
      teams: [...current.teams],
      users: [...current.users],
    };

    if (userCustomCallType?.id !== undefined) {
      const userId = userCustomCallType.id;
      const userIndex = newSelection.users.indexOf(userId);
      if (userIndex > -1) {
        newSelection.users.splice(userIndex, 1); // Remove if exists
      } else {
        newSelection.users.push(userId); // Add if doesn't exist
      }
    }

    if (teamCustomCallType?.id !== undefined) {
      const teamId = teamCustomCallType.id;
      const teamIndex = newSelection.teams.indexOf(teamId);
      if (teamIndex > -1) {
        newSelection.teams.splice(teamIndex, 1); // Remove if exists
      } else {
        newSelection.teams.push(teamId); // Add if doesn't exist
      }
    }

    if (organizationCustomCallType?.id !== undefined) {
      const orgId = organizationCustomCallType.id;
      const orgIndex = newSelection.organization.indexOf(orgId);
      if (orgIndex > -1) {
        newSelection.organization.splice(orgIndex, 1); // Remove if exists
      } else {
        newSelection.organization.push(orgId); // Add if doesn't exist
      }
    }

    if (onCallTypesUpdated) {
      onCallTypesUpdated(newSelection);
    }
  };

  const clearAll = () => {
    if (onCallTypesUpdated) {
      onCallTypesUpdated({
        organization: [],
        teams: [],
        users: [],
      });
    }

    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={updateFilter}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border w-full"
          style={{ justifyContent: 'start' }}
        >
          <PhoneIcon className="mr-2 h-4 w-4" />
          Custom Call Type
          {current?.organization.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {current?.organization.length + current?.teams.length + current?.users.length} selected
                </Badge>
              </div>
            </>
          )}
          <div className="flex-1"></div>
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="p-0 py-1">
        <Command>
          <span className="text-xs font-semibold my-1 mx-3">Organization</span>
          <CommandList>
            {allCustomCallTypes?.organization.map((ct) => {
              const Icon =
                CALL_TYPE_TO_ICON?.[ct.callType as keyof typeof CALL_TYPE_TO_ICON]
                  ?.Icon;
              return (
                <CommandItem
                  key={ct.id?.toString() || ''}
                  value={ct.id?.toString() || ''}
                  onSelect={() => toggleCallType(undefined, undefined, ct)}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      current.organization.find((val) => val === ct.id!)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex items-center">
                    {Icon && <Icon className="mr-2 h-4 w-4" />}
                    <div className="capitalize">{ct.label}</div>
                  </div>
                </CommandItem>
              );
            })}
          </CommandList>
          <span className="text-xs font-semibold my-1 mx-3">Teams</span>
          <CommandList>
            {allCustomCallTypes?.teams.map((ct) => {
              const Icon =
                CALL_TYPE_TO_ICON?.[ct.callType as keyof typeof CALL_TYPE_TO_ICON]
                  ?.Icon;
              return (
                <CommandItem
                  key={ct.id?.toString() || ''}
                  value={ct.id?.toString() || ''}
                  onSelect={() => toggleCallType(undefined, ct, undefined)}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      current?.organization.find((val) => val === ct.id!)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex items-center">
                    {Icon && <Icon className="mr-2 h-4 w-4" />}
                    <div className="capitalize">{ct.label}</div>
                  </div>
                </CommandItem>
              );
            })}
          </CommandList>
          <span className="text-xs font-semibold my-1 mx-3">Users</span>
          <CommandList>
            {allCustomCallTypes?.users.map((ct) => {
              const Icon =
                CALL_TYPE_TO_ICON?.[ct.callType as keyof typeof CALL_TYPE_TO_ICON]
                  ?.Icon;
              return (
                <CommandItem
                  key={ct.id?.toString() || ''}
                  value={ct.id?.toString() || ''}
                  onSelect={() => toggleCallType(ct, undefined, undefined)}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      current.users.find((val) => val === ct.id!)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex items-center">
                    {Icon && <Icon className="mr-2 h-4 w-4" />}
                    <div className="capitalize">{ct.label}</div>
                  </div>
                </CommandItem>
              );
            })}
          </CommandList>
          {current?.organization.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}
