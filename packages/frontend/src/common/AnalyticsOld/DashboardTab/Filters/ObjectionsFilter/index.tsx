import { useEffect, useState, useRef } from 'react';
import Filter, { FilterState } from '../Filter';
import { PilcrowIcon } from 'lucide-react';

interface IObjectionsFilterProps {
  current: string[];
  onObjectionsUpdated: (objections: string[]) => void;
  locked?: boolean;
}

const objectionList = [
    {
        id: 'Not interested at the moment',
        name: 'Not interested at the moment'
    },
    {
        id: 'Pricing',
        name: 'Pricing'
    },
    
    {
        id: 'Implementation Time',
        name: 'Implementation Time'
    },
    {
        id: 'Legal or Procurement Team',
        name: 'Legal or Procurement Team'
    },
    {
        id: 'CRM Integration',
        name: 'CRM Integration'
    },
    {
        id: 'Busy',
        name: 'Busy'
    },
    {
        id: 'Unclassified',
        name: 'Unclassified'
    }
]

export default function ObjectionsFilter({
  current,
  onObjectionsUpdated,
  locked,
}: IObjectionsFilterProps) {

  if (!current) {
    current = [];
  }

  const transformIntoObject = (s: string[]): any[] => {
    const tmp: any[] = [];
    for (const sec of s) {
      tmp.push({ id: sec, name: sec });
    }

    return tmp;
  };

  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(false, 10, false),
  );
  const [selected, setSelected] = useState<any[]>(transformIntoObject(current));





  const onFilterUpdated = (ns: any[]) => {
    setSelected(ns);

    if (onObjectionsUpdated) {
        onObjectionsUpdated(ns.map((a) => a.id.toString()));
    }
  };

  const printLabel = (sec: any) => {
    return <div className="flex space-x-2 items-center">{sec.name}</div>;
  };

  return (
    <Filter
      Icon={PilcrowIcon}
      items={objectionList}
      selected={selected}
      onStateUpdated={setFilterState}
      onFiltersUpdated={onFilterUpdated}
      printLabel={printLabel}
      filterState={filterState}
      useIdAsString={true}
      filterName={'Objections'}
      locked={locked}
      lockedMessage={'Book a demo to access objections'}
    />
  );
}
