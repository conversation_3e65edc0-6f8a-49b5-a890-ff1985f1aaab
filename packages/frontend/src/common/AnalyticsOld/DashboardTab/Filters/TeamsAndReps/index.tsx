import useTeams from '@/hooks/useTeams';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import {
  ChevronDown,
  ChevronRight,
  Loader2Icon,
  Search,
  Square,
  SquareCheck,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { TeamDto, UserDto, UserTeamDto } from '@/lib/User/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export interface IProps {
  placeholder?: string;
  current: UserDto[];
  onUpdate: (selection: UserDto[]) => void;
}

export default function TeamsAndRepsFilter({
  placeholder,
  current,
  onUpdate,
}: IProps) {
  const [selectedUsers, setSelectedUsers] = useState<number[]>(
    current.map((u) => u.id),
  );
  const [selectedUsersDto, setSelectedUsersDto] = useState<UserDto[]>(current);
  //const [selectedTeams, setSelectedTeams] = useState<number[]>([]);
  const [selectedUsersPerTeam, setSelectedUsersPerTeam] = useState<{
    [key: number]: number[];
  }>({});

  const [openTeams, setOpenTeams] = useState<number[]>([]);
  const [open, setOpen] = useState(false);
  const [searchFilter, setSearchFilter] = useState<string>('');

  const { data: allTeams, isLoading: isLoadingTeams } = useTeams(
    0,
    100,
    searchFilter,
    true,
  );
  const teams = allTeams || [];
  const teamsRef = useRef<TeamDto[]>([]);
  teamsRef.current = teams;

  useEffect(() => {
    if (current && teamsRef.current) {
      const cIds = current.map((u) => u.id);
      setSelectedUsersDto(current);
      setSelectedUsers(cIds);

      const supt: { [key: number]: number[] } = {};
      for (const t of teamsRef.current) {
        if (t.users) {
          for (const u of t.users) {
            if (cIds.indexOf(u.id) > -1) {
              if (!supt[t.id]) {
                supt[t.id] = [];
              }
              if (supt[t.id].indexOf(u.id) < 0) {
                supt[t.id].push(u.id);
              }
            }
          }
        }
      }
      setSelectedUsersPerTeam({ ...supt });
    }
  }, [current]);

  /**************************************/
  /************** SEARCH *************/
  /**************************************/

  const [searchString, setSearchString] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout>>(null);

  const search = async (s: string) => {
    setSearchString(s);

    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      setSearchFilter(s);
    }, 300);
  };

  // const loadMore = () => {
  //   // onStateUpdated({ ...filterState, from: filterState.from + filterState.numberOfResults });
  // };

  const toggleTeam = (tid: number) => {
    let add = true;
    const tmp: number[] = [];
    const tmpDtos: UserDto[] = [];
    for (const t of teams) {
      if (t.id == tid && t.users) {
        const usrs = selectedUsersPerTeam[tid];
        if (usrs && usrs.length == t.users?.length) {
          selectedUsersPerTeam[tid] = [];
          add = false;
          for (const tu of t.users) {
            tmp.push(tu.id);
            tmpDtos.push(tu as unknown as UserDto);
          }
        } else {
          selectedUsersPerTeam[tid] = [];
          for (const tu of t.users) {
            selectedUsersPerTeam[tid].push(tu.id);
            tmp.push(tu.id);
            tmpDtos.push(tu as unknown as UserDto);
          }
        }
      }
    }

    setSelectedUsersPerTeam({ ...selectedUsersPerTeam });

    if (add) {
      //if not prev selected => add it
      const currentUsers = [...selectedUsers];
      const currentUsersDto = [...selectedUsersDto];
      for (const u of tmpDtos) {
        if (selectedUsers.indexOf(u.id) < 0) {
          currentUsers.push(u.id);
          currentUsersDto.push(u);
        }
      }

      setSelectedUsers(currentUsers);
      setSelectedUsersDto(currentUsersDto);
      if (onUpdate) {
        onUpdate(currentUsersDto);
      }
    } else {
      //remove users from all teams

      const currentUsers: number[] = [];
      const currentUsersDto: UserDto[] = [];
      for (const u of selectedUsersDto) {
        if (tmp.indexOf(u.id) < 0) {
          currentUsersDto.push(u);
          currentUsers.push(u.id);
        }
      }

      setSelectedUsers(currentUsers);
      setSelectedUsersDto(currentUsersDto);
      if (onUpdate) {
        onUpdate(currentUsersDto);
      }
    }
  };

  const toggleUser = (tid: number, u: UserDto) => {
    const dtos: UserDto[] = [];
    const tmp: number[] = [];
    let found = false;
    for (const tu of selectedUsersDto) {
      if (tu.id != u.id) {
        tmp.push(tu.id);
        dtos.push(tu);
      } else {
        found = true;
      }
    }

    if (!found) {
      tmp.push(u.id);
      dtos.push(u);
    }

    if (!found) {
      //add it to all teams that have this user
      for (const t of teams) {
        if (t.users) {
          for (const tu of t.users) {
            if (u.id == tu.id) {
              if (!selectedUsersPerTeam[t.id]) {
                selectedUsersPerTeam[t.id] = [];
              }
              selectedUsersPerTeam[t.id].push(u.id);
            }
          }
        }
      }
    } else {
      //remove it from all teams that have this user
      for (const tid of Object.keys(selectedUsersPerTeam)) {
        const usrs = selectedUsersPerTeam[parseInt(tid)];
        if (usrs) {
          if (usrs.indexOf(u.id) > -1) {
            const tmp: number[] = [];
            for (const uid of usrs) {
              if (uid != u.id) {
                tmp.push(uid);
              }
            }
            selectedUsersPerTeam[parseInt(tid)] = tmp;
          }
        }
      }
    }

    setSelectedUsersPerTeam({ ...selectedUsersPerTeam });
    setSelectedUsers(tmp);
    setSelectedUsersDto(dtos);
    if (onUpdate) {
      onUpdate(dtos);
    }
  };

  const toggleOpen = (tid: number) => {
    setOpenTeams((old) => {
      const tmp: number[] = [];
      let found = false;
      for (const id of old) {
        if (id != tid) {
          tmp.push(id);
        } else {
          found = true;
        }
      }

      if (!found) {
        tmp.push(tid);
      }

      return tmp;
    });
  };

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
      modal={true}
    >
      <PopoverTrigger asChild>
        <div className="border border-gray-200 bg-white rounded-lg p-2 flex items-center cursor-pointer">
          <div className="flex-1">
            <div className="text-muted-foreground">
              {selectedUsers.length == 0 && placeholder}

              {selectedUsers.length > 0 && (
                <div className="flex items-center">
                  <div
                    className="relative  h-[20px]"
                    style={{
                      width: `${14 + (selectedUsers.length > 3 ? 3 * 16 : selectedUsers.length * 16)}px`,
                    }}
                  >
                    {selectedUsersDto.map((u: UserDto, i: number) => {
                      if (i == 2) {
                        const remaining = selectedUsers.length - 2;
                        if (remaining > 0) {
                          return (
                            <div
                              key={u.id}
                              className="absolute "
                              style={{ left: `${i * 16}px` }}
                            >
                              <div className="w-[24px] h-[24px] border-white border-2 rounded-full bg-gray-100 text-[10px] text-center ">
                                +{remaining}
                              </div>
                            </div>
                          );
                        }
                      } else if (i < 2) {
                        return (
                          <div
                            key={u.id}
                            className="absolute "
                            style={{ left: `${i * 16}px` }}
                          >
                            <Avatar className="w-[24px] h-[24px] border-white border-2">
                              <AvatarImage src={u?.avatar} />
                              <AvatarFallback className="text-sm">
                                {u?.firstName?.charAt(0) || ''}
                                {u?.lastName?.charAt(0) || ''}
                              </AvatarFallback>
                            </Avatar>
                          </div>
                        );
                      } else {
                        return;
                      }
                    })}
                  </div>
                  <div className="flex-1">
                    {selectedUsers.length +
                      ` assignee${selectedUsers.length == 1 ? '' : 's'}`}
                  </div>
                </div>
              )}
            </div>
          </div>
          <motion.div
            animate={{
              rotate: open ? 180 : 0,
            }}
            transition={{
              ease: 'easeOut',
              duration: 0.2,
              delay: 0.1,
            }}
            className="mr-1"
          >
            <ChevronDown className="" size={18} />
          </motion.div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        avoidCollisions={true}
        className="p-0"
        style={{
          maxHeight: `240px`,
          width: 'var(--radix-popover-trigger-width)',
          overflow: 'auto',
        }}
      >
        <div className="border rounded-lg p-2 text-sm flex items-center m-2">
          <div className="mr-2">
            <Search size={16} className="text-muted-foreground" />
          </div>
          <div className="flex-1 mr-2">
            <input
              value={searchString}
              onChange={(e) => {
                search(e.target.value);
              }}
              className="outline-none w-full "
              placeholder="Search"
            />
          </div>
          <div>
            <Loader2Icon
              className={cn('animate-spin text-muted-foreground', {
                invisible: !isLoadingTeams,
              })}
              size={16}
            />
          </div>
        </div>
        {teams.length == 0 && (
          <div className="pt-2 text-sm m-2">No team found.</div>
        )}
        {teams.length > 0 && (
          <div className="text-sm">
            {teams.map((t) => {
              const isOpen = openTeams.indexOf(t.id) > -1;
              let isSelected = false;
              if (t.users && t.users.length > 0 && selectedUsersPerTeam[t.id]) {
                if (selectedUsersPerTeam[t.id].length == t.users.length) {
                  isSelected = true;
                }
              }

              const selectedMembers = (selectedUsersPerTeam[t.id] || []).length;
              return (
                <div key={t.id} className="border-b">
                  <div className="flex items-center m-1 p-1 hover:bg-gray-100 rounded-lg">
                    <motion.div
                      animate={{
                        rotate: isOpen ? 90 : 0,
                      }}
                      transition={{
                        ease: 'easeOut',
                        duration: 0.2,
                        delay: 0.1,
                      }}
                      className="mr-2 cursor-pointer"
                      onClick={() => {
                        toggleOpen(t.id);
                      }}
                    >
                      <ChevronRight
                        className="text-muted-foreground"
                        size={16}
                      />
                    </motion.div>
                    <div
                      className="mr-2 cursor-pointer"
                      onClick={() => {
                        toggleTeam(t.id);
                      }}
                    >
                      {isSelected ? (
                        <SquareCheck
                          size={16}
                          className="text-muted-foreground"
                        />
                      ) : (
                        <Square size={16} className="text-muted-foreground" />
                      )}
                    </div>
                    <div
                      className="flex-1 cursor-pointer"
                      onClick={() => {
                        toggleOpen(t.id);
                      }}
                    >
                      {t.name}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {selectedMembers}/{t.users?.length} member
                      {t.users?.length == 1 ? '' : 's'}
                    </div>
                  </div>

                  <motion.div
                    initial={{ height: 0 }}
                    animate={{
                      height: isOpen ? 'auto' : 0,
                    }}
                    transition={{
                      ease: 'easeOut',
                      duration: 0.2,
                      delay: 0.1,
                    }}
                    className="overflow-hidden"
                  >
                    <div className="pb-1">
                      {t.users?.map((u: UserTeamDto) => {
                        const isSelected =
                          (selectedUsersPerTeam[t.id] || []).indexOf(u.id) > -1;

                        return (
                          <div
                            key={u.id}
                            className="ml-6 flex items-center cursor-pointer p-1 pl-2 hover:bg-gray-100 rounded-lg mr-2"
                            onClick={() => {
                              toggleUser(t.id, u as unknown as UserDto);
                            }}
                          >
                            <div className="mr-2 cursor-pointer">
                              {isSelected ? (
                                <SquareCheck
                                  size={16}
                                  className="text-muted-foreground"
                                />
                              ) : (
                                <Square
                                  size={16}
                                  className="text-muted-foreground"
                                />
                              )}
                            </div>
                            <div className="flex-1 flex items-center">
                              <Avatar className="w-5 h-5 mr-2">
                                {u?.avatar && <AvatarImage src={u?.avatar} />}
                                <AvatarFallback className="text-sm">
                                  {u?.firstName?.charAt(0) || ''}
                                  {u?.lastName?.charAt(0) || ''}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                {u.firstName} {u.lastName}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </motion.div>
                </div>
              );
            })}
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
