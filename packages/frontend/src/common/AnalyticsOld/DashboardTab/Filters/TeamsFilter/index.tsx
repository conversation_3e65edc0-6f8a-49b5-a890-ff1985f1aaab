import React, { useState, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContentWithoutPortal as PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, Loader2Icon, FilterX, Users, LockIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import useTeams from '@/hooks/useTeams';
import { TeamDto } from '@/lib/User/types';
import useUserSession from '@/hooks/useUserSession';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ITeamsFilterProps {
  current: number[];
  onFiltersUpdated: (teamsIds: number[]) => void;
  className?: string;
}

function TeamsFilter({
  current,
  onFiltersUpdated,
  className,
}: ITeamsFilterProps) {
  if (!current) {
    current = [];
  }

  const defaultNumberOfResults = 10;

  const { isLoggedIn } = useUserSession();

  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );
  const [searchString, setSearchString] = useState<string>('');

  const [open, setOpen] = useState(false);

  const { data: allTeams, isLoading: isLoadingTeams } = useTeams(
    0,
    numberOfResults,
    searchString,
    true,
    true,
    false,
  );
  const teams = allTeams || [];

  let noMoreTeams = false;
  const currentlySelectedTeams: TeamDto[] = [];
  if (allTeams) {
    if (allTeams.length > 0) {
      if (allTeams.length < numberOfResults) {
        noMoreTeams = true;
      }
      allTeams.map((t) => {
        if (current.indexOf(t.id) > -1) {
          currentlySelectedTeams.push(t);
        }
      });
    }
  }

  const [selected, setSelected] = useState<TeamDto[]>(currentlySelectedTeams);
  const [prevSelected, setPrevSelected] = useState<TeamDto[]>(
    currentlySelectedTeams,
  );

  if (
    currentlySelectedTeams.length != prevSelected.length ||
    JSON.stringify(currentlySelectedTeams) != JSON.stringify(prevSelected)
  ) {
    setPrevSelected(currentlySelectedTeams);
    setSelected(currentlySelectedTeams);
  }

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const resetAgentsForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetAgentsForSearch.current = true;
      setNumberOfResults(defaultNumberOfResults);
    }, 200);

    setSearchString(s);
  };

  const clearAll = () => {
    setSelected([]);
    if (onFiltersUpdated) {
      onFiltersUpdated([]);
    }
    setOpen(false);
  };

  const toggleTag = (_id: string) => {
    const id = parseInt(_id);

    let newSelection: TeamDto[] = [];

    if (selected.find((val) => val.id === id)) {
      newSelection = selected.filter((val) => val.id !== id) as TeamDto[];
    } else {
      let selectedDto: TeamDto | null = null;
      teams.map((a) => {
        if (a.id == id) {
          selectedDto = a;
          return;
        }
      });

      if (selectedDto) {
        newSelection = [...selected, selectedDto] as TeamDto[];
      }
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(newSelection.map((a) => a.id));
    }
    setSelected([...newSelection] as TeamDto[]);
  };

  const loadMore = () => {
    setNumberOfResults(numberOfResults + 15);
  };

  if (isLoggedIn) {
    return (
      <Popover
        open={open}
        onOpenChange={(o) => {
          setOpen(o);
        }}
      >
        <PopoverTrigger asChild className='w-full"'>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn('border w-full', className)}
            style={{ justifyContent: 'start' }}
          >
            <Users className="mr-2 h-4 w-4" />
            Teams
            {selected.length > 0 && (
              <>
                <Separator orientation="vertical" className="mx-2 h-4" />
                <div className="space-x-1 lg:flex">
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1 font-normal"
                  >
                    {selected.length} selected
                  </Badge>
                </div>
              </>
            )}
            <div className="flex-1"></div>
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          className="p-0 max-h-[50vh] overflow-y-auto"
        >
          {' '}
          <Command>
            <CommandInput
              placeholder="Search teams..."
              className="h-9"
              value={searchString}
              onValueChange={filterResults}
            />
            <CommandList>
              <CommandGroup heading="Teams">
                {teams.map((v) => (
                  <CommandItem
                    key={v.id}
                    value={String(v.id)}
                    onSelect={toggleTag}
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        selected?.find((val) => val.id === v.id)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    <div className="flex space-x-2 items-center">
                      <div className="capitalize">{v.name}</div>
                    </div>
                  </CommandItem>
                ))}

                {isLoadingTeams ? (
                  <CommandItem className="justify-center text-center">
                    <Loader2Icon className="animate-spin" />
                  </CommandItem>
                ) : (
                  <>
                    {teams.length > 0 ? (
                      <>
                        {!noMoreTeams && (
                          <CommandItem
                            onSelect={loadMore}
                            className="justify-center text-center"
                          >
                            More...
                          </CommandItem>
                        )}
                      </>
                    ) : (
                      <CommandItem className="justify-center text-center">
                        No team found
                      </CommandItem>
                    )}
                  </>
                )}
              </CommandGroup>
            </CommandList>
            {selected?.length > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={clearAll}
                    className="justify-center text-center"
                  >
                    <FilterX size="14" />
                    &nbsp;Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </Command>
        </PopoverContent>
      </Popover>
    );
  } else {
    return (
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              className="border-dashed opacity-45"
            >
              <Users className="mr-2 h-4 w-4" />
              Teams
              <Separator orientation="vertical" className="mx-2 h-4" />
              <LockIcon className="w-4 h-4 text-muted-foreground" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Book a demo to access Teams</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
}

export default React.memo(TeamsFilter);
