import React, { useState, useEffect } from 'react';
import Filter, { FilterState, FilterItem } from '../Filter';
import { UserDto } from '@/lib/User/types';
import useOrgUsers from '@/hooks/useOrgUsers';
import useOrgUsersByIds from '@/hooks/useOrgUsersByIds';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Users2Icon } from 'lucide-react';

// Helper function to convert UserDto to FilterItem
const userToFilterItem = (user: UserDto): FilterItem => ({
  id: user.id,
  firstName: user.firstName,
  lastName: user.lastName,
  avatar: user.avatar,
  [Symbol.iterator]: function* () {
    yield* Object.entries(this);
  },
});

interface IRepFilterProps {
  current: number[];
  onRepsUpdated: (reps: string[]) => void;
  isRadioSelect?: boolean;
  keepSelectionInPlace?: boolean;
  displaySelectedName?: boolean;
  className?: string;
  hideClearBtn?: boolean;
}

export default function RepsFilter({
  current,
  onRepsUpdated,
  isRadioSelect,
  keepSelectionInPlace,
  displaySelectedName,
  className,
  hideClearBtn,
}: IRepFilterProps) {
  if (!current) {
    current = [];
  }

  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(true, 10),
  );
  const [allItems, setAllItems] = useState<UserDto[]>([]);
  const [selected, setSelected] = useState<UserDto[]>([]);

  const { data: itemsDB, isLoading: isLoadingItems } = useOrgUsers(
    true,
    filterState.from,
    filterState.numberOfResults,
    filterState.search,
  );

  const { data: currentlySelected, isLoading: isLoadingCurrentlySelected } =
    useOrgUsersByIds(current);

  useEffect(() => {
    let hasMore = true;
    if (!isLoadingItems && itemsDB) {
      if (itemsDB.data.length < filterState.numberOfResults) {
        hasMore = false;
      }
      if (filterState.isSearching) {
        setAllItems([...itemsDB.data].filter((i) => !!i));
      } else {
        setAllItems([...allItems, ...itemsDB.data].filter((i) => !!i));
      }

      setFilterState({
        ...filterState,
        isLoadingItems: isLoadingItems,
        hasMore,
        isSearching: false,
      });
    }
  }, [isLoadingItems, itemsDB]);

  useEffect(() => {
    if (!isLoadingCurrentlySelected && currentlySelected) {
      setSelected(currentlySelected.filter((i) => !!i));
    }
  }, [currentlySelected, isLoadingCurrentlySelected]);

  const onFilterUpdated = (ns: FilterItem[]) => {
    // Since we know these FilterItems were originally UserDtos, this conversion is safe
    const users = ns
      .map((item) => allItems.find((user) => user.id === item.id)!)
      .filter((i) => !!i);
    setSelected(users);

    if (onRepsUpdated) {
      onRepsUpdated(users.map((u) => u.id.toString()));
    }
  };

  const printLabel = (item: FilterItem) => {
    const displayUser = {
      avatar: item.avatar,
      firstName: item.firstName,
      lastName: item.lastName,
    } as UserDto;
    return (
      <div className="flex space-x-2 items-center">
        <Avatar className="w-6 h-6">
          {displayUser?.avatar && (
            <AvatarImage
              src={displayUser?.avatar}
              alt="Avatar"
              referrerPolicy="no-referrer"
            />
          )}
          <AvatarFallback className="text-sm">
            {displayUser?.firstName?.charAt(0) || ''}
            {displayUser?.lastName?.charAt(0) || ''}
          </AvatarFallback>
        </Avatar>
        <div className="capitalize">
          {displayUser?.firstName || ''} {displayUser?.lastName || ''}
        </div>
      </div>
    );
  };

  return (
    <Filter
      Icon={Users2Icon}
      items={allItems.map(userToFilterItem)}
      selected={selected.map(userToFilterItem)}
      onStateUpdated={setFilterState}
      onFiltersUpdated={onFilterUpdated}
      printLabel={printLabel}
      filterState={filterState}
      filterName={'Reps'}
      isRadioSelect={isRadioSelect}
      keepSelectionInPlace={keepSelectionInPlace}
      displaySelectedName={displaySelectedName}
      className={className}
      hideClearBtn={hideClearBtn}
    />
  );
}
