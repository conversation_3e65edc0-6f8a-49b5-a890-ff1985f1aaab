import { useState, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, Loader2Icon, FilterX, LockIcon, ListChecks } from 'lucide-react';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { cn } from '@/lib/utils';
import { MagnifyingGlassIcon } from '@radix-ui/react-icons';
import { Separator } from '@radix-ui/react-separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { LucideIcon } from 'lucide-react';

export interface FilterItem {
  id: string | number;
  [key: string]: unknown; // Allow additional properties
}

export class FilterState {
  from: number = 0;
  numberOfResults: number = 0;
  search: string = '';
  hasMore: boolean = true;
  isLoadingItems: boolean = false;
  isSearching: boolean = false;

  constructor(
    isLoadingItems: boolean,
    numberOfResults: number = 20,
    hasMore = true,
  ) {
    this.isLoadingItems = isLoadingItems;
    this.numberOfResults = numberOfResults;
    this.hasMore = hasMore;
  }
}

interface IFilterProps {
  className?: string;
  selected: FilterItem[];
  items: FilterItem[];
  filterState: FilterState;
  onStateUpdated: (s: FilterState) => void;
  onFiltersUpdated: (s: FilterItem[]) => void;
  printLabel: (s: FilterItem) => React.ReactNode;
  filterName: string;
  Icon: LucideIcon;
  useIdAsString?: boolean;
  hideSearch?: boolean;
  locked?: boolean;
  lockedMessage?: string;
  isRadioSelect?: boolean;
  keepSelectionInPlace?: boolean; //instead of showing on top all selected items.....leave them in the list
  displaySelectedName?: boolean; //instead of showing number of items selected.....show the name
  hideClearBtn?: boolean;
  hideSelectAllBtn?: boolean;
}

interface PresentList {
  [key: string | number]: boolean;
}

export default function Filter({
  selected,
  items,
  onFiltersUpdated,
  onStateUpdated,
  printLabel,
  filterName,
  filterState,
  Icon,
  useIdAsString,
  hideSearch,
  locked,
  lockedMessage,
  isRadioSelect,
  keepSelectionInPlace,
  displaySelectedName,
  className,
  hideClearBtn,
  hideSelectAllBtn,
}: IFilterProps) {
  if (!hideSearch) {
    hideSearch = false;
  }

  const [open, setOpen] = useState(false);

  const presentInList = useRef<PresentList>({});

  if (selected) {
    if (selected.length > 0) {
      presentInList.current = {};
      selected.forEach((a) => {
        presentInList.current[a.id] = true;
      });
    }
  }

  /**************************************/
  /************** STATE MNG *************/
  /**************************************/

  const [searchString, setSearchString] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);

  const search = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      onStateUpdated({ ...filterState, from: 0, isSearching: true, search: s });
    }, 200);

    setSearchString(s);
  };

  const loadMore = () => {
    onStateUpdated({
      ...filterState,
      from: filterState.from + filterState.numberOfResults,
    });
  };

  /**************************************/
  /************** TOGGLE ****************/
  /**************************************/

  const toggleItem = (itemId: string) => {
    let id: number | string = parseInt(itemId);
    if (useIdAsString) {
      id = itemId;
    }

    let newSelection: FilterItem[] = [];

    if (isRadioSelect) {
      presentInList.current = {};
      let selectedItem: FilterItem | null = null;
      items.forEach((a) => {
        if (a.id === id) {
          selectedItem = a;
        }
      });

      if (selectedItem) {
        newSelection = [selectedItem];
      }
    } else {
      if (selected.find((val) => val.id === id)) {
        presentInList.current[id] = false;
        newSelection = selected.filter((val) => val.id !== id);
      } else {
        presentInList.current[id] = true;
        let selectedItem: FilterItem | null = null;
        items.forEach((a) => {
          if (a.id === id) {
            selectedItem = a;
          }
        });

        if (selectedItem) {
          newSelection = [...selected, selectedItem];
        }
      }
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(newSelection);
    }
  };

  const clearAll = () => {
    presentInList.current = {};
    if (onFiltersUpdated) {
      onFiltersUpdated([]);
    }
  };

  const selectAll = () => {
    presentInList.current = {};
    if (onFiltersUpdated) {
      onFiltersUpdated(items);
    }
  };

  /**************************************/
  /*********** RENDERING ****************/
  /**************************************/

  if (!locked) {
    return (
      <Popover
        open={open}
        onOpenChange={(o) => {
          setOpen(o);
        }}
        modal={true}
      >
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn('border w-full', className)}
            style={{ justifyContent: 'start' }}
          >
            <Icon className="mr-2 h-4 w-4" />
            {filterName}
            {selected.length > 0 && (
              <>
                <Separator orientation="vertical" className="mx-2 h-4" />
                {!displaySelectedName ? (
                  <div className="space-x-1 lg:flex">
                    <Badge
                      variant="secondary"
                      className="rounded-sm px-1 font-normal"
                    >
                      {selected.length} selected
                    </Badge>
                  </div>
                ) : (
                  <div className="flex items-center">
                    {selected.map((v) => {
                      return <div key={v.id}>{printLabel(v)}</div>;
                    })}
                  </div>
                )}
              </>
            )}
            <div className="flex-1"></div>
            <CaretSortIcon className="w-4 h-4 ml-2 text-muted-foreground" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          className="p-0"
          style={{ width: 'var(--radix-popover-trigger-width)' }}
        >
          {!hideSearch && (
            <div className="flex items-center border-b px-3">
              <MagnifyingGlassIcon className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                placeholder="Search..."
                className="h-10 py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                value={searchString}
                onChange={(e) => {
                  search(e.target.value);
                }}
              />
            </div>
          )}

          {selected.length > 0 && !keepSelectionInPlace && (
            <>
              <div className="text-xs text-muted-foreground px-3 pt-3 pb-2 font-semibold">
                Selected
              </div>
              <div className="max-h-[100px] overflow-auto">
                {selected.map((v) => (
                  <div
                    key={v.id}
                    onClick={() => {
                      toggleItem(String(v.id));
                    }}
                    className="flex items-center rounded-sm text-sm mx-1 px-2 py-1 cursor-pointer hover:bg-muted"
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center  border border-primary',
                        selected.find((val) => val.id === v.id)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                        {
                          'rounded-full': isRadioSelect,
                          'rounded-sm': !isRadioSelect,
                        },
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    {printLabel(v)}
                  </div>
                ))}
              </div>
            </>
          )}

          <div className="text-xs text-muted-foreground px-3 pt-3 pb-2 font-semibold">
            {filterName}
          </div>
          <div
            className={
              selected.length > 0
                ? 'max-h-[100px] overflow-auto'
                : 'max-h-[200px] overflow-auto'
            }
          >
            {items.map((v, i) => {
              if (!presentInList.current[v.id] || keepSelectionInPlace) {
                return (
                  <div
                    key={'item-' + i}
                    onClick={() => {
                      toggleItem(String(v.id));
                    }}
                    className="flex items-center rounded-sm text-sm mx-1 px-2 py-1 cursor-pointer hover:bg-muted"
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center border border-primary',
                        selected.find((val) => val.id === v.id)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                        {
                          'rounded-full': isRadioSelect,
                          'rounded-sm': !isRadioSelect,
                        },
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    {printLabel(v)}
                  </div>
                );
              }
            })}
            {filterState.isLoadingItems ? (
              <div className="flex justify-center text-center text-muted-foreground m-1 p-1">
                <Loader2Icon className="animate-spin" />
              </div>
            ) : (
              <>
                {items.length > 0 ? (
                  <>
                    {filterState.hasMore && (
                      <div
                        onClick={loadMore}
                        className="text-center text-muted-foreground text-sm m-1 p-1 cursor-pointer hover:bg-muted hover:text-black rounded-sm"
                      >
                        More...
                      </div>
                    )}
                  </>
                ) : (
                  <div className="justify-center text-center text-muted-foreground">
                    No item found
                  </div>
                )}
              </>
            )}
          </div>
          {(items.length - selected.length) > 1 && !hideSelectAllBtn && (
            <div className="border-t p-1">
              <div
                onClick={selectAll}
                className="justify-center text-center flex items-center text-sm p-1 cursor-pointer hover:bg-muted hover:text-black rounded-sm"
              >
                <ListChecks size="14" />
                &nbsp;Select all
              </div>
            </div>
          )}
          {selected.length > 0 && !hideClearBtn && (
            <div className="border-t p-1">
              <div
                onClick={clearAll}
                className="justify-center text-center flex items-center text-sm p-1 cursor-pointer hover:bg-muted hover:text-black rounded-sm"
              >
                <FilterX size="14" />
                &nbsp;Clear filters
              </div>
            </div>
          )}
        </PopoverContent>
      </Popover>
    );
  } else {
    return (
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              className="border-dashed opacity-45"
            >
              <Icon className="mr-2 h-4 w-4" />
              {filterName}
              <Separator orientation="vertical" className="mx-2 h-4" />
              <LockIcon className="w-4 h-4 text-muted-foreground" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>{lockedMessage}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
}
