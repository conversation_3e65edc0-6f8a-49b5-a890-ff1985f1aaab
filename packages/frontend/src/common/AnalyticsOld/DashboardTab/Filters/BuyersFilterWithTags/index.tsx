import { cn } from '@/lib/utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { BotIcon, ChevronsUpDown } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface IProps {
  current: number[];
  onBuyersUpdated: (buyers: number[]) => void;
  isRadioSelect?: boolean;
  keepSelectionInPlace?: boolean;
  displaySelectedName?: boolean;
  className?: string;
  hideClearBtn?: boolean;
  hideSearch?: boolean;
}

export default function BuyerFilterWithTags({
  current,
  onBuyersUpdated,
  isRadioSelect,
  className,
  hideSearch,
}: IProps) {
  const [open, setOpen] = useState(false);

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
      modal={true}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('border w-full', className)}
          style={{ justifyContent: 'start' }}
        >
          <BotIcon className="mr-2 h-4 w-4" />
          {isRadioSelect ? 'Buyer' : 'Buyers'}
          {current.length > 0 && (
            <div className="flex items-center font-normal">
              <Separator orientation="vertical" className="mx-2 h-4" />
              {current.length} selected
            </div>
          )}
          <div className="flex-1"></div>
          <ChevronsUpDown className="ml-2 text-muted-foreground" size={16} />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="p-0"
        style={{ width: 'var(--radix-popover-trigger-width)' }}
      ></PopoverContent>
    </Popover>
  );
}
