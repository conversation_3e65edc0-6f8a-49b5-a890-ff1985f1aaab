import { useState } from 'react';
import Filter, { FilterState } from '../Filter';
import useUserSession from '@/hooks/useUserSession';
import { RepsCanEditScoreResults } from '@/lib/Organization/types';
import { Earth, ListChecks } from 'lucide-react';
import { AgentLanguagesLabels } from '@/lib/Agent/types';

interface IProps {
  current: string[];
  onFiltersUpdated: (langs: string[]) => void;
  locked?: boolean;
}

export default function LanguagesFilter({
  current,
  onFiltersUpdated,
  locked,
}: IProps) {
  if (!current) {
    current = [];
  }

  const transformIntoObject = (s: string[]): any[] => {
    const tmp: any[] = [];
    for (const lang of s) {
      tmp.push({ id: lang, name: AgentLanguagesLabels[lang] });
    }

    return tmp;
  };

  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(false, 10, false),
  );

  const _temp: any[] = [];
  for (const key in AgentLanguagesLabels) {
    _temp.push({ id: key, name: AgentLanguagesLabels[key] });
  }
  const [allItems, setAllItems] = useState<any[]>(_temp);
  const [selected, setSelected] = useState<any[]>(transformIntoObject(current));

  /**************************************/
  /************** TOGGLE ****************/
  /**************************************/

  const _onFilterUpdated = (ns: any[]) => {
    setSelected(ns);

    if (onFiltersUpdated) {
      onFiltersUpdated(ns.map((a) => a.id.toString()));
    }
  };

  const printLabel = (sec: any) => {
    return <div className="flex space-x-2 items-center">{sec.name}</div>;
  };

  return (
    <Filter
      Icon={Earth}
      items={allItems}
      selected={selected}
      onStateUpdated={setFilterState}
      onFiltersUpdated={_onFilterUpdated}
      printLabel={printLabel}
      filterState={filterState}
      useIdAsString={true}
      filterName={'Buyer Language'}
      hideSearch={true}
      locked={locked}
      lockedMessage={'Book a demo to access language options'}
    />
  );
}
