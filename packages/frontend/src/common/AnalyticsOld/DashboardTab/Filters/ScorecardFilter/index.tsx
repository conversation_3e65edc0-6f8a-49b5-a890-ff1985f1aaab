import React, { useState, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContentWithoutPortal as PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  CheckIcon,
  Loader2Icon,
  FilterX,
  Target,
  LockIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';

interface IScorecardsFilterFilterProps {
  current: number[];
  onFiltersUpdated: (tagsIds: number[]) => void;
  locked?: boolean;
  lockedMessage?: string;
}

function ScorecardsFilter({
  current,
  onFiltersUpdated,
  locked,
  lockedMessage,
}: IScorecardsFilterFilterProps) {
  if (!current) {
    current = [];
  }

  const defaultNumberOfResults = 10;

  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );
  const [searchString, setSearchString] = useState<string>('');

  const [open, setOpen] = useState(false);

  const { data: allScorecardsDB, isLoading: isLoadingScorecards } =
    useScorecardConfigsForOrg();

  const allScorecards = allScorecardsDB || [];

  let noMoreSC = false;
  const currentlySelectedScorecards: ScorecardConfigDto[] = [];
  if (allScorecards) {
    if (allScorecards.length > 0) {
      if (allScorecards.length < numberOfResults) {
        noMoreSC = true;
      }
      allScorecards.map((t) => {
        if (t.id) {
          if (current.indexOf(t.id) > -1) {
            currentlySelectedScorecards.push(t);
          }
        }
      });
    }
  }

  const [selected, setSelected] = useState<ScorecardConfigDto[]>(
    currentlySelectedScorecards,
  );
  const [prevSelected, setPrevSelected] = useState<ScorecardConfigDto[]>(
    currentlySelectedScorecards,
  );

  if (
    currentlySelectedScorecards.length != prevSelected.length ||
    JSON.stringify(currentlySelectedScorecards) != JSON.stringify(prevSelected)
  ) {
    setPrevSelected(currentlySelectedScorecards);
    setSelected(currentlySelectedScorecards);
  }

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const resetAgentsForSearch = useRef<boolean>(false);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      resetAgentsForSearch.current = true;
      setNumberOfResults(defaultNumberOfResults);
    }, 200);

    setSearchString(s);
  };

  const clearAll = () => {
    setSelected([]);
    if (onFiltersUpdated) {
      onFiltersUpdated([]);
    }
    setOpen(false);
  };

  const toggleScorecard = (_id: string) => {
    const id = parseInt(_id);

    let newSelection: ScorecardConfigDto[] = [];

    if (selected.find((val) => val.id === id)) {
      newSelection = selected.filter((val) => val.id !== id);
    } else {
      let selectedTagDto = null;
      allScorecards.map((a) => {
        if (a.id == id) {
          selectedTagDto = a;
          return;
        }
      });

      if (selectedTagDto) {
        newSelection = [...selected, selectedTagDto];
      }
    }

    if (onFiltersUpdated) {
      onFiltersUpdated(
        newSelection
          ?.map((a) => a.id)
          .filter((id): id is number => id !== undefined),
      );
    }
    setSelected([...newSelection]);
  };

  const loadMore = () => {
    setNumberOfResults(numberOfResults + 15);
  };

  if (!locked) {
    return (
      <>
        <Popover
          open={open}
          onOpenChange={(o) => {
            setOpen(o);
          }}
        >
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="border w-full"
              style={{ justifyContent: 'start' }}
            >
              <Target className="mr-2 h-4 w-4" />
              Scorecards
              {selected.length > 0 && (
                <>
                  <Separator orientation="vertical" className="mx-2 h-4" />
                  <div className="space-x-1 lg:flex">
                    <Badge
                      variant="secondary"
                      className="rounded-sm px-1 font-normal"
                    >
                      {selected.length} selected
                    </Badge>
                  </div>
                </>
              )}
              <div className="flex-1"></div>
              <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent align="start" className="p-0">
            <Command>
              <CommandInput
                placeholder="Search scorecards..."
                className="h-9"
                value={searchString}
                onValueChange={filterResults}
              />
              <CommandList>
                <CommandGroup heading="Scorecards">
                  {allScorecards.map((v) => (
                    <CommandItem
                      key={v.id}
                      value={String(v.id)}
                      onSelect={toggleScorecard}
                    >
                      <div
                        className={cn(
                          'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                          selected?.find((val) => val.id === v.id)
                            ? 'bg-primary text-primary-foreground'
                            : 'opacity-50 [&_svg]:invisible',
                        )}
                      >
                        <CheckIcon className={cn('h-4 w-4')} />
                      </div>
                      <div className="flex space-x-2 items-center">
                        <div className="capitalize">{v.tag}</div>
                      </div>
                    </CommandItem>
                  ))}

                  {isLoadingScorecards ? (
                    <CommandItem className="justify-center text-center">
                      <Loader2Icon className="animate-spin" />
                    </CommandItem>
                  ) : (
                    <>
                      {allScorecards.length > 0 ? (
                        <>
                          {!noMoreSC && (
                            <CommandItem
                              onSelect={loadMore}
                              className="justify-center text-center"
                            >
                              More...
                            </CommandItem>
                          )}
                        </>
                      ) : (
                        <CommandItem className="justify-center text-center">
                          No scorecard found
                        </CommandItem>
                      )}
                    </>
                  )}
                </CommandGroup>
              </CommandList>
              {selected?.length > 0 && (
                <>
                  <CommandSeparator />
                  <CommandGroup>
                    <CommandItem
                      onSelect={clearAll}
                      className="justify-center text-center"
                    >
                      <FilterX size="14" />
                      &nbsp;Clear filters
                    </CommandItem>
                  </CommandGroup>
                </>
              )}
            </Command>
          </PopoverContent>
        </Popover>
      </>
    );
  } else {
    return (
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              className="border-dashed opacity-45"
            >
              <Target className="mr-2 h-4 w-4" />
              Scorecards
              <Separator orientation="vertical" className="mx-2 h-4" />
              <LockIcon className="w-4 h-4 text-muted-foreground" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>{lockedMessage}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
}

export default React.memo(ScorecardsFilter);
