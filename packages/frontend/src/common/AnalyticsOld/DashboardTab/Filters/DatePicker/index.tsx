import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, CaretSortIcon } from '@radix-ui/react-icons';
import { Calendar } from '@/components/ui/calendar';
import { useMemo, useState } from 'react';
import dayjs from 'dayjs';

interface IProps {
  current?: Date;
  onUpdate: (d: Date) => void;
  minDate?: Date;
}
export default function DatePicker({ current, onUpdate, minDate }: IProps) {
  const [open, setOpen] = useState(false);
  const [trigger, setTrigger] = useState<any>(null);
  const triggerBottom = useMemo(
    () => trigger?.offsetTop + trigger?.offsetHeight + 20,
    [trigger],
  );

  const date = current ? dayjs(current) : undefined;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild ref={setTrigger}>
        <div className="flex items-center border p-2 rounded-lg cursor-pointer">
          <div className="flex-1 mr-1">{date?.format('MM/DD/YYYY')}</div>
          <div>
            <CalendarIcon className="h-[16px] w-[16px] text-muted-foreground" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        avoidCollisions={true}
        className="py-0 flex justify-center"
      >
        <Calendar
          initialFocus
          mode="single"
          selected={current}
          onSelect={(data) => {
            if (data) {
              onUpdate(data);
              setOpen(false);
            }
          }}
          numberOfMonths={1}
          fromDate={minDate}
        />
      </PopoverContent>
    </Popover>
  );
}
