import { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { CalendarIcon, CaretSortIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';
import {
  AnalyticsFilterDateRange,
  DateFilterType,
  ANALYTICS_DATE_RANGES,
} from '@/lib/AnalyticsOld/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import { SlidersHorizontal } from 'lucide-react';

interface IDatesFilterProps {
  current: DateFilterType;
  onDatesUpdated: (n: DateFilterType) => void;
}

export default function DatesFilter({
  current,
  onDatesUpdated,
}: IDatesFilterProps) {
  if (!current.range) {
    current.range = AnalyticsFilterDateRange.THIS_MONTH;
  }

  const [dateRange, setDateRange] = useState<DateRange>({
    from: current.fromDate,
    to: current.toDate,
  });
  const [rangeType, setRangeType] = useState<AnalyticsFilterDateRange>(
    current.range,
  );

  const updateFilter = (
    from: Date,
    to: Date,
    range: AnalyticsFilterDateRange,
  ) => {
    onDatesUpdated({ fromDate: from, toDate: to, range: range });
  };

  if (current.range == AnalyticsFilterDateRange.CUSTOM) {
    return (
      <div className="flex items-center px-1">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              id="dateRange"
              variant={'outline'}
              className={cn(
                'w-[268px] border justify-start text-left font-normal',
                !dateRange.from && !dateRange.to && 'text-muted-foreground',
              )}
            >
              <CalendarIcon className="mr-2 h-6 w-6" />
              {dateRange.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, 'LLL dd, y')} -{' '}
                    {format(dateRange.to, 'LLL dd, y')}
                  </>
                ) : (
                  format(dateRange.from, 'LLL dd, y')
                )
              ) : (
                <span>Pick a date range</span>
              )}
              <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={dateRange.from}
              selected={dateRange}
              onSelect={(data) => {
                if (data) {
                  setDateRange(data as DateRange);
                  setRangeType(AnalyticsFilterDateRange.CUSTOM);
                  const { from, to } = data;
                  if (from && to) {
                    onDatesUpdated({
                      fromDate: from,
                      toDate: to,
                      range: AnalyticsFilterDateRange.CUSTOM,
                    });
                  }
                }
              }}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
        <div
          className="ml-2 cursor-pointer"
          onClick={() => {
            setRangeType(AnalyticsFilterDateRange.THIS_MONTH);
            updateFilter(
              new Date(),
              new Date(),
              AnalyticsFilterDateRange.THIS_MONTH,
            );
          }}
        >
          <SlidersHorizontal size={18} />
        </div>
      </div>
    );
  } else {
    return (
      <div className="flex intems-center px-1">
        <Select
          onValueChange={(v: string) => {
            setRangeType(v as AnalyticsFilterDateRange);
            updateFilter(
              dateRange.from || new Date(),
              dateRange.to || new Date(),
              v as AnalyticsFilterDateRange,
            );
          }}
          value={rangeType}
        >
          <SelectTrigger>
            <div className="flex items-center">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {ANALYTICS_DATE_RANGES?.map((v) => {
                if (v.value == rangeType) {
                  return v.label;
                }
              })}
            </div>
          </SelectTrigger>
          <SelectContent>
            {ANALYTICS_DATE_RANGES?.map((v) => {
              return (
                <SelectItem key={v.value} value={v.value}>
                  {v.label}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
    );
  }
}
