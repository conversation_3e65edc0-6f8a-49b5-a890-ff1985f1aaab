import React, { useState, useEffect, useRef } from 'react';
import { AgentDto, AgentFolderDto } from '@/lib/Agent/types';
import { BotIcon, ChevronsUpDown } from 'lucide-react';
import {
  useAgentsFolders,
  useAgentsWithNoFolder,
} from '@/hooks/useAgentsFolders';
import { cn } from '@/lib/utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import Folder from './Folder/index';
import { MagnifyingGlassIcon } from '@radix-ui/react-icons';
import { Loader2Icon } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import AgentAvatar from '@/components/Avatars/Agent';
import useOrgAgents from '@/hooks/useOrgAgents';
import Agent from './Folder/Agent/index';

interface IProps {
  current: number[];
  onBuyersUpdated: (buyers: number[]) => void;
  isRadioSelect?: boolean;
  keepSelectionInPlace?: boolean;
  displaySelectedName?: boolean;
  className?: string;
  hideClearBtn?: boolean;
  hideSearch?: boolean;
  selectLimit?: number;
}

export default function BuyersFilterWithFolders({
  current,
  onBuyersUpdated,
  isRadioSelect,
  className,
  hideSearch,
  selectLimit,
}: IProps) {
  if (!current) {
    current = [];
  }

  const [startAgentsWithNoFilter, setStartAgentsWithNoFilter] =
    useState<number>(0);
  const [open, setOpen] = useState(false);
  const [folderForAgentsWithNoFolder, setFolderForAgentsWithNoFolder] =
    useState<AgentFolderDto>();
  // const { data: currentlySelectedBuyers, isLoading: isLoadingCurrentlySelectedBuyers } = useOrgAgentsById(current);
  const {
    data: dbFolders,
    isLoading: isLoadingFolders,
    refetch: refetchTree,
    isRefetching: isRefetchingFolders,
  } = useAgentsFolders();
  const { data: agentsWithNoFolders, isLoading: isLoadingAgentsWithNoFolders } =
    useAgentsWithNoFolder(startAgentsWithNoFilter);

  /*******************************/
  /************* INIT ************/
  /*******************************/

  useEffect(() => {
    if (!isLoadingAgentsWithNoFolders && agentsWithNoFolders) {
      const children = [];
      for (const a of agentsWithNoFolders) {
        children.push({
          agent: a,
        });
      }
      setFolderForAgentsWithNoFolder({
        name: 'No folder',
        children: children,
      } as AgentFolderDto);
    }
  }, [agentsWithNoFolders, isLoadingAgentsWithNoFolders]);

  /*******************************/
  /************ UTILS ************/
  /*******************************/

  const printLabel = (agent: AgentDto) => {
    return (
      <div className="flex space-x-2 items-center">
        <AgentAvatar className="w-6 h-6" agent={agent} />

        <div className="capitalize">
          {agent?.firstName || ''} {agent?.lastName || ''}
        </div>
      </div>
    );
  };

  /*******************************/
  /********** ACITONS ************/
  /*******************************/

  const [searchString, setSearchString] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [debouncedSearchString, setDebouncedSearchString] = useState<string>('');
  const [_from, _setFrom] = useState<number>(0);
  const _numberOfResults = 20
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [allBuyers, setAllBuyers] = useState<AgentDto[]>([]);

  const { data: allBuyersDB, isLoading: isLoadingBuyers, isFetching: isFetchingBuyers } = useOrgAgents(
    undefined,
    true,
    _from,
    _numberOfResults,
    debouncedSearchString,
    undefined,
    true,
  );

  useEffect(() => {
    let hasMore = true;
    if (!isLoadingBuyers && allBuyersDB) {
      if (allBuyersDB.length < _numberOfResults) {
        hasMore = false;
      }
      setAllBuyers([...allBuyersDB]);
      setIsSearching(false);
      setHasMore(hasMore);
    }
  }, [isLoadingBuyers, allBuyersDB]);

  const search = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      setDebouncedSearchString(s);
    }, 400);

    setSearchString(s);
  };

  const loadMore = () => {
    _setFrom(_from + _numberOfResults);
  };

  const onToggleAgent = (agentId: number) => {
    const tmp: number[] = [];
    let add = true;
    for (const id of current) {
      if (id !== agentId) {
        tmp.push(id);
      } else {
        add = false;
      }
    }

    if (add && selectLimit === 1) {
      onBuyersUpdated([agentId]);
      setOpen(false);
      return;
    }

    if (add) {
      if (selectLimit && current.length >= selectLimit) return;
      tmp.push(agentId);
    }

    onBuyersUpdated(tmp);
  };

  /*******************************/
  /************* RENDER **********/
  /*******************************/

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
      modal={true}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('border w-full', className)}
          style={{ justifyContent: 'start' }}
        >
          <BotIcon className="mr-2 h-4 w-4" />
          {isRadioSelect ? 'Buyer' : 'Buyers'}
          {current.length > 0 && (
            <div className="flex items-center font-normal">
              <Separator orientation="vertical" className="mx-2 h-4" />
              {current.length} selected
            </div>
          )}
          <div className="flex-1"></div>
          <ChevronsUpDown className="ml-2 text-muted-foreground" size={16} />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="p-0"
        style={{ width: 'var(--radix-popover-trigger-width)' }}
      >
        {!hideSearch && (
          <div className="flex items-center border-b px-3 text-xs">
            <MagnifyingGlassIcon className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              placeholder="Search..."
              className="h-10 py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              value={searchString}
              onChange={(e) => {
                search(e.target.value);
              }}
            />
            {(isLoadingBuyers || isFetchingBuyers || isRefetchingFolders || isLoadingFolders) && (
              <Loader2Icon className="absolute right-2 animate-spin text-muted-foreground" />
            )}
          </div>
        )}

        <div className="text-xs text-muted-foreground px-3 pt-3 pb-2 font-semibold">
          {isRadioSelect ? 'Buyer' : 'Buyers'}
        </div>

        {searchString.length > 0 && (<div className="max-h-[300px] lg:max-h-[400px] overflow-y-auto">
          {allBuyers.map(a => {
              return (
                <div className="ml-3">
                  <Agent
                    key={a.id}
                    agent={a}
                    selectedAgentsIds={current}
                    isRadioSelect={isRadioSelect}
                    canSelectMultiple={selectLimit === undefined || selectLimit > 1}
                    onToggleAgent={onToggleAgent}
                  />
                </div>
              );
            })}

            {allBuyers.length === 0 && !isLoadingBuyers && !isFetchingBuyers && (
              <div className="flex justify-center text-center text-xs text-muted-foreground m-6">
                No agents found
              </div>
            )}

            {hasMore && (
              <div
                onClick={loadMore}
                  className="text-center text-muted-foreground text-sm m-1 p-1 cursor-pointer hover:bg-muted hover:text-black rounded-sm"
                >
                More...
              </div>
            )}
          </div>
        )}

        <div className="max-h-[400px] lg:max-h-[500px] overflow-y-auto">
          {searchString.length === 0 && dbFolders?.map((f) => {
            if (!f.agent) {
              return (
                <Folder
                  key={f.id}
                  folder={f}
                  selectedAgentsIds={current}
                  onToggleAgent={onToggleAgent}
                  isRadioSelect={isRadioSelect}
                  canSelectMultiple={selectLimit === undefined || selectLimit > 1}
                  className={'border-b p-1'}
                />
              );
            }
          })}
          {searchString.length === 0 && agentsWithNoFolders &&
            agentsWithNoFolders?.length > 0 &&
            folderForAgentsWithNoFolder && (
              <Folder
                folder={folderForAgentsWithNoFolder}
                selectedAgentsIds={current}
                onToggleAgent={onToggleAgent}
                isRadioSelect={isRadioSelect}
                canSelectMultiple={selectLimit === undefined || selectLimit > 1}
                hideFolderIcon={true}
                className={'p-1'}
              />
            )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
