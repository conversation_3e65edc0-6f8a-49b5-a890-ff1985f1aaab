import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AgentCallType, AgentDto, AgentFolderDto } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import Agent from './Agent';

interface IProps {
  folder: AgentFolderDto;
  selectedAgentsIds: number[];
  isRadioSelect?: boolean;
  onToggleAgent: (agentId: number) => void;
  hideFolderIcon?: boolean;
  className?: string;
  canSelectMultiple?: boolean;
}

export default function Folder({
  folder,
  selectedAgentsIds,
  isRadioSelect,
  onToggleAgent,
  hideFolderIcon,
  className,
  canSelectMultiple,
}: IProps) {
  const hasContent = folder.children && folder.children.length > 0;
  const [isOpen, setIsOpen] = useState<boolean>(false);

  /***********************************/
  /************ ACTIONS **************/
  /***********************************/

  const toggleOpenFolder = () => {
    if (hasContent) {
      setIsOpen(!isOpen);
    }
  };

  /***********************************/
  /************ RENDER ***************/
  /***********************************/

  return (
    <div className={cn('text-sm', className)}>
      <div
        className="flex items-center p-1 cursor-pointer hover:bg-gray-50"
        onClick={toggleOpenFolder}
      >
        <motion.div
          animate={{
            rotate: isOpen ? 90 : 0,
          }}
          transition={{
            ease: 'easeOut',
            duration: 0.2,
          }}
          className={cn('text-muted-foreground mr-2', {
            'opacity-0': !hasContent,
          })}
        >
          <ChevronRight size={16} />
        </motion.div>
        {!hideFolderIcon && (
          <div className="text-muted-foreground pr-2">
            {isOpen ? (
              <Image
                src="/images/icons/folderOpen.svg"
                alt="Folder Open"
                width={16}
                height={16}
              />
            ) : (
              <Image
                src="/images/icons/folderClosed.svg"
                alt="Folder Closed"
                width={16}
                height={16}
              />
            )}
          </div>
        )}

        <div className="flex-1 mr-3 flex items-center">{folder.name}</div>
      </div>
      <AnimatePresence>
        {folder.children && isOpen && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ duration: 0.2 }}
            className="ml-6 overflow-auto max-h-[300px] lg:max-h-[400px]"
          >
            {folder.children.map((f, i) => {
              const isLast = (folder?.children?.length || 0) == i + 1;
              if (f.agent) {
                const a: AgentDto = f.agent;
                const Icon =
                  CALL_TYPE_TO_ICON?.[
                    a?.callType as keyof typeof CALL_TYPE_TO_ICON
                  ]?.Icon;
                return (
                  <Agent
                    key={f.id}
                    agent={a}
                    selectedAgentsIds={selectedAgentsIds}
                    onToggleAgent={onToggleAgent}
                    canSelectMultiple={canSelectMultiple}
                  />
                );
              } else {
                return (
                  <Folder
                    key={f.id}
                    folder={f}
                    selectedAgentsIds={selectedAgentsIds}
                    onToggleAgent={onToggleAgent}
                    canSelectMultiple={canSelectMultiple}
                  />
                );
              }
            })}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
