/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, ChevronsUpDown, Lock } from 'lucide-react';
import { useMemo, useState } from 'react';
import {
  LEARNING_MODULES_TASK_TYPES_LABELS,
  LearningModuleTaskType,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';

export interface IProps {
  placeholder?: string;
  current: LearningModuleTaskType[];
  onUpdate: (stats: string[]) => void;
  isRadioSelect?: boolean;
}

export default function LearningModuleTaskTypeFilter({
  placeholder,
  current,
  onUpdate,
  isRadioSelect,
}: IProps) {
  const [open, setOpen] = useState(false);

  /**************************************/
  /************** FE UTILS  *************/
  /**************************************/

  const [trigger, setTrigger] = useState<any>(null);
  const triggerBottom = useMemo(
    () => trigger?.offsetTop + trigger?.offsetHeight + 20,
    [trigger],
  );

  const toggle = (opt: LearningModuleTaskType) => {
    const tmp: string[] = [];

    if (isRadioSelect) {
      if (current.indexOf(opt) < 0) {
        tmp.push(opt);
      }
    } else {
      let add = true;
      for (const o of current) {
        if (o == opt) {
          add = false;
        } else {
          tmp.push(o);
        }
      }
      if (add) {
        tmp.push(opt);
      }
    }

    if (onUpdate) {
      onUpdate(tmp);
    }
  };
  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
      modal={true}
    >
      <PopoverTrigger asChild ref={setTrigger}>
        <div className="border border-gray-200 bg-white rounded-lg p-2 flex items-center cursor-pointer">
          <div className="flex-1">
            {current.length == 0 && (
              <div className="text-sm text-muted-foreground">
                {placeholder ? placeholder : 'Select task type'}
              </div>
            )}
            {current.length > 0 && (
              <div className="text-sm flex items-center">
                {LEARNING_MODULES_TASK_TYPES_LABELS.map((s: any) => {
                  const isChecked = current.indexOf(s.id) > -1;
                  if (isChecked) {
                    return <div key={s.id}>{s.label}</div>;
                  }
                })}
              </div>
            )}
          </div>
          <div className="ml-2 mr-2">
            <ChevronsUpDown className="text-muted-foreground" size={16} />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="p-0"
        style={{
          maxHeight: `calc(100vh - ${triggerBottom || 0}px)`,
          width: 'var(--radix-popover-trigger-width)',
          overflow: 'auto',
        }}
      >
        <div className="p-3 pb-0">
          {LEARNING_MODULES_TASK_TYPES_LABELS.map((s: any) => {
            const isChecked = current.indexOf(s.id) > -1;
            const isLocked = s.locked;
            return (
              <div
                key={s.id}
                className={cn('pb-3', {
                  'opacity-50': isLocked,
                  'cursor-pointer': !isLocked,
                })}
              >
                <div
                  className={cn('flex items-center')}
                  onClick={() => {
                    if (!isLocked) {
                      toggle(s.id);
                    }
                  }}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center  border border-primary',
                      isChecked
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                      {
                        'rounded-full': isRadioSelect,
                        'rounded-sm': !isRadioSelect,
                      },
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex-1 text-sm flex items-center">
                    <div>{s.label}</div>
                    {isLocked && (
                      <div className="ml-1">
                        <Lock size={16} />
                      </div>
                    )}
                  </div>
                </div>
                {isLocked && (
                  <div className="text-xs ml-6 mt-1">Coming soon!</div>
                )}
              </div>
            );
          })}
        </div>
      </PopoverContent>
    </Popover>
  );
}
