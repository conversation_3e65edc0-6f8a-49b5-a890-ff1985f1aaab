import { useEffect, useState } from 'react';
import useDashboardWidgetTemplates from '@/hooks/useDashboardWidgetTemplates';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DashboardWidgetTemplateDto,
  DashboardWidgetTemplateWidgetType,
} from '@/lib/AnalyticsOld/types';
import { LineChart, SquareGanttChart } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface IChartsTemplatesProps {
  open: boolean;
  onOpenChange: () => void;
  onWidgetClick: (widget: DashboardWidgetTemplateDto) => void;
}

export default function AddChart({
  open,
  onOpenChange,
  onWidgetClick,
}: IChartsTemplatesProps) {
  const [widgets, setWidgets] = useState<DashboardWidgetTemplateDto[]>([]);
  const [filteredWidgets, setFilteredWidgets] = useState<
    DashboardWidgetTemplateDto[]
  >([]);
  const [searchLbl, setSearchLbl] = useState<string>('');
  const { data: widgetsDb, isLoading } = useDashboardWidgetTemplates();

  useEffect(() => {
    if (!isLoading && widgetsDb) {
      setWidgets(widgetsDb);
      setFilteredWidgets(widgetsDb);
    }
  }, [isLoading, widgetsDb]);

  const addWidget = (widget: DashboardWidgetTemplateDto) => {
    onWidgetClick(widget);
  };

  const searchWidget = (s: string) => {
    setSearchLbl(s);

    if (s == '') {
      setFilteredWidgets(widgets);
    } else {
      setFilteredWidgets(
        widgets.filter((widget) => {
          return (
            widget.name.toLowerCase().includes(s.toLowerCase()) ||
            widget.instructions.toLowerCase().includes(s.toLowerCase())
          );
        }),
      );
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="flex flex-col">
        <SheetHeader>
          <SheetTitle>Select widget:</SheetTitle>
        </SheetHeader>
        <SheetDescription>
          <Input
            className="text-black"
            placeholder="Search widgets"
            value={searchLbl}
            onChange={(e) => {
              searchWidget(e.target.value);
            }}
          />
        </SheetDescription>
        <ScrollArea className="grow">
          {filteredWidgets.map((widget) => {
            return (
              <div
                key={widget.id}
                className="cursor-pointer border p-4 hover:border-slate-500 rounded-md mb-4"
                onClick={() => {
                  addWidget(widget);
                }}
              >
                <div className="flex intems-center">
                  <div className="mr-1 pt-1">
                    {widget.widgetType ==
                      DashboardWidgetTemplateWidgetType.CHART && (
                      <LineChart size={16} />
                    )}
                    {widget.widgetType ==
                      DashboardWidgetTemplateWidgetType.CARD && (
                      <SquareGanttChart size={16} />
                    )}
                  </div>
                  <div className="font-semibold flex-1">{widget.name}</div>
                </div>
                <div className="text-sm mt-1 whitespace-pre-line">
                  {widget.instructions}
                </div>
              </div>
            );
          })}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}
