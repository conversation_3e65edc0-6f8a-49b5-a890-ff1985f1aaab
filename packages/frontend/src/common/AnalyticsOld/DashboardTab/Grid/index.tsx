import React, {
  useMemo,
  useLayoutEffect,
  useRef,
  useState,
  useCallback,
  useEffect,
} from 'react';
import { Responsive, WidthProvider, Layouts, Layout } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import './styles.css';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import Widget from './Widget';
import { DashboardTemplateWidget } from '@/lib/AnalyticsOld/DashboardTemplates/types';

// UTILS **************************************************

//see https://github.com/metabase/metabase/blob/master/frontend/src/metabase/dashboard/components/grid/GridLayout.jsx

function generateGridBackground(
  h: number,
  margin: any,
  cols: any,
  gridWidth: any,
) {
  const XMLNS = 'http://www.w3.org/2000/svg';
  const [horizontalMargin, verticalMargin] = margin;
  const rowHeight = h + verticalMargin;
  const cellStrokeColor = '#e1e1e1';

  const y = 0;

  const rectangles = [];
  let x = 0;
  let i = 0;
  while (x < gridWidth) {
    x = i * (h + horizontalMargin);
    rectangles.push(
      `<rect stroke='${cellStrokeColor}' stroke-width='0.5' fill='none' x='${x}' y='${y}' width='${h}' height='${h}'/>`,
    );
    i++;
  }

  const svg = [
    `<svg xmlns='${XMLNS}' width='${gridWidth}' height='${rowHeight}' style='margin:${horizontalMargin}px;padding:0px 20px 0px 20px;'>`,
    ...rectangles,
    `</svg>`,
  ].join('');

  return `url("data:image/svg+xml;utf8,${encodeURIComponent(svg)}")`;
}

//************************************************** END UTILS

const ResponsiveReactGridLayout = WidthProvider(Responsive);

interface IGraphProps {
  widgets: DashboardWidgetDto[] | DashboardTemplateWidget[];
  isEditing: boolean;
  onWidgetsUpdated: (
    w: DashboardWidgetDto[] | DashboardTemplateWidget[],
  ) => void;
  onWidgetsDelete: (w: DashboardWidgetDto | DashboardTemplateWidget) => void;
  paddingX?: number;
  doNotFitHeightToScreen?: boolean;
  hideWidgetFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

function Grid({
  widgets,
  isEditing,
  onWidgetsUpdated,
  onWidgetsDelete,
  paddingX,
  doNotFitHeightToScreen,
  hideWidgetFilters,
  overwriteFilters,
}: IGraphProps) {
  if (!paddingX) {
    paddingX = 26;
  }

  const _itemsProps: { [key: number]: any } = [];
  widgets?.map((widget) => {
    _itemsProps[widget.id] = JSON.parse(JSON.stringify(widget));
  });

  const gridContainer = useRef<HTMLDivElement>(null);
  const [background, setBackground] = useState('');
  const [itemsProps, setItemsProps] = useState<{
    [key: number]: DashboardWidgetDto;
  }>({ ..._itemsProps });

  /*********************************/
  /************** EVENTS ***********/
  /*********************************/

  const updateLayout = (layout: Layout[]) => {
    layout.map((l) => {
      const id = parseInt(l.i.substring(1));
      itemsProps[id].props = l;
    });
    setItemsProps({ ...itemsProps });
    onWidgetsUpdated(Object.values(itemsProps));
  };

  useLayoutEffect(() => {
    function updateSize() {
      if (gridContainer.current) {
        const s = gridContainer.current.getBoundingClientRect();

        if (doNotFitHeightToScreen) {
          gridContainer.current.style.height = '1050px';

          // setTimeout(() => {
          //   for (const n in gridContainer.current?.firstElementChild?.childNodes) {
          //     console.log(n);

          //   }
          //   // console.log(gridContainer.current?.firstElementChild?.getBoundingClientRect());
          //   // console.log(gridContainer.current?.childNodes[0]);
          // }, 2000);
        } else {
          gridContainer.current.style.height =
            window.innerHeight - s.y - 10 + 'px';
        }

        if (isEditing) {
          const currenCols = 12;
          const h = 30;
          const bg = generateGridBackground(h, [10, 10], currenCols, s.width);
          setBackground(bg);
        }
      }
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, [isEditing]);

  const updateWidgetInfo = (w: DashboardWidgetDto) => {
    itemsProps[w.id] = w;
    setItemsProps({ ...itemsProps });
    onWidgetsUpdated(Object.values(itemsProps));
  };

  const deleteWidget = (w: DashboardWidgetDto) => {
    onWidgetsDelete(w);
  };

  /*********************************/
  /************** RENDER ***********/
  /*********************************/

  const style = {
    background: isEditing ? background : '',
  };

  const renderWidget = useCallback(
    (widget: DashboardWidgetDto | DashboardTemplateWidget) => {
      if (!widget.isDelete) {
        const infos = itemsProps[widget.id];
        if (!infos.props) {
          infos.props = {};
        }
        if (!isEditing) {
          infos.props.static = true;
        } else {
          infos.props.static = false;
        }
        return (
          <div key={infos.props.i} data-grid={infos.props}>
            <Widget
              widget={widget}
              isEditing={isEditing}
              updateWidgetInfo={updateWidgetInfo}
              deleteWidget={deleteWidget}
              hideWidgetFilters={hideWidgetFilters}
              overwriteFilters={overwriteFilters}
            />
          </div>
        );
      }
    },
    [itemsProps, isEditing, overwriteFilters, hideWidgetFilters],
  );

  // https://github.com/react-grid-layout/react-grid-layout#performance
  const children = useMemo(
    () => widgets?.map(renderWidget),
    [widgets, renderWidget],
  );

  //onResizeStop={ }
  return (
    <div className="relative overflow-auto" ref={gridContainer} style={style}>
      <ResponsiveReactGridLayout
        cols={{ lg: 12, md: 12, sm: 6, xs: 1 }}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480 }}
        rowHeight={30}
        onLayoutChange={(layout: Layout[], layouts: Layouts) => {
          updateLayout(layout);
        }}
        autoSize={false}
        draggableCancel=".nodrag"
        containerPadding={[paddingX, 0]}
      >
        {children}
      </ResponsiveReactGridLayout>
    </div>
  );
}

export default React.memo(Grid);
