import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import { DonutChart, List, ListItem } from '@tremor/react';

// Define StatusData interface to strongly type currentData
interface StatusData {
  name: string;
  amount: number;
  color: string;
  share: string;
}

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function HbActiveUsers({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  // const MenuComponent: React.ComponentType | JSX.Element = !hideFilters && DefaultChartDropDownMenu;
  const MenuComponent = hideFilters ? undefined : DefaultChartDropDownMenu;
  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );

  const [currentData, setCurrentData] = useState<StatusData[]>(data);

  useEffect(() => {
    if (!isLoading && data) {
      setCurrentData(data);
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : currentData.length === 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          No Users to display for this period
        </div>
      ) : (
        <div className="w-full">
          <DonutChart
            className="mt-8"
            data={currentData}
            category="amount"
            index="name"
            showTooltip={true}
            colors={currentData.map((item) =>
              item.color.replace('bg-', '').replace('-500', ''),
            )}
          />
          <p className="mt-8 flex items-center justify-between text-tremor-label text-tremor-content dark:text-dark-tremor-content">
            <span>Status</span>
            <span>Users / Percentage</span>
          </p>
          <List className="mt-2">
            {currentData.map((item: StatusData) => (
              <ListItem key={item.name} className="space-x-6">
                <div className="flex items-center space-x-2.5 truncate">
                  <span
                    className={classNames(
                      item.color,
                      'size-2.5 shrink-0 rounded-sm',
                    )}
                  />
                  <span className="truncate dark:text-dark-tremor-content-emphasis">
                    {item.name}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium tabular-nums text-tremor-content-strong dark:text-dark-tremor-content-strong">
                    {item.amount}
                  </span>
                  <span className="rounded-tremor-small bg-tremor-background-subtle px-1.5 py-0.5 text-tremor-label font-medium tabular-nums text-tremor-content-emphasis dark:bg-dark-tremor-background-subtle dark:text-dark-tremor-content-emphasis">
                    {item.share}
                  </span>
                </div>
              </ListItem>
            ))}
          </List>
        </div>
      )}
    </WidgetCard>
  );
}
