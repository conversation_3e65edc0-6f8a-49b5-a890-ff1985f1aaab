import { useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { TableState } from '@/common/Calls/AIRoleplay/List/common';
import CallsTable from '@/common/Calls/AIRoleplay/List/table';
import useUserSession from '@/hooks/useUserSession';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function SymCallCallHistory({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { userId } = useUserSession();

  let reps: number[] = [];
  if (userId) {
    reps = [userId];
  }

  const [tableState, setTableState] = useState<TableState>(
    new TableState(
      undefined,
      undefined, //
      [],
      reps,
    ),
  );

  return (
    <div
      className={
        'h-full w-full py-4 rounded-xl text-card-foreground flex flex-col '
      }
    >
      <div className="font-semibold mb-6">Call History</div>
      <div>
        <CallsTable
          tableState={tableState}
          updateTableState={setTableState}
          updatePagination={(from: number, numberOfResults: number) => {
            tableState.from = from;
            tableState.numberOfResults = numberOfResults;

            setTableState({ ...tableState });
          }}
        />
      </div>
    </div>
  );
}
