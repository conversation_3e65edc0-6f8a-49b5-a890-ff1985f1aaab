import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import useDashboardChartData from '@/hooks/useDashboardChartData';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import AnalyticsService from '@/lib/AnalyticsOld';
import useWidgetCard from '../WidgetCard/useWidgetCard';

interface IProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function TotalCalls({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IProps) {
  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [s, setS] = useState<string>('s');

  useEffect(() => {
    if (!isLoading && (data || data == 0)) {
      if (data == 1) {
        setS('');
      }
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || data == undefined ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : (
        <div className="w-full h-full flex items-center justify-center font-bold text-2xl">
          {data} call{s}
        </div>
      )}
    </WidgetCard>
  );
}
