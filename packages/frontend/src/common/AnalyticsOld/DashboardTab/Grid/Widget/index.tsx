import React, { ReactElement } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';

import CallVolumeHistory from './WidgetTypes/CallVolumeHistory';
import AvgBotsCalledPerDay from './WidgetTypes/AvgBotsCalledPerDay';
import TotalCalls from './WidgetTypes/TotalCalls';
import TotalCallsDuration from './WidgetTypes/TotalCallsDuration';
import ActiveReps from './WidgetTypes/ActiveReps';
import ScorecardsStats from './WidgetTypes/ScorecardsStats';
import ScorecardsTrends from './WidgetTypes/ScorecardsTrends';
import ScorecardsSectionsTrends from './WidgetTypes/ScorecardsSectionsTrends';
import FullReport from './WidgetTypes/FullReport';
import AvgImprovementVsCallsPerRep from './WidgetTypes/AvgImprovementVsCallsPerRep';
import ImprovementPerCriterionByBot from './WidgetTypes/ImprovementPerCriterionByBot';
import TopUser from './WidgetTypes/TopUser';
// import QuestionsObjectionsVolume from './WidgetTypes/ScorecardsVolume';
// import QuestionsDisputedStats from './WidgetTypes/ScorecardsVolume';
import ScorecardsVolume from './WidgetTypes/ScorecardsVolume';
import {
  DashboardTemplateWidget,
  WidgetType,
} from '@/lib/AnalyticsOld/DashboardTemplates/types';
import PerformanceComparison from './WidgetTypes/PerformanceComparison';
import ProficiencyComparison from './WidgetTypes/ProficiencyComparison';
// import RepOverallPerformance from './WidgetTypes/RepOverallPerformance';
import CallsCompletedPerRep from './WidgetTypes/CallsCompletedPerRep';
import AvgCallScorePerRep from './WidgetTypes/AvgCallScorePerRep';
import WeeklyCallVolumeVsGoalPerRep from './WidgetTypes/WeeklyCallVolumeVsGoalPerRep';
import LearningModulePerRep from './WidgetTypes/LearningModulesPerRep';
import ObjectionFreqHandPerRep from './WidgetTypes/ObjectionFreqHandPerRep';
import CriterionImprPerRep from './WidgetTypes/CriterionImprPerRep';
import ObjectionImprPerRep from './WidgetTypes/ObjectionImprPerRep';
import WeeklyCallScoreVsGoalPerRep from './WidgetTypes/WeeklyCallScoreVsGoalPerRep';
import RealCallsCriterionImprPerRep from './WidgetTypes/CriterionImprPerRep/realCalls';
import SymCallCallHistory from './WidgetTypes/CallHistory';
import RealCallCallHistory from './WidgetTypes/CallHistory/realCalls';
import CallsGroupedSummaryByRep from './WidgetTypes/CallsGroupedSummaryByRep';
import CallVolumeHistoryByRepWithPassRate from './WidgetTypes/CallVolumeHistoryByRepWithPassRate';
import ObjectionsByRep from './WidgetTypes/ObjectionsByRep';
import SimCallRepsOfConcern from './WidgetTypes/RepsOfConcern';
import CriterionByRep from './WidgetTypes/CriterionByRep';
import RcCriterionByRep from './WidgetTypes/RealCalls/CriterionsByRep';
import RcObjectionsByRep from './WidgetTypes/RealCalls/ObjectionsByRep';
import RcOverallSummaryByRep from './WidgetTypes/RealCalls/OverallSummaryByRep';
import RcPassRateByRep from './WidgetTypes/RealCalls/PassRateByRep';
import RcProgressOverTime from './WidgetTypes/RealCalls/ProgressOverTime';
import RcRepsOfConcern from './WidgetTypes/RealCalls/RepsOfConcern';
import HbActiveUsers from './WidgetTypes/ActiveUsers';
import SimCallTalkStats from './WidgetTypes/TalkStats';
import RcTalkStats from './WidgetTypes/RealCalls/TalkStats';



interface IWidgetProps {
  widget: DashboardWidgetDto | DashboardTemplateWidget;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideWidgetFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

// TODO: we need to more carefully distinguish between DashboardWidgetDto and DashboardTemplateWidget
type WidgetComponent = (props: any) => ReactElement;

export const MapWidgetToType: Record<WidgetType, WidgetComponent> = {
  [WidgetType.CALL_VOLUME_HISTORY_BY_REP]: CallVolumeHistory,
  [WidgetType.CALL_VOLUME_HISTORY_BY_TEAM]: CallVolumeHistory,
  [WidgetType.AVG_BOTS_CALLED_PER_DAY_BY_REP]: AvgBotsCalledPerDay,
  [WidgetType.AVG_BOTS_CALLED_PER_DAY_BY_TEAM]: AvgBotsCalledPerDay,
  [WidgetType.TOTAL_CALLS]: TotalCalls,
  [WidgetType.TOTAL_CALLS_DURATION]: TotalCallsDuration,
  [WidgetType.ACTIVE_REPS]: ActiveReps,
  [WidgetType.SCORECARDS_STATS]: ScorecardsStats,
  [WidgetType.SCORECARDS_TRENDS_BY_REP]: ScorecardsTrends,
  [WidgetType.SCORECARDS_TRENDS_BY_TEAM]: ScorecardsTrends,
  [WidgetType.SCORECARDS_SECTIONS_TRENDS]: ScorecardsSectionsTrends,
  [WidgetType.SCORECARDS_CRITERIONS_TRENDS]: ScorecardsSectionsTrends,
  [WidgetType.FULL_REPORT]: FullReport,
  [WidgetType.AVG_IMPROVEMENT_VS_CALLS_PER_REP]: AvgImprovementVsCallsPerRep,
  [WidgetType.IMPROVEMENT_PER_CRITERION_BY_BOT]: ImprovementPerCriterionByBot,
  [WidgetType.TOP_USER]: TopUser,
  [WidgetType.SCORECARDS_VOLUME]: ScorecardsVolume,
  [WidgetType.PERFORMANCE_COMPARISON]: PerformanceComparison,
  [WidgetType.PROFICIENCY_COMPARISON]: ProficiencyComparison,
  // [WidgetType.REP_OVERALL_PERFORMANCE]: RepOverallPerformance,
  [WidgetType.REAL_CALL_VOLUME_BY_REP]: CallsCompletedPerRep,
  [WidgetType.SIM_CALL_VOLUME_BY_REP]: CallsCompletedPerRep,
  [WidgetType.REAL_CALL_AVG_SCORE_BY_REP]: AvgCallScorePerRep,
  [WidgetType.SIM_CALL_AVG_SCORE_BY_REP]: AvgCallScorePerRep,
  [WidgetType.REAL_CALL_WEEKLY_CALL_VOLUME_VS_GOAL_PER_REP]:
    WeeklyCallVolumeVsGoalPerRep,
  [WidgetType.SIM_CALL_WEEKLY_CALL_VOLUME_VS_GOAL_PER_REP]:
    WeeklyCallVolumeVsGoalPerRep,
  [WidgetType.REAL_CALL_WEEKLY_CALL_SCORE_VS_GOAL_PER_REP]:
    WeeklyCallScoreVsGoalPerRep,
  [WidgetType.SIM_CALL_WEEKLY_CALL_SCORE_VS_GOAL_PER_REP]:
    WeeklyCallScoreVsGoalPerRep,
  [WidgetType.LEARNING_MODULES_PER_REP]: LearningModulePerRep,
  [WidgetType.OBJECTION_FREQ_HAND_PER_REP]: ObjectionFreqHandPerRep,
  [WidgetType.SIM_CALL_CRITERION_SUGGESTIONS_BY_REP]: CriterionImprPerRep,
  [WidgetType.REAL_CALL_CRITERION_SUGGESTIONS_BY_REP]:
    RealCallsCriterionImprPerRep,
  [WidgetType.SIM_CALL_OBJECTION_IMPR_PER_REP]: ObjectionImprPerRep,
  [WidgetType.SIM_CALL_CALL_HISTORY]: SymCallCallHistory,
  [WidgetType.REAL_CALL_CALL_HISTORY]: RealCallCallHistory,
  [WidgetType.SIM_CALL_GROUPED_SUMMARY_BY_REP]: CallsGroupedSummaryByRep,
  [WidgetType.SIM_CALL_VOLUME_BY_REP_WITH_PASS_RATE]:
    CallVolumeHistoryByRepWithPassRate,
  [WidgetType.SIM_CALL_OBJECTIONS_BY_REP]: ObjectionsByRep,
  [WidgetType.SIM_CALL_CRITERION_BY_REP]: CriterionByRep,
  [WidgetType.SIM_CALL_REPS_OF_CONCERN]: SimCallRepsOfConcern,
  [WidgetType.REAL_CALL_OBJECTIONS_BY_REP]: RcObjectionsByRep,
  [WidgetType.REAL_CALL_CRITERION_BY_REP]: RcCriterionByRep,
  [WidgetType.REAL_CALL_GROUPED_SUMMARY_BY_REP]: RcOverallSummaryByRep,
  [WidgetType.REAL_CALL_VOLUME_BY_REP_WITH_PASS_RATE]: RcPassRateByRep,
  [WidgetType.REAL_CALL_PROGRESS_OVER_TIME]: RcProgressOverTime,
  [WidgetType.REAL_CALL_REPS_OF_CONCERN]: RcRepsOfConcern,
  [WidgetType.ACTIVE_USERS]: HbActiveUsers,
  [WidgetType.SIM_CALL_TALK_STATS]: SimCallTalkStats,
  [WidgetType.REAL_CALL_TALK_STATS]: RcTalkStats,
  
} as const;

function Widget({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideWidgetFilters,
  overwriteFilters,
}: IWidgetProps) {
  const ContentComponent = MapWidgetToType[widget.type];
  if (ContentComponent) {
    return (
      <ContentComponent
        widget={{
          ...widget,
        }}
        isEditing={isEditing}
        updateWidgetInfo={updateWidgetInfo}
        deleteWidget={deleteWidget}
        hideFilters={hideWidgetFilters}
        overwriteFilters={overwriteFilters}
      />
    );
  } else {
    return (
      <div>
        NO WIDGET FOUND FOR {widget.type} (ID: {widget.id} - TEMPLATE:{' '}
        {widget.widgetTemplateId})
      </div>
    );
  }
}

export default React.memo(Widget);
