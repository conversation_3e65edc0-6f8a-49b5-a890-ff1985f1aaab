import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DashboardWidgetDto } from '@/lib/AnalyticsOld/types';
import { Settings, MoreVerticalIcon, Filter } from 'lucide-react';

interface IChartDropDownMenuProps {
  widget: DashboardWidgetDto;
  startEditing: () => void;
}

export default function DefaultChartDropDownMenu({
  widget,
  startEditing,
}: IChartDropDownMenuProps) {
  const hasFilters = widget.customFilters && widget.customFilters != '';

  // return (
  //   <DropdownMenu>
  //     <DropdownMenuTrigger asChild>
  //       <MoreVerticalIcon size={16} />
  //     </DropdownMenuTrigger>

  //     <DropdownMenuContent>
  //       {
  //         hasFilters && (
  //           <DropdownMenuItem
  //             className="cursor-pointer"
  //             onClick={startEditing}
  //           >
  //             <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
  //             <span>Filters</span>
  //           </DropdownMenuItem>
  //         )
  //       }

  //     </DropdownMenuContent>
  //   </DropdownMenu>
  // )

  if (hasFilters) {
    return (
      <div className="cursor-pointer" onClick={startEditing}>
        <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
      </div>
    );
  } else {
    return <div></div>;
  }
}
