import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import { formatDuration } from '@/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import useUserSession from '@/hooks/useUserSession';
import { RepsCanEditScoreResults } from '@/lib/Organization/types';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function FullReport({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { dbOrg } = useUserSession();
  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>(data);

  useEffect(() => {
    if (!isLoading && data) {
      setCurrentData(data);
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : (
        <Table className="mt-2">
          <TableBody>
            <TableRow className="">
              <TableCell className="[&:nth-child(1)]:pl-6">
                Number of Users
              </TableCell>
              <TableCell className="[&:nth-child(1)]:pl-6">
                {currentData.numbOfActiveReps}
              </TableCell>
            </TableRow>
            <TableRow className="">
              <TableCell className="[&:nth-child(1)]:pl-6">
                Total Talk Time
              </TableCell>
              <TableCell className="[&:nth-child(1)]:pl-6">
                {`${formatDuration(
                  currentData.totalCallDurationInMinutes * 60 * 1000,
                )} mins`}
              </TableCell>
            </TableRow>
            <TableRow className="">
              <TableCell className="[&:nth-child(1)]:pl-6">
                Number of Calls
              </TableCell>
              <TableCell className="[&:nth-child(1)]:pl-6">
                {currentData.numbOfCalls}
              </TableCell>
            </TableRow>
            <TableRow className="">
              <TableCell className="[&:nth-child(1)]:pl-6">
                Top Rep Name
              </TableCell>
              <TableCell className="[&:nth-child(1)]:pl-6">
                {currentData.topRepStats.firstName}{' '}
                {currentData.topRepStats.lastName}
              </TableCell>
            </TableRow>
            <TableRow className="">
              <TableCell className="[&:nth-child(1)]:pl-6">
                Top Rep Number of Calls
              </TableCell>
              <TableCell className="[&:nth-child(1)]:pl-6">
                {currentData.topRepStats.numbOfCalls}
              </TableCell>
            </TableRow>
            <TableRow className="">
              <TableCell className="[&:nth-child(1)]:pl-6">
                Top Rep Total Talk Time
              </TableCell>
              <TableCell className="[&:nth-child(1)]:pl-6">
                {`${formatDuration(
                  currentData.topRepStats.talkTimeInMinutes * 60 * 1000,
                )} mins`}
              </TableCell>
            </TableRow>
            <TableRow className="">
              <TableCell className="[&:nth-child(1)]:pl-6">
                Total Number of Objections Faced
              </TableCell>
              <TableCell className="[&:nth-child(1)]:pl-6">
                {currentData.numbOfObjections}
              </TableCell>
            </TableRow>
            <TableRow className="">
              <TableCell className="[&:nth-child(1)]:pl-6">
                Total Number of Questions Asked
              </TableCell>
              <TableCell className="[&:nth-child(1)]:pl-6">
                {currentData.numbOfQuestions}
              </TableCell>
            </TableRow>
            {dbOrg?.repsCanEditScoreResults != RepsCanEditScoreResults.NO && (
              <TableRow className="">
                <TableCell className="[&:nth-child(1)]:pl-6">
                  Questions Reported
                </TableCell>
                <TableCell className="[&:nth-child(1)]:pl-6">
                  {currentData.percentOfQuestionsReported}%
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}
    </WidgetCard>
  );
}
