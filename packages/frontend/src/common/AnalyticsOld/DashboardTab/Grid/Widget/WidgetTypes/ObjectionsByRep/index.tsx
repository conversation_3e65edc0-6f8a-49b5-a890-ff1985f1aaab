import { useEffect, useState } from 'react';
import {
  AnalyticsFilterDateRange,
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { RiBarChartFill } from '@remixicon/react';
import { Card } from '@tremor/react';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';

const colorClasses = [
  'bg-[#EF4B42]', // Darker red
  'bg-[#F15C55]', // Middle red
  'bg-[#F78A84]', // Lighter red
  'bg-[#66BDB9]', // Lighter green
  'bg-[#4D9A97]', // Middle green
  'bg-[#33ABB2]', // Darker green
];

// Text color classes for dynamic text color based on background
const textColorClasses = [
  'text-white', // For red, white text is a good contrast
  'text-white', // For slightly darker red
  'text-white', // Light red can use dark text
  'text-white', // Yellow can use dark text
  'text-white', // Light green uses dark text
  'text-white', // Strong green uses white text for contrast
];

// Function to calculate the background color and text color based on the value
const getBackgroundAndTextColor = (
  value: number,
  minValue: number,
  maxValue: number,
) => {
  if (value == null) {
    return {
      backgroundColor: 'bg-gray-50 ',
      textColor: 'text-gray-200',
    };
  }

  const normalizedValue = (value - minValue) / (maxValue - minValue);
  const index = Math.min(
    Math.floor(normalizedValue * colorClasses.length),
    colorClasses.length - 1,
  );

  return {
    backgroundColor: colorClasses[index],
    textColor: textColorClasses[index],
  };
};

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function CriterionByRep({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const router = useRouter();
  const { filterState, updateFiltersState, description, isLoading, data } =
    useWidgetCard(widget, updateWidgetInfo, overwriteFilters);
  const [currentData, setCurrentData] = useState<any>(data);

  useEffect(() => {
    if (!isLoading && data) {
      setCurrentData(data);
    }
  }, [isLoading, data]);

  const res = currentData || {};
  const handleCellNavigation = (
    type: string,
    objection: string,
    callerId?: number,
  ) => {
    const queryParams = new URLSearchParams();
    queryParams.set('objections', objection);
    if (filterState) {
      if (filterState.dates?.range == AnalyticsFilterDateRange.CUSTOM) {
        queryParams.set(
          'fromDate',
          dayjs(filterState.dates?.fromDate).format('YYYY-MM-DD'),
        );
        queryParams.set(
          'toDate',
          dayjs(filterState.dates?.toDate).format('YYYY-MM-DD'),
        );
      }
      if (filterState.teams?.length > 0) {
        queryParams.set('teams', filterState.teams.join(','));
      }
      if (filterState.tags?.length > 0) {
        queryParams.set('tags', filterState.tags.join(','));
      }
      if (filterState.callTypes?.length > 0) {
        queryParams.set('callTypes', filterState.callTypes.join(','));
      }
    }
    if (type === 'rep' && callerId) {
      queryParams.set('reps', String(callerId));
    }
    router.push(`/calls?${queryParams.toString()}`);
  };
  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={DefaultChartDropDownMenu}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : currentData.vertical_axis_labels.length === 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          <Card className="sm:mx-auto sm:max-w-lg">
            <div className="mt-4 flex h-44 items-center justify-center rounded-tremor-small border border-dashed border-tremor-border p-4 dark:border-dark-tremor-border">
              <div className="text-center">
                <RiBarChartFill
                  className="mx-auto h-7 w-7 text-tremor-content-subtle dark:text-dark-tremor-content-subtle"
                  aria-hidden={true}
                />
                <p className="mt-2 text-tremor-default font-medium text-tremor-content-strong dark:text-dark-tremor-content-strong">
                  No data to show
                </p>
                <p className="text-tremor-default text-tremor-content dark:text-dark-tremor-content text-left">
                  <br></br>
                  Try adjusting your filters <br></br>
                </p>
              </div>
            </div>
          </Card>
        </div>
      ) : (
        <>
          <div className="sticky top-0 z-20 min-w-full">
            {/* <table className="min-w-full table-auto border-separate border-spacing-1"> */}
            <table className="min-w-full table-auto border-separate border-spacing-x-1 border-spacing-y-0">
              <thead>
                <tr>
                  <th className="sticky left-0 text-left min-w-[350px] bg-white z-10">
                    <div className="flex items-center">
                      <div className="flex-1">
                        <span className="text-sm">Objection</span>
                      </div>
                      <div className="font-medium text-xs min-w-[90px] text-left mr-2">
                        Overall <br /> Pass Rate
                      </div>
                    </div>
                  </th>
                  {res.horizontal_axis_labels
                    .filter((caller: string) => caller !== '#Total#')
                    .map((caller: string) => (
                      <th
                        key={caller}
                        className={`border-none font-medium min-w-[90px] max-w-[120px] text-left break-words whitespace-normal text-xs bg-white`}
                      >
                        {caller}
                      </th>
                    ))}
                </tr>
              </thead>
            </table>
          </div>

          {/* Wrapping the table with overflow-x-auto */}
          {/* <div className="overflow-x-auto"> */}
          <div className="top-0 -mt-9">
            <table className="min-w-full table-auto border-separate border-spacing-1">
              <thead>
                {/* <thead className="hidden"> */}
                <tr>
                  <th className="sticky left-0 text-left min-w-[350px] bg-white z-10">
                    <div className="flex items-center">
                      <div className="flex-1">
                        <span className="text-sm">Objection</span>
                      </div>
                      <div className="font-medium text-xs min-w-[90px] text-left mr-2">
                        Overall <br /> Pass Rate
                      </div>
                    </div>
                  </th>
                  {res.horizontal_axis_labels
                    .filter((caller: string) => caller !== '#Total#')
                    .map((caller: string) => (
                      <th
                        key={caller}
                        className={`border-none font-medium min-w-[90px] max-w-[120px] text-left break-words whitespace-normal text-xs bg-white`}
                      >
                        {caller}
                      </th>
                    ))}
                </tr>
              </thead>
              <tbody>
                {res.vertical_axis_labels.map((classification: string) => {
                  const total =
                    res.cell_details[classification]?.['#Total#']?.rate ?? null;
                  const classificationCount =
                    res.cell_details[classification]?.['#Total#']?.count ??
                    null;
                  const {
                    backgroundColor: backgroundColorOverall,
                    textColor: textColorOverall,
                  } = getBackgroundAndTextColor(total, res.min, res.max);

                  return (
                    <tr key={classification} className="h-full">
                      <td className="sticky left-0 z-10 bg-white p-0 dark:bg-gray-950 text-left min-w-[150px] max-w-[150px] ">
                        <TooltipProvider delayDuration={50}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center">
                                <div className="flex-1 whitespace-nowrap overflow-hidden mr-2 pr-2">
                                  <span className="block text-xs font-medium w-[220px] text-ellipsis overflow-hidden">
                                    {classification}
                                  </span>
                                  {total !== null && (
                                    <span className="mt-0 text-[11px] font-light text-gray-500">
                                      ({classificationCount})
                                    </span>
                                  )}
                                </div>
                                <div
                                  className={`flex h-[40px] w-[90px] flex-col justify-center text-center rounded mr-2 ${backgroundColorOverall} cursor-pointer`}
                                  onClick={() =>
                                    handleCellNavigation('team', classification)
                                  }
                                >
                                  <span
                                    className={`block text-xs font-medium ${textColorOverall}`}
                                  >
                                    {total != null
                                      ? total.toFixed(1) + '%'
                                      : 'N/A'}
                                  </span>
                                </div>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent side="right">
                              <strong>{classification}</strong>
                              <br />
                              Hyperbound reviewed <u>
                                {classificationCount}
                              </u>{' '}
                              calls and identified that
                              <br />
                              your team handled <u>{total.toFixed(1)}%</u>{' '}
                              occurrences of this Objection
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </td>
                      {res.horizontal_axis_labels
                        .filter((caller: string) => caller !== '#Total#')
                        .map((caller: string) => {
                          const cellData =
                            res.cell_details[classification]?.[caller] ?? null;
                          const callerId = cellData ? cellData.callerId : null;
                          const rate = cellData ? cellData.rate : null;
                          const count = cellData ? cellData.count : null;
                          const passed = cellData ? cellData.passed : null;
                          const notPassed = cellData
                            ? cellData.not_passed
                            : null;
                          const { backgroundColor, textColor } =
                            getBackgroundAndTextColor(rate, res.min, res.max);

                          return (
                            <td
                              key={`${classification}-${caller}`}
                              className={`h-full min-w-[80px] max-w-[120px] p-0 text-center break-words whitespace-normal rounded border ${backgroundColor} border-spacing-10 cursor-pointer`}
                              onClick={() =>
                                handleCellNavigation(
                                  'rep',
                                  classification,
                                  callerId,
                                )
                              }
                            >
                              <TooltipProvider delayDuration={50}>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="flex h-full flex-col justify-center text-center rounded ">
                                      <span
                                        className={`block text-xs font-medium ${textColor}`}
                                      >
                                        {rate != null
                                          ? rate.toFixed(1) + '%'
                                          : 'N/A'}
                                      </span>
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent side="bottom">
                                    {rate != null ? (
                                      <>
                                        <p className="text-xs">
                                          Total: {count}
                                        </p>
                                        <p className="text-xs">
                                          Passed: {passed}
                                        </p>
                                        <p className="text-xs">
                                          Failed: {notPassed}
                                        </p>
                                      </>
                                    ) : (
                                      <>
                                        <p className="text-xs">
                                          Rate unavailable
                                        </p>
                                        <p className="text-xs">Too few calls</p>
                                      </>
                                    )}
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </td>
                          );
                        })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </>
      )}
    </WidgetCard>
  );
}
