import AnalyticsService from '@/lib/AnalyticsOld';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { useState, useEffect } from 'react';
import useDashboardChartData from '@/hooks/useDashboardChartData';

export default function useWidgetCard(
  widget: DashboardWidgetDto,
  updateWidgetInfo: (widget: DashboardWidgetDto) => void,
  overwriteFilters?: AnalyticsFilterState,
  enabledDataLoading = true,
) {
  const appliedFilters = overwriteFilters || widget.appliedFilters;
  const [filterState, setFilterState] =
    useState<AnalyticsFilterState>(appliedFilters);
  const [description, setDescription] = useState<string>(
    AnalyticsService.calculateDescription(widget),
  );

  const { data, isLoading, refetch } = useDashboardChartData(
    widget.type,
    filterState,
    enabledDataLoading,
  );
  const [currentData, setCurrentData] = useState<any>(data);

  useEffect(() => {
    if (!isLoading && (data || data == 0)) {
      setCurrentData(data);
      updateDescription(data);
    }
  }, [isLoading, data]);

  useEffect(() => {
    if (!isLoading) {
      refetch();
    }
  }, [filterState]);

  useEffect(() => {
    if (overwriteFilters) {
      setFilterState(overwriteFilters);
    }
  }, [overwriteFilters]);

  const updateFiltersState = (filters: AnalyticsFilterState) => {
    setFilterState(filters);
    updateWidgetInfo({ ...widget, appliedFilters: filters });
  };

  const updateDescription = (data: any) => {
    setDescription(
      AnalyticsService.calculateDescription(
        { ...widget, appliedFilters: filterState },
        data,
      ),
    );
  };

  return {
    filterState,
    updateFiltersState,
    description,
    isLoading,
    data: currentData,
  };
}
