import { useState, useRef } from 'react';
import {
  AnalyticsFilterDateRange,
  AnalyticsFilterState,
  AnalyticsFilterType,
  DashboardWidgetDto,
  DateFilterType,
} from '@/lib/AnalyticsOld/types';
import {
  Sheet,
  SheetHeader,
  SheetTitle,
  SheetContentLight,
  SheetDescription,
} from '@/components/ui/sheet';
import { Settings } from 'lucide-react';
import DatesFilter from '../../../Filters/DateFilter';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import AnalyticsService from '@/lib/AnalyticsOld';
import { Id, toast } from 'react-toastify';
import RepsFilter from '../../../Filters/RepsFilter';
import BuyersFilter from '../../../Filters/BuyersFilter';
import CallTypeFilter from '../../../Filters/CallTypeFilter';
import { AgentCallType } from '@/lib/Agent/types';
import TagsFilter from '../../../Filters/TagsFilter';
import TeamsFilter from '../../../Filters/TeamsFilter';
import { useQueryClient } from '@tanstack/react-query';
import ScorecardsFilter from '../../../Filters/ScorecardFilter';
import ScorecardsSectionsFilter from '../../../Filters/ScorecardsSectionsFilter';
import ScorecardsCriterionsFilter from '../../../Filters/ScorecardsCriterionsFilter';
import CustomCallTypesFilter, {
  CustomCallTypesIds,
} from '../../../Filters/CustomCallTypesFilter';

interface IEditWidgetSettingsProps {
  widget: DashboardWidgetDto;
  isOpen: boolean;
  setOpen: (modalOpen: boolean) => void;
  onFiltersUpdated: (filters: AnalyticsFilterState) => void;
  isEditingDashboard: boolean;
}

export default function EditWidgetSettings({
  widget,
  isOpen,
  setOpen,
  onFiltersUpdated,
  isEditingDashboard,
}: IEditWidgetSettingsProps) {
  const tmpFilters = widget.appliedFilters;
  if (!tmpFilters[AnalyticsFilterType.DATE]) {
    tmpFilters[AnalyticsFilterType.DATE] = {
      range: AnalyticsFilterDateRange.THIS_MONTH,
    };
  } else {
    const { from, to } = AnalyticsService.getDatesRange(
      tmpFilters[AnalyticsFilterType.DATE],
    );
    tmpFilters[AnalyticsFilterType.DATE] = {
      ...tmpFilters[AnalyticsFilterType.DATE],
      fromDate: from,
      toDate: to,
    };
  }
  const [filterState, setFilterState] = useState<AnalyticsFilterState>(
    JSON.parse(JSON.stringify(tmpFilters)),
  );
  const [originalFilterState, _] = useState<AnalyticsFilterState>(
    JSON.parse(JSON.stringify(tmpFilters)),
  );
  const errorToastId = useRef<Id | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const queryClient = useQueryClient();

  /************************************/
  /********* STATE MANAGMENT **********/
  /************************************/

  const updateDates = (n: DateFilterType) => {
    filterState[AnalyticsFilterType.DATE] = n;

    updateState(filterState);
  };

  const updateReps = (n: string[]) => {
    filterState[AnalyticsFilterType.REPS] = n.map(Number);

    updateState(filterState);
  };

  const updateBuyers = (n: string[]) => {
    filterState[AnalyticsFilterType.BUYERS] = n.map(Number);
    updateState(filterState);
  };

  const updateTags = (n: number[]) => {
    filterState[AnalyticsFilterType.TAGS] = n;

    updateState(filterState);
  };

  const updateCallTypes = (n: AgentCallType[]) => {
    filterState[AnalyticsFilterType.CALL_TYPES] = n;
    updateState(filterState);
  };

  const updateCustomCallTypes = (n: CustomCallTypesIds) => {
    filterState[AnalyticsFilterType.CUSTOM_CALL_TYPES_ORG_IDS] = n.organization;
    filterState[AnalyticsFilterType.CUSTOM_CALL_TYPES_TEAM_IDS] = n.teams;
    filterState[AnalyticsFilterType.CUSTOM_CALL_TYPES_USER_IDS] = n.users;
    updateState(filterState);
  };

  const updateTeams = (n: number[]) => {
    filterState[AnalyticsFilterType.TEAMS] = n;

    updateState(filterState);
  };

  const updateScorecards = (n: number[]) => {
    filterState[AnalyticsFilterType.SCORECARDS] = n;
    updateState(filterState);
  };

  const updateScorecardsSections = (s: string[]) => {
    filterState[AnalyticsFilterType.SCORECARDS_SECTIONS] = s;
    updateState(filterState);
  };

  const updateScorecardsCriterions = (s: string[]) => {
    filterState[AnalyticsFilterType.SCORECARDS_CRITERIONS] = s;
    updateState(filterState);
  };

  const clearAll = () => {
    const clearFilter: AnalyticsFilterState = new AnalyticsFilterState();
    updateState(clearFilter);
  };

  const updateState = (fs: AnalyticsFilterState) => {
    setFilterState({ ...fs });
    onFiltersUpdated(fs);
    save(fs);
  };

  /************************************/
  /********** SAVE ACTIONS ************/
  /************************************/

  const save = async (fs: AnalyticsFilterState) => {
    if (!isEditingDashboard) {
      setIsSaving(true);
      try {
        await AnalyticsService.updateWidget(
          widget.id,
          widget.name,
          widget.description,
          widget.props,
          fs,
        );
      } catch (e) {
        console.log(e);
        errorToastId.current = toast.error(
          'There was an error. Please try again.',
        );
      }
      setIsSaving(false);

      queryClient.removeQueries({ queryKey: ['dashboard'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardChartData'] });
    }
  };

  const cancel = () => {
    setFilterState(JSON.parse(JSON.stringify(originalFilterState)));
    onFiltersUpdated(JSON.parse(JSON.stringify(originalFilterState)));
    setOpen(false);
  };

  const onEsc = () => {
    setOpen(false);
  };

  /************************************/
  /************** RENDER **************/
  /************************************/

  let showAll = false;
  if (!widget.customFilters) {
    widget.customFilters = '';
  }
  if (widget.customFilters.toLowerCase() == 'all') {
    showAll = true;
  }
  return (
    <Sheet open={isOpen} onOpenChange={onEsc}>
      <SheetContentLight className="flex flex-col">
        <SheetHeader>
          <SheetTitle className="flex items-center">
            <Settings className="mr-2" size={20} /> {widget.name}
          </SheetTitle>
          {widget.widgetDetails && widget.widgetDetails.instructions != '' && (
            <SheetDescription className="whitespace-pre-line">
              {widget.widgetDetails.instructions}
            </SheetDescription>
          )}
        </SheetHeader>
        <ScrollArea className="grow">
          <div className="my-4 ">
            {(widget.customFilters.includes('dates') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">
                  Date range:
                </div>
                <DatesFilter
                  current={filterState[AnalyticsFilterType.DATE]}
                  onDatesUpdated={updateDates}
                />
              </div>
            )}

            {(widget.customFilters.includes('reps') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">
                  Representatives:
                </div>
                <RepsFilter
                  current={filterState[AnalyticsFilterType.REPS]}
                  onRepsUpdated={updateReps}
                />
              </div>
            )}

            {(widget.customFilters.includes('teams') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">Teams:</div>
                <TeamsFilter
                  current={filterState[AnalyticsFilterType.TEAMS]}
                  onFiltersUpdated={updateTeams}
                />
              </div>
            )}

            {(widget.customFilters.includes('buyers') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">Buyers:</div>
                <BuyersFilter
                  current={filterState[AnalyticsFilterType.BUYERS]}
                  onBuyersUpdated={updateBuyers}
                />
              </div>
            )}

            {(widget.customFilters.includes('tags') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">Tags:</div>
                <TagsFilter
                  current={filterState[AnalyticsFilterType.TAGS]}
                  onFiltersUpdated={updateTags}
                />
              </div>
            )}

            {(widget.customFilters.includes('calltype') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">
                  Call type:
                </div>
                <CallTypeFilter
                  current={filterState[AnalyticsFilterType.CALL_TYPES]}
                  onCallTypesUpdated={updateCallTypes}
                />
              </div>
            )}

            {(widget.customFilters.includes('customcalltypes') || showAll) && (
              <div className="mb-6">
                <div className="text-xs font-semibold mb-1">
                  Custom Call Type
                </div>
                <CustomCallTypesFilter
                  current={{
                    organization:
                      filterState[
                        AnalyticsFilterType.CUSTOM_CALL_TYPES_ORG_IDS
                      ] || [],
                    teams:
                      filterState[
                        AnalyticsFilterType.CUSTOM_CALL_TYPES_TEAM_IDS
                      ] || [],
                    users:
                      filterState[
                        AnalyticsFilterType.CUSTOM_CALL_TYPES_USER_IDS
                      ] || [],
                  }}
                  onCallTypesUpdated={updateCustomCallTypes}
                />
              </div>
            )}

            {(widget.customFilters.includes('scorecards') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">
                  Scorecards:
                </div>
                <ScorecardsFilter
                  current={filterState[AnalyticsFilterType.SCORECARDS]}
                  onFiltersUpdated={updateScorecards}
                />
              </div>
            )}

            {(widget.customFilters.includes('sections') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">
                  Scorecard&apos;s Section(s):
                </div>
                <ScorecardsSectionsFilter
                  current={filterState[AnalyticsFilterType.SCORECARDS_SECTIONS]}
                  onSectionsUpdated={updateScorecardsSections}
                  scorecards={filterState[AnalyticsFilterType.SCORECARDS]}
                />
              </div>
            )}

            {(widget.customFilters.includes('criterions') || showAll) && (
              <div className="mb-6">
                <div className="text-sm ml-1 mb-1 font-semibold">
                  Scorecard&apos;s Criterion(s):
                </div>
                <ScorecardsCriterionsFilter
                  current={
                    filterState[AnalyticsFilterType.SCORECARDS_CRITERIONS]
                  }
                  onCriterionsUpdated={updateScorecardsCriterions}
                  sections={
                    filterState[AnalyticsFilterType.SCORECARDS_SECTIONS]
                  }
                />
              </div>
            )}
          </div>
        </ScrollArea>
        <div className="flex items-center">
          <Button
            onClick={clearAll}
            disabled={isSaving}
            variant={'secondary'}
            className="mr-2"
          >
            Clear all
          </Button>
          <div className="flex-1"></div>
          <Button
            onClick={cancel}
            disabled={isSaving}
            variant={'secondary'}
            className="mr-2"
          >
            Cancel
          </Button>
        </div>
      </SheetContentLight>
    </Sheet>
  );
}
