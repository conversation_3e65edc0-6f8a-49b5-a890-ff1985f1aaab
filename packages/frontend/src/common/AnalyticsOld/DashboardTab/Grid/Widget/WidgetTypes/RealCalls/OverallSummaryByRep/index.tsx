import React, { useEffect, useState, useRef } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon, SquareMinus, SquarePlus } from 'lucide-react';
import WidgetCard from '../../WidgetCard';
// import DefaultChartDropDownMenu from '../../defaultChartDropDownMenu';
import useWidgetCard from '../../WidgetCard/useWidgetCard';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import CallsGroupedMenu from './dropDownMenu';

import { RiBarChartFill } from '@remixicon/react';
import { Card } from '@tremor/react';

type ExpandedRowState = {
  [group: string]:
    | boolean
    | {
        [type: string]: boolean | { [tag: string]: boolean };
      };
};

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function CallsGroupedSummaryByRep({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>(data);
  const [expandedRow, setExpandedRow] = useState<any>({});

  const containerRef = useRef<HTMLDivElement>(null);
  const [maxHeight, setMaxHeight] = useState<number>(700); // default max height
  const [tableWidth, setTableWidth] = useState<number>(0);
  const [columnWidths, setColumnWidths] = useState<{ [key: string]: string }>(
    {},
  );

  const calculateColumnWidths = () => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.clientWidth;
      setTableWidth(containerWidth);
      const columnCount = 5; // We have 5 columns
      const widthPerColumn = containerWidth / columnCount;
      setColumnWidths({
        fullName: `${widthPerColumn * 1.2}px`, // Give "Full Name" a bit more space
        callType: `${widthPerColumn * 0.6}px`,
        tag: `${widthPerColumn * 0.6}px`,
        totalCalls: `${widthPerColumn * 0.6}px`,
        avgScore: `${widthPerColumn * 0.6}px`,
        maxScore: `${widthPerColumn * 0.6}px`,
        tag_indent: `${widthPerColumn * 2}px`,
      });
    }
  };

  const aggregateData = (data: any) => {
    const fullNameGroups: any = {};

    data.forEach((item: any) => {
      const callCount = Number(item.callCount);
      const avgScore = Number(item.avgScore);
      const maxScore = Number(item.maxScore);

      const fullName = item.fullName && item.fullName.trim();
      const callType = item.callType && item.callType.trim();
      const tag = item.tag;

      if (fullName !== '' && !fullNameGroups[fullName]) {
        fullNameGroups[fullName] = {
          callCount: callCount,
          avgScore: avgScore,
          maxScore: maxScore,
          callTypes: {},
        };
      }

      if (callType !== '' && !fullNameGroups[fullName].callTypes[callType]) {
        fullNameGroups[fullName].callTypes[callType] = {
          callCount: callCount,
          avgScore: avgScore,
          maxScore: maxScore,
          tags: {},
        };
      }

      if (
        tag !== '' &&
        !fullNameGroups[fullName].callTypes[callType].tags[tag]
      ) {
        fullNameGroups[fullName].callTypes[callType].tags[tag] = {
          callCount: callCount,
          avgScore: avgScore,
          maxScore: maxScore,
        };
      }
    });

    return fullNameGroups;
  };

  const toggleRow = (group: string, type: string, tag: string) => {
    setExpandedRow((prev: ExpandedRowState) => {
      const newState = { ...prev };

      // Case 1: Toggling the full group (collapse or expand the group)
      if (type === '' && tag === '') {
        // If the group is an object, collapse it (set to false)
        if (typeof prev[group] === 'object') {
          newState[group] = false; // Collapse group by setting it to false
        } else {
          // If the group is a boolean, toggle it
          newState[group] = !prev[group];
        }
      }
      // Case 2: Toggling a specific type within the group
      else if (tag === '') {
        if (typeof prev[group] === 'object') {
          // Toggle the type within the group
          newState[group] = {
            ...prev[group],
            [type]: !prev[group]?.[type], // Toggle the type boolean value
          };
        } else {
          // If group is a boolean, initialize the type
          newState[group] = { [type]: true };
        }
      }
      // Case 3: Toggling a specific tag within the group and type
      else {
        if (typeof prev[group] === 'object') {
          if (typeof prev[group][type] === 'object') {
            // Toggle the tag within the type
            newState[group] = {
              ...prev[group],
              [type]: {
                ...prev[group][type],
                [tag]: !prev[group][type]?.[tag], // Toggle the tag
              },
            };
          } else {
            // If type is a boolean, initialize it as an object with the tag
            newState[group] = {
              ...prev[group],
              [type]: {
                [tag]: true, // Initialize the tag
              },
            };
          }
        } else {
          // If the group is a boolean, initialize the nested structure
          newState[group] = {
            [type]: {
              [tag]: true, // Initialize the tag if necessary
            },
          };
        }
      }

      return newState;
    });
  };
  // console.log(data);
  useEffect(() => {
    if (!isLoading && data) {
      const aggregatedData = aggregateData(data);
      setCurrentData(aggregatedData);
    }
  }, [isLoading, data]);

  useEffect(() => {
    window.addEventListener('resize', calculateColumnWidths);
    calculateColumnWidths();
    return () => window.removeEventListener('resize', calculateColumnWidths);
  }, []);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={CallsGroupedMenu}
      updateFiltersState={updateFiltersState}
      description={description}
      menuComponentData={data}
    >
      {isLoading || !currentData ? (
        <div className="h-full w-full py-4 rounded-xl text-card-foreground flex flex-col">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : data.length === 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          <Card className="sm:mx-auto sm:max-w-lg">
            <div className="mt-4 flex h-44 items-center justify-center rounded-tremor-small border border-dashed border-tremor-border p-4 dark:border-dark-tremor-border">
              <div className="text-center">
                <RiBarChartFill
                  className="mx-auto h-7 w-7 text-tremor-content-subtle dark:text-dark-tremor-content-subtle"
                  aria-hidden={true}
                />
                <p className="mt-2 text-tremor-default font-medium text-tremor-content-strong dark:text-dark-tremor-content-strong">
                  No data to show
                </p>
                <p className="text-tremor-default text-tremor-content dark:text-dark-tremor-content text-left">
                  <br></br>
                  Try adjusting your filters <br></br>
                </p>
              </div>
            </div>
          </Card>
        </div>
      ) : (
        <div ref={containerRef}>
          <div
            style={{
              marginTop: '30px',
              maxHeight: `${maxHeight}px`,
              overflowY: 'auto',
            }}
          >
            <Table style={{ tableLayout: 'fixed', width: '100%' }}>
              <TableHeader
                style={{
                  position: 'sticky',
                  top: 0,
                  zIndex: 10,
                  backgroundColor: '#fff',
                }}
              >
                <TableRow>
                  <TableCell
                    style={{ flex: '0 1 150px', width: columnWidths.fullName }}
                  >
                    Full Name
                  </TableCell>
                  <TableCell
                    style={{ flex: '0 1 100px', width: columnWidths.callType }}
                  >
                    Call Type
                  </TableCell>
                  <TableCell
                    style={{ flex: '0 1 150px', width: columnWidths.tag }}
                  >
                    Scorecard
                  </TableCell>
                  <TableCell
                    style={{
                      flex: '0 1 50px',
                      width: columnWidths.totalCalls,
                    }}
                  >
                    Total Calls
                  </TableCell>
                  <TableCell
                    style={{ flex: '0 1 50px', width: columnWidths.avgScore }}
                  >
                    Avg Score
                  </TableCell>
                  <TableCell
                    style={{ flex: '0 1 50px', width: columnWidths.maxScore }}
                    className="text-right"
                  >
                    Max Score
                  </TableCell>
                </TableRow>
              </TableHeader>

              <TableBody>
                {Object.keys(currentData).map((fullName) => {
                  const fullNameData = currentData[fullName];

                  return (
                    <React.Fragment key={fullName}>
                      <TableRow
                        onClick={() => toggleRow(fullName, '', '')}
                        style={{ cursor: 'pointer' }}
                      >
                        <TableCell
                          className="flex items-center"
                          style={{ whiteSpace: 'nowrap' }}
                        >
                          <span>
                            {expandedRow[fullName] ? (
                              <SquareMinus size={16} className="mr-2" />
                            ) : (
                              <SquarePlus size={16} className="mr-2" />
                            )}
                          </span>
                          {fullName}
                        </TableCell>
                        <TableCell></TableCell>
                        <TableCell></TableCell>
                        <TableCell>{fullNameData.callCount}</TableCell>
                        <TableCell>
                          {(fullNameData.avgScore * 100).toFixed(2)}%
                        </TableCell>
                        <TableCell className="text-right">
                          {(fullNameData.maxScore * 100).toFixed(2)}%
                        </TableCell>
                      </TableRow>

                      {expandedRow[fullName] &&
                        Object.keys(fullNameData.callTypes).map((callType) => {
                          const callTypeData = fullNameData.callTypes[callType];

                          return (
                            <React.Fragment key={`${fullName}-${callType}`}>
                              <TableRow
                                onClick={() =>
                                  toggleRow(fullName, callType, '')
                                }
                                style={{ cursor: 'pointer' }}
                              >
                                <TableCell></TableCell>
                                <TableCell
                                  className="flex items-center"
                                  style={{
                                    whiteSpace: 'nowrap',
                                  }}
                                >
                                  <span>
                                    {expandedRow[fullName]?.[callType] ? (
                                      <SquareMinus size={16} className="mr-2" />
                                    ) : (
                                      <SquarePlus size={16} className="mr-2" />
                                    )}
                                  </span>
                                  {callType}
                                </TableCell>
                                {/* <TableCell></TableCell> */}
                                <TableCell></TableCell>
                                <TableCell>{callTypeData.callCount}</TableCell>
                                <TableCell>
                                  {(callTypeData.avgScore * 100).toFixed(2)}%
                                </TableCell>
                                <TableCell className="text-right">
                                  {(callTypeData.maxScore * 100).toFixed(2)}%
                                </TableCell>
                              </TableRow>

                              {expandedRow[fullName]?.[callType] &&
                                Object.keys(callTypeData.tags).map((tag) => {
                                  const tagData = callTypeData.tags[tag];

                                  return (
                                    <TableRow
                                      key={`${fullName}-${callType}-${tag}`}
                                      style={{ backgroundColor: '#f9f9f9' }}
                                    >
                                      <TableCell></TableCell>
                                      <TableCell></TableCell>
                                      <TableCell
                                        style={{
                                          whiteSpace: 'nowrap',
                                        }}
                                      >
                                        {tag}
                                      </TableCell>

                                      <TableCell>{tagData.callCount}</TableCell>
                                      <TableCell>
                                        {(tagData.avgScore * 100).toFixed(2)}%
                                      </TableCell>
                                      <TableCell className="text-right">
                                        {(tagData.maxScore * 100).toFixed(2)}%
                                      </TableCell>
                                    </TableRow>
                                  );
                                })}
                            </React.Fragment>
                          );
                        })}
                    </React.Fragment>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </WidgetCard>
  );
}
