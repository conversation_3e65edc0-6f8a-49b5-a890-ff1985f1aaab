import { useEffect, useState } from 'react';
import {
  AnalyticsFilterDateRange,
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { ExternalLink, Link, Loader2Icon, Phone } from 'lucide-react';
import WidgetCard from '../../WidgetCard';
import DefaultChartDropDownMenu from '../../defaultChartDropDownMenu';
import { cn, formatDuration } from '@/lib/utils';
import useWidgetCard from '../../WidgetCard/useWidgetCard';
import dayjs from 'dayjs';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AgentDto } from '@/lib/Agent/types';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import RealCallsTable from '@/common/Calls/Real/List/Table';
import { FilterState } from '@/common/Calls/Real/List';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function RealCallCallHistory({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { userId } = useUserSession();

  let reps: number[] = [];
  if (userId) {
    reps = [userId];
  }

  const toDate = dayjs();
  // Subtract 4 months from the current date
  const fromDate = toDate.subtract(1, 'months');

  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(
      fromDate.toDate(),
      toDate.toDate(),
      undefined,
      undefined,
      AnalyticsFilterDateRange.LAST_TWO_WEEKS,
      0,
      undefined,
      reps,
    ),
  );

  return (
    <div
      className={
        'h-full w-full py-4 rounded-xl text-card-foreground flex flex-col '
      }
    >
      <div className="font-semibold mb-6">Call History</div>
      <div>
        <RealCallsTable filters={filterState} updateFilters={setFilterState} />
      </div>
    </div>
  );
}
