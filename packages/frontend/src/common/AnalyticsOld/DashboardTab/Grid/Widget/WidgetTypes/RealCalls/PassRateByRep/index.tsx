

import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AnalyticsFilterState, DashboardWidgetDto } from "@/lib/AnalyticsOld/types";
import { Loader2Icon } from "lucide-react";
import WidgetCard from "../../WidgetCard";
import DefaultChartDropDownMenu from "../../defaultChartDropDownMenu";
import useWidgetCard from "../../WidgetCard/useWidgetCard";
import {BarChart, type BarChartEventProps } from "@/components/ui/Charts/barChart"
import React from "react"
import { RiBarChartFill } from '@remixicon/react';
import { Card } from '@tremor/react';

// import { BarChart, Card,  type BarChartEventProps } from '@tremor/react';
interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function CallVolumeHistoryByRepWithPassRate({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const [value, setValue] = React.useState<BarChartEventProps>(null);

  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;
  
  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters
  );



  // ***** NOTE TO LUCA
  // ***** NOTE TO LUCA
  // ***** NOTE TO LUCA
  // ***** I WAS TRYING TO GET THE BAR WIDTH TO BE CONSISTENT BUT THERE WAS NOT A GOOD DEFAULT WAY
  const [currentData, setCurrentData] = useState<any>(data);
  const [chartHeight, setChartHeight] = useState(500);  // Default height of the chart

  const valueFormatter = (number: number) =>
    `${Intl.NumberFormat("us").format(number).toString()}`;

  // ***** NOTE TO LUCA
  // ***** NOTE TO LUCA
  // ***** NOTE TO LUCA
  // ***** I WAS TRYING TO GET THE AVATAR TO SHOW UP BUT I COULD NOT GET IT WORKING
  const generateAvatar = (u: any) => {
    const n = u.rep.split(' ');
    const firstName = n[0];
    const lastName = n[1];
    return (
      <Avatar className="w-5 h-5 mr-1">
        {u.avatar && <AvatarImage src={u.avatar} />}
        <AvatarFallback className="text-sm capitalize">
          {firstName?.charAt(0)}
          {lastName?.charAt(0)}
        </AvatarFallback>
      </Avatar>
    );
  };

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(" ");
  }

  useEffect(() => {
    if (!isLoading && data) {
      const formattedData = Object.values(data).map((item: any) => ({
        rep: item.rep,
        calls: item.calls,
        passed_calls: item.passed_calls,
        failed_calls: item.failed_calls,
        avg_score: item.avg_score,
        icon: generateAvatar(item)
      }));
      const sortedData = formattedData.sort((a, b) => b.calls - a.calls);

      setCurrentData(sortedData);

      // Dynamically adjust the height based on data length
      const newHeight = Math.max(100, currentData?.length * 50);  // Minimum height of 500px
      setChartHeight(newHeight);
      
    }
  }, [isLoading, data, currentData?.length]);  // Recalculate height when data changes

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon size={40} className="animate-spin text-muted-foreground" />
        </div>
        
      ) : currentData.length === 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
         
      <Card className="sm:mx-auto sm:max-w-lg">
        
        <div className="mt-4 flex h-44 items-center justify-center rounded-tremor-small border border-dashed border-tremor-border p-4 dark:border-dark-tremor-border">
          <div className="text-center">
            <RiBarChartFill
              className="mx-auto h-7 w-7 text-tremor-content-subtle dark:text-dark-tremor-content-subtle"
              aria-hidden={true}
            />
            <p className="mt-2 text-tremor-default font-medium text-tremor-content-strong dark:text-dark-tremor-content-strong">
              No data to show
            </p>
            <p className="text-tremor-default text-tremor-content dark:text-dark-tremor-content text-left">
              <br></br>
              Try adjusting your filters <br></br>
            </p>
          </div>
        </div>
      </Card>
 
        </div>
      ) :  (
        <>
          <style>
            {`
              .tremor-bar-chart .y-axis .tick text {
                font-size: 1000px !important;  // Adjust the font size as needed
              }
            `}
          </style>
          <div style={{ display: 'flex', height: '100%', width: '95%'  }}>
            <BarChart
              data={currentData}
              index="rep"
              categories={["passed_calls", "failed_calls"]}
              colors={["emerald", "red"]}
              type= {'stacked'}
              showLegend={true}
              layout="vertical"
              className="mt-2"
              valueFormatter={valueFormatter}
              onValueChange={(v) => setValue(v)}
              // ***** NOTE TO LUCA
              // ***** NOTE TO LUCA
              // ***** NOTE TO LUCA
              // ***** it would be nice if the X axis would be sticky as you scroll up and down the page
              // ***** alternatively it would be nice ti display the number in the stack bars or as a total at the end of each bar
              // showXAxis={false}
              // showGridLines={false}
              yAxisWidth={100}
              style={{ height: `${chartHeight}px` }} // Dynamically set the height here

            />
          </div>
        </>
      
      )}
    </WidgetCard>
  );
}
