import React, { useEffect, useState, useRef } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon, SquareMinus, SquarePlus } from 'lucide-react';
import WidgetCard from '../WidgetCard';
// import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import CallsGroupedMenu from './dropDownMenu';

import { RiBarChartFill } from '@remixicon/react';
import { Card } from '@tremor/react';

type ExpandedRowState = {
  [group: string]:
    | boolean
    | {
        [scorecard: string]:
          | boolean
          | {
              [tag: string]: boolean;
            };
      };
};

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function SimCallTalkStats({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );

  const [currentData, setCurrentData] = useState<any>(data);
  const [expandedRow, setExpandedRow] = useState<ExpandedRowState>({});
  //find error
  const containerRef = useRef<HTMLDivElement>(null);
  const [maxHeight, setMaxHeight] = useState<number>(700);
  const [tableWidth, setTableWidth] = useState<number>(0);
  const [columnWidths, setColumnWidths] = useState<{ [key: string]: string }>(
    {},
  );

  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc'); // Default sorting order is ascending
  const [sortedData, setSortedData] = useState<any>(currentData);

  function formatSecondsToHHMMSS(totalSeconds: number) {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);

    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      seconds.toString().padStart(2, '0'),
    ].join(':');
  }

  const calculateColumnWidths = () => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.clientWidth;
      setTableWidth(containerWidth);
      const columnCount = 10; // updated for new columns
      const widthPerColumn = containerWidth / columnCount;
      setColumnWidths({
        fullName: `${widthPerColumn * 1.3}px`, // more space
        scorecardName: `${widthPerColumn * 1.2}px`,
        tag: `${widthPerColumn * 0.6}px`,
        totalCalls: `${widthPerColumn * 0.3}px`,
        avgScore: `${widthPerColumn * 0.3}px`,
        maxScore: `${widthPerColumn * 0.3}px`,
        avgTalkListenRatio: `${widthPerColumn * 0.3}px`,
        avgDuration: `${widthPerColumn * 0.3}px`,
        avgFillerWords: `${widthPerColumn * 0.3}px`,
        avgTalkSpeed: `${widthPerColumn * 0.3}px`,
        avgLongestMonologue: `${widthPerColumn * 0.3}px`,
      });
    }
  };

  const aggregateData = (data: any) => {
    const fullNameGroups: any = {};

    data.forEach((item: any) => {
      const callCount = Number(item.callCount);
      const avgScore = Number(item.avgScore);
      const maxScore = Number(item.maxScore);
      const avgTalkListenRatio = Number(item.avgTalkListenRatio);
      const avgDuration = Number(item.avgDuration);
      const avgFillerWords = Number(item.avgFillerWords);
      const avgTalkSpeed = Number(item.avgTalkSpeed);
      const avgLongestMonologue = Number(item.avgLongestMonologue);

      const fullName = item.fullName && item.fullName.trim();
      const scorecardName = item.scorecardName && item.scorecardName.trim();
      const tag = item.tag;

      if (fullName !== '' && !fullNameGroups[fullName]) {
        fullNameGroups[fullName] = {
          callCount,
          avgScore,
          maxScore,
          avgTalkListenRatio,
          avgDuration,
          avgFillerWords,
          avgTalkSpeed,
          avgLongestMonologue,
          scorecards: {},
        };
      }

      if (
        scorecardName !== '' &&
        !fullNameGroups[fullName].scorecards[scorecardName]
      ) {
        fullNameGroups[fullName].scorecards[scorecardName] = {
          callCount,
          avgScore,
          maxScore,
          avgTalkListenRatio,
          avgDuration,
          avgFillerWords,
          avgTalkSpeed,
          avgLongestMonologue,
          tags: {},
        };
      }

      if (
        tag !== '' &&
        !fullNameGroups[fullName].scorecards[scorecardName].tags[tag]
      ) {
        fullNameGroups[fullName].scorecards[scorecardName].tags[tag] = {
          callCount,
          avgScore,
          maxScore,
          avgTalkListenRatio,
          avgDuration,
          avgFillerWords,
          avgTalkSpeed,
          avgLongestMonologue,
        };
      }
    });

    return fullNameGroups;
  };

  const toggleRow = (group: string, scorecard: string, tag: string) => {
    setExpandedRow((prev: ExpandedRowState) => {
      const newState = { ...prev };
      const groupData = prev[group];

      // Case 1: Toggle the whole group
      if (scorecard === '' && tag === '') {
        newState[group] =
          typeof groupData === 'object' && groupData !== null
            ? false
            : !groupData;
      }

      // Case 2: Toggle the scorecard within the group
      else if (tag === '') {
        if (
          typeof groupData === 'object' &&
          groupData !== null &&
          !Array.isArray(groupData)
        ) {
          const currentScorecard = groupData[scorecard];
          newState[group] = {
            ...groupData,
            [scorecard]:
              typeof currentScorecard === 'boolean' ? !currentScorecard : true,
          };
        } else {
          newState[group] = { [scorecard]: true };
        }
      }

      // Case 3: Toggle a tag within the scorecard
      else {
        const currentScorecardData =
          typeof groupData === 'object' &&
          groupData !== null &&
          !Array.isArray(groupData)
            ? groupData[scorecard]
            : undefined;

        newState[group] = {
          ...(typeof groupData === 'object' && groupData !== null
            ? groupData
            : {}),
          [scorecard]: {
            ...(typeof currentScorecardData === 'object' &&
            currentScorecardData !== null
              ? currentScorecardData
              : {}),
            [tag]: !(
              typeof currentScorecardData === 'object' &&
              currentScorecardData !== null &&
              currentScorecardData[tag]
            ),
          },
        };
      }

      return newState;
    });
  };

  useEffect(() => {
    if (!isLoading && data) {
      setCurrentData(aggregateData(data));
    }
  }, [isLoading, data]);

  useEffect(() => {
    window.addEventListener('resize', calculateColumnWidths);
    calculateColumnWidths();
    return () => window.removeEventListener('resize', calculateColumnWidths);
  }, []);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={CallsGroupedMenu}
      updateFiltersState={updateFiltersState}
      description={description}
      menuComponentData={data}
    >
      {isLoading || !currentData ? (
        <div className="h-full w-full py-4 rounded-xl text-card-foreground flex flex-col">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : data.length === 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          <Card className="sm:mx-auto sm:max-w-lg">
            <div className="mt-4 flex h-44 items-center justify-center rounded-tremor-small border border-dashed border-tremor-border p-4 dark:border-dark-tremor-border">
              <div className="text-center">
                <RiBarChartFill className="mx-auto h-7 w-7 text-tremor-content-subtle dark:text-dark-tremor-content-subtle" />
                <p className="mt-2 font-medium">No data to show</p>
                <p className="text-left">
                  <br />
                  Try adjusting your filters
                  <br />
                </p>
              </div>
            </div>
          </Card>
        </div>
      ) : (
        <div ref={containerRef}>
          <div
            style={{
              marginTop: '30px',
              maxHeight: `${maxHeight}px`,
              overflowY: 'auto',
            }}
          >
            <Table style={{ tableLayout: 'fixed', width: '100%' }}>
              <TableHeader
                style={{
                  position: 'sticky',
                  top: 0,
                  zIndex: 10,
                  backgroundColor: '#fff',
                }}
              >
                <TableRow>
                  <TableCell style={{ width: columnWidths.fullName }}>
                    Full Name
                  </TableCell>
                  <TableCell style={{ width: columnWidths.scorecardName }}>
                    Scorecard Name
                  </TableCell>
                  <TableCell style={{ width: columnWidths.tag }}>Tag</TableCell>
                  <TableCell style={{ width: columnWidths.totalCalls }}>
                    Total Calls
                  </TableCell>
                  <TableCell style={{ width: columnWidths.avgScore }}>
                    Avg Score
                  </TableCell>
                  <TableCell style={{ width: columnWidths.maxScore }}>
                    Max Score
                  </TableCell>
                  <TableCell style={{ width: columnWidths.avgTalkListenRatio }}>
                    Avg Talk / Listen
                  </TableCell>
                  <TableCell style={{ width: columnWidths.avgDuration }}>
                    Avg Call Duration
                  </TableCell>
                  <TableCell style={{ width: columnWidths.avgFillerWords }}>
                    Avg Filler Words (wpm)
                  </TableCell>
                  <TableCell style={{ width: columnWidths.avgTalkSpeed }}>
                    Avg Talk Speed (wpm)
                  </TableCell>
                  <TableCell
                    style={{ width: columnWidths.avgLongestMonologue }}
                  >
                    Avg Longest Monologue (s)
                  </TableCell>
                </TableRow>
              </TableHeader>

              <TableBody>
                {Object.keys(currentData).map((fullName: string) => {
                  const d = currentData[fullName];
                  return (
                    <React.Fragment key={fullName}>
                      <TableRow
                        onClick={() => toggleRow(fullName, '', '')}
                        style={{ cursor: 'pointer' }}
                      >
                        <TableCell
                          className="flex items-center"
                          style={{ whiteSpace: 'nowrap' }}
                        >
                          {expandedRow[fullName] ? (
                            <SquareMinus size={16} className="mr-2" />
                          ) : (
                            <SquarePlus size={16} className="mr-2" />
                          )}
                          {fullName}
                        </TableCell>
                        <TableCell />
                        <TableCell />
                        <TableCell>{d.callCount}</TableCell>
                        <TableCell>{(d.avgScore * 100).toFixed(0)}%</TableCell>
                        <TableCell>{(d.maxScore * 100).toFixed(0)}%</TableCell>
                        <TableCell>
                          {(d.avgTalkListenRatio * 100).toFixed(0)}
                        </TableCell>
                        <TableCell>
                          {formatSecondsToHHMMSS(d.avgDuration)}
                        </TableCell>
                        <TableCell>{d.avgFillerWords.toFixed(0)}</TableCell>
                        <TableCell>{d.avgTalkSpeed.toFixed(0)}</TableCell>
                        <TableCell>
                          {d.avgLongestMonologue.toFixed(0)}
                        </TableCell>
                      </TableRow>

                      {expandedRow[fullName] &&
                        Object.keys(d.scorecards).map((score) => {
                          const sd = d.scorecards[score];
                          return (
                            <React.Fragment key={`${fullName}-${score}`}>
                              <TableRow
                                onClick={() => toggleRow(fullName, score, '')}
                                style={{ cursor: 'pointer' }}
                              >
                                <TableCell />
                                <TableCell
                                  className="flex items-center"
                                  style={{ whiteSpace: 'nowrap' }}
                                >
                                  {/* {expandedRow[fullName]?.[score] ? <SquareMinus size={16} className="mr-2" /> : <SquarePlus size={16} className="mr-2" />} */}
                                  {typeof expandedRow[fullName] === 'object' &&
                                  expandedRow[fullName] !== null &&
                                  !Array.isArray(expandedRow[fullName]) &&
                                  (
                                    expandedRow[fullName] as {
                                      [key: string]:
                                        | boolean
                                        | { [tag: string]: boolean };
                                    }
                                  )[score] ? (
                                    <SquareMinus size={16} className="mr-2" />
                                  ) : (
                                    <SquarePlus size={16} className="mr-2" />
                                  )}
                                  {score}
                                </TableCell>
                                <TableCell />
                                <TableCell>{sd.callCount}</TableCell>
                                <TableCell>
                                  {(sd.avgScore * 100).toFixed(0)}%
                                </TableCell>
                                <TableCell>
                                  {(sd.maxScore * 100).toFixed(0)}%
                                </TableCell>
                                <TableCell>
                                  {(sd.avgTalkListenRatio * 100).toFixed(0)}
                                </TableCell>
                                <TableCell>
                                  {formatSecondsToHHMMSS(sd.avgDuration)}
                                </TableCell>
                                <TableCell>
                                  {sd.avgFillerWords.toFixed(0)}
                                </TableCell>
                                <TableCell>
                                  {sd.avgTalkSpeed.toFixed(0)}
                                </TableCell>
                                <TableCell>
                                  {sd.avgLongestMonologue.toFixed(0)}
                                </TableCell>
                              </TableRow>

                              {/* {expandedRow[fullName]?.[score] &&
                                Object.keys(sd.tags).map((tag) => {
                                  const td = sd.tags[tag];
                                  return (
                                    <TableRow key={`${fullName}-${score}-${tag}`} style={{ backgroundColor: '#f9f9f9' }}>
                                      <TableCell />
                                      <TableCell />
                                      <TableCell>{tag}</TableCell>
                                      <TableCell>{td.callCount}</TableCell>
                                      <TableCell>{(td.avgScore * 100).toFixed(0)}%</TableCell>
                                      <TableCell>{(td.maxScore * 100).toFixed(0)}%</TableCell>
                                      <TableCell>{(td.avgTalkListenRatio * 100).toFixed(0)}</TableCell>
                                      <TableCell>{td.avgFillerWords.toFixed(0)}</TableCell>
                                      <TableCell>{td.avgTalkSpeed.toFixed(0)}</TableCell>
                                      <TableCell>{td.avgLongestMonologue.toFixed(0)}</TableCell>
                                    </TableRow>
                                  );
                                })} */}
                              {typeof expandedRow[fullName] === 'object' &&
                                !Array.isArray(expandedRow[fullName]) &&
                                expandedRow[fullName] !== null &&
                                (
                                  expandedRow[fullName] as {
                                    [key: string]:
                                      | boolean
                                      | { [tag: string]: boolean };
                                  }
                                )[score] &&
                                Object.keys(sd.tags).map((tag) => {
                                  const td = sd.tags[tag];
                                  return (
                                    <TableRow
                                      key={`${fullName}-${score}-${tag}`}
                                      style={{ backgroundColor: '#f9f9f9' }}
                                    >
                                      <TableCell />
                                      <TableCell />
                                      <TableCell>{tag}</TableCell>
                                      <TableCell>{td.callCount}</TableCell>
                                      <TableCell>
                                        {(td.avgScore * 100).toFixed(0)}%
                                      </TableCell>
                                      <TableCell>
                                        {(td.maxScore * 100).toFixed(0)}%
                                      </TableCell>
                                      <TableCell>
                                        {(td.avgTalkListenRatio * 100).toFixed(
                                          0,
                                        )}
                                      </TableCell>
                                      <TableCell>
                                        {formatSecondsToHHMMSS(td.avgDuration)}
                                      </TableCell>
                                      <TableCell>
                                        {td.avgFillerWords.toFixed(0)}
                                      </TableCell>
                                      <TableCell>
                                        {td.avgTalkSpeed.toFixed(0)}
                                      </TableCell>
                                      <TableCell>
                                        {td.avgLongestMonologue.toFixed(0)}
                                      </TableCell>
                                    </TableRow>
                                  );
                                })}
                            </React.Fragment>
                          );
                        })}
                    </React.Fragment>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </WidgetCard>
  );
}
