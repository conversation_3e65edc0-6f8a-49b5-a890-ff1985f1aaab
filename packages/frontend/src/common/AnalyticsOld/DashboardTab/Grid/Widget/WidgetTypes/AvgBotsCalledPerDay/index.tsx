import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import LinksManager from '@/lib/linksManager';
import { BarList } from '@tremor/react';
import { Loader2Icon } from 'lucide-react';
import { useEffect, useState } from 'react';
import WidgetCard from '../WidgetCard';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

//support 'AVG-BOTS-CALLED-PER-DAY-BY-REP' and 'AVG-BOTS-CALLED-PER-DAY-BY-TEAM'
export default function AvgBotsCalledPerDay({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>(data);
  const [showXAxis, setShowXAxis] = useState<boolean>(true);

  const generateAvatar = (u: any) => {
    const n = u.rep.split(' ');
    const firstName = n[0];
    const lastName = n[1];
    return (
      <Avatar className="w-5 h-5 mr-1">
        {u.avatar && <AvatarImage src={u.avatar} />}
        <AvatarFallback className="text-sm capitalize">
          {firstName?.charAt(0)}
          {lastName?.charAt(0)}
        </AvatarFallback>
      </Avatar>
    );
  };

  useEffect(() => {
    if (!isLoading && data) {
      if (data.data.length > 5) {
        setShowXAxis(false);
      }
      const formattedData: any[] = [];

      data.data.forEach((d: any, i: number) => {
        let filter = 'reps';
        if (widget.type == 'AVG-BOTS-CALLED-PER-DAY-BY-TEAM') {
          filter = 'teams';
        }
        let icon = undefined;
        if (d.avatar) {
          icon = function () {
            return generateAvatar(d);
          };
        }

        formattedData.push({
          name: d.rep,
          value: d.average,
          href: LinksManager.trainingCalls(`?${filter}=${d.repId}`),
          icon: icon,
        });
      });

      data.formattedData = formattedData;
      setCurrentData(data);
    }
  }, [isLoading, data]);

  // <BarChart
  //           data={currentData.data}
  //           index="rep"
  //           categories={["Average"]}
  //           colors={CHART_COLORS}
  //           yAxisWidth={45}
  //           showXAxis={showXAxis}
  //         />
  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
      chartClassName="my-4 pr-4"
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : currentData?.formattedData?.[0]?.value == undefined ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          No data
        </div>
      ) : (
        <BarList data={currentData.formattedData} color={'teal'} />
      )}
    </WidgetCard>
  );
}
