import { useEffect, useRef, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import CHART_COLORS from '../colors';
import dynamic from 'next/dynamic';
const ReactApexChart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});
import { ApexOptions } from 'apexcharts';
import useUserSession from '@/hooks/useUserSession';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function PerformanceComparison({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const { dbOrg } = useUserSession();

  const options: ApexOptions = {
    chart: {
      toolbar: {
        show: false,
      },
      type: 'radar',
      dropShadow: {
        enabled: true,
        blur: 1,
        left: 1,
        top: 1,
      },
    },
    stroke: {
      width: 1,
    },
    fill: {
      opacity: 0.1,
    },
    markers: {
      size: 0,
    },
    yaxis: {
      stepSize: 20,
      show: false,
    },
    xaxis: {
      categories: [
        'Call Duration',
        'Filler Words',
        'Longest Monologue',
        'Score',
        'Talk/Listen Ratio',
        'Talk Speed',
      ],
    },
  };

  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>(data);

  useEffect(() => {
    if (!isLoading && data) {
      const tmp = [];
      for (const d of data) {
        tmp.push({
          name: d.object_name == `null` ? dbOrg?.name : d.object_name,
          data: [
            100 * d.normalized_avg_callduraction,
            100 * d.normalized_avg_fillerwords,
            100 * d.normalized_avg_longestmonologue,
            100 * d.normalized_avg_score,
            100 * d.normalized_avg_talklistenration,
            100 * d.normalized_avg_talkspeed,
          ],
        });
      }
      // console.log(data, tmp);
      setCurrentData(tmp);
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : currentData.length == 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          No data
        </div>
      ) : (
        <ReactApexChart options={options} series={currentData} type="radar" />
      )}
    </WidgetCard>
  );
}
