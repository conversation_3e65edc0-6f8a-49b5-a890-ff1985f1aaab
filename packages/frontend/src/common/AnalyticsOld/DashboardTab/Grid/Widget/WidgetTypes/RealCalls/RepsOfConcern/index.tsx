import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import {
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Loader2Icon,
  Phone,
} from 'lucide-react';

import { cn } from '@/lib/utils';
import WidgetCard from '../../WidgetCard';
// import DefaultChartDropDownMenu from '../../defaultChartDropDownMenu';
import DefaultChartDropDownMenu from "../../defaultChartDropDownMenu";
import useWidgetCard from '../../WidgetCard/useWidgetCard';
import dayjs from 'dayjs';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AgentDto } from '@/lib/Agent/types';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import useRouting from '@/hooks/useRouting';
import AgentAvatar from '@/components/Avatars/Agent';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { RiBarChartFill } from '@remixicon/react';
import { Card } from '@tremor/react';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function RcRepsOfConcern({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;
  const router = useRouter();

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>(data);
  const [selectedCriterionPerUser, setSelectedCriterionPerUser] = useState<{
    [userId: number]: string;
  }>({});

  useEffect(() => {
    if (!isLoading && data) {
      const tmp: { [userId: number]: string } = {};



      // data.map((info: any) => {
      //   const suggestedCriterion = Object.keys(info.suggested_criterion);
      //   if (suggestedCriterion && suggestedCriterion.length > 0) {
      //     tmp[info.callerId] = suggestedCriterion[0];
      //   } else {
      //     tmp[info.callerId] = '';
      //   }
      // });
      // setSelectedCriterionPerUser(tmp);
      // setCurrentData(data);
      const sortedData = data.sort((a: any, b: any) => {
        const sortOrder = [
          b.team_size - a.team_size, 
          b.compositeScore - a.compositeScore,  // Then by composite_score (descending)
          a.avg_tfidf - b.avg_tfidf,  // Then by avg_tfidf (ascending)
          a.criterionAvgScore - b.criterionAvgScore,  // Then by criterionAvgScore (ascending)
          a.team_avg_score - b.team_avg_score,  // Then by team_avg_score (ascending)
          a.teamId - b.teamId,  // sort by teamId (ascending)
        ];
  
        // Return the first non-zero value in the sortOrder array (if all are equal, it'll return 0)
        return sortOrder.find(order => order !== 0) || 0;
      });
      
      sortedData.map((info: any) => {
        const suggestedCriterion = Object.keys(info.suggested_criterion);
        if (suggestedCriterion && suggestedCriterion.length > 0) {
          tmp[info.callerId] = suggestedCriterion[0];
        } else {
          tmp[info.callerId] = '';
        }
      });

      // console.log(sortedData);

      setSelectedCriterionPerUser(tmp);
      setCurrentData(sortedData);


    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : currentData.length === 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
         
      <Card className="sm:mx-auto sm:max-w-lg">
        
        <div className="mt-4 flex h-44 items-center justify-center rounded-tremor-small border border-dashed border-tremor-border p-4 dark:border-dark-tremor-border">
          <div className="text-center">
            <RiBarChartFill
              className="mx-auto h-7 w-7 text-tremor-content-subtle dark:text-dark-tremor-content-subtle"
              aria-hidden={true}
            />
            <p className="mt-2 text-tremor-default font-medium text-tremor-content-strong dark:text-dark-tremor-content-strong">
              No data to show
            </p>
            <p className="text-tremor-default text-tremor-content dark:text-dark-tremor-content text-left">
              <br></br>
              (1) May take 24 hours for data to load <br></br>
              (2) You may not have teams setup correctly in your org <br></br>
              (3) Missing a rep? Each rep needs ~5 complete calls to score
            </p>
          </div>
        </div>
      </Card>
 
        </div>
      ) : (
        <div className="mt-4">
          {currentData.map((info: any, i: number) => {
            const suggestedCriterion = Object.keys(info.suggested_criterion);

            return (
              <div
                key={i}
                className="mb-2 border rounded-xl p-4 w-full  text-card-foreground flex items-top"
              >
                <div className="w-[20%] mr-4">
                  <div className="flex items-top">
                    <div>
                      <Avatar className="w-6 h-6 mr-1">
                        {info.callerAvatar && (
                          <AvatarImage src={info.callerAvatar} />
                        )}
                      </Avatar>

                    </div>
                    <div>
                      <div>{info.callerName}</div>
                      <div className="text-muted-foreground text-xs mt-2">
                        <div>
                          {/* Avg. score: {Math.trunc(info.avgCallScore.toFixed(2))} */}
                          Avg. score: {Math.trunc(info.avgCallScore * 100) / 100}
                          %
                        </div>
                        <div>Numb. of Calls: {info.totalCallCount}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="w-[30%]">
                  {suggestedCriterion.map((criterion: string, k: number) => {
                    const criterionInfo = info.suggested_criterion[criterion];
                    const isSelected =
                      selectedCriterionPerUser[info.callerId] == criterion;
                    const Icon =
                      CALL_TYPE_TO_ICON[
                        criterionInfo?.callType as keyof typeof CALL_TYPE_TO_ICON
                      ]?.Icon;

                    return (
                      <div
                        key={k}
                        className={cn(
                          'flex items-center hover:underline cursor-pointer',
                          {
                            'text-muted-foreground': !isSelected,
                          },
                        )}
                        onClick={() => {
                          setSelectedCriterionPerUser({
                            ...selectedCriterionPerUser,
                            [info.callerId]: criterion,
                          });
                        }}
                      >
                        {isSelected ? (
                          <div className="w-[20px]">
                            <ChevronRight size={14} />
                          </div>
                        ) : (
                          <div className="w-[20px]"></div>
                        )}
                        <div>{Icon && <Icon className="mr-1" size={14} />}</div>
                        <div>{criterion}</div>
                      </div>
                    );
                  })}
                </div>
                <div className="flex-1 ">
                  {suggestedCriterion.map((criterion: string, k: number) => {
                    const criterionInfo = info.suggested_criterion[criterion];
                    if (selectedCriterionPerUser[info.callerId] == criterion) {
                      return (
                        <div key={'infos' + k}>
                          <div className="text-xs font-semibold">
                            {criterion}
                          </div>

                          <div className="flex items-center mt-2">
                            <div className="text-xs text-muted-foreground flex-1">
                              Rep avg score:{' '}
                              {parseFloat(
                                criterionInfo?.criterionAvgScore,
                              ).toFixed(2)}
                              %
                            </div>

                            <div className="text-xs text-muted-foreground flex-1">
                              Rep calls with this criteria:{' '}
                              {criterionInfo?.criterionPossibleScore}
                            </div>

                            <div className="text-xs text-muted-foreground flex-1">
                              Team pass rate:{' '}
                              {parseFloat(criterionInfo?.teamAverage).toFixed(
                                2,
                              )}
                              %
                            </div>
                          </div>

                          <div className="text-xs text-muted-foreground mt-2">
                            Calls to review:
                          </div>
                          <table className="w-full">
                            {criterionInfo.suggestedCalls.map(
                              (call: any, j: number) => {
                                // let score = '0';
                                // if (call && call.scorecard) {
                                //   score = Number(
                                //     (call.scorecard?.passedScore /
                                //       call.scorecard?.totalScore) *
                                //       100 || 0,
                                //   ).toLocaleString('en-US', {
                                //     minimumFractionDigits: 0,
                                //     maximumFractionDigits: 0,
                                //   });
                                // }
                                const date = dayjs(call.createdAt).format('MMM DD',);

                                return (
                                  <tr
                                    key={'call-' + j}
                                    className="border-b hover:bg-gray-100 cursor-pointer text-xs"
                                    onClick={() => {
                                      router.push(
                                        LinksManager.realCalls(call.id),
                                      );
                                    }}
                                  >
                                    <td className="flex items-center p-2">


                                      <Avatar className="w-6 h-6 mr-1">
                                        {info.callerAvatar && (
                                          <AvatarImage src={info.callerAvatar} />
                                        )}
                                      </Avatar>

                                      {call.caller?.firstName}{' '}
                                      {call.caller?.lastName}
                                    </td>
                                    <td className="p-2">&nbsp;</td>
                                    <td className="text-muted-foreground ">
                                      <div className="flex items-center justify-end mr-4">
                                        {date}
                                        <ExternalLink
                                          size={16}
                                          className="ml-2"
                                        />
                                      </div>
                                    </td>
                                  </tr>
                                );
                              },
                            )}
                          </table>
                        </div>
                      );
                    }
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </WidgetCard>
  );
}
