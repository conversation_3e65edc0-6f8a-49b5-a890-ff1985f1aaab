import React, { useState, forwardRef } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Trash2, InfoIcon, Filter } from 'lucide-react';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import EditWidgetSettings from '../../EditWidgetSettings';
import { Input } from '@/components/ui/input';
import AnalyticsService from '@/lib/AnalyticsOld';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface IWidgetCardProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  children: React.ReactNode;
  MenuComponent?: any;
  updateAdditionalFeatures?: (features: any) => void;
  updateFiltersState: (filterState: AnalyticsFilterState) => void;
  description: string;
  chartClassName?: string;
  menuComponentData?: any;
}

const WidgetCard = forwardRef<HTMLDivElement, IWidgetCardProps>(
  (
    {
      widget,
      isEditing,
      updateWidgetInfo,
      deleteWidget,
      children,
      MenuComponent,
      updateAdditionalFeatures,
      updateFiltersState,
      description,
      chartClassName,
      menuComponentData,
    }: IWidgetCardProps,
    ref,
  ) => {
    const [editSettings, setEditSettings] = useState<boolean>(false);
    const [name, setName] = useState<string>(widget.name);
    const [editingDescription, setEditingDescription] = useState<string>(
      widget.description,
    );
    /********************************/
    /*********** ACTIONS ************/
    /********************************/

    const _updateAdditionalFeatures = (features: any) => {
      if (updateAdditionalFeatures) {
        updateAdditionalFeatures(features);
      }
    };

    const startEditing = () => {
      setEditSettings(true);
    };

    const updateFilters = (filters: AnalyticsFilterState) => {
      // widget.appliedFilters = filters;
      updateFiltersState(filters);
    };

    const updateName = (name: string) => {
      setName(name);
      updateWidgetInfo({ ...widget, name: name });
    };

    const updateDescription = (d: string) => {
      setEditingDescription(d);
      updateWidgetInfo({ ...widget, description: d });
    };

    const startDeleteWidget = () => {
      deleteWidget(widget);
    };

    /********************************/
    /********* RENDERING ************/
    /********************************/

    return (
      <div
        className={
          'h-full w-full p-4 rounded-xl border bg-card text-card-foreground shadow-sm flex flex-col ' +
          (isEditing && 'cursor-grab')
        }
      >
        <div className="flex items-top">
          <div className="flex-1 font-semibold mr-10">
            {isEditing ? (
              <Input
                className="nodrag"
                value={name}
                onChange={(e) => updateName(e.target.value)}
              />
            ) : (
              <div className="">
                <div>{widget.name}</div>
                {widget.widgetDetails?.inCardInfos && (
                  <div className="text-xs text-muted-foreground mt-1 mb-1">
                    {widget.widgetDetails?.inCardInfos}
                  </div>
                )}
              </div>
            )}
          </div>
          <div
            className="cursor-pointer"
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onClick={() => {}}
          >
            {isEditing ? (
              <div className="flex items-center">
                <Filter
                  size={16}
                  className="text-card-foreground cursor-pointer mr-2"
                  onClick={startEditing}
                />
                <Trash2
                  size={16}
                  className="text-card-foreground cursor-pointer"
                  onClick={startDeleteWidget}
                />
              </div>
            ) : (
              MenuComponent && (
                <MenuComponent
                  widget={widget}
                  menuComponentData={menuComponentData}
                  startEditing={startEditing}
                  setAdditionalFeatures={_updateAdditionalFeatures}
                />
              )
            )}
          </div>
        </div>
        <div
          className={
            'flex-1 ' +
            (isEditing ? 'overflow-hidden' : 'overflow-auto opacity-[1]') +
            ` ${chartClassName}`
          }
          ref={ref}
        >
          {children}
        </div>
        {isEditing ? (
          <Input
            className="nodrag"
            value={editingDescription}
            onChange={(e) => updateDescription(e.target.value)}
          />
        ) : (
          widget.description && (
            <div
              className="text-xs text-muted-foreground max-h-[30px] overflow-hidden"
              title={description}
            >
              {description}
            </div>
          )
        )}
        <EditWidgetSettings
          isEditingDashboard={isEditing}
          widget={widget}
          isOpen={editSettings}
          setOpen={setEditSettings}
          onFiltersUpdated={updateFilters}
        />
      </div>
    );
  },
);

WidgetCard.displayName = 'WidgetCard';

export default WidgetCard;
