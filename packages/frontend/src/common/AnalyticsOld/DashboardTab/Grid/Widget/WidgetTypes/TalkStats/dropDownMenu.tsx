import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DashboardWidgetDto } from '@/lib/AnalyticsOld/types';
import { Filter, Download, MoreVerticalIcon } from 'lucide-react';
import { useState, useEffect } from 'react';

interface IChartDropDownMenuProps {
  widget: DashboardWidgetDto;
  startEditing: () => void;
  menuComponentData: any;
}

export default function ScorecardGroupedMenu({
  widget,
  startEditing,
  menuComponentData,
}: IChartDropDownMenuProps) {
  const hasFilters = widget.customFilters && widget.customFilters !== '';

  const [currentData, setCurrentData] = useState<any>(menuComponentData);

  useEffect(() => {
    setCurrentData(menuComponentData);
  }, [menuComponentData, widget]);
  function formatSecondsToHHMMSS(totalSeconds: number) {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);

    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      seconds.toString().padStart(2, '0'),
    ].join(':');
  }
  const exportToCSVAll = () => {
    let csv = `Layer, Full Name, Scorecard Name, Tag, Total Calls, Avg Score, Max Score, Avg Talk/Listen Ratio, Avg Call Duration, Avg Filler Words, Avg Talk Speed, Avg Longest Monologue\n`;

    currentData.forEach((i: any) => {
      const avgDuration =
        typeof i.avgDuration === 'number' && !isNaN(i.avgDuration)
          ? formatSecondsToHHMMSS(i.avgDuration)
          : '00:00:00';

      csv += `${i.displayLayer}, ${i.fullName}, ${i.scorecardName}, ${i.tag}, ${i.callCount}, ${(100 * i.avgScore).toFixed(2)}%, ${(100 * i.maxScore).toFixed(2)}%,${i.avgTalkListenRatio}, ${formatSecondsToHHMMSS(i.avgDuration)} ,${i.avgFillerWords}, ${i.avgTalkSpeed}, ${i.avgLongestMonologue}\n`;
    });

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'scorecard_summary_all.csv');
      link.click();
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <MoreVerticalIcon size={16} />
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end">
        {hasFilters && (
          <DropdownMenuItem className="cursor-pointer" onClick={startEditing}>
            <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
            <span>Filters</span>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem className="cursor-pointer" onClick={exportToCSVAll}>
          <Download className="w-4 h-4 mr-2 text-muted-foreground" />
          <span>Export to CSV</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
