import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import CHART_COLORS from '../colors';
import { LineChart, SparkAreaChart } from '@tremor/react';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function ScorecardsSectionsTrends({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu;

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>(data);

  useEffect(() => {
    if (!isLoading && data) {
      const tmp = [];

      for (const d of data) {
        const info: any = {};
        info.name = d.name;
        info.progress = d.slope;
        info.days = d.days;
        if (d.slope > 0) {
          info.color = 'green';
        } else {
          info.color = 'red';
        }

        info.data = [];
        const one = {
          x: 0,
          y: d.intercept,
        };
        info.data.push(one);
        const two = {
          x: 10,
          y: 10 * d.slope + d.intercept,
        };
        info.data.push(two);

        tmp.push(info);
      }

      setCurrentData(tmp);
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : currentData.length == 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          No data
        </div>
      ) : (
        <div className="mt-6">
          {currentData.map((d: any) => {
            return (
              <div
                key={d.name}
                className="my-2 mx-auto flex max-w-lg items-center justify-between border rounded py-2 px-3"
              >
                <div className="flex-1 font-semibold">{d.name}</div>
                <div className="w-[120px] overflow-hidden">
                  <SparkAreaChart
                    data={d.data}
                    categories={['y']}
                    index={'x'}
                    colors={[d.color]}
                  />
                </div>
                <div
                  className="w-[50px] text-white p-2 rounded ml-8 text-center"
                  style={{ backgroundColor: d.color }}
                >
                  {Math.trunc(100 * d.progress * d.days)}%
                </div>
              </div>
            );
          })}
        </div>
      )}
    </WidgetCard>
  );
}
