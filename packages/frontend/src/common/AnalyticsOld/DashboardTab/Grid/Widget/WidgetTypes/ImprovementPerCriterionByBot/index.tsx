import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  AnalyticsFilterType,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import DefaultChartDropDownMenu from '../defaultChartDropDownMenu';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import CHART_COLORS from '../colors';
import { ScatterChart } from '@tremor/react';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function ImprovementPerCriterionByBot({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu; //if you need to customize this, see below (CUSTOM WIDGET MENU EXAMPLE)

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [currentData, setCurrentData] = useState<any>(data);
  useEffect(() => {
    if (!isLoading && data) {
      setCurrentData(data);
    }
  }, [isLoading, data]);

  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}
    >
      {isLoading || !currentData ? (
        <div className="w-full h-full flex items-center justify-center">
          <Loader2Icon
            size={40}
            className="animate-spin text-muted-foreground"
          />
        </div>
      ) : currentData.length == 0 ? (
        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
          No data
        </div>
      ) : (
        <ScatterChart
          className="h-[90%] mt-4"
          data={currentData}
          category="agentFullName"
          x="numbOfCalls"
          y="improvement"
          showOpacity={true}
          minYValue={0}
          valueFormatter={{
            x: (nc) => `${nc}`,
            y: (improvement) => `${improvement}%`,
          }}
          showLegend={false}
          xAxisLabel="Number of Calls"
          yAxisLabel="Improvement %"
        />
      )}
    </WidgetCard>
  );
}
