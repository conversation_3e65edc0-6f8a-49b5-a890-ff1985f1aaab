import { useEffect, useState } from 'react';
import {
  AnalyticsFilterState,
  DashboardWidgetDto,
} from '@/lib/AnalyticsOld/types';
import { Loader2Icon } from 'lucide-react';
import WidgetCard from '../WidgetCard';
import useWidgetCard from '../WidgetCard/useWidgetCard';
import dayjs from 'dayjs';
import { BarChart } from '@/components/ui/Charts/barChart';

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?: boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function WeeklyCallVolumeVsGoalPerRep({
  widget,
  isEditing,
  updateWidgetInfo,
  deleteWidget,
  hideFilters,
  overwriteFilters,
}: IGraphProps) {
  // console.log(widget);

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(
    widget,
    updateWidgetInfo,
    overwriteFilters,
  );
  const [formattedData, setFormattedData] = useState<any[]>([]);
  useEffect(() => {
    if (!isLoading && data && data.length > 0) {
      const tmp: any[] = [];
      for (const d of data) {
        const tmpDate = dayjs(d.date).format('M/DD');

        tmp.push({
          date: tmpDate,
          passed: d.passed,
          failed: d.failed,
        });
      }
      setFormattedData(tmp);
    }
  }, [isLoading, data]);

  if (isEditing) {
    return (
      <WidgetCard
        widget={widget}
        isEditing={isEditing}
        updateWidgetInfo={updateWidgetInfo}
        deleteWidget={deleteWidget}
        updateFiltersState={updateFiltersState}
        description={description}
      >
        {isLoading ? (
          <div className="w-full h-full flex items-center justify-center">
            <Loader2Icon
              size={40}
              className="animate-spin text-muted-foreground"
            />
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center font-bold text-2xl">
            &nbsp;
          </div>
        )}
      </WidgetCard>
    );
  } else {
    return (
      <div
        className={
          'h-full w-full p-4 rounded-xl border bg-card text-card-foreground shadow-sm flex flex-col '
        }
      >
        <div className="font-semibold">Weekly Call Volume</div>
        <div className="flex-1">
          {isLoading ? (
            <div className="w-full h-full flex items-center justify-center">
              <Loader2Icon
                size={40}
                className="animate-spin text-muted-foreground"
              />
            </div>
          ) : formattedData.length === 0 ? (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              No data available for this period
            </div>
          ) : (
            <div className="">
              <BarChart
                data={formattedData}
                index="date"
                type="stacked"
                categories={['passed', 'failed']}
                colors={['cyan', 'red']}
              />
            </div>
          )}
        </div>
      </div>
    );
  }
}
