
import { useEffect, useState } from "react";
import { AnalyticsFilterState, DashboardWidgetDto } from "@/lib/AnalyticsOld/types";
import { Loader2Icon } from "lucide-react";
import WidgetCard from "../../WidgetCard";
import DefaultChartDropDownMenu from "../../defaultChartDropDownMenu";
import useWidgetCard from "../../WidgetCard/useWidgetCard";
import { ComboChart } from "@/components/ui/Charts/comboCharts";

interface IGraphProps {
  widget: DashboardWidgetDto;
  isEditing: boolean;
  updateWidgetInfo: (widget: DashboardWidgetDto) => void;
  deleteWidget: (widget: DashboardWidgetDto) => void;
  hideFilters?:boolean;
  overwriteFilters?: AnalyticsFilterState;
}

export default function ProgressOverTime({ widget, isEditing, updateWidgetInfo, deleteWidget, hideFilters, overwriteFilters }: IGraphProps) {


  const MenuComponent: any = !hideFilters && DefaultChartDropDownMenu; //if you need to customize this, see below (CUSTOM WIDGET MENU EXAMPLE)

  const { updateFiltersState, description, isLoading, data } = useWidgetCard(widget, updateWidgetInfo, overwriteFilters);
  const [currentData, setCurrentData] = useState<any>(data);

  useEffect(() => {
    if (!isLoading && data) {
      const tmp = [];
      for(const d of data) {
        // console.log(d.week_start_date.replace('T00:00:00.000Z', ''));
        tmp.push({
          week_start_date: d.week_start_date.replace('T00:00:00.000Z', ''),
          call_volume: d.call_volume,
          avg_callscore: d.avg_callscore,
        })
      }
      setCurrentData(tmp);
    }
  }, [isLoading, data]);


  return (
    <WidgetCard
      widget={widget}
      isEditing={isEditing}
      updateWidgetInfo={updateWidgetInfo}
      deleteWidget={deleteWidget}
      MenuComponent={MenuComponent}
      updateFiltersState={updateFiltersState}
      description={description}>
      {
        (isLoading || !currentData) ? (
          <div className="w-full h-full flex items-center justify-center"><Loader2Icon size={40} className="animate-spin text-muted-foreground" /></div>
        ) : (
          <div className="w-full h-full flex items-center justify-center text-muted-foreground">
            
            <ComboChart
                data={currentData}
                index="week_start_date"
                enableBiaxial={true}
                barSeries={{
                  colors: ["amber"],
                  categories: ["call_volume"],
                  // valueFormatter: (v) => currencyFormatter(v),
                }}
                lineSeries={{
                  colors: ["gray"],
                  categories: ["avg_callscore"],
                  // valueFormatter: (v) => percentageFormatter(v),
                }}
                tooltipCallback={(props) => {
                  // if (props.active) {
                  //   setDatas((prev) => {
                  //     if (prev?.label === props.label) return prev
                  //     return props
                  //   })
                  // } else {
                  //   setDatas(null)
                  // }
                  return null
                }}
              />

          </div>
        )
      }
    </WidgetCard >
  )
}