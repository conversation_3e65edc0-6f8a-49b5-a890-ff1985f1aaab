import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  B<PERSON><PERSON><PERSON>bPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import useUserSession from '@/hooks/useUserSession';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import React from 'react';
import CompetitionCountdownTimer from '../CompetitionCountdownTimer';
import AgentAvatar from '@/components/Avatars/Agent';
import { AgentDto } from '@/lib/Agent/types';

export interface BreadcrumbItem {
  title: string | React.ReactNode;
  href?: string;
  subItems?: BreadcrumbItem[];
}

interface IDashboardNavbarProps {
  className?: string;
  titleAsContent?: React.ReactNode;
  titleRight?: React.ReactNode;
  subContent?: React.ReactNode;
  rightContent?: React.ReactNode;
  agent?: AgentDto;
  breadcrumbs?: BreadcrumbItem[];
  hideNav?: boolean;
}

function DashboardNavbar({
  className,
  titleRight,
  subContent,
  rightContent,
  agent,
  titleAsContent,
  breadcrumbs = [],
  hideNav = false,
}: IDashboardNavbarProps) {
  const { isLoggedIn, isCompetitionOrg } = useUserSession();
  const router = useRouter();

  return (
    <nav
      className={cn(
        'top-0 bg-white z-10 border-b',
        {
          'top-0': isLoggedIn,
          'top-14': !isLoggedIn,
          hidden: hideNav,
        },
        className,
      )}
    >
      <div className="flex flex-wrap justify-between items-center px-4 py-2">
        <div className="flex space-x-4 items-start">
          {agent && <AgentAvatar className="w-16 h-16" agent={agent} />}
          <div>
            {breadcrumbs?.length ? (
              <div className="flex items-center space-x-2">
                <Breadcrumb>
                  <BreadcrumbList>
                    {breadcrumbs
                      .slice(0, breadcrumbs?.length - 1)
                      .map((bc, i) => {
                        if (bc.subItems && bc.subItems?.length > 0) {
                          return (
                            <React.Fragment key={i}>
                              <BreadcrumbItem>
                                <DropdownMenu>
                                  <DropdownMenuTrigger className="flex items-center gap-1">
                                    <BreadcrumbEllipsis className="h-4 w-4" />
                                    <span className="sr-only">Toggle menu</span>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="start">
                                    {bc.subItems.map((subItem, j) => (
                                      <DropdownMenuItem
                                        key={j}
                                        onClick={() =>
                                          router.push(subItem.href as string)
                                        }
                                      >
                                        {subItem.title}
                                      </DropdownMenuItem>
                                    ))}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </BreadcrumbItem>
                              <BreadcrumbSeparator />
                            </React.Fragment>
                          );
                        }

                        return (
                          <React.Fragment key={i}>
                            <BreadcrumbItem>
                              <BreadcrumbLink href={bc.href}>
                                {bc.title}
                              </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                          </React.Fragment>
                        );
                      })}
                    <BreadcrumbItem>
                      <BreadcrumbPage className="text-base font-semibold">
                        {breadcrumbs[breadcrumbs.length - 1]?.title || ''}
                      </BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
                {/* <h2 className="text-xl font-semibold">{title}</h2> */}
                {titleRight}
              </div>
            ) : titleAsContent ? (
              titleAsContent
            ) : (
              <div />
            )}
            {subContent}
          </div>
        </div>
        <div className="flex flex-row items-center">
          {isCompetitionOrg && (
            <div
              className={cn({
                'pr-2': !!rightContent,
              })}
            >
              <CompetitionCountdownTimer />
            </div>
          )}
          {rightContent}
        </div>
      </div>
    </nav>
  );
}

export default DashboardNavbar;
