import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import useKnowledeGap from '@/hooks/useKnowledeGap';
import useUserSession from '@/hooks/useUserSession';
import KnowledgeGapService from '@/lib/KnowledgeGap';
import { KnowledgeGapDto } from '@/lib/KnowledgeGap/types';
import { useAuthInfo } from '@propelauth/react';
import { Textarea } from '@tremor/react';
import {
  ChevronLeft,
  Dot,
  Lock,
  Mail,
  NotebookTabs,
  Pencil,
  Trash2,
} from 'lucide-react';
import Image from 'next/image';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import Markdown from 'react-markdown';
import { Id, toast } from 'react-toastify';
import rehypeRaw from 'rehype-raw'; //to enable HTML in markdown
import remarkGfm from 'remark-gfm'; //to enable the extensions to markdown that GitHub adds with GFM

export default function KnowledgeGap() {
  const authInfo = useAuthInfo();

  const { blurSecondaryPages, isCompetitionOrg, useNewSidebar } =
    useUserSession();

  const showEditBtn = authInfo.user?.email.includes('@hyperbound.ai');

  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>(
    useNewSidebar
      ? [{ title: 'Coaching' }]
      : [{ title: 'Coaching' }, { title: 'Knowledge Gap' }],
  );
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [allParagraphs, setAllParagraphs] = useState<KnowledgeGapDto[]>([]);
  const [editingParagraph, setEditingParagraph] = useState<any>({
    paragraphId: '',
    paragraph: '',
  });
  const pageContainer = useRef<HTMLDivElement>(null);
  const pageEditorContainer = useRef<HTMLDivElement>(null);
  const nameInput = useRef<HTMLInputElement>(null);
  const errorToastId = useRef<Id | null>(null);

  /*********************************/
  /************* INIT ***********/
  /*********************************/

  const { data, isLoading } = useKnowledeGap();

  useEffect(() => {
    if (!isLoading && data) {
      setAllParagraphs(data);
    }
  }, [isLoading, data]);

  useLayoutEffect(() => {
    function updateSize() {
      if (pageContainer.current) {
        const s = pageContainer.current.getBoundingClientRect();
        pageContainer.current.style.height = window.innerHeight - s.y + 'px';
      }
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, [isEditing]);

  useLayoutEffect(() => {
    if (isEditing && nameInput.current) {
      nameInput.current.focus();

      if (pageEditorContainer.current) {
        pageEditorContainer.current.scrollTo(
          0,
          pageEditorContainer.current.scrollHeight,
        );
      }
    }
  }, [isEditing]);

  /*********************************/
  /************* ACTIONS ***********/
  /*********************************/

  const startEditing = () => {
    setIsEditing(true);
  };

  const cancelEditing = () => {
    setIsEditing(false);
  };

  const startEditingParagraph = (p: KnowledgeGapDto) => {
    setEditingParagraph(p);
  };

  const startAddNewParagraph = () => {
    setEditingParagraph({ paragraphId: '', paragraph: '' });
  };

  const deleteParagraph = async (p: KnowledgeGapDto) => {
    setIsSaving(true);

    let ok = true;
    const pid = p.id;

    try {
      await KnowledgeGapService.deleteParagraph(pid);
    } catch (e) {
      ok = false;
      console.log(e);
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error. Please try again.',
        );
      }
    }

    if (ok) {
      setEditingParagraph({ paragraphId: '', paragraph: '' });
      const tmp: KnowledgeGapDto[] = [];
      allParagraphs.map((op) => {
        if (op.id != pid) {
          tmp.push(op);
        }
      });
      setAllParagraphs(tmp);
    }

    setIsSaving(false);
  };

  const saveEditingParagraph = async () => {
    setIsSaving(true);
    let ok = true;
    let editedPar = { ...editingParagraph };
    let isNew = false;
    if (!editedPar.id) {
      isNew = true;
    }

    if (editedPar.paragraphId == '' || editedPar.paragraph == '') {
      ok = false;
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'Paragraph cannot be empty. Please fill in the paragraph.',
        );
      }
    }

    if (ok) {
      try {
        editedPar = await KnowledgeGapService.save(editedPar);
      } catch (e) {
        ok = false;
        console.log(e);
        if (!toast.isActive(errorToastId.current as Id)) {
          errorToastId.current = toast.error(
            'There was an error. Please try again.',
          );
        }
      }
    }

    if (ok) {
      setEditingParagraph({ paragraphId: '', paragraph: '' });
      if (isNew) {
        setAllParagraphs([...allParagraphs, editedPar]);
      } else {
        const tmp = allParagraphs.map((p) => {
          if (p.id == editedPar.id) {
            return editedPar;
          } else {
            return p;
          }
        });
        setAllParagraphs(tmp);
      }
    }

    setIsSaving(false);
  };

  /*********************************/
  /************* RENDER ************/
  /*********************************/

  if (blurSecondaryPages || isCompetitionOrg) {
    return (
      <div className={'relative h-[100vh] block overflow-hidden'}>
        <div className="h-[100vh] overflow-hidden">
          <DashboardNavbar
            breadcrumbs={breadcrumbs}
            rightContent={
              <div className="flex h-[34px]">
                {allParagraphs.length > 0 &&
                  (isEditing ? (
                    <>
                      <Button
                        onClick={cancelEditing}
                        variant={'outline'}
                        className="mr-2"
                        disabled={isSaving}
                      >
                        <ChevronLeft size={16} className="mr-2" /> Close
                      </Button>
                    </>
                  ) : (
                    showEditBtn && (
                      <Button onClick={startEditing} variant={'default'}>
                        <Pencil size={16} className="mr-2" /> Edit
                      </Button>
                    )
                  ))}
              </div>
            }
          />
          <div className="w-full">
            <Image
              src={'/images/coaching.png'}
              width={1000}
              height={1000}
              className="mt-4 mx-3 h-full"
              alt="coaching"
            />
          </div>
        </div>
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              'linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock personalized coaching &amp; knowledge gap insights
            </div>
            <div className="text-base mt-2 text-muted-foreground max-w-[50%] text-center">
              Complete your onboarding to unlock the coaching page and access
              actionable insights to improve the performance of your reps
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[100vh]">
      <DashboardNavbar
        breadcrumbs={breadcrumbs}
        rightContent={
          <div className="flex h-[34px]">
            {allParagraphs.length > 0 &&
              (isEditing ? (
                <>
                  <Button
                    onClick={cancelEditing}
                    variant={'outline'}
                    className="mr-2"
                    disabled={isSaving}
                  >
                    <ChevronLeft size={16} className="mr-2" /> Close
                  </Button>
                </>
              ) : (
                showEditBtn && (
                  <Button onClick={startEditing} variant={'default'}>
                    <Pencil size={16} className="mr-2" /> Edit
                  </Button>
                )
              ))}
          </div>
        }
      />

      {isEditing ? (
        <div className="flex flex-col" ref={pageContainer}>
          <div className="overflow-auto flex-1" ref={pageEditorContainer}>
            {allParagraphs.map((p) => {
              if (editingParagraph.id == p.id) {
                return (
                  <div className="flex mb-20 items-start" key={p.id}>
                    <div className="w-[20%] font-semibold pl-4 text-lg sticky top-0">
                      {editingParagraph.paragraphId}
                    </div>
                    <div className="flex-1">
                      <Markdown
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeRaw]}
                        className="prose"
                      >
                        {editingParagraph.paragraph}
                      </Markdown>
                    </div>
                    <div className="w-[60px]">&nbsp;</div>
                  </div>
                );
              } else {
                return (
                  <div key={p.id} className="flex mb-20 group items-start">
                    <div className="w-[20%] font-semibold pl-4 text-lg sticky top-0">
                      {p.paragraphId}
                    </div>
                    <div className="flex-1">
                      <Markdown
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeRaw]}
                        className="prose"
                      >
                        {p.paragraph}
                      </Markdown>
                    </div>
                    <div className="mt-2 invisible group-hover:visible flex items-top sticky top-0">
                      <Button
                        onClick={() => {
                          startEditingParagraph(p);
                        }}
                        variant={'ghost'}
                        className="mr-2"
                        disabled={isSaving}
                      >
                        <Pencil size={18} />
                      </Button>
                      <Button
                        onClick={() => {
                          deleteParagraph(p);
                        }}
                        variant={'ghost'}
                        disabled={isSaving}
                      >
                        <Trash2 size={18} />
                      </Button>
                    </div>
                  </div>
                );
              }
            })}
            {editingParagraph.id == undefined && (
              <div className="flex mb-20 items-start">
                <div className="w-[20%] font-semibold pl-4 text-lg sticky top-0">
                  {editingParagraph.paragraphId}
                </div>
                <div className="flex-1">
                  <Markdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                    className="prose"
                  >
                    {editingParagraph.paragraph}
                  </Markdown>
                </div>
                <div className="w-[60px]">&nbsp;</div>
              </div>
            )}
          </div>
          <div className="h-[30%] overflow-hidden flex p-2 border-t grow-0">
            <div className="mr-2">
              <Label>Name</Label>
              <Input
                ref={nameInput}
                placeholder={'Name...'}
                type="text"
                value={editingParagraph.paragraphId}
                onChange={(e) =>
                  setEditingParagraph({
                    ...editingParagraph,
                    paragraphId: e.target.value,
                  })
                }
              />
            </div>
            <div className="flex-1">
              <Label>Paragraph</Label>
              <Textarea
                placeholder={'Paragraph...'}
                className="w-full p-4 outline-none resize-none h-[80%]"
                value={editingParagraph.paragraph}
                onChange={(e) =>
                  setEditingParagraph({
                    ...editingParagraph,
                    paragraph: e.target.value,
                  })
                }
              />
            </div>
            <div className="ml-2 mt-4 flex flex-col">
              <Button
                onClick={saveEditingParagraph}
                variant={'default'}
                disabled={isSaving}
              >
                Save
              </Button>

              {editingParagraph.id != undefined && (
                <Button
                  onClick={startAddNewParagraph}
                  variant={'ghost'}
                  className="mt-2"
                  disabled={isSaving}
                >
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="overflow-auto my-4" ref={pageContainer}>
          {allParagraphs.map((p) => {
            return (
              <div key={p.id} className="flex items-start mb-20">
                <div className="w-[20%] font-semibold pl-4 text-lg sticky top-0">
                  {p.paragraphId}
                </div>
                <div className="flex-1">
                  <Markdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                    className="prose"
                  >
                    {p.paragraph}
                  </Markdown>
                </div>
              </div>
            );
          })}
          {allParagraphs.length == 0 && (
            <div className="flex items-center justify-center">
              <div className="ml-4 mt-4 text-base w-[50vw] border p-4 rounded-lg">
                <div className="font-bold text-xl mb-6 flex">
                  <NotebookTabs className="mr-2" />
                  Knowledge Gap
                </div>
                <div className="mb-6">
                  The Knowledge and Skills Gap assessment is designed to help
                  you identify key areas for improvement within your team.
                </div>
                <div className="mb-2">For example:</div>
                <div className="flex items-center">
                  <div>
                    <Dot size={16} className="mr-2" />
                  </div>
                  <div>
                    Hyperbound can identify reps who struggle the most with
                    tackling X objection, when it is presented by Y buyer.
                  </div>
                </div>
                <div className="flex items-center">
                  <div>
                    <Dot size={16} className="mr-2" />
                  </div>
                  <div>
                    Hyperbound can identify the reps who struggle to use the
                    correct wording to position your product.
                  </div>
                </div>
                <div className="flex items-center">
                  <div>
                    <Dot size={16} className="mr-2" />
                  </div>
                  <div>
                    Hyperbound can identify reps who struggle to use your
                    desired call methodology/structure, or fail to discovery
                    critical information during a call and much much more!
                  </div>
                </div>

                <Button
                  size={'lg'}
                  variant={'default'}
                  className={
                    'mt-10 w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200'
                  }
                  style={{
                    backgroundImage:
                      'linear-gradient(to right, #000000, #5189CE, #A168A2)',
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    window.location.href = 'mailto:<EMAIL>';
                  }}
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Contact the Hyperbound team to{' '}
                  {authInfo?.isLoggedIn ? 'get started' : 'learn more'}
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
