'use client';

import { CaretSortIcon, CheckIcon } from '@radix-ui/react-icons';
import * as React from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import { FormControl } from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import useAdminAssignments from '@/hooks/useAdminAssignments';
import useOrgUsers from '@/hooks/useOrgUsers';
import { UserDto } from '@/lib/User/types';
import { cn } from '@/lib/utils';
import dayjs from 'dayjs';

interface IRepDropdownProps {
  onChange: (values: number[]) => void;
  defaultValues: number[];
  curAgentId?: number;
}

export function RepDropdown({
  onChange,
  defaultValues = [],
  curAgentId,
}: IRepDropdownProps) {
  const [open, setOpen] = React.useState(false);
  const [values, setValues] = React.useState<number[]>(defaultValues);
  const { data: result } = useOrgUsers();
  const orgUsers = result?.data || [];
  const { data: assignments } = useAdminAssignments();

  const existingUserIds: number[] = [];

  assignments
    ?.find((assignment) => assignment.agentId === curAgentId)
    ?.userAssignments.forEach((ua) => {
      if (dayjs(ua.dueDate).isAfter(dayjs())) {
        existingUserIds.push(ua.userId);
      }
    });

  const users = (orgUsers || [])?.filter(
    (user) => !existingUserIds.includes(user.id),
  );

  const renderValues = (values: number[]) => {
    if (!values?.length) {
      return null;
    }

    return (
      <>
        <Separator orientation="vertical" className="mx-2 h-4" />
        <div className="hidden space-x-1 md:flex">
          {values.length > 2 ? (
            <Badge variant="secondary" className="rounded-sm px-1 font-normal">
              {values.length} selected
            </Badge>
          ) : (
            values.map((value) => (
              <Badge
                variant="secondary"
                key={value}
                className="rounded-sm px-1 font-normal"
              >
                {users.find((user) => user.id === value)?.firstName}
              </Badge>
            ))
          )}
        </div>
      </>
    );
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <FormControl>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            disabled={!curAgentId}
            aria-expanded={open}
            className="w-[400px] justify-start"
          >
            Choose reps
            {renderValues(values)}
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
      </FormControl>
      <PopoverContent className="w-[400px] p-0">
        <Command
          filter={(value, search) => {
            const user = users?.find(
              (user) => user.id === Number(value),
            ) as UserDto;
            const name = `${user?.firstName} ${user?.lastName}`.toLowerCase();
            if (name?.includes(search.toLowerCase())) return 1;
            return 0;
          }}
        >
          <CommandInput placeholder="Search reps..." className="h-9" />
          <CommandEmpty>No reps found</CommandEmpty>
          <CommandGroup className="max-h-[20vh] overflow-y-auto">
            {(users || []).map((user) => (
              <CommandItem
                key={user.id}
                value={String(user?.id || '')}
                onSelect={(currentValue) => {
                  const newValues = values.find(
                    (val) => val === Number(currentValue),
                  )
                    ? values.filter((val) => val !== Number(currentValue))
                    : [...values, Number(currentValue)];
                  setValues(newValues);
                  onChange(newValues);
                }}
              >
                <div
                  className={cn(
                    'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                    values.find((val) => val === user?.id)
                      ? 'bg-primary text-primary-foreground'
                      : 'opacity-50 [&_svg]:invisible',
                  )}
                >
                  <CheckIcon className={cn('h-4 w-4')} />
                </div>
                <div className="flex space-x-2 items-center">
                  <Avatar className="w-6 h-6">
                    {user?.avatar && <AvatarImage src={user.avatar} />}
                    <AvatarFallback className="text-sm capitalize">
                      {user?.firstName?.charAt(0) || ''}
                      {user?.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="capitalize">
                    {user?.firstName || ''} {user?.lastName || ''}
                  </div>
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
