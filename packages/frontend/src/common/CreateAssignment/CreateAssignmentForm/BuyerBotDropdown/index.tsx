'use client';

import { CaretSortIcon, CheckIcon } from '@radix-ui/react-icons';
import * as React from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import { FormControl } from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import useOrgAgents from '@/hooks/useOrgAgents';
import { cn } from '@/lib/utils';
import { BotIcon } from 'lucide-react';
import AgentAvatar from '@/components/Avatars/Agent';

interface IBuyerBotDropdownProps {
  onChange: (value: number) => void;
  defaultValue: number;
}

export function BuyerBotDropdown({
  onChange,
  defaultValue = 0,
}: IBuyerBotDropdownProps) {
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = React.useState<number>(defaultValue);
  const { data: orgAgents } = useOrgAgents();

  const agents = orgAgents || [];

  const renderValue = (value: number) => {
    if (!value) {
      return 'Choose a buyer bot';
    }

    const agent = agents.find((agent) => agent.id === Number(value));

    return (
      <div className="flex w-full space-x-2 items-center">
        <AgentAvatar className="w-6 h-6" agent={agent} />
        <div className="capitalize">
          {agent?.firstName || ''} {agent?.lastName || ''}
        </div>
      </div>
    );
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <FormControl>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-[400px] justify-between"
          >
            {renderValue(value)}
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
      </FormControl>
      <PopoverContent className="w-[400px] p-0">
        <Command
          filter={(value, search) => {
            const agent = agents?.find((agent) => agent.id === Number(value));
            const name = `${agent?.firstName} ${agent?.lastName}`.toLowerCase();
            if (name.includes(search.toLowerCase())) return 1;
            return 0;
          }}
        >
          <CommandInput placeholder="Search buyers..." className="h-9" />
          <CommandEmpty>No buyers found</CommandEmpty>
          <CommandGroup className="max-h-[20vh] overflow-y-auto">
            {agents.map((agent) => (
              <CommandItem
                key={agent.id}
                value={String(agent.id)}
                onSelect={(currentValue) => {
                  const newValue =
                    Number(currentValue) === value ? 0 : Number(currentValue);
                  setValue(newValue);
                  onChange(newValue);
                  setOpen(false);
                }}
              >
                <div className="flex space-x-2 items-center">
                  <AgentAvatar className="w-6 h-6" agent={agent} />

                  <div className="capitalize">
                    {agent?.firstName || ''} {agent?.lastName || ''}
                  </div>
                </div>
                <CheckIcon
                  className={cn(
                    'ml-auto h-4 w-4',
                    value === agent.id ? 'opacity-100' : 'opacity-0',
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
