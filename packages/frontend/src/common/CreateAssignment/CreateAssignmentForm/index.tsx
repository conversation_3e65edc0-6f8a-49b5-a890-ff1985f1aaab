'use client';

import * as z from 'zod';

import { DatePickerWithPresets } from '@/components/DatePickerWithPresets';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import AssignmentService from '@/lib/Assignment';
import Analytics from '@/system/Analytics';
import { AssignmentEvents } from '@/system/Analytics/events/AssignmentEvents';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Loader2Icon } from 'lucide-react';
import { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Id, ToastContainer, toast } from 'react-toastify';
import { BuyerBotDropdown } from './BuyerBotDropdown';
import { RepDropdown } from './RepDropdown';
import { useRouter } from 'next/navigation';

const formSchema = z.object({
  agentId: z.number().int().gt(0, 'This field is required'),
  dueDate: z.date(),
  userIds: z.array(z.number().int().gt(0)).min(1, 'This field is required'),
  numAssignedCalls: z.number().int().gt(0),
});

interface ICreateAssignmentFormProps {
  onSubmit: () => void;
}

export function CreateAssignmentForm({
  onSubmit: onSubmitted,
}: ICreateAssignmentFormProps) {
  const errorToastId = useRef<Id | null>(null);
  const queryClient = useQueryClient();
  const [curAgentId, setCurAgentId] = useState<number>(0);
  const router = useRouter();

  // 1. Define your form.
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      agentId: 0,
      dueDate: new Date(),
      userIds: [],
      numAssignedCalls: 10,
    },
  });

  const createAssignmentMutation = useMutation({
    mutationFn: AssignmentService.createAssignment,
    onSuccess: async (assignment, params) => {
      Analytics.track(AssignmentEvents.CREATED_SUCCESS, {
        ...params,
      });
      queryClient.invalidateQueries({ queryKey: ['assignments'] });
      queryClient.invalidateQueries({ queryKey: ['adminAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['adminAssignmentsByUser'] });
      onSubmitted();
      router.push(`/coaching/assignments/${assignment.agentId}`);
    },
    onError: (error, params) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error creating your assignment. Please try again.',
        );
      }
      console.log('ERROR creating assignment', error);
      Analytics.track(AssignmentEvents.CREATED_ERROR, {
        ...params,
        err: 'Error creating assignment',
      });
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values);
    createAssignmentMutation.mutate({
      ...values,
    });
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="agentId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Choose a buyer bot to assign your reps</FormLabel>
              <div>
                <BuyerBotDropdown
                  defaultValue={field.value}
                  onChange={(value) => {
                    field.onChange(value);
                    setCurAgentId(value);
                    //   Analytics.track(DemoInboundFormEvents.FIELD_COMPLETED, {
                    //     ...form.getValues(),
                    //     blurField: field.name,
                    //   });
                  }}
                />
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        {!!curAgentId && (
          <>
            <FormField
              control={form.control}
              name="userIds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Choose reps to assign</FormLabel>
                  <div>
                    <RepDropdown
                      defaultValues={field.value}
                      onChange={(values) => field.onChange(values)}
                      curAgentId={curAgentId}
                    />
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="numAssignedCalls"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How many calls to complete?</FormLabel>
                  <FormControl>
                    <Input
                      required
                      type="number"
                      {...field}
                      {...form.register('numAssignedCalls', {
                        valueAsNumber: true,
                      })}
                      disabled={!curAgentId}
                      onBlur={() => {
                        // Analytics.track(DemoInboundFormEvents.FIELD_COMPLETED, {
                        //   ...form.getValues(),
                        //   blurField: field.name,
                        // });
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="dueDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>When is this due?</FormLabel>
                  <div>
                    <DatePickerWithPresets
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                    />
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}
        <Button
          type="submit"
          disabled={
            !form.formState.isValid || createAssignmentMutation.isPending
          } // here
          className="float-right text-primary"
          variant={'outline'}
        >
          {createAssignmentMutation.isPending ? (
            <>
              <Loader2Icon className="animate-spin mr-2" />
              Creating
            </>
          ) : (
            'Create'
          )}
        </Button>
      </form>
      <ToastContainer />
    </Form>
  );
}
