import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { CreateAssignmentForm } from '../CreateAssignmentForm';

interface ICreateAssignmentModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  onSubmit: () => void;
}

function CreateAssignmentModal({
  modalOpen,
  setModalOpen,
  onSubmit,
}: ICreateAssignmentModalProps) {
  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Create new assignment
          </DialogTitle>
          <DialogDescription className="py-4">
            <CreateAssignmentForm onSubmit={onSubmit} />
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}

export default CreateAssignmentModal;
