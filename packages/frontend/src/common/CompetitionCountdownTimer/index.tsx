import CountdownTimer from '@/components/CountdownTimer';
import useUserSession from '@/hooks/useUserSession';
import { useState } from 'react';

export default function CompetitionCountdownTimer() {
  const { competitionEndDate } = useUserSession();
  const endDate = new Date(competitionEndDate ? competitionEndDate : '');
  const [competitionEnded, setCompetitionEnded] = useState(
    new Date().getTime() > endDate.getTime(),
  );
  if (competitionEnded) {
    return <>{''}</>;
  }
  return (
    <>
      {'Competition ends in '}
      <CountdownTimer
        targetDate={endDate}
        abbreviatedSuffix
        onExpire={async () => {
          setCompetitionEnded(true);
        }}
      />
    </>
  );
}
