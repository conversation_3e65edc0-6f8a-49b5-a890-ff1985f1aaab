import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useAgent } from '@/hooks/useAgent';
import useCall from '@/hooks/useCall';
import useCallAggregatedScorecard from '@/hooks/useCallAggregatedScorecard';
import useScorecardConfigById from '@/hooks/useScorecardConfigById';
import useUserSession from '@/hooks/useUserSession';
import {
  CallAggregatedScorecardListDto,
  CallDto,
  CallScorecardInnerScorecard,
} from '@/lib/Call/types';
import LinksManager from '@/lib/linksManager';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { cn } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { BuyerEvents } from '@/system/Analytics/events/BuyerEvents';
import { CallEvents } from '@/system/Analytics/events/CallEvents';
import { InfoIcon, LayoutListIcon, Loader2Icon, PhoneIcon } from 'lucide-react';
import Link from 'next/link';
import { useMemo, useState } from 'react';

interface IAggregateScoreBreakdownModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  calls: {
    id: number;
    createdAt: Date;
    vapiId?: string | undefined;
  }[];
  agent?: {
    id: number;
    vapiId: string;
  };
  highestScoreCallId?: number;
  callAggregatedScorecardList: CallAggregatedScorecardListDto;
  scorecardConfig?: ScorecardConfigDto;
  isUsersBreakdown: boolean;
  firstName: string;
  lastName: string;
  isLoading: boolean;
  hideUnattemptedColumns: boolean;
  triesLeft: number;
}

enum STAT_KEY {
  TALK_SPEED = 'talkSpeed',
  TALK_LISTEN_RATIO = 'talkListenRatio',
  FILLER_WORDS = 'fillerWords',
  LONGEST_MONOLOGUE = 'longestMonologue',
}

const stats: {
  name: string;
  key: STAT_KEY;
  suffix?: string;
  info: string;
}[] = [
  {
    name: 'Talk Speed',
    key: STAT_KEY.TALK_SPEED, // 'talkSpeed',
    suffix: 'wpm',
    info: 'How fast did you speak?',
  },
  {
    name: 'Talk/Listen Ratio',
    key: STAT_KEY.TALK_LISTEN_RATIO, // 'talkListenRatio',
    info: 'How much you talked vs. how much you listened.',
  },
  {
    name: 'Filler Words',
    key: STAT_KEY.FILLER_WORDS, // 'fillerWords',
    suffix: 'wpm',
    info: "How many filler words like 'um' and 'uh' did you use per min?",
  },
  {
    name: 'Longest Monologue',
    key: STAT_KEY.LONGEST_MONOLOGUE, // 'longestMonologue',
    suffix: 'secs',
    info: 'How many secs was your longest monologue?',
  },
];

export function AggregateScoreBreakdownModal({
  modalOpen,
  setModalOpen,
  calls,
  agent,
  highestScoreCallId,
  callAggregatedScorecardList,
  scorecardConfig,
  firstName,
  lastName,
  isUsersBreakdown,
  isLoading,
  hideUnattemptedColumns,
  triesLeft,
}: IAggregateScoreBreakdownModalProps) {
  const callsSorted = useMemo(() => {
    if (!calls?.length) {
      return [];
    }
    return calls.sort(
      (c1, c2) =>
        new Date(c1.createdAt).getTime() - new Date(c2.createdAt).getTime(),
    );
  }, [calls]);
  const hasAnyValidCall = useMemo(() => {
    return callsSorted.filter((c) => !!c.id).length > 0;
  }, [callsSorted]);

  const renderInfoButton = (category: string) => {
    const categoryName = category.split('(')[0]?.trim();
    const stat = stats.find((stat) => stat.name === categoryName);

    let info = stat?.info;

    if (!stat) {
      const scorecards = (Object.values(callAggregatedScorecardList)?.[0]
        ?.scorecards || []) as unknown as CallScorecardInnerScorecard[];

      const scorecard = scorecards.find((sc) => sc.sectionTitle === category);

      if (!scorecard) {
        return null;
      }

      info = scorecard.description;
    }

    return (
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger>
            <InfoIcon className="w-4 h-4 text-muted-foreground" />
          </TooltipTrigger>
          <TooltipContent side="right">{info || ''}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const innerScorecardsObj = useMemo(() => {
    if (!callsSorted?.length || !callAggregatedScorecardList) {
      return {};
    }

    const obj: {
      [callVapiId: string]: {
        [sectionTitle: string]: CallScorecardInnerScorecard;
      };
    } = {};

    callsSorted.forEach((c) => {
      obj[c.vapiId as string] = {};
      const aggScorecardDto = callAggregatedScorecardList[c.vapiId as string];
      aggScorecardDto?.scorecards?.forEach?.((sc) => {
        const scorecard = sc as unknown as CallScorecardInnerScorecard;
        obj[c.vapiId as string][scorecard.sectionTitle] = scorecard;
      });
    });

    return obj;
  }, [callsSorted, callAggregatedScorecardList]);

  const totalWeight = useMemo(() => {
    const weightsObj =
      scorecardConfig?.competitionConfig?.aggregateScoreWeights;
    if (!weightsObj) {
      return 1;
    }
    return [
      ...Object.values(weightsObj.statWeights),
      ...Object.values(weightsObj.sectionCriteriaWeights),
    ].reduce((prev, curr) => prev + curr, 0);
  }, [scorecardConfig]);

  const rows = useMemo(() => {
    if (!scorecardConfig) {
      return [];
    }
    const sectionRows = ScorecardConfigService.getSortedKeys(
      scorecardConfig,
    ).map((sectionTitle) => {
      const weight: number =
        scorecardConfig.competitionConfig?.aggregateScoreWeights
          ?.sectionCriteriaWeights?.[sectionTitle] || 0;
      const weightPercStr = `${((weight / totalWeight) * 100).toLocaleString(
        'en-US',
        {
          minimumFractionDigits: 0,
          maximumFractionDigits: 1,
        },
      )}%`;
      const row = new Map<string, { val: string; weightStr?: string }>();
      row.set('category', {
        val: `${sectionTitle}`,
        weightStr: weightPercStr,
      });

      callsSorted.forEach((c) => {
        const innerScorecard =
          innerScorecardsObj[c.vapiId as string]?.[sectionTitle];
        if (innerScorecard) {
          row.set(c.id.toString(), {
            val: `${innerScorecard.passedCriteriaCount} / ${innerScorecard.totalCriteriaCount}`,
          });
        } else {
          row.set(c.id.toString(), { val: '-' });
        }
      });

      return row;
    });
    const statRows = stats.map((s) => {
      const target =
        scorecardConfig.competitionConfig?.aggregateScoreWeights?.statWeights;
      const weight: number = target?.[s.key] || 0;
      const weightPercStr = `${((weight / totalWeight) * 100).toLocaleString(
        'en-US',
        {
          minimumFractionDigits: 0,
          maximumFractionDigits: 1,
        },
      )}%`;
      const row = new Map<string, { val: string; weightStr?: string }>();
      row.set('category', { val: `${s.name}`, weightStr: weightPercStr });

      callsSorted.forEach((c) => {
        const aggregateScorecard =
          callAggregatedScorecardList[c.vapiId as string];
        const val = aggregateScorecard?.[s.key]?.value;
        if (typeof val === 'number') {
          row.set(c.id.toString(), {
            val: !hasAnyValidCall ? '-' : `${val} ${s.suffix || ''}`.trimEnd(),
          });
        } else {
          row.set(c.id.toString(), { val: '-' });
        }
      });

      return row;
    });
    return [...sectionRows, ...statRows];
  }, [
    scorecardConfig,
    callsSorted,
    callAggregatedScorecardList,
    totalWeight,
    hasAnyValidCall,
  ]);

  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn max-w-[1000px] overflow-hidden overflow-x-scroll">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Scoring Breakdown
          </DialogTitle>
          <DialogDescription>
            for {firstName} {lastName}
            {isUsersBreakdown ? ' (You)' : ''}
          </DialogDescription>
          {isLoading ? (
            <Loader2Icon />
          ) : (
            <Table>
              <TableCaption>Final scores are out of 100</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[180px]">Category</TableHead>
                  {callsSorted.map((item, i) => (
                    <TableHead key={i}>
                      <p>
                        {!hasAnyValidCall
                          ? ''
                          : highestScoreCallId === item.id
                            ? 'Best Try'
                            : `Try ${i + 1}`}
                      </p>
                    </TableHead>
                  ))}
                  {!hideUnattemptedColumns &&
                    new Array(triesLeft).fill(0).map((_, i) => {
                      return (
                        <TableHead key={callsSorted.length + i}>
                          Try {callsSorted.length + i + 1}
                        </TableHead>
                      );
                    })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {rows.map((row) => (
                  <TableRow key={row.get('category')?.val}>
                    {Array.from(row.keys()).map((key, i) => {
                      const { val, weightStr } = row.get(key) || { val: '' };
                      return (
                        <TableCell
                          className={cn({
                            'bg-green-100':
                              i >= 1 &&
                              callsSorted[i - 1]?.id === highestScoreCallId,
                            'flex justify-between items-center':
                              key === 'category',
                          })}
                          key={key}
                        >
                          <p>
                            {val}
                            {weightStr ? ` (${weightStr})` : ''}
                          </p>
                          {key === 'category' && renderInfoButton(val)}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
              <TableFooter>
                <TableRow>
                  <TableCell>Score</TableCell>
                  {callsSorted.map((item) => (
                    <TableCell
                      className={cn('font-bold', {
                        'bg-green-100': item.id === highestScoreCallId,
                      })}
                      key={item.id}
                    >
                      {!hasAnyValidCall
                        ? '-'
                        : Number(
                            (callAggregatedScorecardList[item.vapiId as string]
                              ?.aggregateScore || 0) * 100,
                          ).toLocaleString('en-US', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 1,
                          })}
                    </TableCell>
                  ))}
                  {!hideUnattemptedColumns &&
                    new Array(triesLeft).fill(0).map((_, i) => {
                      return (
                        <TableCell key={callsSorted.length + i}>-</TableCell>
                      );
                    })}
                </TableRow>
                {!hideUnattemptedColumns && (
                  <TableRow>
                    <TableCell>Call Transcript</TableCell>
                    {callsSorted.map((item) => {
                      return (
                        <TableCell
                          className={cn({
                            'bg-green-100': item.id === highestScoreCallId,
                          })}
                          key={item.id}
                        >
                          <Link
                            target="_blank"
                            className="text-[#8E71AD] underline underline-offset-2"
                            href={LinksManager.trainingCalls(item.vapiId)}
                          >
                            View
                          </Link>
                        </TableCell>
                      );
                    })}
                  </TableRow>
                )}
              </TableFooter>
            </Table>
          )}
        </DialogHeader>
        <DialogFooter>
          {isUsersBreakdown && !!agent && (
            <>
              <Link
                href={LinksManager.trainingCalls()}
                className="h-min"
                onClick={() => {
                  Analytics.track(BuyerEvents.VIEW_CALLS_CLICKED, {
                    agentId: 497,
                    from: 'leaderboard',
                  });
                }}
              >
                <Button variant="outline">
                  <LayoutListIcon className="w-4 h-4 mr-2" />
                  View my calls/transcripts
                </Button>
              </Link>
              <Link
                href={`/home`}
                onClick={() => {
                  Analytics.track(CallEvents.START_NEW_CALL_CLICKED, {
                    agentId: agent.vapiId,
                    from: 'competition_agg_score_modal',
                  });
                }}
                className={cn({
                  'pointer-events-none': !triesLeft,
                })}
              >
                <Button variant={'default'} disabled={!triesLeft}>
                  <PhoneIcon className="w-4 h-4 mr-2" /> Start new call (
                  {triesLeft}
                  {triesLeft === 1 ? ' try' : ' tries'} left)
                </Button>
              </Link>
            </>
          )}
          {/* <Button onClick={() => setModalOpen(false)} variant={"secondary"}>
            Close
          </Button> */}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export const useAggregateScoreBreakdownForSingleCallModal = (
  callId?: string,
) => {
  const { firstName, lastName, isLoggedIn } = useUserSession();
  const [modalOpen, setModalOpen] = useState(false);
  const { data: call, isLoading: isCallLoading } = useCall(
    callId || '',
    !isLoggedIn,
    !!callId,
  );
  const {
    data: callAggregatedScorecard,
    isLoading: isCallAggregatedScorecardLoading,
  } = useCallAggregatedScorecard(callId || '', !!callId);
  const { data: agent, isLoading: isAgentLoading } = useAgent(
    call?.agent?.vapiId || '',
    !!call?.agent?.vapiId,
  );
  const { data: scorecardConfig, isLoading: isScorecardConfigLoading } =
    useScorecardConfigById(
      agent?.scorecardConfigId || 0,
      !!agent?.scorecardConfigId,
    );

  const isLoading =
    isCallLoading ||
    isCallAggregatedScorecardLoading ||
    isAgentLoading ||
    isScorecardConfigLoading;

  const { calls, callAggregatedScorecardList } = useMemo(() => {
    const calls: CallDto[] = [];
    if (!!call && !!callAggregatedScorecard) {
      calls.push(call);
      const callId = call?.vapiId as string;
      const callAggregatedScorecardList = {
        [callId]: callAggregatedScorecard,
      };
      return {
        calls,
        callAggregatedScorecardList,
      };
    }
    return {
      calls: [],
      callAggregatedScorecardList: {},
    };
  }, [call, callAggregatedScorecard]);

  const modal = (
    <AggregateScoreBreakdownModal
      modalOpen={modalOpen}
      setModalOpen={setModalOpen}
      calls={calls}
      callAggregatedScorecardList={callAggregatedScorecardList}
      agent={agent}
      scorecardConfig={scorecardConfig}
      isLoading={isLoading}
      triesLeft={agent?.competitionAgent?.user?.numTriesLeft || 0}
      highestScoreCallId={agent?.competitionAgent?.user?.highestScoreCallId}
      firstName={firstName || ''}
      lastName={lastName || ''}
      isUsersBreakdown
      hideUnattemptedColumns
    />
  );

  return {
    modal,
    modalOpen,
    setModalOpen,
  };
};
