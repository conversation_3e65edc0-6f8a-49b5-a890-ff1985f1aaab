import { useState, useRef, useEffect } from 'react';
import { Edit2, Loader2Icon, PlusCircle, Trash2 } from 'lucide-react';
import { DialogFooter } from '@/components/ui/dialog';
import useTeams from '@/hooks/useTeams';
import { But<PERSON> } from '@/components/ui/button';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { TeamDto } from '@/lib/User/types';
import EditTeam from './EditTeam';
import TeamsService from '@/lib/User/Team';
import { useQueryClient } from '@tanstack/react-query';
import { Id, toast } from 'react-toastify';
import useUserSession from '@/hooks/useUserSession';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';
import PageHeader from '@/components/PageHeader';
import SearchBox from '@/components/ui/Hyperbound/search-box';
import { AppPermissions } from '@/lib/permissions';

const newTeamSkeleton = {
  name: '',
  description: '',
  numberOfUsers: 0,
} as TeamDto;

export default function Teams() {
  const queryClient = useQueryClient();
  const [pageTitle, setPageTitle] = useState<string>('Teams');
  const [searchStr, setSearchStr] = useState<string>('');
  const [searchLabel, setSearchLabel] = useState<string>('');
  const { data: allTeams, isLoading: isLoadingTeams } = useTeams(
    0,
    50,
    searchStr,
    true,
  );
  const [teams, setTeams] = useState<TeamDto[]>([]);
  const [editingTeam, setEditingTeam] = useState<TeamDto | null>(null);
  const [deleteTeam, setDeleteTeam] = useState<TeamDto | null>(null);
  const errorToastId = useRef<Id | null>(null);

  const { canAccess } = useUserSession();

  useEffect(() => {
    if (!isLoadingTeams && allTeams) {
      setTeams(allTeams);
    }
  }, [isLoadingTeams, allTeams]);

  /**************************************/
  /*************** SERCH ***************/
  /**************************************/

  const timeoutSerch = useRef<ReturnType<typeof setTimeout> | null>(null);

  const search = (s: string) => {
    if (timeoutSerch.current) {
      clearTimeout(timeoutSerch.current);
    }

    setSearchLabel(s);

    timeoutSerch.current = setTimeout(async () => {
      setSearchStr(s);
    }, 200);
  };

  /**************************************/
  /*************** EDITING **************/
  /**************************************/

  const startEditTeam = (team: TeamDto) => {
    setPageTitle(team.name);
    setEditingTeam({ ...team });
  };

  const closeEditTeam = () => {
    setPageTitle('Teams');
    setEditingTeam(null);
  };

  const createNewTeam = () => {
    setPageTitle('New Team');
    setEditingTeam({ ...newTeamSkeleton });
  };

  const startDeleteTeam = (team: TeamDto) => {
    setDeleteTeam(team);
  };

  const cancelDeleteTeam = () => {
    setDeleteTeam(null);
  };

  const doDeleteTeam = async () => {
    let ok = true;
    try {
      if (deleteTeam && deleteTeam.id) {
        await TeamsService.deleteTeam(deleteTeam.id);
      }
    } catch (e) {
      console.log(e);
      ok = false;
      errorToastId.current = toast.error(
        'There was an error. Please try again.',
      );
    }

    if (ok) {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['teams-by-id'] });
      queryClient.invalidateQueries({ queryKey: ['orgUsers'] });
      setDeleteTeam(null);
    }
  };

  /**************************************/
  /************** RENDERING *************/
  /**************************************/

  if (!canAccess(AppPermissions.MANAGE_TEAMS) && teams.length == 0) {
    return (
      <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6 overflow-auto">
        <PageHeader title={'Teams'} />

        <div className="mt-20 flex justify-center">
          <div className="flex flex-col justify-center">
            <div className="font-semibold text-center">No team yet</div>
            <div className="text-muted-foreground mt-3 mb-6 text-center">
              Only admins can create teams for this organization
            </div>
          </div>
        </div>
      </ScrollablePage>
    );
  }

  return (
    <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6 overflow-auto">
      <PageHeader title={pageTitle} />

      {/* CONTENT */}
      <div className="w-full">
        {!editingTeam && !deleteTeam ? (
          <div className="mt-8">
            {/*****************************/}
            {/********** CMD LINE *********/}
            {/*****************************/}
            <div className="flex items-center">
              <div className="max-w-[50%]">
                <SearchBox
                  value={searchLabel}
                  onChange={search}
                  className="w-56 m-3 ml-0"
                  placeholder="Search..."
                />
              </div>
              <div className="ml-2">
                {isLoadingTeams && <Loader2Icon className="animate-spin" />}
              </div>
              <div className="flex-1"></div>
              {canAccess(AppPermissions.MANAGE_TEAMS) && (
                <Button variant={'default'} onClick={createNewTeam}>
                  <PlusCircle className="w-4 h-4 mr-2" />
                  New
                </Button>
              )}
            </div>
            <div className="w-full mt-2">
              {/*****************************/}
              {/************ TABLE **********/}
              {/*****************************/}
              <Table hideFooter={true}>
                <TableContent>
                  <TableRow>
                    <TableCellHead>Name</TableCellHead>
                    <TableCellHead>Description</TableCellHead>
                    <TableCellHead>Number of members</TableCellHead>
                    {canAccess(AppPermissions.MANAGE_TEAMS) && (
                      <TableCellHead>Number of members</TableCellHead>
                    )}
                  </TableRow>
                  {/*****************************/}
                  {/************ BODY ***********/}
                  {/*****************************/}
                  {teams?.length > 0 ? (
                    teams.map((team) => {
                      return (
                        <TableRow key={team.id}>
                          <TableCell>{team.name}</TableCell>
                          <TableCell>{team.description}</TableCell>
                          <TableCell>{team.numberOfUsers}</TableCell>
                          {canAccess(AppPermissions.MANAGE_TEAMS) && (
                            <TableCell className="flex items-center justify-end">
                              <Button
                                variant={'ghost'}
                                onClick={() => {
                                  startEditTeam(team);
                                }}
                              >
                                <Edit2 className="w-4 h-4" />
                              </Button>
                              <Button
                                variant={'ghost'}
                                onClick={() => {
                                  startDeleteTeam(team);
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </TableCell>
                          )}
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow className="border-none">
                      <TableCell colSpan={4} className="text-center">
                        No teams found
                      </TableCell>
                    </TableRow>
                  )}
                </TableContent>
                <TableFooter>&nbsp;</TableFooter>
              </Table>
            </div>
          </div>
        ) : null}

        {/*****************************/}
        {/******** EDIT TEAM **********/}
        {/*****************************/}
        {editingTeam && !deleteTeam && (
          <>
            <EditTeam team={editingTeam} onClose={closeEditTeam} />
          </>
        )}

        {/*****************************/}
        {/******** DELETE TEAM ********/}
        {/*****************************/}
        {!editingTeam && deleteTeam && (
          <>
            <div className="my-10">
              Do you really want to delete {deleteTeam.name}?
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={cancelDeleteTeam}>
                Back
              </Button>
              <Button variant="destructive" onClick={doDeleteTeam}>
                Delete
              </Button>
            </DialogFooter>
          </>
        )}
      </div>
    </ScrollablePage>
  );
}
