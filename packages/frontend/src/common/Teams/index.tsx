import { useState, useRef, useEffect } from 'react';
import { Loader2Icon, PlusCircle, Save } from 'lucide-react';
import { DialogFooter } from '@/components/ui/dialog';
import useTeams from '@/hooks/useTeams';
import { Button } from '@/components/ui/button';
import { TeamDto, TeamHierarchyDto } from '@/lib/User/types';
import EditTeam from './EditTeam';
import TeamsService from '@/lib/User/Team';
import { useQueryClient } from '@tanstack/react-query';
import { Id, toast } from 'react-toastify';
import useUserSession from '@/hooks/useUserSession';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';
import PageHeader from '@/components/PageHeader';
import SearchBox from '@/components/ui/Hyperbound/search-box';
import { AppPermissions } from '@/lib/permissions';
import TeamCard from '@/components/TeamCard';

const newTeamSkeleton = {
  name: '',
  description: '',
  numberOfUsers: 0,
} as TeamDto;

export default function Teams() {
  const queryClient = useQueryClient();
  const [pageTitle, setPageTitle] = useState<string>('Teams');
  const [searchStr, setSearchStr] = useState<string>('');
  const [searchLabel, setSearchLabel] = useState<string>('');
  const { data: allTeams, isLoading: isLoadingTeams } = useTeams(
    0,
    50,
    searchStr,
    true,
    true,
    true,
  );
  const [teams, setTeams] = useState<TeamDto[]>([]);
  const [editingTeam, setEditingTeam] = useState<TeamDto | null>(null);
  const [deleteTeam, setDeleteTeam] = useState<TeamDto | null>(null);
  const [madeChanges, setMadeChanges] = useState<boolean>(false);
  const errorToastId = useRef<Id | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const { canAccess, isManagerHierarchyEnabled } = useUserSession();

  // Root drop zone state
  const [rootDropState, setRootDropState] = useState({
    isDragOver: false,
    isValidDropTarget: false,
  });
  const [isUpdatingHierarchy, setIsUpdatingHierarchy] = useState(false);

  useEffect(() => {
    if (!isLoadingTeams && allTeams) {
      setTeams(allTeams);
    }
  }, [isLoadingTeams, allTeams]);

  /**************************************/
  /*************** SERCH ***************/
  /**************************************/

  const timeoutSerch = useRef<ReturnType<typeof setTimeout> | null>(null);

  const search = (s: string) => {
    if (timeoutSerch.current) {
      clearTimeout(timeoutSerch.current);
    }

    setSearchLabel(s);

    timeoutSerch.current = setTimeout(async () => {
      setSearchStr(s);
    }, 200);
  };

  /**************************************/
  /*************** EDITING **************/
  /**************************************/

  const startEditTeam = (team: TeamDto) => {
    setPageTitle(team.name);
    setEditingTeam({ ...team });
  };

  const closeEditTeam = () => {
    setPageTitle('Teams');
    setEditingTeam(null);
  };

  const createNewTeam = () => {
    setPageTitle('New Team');
    setEditingTeam({ ...newTeamSkeleton });
  };

  const handleTeamMove = (
    draggedTeamId: number,
    targetTeamId: number | null,
  ) => {
    setTeams((prevTeams) => {
      const findAndRemoveTeam = (
        teams: TeamDto[],
        teamId: number,
      ): { teams: TeamDto[]; removedTeam: TeamDto | null } => {
        const updatedTeams = [...teams];

        for (let i = 0; i < updatedTeams.length; i++) {
          if (updatedTeams[i].id === teamId) {
            const removedTeam = updatedTeams.splice(i, 1)[0];
            return { teams: updatedTeams, removedTeam };
          }

          if (
            updatedTeams[i].children &&
            updatedTeams[i].children!.length > 0
          ) {
            const result = findAndRemoveTeam(updatedTeams[i].children!, teamId);
            if (result.removedTeam) {
              updatedTeams[i] = {
                ...updatedTeams[i],
                children: result.teams,
              };
              return { teams: updatedTeams, removedTeam: result.removedTeam };
            }
          }
        }

        return { teams: updatedTeams, removedTeam: null };
      };

      const addTeamToTarget = (
        teams: TeamDto[],
        teamToAdd: TeamDto,
        targetId: number | null,
      ): TeamDto[] => {
        if (targetId === null) {
          // Add to root level
          return [...teams, teamToAdd];
        }

        return teams.map((team) => {
          if (team.id === targetId) {
            return {
              ...team,
              children: [...(team.children || []), teamToAdd],
            };
          }

          if (team.children && team.children.length > 0) {
            return {
              ...team,
              children: addTeamToTarget(team.children, teamToAdd, targetId),
            };
          }

          return team;
        });
      };

      const { teams: teamsAfterRemoval, removedTeam } = findAndRemoveTeam(
        prevTeams,
        draggedTeamId,
      );

      if (!removedTeam) {
        console.warn(`Team with id ${draggedTeamId} not found`);
        return prevTeams;
      }

      const updatedTeams = addTeamToTarget(
        teamsAfterRemoval,
        removedTeam,
        targetTeamId,
      );

      return updatedTeams;
    });

    setMadeChanges(true);
  };

  // Root level drop handlers
  const handleRootDragOver = (e: React.DragEvent) => {
    e.preventDefault();

    setRootDropState({
      isDragOver: true,
      isValidDropTarget: true,
    });

    e.dataTransfer.dropEffect = 'move';
  };

  const handleRootDragLeave = (e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setRootDropState({
        isDragOver: false,
        isValidDropTarget: false,
      });
    }
  };

  const handleRootDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const draggedTeamId = parseInt(e.dataTransfer.getData('text/plain'));

    if (draggedTeamId) {
      handleTeamMove(draggedTeamId, null); // null indicates root level
    }

    setRootDropState({
      isDragOver: false,
      isValidDropTarget: false,
    });
  };

  const cancelDeleteTeam = () => {
    setDeleteTeam(null);
  };

  const doDeleteTeam = async () => {
    let ok = true;
    try {
      if (deleteTeam && deleteTeam.id) {
        await TeamsService.deleteTeam(deleteTeam.id);
      }
    } catch (e) {
      console.log(e);
      ok = false;
      errorToastId.current = toast.error(
        'There was an error. Please try again.',
      );
    }

    if (ok) {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['teams-by-id'] });
      queryClient.invalidateQueries({ queryKey: ['orgUsers'] });
      setDeleteTeam(null);
    }
  };

  const getTeamHierarchy = (teams: TeamDto[]): TeamHierarchyDto[] => {
    return teams.map((t) => ({
      id: t.id,
      children: getTeamHierarchy(t.children || []),
    }));
  };

  const onCancelUpdateHierarchy = () => {
    setIsUpdatingHierarchy(false);
    setTeams(allTeams || []);
  };

  const saveChanges = async () => {
    setIsSaving(true);
    setMadeChanges(false);

    try {
      await TeamsService.updateTeamHierarchy(getTeamHierarchy(teams));
    } catch (e) {
      console.log(e);
    } finally {
      setIsSaving(false);
      setIsUpdatingHierarchy(false);
      queryClient.invalidateQueries({
        predicate: (query) => query.queryKey[0] === 'teams',
      });
      queryClient.invalidateQueries({
        predicate: (query) => query.queryKey[0] === 'teams-by-id',
      });
      queryClient.invalidateQueries({
        predicate: (query) => query.queryKey[0] === 'orgUsers',
      });
    }
  };

  /**************************************/
  /************** RENDERING *************/
  /**************************************/

  if (!canAccess(AppPermissions.MANAGE_TEAMS) && teams.length == 0) {
    return (
      <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6 overflow-auto">
        <PageHeader title={'Teams'} />

        <div className="mt-20 flex justify-center">
          <div className="flex flex-col justify-center">
            <div className="font-semibold text-center">No team yet</div>
            <div className="text-muted-foreground mt-3 mb-6 text-center">
              Only admins can create teams for this organization
            </div>
          </div>
        </div>
      </ScrollablePage>
    );
  }

  return (
    <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6 overflow-auto">
      <PageHeader title={pageTitle} />

      {/* CONTENT */}
      <div className="w-full">
        {!editingTeam && !deleteTeam ? (
          <div className="mt-8">
            {/*****************************/}
            {/********** CMD LINE *********/}
            {/*****************************/}
            <div className="flex items-center">
              <div className="max-w-[50%]">
                <SearchBox
                  value={searchLabel}
                  onChange={search}
                  className="w-56 m-3 ml-0"
                  placeholder="Search..."
                />
              </div>
              <div className="ml-2">
                {isLoadingTeams && <Loader2Icon className="animate-spin" />}
              </div>
              <div className="flex-1"></div>
              {canAccess(AppPermissions.MANAGE_TEAMS) &&
                isUpdatingHierarchy && (
                  <Button
                    variant={'outline'}
                    className="mr-2"
                    onClick={onCancelUpdateHierarchy}
                    disabled={isSaving}
                  >
                    Cancel
                  </Button>
                )}
              {canAccess(AppPermissions.MANAGE_TEAMS) &&
              isManagerHierarchyEnabled ? (
                isUpdatingHierarchy ? (
                  <Button
                    variant={'outline'}
                    className="mr-2"
                    onClick={saveChanges}
                    disabled={!madeChanges || isSaving}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Save Changes
                  </Button>
                ) : (
                  <Button
                    variant={'outline'}
                    className="mr-2"
                    onClick={() => setIsUpdatingHierarchy(true)}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Edit Hierarchy
                  </Button>
                )
              ) : null}
              {canAccess(AppPermissions.MANAGE_TEAMS) && (
                <Button
                  variant={'default'}
                  onClick={createNewTeam}
                  disabled={isSaving}
                >
                  <PlusCircle className="w-4 h-4 mr-2" />
                  New
                </Button>
              )}
            </div>
          </div>
        ) : null}

        {!editingTeam && (
          <>
            {isUpdatingHierarchy && (
              <div
                className={`mb-4 p-4 border-2 border-dashed rounded-lg transition-all duration-200 ${
                  rootDropState.isDragOver && rootDropState.isValidDropTarget
                    ? 'border-blue-400 bg-blue-50 bg-opacity-80 shadow-lg shadow-blue-200'
                    : 'border-gray-300 bg-gray-50 opacity-60'
                }`}
                onDragOver={handleRootDragOver}
                onDragLeave={handleRootDragLeave}
                onDrop={handleRootDrop}
              >
                <div className="text-center text-sm text-muted-foreground">
                  {rootDropState.isDragOver && rootDropState.isValidDropTarget
                    ? '✓ Drop here to move to root level'
                    : 'Drop here to move to root level'}
                </div>
              </div>
            )}

            <div data-teams-container>
              {teams.map((team) => (
                <TeamCard
                  key={team.id}
                  team={team}
                  onTeamMove={handleTeamMove}
                  draggable={isUpdatingHierarchy}
                  onCardClick={() => startEditTeam(team)}
                />
              ))}
            </div>
          </>
        )}

        {/*****************************/}
        {/******** EDIT TEAM **********/}
        {/*****************************/}
        {editingTeam && !deleteTeam && (
          <>
            <EditTeam team={editingTeam} onClose={closeEditTeam} />
          </>
        )}

        {/*****************************/}
        {/******** DELETE TEAM ********/}
        {/*****************************/}
        {!editingTeam && deleteTeam && (
          <>
            <div className="my-10">
              Do you really want to delete {deleteTeam.name}?
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={cancelDeleteTeam}>
                Back
              </Button>
              <Button variant="destructive" onClick={doDeleteTeam}>
                Delete
              </Button>
            </DialogFooter>
          </>
        )}
      </div>
    </ScrollablePage>
  );
}
