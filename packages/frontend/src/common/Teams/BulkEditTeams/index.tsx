import React, {
  useState,
  useEffect,
  useRef,
  SetStateAction,
  Dispatch,
} from 'react';
import { TeamDto, TeamRoleEnum } from '@/lib/User/types';
import { Shield } from 'lucide-react';
import useTeams from '@/hooks/useTeams';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import AddNew from './AddNew';
import TeamsService from '@/lib/User/Team';
import SelectTeam from './SelectTeam';
import { Id, toast } from 'react-toastify';
import { useQueryClient } from '@tanstack/react-query';
import { Badge } from '@/components/ui/badge';
import useUserSession from '@/hooks/useUserSession';
import { DropdownMenuSubContent } from '@/components/ui/dropdown-menu';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  userIds: number[];
  selectedTeams: TeamDto[];
  setSelectedTeams: Dispatch<SetStateAction<TeamDto[]>>;
  selectedTeamsById: { [key: number]: boolean };
  setSelectedTeamsById: Dispatch<SetStateAction<{ [key: number]: boolean }>>;
  postBuldEditCallback: () => void;
}

export default function BulkEditTeams({
  userIds,
  selectedTeams,
  setSelectedTeams,
  selectedTeamsById,
  setSelectedTeamsById,
  postBuldEditCallback,
}: IProps) {
  const queryClient = useQueryClient();

  const [teams, setTeams] = useState<TeamDto[]>([]);

  const { canAccess } = useUserSession();

  const [searchStr] = useState<string>('');

  const errorToastId = useRef<Id | null>(null);

  /**************************************/
  /**************** INIT ****************/
  /**************************************/

  const { data: allTeams, isLoading: isLoadingTeams } = useTeams(
    0,
    10,
    searchStr,
    true,
  );

  useEffect(() => {
    if (!isLoadingTeams && allTeams) {
      setTeams(allTeams);
    }
  }, [isLoadingTeams, allTeams]);

  /**************************************/
  /************** ACTIONS **************/
  /**************************************/

  const pushNewTeam = (team: TeamDto) => {
    setTeams((old) => [...old, team]);
  };

  async function handleBulkUpdateTeams(
    teams: TeamDto[],
    callback: () => void,
    isAdmin?: boolean,
  ) {
    let role = TeamRoleEnum.MEMBER;
    if (isAdmin) {
      role = TeamRoleEnum.ADMIN;
    }

    const promises = userIds.map((userId) => {
      teams.map((team) => {
        TeamsService.addUserToTeam({
          userId,
          teamId: team.id,
          role,
        });
      });
    });

    try {
      const results = await Promise.allSettled(promises);

      const allSuccessful = results.every(
        (result) => result.status === 'fulfilled',
      );

      if (allSuccessful) {
        console.log('All users added to the team successfully.');
        callback();
      } else {
        console.log('Some users could not be added to the team.');
        errorToastId.current = toast.error(
          'Some users could not be added to the team.',
        );
        results.forEach((result, index) => {
          if (result.status === 'rejected') {
            console.log(`Error adding user ${userIds[index]}:`, result.reason);
          }
        });
        errorToastId.current = toast.error(
          'There was an error processing your request. Please try again.',
        );
      }
    } catch (err) {
      console.log(err);
      errorToastId.current = toast.error(
        'There was an error processing your request. Please try again.',
      );
    }
  }

  const toggleTeam = async (team: TeamDto, isAdmin: boolean) => {
    if (selectedTeamsById[team.id]) {
      setSelectedTeams((old) => {
        return old.filter((t) => t.id !== team.id);
      });
      setSelectedTeamsById((old) => ({ ...old, [team.id]: false }));
      try {
        for (const userId of userIds) {
          await TeamsService.removeUserFromTeam({
            userId,
            teamId: team.id,
          });
        }
      } catch (err) {
        console.log(err);
        errorToastId.current = toast.error(
          'There was an error processing your request. Please try again.',
        );
      }
    } else {
      setSelectedTeams((old) => [...old, team]);
      setSelectedTeamsById((old) => ({ ...old, [team.id]: true }));
    }
  };

  /**************************************/
  /*************** RENDER ***************/
  /**************************************/

  if (!canAccess(AppPermissions.MANAGE_TEAMS)) {
    return (
      <div className="flex items-center flex-wrap">
        {selectedTeams.map((team, i) => {
          let icon = undefined;
          if (team.role == TeamRoleEnum.ADMIN) {
            icon = <Shield size={16} className="text-muted-foreground ml-1" />;
          }
          return (
            <Badge
              key={team.id}
              variant={'secondary'}
              className="flex items-center mr-1"
            >
              {team.name}
              {icon}
            </Badge>
          );
        })}
      </div>
    );
  } else {
    return (
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <DropdownMenuSubContent
          // align="start"
          className="w-[240px] p-0"
          onMouseDown={(e) => {
            e.preventDefault(); // Prevent closing on interaction
            e.stopPropagation();
          }}
        >
          <div>
            <Button
              disabled={!selectedTeams.length}
              variant={selectedTeams.length ? 'default' : 'ghost'}
              className="flex w-full disabled:text-gray-300"
              onClick={() =>
                handleBulkUpdateTeams(selectedTeams, postBuldEditCallback)
              }
            >
              Confirm selection
            </Button>
          </div>
          <Separator />
          {teams.length === 0 ? (
            <div className="text-center text-muted-foreground text-sm py-2">
              No team found
            </div>
          ) : (
            <div className="py-1">
              {teams.map((team) => (
                <SelectTeam
                  key={team.id}
                  team={team}
                  selected={selectedTeamsById[team.id]}
                  toggleTeam={toggleTeam}
                />
              ))}
            </div>
          )}
          <Separator />
          <AddNew addTeam={pushNewTeam} />
          <Separator />
        </DropdownMenuSubContent>
      </div>
    );
  }
}
