import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { RealCallAutoEmailTarget } from '@/lib/Integrations/RealCalls/types';
import { Loader2Icon } from 'lucide-react';
import { useEffect, useState } from 'react';

const propertiesForAutoEmailTarget = {
  [RealCallAutoEmailTarget.AllParticipants]: {
    name: 'All Participants',
    description: 'Send emails to all attendees.',
  },
  [RealCallAutoEmailTarget.InternalParticipants]: {
    name: 'Internal Participants (from your organisation)',
    description: 'Send emails only to attendees from your organisation.',
  },
  [RealCallAutoEmailTarget.None]: {
    name: 'None',
    description: "Don't send any email.",
  },
};

export const RealCallAutoEmailTargetSelector = ({
  className,
  defaultValue,
  onSelect,
  isLoading,
}: {
  className?: string;
  defaultValue?: RealCallAutoEmailTarget | '-';
  onSelect: (v: RealCallAutoEmailTarget | '-') => Promise<unknown>;
  isLoading?: boolean;
}) => {
  const [isLoadingI, setIsLoadingI] = useState(false);
  const [target, setTarget] = useState<
    RealCallAutoEmailTarget | '-' | undefined
  >(defaultValue);

  useEffect(() => {
    setTarget(defaultValue);
  }, [defaultValue]);

  const onSelectInternal = async (newTarget: RealCallAutoEmailTarget | '-') => {
    const oldTarget = target;
    setTarget(newTarget);
    setIsLoadingI(true);
    try {
      await onSelect(newTarget);
    } catch (e) {
      setTarget(oldTarget);
    }
    setIsLoadingI(false);
  };

  return (
    <div className={`flex flex-col ${className || ''}`}>
      <div className="flex flex-row space-x-4">
        <p className="font-medium">
          Send summary email to participants automatically
        </p>
        <Switch
          disabled={isLoadingI || isLoading}
          checked={target !== '-'}
          onCheckedChange={(c) => {
            if (c) {
              onSelectInternal(RealCallAutoEmailTarget.InternalParticipants);
            } else {
              onSelectInternal('-');
            }
          }}
        />
      </div>
      {target !== '-' && (
        <div className="flex flex-row items-center mt-2">
          <Select
            value={target}
            onValueChange={onSelectInternal}
            disabled={isLoadingI || isLoading}
          >
            <SelectTrigger className="w-[400px]">
              {target ? propertiesForAutoEmailTarget[target].name : '...'}
            </SelectTrigger>
            <SelectContent>
              {Object.entries(propertiesForAutoEmailTarget).map((t) => {
                return (
                  <SelectItem
                    key={t[0]}
                    value={t[0]}
                    className="flex flex-col w-full items-start"
                  >
                    <p>{t[1].name}</p>
                    <p className="text-muted-foreground">{t[1].description}</p>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
          <div className="ml-2">
            {(isLoadingI || isLoading) && (
              <Loader2Icon
                className="animate-spin text-muted-foreground"
                size={16}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};
