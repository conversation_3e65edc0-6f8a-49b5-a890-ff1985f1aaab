import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { LightningBoltIcon } from '@radix-ui/react-icons';
import NewCallBlitz from '../newCallBlitz';
import { CallBlitzDto } from '@/lib/CallBlitz/types';

interface INewCallBlitzModalProps {
  open: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  setConfiguration: (session: CallBlitzDto) => void;
}

export default function NewCallBlitzModal({
  open,
  setModalOpen,
  setConfiguration,
}: INewCallBlitzModalProps) {
  return (
    <Dialog open={open} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <LightningBoltIcon className="mr-1" />
            New Call Blitz
          </DialogTitle>
          <DialogDescription className="py-4">
            <NewCallBlitz
              setConfiguration={setConfiguration}
              isInsideModal={true}
              setModalOpen={setModalOpen}
            />
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
