import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import useCallBlitzSearch from '@/hooks/useCallBlitzSearch';
import { TagDto } from '@/lib/Agent/types';
import { CallBlitzDto } from '@/lib/CallBlitz/types';
import { DotsVerticalIcon } from '@radix-ui/react-icons';
import dayjs from 'dayjs';
import {
  ArrowDownToLine,
  CirclePlus,
  LayoutListIcon,
  Loader2Icon,
  PlayIcon,
  Tag,
  Trash2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useRef, useState, useEffect } from 'react';
import NewCallBlitz from './newCallBlitz';
import NewCallBlitzModal from './newCallBlitzModal';
import LinksManager from '@/lib/linksManager';
import useUserSession from '@/hooks/useUserSession';
import RepsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/RepsFilter';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import CallBlitzService from '@/lib/CallBlitz';
import { Id, toast } from 'react-toastify';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface IProps {
  setConfiguration: (session: CallBlitzDto) => void;
}

const defaultNumberOfResults = 30;

export default function AllSessionsCallBlitz({ setConfiguration }: IProps) {
  const { isAdmin, dbOrg } = useUserSession();

  const breadcrumbs: BreadcrumbItem[] = [{ title: 'Call Blitz' }];
  const router = useRouter();

  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );

  const [from, setFrom] = useState<number>(0);
  const [searchSessionString, setSearchSessionString] = useState<string>('');
  const [searchLabel, setSearchLabel] = useState<string>('');
  const [repsFilter, setRepsFilter] = useState<number[]>([]);
  const overwriteSessions = useRef<boolean>(true);

  /*********************************/
  /************ INIT ***********/
  /*********************************/

  const {
    data: sessionsDb,
    isLoading,
    refetch,
  } = useCallBlitzSearch(
    from,
    numberOfResults,
    searchSessionString,
    repsFilter,
  );
  const [sessions, setSessions] = useState<CallBlitzDto[]>([]);

  let showSession = true;
  if (
    !isLoading &&
    sessions?.length === 0 &&
    searchSessionString == '' &&
    repsFilter.length == 0
  ) {
    showSession = false;
  }

  useEffect(() => {
    if (!isLoading && sessionsDb) {
      if (sessionsDb.length >= defaultNumberOfResults) {
        setCanLoadMore(true);
      } else {
        setCanLoadMore(false);
      }

      if (!overwriteSessions.current) {
        setSessions((prev) => [...prev, ...sessionsDb]);
        overwriteSessions.current = true;
      } else {
        setSessions([...sessionsDb]);
      }
    }
  }, [isLoading, sessionsDb]);

  // useEffect(() => {
  //   console.log(sessions);
  // }, [sessions])

  /*********************************/
  /********* LOAD MORE *************/
  /*********************************/

  const [canLoadMore, setCanLoadMore] = useState<boolean>(true);

  const loadMore = () => {
    overwriteSessions.current = false;
    setFrom(from + numberOfResults);
  };

  /*********************************/
  /************ SAERCH *************/
  /*********************************/

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);

  const onSearchChange = (e: any) => {
    setSearchLabel(e.target.value);

    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      setSearchSessionString(e.target.value);
    }, 500);
  };

  /*********************************/
  /************** REPS *************/
  /*********************************/

  const updateRepsFilter = (reps: number[]) => {
    setRepsFilter(reps);
  };

  /*********************************/
  /************ NEW MODAL **********/
  /*********************************/

  const [newModalOpen, setNewModalOpen] = useState(false);

  const openNewModal = () => {
    setNewModalOpen(true);
  };

  const closeModal = () => {
    setNewModalOpen(false);
    refetch();
  };

  const resumeSession = (session: CallBlitzDto) => {
    setConfiguration(session);
  };

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  /*********************************/
  /********* DELETE MODAL **********/
  /*********************************/

  const [deleteModal, setDeleteModal] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const errorToastId = useRef<Id | null>(null);

  const startDeleteSession = (id: number | undefined) => {
    setIsDeleting(false);
    if (id) {
      setDeleteModal(true);
      setSessionToDelete(id);
    }
  };

  const handleCancelDeleteSession = () => {
    setDeleteModal(false);
    setSessionToDelete(null);
  };

  const handleDeleteSession = async () => {
    setIsDeleting(true);
    try {
      await CallBlitzService.deleteSession(sessionToDelete as number);
    } catch (e) {
      errorToastId.current = toast.error(
        'There was an error deleting this session. Please try again.',
      );
    }

    refetch();
    setDeleteModal(false);
  };

  /*********************************/
  /************ RENDERING **********/
  /*********************************/

  if (!showSession) {
    return (
      <div
        style={{
          opacity: isLoading ? 0 : 1,
          transition: 'opacity 0.2s ease-in-out',
        }}
      >
        <DashboardNavbar breadcrumbs={breadcrumbs} />
        <div className="my-8 mx-8 ">
          <NewCallBlitz setConfiguration={setConfiguration} />
        </div>
      </div>
    );
  } else {
    return (
      <div>
        <DashboardNavbar
          breadcrumbs={breadcrumbs}
          rightContent={
            <div className="flex space-x-2">
              <div className="flex space-x-4">
                <Button
                  onClick={openNewModal}
                  variant={'default'}
                  disabled={isPilotEnded}
                >
                  <CirclePlus className="w-4 h-4 mr-2" />
                  New
                </Button>
              </div>
            </div>
          }
          subContent={
            <p>
              Practice with a tagged group of bots with a simulated autodialer
            </p>
          }
        />

        {/* FILTERS */}

        <div className="mt-8 mb-6 mx-4 flex items-center">
          <div className="">
            <Input
              placeholder="Search..."
              value={searchLabel}
              onChange={onSearchChange}
              className="w-[400px] h-[34px]"
            />
          </div>
          {isAdmin && (
            <div className="ml-2">
              <RepsFilter
                current={repsFilter}
                onRepsUpdated={(reps) => {
                  updateRepsFilter(reps.map(Number));
                }}
              />
            </div>
          )}
          {isLoading && (
            <div className="ml-1">
              <Loader2Icon className="animate-spin" />
            </div>
          )}
        </div>
        {/* TABLE */}
        <div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <div className="ml-4">Started At</div>
                </TableHead>
                {isAdmin && <TableHead>Rep</TableHead>}
                <TableHead>Name</TableHead>
                <TableHead>Tags</TableHead>
                <TableHead>&nbsp;</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sessions?.map((session: CallBlitzDto) => {
                const startedAt = dayjs(session.createdAt).format(
                  'MMM D, h:mm A',
                );
                return (
                  <TableRow
                    key={session.id}
                    className="cursor-pointer"
                    onClick={(e) => resumeSession(session)}
                  >
                    <TableCell>
                      <div className="ml-4">{startedAt}</div>
                    </TableCell>
                    {isAdmin && (
                      <TableCell className="flex items-center">
                        <Button
                          variant={'outline'}
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(
                              LinksManager.members(`${session?.assignee?.id}`),
                            );
                          }}
                          className="space-x-2 pl-2 pr-3 py-1 rounded-full hover:bg-muted/80 hover:transition-all duration-300"
                        >
                          <Avatar className="w-6 h-6">
                            {session?.assignee?.avatar && (
                              <AvatarImage src={session?.assignee?.avatar} />
                            )}
                            <AvatarFallback className="text-sm capitalize">
                              {session.assignee?.firstName?.charAt(0)}
                              {session.assignee?.lastName?.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="capitalize">
                            {session.assignee?.firstName}{' '}
                            {session.assignee?.lastName}
                          </div>
                        </Button>
                      </TableCell>
                    )}
                    <TableCell>{session.name}</TableCell>
                    <TableCell>
                      {session.tags?.map((tag: TagDto | number) => (
                        <Badge
                          key={typeof tag === 'number' ? tag : tag.id}
                          variant="default"
                          className="m-1 bg-teal-600"
                        >
                          <Tag size={12} className="mr-1" />
                          {typeof tag === 'number' ? tag : tag.name}
                        </Badge>
                      ))}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="secondary"
                        onClick={(e) => resumeSession(session)}
                      >
                        <PlayIcon className="w-4 h-4 mr-1" />
                        Resume
                      </Button>
                    </TableCell>
                    <TableCell>
                      {isDeleting && sessionToDelete === session.id ? (
                        <Loader2Icon className="animate-spin" size={18} />
                      ) : (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="p-0 rounded-full"
                            >
                              <span className="sr-only">Open menu</span>
                              <DotsVerticalIcon className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              className="cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(
                                  LinksManager.trainingCalls(
                                    `?callblitz=${session.id}`,
                                  ),
                                );
                              }}
                            >
                              <LayoutListIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                              <span>View call history</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="cursor-pointer text-red-400"
                              onClick={(e) => {
                                e.stopPropagation();
                                startDeleteSession(session.id);
                              }}
                              disabled={isPilotEnded}
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              <span>Delete session</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>

          {canLoadMore && (
            <div className="flex justify-center">
              <Button
                onClick={loadMore}
                className="m-4"
                variant="ghost"
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2Icon className="animate-spin mr-2" />
                ) : (
                  <ArrowDownToLine className="mr-2" />
                )}
                Load More
              </Button>
            </div>
          )}
        </div>
        <NewCallBlitzModal
          open={newModalOpen}
          setModalOpen={closeModal}
          setConfiguration={setConfiguration}
        />
        <DeleteConfirmationModal
          open={deleteModal}
          onCancel={handleCancelDeleteSession}
          onConfirm={handleDeleteSession}
          title={'Delete session'}
          description={'Are you sure you want to delete this blitz session?'}
        />
      </div>
    );
  }
}
