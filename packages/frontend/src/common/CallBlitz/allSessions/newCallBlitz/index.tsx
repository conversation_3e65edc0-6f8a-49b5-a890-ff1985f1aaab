import React, { useState, useRef, useEffect } from 'react';
import { TagDto } from '@/lib/Agent/types';
import {
  Tags,
  Loader2Icon,
  ArrowDownToLine,
  X,
  User2,
  Users2,
} from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import useTags from '@/hooks/useTags';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LightningBoltIcon } from '@radix-ui/react-icons';
import { Command } from '@/components/ui/command';
import { Command as CommandPrimitive } from 'cmdk';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import CallBlitzService from '@/lib/CallBlitz';
import { Id, toast } from 'react-toastify';
import { CallBlitzDto } from '@/lib/CallBlitz/types';
import useOrg from '@/hooks/useOrg';
import dayjs from 'dayjs';
import useUserSession from '@/hooks/useUserSession';
import RepsFilter from '@/common/Analytics/DashboardTab/Filters/RepsFilter';

const defaultNumberOfResults = 20;

type SelectedTags = {
  [key: number]: boolean;
};

interface INewCallBlitzProps {
  isInsideModal?: boolean;
  setModalOpen?: (modalOpen: boolean) => void;
  setConfiguration: (session: CallBlitzDto) => void;
}

export default function NewCallBlitz({
  setConfiguration,
  isInsideModal,
  setModalOpen,
}: INewCallBlitzProps) {
  const { isAdmin } = useUserSession();

  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  const { data: org } = useOrg();

  const upsertSessionMutation = useMutation({
    mutationFn: CallBlitzService.upsertSession,
  });

  const assignNewSessionMutations = useMutation({
    mutationFn: CallBlitzService.assignNewSession,
  });

  const inputRef = React.useRef<HTMLInputElement>(null);
  const dummyFocusRef = React.useRef<HTMLInputElement>(null); //to not show the select open on load
  const [open, setOpen] = React.useState(false);
  const [numberOfResults, setNumberOfResults] = useState<number>(
    defaultNumberOfResults,
  );
  const [searchString, setSearchString] = useState<string>('');

  const { data: allTags, isLoading: isLoadingTags } = useTags(
    true,
    0,
    numberOfResults,
    searchString,
  );
  const tags = allTags || [];

  let noMoreTags = false;
  if (allTags) {
    if (allTags.length > 0) {
      if (allTags.length < numberOfResults) {
        noMoreTags = true;
      }
    }
  }

  const [selected, setSelected] = useState<TagDto[]>([]);
  const [selectedById, setSelectedById] = useState<SelectedTags>({});
  const [assignees, setAssignees] = useState<string[]>([]);

  useEffect(() => {
    if (dummyFocusRef.current) {
      dummyFocusRef.current.focus();
    }
  }, []);

  const loadMore = () => {
    setNumberOfResults(numberOfResults + defaultNumberOfResults);
  };

  const startCallBlitz = async () => {
    let configuration: CallBlitzDto = {};
    const d = new Date();
    const dayOfTheWeek = new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
    }).format(d);
    const date = new Intl.DateTimeFormat('en-US').format(d);
    const time = d.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
    let sessionName = `${dayOfTheWeek} ${date} ${time}`;

    if (selected.length > 1) {
      sessionName += ` [${selected.length} tags]`;
    } else if (selected.length == 1) {
      sessionName += ` [${selected[0].name}]`;
    } else {
      sessionName += ` [All Agents]`;
    }

    if (isAdmin && assignees.length > 0) {
      try {
        const sessions: CallBlitzDto[] =
          await assignNewSessionMutations.mutateAsync({
            name: sessionName,
            tags: selected,
            assignToReps: assignees.map(Number),
          });
      } catch (e) {
        if (!toast.isActive(errorToastId.current as Id)) {
          errorToastId.current = toast.error(
            'There was an error creating a new Call Blitz. Please try again.',
          );
        }
      }

      queryClient.invalidateQueries({ queryKey: ['call-blitz-search'] });

      if (setModalOpen) {
        setModalOpen(false);
      }
    } else {
      try {
        const session = await upsertSessionMutation.mutateAsync({
          name: sessionName,
          tags: selected,
        });

        configuration = session;
      } catch (e) {
        if (!toast.isActive(errorToastId.current as Id)) {
          errorToastId.current = toast.error(
            'There was an error creating a new Call Blitz. Please try again.',
          );
        }
      }

      if (setConfiguration) {
        setConfiguration(configuration);
      }
    }
  };

  /*********************************/
  /************ RENDER *************/
  /*********************************/

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      const input = inputRef.current;
      if (input) {
        if (e.key === 'Delete' || e.key === 'Backspace') {
          if (input.value === '') {
            setSelected((prev) => {
              const newSelected = [...prev];
              newSelected.pop();
              return newSelected;
            });
          }
        }
        // This is not a default behaviour of the <input /> field
        if (e.key === 'Escape') {
          input.blur();
        }
      }
    },
    [],
  );

  const handleUnselect = React.useCallback((tag: TagDto) => {
    setSelected((prev) => prev.filter((s) => s.id !== tag.id));
    setSelectedById((prev) => ({ ...prev, [tag.id]: false }));
  }, []);

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <div className="max-w-[800px]">
      {isInsideModal ? (
        <div className="flex justify-between items-center">
          <div>
            <div className="flex items-center space-x-2">
              <Tags className="h-4 w-4" />
              <p className="text-base font-semibold text-primary">
                Select tags
              </p>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center">
            <div>
              <div className="flex items-center space-x-2">
                <Tags className="h-4 w-4" />
                <p className="text-base font-semibold text-primary">
                  Select tags to begin...
                </p>
              </div>
              <p className="text-muted-foreground">
                Agents from the selected tags will be included in the blitz in a
                random order
              </p>
            </div>
          </div>
          <Separator className="mt-4 mb-4" />
        </>
      )}

      {/*********************************/}
      {/*********** TAGS ************/}
      {/*********************************/}
      <Command
        onKeyDown={handleKeyDown}
        className="overflow-visible bg-transparent mt-2"
      >
        <div className="group border border-input px-3 py-2 text-sm ring-offset-background rounded-md ">
          <div className="flex gap-1 flex-wrap">
            {selected.map((tag) => {
              return (
                <Badge key={tag.id} variant="secondary">
                  {tag.name}
                  <button
                    className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleUnselect(tag);
                      }
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onClick={() => handleUnselect(tag)}
                  >
                    <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                  </button>
                </Badge>
              );
            })}
            <CommandPrimitive.Input
              ref={inputRef}
              value={searchString}
              onValueChange={setSearchString}
              onBlur={() => setOpen(false)}
              onFocus={() => setOpen(true)}
              placeholder="Select tags..."
              className="ml-2 bg-transparent outline-none placeholder:text-muted-foreground flex-1"
              disabled={isPilotEnded}
            />
          </div>
        </div>
        <div className="relative mt-1">
          {open && tags.length > 0 && (
            <div className="absolute w-full z-10 top-0 rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
              <div className="overflow-auto">
                {tags.map((tag) => {
                  if (selectedById[tag.id]) {
                    return null;
                  }

                  return (
                    <div
                      key={tag.id}
                      className="flex space-x-2 items-center cursor-pointer hover:bg-muted/80 px-2 py-2"
                      onMouseDown={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onClick={() => {
                        setSearchString('');
                        setSelected((prev) => [...prev, tag]);
                        setSelectedById((prev) => ({
                          ...prev,
                          [tag.id]: true,
                        }));
                      }}
                    >
                      <div className="capitalize">{tag.name}</div>
                    </div>
                  );
                })}

                {noMoreTags ? (
                  <div className="flex justify-center">
                    {isLoadingTags && (
                      <Loader2Icon className="animate-spin mr-2" />
                    )}
                  </div>
                ) : (
                  <>
                    <Separator className="mt-1 mb-1" />
                    <div
                      className="text-sm cursor-pointer hover:bg-muted/80 px-2 py-2"
                      onMouseDown={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onClick={() => {
                        loadMore();
                      }}
                    >
                      {isLoadingTags ? (
                        <Loader2Icon className="animate-spin mr-2" />
                      ) : (
                        <ArrowDownToLine size={14} className="mr-2" />
                      )}
                      Load More
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
          <div
            style={{ visibility: searchString !== '' ? 'visible' : 'hidden' }}
          >
            No tag found for &quot;{searchString}&quot;
          </div>
        </div>
      </Command>
      {isAdmin && (
        <div className={'mb-4 ' + (isInsideModal ? '' : 'mt-4')}>
          {isInsideModal ? (
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center space-x-2">
                  <Users2 className="h-4 w-4" />
                  <p className="text-base font-semibold text-primary">
                    Assign to
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center">
                <div>
                  <div className="flex items-center space-x-2">
                    <Users2 className="h-4 w-4" />
                    <p className="text-base font-semibold text-primary">
                      Assign to...
                    </p>
                  </div>
                  <p className="text-muted-foreground">
                    You can assign this call blitz to specific users
                  </p>
                </div>
              </div>
              <Separator className="mt-4 mb-4" />
            </>
          )}
          <div className="mt-2">
            <RepsFilter
              onRepsUpdated={setAssignees}
              current={assignees.map(Number)}
            />
          </div>
        </div>
      )}
      {/*********************************/}
      {/*********** FOOTER ************/}
      {/*********************************/}
      <div className="flex items-center justify-end">
        <Button
          onClick={startCallBlitz}
          variant={'default'}
          disabled={
            selected.length == 0 ||
            upsertSessionMutation.isPending ||
            isPilotEnded ||
            (isAdmin && assignees.length == 0)
          }
        >
          {isAdmin ? (
            assignNewSessionMutations.isPending ? (
              <>
                <Loader2Icon className="mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <LightningBoltIcon className="mr-2" />
                Assign call blitz
              </>
            )
          ) : upsertSessionMutation.isPending ? (
            <>
              <Loader2Icon className="mr-2 animate-spin" />
              Starting...
            </>
          ) : (
            <>
              <LightningBoltIcon className="mr-2" />
              Start call blitz
            </>
          )}
        </Button>
      </div>
      {/* <input ref={dummyFocusRef} className="opacity-0 h-[0px] w-[0px]" /> */}
    </div>
  );
}
