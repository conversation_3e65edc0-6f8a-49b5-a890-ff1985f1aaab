import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import CallBlitzService from '@/lib/CallBlitz';
import { Loader2Icon } from 'lucide-react';

interface IAccessDeniedModalProps {
  sessionId: number;
}

export default function AccessDeniedModal({
  sessionId,
}: IAccessDeniedModalProps) {
  const [loading, setLoading] = useState(false);

  const router = useRouter();

  const closeNoCloning = () => {
    router.push('/call-blitz');
  };

  const closeCloning = async () => {
    setLoading(true);
    const newSession = await CallBlitzService.clone(sessionId);
    if (newSession) {
      router.push(`/call-blitz/${newSession.id}`);
    }
  };

  return (
    <Dialog open={true} onOpenChange={closeNoCloning}>
      <DialogContent className="close-btn overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center">Access Denied</DialogTitle>
          <DialogDescription>
            This session is assigned to another user.
          </DialogDescription>
        </DialogHeader>
        <div>
          You can create a new session or clone this session&apos;s settings
        </div>
        <DialogFooter>
          {loading ? (
            <div>
              <Loader2Icon className="animate-spin" />
            </div>
          ) : (
            <>
              <Button onClick={closeCloning} variant="outline">
                Clone
              </Button>
              <Button onClick={closeNoCloning}>New</Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
