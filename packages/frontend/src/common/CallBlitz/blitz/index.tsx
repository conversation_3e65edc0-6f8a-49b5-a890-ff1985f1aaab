import React, { useState, useRef, useEffect, useTransition } from 'react';
import { TagDto } from '@/lib/Agent/types';
import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import {
  Loader2Icon,
  Tag,
  ChevronLeft,
  ChevronRight,
  InfoIcon,
  PhoneForwardedIcon,
  SkipForwardIcon,
  LayoutListIcon,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AgentDto } from '@/lib/Agent/types';
import useCallBlitzRandomAgents from '@/hooks/useCallBlitzRandomAgents';
import BuyerCard, { CardType } from '@/components/BuyerCard';
import { Switch } from '@/components/ui/switch';
import { useRouter } from 'next/navigation';
import useCallBliz from '@/hooks/useCallBlitz';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import CallBlitzService from '@/lib/CallBlitz';
import AccessDeniedModal from './accessDeniedModal';
import CallSimulation from '@/components/CallSimulation';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import LinksManager from '@/lib/linksManager';

interface IBlitzProps {
  sessionId: number;
}

class BlitzFetchingState {
  fetchedBots: number[] = [];
  prevBatchFetched: number = -1;
  currentBatchFetched: number = 0;
  firstBatch: boolean = true;
}

export default function Blitz({ sessionId }: IBlitzProps) {
  const curSearchParams = new URLSearchParams();
  const router = useRouter();

  //to handle the case when the user is not assigned to this session
  let showAccessDenied = false;
  const {
    data: session,
    isError,
    isLoading: isLoadingSession,
  } = useCallBliz(sessionId);
  if (isError) {
    showAccessDenied = true;
  }

  const [sessionName, setSessionName] = useState<string>(session?.name || '');
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([
    { title: 'Call Blitz', href: '/call-blitz' },
    { title: session?.name },
  ]);

  useEffect(() => {
    if (session) {
      setBreadcrumbs([
        { title: 'Call Blitz', href: '/call-blitz' },
        { title: session.name },
      ]);
      setSessionName(session.name || '');
    }
  }, [session]);

  const [batchCallProgress, setBatchCallProgress] = useState<number>(0);
  const [agentCallProgress, setAgentCallProgress] = useState<number>(0);
  const [totNumberOfAgents, setTotNumberOfAgents] = useState<number>(0);
  const [callHistory, setCallHistory] = useState<AgentDto[]>([]);
  const scrollToBottomDiv = useRef<HTMLDivElement>(null);
  const [_, startLoadCallCard] = useTransition(); //needed if user starts spamming next btn

  /**************************************/
  /*************** INIT *****************/
  /**************************************/

  const numberOfAgentsPerBatch = 10;
  const fetchState = useRef<BlitzFetchingState>(new BlitzFetchingState());
  const { data: nextAgentsBatch, isLoading: isLoadingNextBatch } =
    useCallBlitzRandomAgents(
      sessionId,
      numberOfAgentsPerBatch,
      fetchState.current.fetchedBots,
    );

  let _currentAgent: AgentDto | undefined = undefined;
  let _previousAgent: AgentDto | undefined = undefined;
  let _agentsHistory: AgentDto[] = [];
  let _totNumberOfAgents = 0;
  if (!isLoadingNextBatch && session) {
    if (
      nextAgentsBatch &&
      fetchState.current.currentBatchFetched !=
        fetchState.current.prevBatchFetched
    ) {
      _totNumberOfAgents = nextAgentsBatch?.numberOfAgentsInSession || 0;
      if (nextAgentsBatch.agents.length > 0) {
        _currentAgent = nextAgentsBatch.agents[batchCallProgress];
      }

      if (!_currentAgent) {
        if (nextAgentsBatch.calledAgents.length > 0) {
          _currentAgent = nextAgentsBatch.calledAgents.pop();
        }
      }
      _agentsHistory = nextAgentsBatch.calledAgents;
      fetchState.current.currentBatchFetched =
        fetchState.current.prevBatchFetched;
    }
  }

  const [currentAgent, setCurrentAgent] = useState<AgentDto | undefined>(
    _currentAgent,
  );
  const [previousAgent, setPreviousAgent] = useState<AgentDto | undefined>(
    _previousAgent,
  );
  const [noMoreBots, setNoMoreBots] = useState<boolean>(false);

  if (_currentAgent) {
    if (fetchState.current.firstBatch) {
      if (_agentsHistory.length > 0) {
        _previousAgent = _agentsHistory[_agentsHistory.length - 1];
        setAgentCallProgress(_agentsHistory.length);
      }
      setCallHistory([..._agentsHistory, _currentAgent]);
      fetchState.current.firstBatch = false;
      setTotNumberOfAgents(_totNumberOfAgents);
      setCurrentAgent(_currentAgent);
      setPreviousAgent(_previousAgent);

      if (nextAgentsBatch && nextAgentsBatch.agents.length == 0) {
        setNoMoreBots(true);
      }
    } else {
      if (callHistory && callHistory.length > 0) {
        _previousAgent = callHistory[callHistory.length - 1];
      }
      setPreviousAgent(_previousAgent);
      setAgentCallProgress(callHistory.length);
      setCurrentAgent(_currentAgent);
      // @ts-ignore
      setCallHistory((old) => [...old, _currentAgent]);
    }
  }

  const [nextAgent, setNextAgent] = useState<AgentDto | undefined>();

  const endCallBlitz = () => {
    curSearchParams.set('callblitz', String(sessionId));
    router.push(LinksManager.trainingCalls(`?${curSearchParams.toString()}`));
  };

  /**************************************/
  /********** LOAD NEXT LOGIC ***********/
  /**************************************/

  const loadNextRandomAgent = () => {
    setNextAgent(undefined);
    setAgentCallProgress(callHistory.length);
    const nextBatchProgress = batchCallProgress + 1;
    if (nextAgentsBatch) {
      if (nextBatchProgress < nextAgentsBatch?.agents.length) {
        const pa = nextAgentsBatch.agents[batchCallProgress];
        setPreviousAgent(pa);

        const na = nextAgentsBatch.agents[nextBatchProgress];
        startLoadCallCard(() => {
          setCurrentAgent(na);
        });
        setCallHistory([...callHistory, na]);
        setBatchCallProgress(nextBatchProgress);

        //check if we reached the end:
        const nextCounter = nextBatchProgress + 1;
        if (nextCounter >= nextAgentsBatch?.agents.length) {
          if (nextAgentsBatch?.agents.length < numberOfAgentsPerBatch) {
            setNoMoreBots(true);
          }
        }
      } else {
        if (nextAgentsBatch?.agents.length < numberOfAgentsPerBatch) {
          setNoMoreBots(true);
        } else {
          nextAgentsBatch?.agents.map((a) => {
            fetchState.current.fetchedBots.push(a.id);
          });
          fetchState.current.currentBatchFetched++;
          setBatchCallProgress(0); //restart from beginning of next batch, it also triggers rendering
        }
      }
    }
  };

  const loadNextAgent = () => {
    const _agentCallProgress = agentCallProgress + 1;
    setAgentCallProgress(_agentCallProgress);

    let atEndOfBatch = true;

    if (callHistory && callHistory.length > 0) {
      if (_agentCallProgress < callHistory.length) {
        atEndOfBatch = false;
        const na = callHistory[_agentCallProgress];
        setPreviousAgent(currentAgent);
        startLoadCallCard(() => {
          setCurrentAgent(na);
        });
        const nextCounter = _agentCallProgress + 1;
        if (nextCounter >= callHistory.length) {
          setNextAgent(undefined);
          //check if we reached the end:
          const nextBatchProgress = batchCallProgress + 1;
          if (nextAgentsBatch) {
            if (nextBatchProgress >= nextAgentsBatch?.agents.length) {
              if (nextAgentsBatch?.agents.length < numberOfAgentsPerBatch) {
                setNoMoreBots(true);
              }
            }
          }
        } else {
          const na = callHistory[_agentCallProgress];
          setNextAgent(na);
        }
      }
    }

    if (atEndOfBatch) {
      loadNextRandomAgent();
    }
  };

  const callPreviousAgent = () => {
    setNoMoreBots(false);
    setNextAgent(currentAgent);
    setCurrentAgent(previousAgent);
    let _agentCallProgress = agentCallProgress - 1;
    if (_agentCallProgress < 0) {
      _agentCallProgress = 0;
    }

    setAgentCallProgress(_agentCallProgress);
    const prevBatchProgress = _agentCallProgress - 1;
    if (prevBatchProgress < 0) {
      setPreviousAgent(undefined);
    } else {
      if (callHistory && callHistory.length > 0) {
        const na = callHistory[prevBatchProgress];
        setPreviousAgent(na);
      }
    }
  };

  /**************************************/
  /******** SESSION NAME UPDATE *********/
  /**************************************/

  const queryClient = useQueryClient();
  const timeoutUpdateName = useRef<ReturnType<typeof setTimeout>>();
  const upsertSessionMutation = useMutation({
    mutationFn: CallBlitzService.upsertSession,
  });

  const updateSessionName = async (name: string) => {
    if (timeoutUpdateName.current) {
      clearTimeout(timeoutUpdateName.current);
    }

    setSessionName(name);
    setBreadcrumbs([{ title: 'Call Blitz' }, { title: name }]);

    timeoutUpdateName.current = setTimeout(async () => {
      try {
        await upsertSessionMutation.mutateAsync({
          id: sessionId,
          name: name,
        });

        queryClient.invalidateQueries({ queryKey: ['get-call-blitz'] });
      } catch (e) {
        console.log(e);
      }
    }, 500);
  };

  /**************************************/
  /************ CALL MANAGEMENT *********/
  /**************************************/

  const [latestCall, setLatestCall] = useState<any>();
  const [callOngoing, setCallOngoing] = useState<boolean>(false);
  const [autoplay, setAutoplay] = useState<boolean>(true);
  const [firstCallStarted, setFirstCallStarted] = useState<boolean>(false);

  const onCallEnds = () => {
    setCallOngoing(false);
    if (autoplay) {
      loadNextAgent();
    }
  };

  const onCallStarts = () => {
    setFirstCallStarted(true);
  };

  const startCallWithAgent = (agent: AgentDto) => {
    if (!callOngoing) {
      if (callHistory && callHistory.length > 0) {
        const index = callHistory.findIndex((a) => a.id == agent.id);
        if (index > -1) {
          setAgentCallProgress(index);
        }
        const prevIndex = index - 1;
        if (prevIndex > -1) {
          const na = callHistory[prevIndex];
          setPreviousAgent(na);
        } else {
          setPreviousAgent(undefined);
        }

        const nextIndex = index + 1;
        if (nextIndex < callHistory.length) {
          const na = callHistory[nextIndex];
          setNextAgent(na);
        } else {
          setNextAgent(undefined);
        }
      }
      setCurrentAgent(agent);
    }
  };

  /**************************************/
  /*************** RENDER ***************/
  /**************************************/

  setTimeout(async () => {
    if (currentAgent || previousAgent) {
      if (previousAgent) {
        document
          .getElementById('agentCard' + previousAgent.id)
          ?.scrollIntoView({ behavior: 'smooth' });
      } else if (currentAgent) {
        document
          .getElementById('agentCard' + currentAgent.id)
          ?.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      scrollToBottomDiv.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, 200);

  //to manage the window resize event
  const botsContainer = useRef<HTMLDivElement>(null);
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout>>();

  const handleWindowSizeChange = (e?: Event) => {
    if (botsContainer.current) {
      const bx = botsContainer.current;
      bx.style.opacity = '0';
      bx.style.height = '0px';

      if (timeoutSearchRes.current) {
        clearTimeout(timeoutSearchRes.current);
      }

      timeoutSearchRes.current = setTimeout(async () => {
        if (bx.parentElement) {
          bx.style.opacity = '1';
          bx.style.height =
            bx.parentElement.getBoundingClientRect().height + 'px';
          scrollToBottomDiv.current?.scrollIntoView({ behavior: 'smooth' });
        }
      }, 200);
    }
  };

  useEffect(() => {
    handleWindowSizeChange();

    window.addEventListener('resize', handleWindowSizeChange);

    return () => {
      window.removeEventListener('resize', handleWindowSizeChange);
    };
  }, []);

  if (showAccessDenied) {
    return <AccessDeniedModal sessionId={sessionId} />;
  }

  return (
    <div
      className="h-screen overflow-hidden flex-col flex"
      style={{ opacity: isLoadingSession ? 0 : 1 }}
    >
      {/*********************************/}
      {/************* HEADER ************/}
      {/*********************************/}
      <DashboardNavbar breadcrumbs={breadcrumbs} />

      {/*********************************/}
      {/*********** CMD LINE ************/}
      {/*********************************/}
      <div className="py-2 px-6 flex items-center flex-wrap">
        <div className="text-center text-xs font-semibold mr-2">
          {callHistory.length} out of {totNumberOfAgents}
        </div>
        <div className="flex-1">
          <div className="flex items-center">
            {session?.tags?.map((tag: TagDto) => {
              return (
                <div key={tag.id} className="mr-2">
                  <Badge
                    variant="secondary"
                    className="bg-teal-600 text-white py-1"
                  >
                    <Tag className="w-4 h-4 mr-1" />
                    {tag.name}
                  </Badge>
                </div>
              );
            })}
          </div>
        </div>
        <div className="flex items-center">
          <div className="mr-2">Blitz Name:</div>
          <div className="mr-6">
            <Input
              value={sessionName}
              onChange={(e) => {
                updateSessionName(e.target.value);
              }}
              className="w-56"
              placeholder="Call Blitz Name..."
            />
          </div>

          <div
            className="mr-6 flex items-center cursor-pointer space-x-2"
            onClick={() => {
              setAutoplay((o) => !o);
            }}
          >
            <Switch checked={autoplay} />
            <div className="text-sm font-normal">Autoplay</div>
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger>
                  <InfoIcon className="w-4 h-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Starts the next call automatically after each call ends</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Button
            onClick={endCallBlitz}
            variant={'default'}
            disabled={callOngoing}
          >
            <LayoutListIcon className="w-4 h-4 mr-2" />
            View call history
          </Button>
        </div>
      </div>
      <Separator className="w-full" />

      {/*********************************/}
      {/************* PAGE  *************/}
      {/*********************************/}

      <div className="flex flex-grow">
        <div
          className="md:w-[475px] p-2 border-r overflow-auto"
          ref={botsContainer}
        >
          {/*********** AGENTS LIST ************/}
          {callHistory.map((agent, i) => {
            return (
              <div id={'agentCard' + agent.id} key={agent.id} className="p-1">
                <BuyerCard
                  // includeAgentsTags={true}
                  agent={agent}
                  type={CardType.COMPACT}
                  showMenu={false}
                  isSelected={agent.id == currentAgent?.id}
                  onCardClick={() => {
                    startCallWithAgent(agent);
                  }}
                />
              </div>
            );
          })}

          {/*********** div used to control page's scroll ************/}
          <div ref={scrollToBottomDiv} />
        </div>

        {/*********************************/}
        {/************* CALL **************/}
        {/*********************************/}
        <div className="flex-grow flex flex-col items-center">
          <div>
            {/*********** TOP COMMAND  ************/}
            <div className="flex mt-8 mb-4">
              {/*********** NEXT/PREVIOUS BUTTONS ************/}
              <div className="flex">
                <div className="mr-2">
                  <Button
                    variant={'outline'}
                    disabled={
                      callOngoing || isLoadingNextBatch || !previousAgent
                    }
                    className="h-[40px]"
                    onClick={callPreviousAgent}
                  >
                    <ChevronLeft />
                  </Button>
                </div>
                <div className="">
                  {nextAgent && (
                    <Button
                      variant={'outline'}
                      disabled={callOngoing || isLoadingNextBatch}
                      className="h-[40px]"
                      onClick={loadNextAgent}
                    >
                      <ChevronRight />
                    </Button>
                  )}
                </div>
              </div>
              <div className="flex-1" />
              {/*********** NEXT BUTTON ************/}
              {noMoreBots ? (
                <div>&nbsp;</div>
              ) : (
                <div className="">
                  <Button
                    variant={'default'}
                    className="h-[40px] items-center flex"
                    // style={{
                    //   backgroundImage:
                    //     "linear-gradient(to right, #000000, #5189CE, #A168A2)",
                    // }}
                    onClick={loadNextRandomAgent}
                    disabled={callOngoing || isLoadingNextBatch}
                  >
                    {isLoadingNextBatch ? (
                      <Loader2Icon className="animate-spin" />
                    ) : (
                      <>
                        <SkipForwardIcon className="w-4 h-4" />
                        <div className="ml-2 flex">Call next</div>
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>

            {/*********** CALL CARD ************/}
            {/* {
            currentAgent && (<ColdCallSimulationCard key={currentAgent.id}
              agent={currentAgent}
              isLoadingAgent={false}
              latestCall={latestCall}
              callOngoing={callOngoing}
              setCallOngoing={setCallOngoing}
              setLatestCall={setLatestCall}
              isDemoAgent={false}
              location="call_blitz"
              compactView={true}
              onCallEnds={onCallEnds}
              callBlitzSessionId={sessionId} />)
          } */}
            {currentAgent && (
              <CallSimulation
                key={currentAgent.id}
                agent={currentAgent}
                onCallEnds={onCallEnds}
                onCallStarts={onCallStarts}
                callBlitzSessionId={sessionId}
                includeAgentsTags={true}
                playOnLoad={autoplay && firstCallStarted}
                autoPlayDelay={3}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
