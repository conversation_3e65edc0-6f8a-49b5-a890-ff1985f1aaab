import { CallBlitzDto } from '@/lib/CallBlitz/types';
import { usePathname, useRouter } from 'next/navigation';
import Blitz from './blitz';
import ConfigureBlitz from './allSessions';

interface ICallBlitzProps {
  sessionId?: number;
}

export default function CallBlitz({ sessionId }: ICallBlitzProps) {
  const router = useRouter();
  const pathname = usePathname();

  const updateConfiguration = (session: CallBlitzDto) => {
    if (session) {
      router.push(`${pathname}/${session.id}`);
    }
  };

  if (!sessionId) {
    return <ConfigureBlitz setConfiguration={updateConfiguration} />;
  } else {
    return <Blitz sessionId={Number(sessionId)} />;
  }
}
