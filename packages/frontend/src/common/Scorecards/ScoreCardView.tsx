import { Card, CardContent } from '@/components/ui/card';
import { MoreVerticalIcon, Pencil, Share2 } from 'lucide-react';

import ScorecardPreviewDetailView from './scorecardPreview/detailView';
import ScorecardPreviewRepView from './scorecardPreview/repView';
import { AgentCallType } from '@/lib/Agent/types';
import { Button } from '@/components/ui/button';
import { timeAgo } from '@/lib/utils';
import { CallTypeBadge } from './CallTypeBadge';
import { Badge } from '@/components/ui/badge';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { Dispatch, SetStateAction } from 'react';

interface IProps {
  activeTab: 'detail-view' | 'rep-view';
  canCreateSubOrgs: boolean;
  onEditScorecardPress: (id: number) => void;
  selectedScorecardDto: ScorecardConfigDto;
  setShareWithOrgPnl: Dispatch<SetStateAction<boolean>>;
}

export default function ScoreCardView({
  activeTab,
  canCreateSubOrgs,
  onEditScorecardPress,
  selectedScorecardDto,
  setShareWithOrgPnl,
}: IProps) {
  return (
    <Card className="mt-4 flex-1 relative">
      <CardContent className="absolute top-0 left-0 right-0 bottom-0 px-3 py-4 flex flex-col">
        <div className="flex flex-row justify-between">
          <div className=" flex flex-col pb-2">
            <p>
              <span className="font-medium">{selectedScorecardDto.tag}</span>
            </p>
            <p className="text-muted-foreground mt-2">
              Updated{' '}
              {timeAgo(
                selectedScorecardDto.updatedAt
                  ? new Date(selectedScorecardDto.updatedAt)
                  : new Date(),
              )}
            </p>
          </div>
          <div className="flex flex-row space-x-2">
            {canCreateSubOrgs && !!selectedScorecardDto && (
              <Button
                variant={'outline'}
                onClick={() => {
                  setShareWithOrgPnl(true);
                }}
                className="mr-2"
              >
                <Share2 size={16} className="mr-2" />
                Share with organization
              </Button>
            )}
            {!!selectedScorecardDto?.editable && (
              <Button
                variant={'outline'}
                onClick={() => {
                  onEditScorecardPress(selectedScorecardDto.id || 0);
                }}
              >
                <Pencil size={16} className="mr-2" />
                Edit
              </Button>
            )}
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          <p className="mt-1">
            {selectedScorecardDto.callTypes?.length ? (
              selectedScorecardDto.callTypes.map((c) => {
                return (
                  <CallTypeBadge
                    key={c.callType}
                    className="mt-2 mr-1"
                    callType={c.callType as AgentCallType}
                    showDefaultLabel={c.isDefaultForCallType}
                  />
                );
              })
            ) : (
              <Badge variant={'secondary'} className="mt-2 mr-1 font-normal">
                All
              </Badge>
            )}
          </p>
          {activeTab === 'detail-view' && (
            <ScorecardPreviewDetailView scorecard={selectedScorecardDto} />
          )}
          {activeTab === 'rep-view' && (
            <ScorecardPreviewRepView scorecard={selectedScorecardDto} />
          )}
        </div>
      </CardContent>
    </Card>
  );
}
