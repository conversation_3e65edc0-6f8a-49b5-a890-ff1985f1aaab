import useScorecardConfigMaterials from '@/hooks/useScorecardConfigMaterials';
import { Loader2Icon } from 'lucide-react';
import MaterialList from '../MaterialList';
import { ScorecardConfigMaterialScope } from '@/lib/ScorecardConfig/types';
import { useMemo } from 'react';

interface IProps {
  scorecardConfigId: number;
  scope: ScorecardConfigMaterialScope ;
}

export default function ViewLearningMaterials({
  scorecardConfigId,
  scope,
}: IProps) {
  const { data: learningMaterialsDb, isLoading } =
    useScorecardConfigMaterials(scorecardConfigId);
    const filteredLearningMaterials = useMemo(() => {
      return learningMaterialsDb?.filter(l => l.scope === scope) || [];
    }, [learningMaterialsDb, scope]);
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="mt-20 w-full flex justify-center">
          <Loader2Icon
            className="animate-spin text-muted-foreground"
            size={32}
          />
        </div>
      );
    } else {
      if (filteredLearningMaterials && filteredLearningMaterials.length > 0) {
        return (
          <div className="mt-4 pr-3">
            <MaterialList
              scorecardConfigId={scorecardConfigId}
              materialList={filteredLearningMaterials}
              scope={ScorecardConfigMaterialScope.SCORECARD}
              viewOnly={true}
            />
          </div>
        );
      } else {
        return (
          <div className="text-muted-foreground mt-4">
            No learning material found
          </div>
        );
      }
    }
  };
  return renderContent();
}
