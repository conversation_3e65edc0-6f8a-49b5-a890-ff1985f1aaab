import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useEffect, useRef, useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import DragAndDrop, {
  DropRejectedReason,
} from '@/common/DragAndDropModal/DragAndDrop';
import { ChevronDown, Folder, Loader2Icon } from 'lucide-react';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import { ToastContainer } from 'react-toastify';
import { ScorecardConfigMaterialScope } from '@/lib/ScorecardConfig/types';

interface IAddMaterialModalProps {
  open: boolean;
  onClose: () => void;
  scorecardConfigId: number | undefined;
  scope: ScorecardConfigMaterialScope;
  onAddDone: (newMaterialId?: number) => Promise<void>;
}

export interface ILearningMaterialDto {
  title: string | undefined;
  description: string | undefined;
  type: 'LINK' | 'FILE';
  scope: ScorecardConfigMaterialScope;
  link: string | undefined;
  metadata: {
    fileType: string | undefined;
    fileName: string | undefined;
    fileSize: number | undefined;
  };
  id?: number;
}
export default function AddMaterialModal({
  open,
  onClose,
  scorecardConfigId,
  scope,
  onAddDone,
}: IAddMaterialModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [fileUploadError, setFileUploadError] = useState('');
  const uploadedFile = useRef<File | null>(null);

  const [learningMaterial, setLearningMaterial] =
    useState<ILearningMaterialDto>({
      title: '',
      description: '',
      type: 'LINK',
      scope,
      link: '',
      metadata: {
        fileType: '',
        fileName: '',
        fileSize: 0,
      },
    });
  const handleAddLearningMaterial = async () => {
    setIsLoading(true);
    setError('');

    if (learningMaterial.title == '') {
      setError('Title cannot be empty');
      setIsLoading(false);
      return;
    }
    if (learningMaterial.type === 'FILE' && uploadedFile.current === null) {
      setError('No file has been added');
      setIsLoading(false);
      return;
    }
    if (learningMaterial.type === 'LINK' && learningMaterial.link === '') {
      setError('No link has been added');
      setIsLoading(false);
      return;
    }

    try {
      if (scorecardConfigId) {
        const newMaterial = await ScorecardConfigService.upsertLearningMaterial(
          scorecardConfigId,
          learningMaterial.title || '',
          learningMaterial.description || '',
          learningMaterial.type,
          learningMaterial.link || '',
          JSON.stringify(learningMaterial.metadata),
          scope,
        );
        if (newMaterial && newMaterial.id) {
          if (uploadedFile.current) {
            await ScorecardConfigService.uploadLearningMaterialFile(
              newMaterial.id,
              uploadedFile.current,
            );
          }
        }
        await onAddDone(newMaterial?.id);
        setIsLoading(false);
        onClose();
      }
    } catch (e) {
      setError('Something went wrong, please try again or contact support');
      console.log(error);
      console.error(e);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    return () => {
      if (!open) {
        setLearningMaterial({
          title: '',
          description: '',
          type: 'LINK',
          scope,
          link: '',
          metadata: {
            fileType: '',
            fileName: '',
            fileSize: 0,
          },
        });
        uploadedFile.current = null;
      }
    };
  }, [open]);
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[476px] top-[50%] translate-y-[-50%]">
        <DialogHeader>
          <DialogTitle>Add new Learning Material</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-6">
          <div className="flex flex-col gap-4 mt-6">
            <div className="flex flex-col gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="name"
                value={learningMaterial.title}
                placeholder="Enter"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  setLearningMaterial((prev) => {
                    return {
                      ...prev,
                      title: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={learningMaterial.description}
                className="resize-none"
                placeholder="Enter"
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                  setLearningMaterial((prev) => {
                    return {
                      ...prev,
                      description: e.target.value,
                    };
                  });
                }}
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="type">Type</Label>
              <Select
                defaultValue="LINK"
                onValueChange={(value: string) => {
                  setLearningMaterial((prev) => {
                    return {
                      ...prev,
                      type: value as 'LINK' | 'FILE',
                    };
                  });
                }}
              >
                <SelectTrigger icon={ChevronDown}>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LINK">Link</SelectItem>
                  <SelectItem value="FILE">File</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {learningMaterial.type === 'LINK' ? (
              <div className="flex flex-col gap-2">
                <Label htmlFor="link">Enter Link</Label>
                <Input
                  id="link"
                  value={learningMaterial.link}
                  placeholder="https://"
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setLearningMaterial((prev) => {
                      return {
                        ...prev,
                        link: e.target.value,
                      };
                    });
                  }}
                />
              </div>
            ) : (
              <DragAndDrop
                onDrop={(files: File[]) => {
                  const file = files[0];
                  if (file) {
                    uploadedFile.current = file;
                    setLearningMaterial((prev) => {
                      return {
                        ...prev,
                        metadata: {
                          fileType: file.type,
                          fileName: file.name,
                          fileSize: file.size,
                        },
                      };
                    });
                  }
                }}
                onDropRejected={(reason) => {
                  if (reason === DropRejectedReason.FileSizeExceeded) {
                    setFileUploadError(
                      'File size cannot exceed 5MB. Please upload a file smaller than 50mb',
                    );
                  }
                }}
                accept={{
                  'application/pdf': ['.pdf'],
                  'application/rtf': ['.rtf'],
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                    ['.docx'],
                  'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                    ['.pptx'],
                  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                    ['.xlsx'],
                  'text/csv': ['.csv'],
                  'text/markdown': ['.md'],
                  'text/plain': ['.txt'],
                }}
                maxSize={50 * 1024 * 1024} // 50MB
                renderContent={(isDragActive) => (
                  <div className="bg-zinc-50 p-6 rounded-lg flex flex-col items-center justify-center gap-4 w-full">
                    {isDragActive ? (
                      <div className="text-sm text-zinc-500">
                        Drop file here...
                      </div>
                    ) : (
                      <>
                        <div className="text-center">
                          <p className="text-sm text-zinc-800 font-medium">
                            Drag and drop a file or click to browse
                          </p>
                        </div>
                        {uploadedFile.current ? (
                          <div className="text-lg text-zinc-800 font-bold">
                            {learningMaterial.metadata.fileName}
                          </div>
                        ) : (
                          <Button variant="outline">
                            <Folder className="w-4 h-4 mr-2" />
                            Browse
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                )}
              />
            )}
            {fileUploadError && (
              <div className="text-sm text-red-500">{fileUploadError}</div>
            )}
          </div>
          <div className="flex gap-2 justify-end">
            {!isLoading ? (
              <Button variant={'outline'} onClick={onClose}>
                Cancel
              </Button>
            ) : (
              <></>
            )}
            <Button
              variant={'default'}
              onClick={handleAddLearningMaterial}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2Icon className="animate-spin" size={16} />
              ) : (
                'Add'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
      <ToastContainer />
    </Dialog>
  );
}
