import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  LearningMaterialDto,
  ScorecardConfigMaterialScope,
} from '@/lib/ScorecardConfig/types';
import {
  ExternalLink,
  FileIcon,
  LinkIcon,
  MoreHorizontal,
  Pencil,
  PresentationIcon,
  Trash,
} from 'lucide-react';
import { useState } from 'react';
import EditMaterialModal from '../EditMaterialModal';
import { ILearningMaterialDto } from '../AddMaterialModal';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import { cn } from '@/lib/utils';
interface IScorecardConfig {
  scorecardConfigId: number | undefined;
  materialList: LearningMaterialDto[];
  onEditDone?: (id: number) => Promise<void>;
  onDeleteDone?: (id: number) => Promise<void>;
  scope: ScorecardConfigMaterialScope;
  viewOnly?: boolean;
}

export const getIcon = (material: LearningMaterialDto) => {
  if (material.type === 'LINK') {
    return <LinkIcon className="w-4 h-4 text-[#71717A]" />;
  } else if (material.type === 'FILE') {
    const metadata =
      typeof material.metadata === 'string'
        ? JSON.parse(material.metadata)
        : material.metadata;
    if (
      metadata?.fileType?.includes('pptx') ||
      metadata?.fileType?.includes('presentation')
    ) {
      return <PresentationIcon className="w-4 h-4 text-[#71717A]" />;
    } else {
      return <FileIcon className="w-4 h-4 text-[#71717A]" />;
    }
  }
};

export default function MaterialList({
  scorecardConfigId,
  materialList,
  onEditDone,
  onDeleteDone,
  scope,
  viewOnly = false,
}: IScorecardConfig) {
  const [openEditLearningMaterialModal, setOpenEditLearningMaterialModal] =
    useState(false);
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<
    LearningMaterialDto | undefined
  >(undefined);

  const handleDelete = async () => {
    setIsDeleting(true);
    if (selectedMaterial?.id) {
      try {
        await ScorecardConfigService.deleteLearningMaterial(
          selectedMaterial?.id,
        );
        if (onDeleteDone) {
          await onDeleteDone(selectedMaterial?.id);
        }
        setIsDeleting(false);
        setOpenDeleteConfirmation(false);
      } catch (e) {
        console.log(e);
        setIsDeleting(false);
      }
    }
  };
  return (
    <>
      {viewOnly ? (
        <></>
      ) : (
        <EditMaterialModal
          open={openEditLearningMaterialModal}
          onClose={() => {
            setOpenEditLearningMaterialModal(false);
          }}
          material={selectedMaterial as ILearningMaterialDto}
          scorecardConfigId={scorecardConfigId}
          scope={scope}
          onEditDone={onEditDone}
        />
      )}
      {viewOnly ? (
        <></>
      ) : (
        <DeleteConfirmationModal
          open={openDeleteConfirmation}
          onCancel={() => {
            setOpenDeleteConfirmation(false);
          }}
          onConfirm={handleDelete}
          title="Delete item"
          description="Are you sure you want to delete this?"
          isLoading={isDeleting}
        />
      )}
      <div className="flex flex-col gap-2 mt-4">
        {materialList?.map((material) => (
          <div
            key={material.id}
            className={cn('flex border border-[#E4E4E7] rounded-lg', {
              'cursor-pointer': viewOnly,
            })}
            onClick={() => {
              if (viewOnly) {
                window.open(material.link, '_blank');
              }
            }}
          >
            <div className="flex w-full overflow-hidden">
              <div className="flex items-center justify-center border-r border-[#E4E4E7]">
                <div className="flex bg-[#F4F4F5] w-8 h-8 rounded-full ml-[18px] mr-[18px] items-center justify-center">
                  {getIcon(material)}
                </div>
              </div>
              <div className="pl-[12px] pt-[12px] pb-[12px] flex-1 overflow-auto">
                <div className="text-[#09090B] font-medium leading-[20px] text-sm">
                  {material.title}
                </div>
                {material.description ? (
                  <div className="text-[#71717A] font-normal leading-[20px] text-sm whitespace-pre-line max-h-[100px] overflow-y-auto pr-1">
                    {material.description}
                  </div>
                ) : (
                  <></>
                )}
                {material.type === 'LINK' ? (
                  <div className="text-[#71717A] font-normal leading-[20px] text-sm truncate">
                    {material.link}
                  </div>
                ) : (
                  <></>
                )}
              </div>
              <div className="mr-[12px] w-4 h-4 mt-[12px]">
                {viewOnly ? (
                  <>
                    <ExternalLink className="w-4 h-4 text-[#71717A]" />
                  </>
                ) : (
                  <Popover>
                    <PopoverTrigger className="flex align-start">
                      <MoreHorizontal className="w-4 h-4 text-[#71717A]" />
                    </PopoverTrigger>
                    <PopoverContent className="w-[168px] p-1" align="end">
                      <div className="flex flex-col">
                        <div
                          className="flex items-center gap-2 font-medium leading-[20px] text-sm text-[#2E3035] cursor-pointer p-2"
                          onClick={() => {
                            setSelectedMaterial(material);
                            setOpenEditLearningMaterialModal(true);
                          }}
                          data-title={material?.title}
                          data-description={material?.description}
                          data-link={material?.link}
                          data-type={material?.type}
                          data-metadata={JSON.stringify(material?.metadata)}
                        >
                          <Pencil className="w-4 h-4 text-[#2E3035]" />
                          Edit
                        </div>
                        <div
                          className="flex items-center gap-2 font-medium leading-[20px] text-sm text-[#EF4444] cursor-pointer p-2"
                          data-id={material?.id}
                          onClick={() => {
                            setSelectedMaterial(material);
                            setOpenDeleteConfirmation(true);
                          }}
                        >
                          <Trash className="w-4 h-4 text-[#EF4444]" />
                          Delete
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
