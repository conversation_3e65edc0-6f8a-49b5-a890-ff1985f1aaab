import { CardContent } from '@/components/ui/card';
import MaterialList from './MaterialList';
import ScorecardConfigDto, {
  LearningMaterialDto,
  ScorecardConfigMaterialScope,
} from '@/lib/ScorecardConfig/types';
import { PlusIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';
import AddMaterialModal from './AddMaterialModal';
import useScorecardConfigMaterials from '@/hooks/useScorecardConfigMaterials';

interface ILearningMaterialsProps {
  selectedScorecardId: number | undefined;
  hasAccess: boolean;
  selectedScorecardInfo: ScorecardConfigDto | undefined;
}

export default function LearningMaterials({
  selectedScorecardId,
  hasAccess,
  selectedScorecardInfo,
}: ILearningMaterialsProps) {
  const [openAddLearningMaterialModal, setOpenAddLearningMaterialModal] =
    useState(false);
  const [materialList, setMaterialList] = useState<LearningMaterialDto[]>([]);
  const {
    data: learningMaterialsDb,
    isLoading,
    refetch: refetchLearningMaterials,
    isRefetching: isRefetchingLearningMaterials,
  } = useScorecardConfigMaterials(selectedScorecardId as number);

  useEffect(() => {
    if (!isLoading && learningMaterialsDb && !isRefetchingLearningMaterials) {
      setMaterialList(
        learningMaterialsDb.filter(
          (l) => l.scope === ScorecardConfigMaterialScope.SCORECARD,
        ),
      );
    }
  }, [isLoading, learningMaterialsDb, isRefetchingLearningMaterials]);
  return (
    <>
      <AddMaterialModal
        open={openAddLearningMaterialModal}
        onClose={() => {
          setOpenAddLearningMaterialModal(false);
        }}
        scorecardConfigId={selectedScorecardId}
        scope={ScorecardConfigMaterialScope.SCORECARD}
        onAddDone={async () => {
          await refetchLearningMaterials();
        }}
      />
      <CardContent className="absolute top-0 left-0 right-0 bottom-0 px-3 py-4 flex flex-col">
        <div className="flex items-center">
          <div className="flex-1">
            Learning Materials (scorecard level) for
            <span className="font-medium ml-1">
              {selectedScorecardInfo?.tag}
            </span>
          </div>
          {hasAccess && (
            <div>
              <Button
                variant={'outline'}
                onClick={() => {
                  setOpenAddLearningMaterialModal(true);
                }}
              >
                <PlusIcon size={16} className="mr-2" />
                Add
              </Button>
            </div>
          )}
        </div>
        <MaterialList
          scorecardConfigId={selectedScorecardId}
          materialList={materialList}
          onEditDone={async () => {
            await refetchLearningMaterials();
          }}
          onDeleteDone={async () => {
            await refetchLearningMaterials();
          }}
          scope={ScorecardConfigMaterialScope.SCORECARD}
        />
      </CardContent>
    </>
  );
}
