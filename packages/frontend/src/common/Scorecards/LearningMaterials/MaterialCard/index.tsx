import { LearningMaterialDto } from '@/lib/ScorecardConfig/types';
import { getIcon } from '../MaterialList';
interface IMaterialCard {
  material: LearningMaterialDto;
}

export default function MaterialCard({ material }: IMaterialCard) {
  return (
    <div
      className="flex flex-col border border-[#E4E4E7] shadow-[0px_4px_5px_0px_#09090B05] rounded-lg cursor-pointer"
      onClick={() => {
        window?.open(material.link, '_blank');
      }}
    >
      <div className="bg-[#F4F4F5] h-[106px] flex items-center justify-center">
        {getIcon(material)}
      </div>
      <div className="flex flex-col p-3 h-[104px] justify-between">
        <div className="text-[#09090B] font-medium leading-[20px] text-sm line-clamp-2 h-[40px]">
          {material.title}
        </div>
        {material.type === 'LINK' ? (
          <div className="flex text-[#71717A] font-normal leading-[20px] text-sm truncate">
            {material.link}
          </div>
        ) : (
          <></>
        )}
      </div>
    </div>
  );
}
