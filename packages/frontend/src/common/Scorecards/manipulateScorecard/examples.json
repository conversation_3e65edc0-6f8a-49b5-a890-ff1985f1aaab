[{"id": 8, "by": "Hyperbound Team", "callTypes": [{"callType": "cold", "isDefaultForCallType": false}], "config": {"Opener": [{"type": "egpt", "query": "Did the sales rep use a permission based opener? This means that they asked the prospect for a short amount of time to explain why they are calling.", "corpus": "both", "criterion": "Permission based opener?", "explanation": "open ended questions"}, {"type": "egpt", "group": null, "query": "Did the sales rep indicate that they had conducted research on the prospect in any way prior to the call? Did they demonstrate that they knew something about that specific individual and understood their role? For example, this can be some personal fact about the prospect, information about their prior work history,  or some specific detail about their current job. ", "corpus": "both", "criterion": "Used research on prospect?", "explanation": "sales rep criterion"}], "Closing": [{"type": "gpt", "query": "Did the sales rep and prospect agree on next steps?", "corpus": "both", "criterion": "Next steps agreed upon?", "explanation": "sales rep criterion"}, {"type": "gpt", "query": "Did the sales rep and prospect agree on a follow-up meeting or a demo call?", "corpus": "both", "criterion": "Follow-up meeting booked?", "explanation": "sales rep criterion"}], "Takeaway": [{"type": "egpt", "group": null, "query": "If the sales rep and the prospect agreed on a follow up meeting, did the sales rep re-confirm that the time and date agreed upon works for the prospect?", "corpus": "both", "criterion": "Re-confirmed that the time works for the prospect?", "explanation": "sales rep criterion"}, {"type": "egpt", "group": null, "query": "Did the rep ask what would make the next call successful for the prospect?", "corpus": "both", "criterion": "Asked for success criteria for next call?", "explanation": "sales rep criterion"}], "Discovery": [{"type": "egpt", "query": "Did the sales rep ask if the prospect was familiar with the product they were trying to sell?", "corpus": "both", "criterion": "SDR asked for preconceptions of product?", "explanation": "open ended questions"}], "Social Proof": [{"type": "egpt", "group": null, "query": "Did the sales rep provide some type of social proof for the prospect on this sales call? Social proof is evidence that others have found value in the product the rep is selling.", "corpus": "both", "criterion": "Provided social proof?", "explanation": "sales rep criterion"}, {"type": "egpt", "group": null, "query": "If the sales rep provided some social proof for the prospect, did the sales rep ask the prospect if that social proof was relevant to the prospect/asked if the prospect resonated with the social proof? Typical ways of asking the prospect if the social proof is relevant would be to say \"Is that something you are experiencing as well?\", \"Does that sound familiar\", or \"Is that relevant\" regarding the social proof. ", "corpus": "both", "criterion": "Asked if social proof was relevant?", "explanation": "sales rep criterion"}]}, "tag": "Default Cold"}, {"id": 228, "by": "Hyperbound Team", "callTypes": [{"callType": "discovery", "isDefaultForCallType": false}], "config": {"Closing": [{"type": "egpt", "query": "Did the seller revisit the upfront contract and clearly define the next steps based on the discussion? Example: 'Based on our conversation, it seems like there is a good fit here. Shall we schedule a follow-up meeting to discuss implementation details?'", "corpus": "both", "criterion": "Did the seller revisit the upfront contract and define next steps?", "explanation": "Closing effectiveness"}, {"type": "egpt", "query": "Did the seller qualify out if no pains were identified or qualify in if there are clear signs of interest and potential? Example: 'Given that we haven't identified any significant pain points today, it might not make sense to proceed further. What do you think?'", "corpus": "both", "criterion": "Did the seller qualify out or in effectively?", "explanation": "Qualification effectiveness"}], "Objection Handling": [{"type": "egpt", "query": "Did the seller listen and acknowledge any concerns or objections, and effectively defuse them while steering the conversation towards relevant business topics? Did the seller use the FFF framework (Feel, Felt, Found) to handle objections? Example: 'I completely understand how you feel. I was speaking with [Company], who felt the same way. What we have found is that [Solution].'", "corpus": "both", "criterion": "Did the seller handle objections effectively using the FFF framework?", "explanation": "Objection handling using the FFF framework"}], "Introduction & Agenda": [{"type": "egpt", "query": "Did the seller discuss the agenda for the meeting and ask the prospect for input regarding their expectations? Example: 'I’d like to start by understanding your current challenges and goals. Is there anything specific you’d like to ensure we cover today?'", "corpus": "both", "criterion": "Did the seller discuss the agenda and ask for prospect's input?", "explanation": "Setting expectations"}, {"type": "egpt", "query": "Did the seller open the call clearly introducing an Upfront Contract? Example: 'If we figure out there is not a good fit/match by the end of this call, then we part ways. But if we agree there is a good fit, we will book in next steps. Typically that involves a second meeting where we go deeper into aspects identified in this meeting.'", "corpus": "both", "criterion": "Did the seller introduce an Upfront Contract?", "explanation": "Establishing mutual agreement"}], "Pain & Metrics Discovery": [{"type": "egpt", "query": "Did the seller uncover the specific issues and obstacles that hinder the prospect's efficiency and effectiveness in their sales process and understand their broader impact on the business? Examples: 'What challenges are you facing in your sales process?', 'How do these challenges affect your overall business operations?'", "corpus": "both", "criterion": "Did the seller uncover specific pain points?", "explanation": "Pain discovery criteria"}, {"type": "egpt", "query": "Did the seller uncover relevant metrics to inform a business case? Examples: 'Can you share your current win rate?', 'What is the average length of your sales cycle?', 'How many sales reps do you have on your team?'", "corpus": "both", "criterion": "Did the seller uncover relevant metrics?", "explanation": "Metric discovery"}], "Customer Reference & Value Pyramid Discovery": [{"type": "egpt", "query": "Did the seller present a customer reference, outlining the before and after using the solution? Example: 'Our customer, [XYZ Company], faced similar challenges and saw [specific results] after implementing our solution.'", "corpus": "both", "criterion": "Did the seller present a customer reference?", "explanation": "Leveraging customer success"}, {"type": "egpt", "query": "Did the seller try to understand what goal-setting framework the prospect is using and uncover top-level goals, strategies, and initiatives? Examples: 'Are you using OKRs, SMART Goals, Balanced Score Cards, or any other framework?', 'Can you share some of your top-level goals and how you measure success?'", "corpus": "both", "criterion": "Did the seller explore the prospect's goal-setting framework?", "explanation": "Understanding strategic goals"}]}, "tag": "De<PERSON>ult Discovery"}, {"id": 231, "by": "Hyperbound Team", "callTypes": [{"callType": "warm", "isDefaultForCallType": false}], "config": {"Opener": [{"type": "egpt", "query": "Did the sales rep use a warm opener referencing previous interactions, mutual connections, or prior interest shown by the prospect? Example: 'Hi [Prospect], we spoke briefly at the [Event] or 'I noticed we have a mutual connection, [Name].'", "corpus": "both", "criterion": "Warm opener used?", "explanation": "open ended questions"}, {"type": "egpt", "query": "Did the sales rep indicate that they had conducted research on the prospect prior to the call, demonstrating knowledge of their role, company, or previous interactions? Example: 'I saw that your company recently launched a new product.'", "corpus": "both", "criterion": "Used research on prospect?", "explanation": "sales rep criterion"}], "Closing": [{"type": "egpt", "query": "Did the sales rep and prospect agree on clear next steps? Example: 'Let's schedule a follow-up meeting next week to discuss further.'", "corpus": "both", "criterion": "Next steps agreed upon?", "explanation": "setting next steps"}, {"type": "egpt", "query": "Did the sales rep confirm the time and date of the next meeting and ensure it works for the prospect? Example: 'Does this time work for you?'", "corpus": "both", "criterion": "Re-confirmed that the time works for the prospect?", "explanation": "confirmation of next steps"}, {"type": "egpt", "query": "Did the sales rep ask what would make the next call successful for the prospect? Example: 'What would you like to cover in our next meeting to make it valuable for you?'", "corpus": "both", "criterion": "Asked for success criteria for next call?", "explanation": "customizing next meeting"}], "Discovery": [{"type": "egpt", "query": "Did the sales rep ask about the prospect's familiarity with the product or service and gauge their current level of interest? Example: 'How familiar are you with [Product/Service]?' and 'Do you have any current needs or challenges that we might be able to help with?'", "corpus": "both", "criterion": "Asked about product familiarity and current needs?", "explanation": "discovery questions"}], "Social Proof": [{"type": "egpt", "query": "Did the sales rep provide social proof, such as case studies or testimonials from similar companies or industry leaders? Example: 'We've helped companies like [Similar Company] achieve [Result].'", "corpus": "both", "criterion": "Provided social proof?", "explanation": "social proof criterion"}, {"type": "egpt", "query": "Did the sales rep ask the prospect if the social proof was relevant to their situation? Example: 'Does this sound like something you are experiencing as well?'", "corpus": "both", "criterion": "Asked if social proof was relevant?", "explanation": "social proof relevance"}], "Building Rapport": [{"type": "egpt", "query": "Did the sales rep effectively build rapport by referencing mutual interests, industry knowledge, or previous conversations? Example: 'I recall you mentioned in our last conversation that you are focusing on [Current Initiative]. How's that going?'", "corpus": "both", "criterion": "Built rapport?", "explanation": "rapport building techniques"}], "Objection Handling": [{"type": "egpt", "query": "Did the sales rep effectively handle objections, acknowledging concerns, and providing relevant responses? Example: 'I understand how you feel. I've spoken to others who felt the same but found that [Solution] addressed their concerns.'", "corpus": "both", "criterion": "Handled objections effectively?", "explanation": "objection handling using the FFF framework"}], "Solution Presentation": [{"type": "egpt", "query": "Did the sales rep succinctly present the solution, highlighting how it addresses the prospect's specific needs or challenges? Example: 'Based on what you've shared, our solution can help you with [Specific Challenge].'", "corpus": "both", "criterion": "Presented relevant solution?", "explanation": "solution presentation"}]}, "tag": "<PERSON><PERSON><PERSON>"}, {"id": 232, "by": "Hyperbound Team", "callTypes": [{"callType": "checkin", "isDefaultForCallType": false}], "config": {"Opening": [{"type": "egpt", "query": "Did the CSM start the call by setting a positive tone and confirming the agenda? Example: 'Hello [Customer], how are you today? I’d like to discuss your experience with our product and see how we can support you better. Does that sound good?'", "corpus": "both", "criterion": "Positive tone and agenda setting?", "explanation": "initial engagement"}, {"type": "egpt", "query": "Did the CSM reference previous interactions or recent activities that the customer might have engaged with? Example: 'I saw that you recently completed [Activity]. How did that go for you?'", "corpus": "both", "criterion": "Referenced previous interactions?", "explanation": "building continuity"}], "Goal Alignment": [{"type": "egpt", "query": "Did the CSM review the customer’s goals and ensure the product is aligned with those goals? Example: 'Last time we spoke, you mentioned [Goal]. How are things progressing towards that?'", "corpus": "both", "criterion": "Reviewed and aligned with customer goals?", "explanation": "goal alignment"}, {"type": "egpt", "query": "Did the CSM identify any new goals or priorities the customer has developed? Example: 'Have any new goals or priorities emerged since our last conversation?'", "corpus": "both", "criterion": "Identified new goals or priorities?", "explanation": "updating customer objectives"}], "Customer Feedback": [{"type": "egpt", "query": "Did the CSM ask for feedback on the product and overall service? Example: 'Do you have any feedback on how we can improve our product or our support?'", "corpus": "both", "criterion": "Solicited customer feedback?", "explanation": "collecting customer insights"}, {"type": "egpt", "query": "Did the CSM acknowledge and record any feedback or suggestions for improvement from the customer? Example: 'Thank you for that suggestion. I’ll ensure it gets passed along to our product team.'", "corpus": "both", "criterion": "Acknowledged and recorded feedback?", "explanation": "feedback acknowledgment"}], "Usage and Satisfaction": [{"type": "egpt", "query": "Did the CSM ask about the customer’s current usage of the product and their satisfaction? Example: 'How have you been finding our product/service? Are there any features you use the most?'", "corpus": "both", "criterion": "Asked about current usage and satisfaction?", "explanation": "gauging customer experience"}, {"type": "egpt", "query": "Did the CSM inquire about any challenges or issues the customer might be facing? Example: 'Are there any pain points or challenges you are encountering with our product?'", "corpus": "both", "criterion": "Inquired about challenges or issues?", "explanation": "identifying customer pain points"}], "Next Steps and Follow-Up": [{"type": "egpt", "query": "Did the CSM and customer agree on clear next steps? Example: 'Based on our conversation, I will send you the information on [Topic]. Shall we follow up in two weeks to see how things are going?'", "corpus": "both", "criterion": "Agreed on next steps?", "explanation": "next steps planning"}, {"type": "egpt", "query": "Did the CSM confirm the time and date of the next follow-up and ensure it works for the customer? Example: 'Does this time work for you?'", "corpus": "both", "criterion": "Re-confirmed next follow-up time?", "explanation": "follow-up confirmation"}, {"type": "egpt", "query": "Did the CSM ask what would make the next call successful for the customer? Example: 'What would you like to cover in our next meeting to make it valuable for you?'", "corpus": "both", "criterion": "Asked for success criteria for next call?", "explanation": "customizing next meeting"}], "Feature Utilization and Education": [{"type": "egpt", "query": "Did the CSM discuss underutilized features that might benefit the customer based on their needs? Example: 'I noticed you haven’t used our [Feature]. It might help you with [Customer Need]. Would you like more information on that?'", "corpus": "both", "criterion": "Discussed underutilized features?", "explanation": "increasing feature adoption"}, {"type": "egpt", "query": "Did the CSM provide any educational resources or best practices to help the customer get more value from the product? Example: 'We have a webinar on [Topic] that addresses some of the challenges you mentioned. Would you like me to send you the details?'", "corpus": "both", "criterion": "Provided educational resources?", "explanation": "customer education"}]}, "tag": "De<PERSON>ult Check-in"}, {"id": 233, "by": "Hyperbound Team", "callTypes": [{"callType": "renewal", "isDefaultForCallType": false}], "config": {"Opening": [{"type": "egpt", "query": "Did the CSM confirm the agenda of the call and set expectations? Example: 'I’d like to review your current use of our product, discuss any concerns, and talk about your plans for the next year. Does that sound good?'", "corpus": "both", "criterion": "Agenda setting and expectation confirmation?", "explanation": "setting expectations"}], "Customer Feedback": [{"type": "egpt", "query": "Did the CSM ask for feedback on the product and services provided? Example: 'Do you have any feedback on how we can improve our product or our support?'", "corpus": "both", "criterion": "Solicited customer feedback?", "explanation": "collecting customer insights"}, {"type": "egpt", "query": "Did the CSM acknowledge and record any feedback or suggestions for improvement? Example: 'Thank you for that suggestion. I’ll ensure it gets passed along to our product team.'", "corpus": "both", "criterion": "Acknowledged and recorded feedback?", "explanation": "feedback acknowledgment"}], "Renewal Discussions": [{"type": "egpt", "query": "Did the CSM explicitly discuss the renewal process, including timelines and terms? Example: 'Your renewal is coming up in [Month]. Let’s review the terms and any changes you might need.'", "corpus": "both", "criterion": "Discussed renewal process and terms?", "explanation": "renewal process clarification"}, {"type": "egpt", "query": "Did the CSM address any concerns or objections related to the renewal? Example: 'I understand you have some concerns about [Issue]. Let’s see how we can address those.'", "corpus": "both", "criterion": "Addressed renewal concerns or objections?", "explanation": "objection handling"}], "Value Demonstration": [{"type": "egpt", "query": "Did the CSM highlight key successes and ROI achieved by the customer using the product? Example: 'I noticed that since you started using [Feature], your [Metric] has improved by [Percentage].'", "corpus": "both", "criterion": "Highlighted key successes and ROI?", "explanation": "demonstrating value"}, {"type": "egpt", "query": "Did the CSM discuss any underutilized features that might benefit the customer? Example: 'I see you haven’t used [Feature] yet. It could help with [Customer Need]. Would you like more information on that?'", "corpus": "both", "criterion": "Discussed underutilized features?", "explanation": "increasing feature adoption"}], "Next Steps and Follow-Up": [{"type": "egpt", "query": "Did the CSM and customer agree on clear next steps for the renewal process? Example: 'Based on our discussion, I will send you the renewal agreement for review. Does that work for you?'", "corpus": "both", "criterion": "Agreed on next steps for renewal?", "explanation": "next steps planning"}, {"type": "egpt", "query": "Did the CSM confirm the time and date of the next follow-up meeting and ensure it works for the customer? Example: 'Does this time work for you?'", "corpus": "both", "criterion": "Re-confirmed next follow-up time?", "explanation": "follow-up confirmation"}, {"type": "egpt", "query": "Did the CSM ask if there is anything else they can assist with or any additional resources the customer might need? Example: 'Is there anything else I can assist you with or resources you might need?'", "corpus": "both", "criterion": "Inquired about additional assistance or resources?", "explanation": "offering support"}], "Current Usage and Satisfaction": [{"type": "egpt", "query": "Did the CSM review the customer’s current usage of the product and overall satisfaction? Example: 'How have you been finding our product/service? Are there any features you use the most?'", "corpus": "both", "criterion": "Reviewed current usage and satisfaction?", "explanation": "gauging customer experience"}, {"type": "egpt", "query": "Did the CSM inquire about any challenges or issues the customer might be facing with the product? Example: 'Are there any pain points or challenges you are encountering with our product?'", "corpus": "both", "criterion": "Inquired about challenges or issues?", "explanation": "identifying customer pain points"}], "Goal Alignment and Future Plans": [{"type": "egpt", "query": "Did the CSM review the customer’s goals and align the product’s capabilities to those goals? Example: 'Last time we spoke, you mentioned [Goal]. How are things progressing towards that?'", "corpus": "both", "criterion": "Reviewed and aligned with customer goals?", "explanation": "goal alignment"}, {"type": "egpt", "query": "Did the CSM identify any new goals or priorities the customer has developed for the upcoming term? Example: 'Have any new goals or priorities emerged for your team in the next year?'", "corpus": "both", "criterion": "Identified new goals or priorities?", "explanation": "updating customer objectives"}]}, "tag": "<PERSON><PERSON><PERSON>"}, {"id": 234, "by": "Hyperbound Team", "callTypes": [{"callType": "gatekeeper", "isDefaultForCallType": false}], "config": {"Opener": [{"type": "egpt", "query": "Did the caller introduce themselves and their company succinctly? Example: 'Hi, I'm [Your Name] from [Your Company].'", "corpus": "both", "criterion": "Introduced themselves and company?", "explanation": "initial introduction"}, {"type": "egpt", "query": "Did the caller clearly state the purpose of the call? Example: 'I’m calling to discuss how we can help [Company] with [Specific Benefit].'", "corpus": "both", "criterion": "Stated the purpose of the call?", "explanation": "clarity of purpose"}], "Transfer Request": [{"type": "egpt", "query": "Did the caller make a clear and direct request for transfer to the decision maker? Example: 'Could you please put me through to [Decision Maker’s Name]?'", "corpus": "both", "criterion": "Made a clear and direct transfer request?", "explanation": "transfer request"}, {"type": "egpt", "query": "Did the caller provide a compelling reason for why the decision maker should take the call? Example: 'It’s regarding a solution that can significantly boost your team’s efficiency.'", "corpus": "both", "criterion": "Provided a compelling reason for transfer?", "explanation": "reason for transfer"}], "Contact Information": [{"type": "egpt", "query": "If the gatekeeper was unable to transfer the call, did the caller ask for the decision maker’s direct contact information? Example: 'Can you please provide me with [Decision Maker’s Name]'s email or direct line?'", "corpus": "both", "criterion": "Asked for decision maker’s contact information?", "explanation": "contact information request"}], "Gatekeeper Interaction": [{"type": "egpt", "query": "Did the caller ask for the decision maker by name or title? Example: 'Could I please speak with [Decision Maker’s Name]?' or 'Can you connect me with the person in charge of [Specific Role]?'", "corpus": "both", "criterion": "Asked for decision maker by name or title?", "explanation": "direct request"}, {"type": "egpt", "query": "Did the caller use a polite and respectful tone when interacting with the gatekeeper? Example: 'Thank you for your help, I really appreciate it.'", "corpus": "both", "criterion": "Used polite and respectful tone?", "explanation": "tone and respect"}, {"type": "egpt", "query": "Did the caller handle any pushback or questions from the gatekeeper effectively? Example: 'I understand they’re busy. This will just take a minute, and it’s quite important.'", "corpus": "both", "criterion": "Handled pushback effectively?", "explanation": "objection handling"}]}, "tag": "De<PERSON>ult Gatekeeper"}]