import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import useDragAndDrop from '@/hooks/ui/useDragAndDrop';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { checkObjHasSomeValues, cn, sha256 } from '@/lib/utils';
import { DialogDescription } from '@radix-ui/react-dialog';
import { Eye, Loader2Icon, SaveIcon, Send } from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import ScorecardDraggableView from '../scorecardPreview/draggableView';
import ScorecardPreviewEdit from '../scorecardPreview/edit';
import ScorecardPreviewRepView from '../scorecardPreview/repView';
import { CallTypesInput } from './CallTypesInput';
import examples from './examples.json';
import { useDebounce } from '@/hooks/ui/useDebounce';
import Image from 'next/image';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export type Criteria = {
  tempUuid?: string;
  type: string;
  criterion: string;
  query: string | string[] | Criteria[];
  corpus?: 'rep' | 'prospect' | 'both';
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
};

export type CardTyp = {
  tempUuid: string;
  sectionTitle: string;
  criteria: Criteria[];
};

export type DraggedObjType = (Criteria | CardTyp) & {
  isExample?: boolean;
};

export type TargetObjType = DraggedObjType & {
  isAfter: boolean;
  isNewSection?: boolean;
};

export const checkIfCard = (obj?: Criteria | CardTyp): obj is CardTyp => {
  return Object.keys(obj || {}).includes('criteria');
};

export default function ManipulateScorecard({
  title,
  breadcrumbsOverride,
  initialScorecardDto,
  onCancel,
  checkCanSave,
  onSave,
  onPublish,
  isPublishAvailable,
  isLoading,
  isPublishing,
  showScorecardExamples = false,
  showSaveAsDraft = true,
}: {
  title?: string;
  breadcrumbsOverride?: BreadcrumbItem[];
  initialScorecardDto?: Partial<ScorecardConfigDto>;
  onCancel: () => unknown;
  checkCanSave: (newDto: Partial<ScorecardConfigDto>) => {
    canSave: boolean;
    reason?: string;
  };
  onSave: (newDto: ScorecardConfigDto, isAutoSave?: boolean) => unknown;
  onPublish: (newDto: ScorecardConfigDto) => unknown;
  isPublishAvailable: boolean;
  isLoading: boolean;
  isPublishing: boolean;
  showScorecardExamples?: boolean;
  showSaveAsDraft?: boolean;
}) {
  const [isAutoSaving, setIsAutosaving] = useState(false);
  const [modifiedScorecardDto, setModifiedScorecardDto] = useState(
    initialScorecardDto || {},
  );
  const modifiedScorecardDtoRef = useRef(modifiedScorecardDto);
  modifiedScorecardDtoRef.current = modifiedScorecardDto;

  useEffect(() => {
    setModifiedScorecardDto(initialScorecardDto || {});
  }, [initialScorecardDto]);

  const [namesHash, setNamesHash] = useState('');
  useEffect(() => {
    (async () => {
      setNamesHash(
        await ScorecardConfigService.getAllScorecardConfigsNameHash(),
      );
    })();
  }, []);

  const [currentNameAndHash, setCurrentNameAndHash] = useState({
    name: '',
    hash: '',
  });
  useEffect(() => {
    const timeout = setTimeout(() => {
      (async () => {
        if (!modifiedScorecardDto.tag) {
          setCurrentNameAndHash({
            name: '',
            hash: '',
          });
          return;
        }
        setCurrentNameAndHash({
          name: modifiedScorecardDto.tag,
          hash: await sha256(modifiedScorecardDto.tag, 'base64'),
        });
      })();
    }, 100);
    return () => clearTimeout(timeout);
  }, [modifiedScorecardDto.tag]);

  const modifyScorecardConfig = (dto: Partial<ScorecardConfigDto>) => {
    setModifiedScorecardDto(dto);
    if (checkIfCanSave(dto)?.canSave) {
      debouncedAutoSave.cancel();
      debouncedAutoSave();
    }
  };

  const isNameTaken = useMemo(() => {
    if (!currentNameAndHash.name || !currentNameAndHash.hash) {
      return false;
    }
    if (initialScorecardDto?.tag === currentNameAndHash.name) {
      return false;
    }
    return namesHash.includes(currentNameAndHash.hash);
  }, [initialScorecardDto?.tag, currentNameAndHash, namesHash]);

  const isChanged = useMemo(() => {
    if (
      !checkObjHasSomeValues(initialScorecardDto || {}) &&
      !checkObjHasSomeValues(modifiedScorecardDto)
    ) {
      return false;
    }
    return (
      JSON.stringify(modifiedScorecardDto) !==
      JSON.stringify(initialScorecardDto)
    );
  }, [modifiedScorecardDto, initialScorecardDto]);

  const checkIfCanSave = (dto?: Partial<ScorecardConfigDto>) => {
    if (!isChanged) {
      return {
        canSave: false,
        reason: "You don't have any pending changes to save!",
      };
    }
    if (isNameTaken) {
      return {
        canSave: false,
        reason: 'This scorecard name is already taken',
      };
    }
    return checkCanSave(dto || modifiedScorecardDto);
  };

  const { canSave, reason: canSaveReason } = useMemo(
    () => checkIfCanSave(),
    [isChanged, checkCanSave, modifiedScorecardDto, isNameTaken],
  );

  const getCanSaveTooltipNode = (component: React.ReactNode) => {
    if (canSave) {
      return component;
    }
    return (
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger>{component}</TooltipTrigger>
          <TooltipContent side="bottom">{canSaveReason}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const getDtoClone = () => {
    const dtoClone = JSON.parse(
      JSON.stringify(modifiedScorecardDto),
    ) as ScorecardConfigDto;
    ((Object.values(dtoClone.config) as Criteria[][]) || []).forEach(
      (criterias) => {
        criterias.forEach((c) => {
          delete c['tempUuid'];
        });
      },
    );
    return dtoClone;
  };

  const onSaveClick = (isAutoSave = false) => {
    const dtoClone = getDtoClone();
    return onSave(dtoClone, isAutoSave);
  };

  const onPublishClick = () => {
    const dtoClone = getDtoClone();
    onPublish(dtoClone);
  };

  const debouncedAutoSave = useDebounce(async () => {
    setIsAutosaving(true);
    await onSaveClick(true);
    setIsAutosaving(false);
  }, 2000);

  const { data: scorecardConfigsForOrg } = useScorecardConfigsForOrg(true);

  const filteredExamples = useMemo(() => {
    if (!modifiedScorecardDto.callTypes?.length) {
      return [
        ...examples,
        ...(scorecardConfigsForOrg || []),
      ] as Partial<ScorecardConfigDto>[];
    }
    const sc1 = examples.filter((e) => {
      return e.callTypes.some((callType) => {
        return (modifiedScorecardDto.callTypes || []).some(
          (cat2) => cat2.callType === callType.callType,
        );
      });
    });
    const sc2 = scorecardConfigsForOrg
      ? scorecardConfigsForOrg.filter((sc) => {
          return (sc.callTypes || []).some((callType) => {
            return (modifiedScorecardDto.callTypes || []).some(
              (cat2) => cat2.callType === callType.callType,
            );
          });
        })
      : [];
    return [...sc1, ...sc2] as Partial<ScorecardConfigDto>[];
  }, [examples, scorecardConfigsForOrg, modifiedScorecardDto]);
  const [selectedExampleId, setSelectedExampleId] = useState(
    filteredExamples[0]?.id || 0,
  );
  const selectedExample = useMemo(() => {
    return filteredExamples.find((e) => e.id === selectedExampleId);
  }, [filteredExamples, selectedExampleId]);

  useEffect(() => {
    if (!selectedExample) {
      setSelectedExampleId(filteredExamples[0]?.id || 0);
    }
  }, [selectedExample, filteredExamples]);

  const [isDNDActive, setIsDNDActive] = useState<boolean>(false);

  const getCardsFromSc = (
    sc: Partial<ScorecardConfigDto>,
    useSectionsOrder: boolean,
  ) => {
    const newCards: CardTyp[] = [];
    if (sc?.config) {
      const scorecardKeysSorted = useSectionsOrder
        ? ScorecardConfigService.getSortedKeys(sc)
        : Object.keys(sc.config);
      scorecardKeysSorted.forEach((sectionTitle: string, idx: number) => {
        const obj = sc.config[sectionTitle];
        const criteria = obj.map((o: Criteria) => ({
          tempUuid: crypto.randomUUID(),
          ...o,
        }));
        newCards.push({
          tempUuid: criteria.length
            ? criteria
                .map((c: Criteria & { tempUuid: string }) =>
                  (c.tempUuid as string).slice(0, 5),
                )
                .join(',')
            : idx.toString(),
          sectionTitle,
          criteria,
        });
      });
    }
    return newCards;
  };

  const [cards, setCards] = useState(
    getCardsFromSc(modifiedScorecardDto, true),
  );
  const [exampleCards, setExampleCards] = useState(
    selectedExample ? getCardsFromSc(selectedExample, true) : [],
  );
  const userStartedEditingCards = useRef(false);
  useEffect(() => {
    setCards(
      getCardsFromSc(modifiedScorecardDto, !userStartedEditingCards.current),
    );
  }, [modifiedScorecardDto]);

  useEffect(() => {
    setExampleCards(
      selectedExample ? getCardsFromSc(selectedExample, true) : [],
    );
  }, [selectedExample]);

  useEffect(() => {
    // to debounce the input
    const timer = setTimeout(() => {
      const newConfig = new Map<string, Criteria[]>();
      cards.forEach((c) => {
        newConfig.set(c.sectionTitle, c.criteria);
      });
      const newConfigObj = Object.fromEntries(newConfig);
      const currentScorecard = { ...modifiedScorecardDtoRef.current };
      if (
        JSON.stringify(newConfigObj || {}) !==
        JSON.stringify(currentScorecard.config || {})
      ) {
        currentScorecard.config = newConfigObj;
        modifyScorecardConfig(currentScorecard);
      }
    }, 50);
    return () => clearTimeout(timer);
  }, [cards]);

  const [currentDraggedObject, setCurrentDraggedObject] =
    useState<DraggedObjType>();

  const resetUuids = <T extends DraggedObjType>(movingObj: T) => {
    movingObj = JSON.parse(JSON.stringify(movingObj));
    if (checkIfCard(movingObj)) {
      movingObj.criteria.forEach((c) => {
        c.tempUuid = crypto.randomUUID();
      });
      movingObj.tempUuid = movingObj.criteria
        .map((c) => (c.tempUuid as string).slice(0, 5))
        .join(',');
    } else {
      movingObj.tempUuid = crypto.randomUUID();
    }
    return movingObj;
  };

  const [droppedCount, setDroppedCount] = useState<Record<string, number>>({});

  const onDrop = (
    movingObj: DraggedObjType,
    targetObj: TargetObjType | undefined,
  ) => {
    if (targetObj) {
      const isMovingObjCard = checkIfCard(movingObj);
      const isTargetObjCard = checkIfCard(targetObj);
      if (isMovingObjCard === isTargetObjCard) {
        // same obj
        if (movingObj.tempUuid === targetObj.tempUuid) {
          return;
        }

        if (isMovingObjCard) {
          let cardObj: CardTyp | undefined;
          if (movingObj.isExample) {
            cardObj = {
              ...exampleCards.find((c) => c.tempUuid === movingObj.tempUuid)!,
            };

            const existingCard = cards.find(
              (card) => card.sectionTitle === cardObj?.sectionTitle,
            );
            if (existingCard) {
              cardObj.sectionTitle = `${(droppedCount[cardObj.tempUuid] || 0) + 1} ${cardObj.sectionTitle}`;
              setDroppedCount({
                ...droppedCount,
                [cardObj.tempUuid]: (droppedCount[cardObj.tempUuid] || 0) + 1,
              });
            }
          } else {
            const idxToRemove = cards.findIndex(
              (c) => c.tempUuid === movingObj.tempUuid,
            );
            cardObj = cards[idxToRemove];
            cards.splice(idxToRemove, 1);
          }
          if (cardObj) {
            if (targetObj.isNewSection) {
              cards.push(resetUuids(cardObj));
            } else {
              const idxOfTarget = cards.findIndex(
                (c) => c.tempUuid === targetObj.tempUuid,
              );
              cards.splice(
                idxOfTarget + (targetObj.isAfter ? 1 : 0),
                0,
                resetUuids(cardObj),
              );
            }
          }
        } else {
          let criteriaObj: Criteria | null = null;
          for (const card of movingObj.isExample ? exampleCards : cards) {
            const idxToRemove = card.criteria.findIndex(
              (c) => c.tempUuid === movingObj.tempUuid,
            );
            if (idxToRemove >= 0) {
              criteriaObj = card.criteria[idxToRemove];
              if (!movingObj.isExample) {
                card.criteria.splice(idxToRemove, 1);
              }
              break;
            }
          }
          if (criteriaObj) {
            for (const card of cards) {
              const idxOfTarget = card.criteria.findIndex(
                (c) => c.tempUuid === targetObj.tempUuid,
              );
              if (idxOfTarget >= 0) {
                card.criteria.splice(
                  idxOfTarget + (targetObj.isAfter ? 1 : 0),
                  0,
                  resetUuids(criteriaObj),
                );
                break;
              }
            }
          }
        }
        userStartedEditingCards.current = true;
        setCards([...cards]);
      } else if (!isMovingObjCard && isTargetObjCard) {
        // criteria moving into a section with no criterias yet
        const targetCard = cards.find((c) => c.tempUuid === targetObj.tempUuid);
        if (targetCard && !targetCard.criteria?.length) {
          let criteriaObj: Criteria | null = null;
          for (const card of movingObj.isExample ? exampleCards : cards) {
            const idxToRemove = card.criteria.findIndex(
              (c) => c.tempUuid === movingObj.tempUuid,
            );
            if (idxToRemove >= 0) {
              criteriaObj = card.criteria[idxToRemove];
              if (!movingObj.isExample) {
                card.criteria.splice(idxToRemove, 1);
              }
              break;
            }
          }
          if (criteriaObj) {
            targetCard.criteria = [resetUuids(criteriaObj)];
          }
          userStartedEditingCards.current = true;
          setCards([...cards]);
        }
      }
    }
  };

  const { dragPanelRef, onMouseDown, onMouseEnter, onMouseLeave } =
    useDragAndDrop<DraggedObjType, TargetObjType>(setIsDNDActive, onDrop);

  const isDraggedObjectCard = checkIfCard(currentDraggedObject);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

  return (
    <div
      className={cn('bg-[#FBFBFB] h-full flex flex-col', {
        '!cursor-grab select-none': isDNDActive,
        'pointer-events-none': isLoading,
      })}
    >
      <DashboardNavbar
        className="bg-[#FBFBFB]"
        breadcrumbs={
          breadcrumbsOverride || [
            { title: 'Scorecards', href: '/scorecards' },
            { title: title || '' },
          ]
        }
        rightContent={
          <div className="flex items-center mr-4">
            <div className="flex-1"></div>
            {isAutoSaving && (
              <div className="flex items-center mr-2 text-muted-foreground text-xs">
                Saving...{' '}
                <Loader2Icon className="animate-spin ml-2" size={16} />
              </div>
            )}
            <Button
              disabled={isLoading || isAutoSaving}
              variant="outline"
              onClick={() => {
                if (isChanged) {
                  setIsCancelModalOpen(true);
                } else {
                  onCancel();
                }
              }}
            >
              Cancel
            </Button>
            <div className="h-[34px] mx-4 py-2">
              <div className="border-r h-full" />
            </div>
            {getCanSaveTooltipNode(
              <Button
                className="mr-2"
                disabled={!canSave || isLoading || isAutoSaving}
                variant="outline"
                onClick={() => {
                  setPreviewModalOpen(true);
                }}
              >
                <Eye className="w-4 h-4 mr-1" />
                Preview as Rep
              </Button>,
            )}
            <>
              {showSaveAsDraft &&
                getCanSaveTooltipNode(
                  <Button
                    variant={'outline'}
                    disabled={!canSave || isLoading || isAutoSaving}
                    onClick={() => onSaveClick()}
                    className="mr-2"
                  >
                    <SaveIcon size={16} className="mr-2" />
                    Save draft
                  </Button>,
                )}
              {getCanSaveTooltipNode(
                <Button
                  onClick={onPublishClick}
                  disabled={
                    !canSave ||
                    isLoading ||
                    !isPublishAvailable ||
                    isAutoSaving ||
                    isPublishing
                  }
                >
                  {isPublishing ? (
                    <>
                      <Image
                        src={`/images/90-ring-with-bg.svg`}
                        alt="Hyperbound logo"
                        width={16}
                        height={16}
                        className="mr-2"
                        priority
                      />
                      Publishing...
                    </>
                  ) : (
                    <>
                      <Send size={16} className="mr-2" />
                      Publish
                    </>
                  )}
                </Button>,
              )}
            </>
          </div>
        }
      />

      <div className="flex-1 relative">
        <div className="p-8 absolute left-0 right-0 top-0 bottom-0 flex flex-row space-x-4">
          <div className="w-[500px] flex flex-col h-full space-y-4 overflow-y-auto pr-2">
            {showScorecardExamples && (
              <Card className="h-full flex flex-col">
                <CardHeader className="bg-[#FBFBFB] rounded-t-xl border-b pt-3 pb-2 px-4">
                  <CardTitle className="text-base font-medium">
                    Scorecard examples
                  </CardTitle>
                  <CardDescription>
                    Drag to copy these examples to your builder on the right
                  </CardDescription>
                </CardHeader>
                <CardContent className="px-0 flex-1 overflow-y-auto relative">
                  <div className="absolute left-0 right-0 top-0 bottom-0 flex flex-col mt-3 px-4">
                    <p className="font-medium mb-2">Select</p>
                    <Select
                      value={selectedExampleId.toString()}
                      onValueChange={(newVal) => {
                        setSelectedExampleId(parseInt(newVal));
                      }}
                    >
                      <SelectTrigger>{selectedExample?.tag}</SelectTrigger>
                      <SelectContent>
                        {filteredExamples.map((e) => (
                          <SelectItem
                            key={e.id}
                            value={e?.id?.toString() || ''}
                          >
                            {e?.tag}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ScorecardDraggableView
                      cards={exampleCards}
                      currentDraggedObject={currentDraggedObject}
                      setCurrentDraggedObject={setCurrentDraggedObject}
                      onMouseDown={onMouseDown}
                      onMouseEnter={onMouseEnter}
                      onMouseLeave={onMouseLeave}
                      isDNDActive={isDNDActive}
                      isExample
                    />
                  </div>
                </CardContent>
              </Card>
            )}
            {/* <HelpCenter className="w-full" questions={[
              {
                title: "What are scorecards?",
                description: "A sales scorecard is a tool used to evaluate and track the performance of salespeople based on specific criteria.",
              },
              // {
              //   title: "How to add a section?",
              //   description: "Click on the Create New button to add a new section. Watch a short educational video below to see how to create the section.",
              //   youtubeEmbed: <iframe width="380" height="207" src="https://www.youtube.com/embed/hRok6zPZKMA?si=0oLxvxhx_n2_bPXc" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>,
              // },
            ]} /> */}
          </div>
          <div className="flex-1 overflow-y-auto">
            <Card className="mr-1">
              <CardContent className="px-3 py-4 flex flex-col">
                <div className="flex flex-col">
                  <div className="font-medium">Enter name</div>
                  <Input
                    disabled={isLoading}
                    placeholder="Name"
                    value={modifiedScorecardDto.tag || ''}
                    onChange={(e) => {
                      modifyScorecardConfig({
                        ...modifiedScorecardDto,
                        tag: e.currentTarget.value,
                      });
                    }}
                    className="max-w-sm mt-2"
                  />
                  {isNameTaken && (
                    <p className="text-red-500 mt-2">
                      This name is already taken. Please enter a unique name.
                    </p>
                  )}
                </div>
                <div className="flex flex-col mt-4 mb-6">
                  <div className="font-medium mb-2 mt-2">Call Types</div>
                  <div className="text-muted-foreground mb-2">
                    Marking a call type as default would make this scorecard the
                    default one for that call type. You can have multiple call
                    type defaults.
                  </div>
                  <CallTypesInput
                    disabled={isLoading}
                    callTypes={modifiedScorecardDto.callTypes || []}
                    onValueChange={(newTypes) => {
                      modifyScorecardConfig({
                        ...modifiedScorecardDto,
                        callTypes: newTypes,
                      });
                    }}
                  />
                </div>
                <ScorecardPreviewEdit
                  cards={cards}
                  setCards={(c) => {
                    userStartedEditingCards.current = true;
                    setCards(c);
                  }}
                  currentDraggedObject={currentDraggedObject}
                  setCurrentDraggedObject={setCurrentDraggedObject}
                  onMouseDown={onMouseDown}
                  onMouseEnter={onMouseEnter}
                  onMouseLeave={onMouseLeave}
                  isDNDActive={isDNDActive}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <div
        className="absolute p-2 border border-gray-700 bg-black/60 text-white rounded-lg"
        style={{ display: 'none' }}
        ref={dragPanelRef}
      >
        {currentDraggedObject
          ? isDraggedObjectCard
            ? `Section: ${currentDraggedObject.sectionTitle}`
            : `Criterion: ${currentDraggedObject.criterion}`
          : ''}
      </div>
      <Dialog
        open={previewModalOpen}
        onOpenChange={(o) => setPreviewModalOpen(o)}
      >
        <DialogContent className="close-btn w-[1000px] min-w-[75vw] bg-[#FBFBFB] p-0">
          <DialogHeader className="pt-6 px-4">
            <DialogTitle>Scorecard Preview</DialogTitle>
            <DialogDescription className="pt-2">
              Reps will see this scorecard after completing the call, providing
              a summary of performance and feedback.
            </DialogDescription>
          </DialogHeader>
          {canSave && !isLoading && true && (
            <div className=" px-4">
              <ScorecardPreviewRepView
                scorecard={modifiedScorecardDto as ScorecardConfigDto}
                cardClassName="mt-1"
              />
            </div>
          )}
          <DialogFooter className="border-t bg-white rounded-b-xl py-4 px-4">
            <DialogClose asChild>
              <Button
                variant={'outline'}
                onClick={() => {
                  setPreviewModalOpen(false);
                }}
              >
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <DeleteConfirmationModal
        title="Are you sure you want to go back?"
        description="You have unsaved changes that will be lost."
        confirmLabel="Go back"
        cancelLabel="Stay here"
        open={isCancelModalOpen}
        onCancel={() => {
          setIsCancelModalOpen(false);
        }}
        onConfirm={() => {
          setIsCancelModalOpen(false);
          onCancel();
        }}
      />
    </div>
  );
}
