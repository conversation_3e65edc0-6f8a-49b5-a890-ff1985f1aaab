import { Badge } from '@/components/ui/badge';
import { MultiSelect } from '@/components/ui/Hyperbound/multi-select';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';
import { AgentCallType } from '@/lib/Agent/types';
import { ScorecardConfigCallType } from '@/lib/ScorecardConfig/types';
import { X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

export const CallTypesInput = ({
  disabled = false,
  callTypes,
  onValueChange,
}: {
  disabled?: boolean;
  callTypes: ScorecardConfigCallType[];
  onValueChange: (types: ScorecardConfigCallType[]) => unknown;
}) => {
  const { callTypeOptions: callTypeOptionsFromHook } = useCallTypeOptions();
  const [selectedCallTypes, setSelectedCallTypes] = useState<string[]>(
    callTypes.map((c) => c.callType),
  );
  const [callTypeDefaultsSelection, setCallTypeDefaultsSelection] = useState<{
    [key: string]: boolean;
  }>(
    Object.fromEntries(
      callTypes.map((ct) => [ct.callType, ct.isDefaultForCallType]),
    ),
  );

  useEffect(() => {
    setSelectedCallTypes((t) => {
      const newSelectedCallTypes = callTypes.map((c) => c.callType);
      if (JSON.stringify(newSelectedCallTypes) !== JSON.stringify(t)) {
        return newSelectedCallTypes;
      }
      return t;
    });
    setCallTypeDefaultsSelection((selection) => {
      const newSelection = Object.fromEntries(
        callTypes.map((ct) => [ct.callType, ct.isDefaultForCallType]),
      );
      if (JSON.stringify(newSelection) !== JSON.stringify(selection)) {
        return newSelection;
      }
      return selection;
    });
  }, [callTypes]);

  useEffect(() => {
    onValueChange(
      selectedCallTypes.map((ct) => ({
        callType: ct as AgentCallType,
        isDefaultForCallType: callTypeDefaultsSelection[ct],
      })),
    );
  }, [selectedCallTypes, callTypeDefaultsSelection]);

  const markCallTypeDefault = (callType: string, isDefault: boolean) => {
    callTypeDefaultsSelection[callType] = isDefault;
    setCallTypeDefaultsSelection({
      ...callTypeDefaultsSelection,
    });
  };

  const callTypeOptions = useMemo(() => {
    return callTypeOptionsFromHook.map((opt) => {
      const isDefault = callTypeDefaultsSelection[opt.value];
      const isSelected = selectedCallTypes.includes(opt.value);
      let rowBadge: React.ReactNode, valueBadge: React.ReactNode;
      if (isSelected) {
        if (isDefault) {
          rowBadge = (
            <Badge
              className="ml-auto"
              onClick={(e) => {
                markCallTypeDefault(opt.value, false);
                e.stopPropagation();
              }}
            >
              Default <X className="w-3 h-3 ml-1" />
            </Badge>
          );
          valueBadge = (
            <Badge
              className="rounded-full ml-2 text-[10px] px-1 py-0"
              variant={'default'}
            >
              Default
            </Badge>
          );
        } else {
          rowBadge = (
            <Badge
              variant={'outline'}
              className="ml-auto"
              onClick={(e) => {
                markCallTypeDefault(opt.value, true);
                e.stopPropagation();
              }}
            >
              Mark Default
            </Badge>
          );
        }
      }
      return {
        ...opt,
        isDefault,
        rowBadge,
        valueBadge,
      };
    });
  }, [selectedCallTypes, callTypeDefaultsSelection, callTypeOptionsFromHook]);

  return (
    <MultiSelect
      disabled={disabled}
      maxCount={100}
      options={callTypeOptions}
      defaultValue={selectedCallTypes}
      placeholder="All"
      onValueChange={(selectedValues) => {
        setSelectedCallTypes(selectedValues);
      }}
      popoverContentClassName="min-w-[400px]"
    />
  );
};
