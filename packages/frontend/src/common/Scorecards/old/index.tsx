import { useState, useEffect } from 'react';
import DashboardNavbar from '@/common/DashboardNavbar';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import ScorecardPreview from '@/common/CreateBuyerForm/Main/ScorecardPreview';
import { Button } from '@/components/ui/button';
import { Loader2Icon, Lock, Pencil, PlusCircle, Share2 } from 'lucide-react';
import dayjs from 'dayjs';
import useOrg from '@/hooks/useOrg';
import ScorecardConfigDto, {
  ScorecardConfigStatus,
} from '@/lib/ScorecardConfig/types';
import UpsertScorecardModal from './upsertScorecardModal';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import useUserSession from '@/hooks/useUserSession';
import GenereatedNewScorecardModal from './generateNewScorecard';
import ShareWithOrganizationDialog from './shareWithOrganizationDialog';

export default function ScorecardsOld() {
  const {
    isAdmin,
    isHyperboundUser,
    dbOrg,
    canCreateSubOrgs,
    blurSecondaryPages,
    isCompetitionOrg,
  } = useUserSession();
  const [selectedScorecard, setSelectedScorecard] = useState<string>('');
  const [selectedScorecardDto, setSelectedScorecardDto] =
    useState<ScorecardConfigDto>();
  const { data: org } = useOrg();

  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);
  const [isLoadingScorecards, setIsLoadingScorecards] =
    useState<boolean>(false);

  const fetchData = async (setDefault = false) => {
    setIsLoadingScorecards(true);

    const scs = await ScorecardConfigService.getAllScorecardConfigsForOrg();
    setAllScorecards(scs);

    if (setDefault) {
      if (scs.length > 0) {
        if (selectedScorecard == '') {
          setSelectedScorecard(String(scs[0].id));
          setSelectedScorecardDto(scs[0]);
        }
      }
    }

    setIsLoadingScorecards(false);
  };

  useEffect(() => {
    if (!blurSecondaryPages && !isCompetitionOrg) {
      fetchData(true);
    }
  }, [blurSecondaryPages, isCompetitionOrg]);

  const [openNewModal, setOpenNewModal] = useState<boolean>(false);
  const [isOpenGenerateNewModal, setIsOpenGenerateNewModal] =
    useState<boolean>(false);

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  const openCreateNewModal = () => {
    setEdtingScorecard(undefined);
    setOpenNewModal(true);
  };

  const [editingScorecard, setEdtingScorecard] = useState<ScorecardConfigDto>();
  const editScorecard = (sc: ScorecardConfigDto) => {
    setEdtingScorecard(sc);
    setOpenNewModal(true);
  };

  const openGenerateNewModal = () => {
    setIsOpenGenerateNewModal(true);
  };

  const openEditModal = (config: any) => {
    setEdtingScorecard({
      stats: {},
      config,
      defaultType: 'Cold',
      orgId: dbOrg?.id || 0,
      tag: '',
      editable: true,
      status: ScorecardConfigStatus.DRAFT,
    });
    setOpenNewModal(true);
  };

  const [shareWithOrgPnl, setShareWithOrgPnl] = useState<boolean>(false);

  return (
    <div>
      <DashboardNavbar
        breadcrumbs={[{ title: 'Custom AI Scorecards' }]}
        rightContent={
          isAdmin && (
            <div className="flex items-center">
              <div className="flex-1"></div>
              {isHyperboundUser && (
                <Button
                  onClick={openGenerateNewModal}
                  disabled={isPilotEnded}
                  className="mr-2"
                  variant={'outline'}
                >
                  <PlusCircle className="w-4 h-4 mr-2" />
                  Go Atul go!
                </Button>
              )}
              <Button onClick={openCreateNewModal} disabled={isPilotEnded}>
                <PlusCircle className="w-4 h-4 mr-2" />
                Create new
              </Button>
            </div>
          )
        }
      />
      {(blurSecondaryPages || isCompetitionOrg) && (
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock the power of Custom AI Scorecards
            </div>
            <div className="text-base mt-2 text-muted-foreground max-w-[50%] text-center">
              Build custom AI scorecards tailored to your business and build
              them around any sales methodology you&apos;d like.
            </div>
          </div>
        </div>
      )}

      <div className="p-8">
        <div className="mb-8 flex items-center">
          <div className="mr-2 font-semibold">Scorecard:</div>
          <div className="w-[25vw]">
            <Select
              onValueChange={(value: string) => {
                setSelectedScorecard(value);
                setSelectedScorecardDto(
                  allScorecards.find((sc) => String(sc.id) == value),
                );
              }}
              value={selectedScorecard}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose a scorecard" />
              </SelectTrigger>
              <SelectContent>
                {allScorecards && (
                  <>
                    {allScorecards.map((option) => {
                      return (
                        <SelectItem key={option.id} value={String(option.id)}>
                          {option.tag}
                        </SelectItem>
                      );
                    })}
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="ml-1">
            {isLoadingScorecards && <Loader2Icon className="animate-spin" />}
          </div>
          <div className="flex-1"></div>
          {canCreateSubOrgs && selectedScorecard && (
            <Button
              variant={'outline'}
              onClick={() => {
                setShareWithOrgPnl(true);
              }}
              className="mr-2"
            >
              <Share2 size={16} className="mr-1" />
              Share with organization
            </Button>
          )}
          <div>
            {allScorecards && (
              <>
                {allScorecards.map((option) => {
                  if (String(option.id) == selectedScorecard) {
                    if (option.editable) {
                      return (
                        <Button
                          variant={'outline'}
                          key={'btn' + option.id}
                          onClick={() => {
                            editScorecard(option);
                          }}
                        >
                          <Pencil size={16} className="mr-1" />
                          Edit
                        </Button>
                      );
                    }
                  }
                })}
              </>
            )}
          </div>
        </div>

        <div>
          {allScorecards && (
            <>
              {allScorecards.map((option) => {
                if (String(option.id) == selectedScorecard) {
                  return (
                    <ScorecardPreview key={option.id} scorecard={option} />
                  );
                }
              })}
            </>
          )}
        </div>
      </div>
      <UpsertScorecardModal
        open={openNewModal}
        onClose={() => {
          setOpenNewModal(false);
          fetchData();
        }}
        scorecard={editingScorecard}
      />
      <GenereatedNewScorecardModal
        open={isOpenGenerateNewModal}
        onSave={(config: any) => {
          setIsOpenGenerateNewModal(false);
          openEditModal(config);
        }}
        onCancel={() => {
          setIsOpenGenerateNewModal(false);
        }}
      />
      {selectedScorecardDto && (
        <ShareWithOrganizationDialog
          open={shareWithOrgPnl}
          onClose={() => {
            setShareWithOrgPnl(false);
          }}
          scorecard={selectedScorecardDto}
        />
      )}
    </div>
  );
}
