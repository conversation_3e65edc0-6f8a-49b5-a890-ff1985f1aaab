/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from '@/components/ui/button';
import DialogFullScreen from '@/components/ui/Hyperbound/DialogFullScreen';
import Header from '@/components/ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '@/components/ui/Hyperbound/DialogFullScreen/ScrollableContent';
import { useGetShareHistory } from '@/hooks/useScorecardConfigsForOrg';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { Check, Loader2Icon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import { Id, toast } from 'react-toastify';
import { useQueryClient } from '@tanstack/react-query';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  open: boolean;
  onClose: () => void;
  scorecard: ScorecardConfigDto;
}

export default function ShareWithOrganizationDialog({
  open,
  onClose,
  scorecard,
}: IProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [shareHistory, setShareHistory] = useState<any[]>([]);
  const [shareWith, setShareWith] = useState<string[]>([]);
  const toastId = useRef<Id | null>(null);
  const queryClient = useQueryClient();

  const { data: dbShareHistory, isLoading: isLadingShareHistory } =
    useGetShareHistory(scorecard.id);
  useEffect(() => {
    if (!isLadingShareHistory && dbShareHistory) {
      setShareHistory(dbShareHistory);
    }
  }, [isLadingShareHistory, dbShareHistory]);

  const toggleShare = (orgId: string) => {
    setShareWith((o) => {
      const tmp: string[] = [];
      let found = false;
      for (const oid of o) {
        if (oid === orgId) {
          found = true;
        } else {
          tmp.push(oid);
        }
      }
      if (!found) {
        tmp.push(orgId);
      }
      return [...tmp];
    });
  };

  const share = async () => {
    if (scorecard && scorecard.id) {
      setIsLoading(true);

      for (const uid of shareWith) {
        try {
          await ScorecardConfigService.ShareWithOrg(scorecard.id, uid);
        } catch (e) {
          console.log('error sharing scorecard', e);
        }
      }

      toastId.current = toast.success('Scorecard successfully shared');

      queryClient.invalidateQueries({
        queryKey: ['get-scorecard-config-share-history'],
      });
      setShareWith([]);
      setIsLoading(false);
      if (onClose) {
        onClose();
      }
    }
  };

  const { sortedOrgs } = useUserSession();

  return (
    <DialogFullScreen open={open} onOpenChange={onClose}>
      <Header
        title="Share scorecard"
        onClose={onClose}
        className="px-4 pt-2"
        isLoading={isLadingShareHistory}
      />
      <ScrollableContent>
        <div className="flex items-stretch p-4 h-full">
          <div className="flex-1 pt-10 pr-4">
            {sortedOrgs
              ?.filter(
                (org) =>
                  !shareHistory?.some(
                    (sharedScorecard) =>
                      sharedScorecard.scorecardConfigId === scorecard.id &&
                      sharedScorecard.sharedWithOrg.uid === org.orgId,
                  ),
              )
              .map((org, i) => {
                let selected = false;
                for (const oid of shareWith) {
                  if (oid === org.orgId) {
                    selected = true;
                    break;
                  }
                }
                return (
                  <div
                    key={'org-' + i}
                    onClick={() => {
                      toggleShare(org.orgId);
                    }}
                    className={
                      'my-1 py-1 px-2 cursor-pointer hover:bg-muted-foreground/50 hover:text-white rounded flex items-center ' +
                      (selected && 'bg-muted text-black ')
                    }
                  >
                    <div className="flex space-x-2 items-center">
                      <Avatar className="w-6 h-6 relative">
                        {org?.orgMetadata?.logo && (
                          <AvatarImage src={org?.orgMetadata?.logo} />
                        )}
                        <AvatarFallback className="text-sm">
                          {org?.orgName?.charAt(0) || ''}
                        </AvatarFallback>
                      </Avatar>
                      <p className="text-base font-medium">
                        {org?.orgName || 'Hyperbound'}
                      </p>
                    </div>
                    <div className="flex-1"></div>
                    {selected && (
                      <div>
                        <Check size={18} />
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
          <div className="flex-1">
            <div className="text-sm font-semibold mb-2">
              Already shared with:
            </div>
            <div>
              {shareHistory.map((sh, i) => {
                let logo: string = '';

                sortedOrgs?.map((org) => {
                  if (org.orgId === sh.sharedWithOrg.uid) {
                    logo = org?.orgMetadata?.logo || '';
                  }
                });

                return (
                  <div key={'sh' + i} className="mb-2">
                    <div className="flex space-x-2 items-center">
                      <Avatar className="w-6 h-6 relative">
                        {logo && <AvatarImage src={logo} />}
                        <AvatarFallback className="text-sm">
                          {sh.sharedWithOrg.name.charAt(0) || ''}
                        </AvatarFallback>
                      </Avatar>
                      <p className="text-sm">{sh.sharedWithOrg.name}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </ScrollableContent>

      {/****************************/}
      {/********* FOOTER *********/}
      {/****************************/}
      <div className="flex items-center justify-end mr-4 mb-4 ml-4">
        <Button
          onClick={share}
          variant={'default'}
          disabled={shareWith.length == 0 || isLoading}
        >
          {isLoading ? <Loader2Icon className="animate-spin" /> : 'Share'}
        </Button>
      </div>
    </DialogFullScreen>
  );
}
