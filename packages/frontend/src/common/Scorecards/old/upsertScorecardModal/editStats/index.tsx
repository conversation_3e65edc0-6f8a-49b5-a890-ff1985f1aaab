import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Headphones, Mic } from 'lucide-react';
import { useState } from 'react';

interface IProps {
  stats: any;
  onEdit: (s: any) => void;
}

export default function EditStats({ stats, onEdit }: IProps) {
  const [error, setError] = useState<any>({});
  if (!stats) {
    stats = {};
  }

  //---------- TALK/LISTEN RATIO
  if (!stats.talkListenRatio) {
    // ratio
    stats.talkListenRatio = {
      talking: 0.8, //80% talking
    };
  }

  //---------- FILLER WORDS
  if (!stats.fillerWords) {
    // Number of filler words per minute
    stats.fillerWords = {
      min: 5,
      max: 10,
    };
  }

  //---------- TALK SPEED
  if (!stats.talkSpeed) {
    // Number of words per minute
    stats.talkSpeed = {
      min: 120,
      max: 130,
    };
  }

  //---------- LONGEST MONOLOGUE
  if (!stats.longestMonologue) {
    // Number of seconds
    stats.longestMonologue = {
      min: 120,
      max: 130,
    };
  }

  /*************************/
  /******* UPDATE **********/
  /*************************/

  const updateNumber = (s: string, n: string) => {
    const regex = /^[0-9]+$/;
    if (regex.test(n)) {
      let _n = Number(n);
      if (_n > 100) {
        if (
          s == 'minTalking' ||
          s == 'maxTalking' ||
          s == 'minListening' ||
          s == 'maxListening' ||
          s == 'minFillerWords' ||
          s == 'maxFillerWords'
        ) {
          _n = 100;
        }
      }

      if (s == 'minLongestMonologue') {
        if (_n > stats.longestMonologue.max) {
          setError({
            ...error,
            longestMonologue:
              'Min longest monologue should be less than max longest monologue',
          });
        } else {
          setError({ ...error, longestMonologue: '' });
        }
        stats.longestMonologue.min = _n;
        onEdit({ ...stats, longestMonologue: stats.longestMonologue });
      } else if (s == 'maxLongestMonologue') {
        if (_n < stats.longestMonologue.min) {
          setError({
            ...error,
            longestMonologue:
              'Max longest monologue should be more than min longest monologue',
          });
        } else {
          setError({ ...error, longestMonologue: '' });
        }
        stats.longestMonologue.max = _n;
        onEdit({ ...stats, longestMonologue: stats.longestMonologue });
      } else if (s == 'minTalkSpeed') {
        if (_n > stats.talkSpeed.max) {
          setError({
            ...error,
            talkSpeed:
              'Min talking speed should be less than max talking speed ',
          });
        } else {
          setError({ ...error, talkSpeed: '' });
        }
        stats.talkSpeed.min = _n;
        onEdit({ ...stats, talkSpeed: stats.talkSpeed });
      } else if (s == 'maxTalkSpeed') {
        if (_n < stats.talkSpeed.min) {
          setError({
            ...error,
            talkSpeed:
              'Max talking speed should be more than min talking speed ',
          });
        } else {
          setError({ ...error, talkSpeed: '' });
        }
        stats.talkSpeed.max = _n;
        onEdit({ ...stats, talkSpeed: stats.talkSpeed });
      } else if (s == 'talkListenRatio') {
        stats.talkListenRatio.talking = _n / 100;
        onEdit({ ...stats, talkListenRatio: stats.talkListenRatio });
      } else if (s == 'minFillerWords') {
        if (_n > stats.fillerWords.max) {
          setError({
            ...error,
            fillerWords:
              'Max filler words should be less than min filler words',
          });
        } else {
          setError({ ...error, fillerWords: '' });
        }
        stats.fillerWords.min = _n;
        onEdit({ ...stats, fillerWords: stats.fillerWords });
      } else if (s == 'maxFillerWords') {
        if (_n < stats.fillerWords.min) {
          setError({
            ...error,
            fillerWords:
              'Max filler words should be more than min filler words',
          });
        } else {
          setError({ ...error, fillerWords: '' });
        }
        stats.fillerWords.max = _n;
        onEdit({ ...stats, fillerWords: stats.fillerWords });
      }
    }
  };

  return (
    <div className="mt-2 border-t pt-2">
      <div>
        <Label>Talk/Listen Ratio</Label>
        <div className="mt-0">
          <div className="text-xs mt-3 text-muted-foreground flex items-center">
            <div className="mr-1">Talking</div>
            <div>
              <Input
                value={100 * stats.talkListenRatio.talking}
                onChange={(e: any) => {
                  updateNumber('talkListenRatio', e.target.value);
                }}
                className="w-[50px]"
              />
            </div>
            <div className="ml-1 mr-1">% of the time. </div>
            <div className="ml-2 text-red-400 text-xs">
              {error['talkListenRatio']}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6">
        <Label>Filler Words</Label>
        <div className="mt-2 text-xs">
          <div className="text-xs mt-3 text-muted-foreground flex items-center">
            <div>
              <Input
                value={stats.fillerWords.min}
                onChange={(e: any) => {
                  updateNumber('minFillerWords', e.target.value);
                }}
                className="w-[50px]"
              />
            </div>
            <div className="ml-1 mr-1">-</div>
            <div>
              <Input
                value={stats.fillerWords.max}
                onChange={(e: any) => {
                  updateNumber('maxFillerWords', e.target.value);
                }}
                className="w-[50px]"
              />
            </div>
            <div className="ml-1">words per minute</div>
            <div className="ml-2 text-red-400 text-xs">
              {error['fillerWords']}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6">
        <Label>Talk Speed</Label>
        <div className="mt-2 flex items-center text-xs">
          <div>
            <Input
              value={stats.talkSpeed.min}
              onChange={(e: any) => {
                updateNumber('minTalkSpeed', e.target.value);
              }}
              className="w-[50px]"
            />
          </div>
          <div className="ml-1 mr-1">-</div>
          <div>
            <Input
              value={stats.talkSpeed.max}
              onChange={(e: any) => {
                updateNumber('maxTalkSpeed', e.target.value);
              }}
              className="w-[50px]"
            />
          </div>
          <div className="ml-1">words per minute</div>
          <div className="ml-2 text-red-400 text-xs">{error['talkSpeed']}</div>
        </div>
      </div>

      <div className="mt-6">
        <Label>Longest Monologue</Label>
        <div className="mt-2 flex items-center text-xs">
          <div>
            <Input
              value={stats.longestMonologue.min}
              onChange={(e: any) => {
                updateNumber('minLongestMonologue', e.target.value);
              }}
              className="w-[50px]"
            />
          </div>
          <div className="ml-1 mr-1">-</div>
          <div>
            <Input
              value={stats.longestMonologue.max}
              onChange={(e: any) => {
                updateNumber('maxLongestMonologue', e.target.value);
              }}
              className="w-[50px]"
            />
          </div>
          <div className="ml-1">seconds</div>
          <div className="ml-2 text-red-400 text-xs">
            {error['longestMonologue']}
          </div>
        </div>
      </div>
    </div>
  );
}
