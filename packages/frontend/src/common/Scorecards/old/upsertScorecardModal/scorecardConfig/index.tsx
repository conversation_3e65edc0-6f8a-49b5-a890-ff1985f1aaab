import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { GripVertical, Trash2 } from 'lucide-react';
import { MouseEvent, useEffect, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';

interface IProps {
  scorecard: ScorecardConfigDto;
  onDelete: (section: any, criterion: any) => void;
  onEdit: (sc: ScorecardConfigDto) => void;
}

export default function ScorecardConfig({
  scorecard,
  onDelete,
  onEdit,
}: IProps) {
  const toastId = useRef<Id | null>(null);

  const cards: any[] = [];
  if (scorecard?.config) {
    Object.keys(scorecard.config).forEach((sectionTitle: string) => {
      let criteria = scorecard.config[sectionTitle];
      if (!criteria) {
        criteria = [];
      }

      cards.push({
        sectionTitle,
        criteria,
      });
    });
  }

  const deleteSection = (section: any) => {
    onDelete(section, undefined);
  };

  const deleteCriterion = (section: any, criterion: any) => {
    onDelete(section, criterion);
  };

  /***********************************/
  /********** DRAG AND DROP **********/
  /***********************************/

  const prevMousePosition = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const isDndRunning = useRef<boolean>(false);
  const mouseMoved = useRef<boolean>(false);
  const mouseDown = useRef<boolean>(false);
  const dragPanel = useRef<HTMLDivElement | null>(null);
  const [currentDraggedItem, setCurrentDraggedItem] = useState<any>({});
  const overtItem = useRef<any>({});
  const movingItem = useRef<any>({});
  const [highlightSection, setHighlightSections] = useState<'Y' | 'N' | ''>('');

  const blockDnd = () => {
    isDndRunning.current = false;
    setIsDragging(false);

    setHighlightSections('');

    if (dragPanel.current) {
      const pnl = dragPanel.current;
      pnl.style.display = 'none';
    }
  };

  const onMouseDown = (
    isSpacer: boolean,
    section: any,
    criterion: any | undefined,
    mouseX: number,
    mouseY: number,
  ) => {
    mouseDown.current = true;
    prevMousePosition.current = { x: mouseX, y: mouseY };
    mouseMoved.current = false;
    movingItem.current = {
      isSpacer,
      section,
      criterion,
    };
    setCurrentDraggedItem(movingItem.current);

    if (section && criterion) {
      setHighlightSections('N');
    } else {
      setHighlightSections('Y');
    }
  };

  const onMouseEnter = (
    isSpacer: boolean,
    section: any,
    criterion: any | undefined,
  ) => {
    if (isDndRunning.current) {
      let go = true;
      if (movingItem.current.section && movingItem.current.criterion) {
        if (movingItem.current.section.sectionTitle != section.sectionTitle) {
          blockDnd();
          go = false;
          toastId.current = toast.error(
            'Criterions cannot be moved across sections',
          );
        }
      }

      if (movingItem.current.section && !movingItem.current.criterion) {
        overtItem.current = {
          isSpacer: false,
          section,
          criterion: undefined,
        };
      } else if (go) {
        overtItem.current = {
          isSpacer,
          section,
          criterion,
        };
      } else {
        overtItem.current = undefined;
      }
    }
  };

  const onMouseLeave = () => {};

  const onMouseUp = async () => {
    mouseDown.current = false;

    if (isDndRunning.current) {
      if (overtItem.current && movingItem.current) {
        if (movingItem.current.criterion) {
          //moving criterion
          if (overtItem.current.isSpacer) {
            if (scorecard?.config) {
              const newConfig: any = {};
              Object.keys(scorecard.config).forEach((sectionTitle: string) => {
                if (sectionTitle == movingItem.current.section.sectionTitle) {
                  let criteria = scorecard.config[sectionTitle];
                  if (!criteria) {
                    criteria = [];
                  }

                  let newCriterions: any[] = [];
                  if (overtItem.current.criterion) {
                    if (
                      movingItem.current.criterion.criterion !=
                      overtItem.current.criterion.criterion
                    ) {
                      for (const cr of criteria) {
                        if (
                          cr.criterion != movingItem.current.criterion.criterion
                        ) {
                          let add = false;
                          if (
                            cr.criterion ==
                            overtItem.current.criterion.criterion
                          ) {
                            add = true;
                          }

                          newCriterions.push(cr);
                          if (add) {
                            newCriterions.push(movingItem.current.criterion);
                          }
                        }
                      }
                    } else {
                      newCriterions = scorecard.config[sectionTitle];
                    }
                  } else {
                    let add = true;
                    for (const cr of criteria) {
                      if (add) {
                        add = false;
                        newCriterions.push(movingItem.current.criterion);
                      }

                      if (
                        cr.criterion != movingItem.current.criterion.criterion
                      ) {
                        newCriterions.push(cr);
                      }
                    }
                  }
                  newConfig[sectionTitle] = newCriterions;
                } else {
                  newConfig[sectionTitle] = scorecard.config[sectionTitle];
                }
              });
              scorecard.config = newConfig;

              onEdit(scorecard);
            }
          }
        } else {
          //moving section
          if (movingItem.current.section && overtItem.current.section) {
            const newConfig: any = {};

            Object.keys(scorecard.config).forEach((sectionTitle: string) => {
              if (sectionTitle == movingItem.current.section.sectionTitle) {
                newConfig[overtItem.current.section.sectionTitle] =
                  overtItem.current.section.criteria;
              } else if (
                sectionTitle == overtItem.current.section.sectionTitle
              ) {
                newConfig[movingItem.current.section.sectionTitle] =
                  movingItem.current.section.criteria;
              } else {
                newConfig[sectionTitle] = scorecard.config[sectionTitle];
              }
            });

            scorecard.config = newConfig;
            onEdit(scorecard);
          }
        }
      }
    }
    blockDnd();
  };

  const onMouseMove = (e: any) => {
    if (mouseDown.current) {
      if (!mouseMoved.current) {
        const dx = Math.abs(e.x - prevMousePosition.current.x);
        const dy = Math.abs(e.y - prevMousePosition.current.y);

        if (dx > 5 || dy > 5) {
          mouseMoved.current = true;
          isDndRunning.current = true;
          setIsDragging(true);
        }
      }

      if (isDndRunning.current) {
        if (dragPanel.current) {
          const pnl = dragPanel.current;
          pnl.style.display = 'block';
          pnl.style.left = e.x + 4 + 'px';
          pnl.style.top = e.y + 0 + 'px';
        }
      }
    }
  };

  useEffect(() => {
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
    return () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    };
  }, [scorecard]);

  /****************************/
  /********** RENDER **********/
  /****************************/

  ///return onMouseDown(c, undefined, e.clientX, e.clientY);
  return (
    <div>
      <div className="mb-6 mt-2 text-sm font-semibold">Preview</div>
      <div
        className={
          'grid grid-cols-2 gap-4 ' + (isDragging ? 'select-none' : '')
        }
      >
        {cards.map((c: any, i: number) => {
          return (
            <Card
              key={'section-' + String(i)}
              onMouseEnter={(e: MouseEvent) => {
                onMouseEnter(false, c, undefined);
              }}
              onMouseLeave={(e: MouseEvent) => {
                onMouseLeave();
              }}
              className={
                highlightSection == 'Y' &&
                currentDraggedItem.section.sectionTitle != c.sectionTitle
                  ? 'hover:bg-muted'
                  : ''
              }
            >
              <CardHeader className="p-3">
                <CardTitle
                  className={
                    'text-sm flex items-center p-2 rounded-lg ' +
                    (isDragging ? '' : 'cursor-grab')
                  }
                  onMouseDown={(e: MouseEvent) => {
                    onMouseDown(false, c, undefined, e.clientX, e.clientY);
                  }}
                >
                  <div>
                    <GripVertical
                      size={16}
                      className="mr-1 text-muted-foreground"
                    />
                  </div>
                  <div className="flex-1">{c.sectionTitle}</div>
                  <div
                    className="cursor-pointer"
                    onClick={() => {
                      deleteSection(c);
                    }}
                  >
                    <Trash2 size={16} />
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="px-3">
                <div
                  className={
                    'h-2 rounded ' +
                    (highlightSection == 'N' ? 'hover:bg-muted' : '')
                  }
                  onMouseEnter={(e: MouseEvent) => {
                    onMouseEnter(true, c, undefined);
                  }}
                  onMouseLeave={(e: MouseEvent) => {
                    onMouseLeave();
                  }}
                ></div>
                {c.criteria.map((cr: any, k: number) => {
                  return (
                    <div key={'criterion-' + String(i) + '-' + String(k)}>
                      <div
                        className={
                          'text-sm flex items-center px-2 rounded-lg ' +
                          (isDragging ? '' : 'cursor-grab')
                        }
                        onMouseDown={(e: MouseEvent) => {
                          onMouseDown(false, c, cr, e.clientX, e.clientY);
                        }}
                      >
                        <div>
                          <GripVertical
                            size={16}
                            className="mr-1 text-muted-foreground"
                          />
                        </div>
                        <div className="flex-1 mr-2">{cr.criterion}</div>
                        <div
                          className="cursor-pointer hover:bg-muted"
                          onClick={() => {
                            deleteCriterion(c, cr);
                          }}
                        >
                          <Trash2 size={16} />
                        </div>
                      </div>
                      <div
                        className={
                          'h-2 rounded ' +
                          (highlightSection == 'N' ? 'hover:bg-muted' : '')
                        }
                        onMouseEnter={(e: MouseEvent) => {
                          onMouseEnter(true, c, cr);
                        }}
                        onMouseLeave={(e: MouseEvent) => {
                          onMouseLeave();
                        }}
                      ></div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div
        className="absolute p-2 border border-gray-300 bg-black/50 text-white rounded-xl"
        style={{ display: 'none' }}
        ref={dragPanel}
      >
        {currentDraggedItem.criterion ? (
          <div className="text-sm">
            {currentDraggedItem.criterion.criterion}
          </div>
        ) : (
          currentDraggedItem.section && (
            <Card className="bg-white/60">
              <CardHeader className="p-3">
                <CardTitle className={'text-sm flex items-center p-2 '}>
                  <div className="flex-1">
                    {currentDraggedItem.section.sectionTitle}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="px-3">
                {currentDraggedItem.section.criteria.map(
                  (cr: any, k: number) => {
                    return (
                      <div key={'mv-criterion-' + String(k)}>
                        <div
                          className={
                            'text-sm flex items-center px-2 rounded-lg '
                          }
                        >
                          <div className="flex-1 mr-2">{cr.criterion}</div>
                        </div>
                      </div>
                    );
                  },
                )}
              </CardContent>
            </Card>
          )
        )}
      </div>
    </div>
  );
}
