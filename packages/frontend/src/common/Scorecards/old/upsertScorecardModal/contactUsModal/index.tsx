import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import useUserSession from '@/hooks/useUserSession';
import { Mail, Target } from 'lucide-react';

interface IProps {
  open: boolean;
  onClose: (o: boolean) => void;
}

export default function ContactUsNewScorecardModal({ open, onClose }: IProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Target className="w-5 h-5 mr-2" />
            New scorecard
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div>
            <Button
              size={'lg'}
              variant={'default'}
              className={
                'w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200'
              }
              style={{
                backgroundImage:
                  'linear-gradient(to right, #000000, #5189CE, #A168A2)',
              }}
              onClick={(e) => {
                e.stopPropagation();
                window.location.href = 'mailto:<EMAIL>';
              }}
            >
              <Mail className="mr-2 h-4 w-4" />
              Contact the Hyperbound team to create
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
