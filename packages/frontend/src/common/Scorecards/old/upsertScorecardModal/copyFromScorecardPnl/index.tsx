import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import SelectScorecardItems from './selectScorecardItems';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  targetScorecard: ScorecardConfigDto;
  onScorecardUpdated: (sc: ScorecardConfigDto) => void;
}

export default function CopyFromScorecardPnl({
  targetScorecard,
  onScorecardUpdated,
}: IProps) {
  const [selectedScorecardId, setSelectedScorecardId] = useState<string>('');

  /***********************************/
  /************ INIT ***************/
  /***********************************/

  const { data: scorecardConfigOptions, isLoading: isLoadingScorecards } =
    useScorecardConfigsForOrg();

  const toggle = (section: any, criterions: any[]) => {
    if (!targetScorecard.config) {
      targetScorecard.config = {};
    }

    if (!targetScorecard.config[section.sectionTitle]) {
      targetScorecard.config[section.sectionTitle] = criterions;
    } else {
      let currentCriterions = targetScorecard.config[section.sectionTitle];
      if (!currentCriterions) {
        currentCriterions = [];
      }

      const allCriteriaObj: any = {};
      for (const cr of currentCriterions) {
        allCriteriaObj[cr.criterion] = cr;
      }

      const tmp = [];
      //console.log(allCriteriaObj, criterions)
      for (const cr of criterions) {
        if (allCriteriaObj[cr.criterion]) {
          //toggle off
          delete allCriteriaObj[cr.criterion];
        } else {
          tmp.push(cr);
        }
      }

      if (tmp.length == 0 && Object.values(allCriteriaObj).length == 0) {
        delete targetScorecard.config[section.sectionTitle];
      } else {
        targetScorecard.config[section.sectionTitle] = [
          ...Object.values(allCriteriaObj),
          ...tmp,
        ];
      }
    }

    onScorecardUpdated(targetScorecard);
  };

  return (
    <div className="">
      <div className="flex items-center">
        <div className="mr-2 text-sm text-nowrap">Copy from scorecard:</div>
        <Select
          onValueChange={(value: string) => {
            setSelectedScorecardId(value);
          }}
          value={selectedScorecardId}
        >
          <SelectTrigger>
            <SelectValue placeholder="Choose a scorecard" />
          </SelectTrigger>
          <SelectContent>
            {scorecardConfigOptions && (
              <>
                {scorecardConfigOptions.map((option) => {
                  if (option.id != targetScorecard.id) {
                    return (
                      <SelectItem key={option.id} value={String(option.id)}>
                        {option.tag}
                      </SelectItem>
                    );
                  }
                })}
              </>
            )}
          </SelectContent>
        </Select>
      </div>

      <div className="mt-4">
        {scorecardConfigOptions && (
          <>
            {scorecardConfigOptions.map((option) => {
              if (String(option.id) == selectedScorecardId) {
                return (
                  <SelectScorecardItems
                    key={option.id}
                    scorecard={option}
                    selectedConfig={targetScorecard.config}
                    onToggleCriteria={toggle}
                  />
                );
              }
            })}
          </>
        )}
      </div>
    </div>
  );
}
