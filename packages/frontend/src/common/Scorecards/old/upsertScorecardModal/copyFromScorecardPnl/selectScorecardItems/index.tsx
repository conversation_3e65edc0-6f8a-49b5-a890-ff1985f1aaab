import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from '@/components/ui/card';
import Checkbox from '@/components/ui/Hyperbound/checkbox-with-label';

interface IProps {
  scorecard: ScorecardConfigDto;
  selectedConfig: any;
  onToggleCriteria: (s: any, c: any[]) => void;
}

export default function SelectScorecardItems({
  scorecard,
  onToggleCriteria,
  selectedConfig,
}: IProps) {
  //console.log(scorecard?.config, selectedConfig);

  const cards: any[] = [];
  if (scorecard?.config) {
    Object.keys(scorecard.config).forEach((sectionTitle: string) => {
      let criteria = scorecard.config[sectionTitle];
      if (!criteria) {
        criteria = [];
      }
      const allCriteriaObj: any = {};
      for (const cr of criteria) {
        cr.selected = false;
        allCriteriaObj[cr.criterion] = cr;
      }

      let selected = false;
      if (selectedConfig[sectionTitle]) {
        selected = true;
        const sCriteria = selectedConfig[sectionTitle];
        if (sCriteria) {
          for (const cr of sCriteria) {
            if (allCriteriaObj[cr.criterion]) {
              allCriteriaObj[cr.criterion].selected = true;
            }
          }
        }
      }

      cards.push({
        sectionTitle,
        criteria: Object.values(allCriteriaObj),
        selected,
      });
    });
  }

  const selectSection = (section: any) => {
    onToggleCriteria(section, section.criteria);
  };
  const selectCriterion = (section: any, criterion: any) => {
    onToggleCriteria(section, [criterion]);
  };

  return (
    <div className="grid grid-cols-2 gap-4">
      {cards.map((c: any, i: number) => {
        return (
          <Card key={'section-' + String(i)}>
            <CardHeader className="p-3">
              <CardTitle className="text-sm">
                <Checkbox
                  onToggle={() => {
                    selectSection(c);
                  }}
                  hover={true}
                  checked={c.selected}
                  className="p-2 m-0"
                >
                  {c.sectionTitle}
                </Checkbox>
              </CardTitle>
            </CardHeader>
            <CardContent className="px-3">
              {c.criteria.map((cr: any, k: number) => {
                return (
                  <div
                    key={'criterion-' + String(i) + '-' + String(k)}
                    className="text-sm"
                  >
                    <Checkbox
                      onToggle={() => {
                        selectCriterion(c, cr);
                      }}
                      hover={true}
                      checked={cr.selected}
                      className="p-2 m-0"
                    >
                      {cr.criterion}
                    </Checkbox>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
