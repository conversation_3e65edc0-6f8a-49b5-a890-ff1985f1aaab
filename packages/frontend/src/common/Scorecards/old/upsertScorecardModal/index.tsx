/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from '@/components/ui/button';
import useUserSession from '@/hooks/useUserSession';
import DialogFullScreen from '@/components/ui/Hyperbound/DialogFullScreen';
import Header from '@/components/ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '@/components/ui/Hyperbound/DialogFullScreen/ScrollableContent';
import { useEffect, useRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ChevronLeft, Loader2Icon } from 'lucide-react';
import CopyFromScorecardPnl from './copyFromScorecardPnl';
import ScorecardConfigDto, {
  ScorecardConfigStatus,
} from '@/lib/ScorecardConfig/types';
import ScorecardConfig from './scorecardConfig';
import { Id, toast } from 'react-toastify';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import { Pencil2Icon } from '@radix-ui/react-icons';
import EditStats from './editStats';

interface IProps {
  open: boolean;
  onClose: () => void;
  scorecard?: ScorecardConfigDto;
}

export default function UpsertScorecardModal({
  open,
  onClose,
  scorecard,
}: IProps) {
  const { dbOrg } = useUserSession();

  const [name, setName] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [editingScorecard, setEditingScorecard] = useState<ScorecardConfigDto>({
    stats: {},
    config: {},
    defaultType: 'Cold',
    orgId: dbOrg?.id || 0,
    tag: '',
    editable: true,
    status: ScorecardConfigStatus.DRAFT,
  });
  const [canSave, setCanSave] = useState<boolean>(false);
  const toastId = useRef<Id | null>(null);
  const [pageState, setPageState] = useState<string>('editConfig');

  useEffect(() => {
    let _name = '';
    let _editingScorecard = {
      stats: {},
      config: {},
      defaultType: 'Cold',
      orgId: dbOrg?.id || 0,
      tag: '',
      editable: true,
      status: ScorecardConfigStatus.DRAFT,
    };
    if (scorecard) {
      _name = scorecard.tag;
      _editingScorecard = { ...scorecard };
    }
    setName(_name);
    setEditingScorecard(_editingScorecard);
    checkState(_editingScorecard, undefined);
  }, [scorecard]);

  /***********************************/
  /************ STATE ***************/
  /***********************************/

  const checkState = (
    csc: ScorecardConfigDto | undefined,
    n: string | undefined,
  ) => {
    let sc: ScorecardConfigDto = editingScorecard;
    if (csc) {
      sc = csc;
    }
    let cs = false;
    if (sc.config) {
      if (Object.keys(sc.config).length > 0) {
        cs = true;
      }
    }
    if (cs) {
      let nn = name;
      if (n || n == '') {
        nn = n;
      }
      if (nn == '') {
        cs = false;
      }
    }
    setCanSave(cs);
  };
  /***********************************/
  /************ CONFIG ***************/
  /***********************************/

  const updateConfig = (sc: ScorecardConfigDto) => {
    checkState(sc, undefined);
    setEditingScorecard({ ...sc });
  };

  const deleteConfigItem = (section: any, criterion: any) => {
    if (!editingScorecard.config) {
      editingScorecard.config = {};
    }

    if (editingScorecard.config[section.sectionTitle]) {
      if (criterion) {
        const currentCriterions = editingScorecard.config[section.sectionTitle];
        const tmp = [];
        for (const cr of currentCriterions) {
          if (cr.criterion == criterion.criterion) {
            //toggle off
          } else {
            tmp.push(cr);
          }
        }
        if (tmp.length == 0) {
          delete editingScorecard.config[section.sectionTitle];
        } else {
          editingScorecard.config[section.sectionTitle] = tmp;
        }
      } else {
        delete editingScorecard.config[section.sectionTitle];
      }
    }

    updateConfig(editingScorecard);
  };

  /***********************************/
  /************ STATS ***************/
  /***********************************/

  const startEditStats = () => {
    setPageState('editStats');
  };

  const backFromStats = () => {
    setPageState('editConfig');
  };

  const updateStats = (s: any) => {
    setEditingScorecard({ ...editingScorecard, stats: s });
  };

  /***********************************/
  /************ SAVE ***************/
  /***********************************/

  const save = async () => {
    setIsLoading(true);

    let ok = true;

    try {
      if (editingScorecard.id) {
        await ScorecardConfigService.Update(
          editingScorecard.id,
          editingScorecard.config,
          name,
          editingScorecard.stats,
        );
      } else {
        await ScorecardConfigService.CreateNew(
          editingScorecard.config,
          name,
          true,
          editingScorecard.stats,
        );
      }
    } catch (e) {
      ok = false;
      console.log(e);
      toastId.current = toast.error('There was an error. Please try again.');
    }

    setIsLoading(false);
    if (ok) {
      onClose();
    }
  };

  /***********************************/
  /************ RENDER ***************/
  /***********************************/

  return (
    <DialogFullScreen open={open} onOpenChange={onClose}>
      <Header title="New scorecard" onClose={onClose} className="px-4 pt-2  " />

      {/****************************/}
      {/********* NAME *************/}
      {/****************************/}
      <div className="ml-4 flex items-center mt-4">
        <Label>Name</Label>
        <div className="ml-2">
          <Input
            value={name}
            onChange={(e) => {
              checkState(undefined, e.target.value);
              setName(e.target.value);
            }}
          />
        </div>
      </div>

      {pageState == 'editConfig' && (
        <ScrollableContent>
          <div className="flex items-stretch p-4 h-full">
            {/****************************/}
            {/********* LEFT PNL *********/}
            {/****************************/}
            <div className="flex-1 pr-6 border-r">
              <CopyFromScorecardPnl
                targetScorecard={editingScorecard}
                onScorecardUpdated={updateConfig}
              />
            </div>

            {/****************************/}
            {/******** RIGHT PNL *********/}
            {/****************************/}
            <div className="flex-1 pl-6">
              <ScorecardConfig
                scorecard={editingScorecard}
                onDelete={deleteConfigItem}
                onEdit={updateConfig}
              />
            </div>
          </div>
        </ScrollableContent>
      )}

      {pageState == 'editStats' && (
        <ScrollableContent>
          <div className="p-4 h-full">
            <EditStats stats={editingScorecard.stats} onEdit={updateStats} />
          </div>
        </ScrollableContent>
      )}

      {/****************************/}
      {/********* FOOTER *********/}
      {/****************************/}
      <div className="flex items-center justify-end mr-4 mb-4 ml-4">
        {pageState == 'editConfig' && (
          <Button variant={'outline'} onClick={startEditStats}>
            <Pencil2Icon className="mr-1" />
            Stats
          </Button>
        )}
        {pageState == 'editStats' && (
          <Button variant={'outline'} onClick={backFromStats}>
            <ChevronLeft className="mr-1" />
            Back
          </Button>
        )}
        <div className="flex-1"></div>
        <Button
          onClick={save}
          variant={'default'}
          disabled={!canSave || isLoading}
        >
          {isLoading ? <Loader2Icon className="animate-spin" /> : 'Save'}
        </Button>
      </div>
    </DialogFullScreen>
  );
}
