import DialogFullScreen from '@/components/ui/Hyperbound/DialogFullScreen';
import Header from '@/components/ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '@/components/ui/Hyperbound/DialogFullScreen/ScrollableContent';
import { Button } from '@/components/ui/button';
import { Loader2Icon } from 'lucide-react';
import { useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface IProps {
  open: boolean;
  onSave: (conf: any) => void;
  onCancel: () => void;
}

export default function GenereatedNewScorecardModal({
  open,
  onSave,
  onCancel,
}: IProps) {
  const [canSave, setCanSave] = useState<boolean>(false);
  const toastId = useRef<Id | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [details, setDetails] = useState<string>('');
  const [config, setConfig] = useState<string>('');
  const [currentStep, setCurrenStep] = useState<string>('enterDetails');

  const back = () => {
    setCurrenStep('enterDetails');
  };

  const openPreview = () => {
    onSave(JSON.parse(config));
  };

  /***********************************/
  /************ GENEREATE ***************/
  /***********************************/

  const generate = async () => {
    setIsLoading(true);

    let ok = true;

    let c: any = {};
    try {
      c = await ScorecardConfigService.GenerateConfig(details);
    } catch (e) {
      ok = false;
      console.log(e);
      toastId.current = toast.error('There was an error. Please try again.');
    }

    setIsLoading(false);
    if (ok) {
      setConfig(JSON.stringify(c, undefined, 4));
      setCurrenStep('previewConfig');
    }
  };

  return (
    <DialogFullScreen open={open} onOpenChange={onCancel}>
      <Header
        title="Genereate new scorecard"
        onClose={onCancel}
        className="px-4 pt-2  "
      />

      <ScrollableContent>
        {currentStep == 'enterDetails' && (
          <div className="px-4 pt-4">
            <Label>Enter details</Label>
            <Textarea
              value={details}
              onChange={(e) => {
                setDetails(e.target.value);
                if (e.target.value != '') {
                  setCanSave(true);
                } else {
                  setCanSave(false);
                }
              }}
            />
          </div>
        )}

        {currentStep == 'previewConfig' && (
          <div className="px-4 pt-4">
            <Label>Generated Config</Label>
            <Textarea
              value={config}
              onChange={(e) => {
                setConfig(e.target.value);
                if (e.target.value != '') {
                  setCanSave(true);
                } else {
                  setCanSave(false);
                }
              }}
            />
          </div>
        )}
      </ScrollableContent>

      {/****************************/}
      {/********* FOOTER *********/}
      {/****************************/}
      <div className="flex items-center justify-end mr-4 mb-4">
        {currentStep == 'previewConfig' && (
          <Button
            onClick={back}
            variant={'outline'}
            disabled={!canSave || isLoading}
          >
            Back
          </Button>
        )}

        {currentStep == 'enterDetails' && (
          <Button
            onClick={generate}
            variant={'default'}
            disabled={!canSave || isLoading}
          >
            {isLoading ? <Loader2Icon className="animate-spin" /> : 'Generate'}
          </Button>
        )}

        {currentStep == 'previewConfig' && (
          <Button onClick={openPreview} variant={'default'} disabled={!canSave}>
            Preview
          </Button>
        )}
      </div>
    </DialogFullScreen>
  );
}
