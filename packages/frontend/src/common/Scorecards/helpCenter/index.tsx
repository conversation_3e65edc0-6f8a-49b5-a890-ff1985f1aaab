import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export const HelpCenter = ({
  questions,
  className,
}: {
  questions: {
    title: string;
    description: string;
    youtubeEmbed?: React.ReactNode;
  }[];
  className?: string;
}) => {
  return (
    <div className={className || ''}>
      <Card>
        <CardHeader className="pt-3 pb-4 px-4 px-[20px]">
          <CardTitle className="text-base font-medium">Help Center</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col px-[20px]">
          {questions.map((q, idx) => {
            return (
              <div
                key={idx}
                className={cn('flex flex-col', {
                  'mt-4 pt-4 border-t': idx !== 0,
                })}
              >
                <p className="font-medium">{q.title}</p>
                <p className="text-muted-foreground mt-2">{q.description}</p>
                {!!q.youtubeEmbed && (
                  <div className="rounded-xl overflow-hidden self-start mt-3">
                    {q.youtubeEmbed}
                  </div>
                )}
              </div>
            );
          })}
        </CardContent>
      </Card>
    </div>
  );
};
