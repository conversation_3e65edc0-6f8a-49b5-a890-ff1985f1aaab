import DashboardNavbar from '@/common/DashboardNavbar';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import useOrg from '@/hooks/useOrg';
import useUserSession from '@/hooks/useUserSession';
import { AgentCallType } from '@/lib/Agent/types';
import LinksManager from '@/lib/linksManager';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto, {
  ScorecardConfigMaterialScope,
  ScorecardConfigStatus,
} from '@/lib/ScorecardConfig/types';
import dayjs from 'dayjs';
import {
  Copy,
  MoreVerticalIcon,
  Loader2Icon,
  Lock,
  Pencil,
  PlusIcon,
  SearchIcon,
  Share2,
  Send,
  Trash2,
  BookMarked,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { CallTypeBadge } from './CallTypeBadge';
import ShareWithOrganizationDialog from './old/shareWithOrganizationDialog';
import ScorecardPreviewDetailView from './scorecardPreview/detailView';
import ScorecardPreviewRepView from './scorecardPreview/repView';
import { Badge } from '@/components/ui/badge';
import { cn, timeAgo } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import CallTypeFilter from './filterControls/CallTypeFilter';
import Image from 'next/image';
import PageHeader from '@/components/PageHeader';
import { AppPermissions } from '@/lib/permissions';
import LearningMaterials from './LearningMaterials';
import useScorecardConfigMaterials from '@/hooks/useScorecardConfigMaterials';

export default function Scorecards({
  selectedScorecardId,
  setSelectedScorecardId,
  callTypes,
  setCallTypes,
  filterType,
  setFilterType,
}: {
  selectedScorecardId?: number;
  setSelectedScorecardId: (newScorecardId: number | undefined) => unknown;
  callTypes: (AgentCallType | string)[];
  setCallTypes: (newCallTypes: (AgentCallType | string)[]) => unknown;
  filterType: string;
  setFilterType: (filterType: string) => unknown;
}) {
  const [search, setSearch] = useState('');

  const { canAccess, canCreateSubOrgs, canAccessScorecardsMaterials } =
    useUserSession();
  const [isPublishing, setIsPublishing] = useState(false);
  const [isArchiving, setIsArchiving] = useState(false);
  const { data: org } = useOrg();

  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);
  const [
    archiveSingleScorecardConfirmation,
    setArchiveSingleScorecardConfirmation,
  ] = useState(false);
  const [
    archiveDefaultScorecardsConfirmation,
    setArchiveDefaultScorecardsConfirmation,
  ] = useState(false);

  const [
    archiveNonDefaultScorecardsConfirmation,
    setArchiveNonDefaultScorecardsConfirmation,
  ] = useState(false);

  const [versionTab, setVersionTab] = useState<'published' | 'draft'>(
    'published',
  );

  const [selectedScorecardDto, setSelectedScorecardDto] =
    useState<ScorecardConfigDto>();

  const initSelectedScorecard = async () => {
    if (selectedScorecardId) {
      let sc = allScorecards.find((sc) => sc.id === selectedScorecardId);
      if (sc) {
        setSelectedScorecardDto(sc);
      } else {
        sc =
          await ScorecardConfigService.getScorecardConfigById(
            selectedScorecardId,
          );
        setSelectedScorecardDto(sc);
      }
    }
  };

  useEffect(() => {
    initSelectedScorecard();
  }, [allScorecards, selectedScorecardId]);

  useEffect(() => {
    setVersionTab(
      selectedScorecardDto?.status === ScorecardConfigStatus.ACTIVE
        ? 'published'
        : 'draft',
    );
  }, [selectedScorecardDto]);

  const selectedScorecardInfo = useMemo(
    () =>
      versionTab === 'published' || !selectedScorecardDto?.drafts?.length
        ? selectedScorecardDto
        : selectedScorecardDto.drafts[0],
    [selectedScorecardDto, versionTab],
  );

  const filteredScorecards = useMemo(() => {
    return allScorecards
      .filter((sc) => {
        const searchTermIncluded = sc.tag.toLowerCase().includes(search);
        const callTypesIncluded =
          !callTypes.length ||
          (!sc.callTypes?.length && filterType !== 'only') ||
          callTypes[0] === 'all' ||
          (filterType === 'only'
            ? sc.callTypes?.length === 1 &&
              sc.callTypes[0].callType === callTypes[0]
            : sc.callTypes?.some((ct) =>
                callTypes.includes(ct.callType as AgentCallType),
              ));
        return searchTermIncluded && callTypesIncluded;
      })
      .sort((sc1, sc2) => {
        return (
          new Date(sc2.updatedAt || 0).getTime() -
          new Date(sc1.updatedAt || 0).getTime()
        );
      });
  }, [allScorecards, search, callTypes, filterType]);

  const defaultScorecards = useMemo(() => {
    return filteredScorecards.filter((sc) => {
      const callTypes = sc.callTypes || [];
      return callTypes.some((ct) => ct.isDefaultForCallType);
    });
  }, [filteredScorecards]);

  const nonDefaultScorecards = useMemo(() => {
    return filteredScorecards.filter((sc) => {
      const callTypes = sc.callTypes || [];
      return !callTypes.some((ct) => ct.isDefaultForCallType);
    });
  }, [filteredScorecards]);

  const defaultArchivableScorecards = useMemo(() => {
    return defaultScorecards.filter((sc) => sc.archivable);
  }, [defaultScorecards]);

  const nonDefaultArchivableScorecards = useMemo(() => {
    return nonDefaultScorecards.filter((sc) => sc.archivable);
  }, [nonDefaultScorecards]);

  const [isLoadingScorecards, setIsLoadingScorecards] =
    useState<boolean>(false);


      const {
        data: learningMaterialsDb,
        refetch: refetchLearningMaterials,
      } = useScorecardConfigMaterials(selectedScorecardId as number);

  const fetchData = async (setDefault = false) => {
    setIsLoadingScorecards(true);

    const scs = await ScorecardConfigService.getAllScorecardConfigsForOrg(
      canAccess(AppPermissions.MANAGE_SCORECARDS),
      false,
    );
    setAllScorecards(scs);

    if (setDefault) {
      if (scs.length > 0) {
        if (selectedScorecardId) {
          const initialSc = scs.find((sc) => sc.id === selectedScorecardId);
          if (initialSc?.id) {
            setSelectedScorecardId(initialSc.id);
          }
        }
      }
    }

    setIsLoadingScorecards(false);
  };

  const onAdjustLearningMaterialsForCriterion = async (
    dto: ScorecardConfigDto,
    learningMaterialIds: number[],
    sectionTitle: string,
    criterionIdx: number,
  ) => {
    if (dto.id) {
      dto.config[sectionTitle][criterionIdx].learningMaterialIds =
        learningMaterialIds;
      await ScorecardConfigService.Update(
        dto.id,
        dto.config,
        dto.tag,
        dto.stats,
        dto.callTypes,
        true,
      );
      await refetchLearningMaterials();
    }
  };

  const onPublish = async (dto: ScorecardConfigDto) => {
    if (dto.id) {
      setIsPublishing(true);
      const updatedScorecardConfig = await ScorecardConfigService.Update(
        dto.id,
        dto.config,
        dto.tag,
        dto.stats,
        dto.callTypes,
        true,
      );

      setAllScorecards(
        allScorecards.map((item) => {
          if (item.id === dto.id) {
            return { ...updatedScorecardConfig };
          }
          return item;
        }),
      );
      setIsPublishing(false);
    }
  };

  const router = useRouter();
  const { blurSecondaryPages, isCompetitionOrg } = useUserSession();

  const onCreateNewScorecardPress = () => {
    router.push(LinksManager.scorecards('new'));
  };

  const handleCloneScorecard = async (scId: number) => {
    const res = await ScorecardConfigService.CloneScorecardConfig(scId);
    if (res?.id) {
      await fetchData(true);
      router.push(LinksManager.scorecards(`?id=${res.id}`));
      setSelectedScorecardId(res.id);
    }
  };

  const handleArchiveScorecard = async (ids: number[]) => {
    setIsArchiving(true);
    await ScorecardConfigService.ArchiveScorecardConfigs(ids);
    await fetchData(true);
    setIsArchiving(false);
    setArchiveDefaultScorecardsConfirmation(false);
    setArchiveNonDefaultScorecardsConfirmation(false);
    setArchiveSingleScorecardConfirmation(false);
  };

  const onEditScorecardPress = (scorecardId: number) => {
    router.push(LinksManager.scorecards(`${scorecardId}/edit`));
  };
  const isPageDisabled = blurSecondaryPages || isCompetitionOrg;

  useEffect(() => {
    if (!isPageDisabled) {
      fetchData(true);
    }
  }, [blurSecondaryPages, isCompetitionOrg]);

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  const [shareWithOrgPnl, setShareWithOrgPnl] = useState<boolean>(false);

  const renderScorecardCard = (sc: ScorecardConfigDto) => {
    return (
      <Card
        key={sc.tag}
        className={cn('mt-4 cursor-pointer hover:bg-secondary relative', {
          'bg-secondary': selectedScorecardId === sc.id,
        })}
        onClick={() => {
          if (sc.id) {
            setSelectedScorecardId(sc.id);
          }
        }}
      >
        <CardContent className="px-3 py-2 flex flex-col">
          <p>
            <span className="font-medium">{sc.tag}</span>

            {(sc.status === ScorecardConfigStatus.DRAFT ||
              (sc.drafts && sc.drafts?.length > 0)) && (
              <span className="ml-4 text-xs text-muted-foreground">Draft</span>
            )}
          </p>

          {canAccess(AppPermissions.DEVELOPER_SETTINGS) && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute top-0 right-0"
                >
                  <MoreVerticalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  className="cursor-pointer flex items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCloneScorecard(sc.id!);
                  }}
                >
                  <Copy className="w-4 h-4 mr-2 text-muted-foreground" /> Clone
                </DropdownMenuItem>
                {sc.archivable && (
                  <DropdownMenuItem
                    className="cursor-pointer flex items-center"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleArchiveScorecard([sc.id!]);
                    }}
                  >
                    <Trash2 className="w-4 h-4 mr-2 text-muted-foreground" />{' '}
                    Archive
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          <p>
            {sc.callTypes?.length ? (
              sc.callTypes.map((c) => (
                <CallTypeBadge
                  key={c.callType}
                  className="mt-2 mr-1"
                  callType={c.callType as AgentCallType}
                  showDefaultLabel={c.isDefaultForCallType}
                />
              ))
            ) : (
              <Badge variant="secondary" className="mt-2 mr-1 font-normal">
                All
              </Badge>
            )}
          </p>
          <p className="text-muted-foreground mt-2">
            Updated{' '}
            {timeAgo(sc.updatedAt ? new Date(sc.updatedAt) : new Date())}
          </p>
        </CardContent>
      </Card>
    );
  };

  const [activeTab, setActiveTab] = useState<
    'detail-view' | 'rep-view' | 'learning-materials'
  >(canAccess(AppPermissions.MANAGE_SCORECARDS) ? 'detail-view' : 'rep-view');

  const handleOpenArchiveSingleScorecard = () => {
    setArchiveSingleScorecardConfirmation(true);
  };

  const handleCloseArchiveSingleScorecard = () => {
    setArchiveSingleScorecardConfirmation(false);
  };

  const handleOpenArchiveAllScorecards = () => {
    setArchiveDefaultScorecardsConfirmation(true);
  };

  const handleCloseArchiveDefaultScorecard = () => {
    setArchiveDefaultScorecardsConfirmation(false);
  };

  const handleCloseArchiveNonDefaultScorecard = () => {
    setArchiveNonDefaultScorecardsConfirmation(false);
  };

  const handleOpenArchiveNonDefaultScorecards = () => {
    setArchiveNonDefaultScorecardsConfirmation(true);
  };

  if (isPageDisabled) {
    if (isCompetitionOrg) {
      return (
        <div
          className={
            'bg-[#FBFBFB] relative h-[100vh] block overflow-hidden px-4 py-4'
          }
        >
          <div className="h-[100vh] w-full overflow-hidden flex flex-col">
            <div className="px-6">
              <PageHeader title="Scorecards" />
            </div>
            <div className="w-full relative flex-1 mt-4">
              <Image
                src={'/images/scorecard.png'}
                fill
                className="object-contain object-top"
                alt="learning modules"
              />
            </div>
          </div>
          <div
            className="absolute top-0 left-0 right-0 bottom-0"
            style={{
              background:
                ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
            }}
          >
            <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
              <div className="flex-1"></div>
              <div className="text-muted-foreground flex flex-col justify-center items-center">
                <div>
                  <Lock size={16} />
                </div>
                <div className="text-xs mt-2">Locked</div>
              </div>
              <div className="font-semibold text-lg mt-4">
                Unlock the power of Custom AI Scorecards
              </div>
              <div className="text-base mt-2 text-muted-foreground max-w-[50%] text-center">
                Build custom AI scorecards tailored to your business and build
                them around any sales methodology you&apos;d like.
              </div>
            </div>
          </div>
        </div>
      );
    }
  }
  return (
    <>
      <div className="bg-[#FBFBFB] h-full flex flex-col">
        <DashboardNavbar
          className="bg-[#FBFBFB]"
          breadcrumbs={[{ title: 'Custom AI Scorecards' }]}
          rightContent={
            canAccess(AppPermissions.MANAGE_SCORECARDS) && (
              <div className="flex items-center">
                <div className="flex-1"></div>
                <Button
                  onClick={onCreateNewScorecardPress}
                  disabled={isPilotEnded}
                >
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Create new
                </Button>
              </div>
            )
          }
        />

        <div className="flex-1 flex flex-row relative">
          <div className="absolute left-0 right-0 bottom-0 top-0 flex flex-row">
            <div className="w-[460px] h-full pt-4 flex flex-col">
              <div className="px-4">
                <div className="relative">
                  <div className="absolute left-0 top-0 bottom-0 flex flex-col pl-2.5 justify-center">
                    <SearchIcon className="text-muted-foreground w-4 h-4" />
                  </div>
                  <Input
                    disabled={isPageDisabled || isLoadingScorecards}
                    className=" pl-8"
                    placeholder="Search"
                    value={search}
                    onChange={(e) => {
                      setSearch(e.target.value);
                    }}
                  />
                </div>
              </div>
              <div className="mt-2 mb-2 px-4">
                <CallTypeFilter
                  scorecards={allScorecards}
                  disabled={isPageDisabled}
                  current={callTypes}
                  onCallTypesUpdated={(newTypes) => {
                    setCallTypes(newTypes);
                  }}
                  onFilterTypeUpdated={(filterType: string) =>
                    setFilterType(filterType)
                  }
                />
              </div>
              <div className="flex-1 overflow-y-auto flex flex-col">
                {isLoadingScorecards && (
                  <div className="h-full w-full flex flex-col justify-center items-center">
                    <Loader2Icon className="animate-spin" />
                  </div>
                )}
                {!!defaultScorecards.length && (
                  <div className="py-4 px-4 border-b">
                    <div className="flex flex-row justify-between items-center pb-2">
                      <p className="font-medium">Default scorecards</p>
                      {defaultArchivableScorecards?.length > 0 &&
                        canAccess(AppPermissions.MANAGE_SCORECARDS) && (
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleOpenArchiveAllScorecards()}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Archive unused (
                            {defaultArchivableScorecards?.length})
                          </Button>
                        )}
                    </div>
                    <p className="text-muted-foreground mt-0.5">
                      These scorecards will be auto-selected for new agents with
                      this type.
                    </p>
                    {defaultScorecards.map(renderScorecardCard)}
                  </div>
                )}
                {!!nonDefaultScorecards.length && (
                  <div className="py-4 px-4 border-b">
                    <div className="flex flex-row justify-between items-center">
                      <p className="font-medium">Other scorecards</p>
                      {nonDefaultArchivableScorecards?.length > 0 &&
                        canAccess(AppPermissions.MANAGE_SCORECARDS) && (
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() =>
                              handleOpenArchiveNonDefaultScorecards()
                            }
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Archive unused (
                            {nonDefaultArchivableScorecards?.length})
                          </Button>
                        )}
                    </div>
                    {nonDefaultScorecards.map(renderScorecardCard)}
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1 border-l flex flex-col">
              {!selectedScorecardDto && (
                <>
                  <div className="flex-1 px-8 flex flex-col justify-center">
                    <p className="font-medium text-center">
                      Select a scorecard on the left to preview
                    </p>
                    <p className="text-center">
                      Choose a scorecard from the left to view and edit its
                      details and criteria.
                    </p>
                  </div>
                  <div className="flex-1" />
                </>
              )}
              {!!selectedScorecardDto && (
                <div className="flex flex-col p-4 h-full w-full">
                  {canAccess(AppPermissions.MANAGE_SCORECARDS) && (
                    <Tabs
                      value={activeTab}
                      onValueChange={(v) =>
                        setActiveTab(
                          v as
                            | 'detail-view'
                            | 'rep-view'
                            | 'learning-materials',
                        )
                      }
                    >
                      <TabsList>
                        <TabsTrigger value="detail-view">
                          Detail View
                        </TabsTrigger>
                        <TabsTrigger value="rep-view">Rep View</TabsTrigger>
                        {canAccessScorecardsMaterials && (
                          <TabsTrigger value="learning-materials">
                            <BookMarked className="mr-1 w-4 h-4" />
                            Learning Materials
                          </TabsTrigger>
                        )}
                      </TabsList>
                    </Tabs>
                  )}
                  <Card className="mt-4 flex-1 relative">
                    {activeTab === 'learning-materials' ? (
                      <LearningMaterials
                        selectedScorecardId={selectedScorecardId}
                        hasAccess={canAccess(AppPermissions.MANAGE_SCORECARDS)}
                        selectedScorecardInfo={selectedScorecardDto}
                      />
                    ) : (
                      <CardContent
                        className="absolute top-0 left-0 right-0 bottom-0 px-3 py-4 flex flex-col"
                        noOverflow
                      >
                        {(selectedScorecardDto.status ===
                          ScorecardConfigStatus.DRAFT ||
                          (selectedScorecardDto.drafts &&
                            selectedScorecardDto.drafts?.length > 0)) && (
                          <div className="-mx-1 mb-3">
                            <Tabs
                              value={versionTab}
                              onValueChange={(v) =>
                                setVersionTab(v as 'published' | 'draft')
                              }
                            >
                              <TabsList>
                                <TabsTrigger
                                  value="published"
                                  disabled={
                                    selectedScorecardDto.status ===
                                    ScorecardConfigStatus.DRAFT
                                  }
                                >
                                  Published
                                </TabsTrigger>
                                <TabsTrigger value="draft">Draft</TabsTrigger>
                              </TabsList>
                            </Tabs>
                          </div>
                        )}
                        <div className="flex flex-row justify-between">
                          <div className=" flex flex-col pb-2">
                            <p className="flex items-center">
                              <span className="font-medium">
                                {selectedScorecardInfo?.tag}
                              </span>
                            </p>
                            <p className="text-muted-foreground mt-2">
                              Updated{' '}
                              {timeAgo(
                                selectedScorecardInfo?.updatedAt
                                  ? new Date(selectedScorecardInfo?.updatedAt)
                                  : new Date(),
                              )}
                            </p>
                          </div>
                          <div className="flex flex-row space-x-2">
                            {canCreateSubOrgs &&
                              !!selectedScorecardDto &&
                              canAccess(AppPermissions.MANAGE_SCORECARDS) && (
                                <Button
                                  variant={'outline'}
                                  onClick={() => {
                                    setShareWithOrgPnl(true);
                                  }}
                                  className="mr-2"
                                >
                                  <Share2 size={16} className="mr-2" />
                                  Share with organization
                                </Button>
                              )}
                            {!!selectedScorecardDto?.editable &&
                              canAccess(AppPermissions.MANAGE_SCORECARDS) && (
                                <Button
                                  variant={'outline'}
                                  onClick={() => {
                                    onEditScorecardPress(
                                      selectedScorecardDto.id || 0,
                                    );
                                  }}
                                >
                                  <Pencil size={16} className="mr-2" />
                                  Edit
                                </Button>
                              )}
                            {canAccess(AppPermissions.MANAGE_SCORECARDS) &&
                              versionTab === 'draft' &&
                              selectedScorecardInfo &&
                              (selectedScorecardInfo.status ===
                                ScorecardConfigStatus.DRAFT ||
                                !!selectedScorecardDto?.drafts?.length) && (
                                <Button
                                  onClick={() =>
                                    onPublish({
                                      ...selectedScorecardInfo,
                                      id: selectedScorecardId,
                                    })
                                  }
                                  disabled={isPublishing}
                                >
                                  {isPublishing ? (
                                    <>
                                      <Image
                                        src={`/images/90-ring-with-bg.svg`}
                                        alt="Hyperbound logo"
                                        width={16}
                                        height={16}
                                        className="mr-2"
                                        priority
                                      />
                                      Publishing...
                                    </>
                                  ) : (
                                    <>
                                      <Send size={16} className="mr-2" />
                                      Publish
                                    </>
                                  )}
                                </Button>
                              )}
                            {selectedScorecardDto?.archivable &&
                              canAccess(AppPermissions.MANAGE_SCORECARDS) && (
                                <Button
                                  variant={'destructive'}
                                  onClick={() => {
                                    if (selectedScorecardDto?.id) {
                                      handleOpenArchiveSingleScorecard();
                                    }
                                  }}
                                >
                                  <Trash2 size={16} className="mr-2" />
                                  Archive
                                </Button>
                              )}
                          </div>
                        </div>
                        <div className="flex-1 overflow-y-auto">
                          <p className="mt-1">
                            {selectedScorecardInfo?.callTypes?.length ? (
                              selectedScorecardInfo?.callTypes.map((c) => {
                                return (
                                  <CallTypeBadge
                                    key={c.callType}
                                    className="mt-2 mr-1"
                                    callType={c.callType as AgentCallType}
                                    showDefaultLabel={c.isDefaultForCallType}
                                  />
                                );
                              })
                            ) : (
                              <Badge
                                variant={'secondary'}
                                className="mt-2 mr-1 font-normal"
                              >
                                All
                              </Badge>
                            )}
                          </p>
                          {activeTab === 'detail-view' &&
                            selectedScorecardInfo && (
                              <ScorecardPreviewDetailView
                                scorecard={selectedScorecardInfo}
                                learningMaterials={learningMaterialsDb}
                                refetchLearningMaterials={refetchLearningMaterials}
                                onAdjustLearningMaterialsForCriterion={onAdjustLearningMaterialsForCriterion}
                              />
                            )}
                          {activeTab === 'rep-view' &&
                            selectedScorecardInfo && (
                              <ScorecardPreviewRepView
                                scorecard={selectedScorecardInfo}
                                learningMaterials={learningMaterialsDb}
                                showLearningMaterials
                              />
                            )}
                        </div>
                      </CardContent>
                    )}
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
        {selectedScorecardDto && (
          <ShareWithOrganizationDialog
            open={shareWithOrgPnl}
            onClose={() => {
              setShareWithOrgPnl(false);
            }}
            scorecard={selectedScorecardDto}
          />
        )}

        <DeleteConfirmationModal
          open={archiveNonDefaultScorecardsConfirmation}
          onCancel={handleCloseArchiveNonDefaultScorecard}
          onConfirm={() => {
            const idsToDelete = nonDefaultArchivableScorecards
              ?.filter((sc) => !!sc.id)
              .map((sc) => Number(sc.id));
            if (idsToDelete?.length) {
              handleArchiveScorecard(idsToDelete);
            }
          }}
          title={'Archive unused scorecards'}
          description={`Are you sure you want to archive all the ${nonDefaultArchivableScorecards?.length} unused scorecards?`}
          isLoading={isArchiving}
        />
        <DeleteConfirmationModal
          open={archiveDefaultScorecardsConfirmation}
          onCancel={handleCloseArchiveDefaultScorecard}
          onConfirm={() => {
            const idsToDelete = defaultArchivableScorecards
              ?.filter((sc) => !!sc.id)
              .map((sc) => Number(sc.id));
            if (idsToDelete?.length) {
              handleArchiveScorecard(idsToDelete);
            }
          }}
          title={'Archive unused scorecards'}
          description={`Are you sure you want to archive all the ${defaultArchivableScorecards?.length} unused scorecards?`}
          isLoading={isArchiving}
        />
        <DeleteConfirmationModal
          open={archiveSingleScorecardConfirmation}
          onCancel={handleCloseArchiveSingleScorecard}
          onConfirm={() => {
            if (selectedScorecardInfo?.id) {
              handleArchiveScorecard([selectedScorecardInfo.id]);
            }
          }}
          title={'Archive scorecard'}
          description={`Are you sure you want to archive ${selectedScorecardInfo?.tag} scorecard?`}
          isLoading={isArchiving}
        />
      </div>
    </>
  );
}
