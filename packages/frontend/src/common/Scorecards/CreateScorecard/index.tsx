import { useCallback, useState } from 'react';
import ManipulateScorecard from '../manipulateScorecard';
import ScorecardConfigDto, {
  ScorecardConfigInnerConfig,
} from '@/lib/ScorecardConfig/types';
import ScorecardConfigService from '@/lib/ScorecardConfig';

export const CreateScorecard = ({
  onSaved,
  onCancel,
  showSaveAsDraft = true,
}: {
  onSaved: (scorecardId: number) => unknown;
  onCancel: () => unknown;
  showSaveAsDraft?: boolean;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [scorecardId, setScorecardId] = useState<number | null>(null);

  const checkCanSave = useCallback((dto: Partial<ScorecardConfigDto>) => {
    if (!dto.tag) {
      return {
        canSave: false,
        reason: 'You need to enter a scorecard name',
      };
    }
    if (!dto.callTypes?.length) {
      return {
        canSave: false,
        reason: 'Call types should be selected',
      };
    }
    const configParsed = dto.config as ScorecardConfigInnerConfig;

    if (!configParsed || !Object.keys(configParsed).length) {
      return {
        canSave: false,
        reason: 'You should specify a config to save',
      };
    }
    if (!Object.keys(configParsed).every((sectionName) => !!sectionName)) {
      return {
        canSave: false,
        reason: 'Every section should have a name',
      };
    }
    if (
      !Object.values(configParsed).every((criterias) => {
        return (
          criterias.length > 0 &&
          criterias.every(
            (c) => !!c.type && !!c.corpus && !!c.query && !!c.criterion,
          )
        );
      })
    ) {
      return {
        canSave: false,
        reason: 'All added fields in the scorecard should be non-empty',
      };
    }
    return {
      canSave: true,
    };
  }, []);

  const onCancelInternal = () => {
    onCancel();
  };

  const onSaveInternal = async (
    dto: ScorecardConfigDto,
    isAutoSave = false,
  ) => {
    setIsLoading(true);
    const resDto = !scorecardId
      ? await ScorecardConfigService.CreateNew(
          dto.config,
          dto.tag,
          true,
          dto.stats,
          dto.callTypes,
        )
      : await ScorecardConfigService.Update(
          scorecardId,
          dto.config,
          dto.tag,
          dto.stats,
          dto.callTypes,
        );

    setScorecardId(resDto.id || 0);
    setIsLoading(false);

    if (!isAutoSave) {
      onSaved(resDto.id || 0);
    }
  };

  const onPublishInternal = async (dto: ScorecardConfigDto) => {
    if (scorecardId) {
      setIsPublishing(true);
      setIsLoading(true);
      const resDto = await ScorecardConfigService.Update(
        scorecardId,
        dto.config,
        dto.tag,
        dto.stats,
        dto.callTypes,
        true,
      );
      setScorecardId(resDto.id || 0);
      setIsPublishing(false);
      setIsLoading(false);
      onSaved(resDto.id || 0);
    }
  };

  return (
    <ManipulateScorecard
      title="Create"
      onCancel={onCancelInternal}
      checkCanSave={checkCanSave}
      onSave={onSaveInternal}
      onPublish={onPublishInternal}
      isPublishAvailable={!!scorecardId}
      isLoading={isLoading}
      isPublishing={isPublishing}
      showScorecardExamples
      showSaveAsDraft={showSaveAsDraft}
    />
  );
};
