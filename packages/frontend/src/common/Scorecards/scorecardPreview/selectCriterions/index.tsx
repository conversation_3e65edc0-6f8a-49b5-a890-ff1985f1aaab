import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { CircleUserRound, Plus, SparklesIcon } from 'lucide-react';
import Checkbox from '@/components/ui/Hyperbound/checkbox-with-label';
import React, { useEffect, useState, useImperativeHandle } from 'react';
import ScorecardConfigService from '@/lib/ScorecardConfig';

interface IProps {
  scorecard: ScorecardConfigDto;
  current: SelectedCriterion[];
  onChange: (c: SelectedCriterion[]) => void;
  hideQuery?: boolean;
  hideSelectAll?: boolean;
  titleComponent?: React.ReactNode;
}

export interface SelectedCriterion {
  section: string;
  criterion: string;
}

export type ScorecardSelectCriterionsRef = {
  toggleAll: () => void;
};

const ScorecardSelectCriterions = React.forwardRef<
  ScorecardSelectCriterionsRef,
  IProps
>(
  (
    {
      scorecard,
      current,
      onChange,
      hideQuery,
      titleComponent,
      hideSelectAll,
    }: IProps,
    ref,
  ) => {
    const cards: {
      sectionTitle: string;
      criteria: {
        type: string;
        criterion: string;
        query: string | string[];
        corpus?: 'rep' | 'prospect' | 'both';
      }[];
    }[] = [];

    if (scorecard?.config) {
      const scorecardKeysSorted =
        ScorecardConfigService.getSortedKeys(scorecard);
      scorecardKeysSorted.forEach((sectionTitle: string) => {
        const obj = scorecard.config[sectionTitle];
        cards.push({
          sectionTitle,
          criteria: obj,
        });
      });
    }
    const [selected, setSelected] = useState<{
      [section: string]: {
        [criterion: string]: boolean;
      };
    }>({});

    useEffect(() => {
      const tmp: {
        [section: string]: {
          [criterion: string]: boolean;
        };
      } = {};
      for (const s of current) {
        if (!tmp[s.section]) {
          tmp[s.section] = {};
        }

        if (!tmp[s.section][s.criterion]) {
          tmp[s.section][s.criterion] = false;
        }

        tmp[s.section][s.criterion] = true;
      }
      setSelected(tmp);
    }, [current]);

    useImperativeHandle(ref, () => ({
      toggleAll: () => {
        toggleAll();
      },
    }));

    const getDisplayNameOfCriteriaType = (criteriaType: string) => {
      if (criteriaType.includes('gpt')) {
        return 'AI Grading';
      }
      const commonDispNamesOfCriteria: {
        [key: string]: string;
      } = {
        find: 'Keyword Matching',
        speak_time: 'Speak Time',
        yes_no: 'Yes/No',
        or: 'Or',
        and: 'And',
      };
      return commonDispNamesOfCriteria[criteriaType] || '';
    };

    const toggleCriterion = (s: string, c: string) => {
      let add = true;
      const tmp: SelectedCriterion[] = [];
      for (const sc of current) {
        if (sc.section == s && sc.criterion == c) {
          add = false;
        } else {
          tmp.push(sc);
        }
      }
      if (add) {
        tmp.push({
          section: s,
          criterion: c,
        });
      }
      onChange(tmp);
    };

    const togglePerSection = (s: string) => {
      const tmp: SelectedCriterion[] = [];

      cards.map((scorecard) => {
        if (scorecard.sectionTitle == s) {
          let isSectionSelected = false;
          if (selected[scorecard.sectionTitle]) {
            const all = Object.keys(selected[scorecard.sectionTitle]);
            let selectedCriterions = 0;
            for (const c of all) {
              if (selected[scorecard.sectionTitle][c]) {
                selectedCriterions++;
              }
            }
            if (selectedCriterions == scorecard.criteria.length) {
              isSectionSelected = true;
            }
          }

          //toggle all off
          for (const sc of current) {
            if (sc.section != s) {
              tmp.push(sc);
            }
          }

          if (!isSectionSelected) {
            //add all
            for (const c of scorecard.criteria) {
              tmp.push({
                section: scorecard.sectionTitle,
                criterion: c.criterion,
              });
            }
          }
        }
      });

      onChange(tmp);
    };

    const toggleAll = () => {
      const tmp: SelectedCriterion[] = [];

      let allSelected = true;

      cards.map((scorecard) => {
        let isSectionSelected = false;
        if (selected[scorecard.sectionTitle]) {
          const all = Object.keys(selected[scorecard.sectionTitle]);
          let selectedCriterions = 0;
          for (const c of all) {
            if (selected[scorecard.sectionTitle][c]) {
              selectedCriterions++;
            }
          }
          if (selectedCriterions == scorecard.criteria.length) {
            isSectionSelected = true;
          }
        }

        if (!isSectionSelected) {
          allSelected = false;
        }
      });

      if (!allSelected) {
        cards.map((scorecard) => {
          for (const c of scorecard.criteria) {
            tmp.push({
              section: scorecard.sectionTitle,
              criterion: c.criterion,
            });
          }
        });
      }

      onChange(tmp);
    };

    return (
      <div className="">
        <div className="flex items-end">
          {titleComponent && titleComponent}
          <div className="flex-1" />
          {!hideSelectAll && (
            <div
              className="text-xs rounded-lg hover:bg-gray-50 cursor-pointer p-2 mb-2 flex items-center"
              onClick={toggleAll}
            >
              <Plus size={12} className="mr-1" /> Toggle all
            </div>
          )}
        </div>
        <div className="grid grid-cols-2 gap-3  ">
          {cards.map((scorecard, i: number) => {
            let isSectionSelected = false;
            let selectedCriterions = 0;
            if (selected[scorecard.sectionTitle]) {
              const all = Object.keys(selected[scorecard.sectionTitle]);
              for (const c of all) {
                if (selected[scorecard.sectionTitle][c]) {
                  selectedCriterions++;
                }
              }
              if (selectedCriterions == scorecard.criteria.length) {
                isSectionSelected = true;
              }
            }

            return (
              <motion.div
                key={scorecard.sectionTitle}
                initial={{ opacity: 0, y: -50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ y: -50 }}
                transition={{
                  duration: 0,
                  delay: i * 0,
                }}
              >
                <Card className="h-full">
                  <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
                    <CardTitle className="flex items-center">
                      <div className="">
                        <Checkbox
                          checked={isSectionSelected}
                          onToggle={() => {
                            togglePerSection(scorecard.sectionTitle);
                          }}
                        >
                          {' '}
                        </Checkbox>
                      </div>
                      <p
                        className="font-semibold flex-1 cursor-pointer"
                        onClick={() => {
                          togglePerSection(scorecard.sectionTitle);
                        }}
                      >
                        {scorecard.sectionTitle}
                      </p>
                      <Badge variant={'secondary'}>
                        {selectedCriterions}
                        {' / '}
                        {scorecard.criteria.length}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    {scorecard.criteria.map((c, idx) => {
                      let isSelected = false;
                      if (selected[scorecard.sectionTitle]) {
                        if (selected[scorecard.sectionTitle][c.criterion]) {
                          isSelected = true;
                        }
                      }

                      return (
                        <div
                          key={idx}
                          className={cn('', {
                            'border-t': idx !== 0,
                          })}
                        >
                          <div
                            className={cn(
                              'flex flex-row space-x-2 p-2  m-1 rounded-lg hover:bg-gray-50 cursor-pointer ',
                            )}
                            onClick={() => {
                              toggleCriterion(
                                scorecard.sectionTitle,
                                c.criterion,
                              );
                            }}
                          >
                            <div className="mt-1  ">
                              <Checkbox
                                checked={isSelected}
                                onToggle={() => {}}
                              >
                                {' '}
                              </Checkbox>
                            </div>
                            <div className="flex flex-col ">
                              <p className="font-medium">{c.criterion}</p>
                              {!hideQuery && (
                                <p className="text-muted-foreground mt-2">
                                  {typeof c.query === 'string'
                                    ? c.query
                                    : c.query.join(', ')}
                                </p>
                              )}
                              <div className="flex flex-row space-x-2 text-muted-foreground mt-2">
                                <Badge
                                  variant="secondary"
                                  className="rounded-full font-medium text-muted-foreground"
                                >
                                  {getDisplayNameOfCriteriaType(c.type)}
                                </Badge>
                                {['gpt', 'egpt', 'find'].includes(c.type) && (
                                  <div className="h-full flex flex-row py-1">
                                    <div className=" border-l " />
                                  </div>
                                )}
                                {c.type === 'egpt' && (
                                  <Badge
                                    variant="secondary"
                                    className="relative pl-5 bg-teal-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
                                  >
                                    <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-teal-500" />
                                    <span className="text-teal-500">
                                      AI Coaching Enabled
                                    </span>
                                  </Badge>
                                )}
                                {c.type === 'gpt' && (
                                  <Badge
                                    variant="secondary"
                                    className="relative pl-5 bg-red-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
                                  >
                                    <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-red-500" />{' '}
                                    <span className="text-red-500">
                                      AI Coaching Disabled
                                    </span>
                                  </Badge>
                                )}
                                {c.type === 'find' && (
                                  <Badge
                                    variant="secondary"
                                    className="relative pl-6 rounded-full font-medium text-muted-foreground capitalize"
                                  >
                                    <CircleUserRound className="absolute left-0 ml-2 w-3 h-3" />
                                    {c.corpus}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    );
  },
);

ScorecardSelectCriterions.displayName = 'ScorecardSelectCriterions';
export default ScorecardSelectCriterions;
