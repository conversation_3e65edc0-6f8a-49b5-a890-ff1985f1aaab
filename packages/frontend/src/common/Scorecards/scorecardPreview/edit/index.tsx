import DeleteConfirmationModal from '@/components/ConfirmationModal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import {
  Check,
  CircleUserRound,
  GripVertical,
  Pencil,
  PencilOff,
  Plus,
  SparklesIcon,
  Trash2,
} from 'lucide-react';
import { Dispatch, MouseEvent, SetStateAction, useRef, useState } from 'react';
import {
  CardTyp,
  checkIfCard,
  Criteria,
  DraggedObjType,
  TargetObjType,
} from '../../manipulateScorecard';
import ScorecardEditCriteria from './editCriteria';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { TooltipArrow } from '@radix-ui/react-tooltip';

interface IProps {
  currentDraggedObject?: DraggedObjType;
  setCurrentDraggedObject: Dispatch<SetStateAction<DraggedObjType | undefined>>;
  onMouseDown: (movingObj: DraggedObjType, e: MouseEvent) => unknown;
  onMouseEnter: (hoverObj: TargetObjType) => unknown;
  onMouseLeave: () => unknown;
  cards: CardTyp[];
  setCards: Dispatch<SetStateAction<CardTyp[]>>;
  isDNDActive: boolean;
}

export default function ScorecardPreviewEdit({
  currentDraggedObject,
  setCurrentDraggedObject,
  onMouseDown,
  onMouseEnter,
  onMouseLeave,
  cards,
  setCards,
  isDNDActive,
}: IProps) {
  const getDisplayNameOfCriteriaType = (criteriaType: string) => {
    if (criteriaType.includes('gpt')) {
      return 'AI Grading';
    }
    const commonDispNamesOfCriteria: {
      [key: string]: string;
    } = {
      find: 'Keyword Matching',
      speak_time: 'Speak Time',
      yes_no: 'Yes/No',
      or: 'OR',
      and: 'AND',
    };
    return commonDispNamesOfCriteria[criteriaType] || '';
  };

  const [newSectionNumber, setNewSectionNumber] = useState(0);
  const finalCardRef = useRef<HTMLDivElement>(null);
  const addNewSection = () => {
    setCards([
      ...cards,
      {
        tempUuid: crypto.randomUUID(),
        sectionTitle: `New Section ${
          newSectionNumber ? newSectionNumber + 1 : ''
        }`.trimEnd(),
        criteria: [],
      },
    ]);
    setNewSectionNumber((n) => n + 1);
    if (finalCardRef.current) {
      finalCardRef.current.scrollIntoView({
        block: 'start',
        behavior: 'smooth',
      });
    }
  };

  const editSectionTitle = (sectionIdx: number, newTitle: string) => {
    cards[sectionIdx].sectionTitle = newTitle;
    setCards([...cards]);
  };

  const [objToDelete, setObjToDelete] = useState<{
    cardIdx: number;
    criteriaIdx?: number;
  } | null>(null);

  const deleteCard = (cardIdx: number) => {
    cards.splice(cardIdx, 1);
    setCards([...cards]);
  };

  const deleteCriteria = (cardIdx: number, criteriaIdx: number) => {
    cards[cardIdx].criteria.splice(criteriaIdx, 1);
    setCards([...cards]);
  };

  const onDeleteButtonPress = (cardIdx: number, criteriaIdx?: number) => {
    setObjToDelete({
      cardIdx,
      criteriaIdx,
    });
  };

  const onCancelDelete = () => {
    setObjToDelete(null);
  };

  const onConfirmDelete = () => {
    if (!objToDelete) {
      return;
    }
    setObjToDelete(null);
    if (typeof objToDelete.criteriaIdx === 'number') {
      deleteCriteria(objToDelete.cardIdx, objToDelete.criteriaIdx);
    } else {
      deleteCard(objToDelete.cardIdx);
    }
  };

  const [isNewCriteria, setIsNewCriteria] = useState(false);
  const [editCriteriaSheetOpen, setEditCriteriaSheetOpen] = useState(false);
  const [editCriteriaIndices, setEditCriteriaIndices] = useState<{
    sectionIdx: number;
    criteriaIdx?: number;
  }>({
    sectionIdx: 0,
  });
  const [criteriaToEdit, setCriteriaToEdit] = useState<Partial<Criteria>>({});

  const onSaveEditCriteria = (newCriteria: Criteria) => {
    setCriteriaToEdit({});
    if (isNewCriteria) {
      newCriteria.tempUuid = crypto.randomUUID();
      cards[editCriteriaIndices.sectionIdx].criteria.push(newCriteria);
    } else {
      cards[editCriteriaIndices.sectionIdx].criteria[
        editCriteriaIndices.criteriaIdx || 0
      ] = newCriteria;
    }
    setCards([...cards]);
  };

  const addCriterionBtn = (idx: number) => (
    <Button
      className="flex h-[36px] ml-2 bg-white"
      variant="outline"
      onClick={() => {
        setEditCriteriaIndices({
          sectionIdx: idx,
        });
        setIsNewCriteria(true);
        setCriteriaToEdit({});
        setEditCriteriaSheetOpen(true);
      }}
    >
      <Plus className="w-4 h-4 mr-2" />
      Add criterion
    </Button>
  );

  const renderOrAndQueryTypes = (config: Criteria) => (
    <div className="flex flex-col flex-1 ml-1.5 mt-4">
      <div className="flex flex-row">
        <p className="font-medium">{config.criterion}</p>
      </div>
      <p className="text-muted-foreground mt-2">
        {typeof config.query === 'string'
          ? config.query
          : config.query.join(', ')}
      </p>
      <div className="flex flex-row space-x-2 text-muted-foreground mt-2">
        <Badge
          variant="secondary"
          className="rounded-full font-medium text-muted-foreground"
        >
          {getDisplayNameOfCriteriaType(config.type)}
        </Badge>
        {['gpt', 'egpt', 'find'].includes(config.type) && (
          <div className="h-full flex flex-row py-1">
            <div className=" border-l " />
          </div>
        )}
        {config.type === 'egpt' && (
          <Badge
            variant="secondary"
            className="relative pl-5 bg-teal-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
          >
            <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-teal-500" />
            <span className="text-teal-500">AI Coaching Enabled</span>
          </Badge>
        )}
        {config.type === 'gpt' && (
          <Badge
            variant="secondary"
            className="relative pl-5 bg-red-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
          >
            <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-red-500" />
            <span className="text-red-500">AI Coaching Disabled</span>
          </Badge>
        )}
        {config.type === 'find' && (
          <Badge
            variant="secondary"
            className="relative pl-6 rounded-full font-medium text-muted-foreground capitalize"
          >
            <CircleUserRound className="absolute left-0 ml-2 w-3 h-3" />
            {config.corpus}
          </Badge>
        )}
      </div>
    </div>
  );

  const onEditCriteriaClick = (
    e: MouseEvent,
    criteria: Criteria,
    criteriaIdx: number,
    sectionIdx: number,
  ) => {
    if (['or', 'and'].includes(criteria.type)) {
      e.stopPropagation();
      window.location.href = 'mailto:<EMAIL>';
    } else {
      setEditCriteriaIndices({
        sectionIdx,
        criteriaIdx,
      });
      setIsNewCriteria(false);
      setCriteriaToEdit(criteria);
      setEditCriteriaSheetOpen(true);
    }
  };

  const isDraggedObjectCard = checkIfCard(currentDraggedObject);

  return (
    <div className={cn('flex flex-col')}>
      <div className="flex flex-row justify-between">
        <div className="flex flex-col mr-4">
          <p className="font-medium">Sections</p>
          <p className="text-muted-foreground mt-2">
            A sales scorecard evaluates a salesperson&apos;s performance using
            specific criteria to measure key activities and outcomes.
          </p>
        </div>
        <Button variant="outline" onClick={addNewSection}>
          <Plus className="w-4 h-4 mr-2" />
          Create New
        </Button>
      </div>
      {cards.map((scorecard, cardIdx: number) => {
        return (
          <motion.div
            key={scorecard.tempUuid}
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ y: -50 }}
            transition={{
              duration: 0,
              delay: cardIdx * 0,
            }}
          >
            <div
              className={cn('w-full h-4 rounded-sm', {
                'bg-secondary hover:bg-gray-200':
                  isDNDActive && isDraggedObjectCard,
              })}
              onMouseEnter={() => {
                onMouseEnter({ ...scorecard, isAfter: false });
              }}
              onMouseLeave={onMouseLeave}
            />
            <Card className="p-0">
              <CardHeader
                className={cn('rounded-t-xl pl-2 pr-3 py-2 bg-gray-50', {
                  'border-b': !scorecard.criteria.length,
                })}
              >
                <div className="flex flex-row items-center">
                  <GripVertical
                    className="h-5 text-muted-foreground cursor-grab"
                    onMouseDown={(e) => {
                      setCurrentDraggedObject(scorecard);
                      onMouseDown(scorecard, e);
                    }}
                  />
                  <Input
                    placeholder="Name"
                    value={scorecard.sectionTitle}
                    onChange={(e) => {
                      editSectionTitle(cardIdx, e.target.value);
                    }}
                    className="flex-1 bg-white ml-1 "
                  />
                  {!!scorecard.criteria.length && (
                    <Badge variant={'secondary'} className="flex ml-2 h-[36px]">
                      {scorecard.criteria.length}
                      {' / '}
                      {scorecard.criteria.length}
                    </Badge>
                  )}
                  {!!scorecard.criteria.length && addCriterionBtn(cardIdx)}
                  <div
                    className="cursor-pointer ml-3"
                    onClick={() => {
                      onDeleteButtonPress(cardIdx);
                    }}
                  >
                    <Trash2 className="w-4 h-4 text-muted-foreground" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0 relative">
                {scorecard.criteria.map((c, criteriaIdx) => (
                  <div key={c.tempUuid} className="flex flex-col w-full">
                    <div
                      className={cn(
                        'w-full h-3 rounded-sm flex flex-col justify',
                        {
                          'bg-secondary hover:bg-gray-200':
                            isDNDActive && !isDraggedObjectCard,
                        },
                      )}
                      onMouseEnter={() => {
                        onMouseEnter({ ...c, isAfter: false });
                      }}
                      onMouseLeave={onMouseLeave}
                    >
                      {!(isDNDActive && !isDraggedObjectCard) && (
                        <div className="border-t w-full"></div>
                      )}
                    </div>
                    <div className="p-3 pt-0 pl-2 flex flex-row w-full">
                      <GripVertical
                        className="h-5 text-muted-foreground cursor-grab"
                        onMouseDown={(e) => {
                          setCurrentDraggedObject(c);
                          onMouseDown(c, e);
                        }}
                      />
                      <div className="mt-1 ml-1 bg-green-600 self-start h-[12px] w-[12px] rounded-[2px]">
                        <Check className="text-white" size={12} />
                      </div>
                      <div className="flex flex-col flex-1 ml-1.5">
                        <div className="flex flex-row">
                          <p className="font-medium">{c.criterion}</p>
                          <div className="flex-1" />

                          {!['or', 'and'].includes(c.type) ? (
                            <div
                              className="rounded-lg hover:bg-gray-50 cursor-pointer p-2"
                              onClick={(e) =>
                                onEditCriteriaClick(e, c, criteriaIdx, cardIdx)
                              }
                            >
                              <Pencil
                                size={16}
                                className="text-muted-foreground"
                              />
                            </div>
                          ) : (
                            <TooltipProvider delayDuration={50}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span tabIndex={0}>
                                    <div className="rounded-lg opacity-50 p-2">
                                      <PencilOff
                                        size={16}
                                        className="text-muted-foreground"
                                      />
                                    </div>
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent side="top">
                                  <TooltipArrow />
                                  <p>Contact Hyperbound to edit this section</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}

                          <div
                            className="rounded-lg hover:bg-gray-50 cursor-pointer p-2"
                            onClick={() => {
                              onDeleteButtonPress(cardIdx, criteriaIdx);
                            }}
                          >
                            <Trash2
                              size={16}
                              className="text-muted-foreground"
                            />
                          </div>
                        </div>
                        {typeof c.query !== 'string' &&
                        typeof c.query[0] === 'object' ? (
                          <div className="mb-4">
                            {c.query.map((config) =>
                              renderOrAndQueryTypes(config as Criteria),
                            )}
                          </div>
                        ) : (
                          <p className="text-muted-foreground mt-2">
                            {typeof c.query === 'string'
                              ? c.query
                              : c.query.join(', ')}
                          </p>
                        )}

                        <div className="flex flex-row space-x-2 text-muted-foreground mt-2">
                          <Badge
                            variant="secondary"
                            className="rounded-full font-medium text-muted-foreground"
                          >
                            {getDisplayNameOfCriteriaType(c.type)}
                          </Badge>
                          {['gpt', 'egpt', 'find'].includes(c.type) && (
                            <div className="h-full flex flex-row py-1">
                              <div className=" border-l " />
                            </div>
                          )}
                          {c.type === 'egpt' && (
                            <Badge
                              variant="secondary"
                              className="relative pl-5 bg-teal-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
                            >
                              <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-teal-500" />
                              <span className="text-teal-500">
                                AI Coaching Enabled
                              </span>
                            </Badge>
                          )}
                          {c.type === 'gpt' && (
                            <Badge
                              variant="secondary"
                              className="relative pl-5 bg-red-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
                            >
                              <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-red-500" />
                              <span className="text-red-500">
                                AI Coaching Disabled
                              </span>
                            </Badge>
                          )}
                          {c.type === 'find' && (
                            <Badge
                              variant="secondary"
                              className="relative pl-6 rounded-full font-medium text-muted-foreground capitalize"
                            >
                              <CircleUserRound className="absolute left-0 ml-2 w-3 h-3" />
                              {c.corpus}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    {criteriaIdx === scorecard.criteria.length - 1 &&
                      isDNDActive &&
                      !isDraggedObjectCard && (
                        <div
                          className={cn(
                            'absolute bottom-0 w-full h-3 rounded-sm flex flex-col justify-center',
                            {
                              'bg-secondary hover:bg-gray-200':
                                isDNDActive && !isDraggedObjectCard,
                            },
                          )}
                          onMouseEnter={() => {
                            onMouseEnter({ ...c, isAfter: true });
                          }}
                          onMouseLeave={onMouseLeave}
                        />
                      )}
                  </div>
                ))}
                {!scorecard.criteria.length && (
                  <>
                    {isDNDActive && !isDraggedObjectCard && (
                      <div
                        className={cn(
                          'w-full h-full absolute rounded-b-xl flex flex-col justify',
                          {
                            'bg-secondary hover:bg-gray-200':
                              isDNDActive && !isDraggedObjectCard,
                          },
                        )}
                        onMouseEnter={() => {
                          onMouseEnter({ ...scorecard, isAfter: false });
                        }}
                        onMouseLeave={onMouseLeave}
                      />
                    )}
                    {
                      <div className="flex flex-row justify-center py-6 z-10">
                        {addCriterionBtn(cardIdx)}
                      </div>
                    }
                  </>
                )}
              </CardContent>
            </Card>
            {cardIdx === cards.length - 1 ? (
              <div
                className={cn('w-full h-4 rounded-sm', {
                  'bg-secondary hover:bg-gray-200':
                    isDNDActive && isDraggedObjectCard,
                })}
                onMouseEnter={() => {
                  onMouseEnter({ ...scorecard, isAfter: true });
                }}
                onMouseLeave={onMouseLeave}
              />
            ) : (
              <></>
            )}
          </motion.div>
        );
      })}

      <Card
        ref={finalCardRef}
        className={cn('bg-[#FBFBFB] border-dashed')}
        onMouseEnter={() => {
          onMouseEnter({
            tempUuid: '',
            sectionTitle: '',
            criteria: [],
            isNewSection: true,
            isAfter: true,
          });
        }}
        onMouseLeave={onMouseLeave}
      >
        <CardContent
          className={cn('py-8 relative', {
            'hover:bg-gray-200': isDNDActive && isDraggedObjectCard,
          })}
        >
          {isDNDActive && isDraggedObjectCard && (
            <div className="absolute left-0 right-0 top-0 bottom-0 flex flex-col justify-center items-center">
              <p className="text-lg font-medium text-center">
                Drop here to create a new section
              </p>
            </div>
          )}
          <div
            className={cn('flex flex-col items-center', {
              'opacity-0': isDNDActive && isDraggedObjectCard,
            })}
          >
            <p className="text-lg font-medium">Create a new section</p>
            <p className="mt-3 text-muted-foreground">
              Choose to create a new section
            </p>
            <Button className="mt-6" onClick={addNewSection}>
              <Plus className="w-4 h-4 mr-2" />
              Create New
            </Button>
          </div>
        </CardContent>
      </Card>

      <ScorecardEditCriteria
        title={isNewCriteria ? 'Create a new criterion' : 'Edit criterion'}
        initialCriteria={criteriaToEdit}
        onSave={onSaveEditCriteria}
        open={editCriteriaSheetOpen}
        setOpen={setEditCriteriaSheetOpen}
      />
      <DeleteConfirmationModal
        title={
          !objToDelete
            ? ''
            : typeof objToDelete?.criteriaIdx === 'number'
              ? `Are you sure you want to delete the criterion "${
                  cards[objToDelete.cardIdx].criteria[objToDelete.criteriaIdx]
                    .criterion
                }" inside section "${cards[objToDelete.cardIdx].sectionTitle}"?`
              : `Are you sure you want to delete the section "${
                  cards[objToDelete.cardIdx].sectionTitle
                }"?`
        }
        description="This action cannot be undone."
        confirmLabel="Delete"
        cancelLabel="Cancel"
        open={!!objToDelete}
        onCancel={onCancelDelete}
        onConfirm={onConfirmDelete}
      />
    </div>
  );
}
