import { Criteria } from '@/common/Scorecards/manipulateScorecard';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import MultiStringsInput from '@/components/ui/Hyperbound/multiStringsInput';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Sheet, SheetContentLight } from '@/components/ui/sheet';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useEffect, useMemo, useState } from 'react';

interface IProps {
  title: string;
  open: boolean;
  setOpen: (open: boolean) => unknown;
  initialCriteria: Partial<Criteria>;
  onSave: (criteria: Criteria) => unknown;
}

export default function ScorecardEditCriteria({
  title,
  initialCriteria,
  open,
  setOpen,
  onSave,
}: IProps) {
  const [criteria, setCriteria] = useState<Partial<Criteria>>(initialCriteria);

  const canSave = useMemo(() => {
    return (
      ['egpt', 'gpt', 'find'].includes(criteria.type || '') &&
      !!criteria.criterion?.length &&
      !!criteria.query?.length
    );
  }, [criteria]);

  const [aiCoachingCheckboxChecked, setAiCoachingCheckboxChecked] =
    useState(false);

  useEffect(() => {
    if (!criteria.type) {
      criteria.type = 'gpt';
      setCriteria({
        ...criteria,
      });
    }
    if (!criteria.corpus && !criteria.criterion && !criteria.query) {
      setAiCoachingCheckboxChecked(true);
    }
  }, [criteria]);

  const [aiGradingQuery, setAiGradingQuery] = useState('');

  const [corpus, setCorpus] = useState('');

  const getKeywordQuery = (criteria: Partial<Criteria>): string[] => {
    if (criteria.type !== 'find' || !criteria.query) {
      return [];
    }
    return typeof criteria.query === 'string'
      ? [criteria.query]
      : criteria.query.map((item) =>
          typeof item === 'string' ? item : String(item.query),
        );
  };
  const [keywordQueries, setKeywordQueries] = useState<string[]>([]);
  const [typingKeywordQuery, setTypingKeywordQuery] = useState<string>('');

  useEffect(() => {
    setCriteria(initialCriteria);
  }, [initialCriteria]);

  useEffect(() => {
    setAiCoachingCheckboxChecked(initialCriteria.type === 'egpt');
    setAiGradingQuery(
      initialCriteria.type?.includes('gpt')
        ? (initialCriteria.query as string) || ''
        : '',
    );
    setCorpus(initialCriteria.corpus || 'both');
    setKeywordQueries(getKeywordQuery(initialCriteria));
  }, [initialCriteria]);

  useEffect(() => {
    if (criteria.type?.includes('gpt')) {
      const newType = aiCoachingCheckboxChecked ? 'egpt' : 'gpt';
      if (criteria.type !== newType) {
        criteria.type = newType;
        setCriteria({
          ...criteria,
        });
      }
    }
  }, [criteria.type, aiCoachingCheckboxChecked]);

  useEffect(() => {
    if (criteria.type?.includes('gpt') && criteria.query !== aiGradingQuery) {
      criteria.query = aiGradingQuery;
      setCriteria({
        ...criteria,
      });
    }
  }, [criteria.type, aiGradingQuery]);

  useEffect(() => {
    if (
      criteria.type === 'find' &&
      JSON.stringify(criteria.query) !== JSON.stringify(keywordQueries)
    ) {
      criteria.query = keywordQueries;
      setCriteria({
        ...criteria,
      });
    }
  }, [criteria.type, keywordQueries]);

  useEffect(() => {
    const newCorpus = criteria.type === 'find' ? corpus : 'both';
    if (criteria.corpus !== newCorpus) {
      criteria.corpus = newCorpus as any;
      setCriteria({
        ...criteria,
      });
    }
  }, [criteria.type, corpus]);

  return (
    <Sheet
      open={open}
      onOpenChange={() => {
        setOpen(false);
      }}
    >
      <SheetContentLight side={'rightFull'} className="p-0 text-sm">
        <div className="flex flex-col w-[700px] h-full">
          <div className="py-3 px-4 border-b">
            <p className="font-medium">{title}</p>
          </div>
          <div className="flex-1 bg-[#FBFBFB] overflow-y-auto">
            <div className="flex flex-col px-4 py-4">
              <p>Criterion type</p>
              <Tabs
                className="mt-1"
                value={
                  !criteria.type || criteria.type.includes('gpt')
                    ? 'ai'
                    : 'keyword'
                }
                onValueChange={(value) => {
                  setCriteria({
                    ...criteria,
                    // setting to gpt alone works because the use effect takes care of switching to egpt
                    type: value === 'ai' ? 'gpt' : 'find',
                  });
                }}
              >
                <TabsList>
                  <TabsTrigger value="ai">AI Grading</TabsTrigger>
                  <TabsTrigger value="keyword">Keyword Matching</TabsTrigger>
                </TabsList>
              </Tabs>
              <Card className="mt-4 rounded-lg">
                <CardContent className="px-3 py-3">
                  <p className="font-medium">Title</p>
                  <Input
                    placeholder="A descriptive title"
                    value={criteria.criterion || ''}
                    onChange={(event) =>
                      setCriteria({
                        ...criteria,
                        criterion: event.target.value,
                      })
                    }
                    className="max-w-full mt-2"
                  />
                  {!!criteria.type?.includes('gpt') && (
                    <>
                      <p className="font-medium mt-6">How to score</p>
                      <p className="text-muted-foreground mt-1">
                        Enter a yes or no question that our AI will answer about
                        the rep&apos;s performance. Try to be as specific as
                        possible, this will help the AI grade accurately and
                        give your rep actionable feedback.
                      </p>
                      <Textarea
                        placeholder="Did the SDR...?"
                        value={aiGradingQuery}
                        onChange={(event) =>
                          setAiGradingQuery(event.target.value)
                        }
                        className="w-full mt-2 min-h-[150px]"
                      />
                      <div
                        className="flex flex-row items-start mt-4 space-x-3 cursor-pointer"
                        onClick={() => {
                          setAiCoachingCheckboxChecked((c) => !c);
                        }}
                      >
                        <Checkbox
                          className="border-[#E4E4E7] mt-0.5"
                          checked={aiCoachingCheckboxChecked}
                        />
                        <div className="flex flex-col">
                          <p className="font-medium">Enable AI Coaching</p>
                          <p className="mt-1 text-muted-foreground ">
                            AI coaching reviews if the Rep met the criterion and
                            offers personalized feedback for improvement. Please
                            note - it may take a moment to load.
                          </p>
                        </div>
                      </div>
                    </>
                  )}
                  {criteria.type === 'find' && (
                    <>
                      <p className="font-medium mt-6">Keywords</p>
                      <p className="text-muted-foreground mt-1">
                        Specify whose words to search for the keywords—options
                        are Rep, Prospect, or Both. Then, provide a list of the
                        keywords to look for.
                      </p>
                      <div className="flex flex-row mt-2 space-x-2 items-stretch">
                        <MultiStringsInput
                          className="flex-1"
                          current={keywordQueries}
                          onChange={(v) => {
                            setKeywordQueries([...v]);
                          }}
                        />
                        <div>
                          <Select
                            value={corpus}
                            onValueChange={(e) => {
                              setCorpus(e);
                            }}
                          >
                            <SelectTrigger className="h-10">
                              <SelectValue placeholder={'Select'} />
                            </SelectTrigger>
                            <SelectContent side="bottom">
                              <SelectItem value="prospect">Prospect</SelectItem>
                              <SelectItem value="rep">Rep</SelectItem>
                              <SelectItem value="both">Both</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
          <div className="py-3 px-4 border-t flex flex-row justify-start">
            <Button
              className="mr-2"
              disabled={!canSave}
              onClick={() => {
                onSave(criteria as Criteria);
                setOpen(false);
              }}
            >
              Save
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                setOpen(false);
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      </SheetContentLight>
    </Sheet>
  );
}
