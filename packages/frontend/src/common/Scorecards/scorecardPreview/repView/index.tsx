import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { cn } from '@/lib/utils';
import { Check, SparklesIcon } from 'lucide-react';

interface IProps {
  scorecard: ScorecardConfigDto;
  cardClassName?: string;
}

export default function ScorecardPreviewRepView({
  scorecard,
  cardClassName,
}: IProps) {
  const cards: {
    sectionTitle: string;
    criteria: {
      type: string;
      criterion: string;
      query: string | string[];
      corpus?: 'rep' | 'prospect' | 'both';
    }[];
  }[] = [];
  if (scorecard?.config) {
    const scorecardKeysSorted = ScorecardConfigService.getSortedKeys(scorecard);
    scorecardKeysSorted.forEach((sectionTitle: string) => {
      const obj = scorecard.config[sectionTitle];
      cards.push({
        sectionTitle,
        criteria: obj,
      });
    });
  }
  return (
    <div className="grid grid-cols-1 items-stretch gap-4 pb-4 md:grid-cols-2">
      {cards.map((card) => {
        return (
          <Card className={cn('mt-4', cardClassName)} key={card.sectionTitle}>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center">
                <p className="font-semibold">{card.sectionTitle}</p>
                <Badge variant={'secondary'}>
                  {card.criteria.length}
                  {' / '}
                  {card.criteria.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {card.criteria.map((c, idx) => (
                <div
                  key={idx}
                  className={cn('flex flex-row space-x-2 p-3', {
                    'border-t': idx !== 0,
                  })}
                >
                  <div className="w-4 h-4 bg-[#b8b8bc] rounded-sm justify-center items-center inline-flex">
                    <Check className="text-white w-4 h-4 relative flex-col justify-start items-start flex overflow-hidden" />
                  </div>
                  <div className="flex flex-col">
                    <p className="font-medium">{c.criterion}</p>
                    <div className="flex flex-row space-x-2 text-muted-foreground mt-2 text-xs">
                      {c.type === 'egpt' && (
                        <span className="relative font-medium inline-flex flex-row items-center space-x-1">
                          <SparklesIcon className="w-3 h-3 text-teal-600" />
                          <span className="text-teal-600">
                            AI Coaching Enabled
                          </span>
                        </span>
                      )}
                      {c.type === 'gpt' && (
                        <span className="relative font-medium inline-flex flex-row items-center space-x-1">
                          <SparklesIcon className="w-3 h-3 text-red-500" />{' '}
                          <span className="text-red-500">
                            AI Coaching Disabled
                          </span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
