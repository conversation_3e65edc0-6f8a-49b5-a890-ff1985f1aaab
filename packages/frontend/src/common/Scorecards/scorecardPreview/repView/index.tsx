import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto, { LearningMaterialDto, ScorecardConfigMaterialScope } from '@/lib/ScorecardConfig/types';
import { cn } from '@/lib/utils';
import { Check, SparklesIcon } from 'lucide-react';
import MaterialList from '../../LearningMaterials/MaterialList';
import { useMemo } from 'react';

interface IProps {
  scorecard: ScorecardConfigDto;
  learningMaterials?: LearningMaterialDto[];
  cardClassName?: string;
  showLearningMaterials?: boolean;
}

export default function ScorecardPreviewRepView({
  scorecard,
  learningMaterials,
  cardClassName,
  showLearningMaterials = false,
}: IProps) {
  const idToLearningMaterial = useMemo(() => {
    if (!learningMaterials) {
      return {};
    }
    const criterionLearningMaterials = learningMaterials.filter(l => l.scope === ScorecardConfigMaterialScope.CRITERION);
    return Object.fromEntries(criterionLearningMaterials.map(l => [l.id, l])) as { [key: number]: LearningMaterialDto };
  }, [learningMaterials]);
  const cards: {
    sectionTitle: string;
    criteria: {
      type: string;
      criterion: string;
      learningMaterialIds?: number[];
      query: string | string[];
      corpus?: 'rep' | 'prospect' | 'both';
    }[];
  }[] = [];
  if (scorecard?.config) {
    const scorecardKeysSorted = ScorecardConfigService.getSortedKeys(scorecard);
    scorecardKeysSorted.forEach((sectionTitle: string) => {
      const obj = scorecard.config[sectionTitle];
      cards.push({
        sectionTitle,
        criteria: obj,
      });
    });
  }
  return (
    <div className={`grid items-stretch gap-4 pb-4 ${
      showLearningMaterials ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'
    }`}>
      {cards.map((card) => {
        return (
          <Card className={cn('mt-4', cardClassName)} key={card.sectionTitle}>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center">
                <p className="font-semibold">{card.sectionTitle}</p>
                <Badge variant={'secondary'}>
                  {card.criteria.length}
                  {' / '}
                  {card.criteria.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {card.criteria.map((c, idx) => {
                const learningMaterials = c.learningMaterialIds?.map(id => idToLearningMaterial[id])?.filter(Boolean) || [];
                return (
                  <div
                    key={idx}
                    className={cn('flex flex-row space-x-2 p-3 w-full', {
                      'border-t': idx !== 0,
                    })}
                  >
                    <div className="w-4 h-4 bg-[#b8b8bc] rounded-sm justify-center items-center inline-flex">
                      <Check className="text-white w-4 h-4 relative flex-col justify-start items-start flex overflow-hidden" />
                    </div>
                    <div className="flex flex-col w-full">
                      <p className="font-medium">{c.criterion}</p>
                      <div className="flex flex-row space-x-2 text-muted-foreground mt-2 text-xs">
                        {c.type === 'egpt' && (
                          <span className="relative font-medium inline-flex flex-row items-center space-x-1">
                            <SparklesIcon className="w-3 h-3 text-teal-600" />
                            <span className="text-teal-600">
                              AI Coaching Enabled
                            </span>
                          </span>
                        )}
                        {c.type === 'gpt' && (
                          <span className="relative font-medium inline-flex flex-row items-center space-x-1">
                            <SparklesIcon className="w-3 h-3 text-red-500" />{' '}
                            <span className="text-red-500">
                              AI Coaching Disabled
                            </span>
                          </span>
                        )}
                      </div>

                      <div className="flex flex-col space-y-2 mt-2 w-full">
                        {showLearningMaterials && learningMaterials.length > 0 && (
                          <MaterialList
                            scorecardConfigId={scorecard.id}
                            materialList={learningMaterials}
                            scope={ScorecardConfigMaterialScope.CRITERION}
                            viewOnly={true}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
