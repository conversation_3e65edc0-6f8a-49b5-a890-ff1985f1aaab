import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import {
  Check,
  CircleUserRound,
  GripVertical,
  SparklesIcon,
} from 'lucide-react';
import { Dispatch, MouseEvent, SetStateAction } from 'react';
import {
  CardTyp,
  checkIfCard,
  DraggedObjType,
  TargetObjType,
} from '../../manipulateScorecard';

interface IProps {
  currentDraggedObject?: DraggedObjType;
  setCurrentDraggedObject: Dispatch<SetStateAction<DraggedObjType | undefined>>;
  onMouseDown: (movingObj: DraggedObjType, e: MouseEvent) => unknown;
  onMouseEnter: (hoverObj: TargetObjType) => unknown;
  onMouseLeave: () => unknown;
  cards: CardTyp[];
  isDNDActive: boolean;
  isExample: boolean;
}

export default function ScorecardDraggableView({
  currentDraggedObject,
  setCurrentDraggedObject,
  onMouseDown,
  onMouseEnter,
  onMouseLeave,
  cards,
  isDNDActive,
  isExample,
}: IProps) {
  const getDisplayNameOfCriteriaType = (criteriaType: string) => {
    if (criteriaType.includes('gpt')) {
      return 'AI Grading';
    }
    const commonDispNamesOfCriteria: {
      [key: string]: string;
    } = {
      find: 'Keyword Matching',
      speak_time: 'Speak Time',
      yes_no: 'Yes/No',
      or: 'Or',
      and: 'And',
    };
    return commonDispNamesOfCriteria[criteriaType] || '';
  };

  const isDraggedObjectCard = checkIfCard(currentDraggedObject);

  return (
    <div className={cn('flex flex-col')}>
      {cards.map((scorecard, cardIdx: number) => {
        return (
          <motion.div
            key={scorecard.tempUuid}
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ y: -50 }}
            transition={{
              duration: 0,
              delay: cardIdx * 0,
            }}
          >
            <div
              className={cn('w-full h-4 rounded-sm')}
              onMouseEnter={() => {
                onMouseEnter({ ...scorecard, isAfter: false });
              }}
              onMouseLeave={onMouseLeave}
            />
            <Card className="p-0">
              <CardHeader
                className={cn('rounded-t-xl pl-2 pr-3 py-2 bg-gray-50', {
                  'border-b': !scorecard.criteria.length,
                })}
              >
                <div className="flex flex-row items-center">
                  <GripVertical
                    className="h-5 text-muted-foreground cursor-grab"
                    onMouseDown={(e) => {
                      setCurrentDraggedObject(scorecard);
                      onMouseDown({ ...scorecard, isExample }, e);
                    }}
                  />
                  <p className="flex-1 ml-2 font-medium">
                    {scorecard.sectionTitle}
                  </p>
                  {!!scorecard.criteria.length && (
                    <Badge variant={'secondary'} className="flex ml-2 h-[24px]">
                      {scorecard.criteria.length}
                      {' / '}
                      {scorecard.criteria.length}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-0 relative">
                {scorecard.criteria.map((c, criteriaIdx) => (
                  <div key={c.tempUuid} className="flex flex-col w-full">
                    <div
                      className={cn(
                        'w-full h-3 rounded-sm flex flex-col justify',
                      )}
                      onMouseEnter={() => {
                        onMouseEnter({ ...c, isAfter: false });
                      }}
                      onMouseLeave={onMouseLeave}
                    >
                      {<div className="border-t w-full"></div>}
                    </div>
                    <div className="p-3 pt-0 pl-2 flex flex-row w-full">
                      <GripVertical
                        className="h-5 text-muted-foreground cursor-grab"
                        onMouseDown={(e) => {
                          setCurrentDraggedObject(c);
                          onMouseDown({ ...c, isExample }, e);
                        }}
                      />
                      <div className="mt-1 ml-1 bg-green-600 self-start h-[12px] w-[12px] rounded-[2px]">
                        <Check className="text-white" size={12} />
                      </div>
                      <div className="flex flex-col flex-1 ml-1.5">
                        <div className="flex flex-row">
                          <p className="font-medium">{c.criterion}</p>
                        </div>
                        <p className="text-muted-foreground mt-2">
                          {typeof c.query === 'string'
                            ? c.query
                            : c.query.join(', ')}
                        </p>
                        <div className="flex flex-row space-x-2 text-muted-foreground mt-2">
                          <Badge
                            variant="secondary"
                            className="rounded-full font-medium text-muted-foreground"
                          >
                            {getDisplayNameOfCriteriaType(c.type)}
                          </Badge>
                          {['gpt', 'egpt', 'find'].includes(c.type) && (
                            <div className="h-full flex flex-row py-1">
                              <div className=" border-l " />
                            </div>
                          )}
                          {c.type === 'egpt' && (
                            <Badge
                              variant="secondary"
                              className="relative pl-5 bg-teal-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
                            >
                              <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-teal-500" />
                              <span className="text-teal-500">
                                AI Coaching Enabled
                              </span>
                            </Badge>
                          )}
                          {c.type === 'gpt' && (
                            <Badge
                              variant="secondary"
                              className="relative pl-5 bg-red-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
                            >
                              <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-red-500" />
                              <span className="text-red-500">
                                AI Coaching Disabled
                              </span>
                            </Badge>
                          )}
                          {c.type === 'find' && (
                            <Badge
                              variant="secondary"
                              className="relative pl-6 rounded-full font-medium text-muted-foreground capitalize"
                            >
                              <CircleUserRound className="absolute left-0 ml-2 w-3 h-3" />
                              {c.corpus}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    {criteriaIdx === scorecard.criteria.length - 1 &&
                      isDNDActive &&
                      !isDraggedObjectCard && (
                        <div
                          className={cn(
                            'absolute bottom-0 w-full h-3 rounded-sm flex flex-col justify-center',
                          )}
                          onMouseEnter={() => {
                            onMouseEnter({ ...c, isAfter: true });
                          }}
                          onMouseLeave={onMouseLeave}
                        />
                      )}
                  </div>
                ))}
              </CardContent>
            </Card>
            {cardIdx === cards.length - 1 ? (
              <div
                className={cn('w-full h-4 rounded-sm')}
                onMouseEnter={() => {
                  onMouseEnter({ ...scorecard, isAfter: true });
                }}
                onMouseLeave={onMouseLeave}
              />
            ) : (
              <></>
            )}
          </motion.div>
        );
      })}
    </div>
  );
}
