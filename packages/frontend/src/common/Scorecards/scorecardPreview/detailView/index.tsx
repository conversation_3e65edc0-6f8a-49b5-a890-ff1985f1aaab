import BuyerCard, { CardType } from '@/components/BuyerCard';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AgentDto } from '@/lib/Agent/types';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { Check, CircleUserRound, SparklesIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface IProps {
  scorecard: ScorecardConfigDto;
}

interface ScorecardPreviewCriteria {
  type: string;
  criterion: string;
  query: string | string[] | ScorecardPreviewCriteria[];
  corpus?: 'rep' | 'prospect' | 'both';
}

export default function ScorecardPreviewDetailView({ scorecard }: IProps) {
  const router = useRouter();

  const cards: {
    sectionTitle: string;
    criteria: ScorecardPreviewCriteria[];
  }[] = [];
  if (scorecard?.config) {
    const scorecardKeysSorted = ScorecardConfigService.getSortedKeys(scorecard);
    scorecardKeysSorted.forEach((sectionTitle: string) => {
      const obj: ScorecardPreviewCriteria[] = scorecard.config[sectionTitle];
      cards.push({
        sectionTitle,
        criteria: obj,
      });
    });
  }

  const getDisplayNameOfCriteriaType = (criteriaType: string) => {
    if (criteriaType.includes('gpt')) {
      return 'AI Grading';
    }
    const commonDispNamesOfCriteria: {
      [key: string]: string;
    } = {
      find: 'Keyword Matching',
      speak_time: 'Speak Time',
      yes_no: 'Yes/No',
      or: 'Or',
      and: 'And',
    };
    return commonDispNamesOfCriteria[criteriaType] || '';
  };

  const renderScorecardConfig = (
    config: ScorecardPreviewCriteria,
    index: number,
    renderCheckmark: boolean = true,
  ) => (
    <div
      key={`${config.type}${index}`}
      className={cn('flex flex-row space-x-2 p-3', {
        'border-t': index !== 0,
      })}
    >
      {renderCheckmark && (
        <div className="mt-1 bg-green-600 self-start h-[12px] w-[12px] rounded-[2px]">
          <Check className="text-white" size={12} />
        </div>
      )}
      <div className="flex flex-col">
        <p className="font-medium">{config.criterion}</p>
        {typeof config.query === 'string' ? (
          <p className="text-muted-foreground mt-2">{config.query}</p>
        ) : typeof config.query[0] === 'string' ? (
          <p className="text-muted-foreground mt-2">{config.query.join(',')}</p>
        ) : (
          config.query.map((config, idx) =>
            renderScorecardConfig(
              config as ScorecardPreviewCriteria,
              idx,
              false,
            ),
          )
        )}
        <div className="flex flex-row space-x-2 text-muted-foreground mt-2">
          <Badge
            variant="secondary"
            className="rounded-full font-medium text-muted-foreground"
          >
            {getDisplayNameOfCriteriaType(config.type)}
          </Badge>
          {['gpt', 'egpt', 'find'].includes(config.type) && (
            <div className="h-full flex flex-row py-1">
              <div className=" border-l " />
            </div>
          )}
          {config.type === 'egpt' && (
            <Badge
              variant="secondary"
              className="relative pl-5 bg-teal-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
            >
              <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-teal-500" />
              <span className="text-teal-500">AI Coaching Enabled</span>
            </Badge>
          )}
          {config.type === 'gpt' && (
            <Badge
              variant="secondary"
              className="relative pl-5 bg-red-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
            >
              <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-red-500" />{' '}
              <span className="text-red-500">AI Coaching Disabled</span>
            </Badge>
          )}
          {config.type === 'find' && (
            <Badge
              variant="secondary"
              className="relative pl-6 rounded-full font-medium text-muted-foreground capitalize"
            >
              <CircleUserRound className="absolute left-0 ml-2 w-3 h-3" />
              {config.corpus}
            </Badge>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col">
      {cards.map((sc, i: number) => (
        <motion.div
          key={sc.sectionTitle}
          className="mt-4"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
          transition={{
            duration: 0,
            delay: i * 0,
          }}
        >
          <Card>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center">
                <p className="font-semibold">{sc.sectionTitle}</p>
                <Badge variant={'secondary'}>
                  {sc.criteria.length}
                  {' / '}
                  {sc.criteria.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {sc.criteria.map((c, idx) => renderScorecardConfig(c, idx))}
            </CardContent>
          </Card>
        </motion.div>
      ))}
      {scorecard?.agents?.length && scorecard?.agents?.length > 0 ? (
        <motion.div
          className="mt-4"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
        >
          <Card>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center">
                <p className="font-semibold">In-Use by Agents</p>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0 pb-4">
              <div className="flex flex-col mx-4 mt-4">
                <div className="grid grid-cols-2 gap-3">
                  {scorecard.agents?.map((agent: AgentDto) => (
                    <BuyerCard
                      key={agent.id}
                      agent={agent}
                      type={CardType.COMPACT}
                      showMenu={false}
                      showAgentsTags={false}
                      showCallIcon={false}
                      hideEditBtn={true}
                      isSelected={false}
                      onCardClick={() => {
                        router.push(`/buyers/${agent.vapiId}`);
                      }}
                    />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ) : null}
    </div>
  );
}
