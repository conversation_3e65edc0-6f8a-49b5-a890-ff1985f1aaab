import BuyerCard, { CardType } from '@/components/BuyerCard';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import useScorecardConfigAgents from '@/hooks/useScorecardConfigAgents';
import { AgentDto } from '@/lib/Agent/types';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto, {
  LearningMaterialDto,
  ScorecardConfigMaterialScope,
} from '@/lib/ScorecardConfig/types';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import {
  Check,
  CircleUserRound,
  Loader2Icon,
  PlusIcon,
  SparklesIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState, useEffect, useMemo, useRef } from 'react';
import PaginationControls from '@/common/Calls/AIRoleplay/List/table/paginationControls';
import MaterialList from '../../LearningMaterials/MaterialList';
import { Button } from '@/components/ui/button';
import AddMaterialModal from '../../LearningMaterials/AddMaterialModal';

interface IProps {
  scorecard: ScorecardConfigDto;
  learningMaterials?: LearningMaterialDto[];
  refetchLearningMaterials: () => Promise<unknown>;
  onAdjustLearningMaterialsForCriterion: (
    dto: ScorecardConfigDto,
    learningMaterialIds: number[],
    sectionTitle: string,
    criterionIdx: number,
  ) => Promise<void>;
}

interface ScorecardPreviewCriteria {
  type: string;
  criterion: string;
  query: string | string[] | ScorecardPreviewCriteria[];
  learningMaterialIds?: number[];
  corpus?: 'rep' | 'prospect' | 'both';
}

export default function ScorecardPreviewDetailView({
  scorecard,
  learningMaterials,
  refetchLearningMaterials,
  onAdjustLearningMaterialsForCriterion,
}: IProps) {
  const router = useRouter();
  const [openAddLearningMaterialModal, setOpenAddLearningMaterialModal] =
    useState(false);
  const onAddLearningMaterialRef = useRef<(id: number) => unknown>(null);

  const [agentSearch, setAgentSearch] = useState('');
  const [agentPage, setAgentPage] = useState(0);
  const [agentPageSize, setAgentPageSize] = useState(10);
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [currentAgents, setCurrentAgents] = useState<AgentDto[]>([]);
  const idToLearningMaterial = useMemo(() => {
    if (!learningMaterials) {
      return {};
    }
    const criterionLearningMaterials = learningMaterials.filter(
      (l) => l.scope === ScorecardConfigMaterialScope.CRITERION,
    );
    return Object.fromEntries(
      criterionLearningMaterials.map((l) => [l.id, l]),
    ) as { [key: number]: LearningMaterialDto };
  }, [learningMaterials]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(agentSearch);
      setAgentPage(0);
    }, 500);

    return () => clearTimeout(timer);
  }, [agentSearch]);

  const { data: agentData, isLoading } = useScorecardConfigAgents(
    scorecard.id,
    true,
    {
      page: agentPage,
      pageSize: agentPageSize,
      search: debouncedSearch,
    },
  );

  useEffect(() => {
    if (agentData?.agents) {
      setCurrentAgents(agentData.agents);
    }
  }, [agentData?.agents]);

  const totalCount = agentData?.totalCount || 0;

  const cards: {
    sectionTitle: string;
    criteria: ScorecardPreviewCriteria[];
  }[] = [];
  if (scorecard?.config) {
    const scorecardKeysSorted = ScorecardConfigService.getSortedKeys(scorecard);
    scorecardKeysSorted.forEach((sectionTitle: string) => {
      const obj: ScorecardPreviewCriteria[] = scorecard.config[sectionTitle];
      cards.push({
        sectionTitle,
        criteria: obj,
      });
    });
  }

  const getDisplayNameOfCriteriaType = (criteriaType: string) => {
    if (criteriaType.includes('gpt')) {
      return 'AI Grading';
    }
    const commonDispNamesOfCriteria: {
      [key: string]: string;
    } = {
      find: 'Keyword Matching',
      speak_time: 'Speak Time',
      yes_no: 'Yes/No',
      or: 'Or',
      and: 'And',
    };
    return commonDispNamesOfCriteria[criteriaType] || '';
  };

  const renderScorecardConfig = (
    sectionTitle: string,
    config: ScorecardPreviewCriteria,
    index: number,
    renderCheckmark: boolean = true,
  ) => {
    const learningMaterials =
      config.learningMaterialIds
        ?.map((id) => idToLearningMaterial[id])
        ?.filter(Boolean) || [];
    return (
      <div
        key={`${config.type}${index}`}
        className={cn('flex flex-row space-x-2 p-3 w-full', {
          'border-t': index !== 0,
        })}
      >
        {renderCheckmark && (
          <div className="mt-1 bg-green-600 self-start h-[12px] w-[12px] rounded-[2px]">
            <Check className="text-white" size={12} />
          </div>
        )}
        <div className="flex flex-col w-full">
          <p className="font-medium">{config.criterion}</p>
          {typeof config.query === 'string' ? (
            <p className="text-muted-foreground mt-2">{config.query}</p>
          ) : typeof config.query[0] === 'string' ? (
            <p className="text-muted-foreground mt-2">
              {config.query.join(',')}
            </p>
          ) : (
            config.query.map((config, idx) =>
              renderScorecardConfig(
                sectionTitle,
                config as ScorecardPreviewCriteria,
                idx,
                false,
              ),
            )
          )}
          <div className="flex flex-row space-x-2 text-muted-foreground mt-2">
            <Badge
              variant="secondary"
              className="rounded-full font-medium text-muted-foreground"
            >
              {getDisplayNameOfCriteriaType(config.type)}
            </Badge>
            {['gpt', 'egpt', 'find'].includes(config.type) && (
              <div className="h-full flex flex-row py-1">
                <div className=" border-l " />
              </div>
            )}
            {config.type === 'egpt' && (
              <Badge
                variant="secondary"
                className="relative pl-5 bg-teal-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
              >
                <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-teal-500" />
                <span className="text-teal-500">AI Coaching Enabled</span>
              </Badge>
            )}
            {config.type === 'gpt' && (
              <Badge
                variant="secondary"
                className="relative pl-5 bg-red-50 rounded-full font-medium inline-flex flex-row items-center space-x-1"
              >
                <SparklesIcon className="absolute left-0 ml-2 w-3 h-3 text-red-500" />{' '}
                <span className="text-red-500">AI Coaching Disabled</span>
              </Badge>
            )}
            {config.type === 'find' && (
              <Badge
                variant="secondary"
                className="relative pl-6 rounded-full font-medium text-muted-foreground capitalize"
              >
                <CircleUserRound className="absolute left-0 ml-2 w-3 h-3" />
                {config.corpus}
              </Badge>
            )}
          </div>
          <div className="flex flex-col space-y-2 mt-2 w-full">
            {learningMaterials.length > 0 && (
              <MaterialList
                scorecardConfigId={scorecard.id}
                materialList={learningMaterials}
                scope={ScorecardConfigMaterialScope.CRITERION}
                onEditDone={async () => {
                  await refetchLearningMaterials();
                }}
                onDeleteDone={async (id) => {
                  await refetchLearningMaterials();
                  const remainingIds =
                    config.learningMaterialIds?.filter((i) => i !== id) || [];
                  await onAdjustLearningMaterialsForCriterion(
                    scorecard,
                    remainingIds,
                    sectionTitle,
                    index,
                  );
                }}
              />
            )}
            <Button
              variant={'outline'}
              onClick={() => {
                setOpenAddLearningMaterialModal(true);
                onAddLearningMaterialRef.current = async (id) => {
                  // since this is a new learning material
                  await onAdjustLearningMaterialsForCriterion(
                    scorecard,
                    [id],
                    sectionTitle,
                    index,
                  );
                };
              }}
            >
              <PlusIcon size={16} className="mr-2" />
              Add Learning Material
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col">
      {cards.map((sc, i: number) => (
        <motion.div
          key={sc.sectionTitle}
          className="mt-4"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
          transition={{
            duration: 0,
            delay: i * 0,
          }}
        >
          <Card>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center">
                <p className="font-semibold">{sc.sectionTitle}</p>
                <Badge variant={'secondary'}>
                  {sc.criteria.length}
                  {' / '}
                  {sc.criteria.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0" noOverflow>
              {sc.criteria.map((c, idx) =>
                renderScorecardConfig(sc.sectionTitle, c, idx),
              )}
            </CardContent>
          </Card>
        </motion.div>
      ))}
      {currentAgents?.length && currentAgents?.length > 0 ? (
        <motion.div
          className="mt-4"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
        >
          <Card>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center">
                <p className="font-semibold">In-Use by Agents</p>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0 pb-4" noOverflow>
              <div className="flex flex-col mx-4 mt-4">
                <div className="mb-4">
                  <Input
                    placeholder="Search agents..."
                    value={agentSearch}
                    onChange={(e) => setAgentSearch(e.target.value)}
                    className="flex-1"
                  />
                </div>
                <div className="grid grid-cols-2 gap-3 mb-8 relative">
                  {currentAgents?.map((agent: AgentDto) => (
                    <BuyerCard
                      key={agent.id}
                      agent={agent}
                      type={CardType.COMPACT}
                      showMenu={false}
                      showAgentsTags={false}
                      showCallIcon={false}
                      hideEditBtn={true}
                      isSelected={false}
                      onCardClick={() => {
                        const agentId = agent.providerAgentId
                          ? agent.providerAgentId
                          : agent.vapiId;
                        router.push(`/buyers/${agentId}`);
                      }}
                    />
                  ))}
                  {isLoading && (
                    <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center">
                      <Loader2Icon className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  )}
                </div>
                <PaginationControls
                  from={agentPage * agentPageSize}
                  numberOfResults={agentPageSize}
                  totNumberOfRows={totalCount || 0}
                  updatePagination={(page, pageSize) => {
                    setAgentPage(Math.floor(page / pageSize));
                    setAgentPageSize(pageSize);
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ) : null}

      <AddMaterialModal
        open={openAddLearningMaterialModal}
        onClose={() => {
          onAddLearningMaterialRef.current = null;
          setOpenAddLearningMaterialModal(false);
        }}
        scorecardConfigId={scorecard.id}
        scope={ScorecardConfigMaterialScope.CRITERION}
        onAddDone={async (id) => {
          if (id) {
            onAddLearningMaterialRef.current?.(id);
          }
          await refetchLearningMaterials();
        }}
      />
    </div>
  );
}
