import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { Badge } from '@/components/ui/badge';
import { AgentCallType } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';

export const CallTypeBadge = ({
  callType,
  className,
  showDefaultLabel = false,
}: {
  callType: AgentCallType;
  className?: string;
  showDefaultLabel?: boolean;
}) => {
  const typeInfo = CALL_TYPE_OPTIONS.find((ct) => ct.value === callType);
  return (
    <Badge
      variant={'secondary'}
      className={cn(
        'rounded-full font-normal',
        {
          'px-1': showDefaultLabel,
          'pl-1 pr-1.5': !showDefaultLabel,
        },
        className,
      )}
    >
      {!!typeInfo?.Icon && <typeInfo.Icon className="w-3 h-3" />}
      <span className="ml-1">{typeInfo?.label}</span>
      {showDefaultLabel && (
        <Badge
          className="rounded-full ml-2 text-[10px] px-1 py-0"
          variant={'default'}
        >
          Default
        </Badge>
      )}
    </Badge>
  );
};
