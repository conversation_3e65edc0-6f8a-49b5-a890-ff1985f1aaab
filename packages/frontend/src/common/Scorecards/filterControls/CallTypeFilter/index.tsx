import { AgentCallType } from '@/lib/Agent/types';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  CheckIcon,
  FilterX,
  ChevronDownIcon,
  ChevronUpIcon,
} from 'lucide-react';
import {
  Command,
  CommandList,
  CommandItem,
  CommandSeparator,
  CommandGroup,
} from '@/components/ui/command';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import _ from 'lodash';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';

interface ICallTypeFilterProps {
  scorecards: ScorecardConfigDto[];
  disabled?: boolean;
  current: (AgentCallType | string)[];
  onCallTypesUpdated: (callTypes: (AgentCallType | string)[]) => void;
  onFilterTypeUpdated: (callTypes: string) => void;
}

export default function CallTypeFilter({
  disabled,
  current,
  onCallTypesUpdated,
  onFilterTypeUpdated,
  scorecards,
}: ICallTypeFilterProps) {
  const [open, setOpen] = useState(false);
  const { callTypeOptions } = useCallTypeOptions();
  const [hovering, setHovering] = useState<string | null>(null);

  if (!current) {
    current = [];
  }

  const updateFilter = (open: boolean) => {
    if (!disabled) {
      setOpen(open);
    }
  };

  const toggleCallType = (callType: AgentCallType | string) => {
    const tmp: (AgentCallType | string)[] = [];
    if (current.find((val) => val === callType) || callType === 'all') {
      tmp.push(
        ...(callType === 'all'
          ? ['all']
          : current.filter((val) => val !== callType)),
      );
    } else {
      tmp.push(...current);
      tmp.push(callType);
    }

    if (onCallTypesUpdated) {
      onCallTypesUpdated(tmp);
      onFilterTypeUpdated('any');
    }
  };

  const clearAll = () => {
    if (onCallTypesUpdated) {
      onCallTypesUpdated([]);
    }

    setOpen(false);
  };

  const onOnlyClick = (callType: AgentCallType | string) => {
    if (onFilterTypeUpdated && onCallTypesUpdated) {
      onCallTypesUpdated([callType]);
      onFilterTypeUpdated('only');
    }
  };

  return (
    <Popover open={open} onOpenChange={updateFilter}>
      <PopoverTrigger asChild>
        <Button
          disabled={disabled}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border w-full"
        >
          Select Call Type {current.length > 0 && ': '}
          {current[0] === 'all'
            ? 'All'
            : `${current?.map((item) => _.capitalize(item)).join(',')}`}
          {open ? (
            <ChevronUpIcon className=" ml-2 h-4 w-4" />
          ) : (
            <ChevronDownIcon className=" ml-2 h-4 w-4" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="p-0"
        style={{ width: 'var(--radix-popover-trigger-width)' }}
      >
        <Command>
          <CommandList>
            <CommandItem
              className="py-3 px-3"
              value="all"
              onSelect={() => toggleCallType('all')}
            >
              <div
                className={cn(
                  'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                  current?.find((val) => val === 'all')
                    ? 'bg-primary text-primary-foreground'
                    : 'opacity-50 [&_svg]:invisible',
                )}
              >
                <CheckIcon className={cn('h-4 w-4')} />
              </div>
              <div className="flex items-center flex-1">
                <div className="capitalize font-medium">Select All</div>
                <div className="flex-1 text-end">
                  {scorecards?.length} total
                </div>
              </div>
            </CommandItem>
            <div className="w-full h-[1px] bg-[#E4E4E7]" />
            {callTypeOptions.map((ct) => {
              const Icon =
                CALL_TYPE_TO_ICON?.[ct.value as keyof typeof CALL_TYPE_TO_ICON]
                  ?.Icon;
              const matchingScorecards = scorecards?.filter((scorecard) =>
                scorecard.callTypes?.some(
                  ({ callType }) => callType === ct.value,
                ),
              );
              const defaultScorecards = matchingScorecards
                .filter((sc) =>
                  sc.callTypes?.some(
                    ({ callType, isDefaultForCallType }) =>
                      callType === ct.value && isDefaultForCallType,
                  ),
                )
                .map((sc) => sc.tag);

              const defaultScorecardText = (() => {
                if (defaultScorecards.length === 0) {
                  return 'None';
                }

                if (defaultScorecards.length <= 2) {
                  return defaultScorecards.join(' and ');
                }

                if (defaultScorecards.length === 3) {
                  return (
                    defaultScorecards.slice(0, 2).join(' and ') + ' and 1 other'
                  );
                }

                return (
                  defaultScorecards.slice(0, 2).join(' and ') +
                  ` and ${defaultScorecards.length - 2} others`
                );
              })();
              return (
                <CommandItem
                  key={ct.value}
                  value={ct.value}
                  onSelect={() => toggleCallType(ct.value)}
                  className="px-3 pt-3 pb-1 hover:cursor-pointer"
                  onMouseEnter={() => setHovering(ct.value)}
                  onMouseLeave={() => setHovering(null)}
                >
                  <div
                    className={cn(
                      'self-start mt-[1px] mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      current?.find((val) => [ct?.value, 'all'].includes(val))
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>

                  <div className="flex flex-1 flex-col">
                    <div className="flex flex-row w-full justify-start items-start min-h-8">
                      {Icon && <Icon className="mr-2 mt-[2px] h-4 w-4" />}
                      <div className="capitalize flex-1">{ct.label}</div>
                      {hovering === ct.value ? (
                        <div
                          className=" hover:bg-blue-50 z-10 transition-colors p-1 rounded-sm text-end text-sm font-semibold text-[#105AD4]"
                          onClick={(e) => {
                            e.stopPropagation();
                            onOnlyClick(ct.value);
                          }}
                        >
                          ONLY
                        </div>
                      ) : (
                        <div className="flex-1 text-end">
                          {matchingScorecards?.length} scorecard
                          {matchingScorecards?.length !== 1 && 's'}
                        </div>
                      )}
                    </div>
                    <div className="flex w-full text-muted-foreground">
                      <div>Default: {defaultScorecardText}</div>
                    </div>
                  </div>
                </CommandItem>
              );
            })}
          </CommandList>
          {current?.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}
