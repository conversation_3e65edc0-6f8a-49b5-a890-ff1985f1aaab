import useScorecardConfigById from '@/hooks/useScorecardConfigById';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto, {
  ScorecardConfigInnerConfig,
} from '@/lib/ScorecardConfig/types';
import { useCallback, useMemo, useState } from 'react';
import ManipulateScorecard from '../manipulateScorecard';

export const EditScorecard = ({
  scorecardId,
  onCancel,
  onSaved,
  showSaveAsDraft = true,
}: {
  scorecardId: number;
  onSaved: (scorecardId: number) => unknown;
  onCancel: () => unknown;
  showSaveAsDraft?: boolean;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);

  const checkCanSave = useCallback((dto: Partial<ScorecardConfigDto>) => {
    if (!dto.tag) {
      return {
        canSave: false,
        reason: 'You need to enter a scorecard name',
      };
    }
    const configParsed = dto.config as ScorecardConfigInnerConfig;

    if (!configParsed || !Object.keys(configParsed).length) {
      return {
        canSave: false,
        reason: 'You should specify a config to save',
      };
    }
    if (!Object.keys(configParsed).every((sectionName) => !!sectionName)) {
      return {
        canSave: false,
        reason: 'Every section should have a name',
      };
    }
    if (
      !Object.values(configParsed).every((criterias) => {
        return (
          criterias.length > 0 &&
          criterias.every(
            (c) => !!c.type && !!c.corpus && !!c.query && !!c.criterion,
          )
        );
      })
    ) {
      return {
        canSave: false,
        reason: 'All added fields in the scorecard should be non-empty',
      };
    }
    return {
      canSave: true,
    };
  }, []);

  const onCancelInternal = () => {
    onCancel();
  };

  const onSaveInternal = async (
    dto: ScorecardConfigDto,
    isAutoSave = false,
  ) => {
    setIsLoading(true);
    await ScorecardConfigService.Update(
      scorecardId,
      dto.config,
      dto.tag,
      dto.stats,
      dto.callTypes,
    );
    setIsLoading(false);
    if (!isAutoSave) {
      onSaved(scorecardId);
    }
  };

  const onPublishInternal = async (dto: ScorecardConfigDto) => {
    setIsLoading(true);
    setIsPublishing(true);
    await ScorecardConfigService.Update(
      scorecardId,
      dto.config,
      dto.tag,
      dto.stats,
      dto.callTypes,
      true,
    );
    setIsLoading(false);
    setIsPublishing(false);
    onSaved(scorecardId);
  };

  const { data: scorecardConfigDto, isFetching: scorecardFetching } =
    useScorecardConfigById(scorecardId, true);

  const scorecard = useMemo(
    () =>
      scorecardConfigDto?.drafts?.length
        ? { ...scorecardConfigDto.drafts[0], id: scorecardConfigDto.id }
        : scorecardConfigDto,
    [scorecardConfigDto],
  );

  return (
    <ManipulateScorecard
      breadcrumbsOverride={[
        { title: 'Scorecards', href: '/scorecards' },
        {
          title: scorecardConfigDto ? scorecardConfigDto.tag : 'New',
          href: `/scorecards?id=${scorecardId}`,
        },
        { title: 'Edit' },
      ]}
      title="Edit Scorecard"
      initialScorecardDto={scorecard}
      onCancel={onCancelInternal}
      checkCanSave={checkCanSave}
      onPublish={onPublishInternal}
      isPublishAvailable
      onSave={onSaveInternal}
      isLoading={isLoading || scorecardFetching}
      isPublishing={isPublishing}
      showScorecardExamples
      showSaveAsDraft={showSaveAsDraft}
    />
  );
};
