import PageHeader from '@/components/PageHeader';
import { Button } from '@/components/ui/button';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import useUserSession from '@/hooks/useUserSession';
import OrganizationService from '@/lib/Organization';
import { cn } from '@/lib/utils';
import { Circle, CircleCheck, Upload } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { ToastContainer, toast } from 'react-toastify';
import { useQueryClient } from '@tanstack/react-query';
import { DefaultRepVisibilityLevel } from '@/lib/Organization/types';

interface OrgInfo {
  name: string;
  onlyAdminCanViewAllCalls: boolean;
  onlyAdminCanViewAllRealBuyerCalls: boolean;
  onlyAdminCanDeleteCalls: boolean;
  isRepInstructionsMandatory: boolean;
  blurLeaderboard: boolean;
  repsCanEditScoreResults: 'YES' | 'NO' | 'DISPUTE_ONLY';
  logoUrl?: string;
  useTeamLevelManagement: boolean;
  teamLeadsCanChangeRepVisibilityLevel: boolean;
  defaultRepVisibilityLevel: DefaultRepVisibilityLevel;
}

export default function OrganizationSettings() {
  const { org, dbOrg } = useUserSession();

  const [orgInfo, setOrgInfo] = useState<OrgInfo>();
  const [imagePreview, setImagePreview] = useState<string>('');
  const timeoutSave = useRef<ReturnType<typeof setTimeout> | null>(null);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (dbOrg && org) {
      setOrgInfo({
        name: dbOrg.name,
        onlyAdminCanViewAllCalls: dbOrg.onlyAdminCanViewAllCalls,
        onlyAdminCanViewAllRealBuyerCalls:
          dbOrg.frontEndConf?.onlyAdminCanViewAllRealBuyerCalls || false,
        onlyAdminCanDeleteCalls:
          dbOrg.frontEndConf?.onlyAdminsCanDeleteUserCalls || false,
        isRepInstructionsMandatory:
          dbOrg.frontEndConf?.isRepInstructionsMandatory || false,
        repsCanEditScoreResults: dbOrg.repsCanEditScoreResults || 'NO',
        blurLeaderboard: dbOrg.frontEndConf?.blurLeaderboard || false,
        logoUrl: org?.orgMetadata?.logo,
        useTeamLevelManagement: dbOrg.useTeamLevelManagement || false,
        teamLeadsCanChangeRepVisibilityLevel:
          dbOrg.teamLeadsCanChangeRepVisibilityLevel || false,
        defaultRepVisibilityLevel:
          dbOrg.defaultRepVisibilityLevel || DefaultRepVisibilityLevel.TEAM,
      });
    }
  }, [dbOrg, org]);

  /********************************/
  /*********** ACTIONS ************/
  /********************************/

  const update = (o: OrgInfo) => {
    setOrgInfo(o);

    if (timeoutSave.current) {
      clearTimeout(timeoutSave.current);
    }
    timeoutSave.current = setTimeout(async () => {
      doSave(o);
    }, 1000);
  };

  const loadFile = useCallback(async (files: File[]) => {
    let image: string = '';

    if (files) {
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > 1024 * 1024 * 2) {
          //2MB
          toast.error('File size must be less than 2MB');
          return;
        }
        image = URL.createObjectURL(files[i]);
      }

      setImagePreview(image);
    }

    if (files[0]) {
      await OrganizationService.updateLogo(files[0]);
    }
  }, []);

  const doSave = async (info: OrgInfo) => {
    if (info?.name == '') {
      toast.error('Organization name cannot be empty');
      return;
    }

    try {
      await OrganizationService.updateOrganizationInfo({
        name: info.name,
        onlyAdminCanViewAllCalls: info.onlyAdminCanViewAllCalls,
        onlyAdminCanViewAllRealBuyerCalls:
          info.onlyAdminCanViewAllRealBuyerCalls,
        onlyAdminCanDeleteCalls: info.onlyAdminCanDeleteCalls,
        repsCanEditScoreResults: info.repsCanEditScoreResults,
        isRepInstructionsMandatory: info.isRepInstructionsMandatory,
        blurLeaderboard: info.blurLeaderboard,
        useTeamLevelManagement: info.useTeamLevelManagement,
        teamLeadsCanChangeRepVisibilityLevel:
          info.teamLeadsCanChangeRepVisibilityLevel,
        defaultRepVisibilityLevel: info.defaultRepVisibilityLevel,
      });
    } catch (e: unknown) {
      console.log(e);
    }

    queryClient.invalidateQueries({ queryKey: ['org'] });
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: loadFile,
  });

  /********************************/
  /************ RENDER ************/
  /********************************/

  return (
    <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6 overflow-auto">
      <PageHeader title="Workspace Settings" />
      <div className="text-muted-foreground mt-2 mb-6">
        You can use this page to update the metadata for this organization and
        manage the access settings for this organization.
      </div>

      <div className="bg-white border rounded-xl p-4">
        <div className="flex items-start mb-6">
          <div>
            {imagePreview ? (
              <img
                src={imagePreview}
                alt="logo"
                className="w-[62px] h-[62px] rounded-xl"
              />
            ) : (
              <Image
                src={orgInfo?.logoUrl || '/images/square-black-logo.svg'}
                alt={`${org?.orgName} logo`}
                width={62}
                height={62}
                className="rounded-xl flex-shrink-0"
                priority
              />
            )}
          </div>
          <div className="ml-3">
            <div className="text-sm mb-2 font-semibold">Workspace logo</div>
            <div>
              <div {...getRootProps()}>
                <input {...getInputProps()} />
                <Button variant="outline">
                  <Upload size={16} className="mr-2" />
                  Upload new
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div>
          <Label>Organization Name</Label>
          <Input
            placeholder="Organization Name"
            className="mt-1"
            value={orgInfo?.name}
            onChange={(e) => {
              update({ ...orgInfo, name: e.target.value } as OrgInfo);
            }}
          />
        </div>
      </div>

      <div className="bg-white border rounded-xl p-4 mt-4">
        <div className="font-semibold">Data visibility settings</div>

        <div className="flex items-center mt-6">
          <div className="flex-1">
            <div className="font-semibold">Use team level management</div>
            <div className="text-muted-foreground mt-2">
              When enabled, data visibility is managed at the team level.
            </div>
          </div>
          <div>
            <Switch
              checked={orgInfo?.useTeamLevelManagement}
              onCheckedChange={(value) => {
                update({
                  ...orgInfo,
                  useTeamLevelManagement: value,
                } as OrgInfo);
              }}
            />
          </div>
        </div>

        <div className="flex items-center mt-6">
          <div className="flex-1">
            <div className="font-semibold">
              Team managers can change rep visibility level
            </div>
            <div className="text-muted-foreground mt-2">
              When enabled, team managers can change the visibility level of
              reps.
            </div>
          </div>
          <div>
            <Switch
              checked={orgInfo?.teamLeadsCanChangeRepVisibilityLevel}
              onCheckedChange={(value) => {
                update({
                  ...orgInfo,
                  teamLeadsCanChangeRepVisibilityLevel: value,
                } as OrgInfo);
              }}
            />
          </div>
        </div>

        <div className="mt-6">
          <div className="font-semibold mb-4">Default visibility level</div>

          <div
            className={cn(
              'cursor-pointer flex items-center mb-3 border rounded-xl p-4 hover:bg-gray-50',
              {
                'border-blue-400':
                  orgInfo?.defaultRepVisibilityLevel ==
                  DefaultRepVisibilityLevel.TEAM,
                'bg-blue-50':
                  orgInfo?.defaultRepVisibilityLevel ==
                  DefaultRepVisibilityLevel.TEAM,
              },
            )}
            onClick={() => {
              update({
                ...orgInfo,
                defaultRepVisibilityLevel: DefaultRepVisibilityLevel.TEAM,
              } as OrgInfo);
            }}
          >
            <div className="flex-1">
              <div className="font-semibold">Team</div>
              <div className="text-muted-foreground mt-2">
                Reps can see data from their team and sub-teams.
              </div>
            </div>
            <div>
              {orgInfo?.defaultRepVisibilityLevel ==
              DefaultRepVisibilityLevel.TEAM ? (
                <CircleCheck size={20} className="text-blue-500" />
              ) : (
                <Circle size={20} className="text-gray-300" />
              )}
            </div>
          </div>

          <div
            className={cn(
              'cursor-pointer flex items-center mb-3 border rounded-xl p-4 hover:bg-gray-50',
              {
                'border-blue-400':
                  orgInfo?.defaultRepVisibilityLevel ==
                  DefaultRepVisibilityLevel.PERSONAL,
                'bg-blue-50':
                  orgInfo?.defaultRepVisibilityLevel ==
                  DefaultRepVisibilityLevel.PERSONAL,
              },
            )}
            onClick={() => {
              update({
                ...orgInfo,
                defaultRepVisibilityLevel: DefaultRepVisibilityLevel.PERSONAL,
              } as OrgInfo);
            }}
          >
            <div className="flex-1">
              <div className="font-semibold">Personal</div>
              <div className="text-muted-foreground mt-2">
                Reps can see only their own data.
              </div>
            </div>
            <div>
              {orgInfo?.defaultRepVisibilityLevel ==
              DefaultRepVisibilityLevel.PERSONAL ? (
                <CircleCheck size={20} className="text-blue-500" />
              ) : (
                <Circle size={20} className="text-gray-300" />
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white border rounded-xl p-4 mt-4">
        <div className="font-semibold">Call settings</div>

        <div className="flex items-center mt-6">
          <div className="flex-1">
            <div className="font-semibold">
              Make &quot;Instructions for reps&quot; section mandatory
            </div>
            <div className="text-muted-foreground mt-2">
              When enabled, reps will be required to acknowledge the
              instructions before they can start a call.
            </div>
          </div>
          <div>
            <Switch
              checked={orgInfo?.isRepInstructionsMandatory}
              onCheckedChange={(value) => {
                update({
                  ...orgInfo,
                  isRepInstructionsMandatory: value,
                } as OrgInfo);
              }}
            />
          </div>
        </div>

        <div className="flex items-center mt-6">
          <div className="flex-1">
            <div className="font-semibold">Hide Leaderboards</div>
            <div className="text-muted-foreground mt-2">
              When enabled, leaderboards will not be displayed to non-admins.
            </div>
          </div>
          <div>
            <Switch
              checked={orgInfo?.blurLeaderboard}
              onCheckedChange={(value) => {
                update({
                  ...orgInfo,
                  blurLeaderboard: value,
                } as OrgInfo);
              }}
            />
          </div>
        </div>
      </div>

      <div className="bg-white border rounded-xl p-4 mt-4">
        <div className="font-semibold">Call permissions</div>

        <div className="flex items-center mt-6">
          <div className="flex-1">
            <div className="font-semibold">
              Only Admins can view all real buyer calls
            </div>
            <div className="text-muted-foreground mt-2">
              When enabled, only admins can view all real buyer calls, while
              reps can see only their own real buyer call history.
            </div>
          </div>
          <div>
            <Switch
              checked={orgInfo?.onlyAdminCanViewAllRealBuyerCalls}
              onCheckedChange={(value) => {
                update({
                  ...orgInfo,
                  onlyAdminCanViewAllRealBuyerCalls: value,
                } as OrgInfo);
              }}
            />
          </div>
        </div>

        <div className="flex items-center mt-6">
          <div className="flex-1">
            <div className="font-semibold">
              Only Admins can view all roleplay calls
            </div>
            <div className="text-muted-foreground mt-2">
              When enabled, only admins can view all roleplay calls, while reps
              can see only their own roleplay call history.
            </div>
          </div>
          <div>
            <Switch
              checked={orgInfo?.onlyAdminCanViewAllCalls}
              onCheckedChange={(value) => {
                update({
                  ...orgInfo,
                  onlyAdminCanViewAllCalls: value,
                } as OrgInfo);
              }}
            />
          </div>
        </div>

        <div className="flex items-center mt-6">
          <div className="flex-1">
            <div className="font-semibold">
              Only Admins can delete user calls
            </div>
            <div className="text-muted-foreground mt-2">
              When enabled, only admins can delete user calls, while reps cannot
              remove any calls.
            </div>
          </div>
          <div>
            <Switch
              checked={orgInfo?.onlyAdminCanDeleteCalls}
              onCheckedChange={(value) => {
                update({
                  ...orgInfo,
                  onlyAdminCanDeleteCalls: value,
                } as OrgInfo);
              }}
            />
          </div>
        </div>
      </div>

      <div className="bg-white border rounded-xl p-4 mt-4">
        <div className="font-semibold mb-6">Score editing</div>

        <div
          className={cn(
            'cursor-pointer flex items-center mb-3 border rounded-xl p-4 hover:bg-gray-50',
            {
              'border-blue-400': orgInfo?.repsCanEditScoreResults == 'YES',
              'bg-blue-50': orgInfo?.repsCanEditScoreResults == 'YES',
            },
          )}
          onClick={() => {
            update({
              ...orgInfo,
              repsCanEditScoreResults: 'YES',
            } as OrgInfo);
          }}
        >
          <div className="flex-1">
            <div className="font-semibold">Full editing</div>
            <div className="text-muted-foreground mt-2">
              Reps can overwrite or reverse the score without approval.
            </div>
          </div>
          <div>
            {orgInfo?.repsCanEditScoreResults == 'YES' ? (
              <CircleCheck size={20} className="text-blue-500" />
            ) : (
              <Circle size={20} className="text-gray-300" />
            )}
          </div>
        </div>

        <div
          className={cn(
            'cursor-pointer flex items-center mb-3 border rounded-xl p-4 hover:bg-gray-50',
            {
              'border-blue-400':
                orgInfo?.repsCanEditScoreResults == 'DISPUTE_ONLY',
              'bg-blue-50': orgInfo?.repsCanEditScoreResults == 'DISPUTE_ONLY',
            },
          )}
          onClick={() => {
            update({
              ...orgInfo,
              repsCanEditScoreResults: 'DISPUTE_ONLY',
            } as OrgInfo);
          }}
        >
          <div className="flex-1">
            <div className="font-semibold">Approval required</div>
            <div className="text-muted-foreground mt-2">
              Reps can dispute the score, but an admin must approve any changes.
            </div>
          </div>
          <div>
            {orgInfo?.repsCanEditScoreResults == 'DISPUTE_ONLY' ? (
              <CircleCheck size={20} className="text-blue-500" />
            ) : (
              <Circle size={20} className="text-gray-300" />
            )}
          </div>
        </div>

        <div
          className={cn(
            'cursor-pointer flex items-center mb-3 border rounded-xl p-4 hover:bg-gray-50',
            {
              'border-blue-400': orgInfo?.repsCanEditScoreResults == 'NO',
              'bg-blue-50': orgInfo?.repsCanEditScoreResults == 'NO',
            },
          )}
          onClick={() => {
            update({
              ...orgInfo,
              repsCanEditScoreResults: 'NO',
            } as OrgInfo);
          }}
        >
          <div className="flex-1">
            <div className="font-semibold">No editing rights</div>
            <div className="text-muted-foreground mt-2">
              Reps cannot modify or dispute the score.
            </div>
          </div>
          <div>
            {orgInfo?.repsCanEditScoreResults == 'NO' ? (
              <CircleCheck size={20} className="text-blue-500" />
            ) : (
              <Circle size={20} className="text-gray-300" />
            )}
          </div>
        </div>
      </div>
      <ToastContainer />
    </ScrollablePage>
  );
}
