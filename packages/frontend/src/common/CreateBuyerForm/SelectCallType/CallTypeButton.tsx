import { Button } from '@/components/ui/button';
import { CreateBuyerBotContext } from '@/contexts/CreateBuyerBotContext';
import { AgentCallType } from '@/lib/Agent/types';
import { LucideIcon } from 'lucide-react';
import { useContext } from 'react';
import { useRouter } from 'next/navigation';
import { CALL_TYPE_TO_ICON } from '../constants';

interface IProps {
  option: {
    label: string;
    value: AgentCallType;
    Icon: LucideIcon;
    description: string;
  };
}

export default function CallTypeButton({
  option: { label, value, description },
}: IProps) {
  const { baseRoute } = useContext(CreateBuyerBotContext);
  const router = useRouter();

  const Icon =
    CALL_TYPE_TO_ICON?.[value as keyof typeof CALL_TYPE_TO_ICON]?.Icon;

  const handleSelectCallType = () =>
    router.push(
      value === AgentCallType.FOCUS // || value === AgentCallType.GATEKEEPER
        ? `${baseRoute}/create/focus?callType=${value}`
        : `${baseRoute}/create/ai-generator?callType=${value}`,
    );

  return (
    value != AgentCallType.GATEKEEPER && (
      <Button
        variant="outline"
        onClick={handleSelectCallType}
        className="min-h-48 rounded-xl bg-white"
      >
        <div className="flex flex-col items-center justify-center">
          <Icon className="w-6 h-6 text-primary" />
          <p className="mt-2 text-base">{label}</p>
          <p className="mt-2 text-muted-foreground text-wrap text-[0.8rem]">
            {description}
          </p>
        </div>
      </Button>
    )
  );
}
