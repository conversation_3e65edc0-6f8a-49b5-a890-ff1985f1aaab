import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { CreateBuyerBotContext } from '@/contexts/CreateBuyerBotContext';
import { AgentCallType } from '@/lib/Agent/types';
import { LucideIcon, PhoneIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useContext } from 'react';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';

function SelectCallType() {
  const { baseRoute } = useContext(CreateBuyerBotContext);
  const router = useRouter();
  const { callTypeOptions } = useCallTypeOptions();

  const renderCallTypeButton = (
    option: {
      label: string;
      value: AgentCallType;
      Icon: LucideIcon;
      description: string;
    },
    i: number,
  ) => {
    if (option.value != AgentCallType.GATEKEEPER) {
      const Icon =
        CALL_TYPE_TO_ICON?.[option?.value as keyof typeof CALL_TYPE_TO_ICON]
          ?.Icon;
      return (
        <Button
          variant="outline"
          key={option.label + i}
          onClick={() =>
            router.push(
              option.value === AgentCallType.FOCUS ||
                option.value === AgentCallType.GATEKEEPER
                ? `${baseRoute}/create/focus?callType=${option.value}`
                : `${baseRoute}/create/ai-generator?callType=${option.value}`,
            )
          }
          className="min-h-64 rounded-xl bg-white"
        >
          <div className="flex flex-col items-center justify-center">
            <Icon className="w-6 h-6 text-primary" />
            {/* <span className="text-4xl">❄️</span>
            <span className="text-4xl">🔥</span>
            <span className="text-4xl">🕵️</span>
            <span className="text-4xl">🚪</span>
            <span className="text-4xl">👁️‍🗨️</span> */}
            <p className="mt-2 text-base">{option.label}</p>
            <p className="mt-2 text-muted-foreground text-wrap text-[0.8rem]">
              {option.description}
            </p>
          </div>
        </Button>
      );
    }
  };

  return (
    <div className="w-[60%]">
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center space-x-2">
            <PhoneIcon className="h-4 w-4" />
            <h3 className="text-base font-semibold">
              Choose a roleplay type to get started
            </h3>
          </div>
          <p className="text-muted-foreground">
            You can choose from the following roleplay types
          </p>
        </div>
      </div>
      <Separator className="mt-4 mb-8" />
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
        {callTypeOptions.map(renderCallTypeButton)}
      </div>
    </div>
  );
}

export default SelectCallType;
