import { Separator } from '@/components/ui/separator';
import { CALL_TYPE_OPTIONS } from '../constants';
import CallTypeButton from './CallTypeButton';
import { PhoneIcon } from 'lucide-react';

export default function SelectCallType() {
  return (
    <div>
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center space-x-2">
            <PhoneIcon className="h-4 w-4" />
            <h3 className="text-base font-semibold">
              Choose a roleplay type to get started
            </h3>
          </div>
          <p className="text-muted-foreground">
            You can choose from the following roleplay types
          </p>
        </div>
      </div>
      <Separator className="mt-4 mb-8" />
      <div className="grid grid-cols-3 gap-3">
        {CALL_TYPE_OPTIONS.map((option, i) => (
          <CallTypeButton option={option} key={option.label + i} />
        ))}
      </div>
    </div>
  );
}
