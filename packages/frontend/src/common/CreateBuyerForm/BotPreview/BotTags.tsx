import {
  CheckIcon,
  ChevronDownIcon,
  Loader2Icon,
  PlusIcon,
  FilterX,
  XIcon,
} from 'lucide-react';
import React, { useContext, useEffect, useRef, useState, useMemo } from 'react';
import { Id, toast } from 'react-toastify';
import { useQueryClient } from '@tanstack/react-query';

import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import CreateNewTagDialog from '@/common/Buyers/ProdSite/multiactionsBar/tagsMenu/createNew';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { TagStatus } from '@/components/BuyerCard/AgentDropdownMenu';
import { TagDto } from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { TagsService } from '@/lib/Agent';
import useTags from '@/hooks/useTags';
import {
  Command,
  CommandInput,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

const BotTags: React.FC = () => {
  const { form, existingAgent: agent } = useContext(
    CreateBuyerBotEditFormContext,
  );
  const formTags = form.watch('tags');

  const [open, setOpen] = useState(false);
  const [searchString, setSearchString] = useState('');

  const { data: allTags } = useTags(true, 0, 0, '');
  const [selectedTagsStatus, setSelectedTagsStatus] = useState<TagStatus>({});
  const [loadingTags, setLoadingTags] = useState<{ [key: number]: boolean }>(
    {},
  );
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  const [isCreatenewDialogOpen, setIsCreatenewDialogOpen] =
    useState<boolean>(false);

  useEffect(() => {
    const _selectedTagsStatus: TagStatus = {};
    (agent?.tags || []).map((t) => {
      if (!_selectedTagsStatus[t.id]) {
        _selectedTagsStatus[t.id] = {
          add: false,
          delete: false,
          isNew: false,
        };
      }
    });
    setSelectedTagsStatus(_selectedTagsStatus);
  }, [agent]);

  const toggleTag = async (
    tag: TagDto,
    showSuccessToaster = false,
    fromCreateNewDialog = false,
  ) => {
    const currentTags = form.getValues().tags ?? [];
    const hasTag = currentTags.some((t) => t.id === tag.id);

    if (agent?.id) {
      // If agent exists, sync with API
      if (!selectedTagsStatus[tag.id]) {
        selectedTagsStatus[tag.id] = {
          add: true,
          delete: false,
          isNew: true,
        };
        saveToDb(
          tag.id,
          true,
          showSuccessToaster,
          fromCreateNewDialog ? tag : undefined,
        );
      } else {
        if (!hasTag) {
          selectedTagsStatus[tag.id].add = false;
          selectedTagsStatus[tag.id].delete = true;
          saveToDb(
            tag.id,
            true,
            showSuccessToaster,
            fromCreateNewDialog ? tag : undefined,
          );
        } else {
          selectedTagsStatus[tag.id].add = true;
          selectedTagsStatus[tag.id].delete = false;
          saveToDb(
            tag.id,
            false,
            showSuccessToaster,
            fromCreateNewDialog ? tag : undefined,
          );
        }
      }
      setSelectedTagsStatus({ ...selectedTagsStatus });
    } else {
      // If no agent, just update form
      const newTags = hasTag
        ? currentTags.filter((t) => t.id !== tag.id)
        : [...currentTags, tag];
      form.setValue('tags', newTags);
    }
  };

  const saveToDb = async (
    tagId: number,
    add: boolean,
    showSuccessToaster = false,
    newTag?: TagDto,
  ) => {
    setLoadingTags({ ...loadingTags, [tagId]: true });

    const currentTags = form.getValues().tags ?? [];
    const tag = newTag ?? allTags?.find((t) => t.id === tagId);
    if (!tag) return;

    // Update form value immediately
    const newTags = add
      ? [...currentTags, tag]
      : currentTags.filter((t) => t.id !== tagId);
    form.setValue('tags', newTags);

    const forAgents: number[] = [agent?.id || 6434];
    const addTags: number[] = [];
    const removeTags: number[] = [];

    if (add) {
      addTags.push(tagId);
    } else {
      removeTags.push(tagId);
    }

    let ok = false;
    try {
      TagsService.editTagsAgentsLinks({
        addTags,
        removeTags,
        forAgents,
      });
      ok = true;
    } catch (e: unknown) {
      // Revert form value on error
      console.error(e);
      form.setValue('tags', currentTags);
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving. Please try again.',
        );
      }
    }

    if (ok) {
      if (showSuccessToaster) {
        errorToastId.current = toast.success('Tag successfully added');
      }
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({
        queryKey: ['orgAgentsByTagsAndVariations'],
      });
      queryClient.invalidateQueries({ queryKey: ['orgAgentVariations'] });
      queryClient.invalidateQueries({ queryKey: ['tags'] });
    }
    setLoadingTags({ ...loadingTags, [tagId]: false });
  };

  const filterResults = (value: string) => {
    setSearchString(value);
  };

  const clearAll = () => {
    form.setValue('tags', [] as TagDto[]);
  };

  const filteredTags = useMemo(() => {
    if (!searchString.trim()) return allTags;
    return allTags?.filter((tag) =>
      tag.name.toLowerCase().includes(searchString.toLowerCase()),
    );
  }, [allTags, searchString]);

  const TagsDisplay = useMemo(
    () => () => {
      return (formTags ?? []).map((tag: TagDto) => (
        <div
          key={tag.id}
          className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md "
        >
          <span className="text-xs font-semibold flex items-center h-4 rounded-lg">
            #{tag.name}
            <span className="inline-flex pl-1">
              <XIcon
                className="w-3 h-3 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  toggleTag(tag);
                }}
              />
            </span>
          </span>
        </div>
      ));
    },
    [formTags],
  );
  return (
    <Form {...form}>
      <div className="w-full mt-2">
        <FormField
          control={form.control}
          name="tags"
          defaultValue={formTags}
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>Tags</FormLabel>
                <FormControl>
                  <div>
                    <Popover open={open} onOpenChange={setOpen}>
                      <PopoverTrigger
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                        className="flex w-full items-center justify-between rounded-md border border-input bg-background p-1 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <div className="flex flex-wrap items-center gap-2">
                          <TagsDisplay />
                          {!field.value?.length && (
                            <>
                              <PlusIcon className="w-4 h-4 text-muted-foreground" />
                              <span className="text-muted-foreground">
                                Add tags
                              </span>
                            </>
                          )}
                        </div>
                        <ChevronDownIcon className="h-4 w-4 text-muted-foreground" />
                      </PopoverTrigger>

                      <PopoverContent
                        className="p-0"
                        style={{ width: 'var(--radix-popover-trigger-width)' }}
                      >
                        <Command shouldFilter={false}>
                          <CommandInput
                            placeholder="Search tags..."
                            className="h-9"
                            value={searchString}
                            onValueChange={filterResults}
                          />
                          {Number(field?.value?.length) > 0 && (
                            <CommandGroup>
                              <CommandItem
                                onSelect={() => {
                                  clearAll();
                                  setOpen(false);
                                }}
                              >
                                <FilterX className="w-4 h-4 mr-2 text-muted-foreground" />
                                <span>Clear all tags</span>
                              </CommandItem>
                            </CommandGroup>
                          )}

                          <CommandSeparator />
                          <CommandList>
                            <CommandGroup heading="Tags">
                              {filteredTags?.map((t) => (
                                <CommandItem
                                  key={t.id}
                                  value={String(t.id)}
                                  onSelect={() => {
                                    toggleTag(t);
                                  }}
                                >
                                  <div
                                    className={cn(
                                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                                      field.value?.some(
                                        (tag) => tag.id === t.id,
                                      )
                                        ? 'bg-primary text-primary-foreground'
                                        : 'opacity-50 [&_svg]:invisible',
                                    )}
                                  >
                                    <CheckIcon className={cn('h-4 w-4')} />
                                  </div>
                                  <div className="flex space-x-2 items-center">
                                    <div className="capitalize flex-1">
                                      {t.name}
                                    </div>
                                    {loadingTags[t.id] && (
                                      <Loader2Icon
                                        className="animate-spin"
                                        size={14}
                                      />
                                    )}
                                  </div>
                                </CommandItem>
                              ))}

                              {filteredTags?.length === 0 && (
                                <CommandItem className="justify-center text-center">
                                  No tag found
                                </CommandItem>
                              )}
                            </CommandGroup>
                          </CommandList>

                          <CommandSeparator />

                          {/* Existing New Tag Option */}
                          <CommandGroup>
                            <CommandItem
                              onSelect={() => {
                                setOpen(false);
                                setIsCreatenewDialogOpen(true);
                              }}
                            >
                              <PlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                              <span>New tag</span>
                            </CommandItem>
                          </CommandGroup>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                </FormControl>
              </FormItem>
            );
          }}
        />
      </div>
      <CreateNewTagDialog
        open={isCreatenewDialogOpen}
        onClose={(t: TagDto | undefined) => {
          if (t) {
            toggleTag(t, true, true);
          }
          setIsCreatenewDialogOpen(false);
        }}
      />
    </Form>
  );
};

export default BotTags;
