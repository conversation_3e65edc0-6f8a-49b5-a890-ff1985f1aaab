import { Card } from '@/components/ui/card';
import BotProfile from './BotProfileDetails';
import FolderSelection from './FolderSelection';
import BotTagInput from './BotTags';
import AIBotCreatorFormBasicDetails from '../Main/AIBotCreatorForm/BasicDetails';
import { AgentCallType } from '@/lib/Agent/types';
import { Separator } from '@/components/ui/separator';
import GatekeeperTemplateSelector from './GatekeeperTemplateSelector';
import ResumeCallSelector from './ResumeCalls/ResumeCallSelector';
import useUserSession from '@/hooks/useUserSession';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { useContext, useMemo } from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import MultiPartyScenarioProfile from './MultiPartyScenarioProfile';
import ScenarioInput from './ScenarioInput';

function BotPreview() {
  const params = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const { useMultiParty } = useUserSession();
  const { isEditMode, callType, currentTab, setCurrentTab } = useContext(
    CreateBuyerBotEditFormContext,
  );
  const onTabChange = (t: 'individual' | 'multi-party') => {
    setCurrentTab(t);
    const newParams = new URLSearchParams(params);
    newParams.delete('tab');
    newParams.set('tab', t);
    router.replace(`${pathname}?${newParams.toString()}`);
  };
  const isValidCallTypeForMultiParty = useMemo(() => {
    return ![
      AgentCallType.COLD,
      AgentCallType.MANAGER_ONE_ON_ONE,
      AgentCallType.FOCUS,
    ].includes(callType);
  }, [callType]);

  return (
    <>
      {useMultiParty && isValidCallTypeForMultiParty ? (
        <>
          <Tabs defaultValue={currentTab} value={currentTab} className="w-full">
            {!isEditMode && (
              <div className="flex items-center mb-3 w-full">
                <TabsList className="w-full">
                  <TabsTrigger
                    className="w-full"
                    value="individual"
                    onClick={() => onTabChange('individual')}
                  >
                    Individual Bot
                  </TabsTrigger>
                  <TabsTrigger
                    className="w-full"
                    value="multi-party"
                    onClick={() => onTabChange('multi-party')}
                  >
                    Multi-party
                  </TabsTrigger>
                </TabsList>
              </div>
            )}

            {currentTab == 'individual' && (
              <div className="flex flex-col gap-3 mb-3">
                <Card className="self-stretch p-3 bg-white rounded-lg border border-zinc-200 flex-col justify-center items-start flex overflow-visible">
                  <div className=" text-zinc-500 text-sm font-medium  leading-tight mb-1">
                    Bot Preview
                  </div>
                  <BotProfile />

                  <FolderSelection />

                  <BotTagInput />
                </Card>

                <Card className="self-stretch pt-3 pb-4 bg-white rounded-lg shadow border border-zinc-200 flex-col justify-start items-start gap-4 flex overflow-auto">
                  <AIBotCreatorFormBasicDetails />

                  {![
                    AgentCallType.MANAGER_ONE_ON_ONE,
                    AgentCallType.FOCUS,
                  ].includes(callType) && (
                    <>
                      <Separator />
                      <GatekeeperTemplateSelector />
                      <Separator />
                    </>
                  )}

                  {callType !== AgentCallType.FOCUS && <ResumeCallSelector />}
                </Card>
              </div>
            )}
            {currentTab === 'multi-party' && (
              <>
                <div className="flex flex-col gap-3 mb-3">
                  <Card className="w-full p-3 bg-white rounded-lg border border-zinc-200 flex-col justify-center items-start flex overflow-visible">
                    <div className=" text-zinc-500 text-sm font-medium  leading-tight mb-1">
                      Scenario Preview
                    </div>
                    <MultiPartyScenarioProfile />
                    <ScenarioInput />
                    <FolderSelection />
                    <BotTagInput />
                  </Card>

                  <Card className="self-stretch pt-3 pb-4 bg-white rounded-lg shadow border border-zinc-200 flex-col justify-start items-start gap-4 flex overflow-auto">
                    <AIBotCreatorFormBasicDetails />
                  </Card>
                </div>
              </>
            )}
          </Tabs>
        </>
      ) : (
        <>
          <Card className="self-stretch p-3 bg-white rounded-lg border border-zinc-200 flex-col justify-center items-start flex overflow-visible">
            <div className=" text-zinc-500 text-sm font-medium  leading-tight mb-1">
              Bot Preview
            </div>
            <BotProfile />

            <FolderSelection />

            <BotTagInput />
          </Card>

          <Card className="self-stretch pt-3 pb-4 bg-white rounded-lg shadow border border-zinc-200 flex-col justify-start items-start gap-4 flex overflow-auto">
            <AIBotCreatorFormBasicDetails />

            {![AgentCallType.MANAGER_ONE_ON_ONE, AgentCallType.FOCUS].includes(
              callType,
            ) && (
              <>
                <Separator />
                <GatekeeperTemplateSelector />
                <Separator />
              </>
            )}

            {callType !== AgentCallType.FOCUS && <ResumeCallSelector />}
          </Card>
        </>
      )}
    </>
  );
}

export default BotPreview;
