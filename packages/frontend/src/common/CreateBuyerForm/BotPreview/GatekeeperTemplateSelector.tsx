import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import useAgentGatekeepersTemplates from '@/hooks/useAgentGatekeeperTemplates';
import {
  AgentCallType,
  AgentDto,
  GatekeeperTemplateDto,
} from '@/lib/Agent/types';
import React, { useState, useMemo, useContext, useEffect } from 'react';
import { getResearchData } from '../Main/utils';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';

export const getFirstName = (name?: string): string | undefined =>
  name?.split(' ')?.[0];

const NoneTemplate = {
  id: 0,
  firstName: 'None',
  lastName: '',
  callScenario: '',
  context: '',
  orgId: -1,
  shareWithAllOrgs: false,
  scorecardConfigId: -1,
};

const defaultStringValue = (value?: number) =>
  value ? String(value) : undefined;

const GatekeeperTemplateSelector = () => {
  const { form, existingAgent } = useContext(CreateBuyerBotEditFormContext);

  const { data: scorecardConfigOptions } = useScorecardConfigsForOrg();
  const { data: fetchedTemplates } = useAgentGatekeepersTemplates();

  const gatekeeperTemplates = useMemo(
    () => [NoneTemplate, ...(fetchedTemplates || [])],
    [fetchedTemplates, existingAgent],
  );

  const changeGKScorecard = (value: string) => {
    if (value) {
      const prev =
        form.getValues().gatekeepers || ({} as GatekeeperTemplateDto);
      prev.scorecardConfigId = parseInt(value);
      setStateAgent(prev as GatekeeperTemplateDto);
    }
  };

  const [templateIndex, settemplateIndex] = useState(-1);
  const [stateAgent, setStateAgent] = useState<GatekeeperTemplateDto>();

  const gatekeepers = useMemo<GatekeeperTemplateDto[]>(() => {
    const updatedGatekeeperTemplates = [...gatekeeperTemplates];

    existingAgent?.gatekeepers?.forEach((gk) => {
      const researchInfo = getResearchData(gk);
      const firstName = getFirstName(researchInfo?.gatekeeper_name);

      const tempIndex =
        gatekeeperTemplates?.findIndex(
          // TODO: don't use name to match
          (template) => template.firstName === firstName,
        ) ?? -1;

      settemplateIndex(tempIndex);

      if (tempIndex !== -1) {
        setStateAgent(gatekeeperTemplates?.[tempIndex]);
        updatedGatekeeperTemplates[tempIndex] = {
          ...updatedGatekeeperTemplates[tempIndex],
          scorecardConfigId: gk.scorecardConfigId,
          orgId: gk.orgId,
        };
        changeGKScorecard(
          String(gatekeeperTemplates?.[tempIndex].scorecardConfigId),
        );
      }
    });

    return updatedGatekeeperTemplates;
  }, [gatekeeperTemplates]);

  // Filtered scorecard options based on call type
  const filteredScorecardGatekeeperConfigOptions = useMemo(() => {
    if (!scorecardConfigOptions) {
      return [];
    }
    return scorecardConfigOptions.filter((sc) => {
      return sc?.callTypes
        ?.map((c) => c.callType)
        .includes(AgentCallType.GATEKEEPER);
    });
  }, [scorecardConfigOptions]);

  const checkGatekeeperNotNonTempalte = (
    value?: GatekeeperTemplateDto | AgentDto,
  ) => {
    return (
      value?.id !== NoneTemplate.id &&
      value?.firstName !== NoneTemplate.firstName
    );
  };

  useEffect(() => {
    form.setValue('gatekeepers', stateAgent);
  }, [stateAgent]);

  useEffect(() => {
    changeGKScorecard(String(existingAgent?.gatekeepers[0]?.scorecardConfigId));
  }, [existingAgent?.gatekeepers[0]?.scorecardConfigId]);

  return (
    <Form {...form}>
      <div className="px-3 flex-col justify-start items-start gap-2 flex mb-0">
        <FormLabel>Gatekeeper</FormLabel>
        <p className="text-muted-foreground mt-2 mb-2">
          Assign a gatekeeper for this buyer. Select a template and a gatekeeper
          bot will be created to answer any call for this buyer.
        </p>

        <div className="flex items-center w-full">
          <FormField
            control={form.control}
            name="gatekeepers"
            render={({ field }) => {
              return (
                <FormItem className="w-full">
                  <Select
                    onValueChange={(value: string) => {
                      const scorecardConfigId = field.value?.scorecardConfigId;
                      gatekeepers?.map((gk: GatekeeperTemplateDto, i) => {
                        if (String(gk.id) === value) {
                          settemplateIndex(Number(i));
                          setStateAgent({
                            ...gatekeeperTemplates?.[i],
                            scorecardConfigId,
                          });
                        }
                      });
                    }}
                    value={defaultStringValue(gatekeepers[templateIndex]?.id)}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Choose a template" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {gatekeepers?.map((gk: GatekeeperTemplateDto) => (
                        <SelectItem key={gk.id} value={String(gk.id)}>
                          {gk.firstName} {gk.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              );
            }}
          />
        </div>
        {stateAgent && filteredScorecardGatekeeperConfigOptions && (
          <div className="w-full">
            <FormField
              control={form.control}
              name="gatekeepers"
              render={({ field }) => {
                return checkGatekeeperNotNonTempalte(field?.value) ? (
                  <FormItem className="w-full">
                    <Select
                      onValueChange={changeGKScorecard}
                      value={defaultStringValue(stateAgent?.scorecardConfigId)}
                      defaultValue={defaultStringValue(
                        existingAgent?.gatekeepers[0]?.scorecardConfigId ||
                          stateAgent?.scorecardConfigId,
                      )}
                    >
                      <FormControl>
                        <SelectTrigger disabled={field?.value == NoneTemplate}>
                          <SelectValue placeholder="Choose a scorecard" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <>
                          {filteredScorecardGatekeeperConfigOptions.map(
                            (option) => {
                              return (
                                <SelectItem
                                  key={option.id}
                                  value={String(option.id)}
                                >
                                  {option.tag}
                                </SelectItem>
                              );
                            },
                          )}
                        </>
                      </SelectContent>
                    </Select>
                  </FormItem>
                ) : (
                  <></>
                );
              }}
            />
          </div>
        )}
      </div>
    </Form>
  );
};

export default GatekeeperTemplateSelector;
