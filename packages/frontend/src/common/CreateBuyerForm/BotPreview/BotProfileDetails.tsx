import React, { useContext } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import BotBadges from './BotBadges'; // Import the BotBadges component
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import AgentAvatar from '@/components/Avatars/Agent';

const BotProfile = () => {
  const { form, callType } = useContext(CreateBuyerBotEditFormContext);
  const {
    firstName,
    lastName,
    jobTitle: _jobTitle,
    emotionalState: _emotionalState,
    companyName: _companyName,
  } = form.getValues();

  const jobTitle = form.watch('jobTitle');
  const emotionalState = form.watch('emotionalState');
  const companyName = form.watch('companyName');

  return (
    <div className="flex-col justify-start items-start gap-3 flex">
      <div className="justify-start items-center gap-4 inline-flex">
        <AgentAvatar />

        <div className="flex-col justify-start items-start gap-1 inline-flex">
          <div className="self-stretch justify-start items-center gap-2 inline-flex">
            <div className="justify-start items-center gap-1 flex">
              {firstName || lastName ? (
                <div className="text-[#2e3035] text-base font-semibold  leading-normal">
                  {firstName || ''} {lastName || ''}
                </div>
              ) : (
                <Skeleton className="w-24 h-4" />
              )}
            </div>
            <div className="px-[3px] bg-gradient-to-b from-[#3dc3e6] via-[#49c8cf] to-[#36c4bf] rounded justify-start items-center flex">
              <div className="text-white text-xs font-medium w-3 h-4 ">AI</div>
            </div>
          </div>

          {jobTitle || companyName ? (
            <div className="justify-center items-start gap-1 inline-flex">
              <div className="text-zinc-500 text-sm font-normal  leading-tight">
                {jobTitle || ''}
              </div>
              <div className="text-zinc-500 text-sm font-normal  leading-tight">
                {companyName ? (
                  ` @ ${companyName}`
                ) : (
                  <span>
                    <Skeleton className="w-32 h-4 mt-1" />
                  </span>
                )}
              </div>
            </div>
          ) : (
            <Skeleton className="w-32 h-4 mt-1" />
          )}
        </div>
      </div>

      <BotBadges callType={callType} emotionalState={emotionalState} />
    </div>
  );
};

export default BotProfile;
