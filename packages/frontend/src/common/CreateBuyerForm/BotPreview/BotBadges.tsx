import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

import { AgentCallType } from '@/lib/Agent/types';
import EmotionBadge from './EmotionBadge';
import CalltypeBadge from './CalltypeBadge';
interface BotBadgesProps {
  callType: AgentCallType;
  emotionalState?: string;
}

const BotBadges: React.FC<BotBadgesProps> = ({ callType, emotionalState }) => {
  const renderContentOrSkeleton = (content: React.ReactNode) => {
    return content ? content : <Skeleton className="w-32 h-4" />;
  };
  return (
    <div className="px-14 justify-center items-start gap-2 inline-flex">
      {renderContentOrSkeleton(<CalltypeBadge callType={callType} />)}
      {renderContentOrSkeleton(
        <EmotionBadge emotionalState={emotionalState} />,
      )}
    </div>
  );
};

export default BotBadges;
