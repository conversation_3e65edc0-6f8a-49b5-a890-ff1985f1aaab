import { Card } from '@/components/ui/card';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import _ from 'lodash';

import { useContext } from 'react';
import { CreateBuyerBotContext } from '../../../contexts/CreateBuyerBotContext';

import FolderSelection from './FolderSelection';
import BotProfile from './BotProfileDetails';
import BotTagInput from './BotTags';
import GatekeeperTemplateSelector from './GatekeeperTemplateSelector';
import ResumeCallSelector from './ResumeCalls/ResumeCallSelector';
import { Separator } from '@/components/ui/separator';
import AIBotCreatorFormBasicDetails from '../Main/AIBotCreatorForm/BasicDetails';
import { FeatureFlagsContext } from '@/contexts/FeatureFlagsContext';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { AgentCallType } from '@/lib/Agent/types';
import useUserSession from '@/hooks/useUserSession';
import BotPreview from './BotPreview';

function BuyerBotPreview() {
  const {
    flags: { dev },
  } = useContext(FeatureFlagsContext);
  const { forms, defaultValues } = useContext(CreateBuyerBotContext);

  const { data: scorecardConfigOptions } = useScorecardConfigsForOrg();

  const { scorecardConfigId } =
    forms.main?.getValues() || defaultValues?.main || {};

  let selectedScorecardName = '';

  if (scorecardConfigId && scorecardConfigOptions) {
    for (const o of scorecardConfigOptions) {
      if (o.id) {
        if (String(o.id) == scorecardConfigId) {
          selectedScorecardName = o.tag;
        }
      }
    }
  }

  return (
    <div className="w-full flex-col justify-start items-start gap-3 inline-flex pl-3">
      <BotPreview />
    </div>
  );
}

export default BuyerBotPreview;
