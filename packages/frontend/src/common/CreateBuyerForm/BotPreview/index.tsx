import { Card } from '@/components/ui/card';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import _ from 'lodash';

import { useContext } from 'react';
import { CreateBuyerBotContext } from '../../../contexts/CreateBuyerBotContext';

import FolderSelection from './FolderSelection';
import BotProfile from './BotProfileDetails';
import BotTagInput from './BotTags';
import GatekeeperTemplateSelector from './GatekeeperTemplateSelector';
import ResumeCallSelector from './ResumeCalls/ResumeCallSelector';
import { Separator } from '@/components/ui/separator';
import AIBotCreatorFormBasicDetails from '../Main/AIBotCreatorForm/BasicDetails';
import { FeatureFlagsContext } from '@/contexts/FeatureFlagsContext';

function BuyerBotPreview() {
  const {
    flags: { dev },
  } = useContext(FeatureFlagsContext);
  const { forms, defaultValues } = useContext(CreateBuyerBotContext);

  const { data: scorecardConfigOptions } = useScorecardConfigsForOrg();

  const { scorecardConfigId } =
    forms.main?.getValues() || defaultValues?.main || {};

  let selectedScorecardName = '';

  if (scorecardConfigId && scorecardConfigOptions) {
    for (const o of scorecardConfigOptions) {
      if (o.id) {
        if (String(o.id) == scorecardConfigId) {
          selectedScorecardName = o.tag;
        }
      }
    }
  }

  return (
    <div className=" py-3 flex-col justify-start items-start gap-3 inline-flex">
      <Card className="self-stretch  p-3 bg-white rounded-lg border border-zinc-200 flex-col justify-center items-start gap-3 flex">
        <div className=" text-zinc-500 text-sm font-medium  leading-tight">
          Bot Preview
        </div>
        <BotProfile />

        <FolderSelection />

        <BotTagInput />
      </Card>

      <Card className="self-stretch pt-3 pb-4 bg-white rounded-lg shadow border border-zinc-200 flex-col justify-start items-start gap-4 flex">
        <AIBotCreatorFormBasicDetails />
        <Separator />
        <GatekeeperTemplateSelector />
        <Separator />
        <ResumeCallSelector />
      </Card>
    </div>
  );
}

export default BuyerBotPreview;
