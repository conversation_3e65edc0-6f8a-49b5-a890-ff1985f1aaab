import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import {
  AgentCallType,
  AgentDto,
  AgentLanguage,
  AgentLanguagesLabels,
} from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import _ from 'lodash';
import {
  AudioLinesIcon,
  BrainIcon,
  Building2Icon,
  CheckCircleIcon,
  FlagTriangleRightIcon,
  LightbulbIcon,
  Settings2Icon,
  TargetIcon,
  User2Icon,
  XCircleIcon,
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useContext } from 'react';
import { CreateBuyerBotContext } from '../../../contexts/CreateBuyerBotContext';
import {
  AdvancedSettingsFormSchema,
  BasicDetailsFormSchema,
  CompanyDetailsFormSchema,
  OpinionsFormSchema,
  PersonalDetailsFormSchema,
  PrioritiesAndObjectionsFormSchema,
  ScorecardFormSchema,
  getZodSchemaFieldsShallow,
} from '../Main/utils';

import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
  VOICE_OPTIONS,
} from '../constants';
import useUserSession from '@/hooks/useUserSession';
import AgentAvatar from '@/components/Avatars/Agent';

function BuyerBotPreview() {
  const { forms, defaultValues } = useContext(CreateBuyerBotContext);
  const searchParams = useSearchParams();

  const callType = searchParams.get('callType') as AgentCallType;

  const { data: scorecardConfigOptions } = useScorecardConfigsForOrg();
  const { CALL_SCENARIO_OPTIONS } = useUserSession();

  const {
    firstName,
    lastName,
    gender,
    avatarUrl,
    jobTitle,
    personalDetails,
    voice,
    emotionalState,
    companyName,
    companyDetails,
    companyOrgStructure,
    callScenario,
    description,
    incumbent_solution_info,
    problem_aware_info,
    solution_aware_info,
    pre_existing_champion_info,
    public_presence,
    warm_call_context,
    discovery_call_context,
    scorecardConfigId,
    goals,
    opinions,
    objections,
    language,
  } = forms.main?.getValues() || defaultValues?.main || {};

  let selectedScorecardName = '';

  if (scorecardConfigId && scorecardConfigOptions) {
    for (const o of scorecardConfigOptions) {
      if (o.id) {
        if (String(o.id) == scorecardConfigId) {
          selectedScorecardName = o.tag;
        }
      }
    }
  }

  const isBasicDetailsValid = BasicDetailsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(BasicDetailsFormSchema),
    ),
  ).success;
  const isPersonalDetailsValid = PersonalDetailsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(PersonalDetailsFormSchema),
    ),
  ).success;
  const isCompanyDetailsValid = CompanyDetailsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(CompanyDetailsFormSchema),
    ),
  ).success;
  const isPrioritiesAndObjectionsValid =
    PrioritiesAndObjectionsFormSchema.safeParse(
      _.pick(
        forms.main?.getValues() || defaultValues.main,
        getZodSchemaFieldsShallow(PrioritiesAndObjectionsFormSchema),
      ),
    ).success;
  const isOpinionsValid = OpinionsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(OpinionsFormSchema),
    ),
  ).success;
  const isScorecardValid = ScorecardFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(ScorecardFormSchema),
    ),
  ).success;
  const isAdvancedSettingsValid = AdvancedSettingsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(AdvancedSettingsFormSchema),
    ),
  ).success;

  // @ts-ignore
  const Icon = CALL_TYPE_TO_ICON?.[callType as AgentCallType]?.Icon;

  return (
    <div className="w-full pt-6 pb-12 px-4 max-h-screen overflow-y-scroll">
      <h3 className="text-base font-semibold">Buyer Bot Preview</h3>
      <div>
        <Card
          className={cn('mt-4', {
            'border-red-200': !isPersonalDetailsValid || !isBasicDetailsValid,
          })}
        >
          <div className="flex justify-between items-center pr-4">
            <CardHeader className="w-full">
              <CardTitle className="flex justify-between w-full">
                <div className="flex items-center">
                  <User2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                  Basic &amp; Personal Details
                </div>
                {isBasicDetailsValid && isPersonalDetailsValid ? (
                  <CheckCircleIcon className="text-green-500" />
                ) : (
                  <XCircleIcon className="text-red-500" />
                )}
              </CardTitle>
              <CardDescription className="text-primary mt-4">
                <div className="flex flex-wrap">
                  <span className="font-medium mr-1">Instructions: </span>
                  <span className="break-all">{description}</span>
                </div>
              </CardDescription>
            </CardHeader>
            {/* <div className="flex items-center space-x-0">
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={"ghost"}
                      onClick={() => {
                        router.push(
                          `${
                            isEditMode
                              ? `${baseRoute}/${params?.id}/edit/main`
                              : `${baseRoute}/create/main`
                          }${queryString}`
                        );
                      }}
                      className="p-2 text-muted-foreground"
                      // disabled={stepNum === 1 || stepNum === 0}
                    >
                      <Edit2Icon className="w-4 h-4 mr-2" /> Edit
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Edit</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div> */}
          </div>
          <CardContent className="text-md px-8">
            <div className="flex items-center space-x-4">
              <AgentAvatar
                className="w-16 h-16"
                agent={
                  {
                    firstName: firstName || '',
                    lastName: lastName || '',
                    avatarUrl: avatarUrl,
                  } as AgentDto
                }
              />
              <div>
                {firstName || lastName ? (
                  <p className="text-base font-medium">
                    {firstName || ''} {lastName || ''}
                  </p>
                ) : (
                  <Skeleton
                    className={cn('w-24 h-4', {
                      // hidden: stepNum !== 1 && stepNum !== 0,
                    })}
                  />
                )}
                {jobTitle || companyName ? (
                  <p className="text-sm text-muted-foreground">
                    {jobTitle || ''}
                    {companyName ? (
                      ` @ ${companyName}`
                    ) : (
                      <span
                        className={cn({
                          // hidden: stepNum !== 2,
                        })}
                      >
                        {' '}
                        @ <Skeleton className={cn('w-32 h-4 mt-1')} />
                      </span>
                    )}
                  </p>
                ) : (
                  <Skeleton
                    className={cn('w-32 h-4 mt-1', {
                      // hidden: stepNum !== 1 && stepNum !== 0,
                    })}
                  />
                )}
              </div>
            </div>
            <div className="mt-4">
              {gender && (
                <div
                  className={cn('flex mt-2', {
                    'space-x-1': gender,
                  })}
                >
                  {gender ? (
                    <Badge variant="secondary">
                      {_.capitalize(gender.toLowerCase()) || ''}
                    </Badge>
                  ) : (
                    <Skeleton
                      className={cn('w-16 h-6 mr-1', {
                        // hidden: stepNum !== 1 && stepNum !== 0,
                      })}
                    />
                  )}
                  <Badge variant="outline">
                    <AudioLinesIcon className="w-4 h-4 mr-1" />
                    {
                      VOICE_OPTIONS[gender].find(
                        (option) => option.value === voice,
                      )?.label
                    }
                  </Badge>
                  {emotionalState ? (
                    <Badge variant="default">
                      <BrainIcon className="w-3 h-3 mr-1" />{' '}
                      {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                        (item) => item.value === emotionalState,
                      )?.label ||
                        emotionalState ||
                        ''}
                    </Badge>
                  ) : (
                    <Skeleton
                      className={cn('w-32 h-4', {
                        // hidden: stepNum !== 3,
                      })}
                    />
                  )}
                  {callType ? (
                    <Badge className="bg-teal-600 text-white" variant="default">
                      {Icon && <Icon className="mr-1 h-3 w-3" />}
                      {
                        CALL_TYPE_OPTIONS.find(
                          (option) => option.value === callType,
                        )?.label
                      }
                    </Badge>
                  ) : (
                    <Skeleton
                      className={cn('w-32 h-4', {
                        // hidden: stepNum !== 3,
                      })}
                    />
                  )}
                </div>
              )}
            </div>
            <p className="font-medium mt-4">Details *</p>
            {personalDetails && personalDetails?.length > 0 ? (
              <ul className="list-decimal pl-6 mt-2">
                {personalDetails.map((point, i) => (
                  <li key={i}>{point}</li>
                ))}
              </ul>
            ) : (
              <div
                className={cn('space-y-2', {
                  // hidden: stepNum !== 1 && stepNum !== 0,
                })}
              >
                <Skeleton className="w-32 h-4" />
                <Skeleton className="w-32 h-4" />
              </div>
            )}
            {callType === AgentCallType.WARM && warm_call_context && (
              <div className="space-y-3 mt-4">
                <p className="font-medium">Warm Call Context *</p>
                <p>{warm_call_context}</p>
              </div>
            )}
            {callType === AgentCallType.DISCOVERY && discovery_call_context && (
              <div className="space-y-3 mt-4">
                <p className="font-medium">Discovery Call Context *</p>
                <p>{discovery_call_context}</p>
              </div>
            )}
          </CardContent>
        </Card>
        <Card
          className={cn('mt-4', {
            'border-red-200': !isCompanyDetailsValid,
          })}
        >
          <div className="flex justify-between items-center pr-4">
            <CardHeader className="w-full">
              <CardTitle className="flex justify-between w-full">
                <div className="flex items-center">
                  <Building2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                  Company Details
                </div>
                {isCompanyDetailsValid ? (
                  <CheckCircleIcon className="text-green-500" />
                ) : (
                  <XCircleIcon className="text-red-500" />
                )}
              </CardTitle>
            </CardHeader>
            {/* <div className="flex items-center space-x-0">
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={"ghost"}
                      onClick={() => {
                        router.push(
                          `${
                            isEditMode
                              ? `${baseRoute}/${params?.id}/edit/2`
                              : `${baseRoute}/create/2`
                          }${queryString}`
                        );
                      }}
                      className="p-2 text-muted-foreground"
                      // disabled={stepNum === 2}
                    >
                      <Edit2Icon className="w-4 h-4 mr-2" /> Edit
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Edit</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
                    </div> */}
          </div>
          <CardContent className="text-md px-8">
            <p className="font-medium">Details *</p>
            {companyDetails && companyDetails?.length > 0 ? (
              <ul className="list-decimal pl-6 mt-2">
                {companyDetails.map((point, i) => (
                  <li key={i}>{point}</li>
                ))}
              </ul>
            ) : (
              <div
                className={cn('space-y-2 mt-2 pl-6', {
                  // hidden: stepNum !== 2,
                })}
              >
                <Skeleton className="w-32 h-4" />
                <Skeleton className="w-32 h-4" />
              </div>
            )}
            <p className="font-medium mt-4">Org structure *</p>
            {companyOrgStructure && companyOrgStructure?.length > 0 ? (
              <ul className="list-decimal pl-6 mt-2">
                {companyOrgStructure.map((point, i) => (
                  <li key={i}>{point}</li>
                ))}
              </ul>
            ) : (
              <div
                className={cn('space-y-2 mt-2 pl-6', {
                  // hidden: stepNum !== 2,
                })}
              >
                <Skeleton className="w-32 h-4" />
                <Skeleton className="w-32 h-4" />
              </div>
            )}
          </CardContent>
        </Card>
        <Card
          className={cn('mt-4', {
            'border-red-200': !isPrioritiesAndObjectionsValid,
          })}
        >
          <div className="flex justify-between items-center pr-4">
            <CardHeader className="w-full">
              <CardTitle className="flex justify-between w-full">
                <div className="flex items-center">
                  <FlagTriangleRightIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  Priorities &amp; Objections
                </div>
                {isPrioritiesAndObjectionsValid ? (
                  <CheckCircleIcon className="text-green-500" />
                ) : (
                  <XCircleIcon className="text-red-500" />
                )}
              </CardTitle>
            </CardHeader>

            {/* <div className="flex items-center space-x-0">
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={"ghost"}
                      onClick={() => {
                        router.push(
                          `${
                            isEditMode
                              ? `${baseRoute}/${params?.id}/edit/5`
                              : `${baseRoute}/create/5`
                          }${queryString}`
                        );
                      }}
                      className="p-2 text-muted-foreground"
                      // disabled={stepNum === 5}
                    >
                      <Edit2Icon className="w-4 h-4 mr-2" /> Edit
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Edit</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
                    </div> */}
          </div>
          <CardContent className="text-md px-8">
            <p className="font-medium">Priorities</p>
            {goals && goals?.length > 0 ? (
              <ul className="list-decimal pl-6 mt-2">
                {(goals || []).map((point, i) => (
                  <li key={i}>{point}</li>
                ))}
              </ul>
            ) : (
              <div
                className={cn('space-y-2 mt-2 pl-6', {
                  // hidden: stepNum !== 4,
                })}
              >
                <Skeleton className="w-32 h-4" />
                <Skeleton className="w-32 h-4" />
              </div>
            )}
            <p className="font-medium mt-4">Objections *</p>
            {objections && objections?.length > 0 ? (
              <ul className="list-decimal pl-6 mt-2">
                {(objections || []).map((point, i) => (
                  <li key={i}>{point}</li>
                ))}
              </ul>
            ) : (
              <div
                className={cn('space-y-2 mt-2 pl-6', {
                  // hidden: stepNum !== 4,
                })}
              >
                <Skeleton className="w-32 h-4" />
                <Skeleton className="w-32 h-4" />
              </div>
            )}
          </CardContent>
        </Card>
        <Card
          className={cn('mt-4', {
            'border-red-200': !isOpinionsValid,
          })}
        >
          <div className="flex justify-between items-center pr-4">
            <CardHeader className="w-full">
              <CardTitle className="flex justify-between w-full">
                <div className="flex items-center">
                  <LightbulbIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  Opinions
                </div>
                {isOpinionsValid ? (
                  <CheckCircleIcon className="text-green-500" />
                ) : (
                  <XCircleIcon className="text-red-500" />
                )}
              </CardTitle>
            </CardHeader>

            {/* <div className="flex items-center space-x-0">
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={"ghost"}
                      onClick={() => {
                        router.push(
                          `${
                            isEditMode
                              ? `${baseRoute}/${params?.id}/edit/5`
                              : `${baseRoute}/create/5`
                          }${queryString}`
                        );
                      }}
                      className="p-2 text-muted-foreground"
                      // disabled={stepNum === 5}
                    >
                      <Edit2Icon className="w-4 h-4 mr-2" /> Edit
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Edit</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div> */}
          </div>
          <CardContent className="text-md px-8">
            <p className="font-medium">Call scenario *</p>
            {callScenario ? (
              <Badge variant="outline" className="mt-2">
                {
                  /* @ts-ignore */
                  CALL_SCENARIO_OPTIONS[callType || AgentCallType.COLD].find(
                    (option: any) => option.value === callScenario,
                  )?.label
                }
              </Badge>
            ) : (
              <Skeleton
                className={cn('w-36 h-4 mt-2', {
                  // hidden: stepNum !== 3,
                })}
              />
            )}
            <p className="font-medium mt-4">General opinions *</p>
            {opinions && opinions?.length > 0 ? (
              <ul className="list-decimal pl-6 mt-2">
                {(opinions || []).map((point, i) => (
                  <li key={i}>{point}</li>
                ))}
              </ul>
            ) : (
              <div
                className={cn('space-y-2 mt-2 pl-6', {
                  // hidden: stepNum !== 4,
                })}
              >
                <Skeleton className="w-32 h-4" />
                <Skeleton className="w-32 h-4" />
              </div>
            )}
            {(callScenario === 'Is aware of problem' ||
              callScenario === 'Is aware of solution' ||
              callScenario === 'Uses a Competitor' ||
              callScenario === 'Pre-existing champion') && (
              <div className="mt-4">
                <div className="space-y-3">
                  <p className="font-medium">
                    What does the buyer know about the problem?
                  </p>
                  {problem_aware_info ? (
                    <p>{problem_aware_info}</p>
                  ) : (
                    <Skeleton
                      className={cn('w-32 h-4', {
                        // hidden: stepNum !== 3,
                      })}
                    />
                  )}
                </div>
              </div>
            )}
            {callScenario === 'Is aware of solution' && (
              <div className="mt-4">
                <div className="space-y-3">
                  <p className="font-medium">
                    What does the buyer know about the solution?
                  </p>
                  {solution_aware_info ? (
                    <p>{solution_aware_info}</p>
                  ) : (
                    <Skeleton
                      className={cn('w-32 h-4', {
                        // hidden: stepNum !== 3,
                      })}
                    />
                  )}
                </div>
              </div>
            )}
            {callScenario === 'Uses a Competitor' && (
              <div className="mt-4">
                <div className="space-y-3">
                  <p className="font-medium">
                    What is the buyer&apos;s existing solution and what do they
                    think of it?
                  </p>
                  {incumbent_solution_info ? (
                    <p>{incumbent_solution_info}</p>
                  ) : (
                    <Skeleton
                      className={cn('w-32 h-4', {
                        // hidden: stepNum !== 3,
                      })}
                    />
                  )}
                </div>
              </div>
            )}
            {callScenario === 'Pre-existing champion' && (
              <div className="space-y-3 mt-4">
                <p className="font-medium">
                  When did the buyer previously champion your product and what
                  did they think about it?
                </p>
                {pre_existing_champion_info ? (
                  <p>{pre_existing_champion_info}</p>
                ) : (
                  <Skeleton
                    className={cn('w-32 h-4', {
                      // hidden: stepNum !== 3,
                    })}
                  />
                )}
              </div>
            )}
          </CardContent>
        </Card>
        <Card
          className={cn('mt-4', {
            'border-red-200': !isScorecardValid,
          })}
        >
          <div className="flex justify-between items-center pr-4">
            <CardHeader className="w-full">
              <CardTitle className="flex justify-between w-full">
                <div className="flex items-center">
                  <TargetIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  Scorecard
                </div>
                {isScorecardValid ? (
                  <CheckCircleIcon className="text-green-500" />
                ) : (
                  <XCircleIcon className="text-red-500" />
                )}
              </CardTitle>
            </CardHeader>

            {/* <div className="flex items-center space-x-0">
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={"ghost"}
                      onClick={() => {
                        router.push(
                          `${
                            isEditMode
                              ? `${baseRoute}/${params?.id}/edit/3`
                              : `${baseRoute}/create/3`
                          }${queryString}`
                        );
                      }}
                      className="p-2 text-muted-foreground"
                      // disabled={stepNum === 3}
                    >
                      <Edit2Icon className="w-4 h-4 mr-2" /> Edit
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Edit</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div> */}
          </div>
          <CardContent className="text-md space-y-3 px-8">
            <div className="space-y-1">
              <p className="font-medium">Scorecard *</p>
              <p>{selectedScorecardName}</p>
            </div>
          </CardContent>
        </Card>
        <Card
          className={cn('mt-4', {
            'border-red-200': !isAdvancedSettingsValid,
          })}
        >
          <div className="flex justify-between items-center pr-4">
            <CardHeader className="w-full">
              <CardTitle className="flex justify-between w-full">
                <div className="flex items-center">
                  <Settings2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                  Advanced Settings
                </div>
                {isAdvancedSettingsValid ? (
                  <CheckCircleIcon className="text-green-500" />
                ) : (
                  <XCircleIcon className="text-red-500" />
                )}
              </CardTitle>
            </CardHeader>
            {/* 
            <div className="flex items-center space-x-0">
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={"ghost"}
                      onClick={() => {
                        router.push(
                          `${
                            isEditMode
                              ? `${baseRoute}/${params?.id}/edit/4`
                              : `${baseRoute}/create/4`
                          }${queryString}`
                        );
                      }}
                      className="p-2 text-muted-foreground"
                      // disabled={stepNum === 4}
                    >
                      <Edit2Icon className="w-4 h-4 mr-2" /> Edit
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Edit</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div> */}
          </div>
          <CardContent className="text-md px-8">
            <div className="space-y-1">
              <p className="font-medium">Language</p>
              <p>{AgentLanguagesLabels[language || AgentLanguage.EN_US]}</p>
            </div>
            <div className="mt-4">
              <div className="space-y-3">
                <p className="font-medium">
                  What did you rep discover about this buyer during their
                  research?
                </p>
                {public_presence && <p>{public_presence}</p>}
              </div>
            </div>
            {/* {callType !== AgentCallType.DISCOVERY &&
            callType !== AgentCallType.WARM ? (
              <>
                <div className="space-y-3 mt-4">
                  <p className="font-medium">Warm Call Context</p>
                  <p>{warm_call_context}</p>
                </div>
                <div className="space-y-3 mt-4">
                  <p className="font-medium">Discovery Call Context</p>
                  <p>{discovery_call_context}</p>
                </div>
              </>
            ) : callType === AgentCallType.DISCOVERY ? (
              <div className="space-y-3 mt-4">
                <p className="font-medium">Warm Call Context</p>
                <p>{warm_call_context}</p>
              </div>
            ) : (
              <div className="space-y-3 mt-4">
                <p className="font-medium">Discovery Call Context</p>
                <p>{discovery_call_context}</p>
              </div>
            )} */}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default BuyerBotPreview;
