import { AvatarComponent } from '@/components/Avatars/AvatarComponent';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { useContext } from 'react';
import CalltypeBadge from './CalltypeBadge';
import { AgentDto } from '@/lib/Agent/types';

export default function MultiPartyScenarioProfile() {
  const { form, callType, supportingAgentInfo } = useContext(
    CreateBuyerBotEditFormContext,
  );
  const primaryBotAvatar = form?.watch('avatarUrl');
  return (
    <div className="flex flex-row w-full justify-between items-center my-3">
      <div className="flex">
        <div className="-ml-0">
          <AvatarComponent
            imageUrl={primaryBotAvatar}
            className="w-16 h-16 border-[3px] border-white"
          />
        </div>

        {supportingAgentInfo?.map(
          (supportingAgent: AgentDto, index: number) => {
            return (
              <div className="-ml-3" key={String(index)}>
                <AvatarComponent
                  imageUrl={supportingAgent.avatarUrl}
                  className="w-16 h-16 border-[3px] border-white"
                />
              </div>
            );
          },
        )}
      </div>
      <div>
        <CalltypeBadge callType={callType} />
      </div>
    </div>
  );
}
