import { BrainIcon } from 'lucide-react';
import { AGENT_EMOTIONAL_STATE_OPTIONS } from '../constants';
import { cn } from '@/lib/utils';

export default function EmotionBadge({
  emotionalState,
  size = 'medium',
  border = true,
}: {
  emotionalState?: string;
  size?: 'small' | 'medium';
  border?: boolean;
}) {
  const emotionalStateOption = AGENT_EMOTIONAL_STATE_OPTIONS.find(
    (item) => item.value === emotionalState,
  );

  return (
    <div
      className={cn(
        'rounded-[100px] justify-start items-center gap-1 flex',
        border ? 'border' : '',
        border ? size === 'small' ? 'pl-1 pr-1' : 'pl-1.5 pr-2 py-0.5' : '',
        border ? emotionalStateOption?.borderColor : '',
        border ? emotionalStateOption?.color : '',
      )}
    >
      <div className={cn("relative", size === 'small' ? 'w-3 h-3' : 'w-4 h-4')}>
        <BrainIcon
          className={cn('mr-1', size === 'small' ? 'w-3 h-3' : 'w-4 h-4', emotionalStateOption?.textColor)}
        />
      </div>
      <div
        className={cn(
          'leading-tight',
          size === 'small' ? 'text-[0.62rem]' : 'text-sm font-medium',
          emotionalStateOption?.textColor,
        )}
      >
        {emotionalStateOption?.label || emotionalState}
      </div>
    </div>
  );
}
