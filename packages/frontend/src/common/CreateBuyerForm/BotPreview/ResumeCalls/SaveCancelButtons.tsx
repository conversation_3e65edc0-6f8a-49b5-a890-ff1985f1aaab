import { Button } from '@/components/ui/button';

interface IProps {
  onClickSave: VoidFunction;
  onClickCancel: VoidFunction;
  error?: boolean;
}

export default function SaveCancelButtons({
  onClickSave,
  onClickCancel,
  error,
}: IProps) {
  return (
    <div className="justify-start items-start gap-2 inline-flex">
      <div className="px-4 bg-white rounded-lg   justify-center items-center gap-2 flex">
        <div className="text-center text-zinc-950 text-sm font-medium  leading-tight">
          <Button
            variant="outline"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClickCancel();
            }}
          >
            Cancel
          </Button>
        </div>
        <div className="text-center text-white text-sm font-medium  leading-tight">
          <Button
            disabled={error}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClickSave();
            }}
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
}
