import { MAX_TRANSCRIPT_LENGTH } from '@/common/DragAndDropModal/utils';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { Dispatch, SetStateAction } from 'react';

interface IProps {
  inputState: [
    inputValue: string,
    setInputValue: Dispatch<SetStateAction<string>>,
  ];
  error?: boolean;
}

export default function PasteModal({ inputState, error }: IProps) {
  const [inputValue, setInputValue] = inputState;

  return (
    <Textarea
      className={cn(
        'w-full h-full p-2 text-sm text-zinc-700  outline-none resize-none rounded-md',
        error ? 'text-red-500' : '',
      )}
      placeholder={`Enter your transcript here (max ${MAX_TRANSCRIPT_LENGTH} characters). Each line is a new message.`}
      id="uploadTranscriptPaste"
      onChange={(e) => {
        setInputValue(e.target.value);
      }}
      value={inputValue}
    />
  );
}
