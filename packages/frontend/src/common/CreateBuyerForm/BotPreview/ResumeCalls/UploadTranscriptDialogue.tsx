import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import SaveCancelButtons from './SaveCancelButtons';
import {
  Dispatch,
  SetStateAction,
  useContext,
  useEffect,
  useState,
} from 'react';
import useResumeCallsParser from '@/hooks/useResumeCallsParser';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import HorizontalDotsLoading from '@/components/ui/Hyperbound/horizontalDotsLoading';
import UploadTabContent from './UploadTabContent';
import { toast } from 'react-toastify';
import { Progress } from '@/components/ui/progress';
import useTranscriptProgress from '@/hooks/useTranscriptProgress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { RiInformation2Fill, RiInformation2Line } from '@remixicon/react';

interface IProps {
  isDialogueOpen: boolean;
  setDialogueOpen: Dispatch<SetStateAction<boolean>>;
  setSidePanelOpen: Dispatch<SetStateAction<boolean>>;
}

export default function UploadTranscriptDialogue({
  isDialogueOpen,
  setDialogueOpen,
  setSidePanelOpen,
}: IProps) {
  const inputState = useState('');
  const [inputValue] = inputState;
  const {
    fetchQuery,
    data: parsedTranscriptMessages,
    isLoading,
    isSuccess,
    isError,
    totalWords,
  } = useResumeCallsParser(inputValue);
  const errorState = useState(false);

  const { numberOfIntervals, intervalDuration } =
    useTranscriptProgress(totalWords);

  const { setCurrentMessages } = useContext(CreateBuyerBotEditFormContext);
  const [progressInterval, setProgressInterval] = useState<number>(1);

  const saveInput = async () => {
    let progressBar = 0; // Initialize progress bar

    const increment = 100 / numberOfIntervals; // Calculate the increment for each interval

    const intervalId = setInterval(() => {
      let currentProgress = 0;
      if (progressBar < 100) {
        progressBar += increment; // Update progress based on the increment
        currentProgress = Math.min(progressBar, 100); // Ensure progress does not exceed 100
        setProgressInterval(currentProgress); // Update state with current progress
      }
    }, intervalDuration); // Update every 5 seconds

    await fetchQuery();
  };

  const handleSaveAndClose = () => {
    saveInput();
    if (isSuccess) {
      setDialogueOpen(false);
    }
  };

  useEffect(() => {
    if (inputState?.length) {
      if (parsedTranscriptMessages) {
        setCurrentMessages(parsedTranscriptMessages);
      }
    }
  }, [parsedTranscriptMessages]);

  useEffect(() => {
    if (isSuccess) {
      setDialogueOpen(false); // Close dialog when query is successful
    }
    if (isError) {
      setDialogueOpen(false);
      setSidePanelOpen(false);
      toast.error(
        'An error occurred while parsing the transcript. Please try again later.',
      );
    }
  }, [isSuccess, isError]); // Trigger effect when the query is successfully completed

  return (
    <Dialog open={isDialogueOpen} onOpenChange={setDialogueOpen}>
      <DialogContent className="max-w-lg w-full p-6 bg-white rounded-lg">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-zinc-800 flex items-center gap-1">
            Upload Transcript
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <RiInformation2Line className="w-5 h-5 text-zinc-500 hover:text-zinc-700 transition-colors cursor-help" />
                </TooltipTrigger>
                <TooltipContent
                  className="max-w-[250px] p-3 bg-zinc-800 text-white rounded-lg shadow-lg"
                  sideOffset={5}
                >
                  <span className="text-sm">
                    {
                      "Due to AI limitations, we can only process files of a specific kind (.txt, .pdf, .docx, and .rtf), up to 500,000 characters. If you're having trouble uploading a file, please try to paste the text instead."
                    }
                  </span>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </DialogTitle>
        </DialogHeader>
        <UploadTabContent inputState={inputState} errorState={errorState} />

        {isLoading ? (
          // Display loading state
          <>
            <div className="mt-4 text-center text-zinc-600">
              Processing{`: ${progressInterval.toFixed(0)}%`}
            </div>
            <Progress indicatorColor={'black'} value={progressInterval} />
            <HorizontalDotsLoading />
          </>
        ) : (
          <div className="flex justify-end mt-6">
            <SaveCancelButtons
              onClickCancel={() => {
                setDialogueOpen(false);
              }}
              onClickSave={handleSaveAndClose}
              error={errorState[0]}
            />
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
