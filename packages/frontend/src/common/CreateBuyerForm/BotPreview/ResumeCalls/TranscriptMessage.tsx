import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { cn } from '@/lib/utils';
import { Trash2 } from 'lucide-react';
import React, { useContext, useMemo, useState, useEffect, useRef } from 'react';
import { MessageProp } from './RecentMessagesDisplay';
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { TooltipContent } from '@radix-ui/react-tooltip';
import AgentAvatar from '@/components/Avatars/Agent';
import UserAvatar from '@/components/Avatars/User';

interface ICurrentMessage {
  id: number;
  message: string;
  avatar: string;
  altText: string;
  role: 'buyer' | 'seller';
}
const TranscriptMessage = ({
  id,
  message = '',
  avatar,
  altText,
  role,
}: ICurrentMessage) => {
  const { setCurrentMessages, currentMessages } = useContext(
    CreateBuyerBotEditFormContext,
  );
  const [inputState, setInputState] = useState<string>(message);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const updateMessage = (newValue: string) => {
    const indexToChange = currentMessages.findIndex((m) => m.id === id);
    if (indexToChange !== -1) {
      const updatedMessages = [...currentMessages];
      updatedMessages.splice(indexToChange, 1, {
        ...currentMessages[indexToChange],
        message: newValue.trim(),
      });
      setCurrentMessages(updatedMessages);
    }
  };

  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const tooltipContent = useMemo(() => {
    if (role === 'seller' && currentMessages.length === 2) {
      return 'Delete message';
    }
    if (role === 'seller' && message.length > 0) {
      return 'Delete message and previous bot message';
    }
    return '';
  }, [role, currentMessages, message]);

  const showDeleteButton = useMemo(
    () => role === 'seller' && message.length > 0,
    [role, currentMessages, message],
  );

  return (
    <TooltipProvider>
      <div className=" grow shrink basis-0 flex-col justify-center items-start gap-3 inline-flex">
        <div className="self-stretch justify-start items-center gap-2 inline-flex">
          <div className=" rounded-sm justify-center items-center flex">
            {role === 'buyer' ? <AgentAvatar /> : <UserAvatar />}
          </div>
          <Input
            className={cn(
              'w-full grow self-stretch bg-white',
              inputState.length && 'text-zinc-900',
            )}
            placeholder="Click to start typing, press ‘Enter’ to add"
            onChange={(e) => {
              const newValue = e.target.value;
              setInputState(newValue);

              if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
              }

              debounceTimeoutRef.current = setTimeout(() => {
                updateMessage(newValue);
              }, 400);
            }}
            value={inputState}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();

                // Prevent adding empty messages
                if (!inputState.trim()) return;

                setCurrentMessages((prev) => {
                  if (prev.length === 0) {
                    return [
                      { id: 1, message: inputState.trim(), role: 'buyer' },
                    ];
                  }
                  const lastMessage = prev[prev.length - 1];

                  // Check if the current ID corresponds to a new message
                  if (id === lastMessage?.id + 1) {
                    const newMessage = {
                      id: lastMessage ? lastMessage.id + 1 : 1,
                      message: inputState.trim(),
                      role: lastMessage?.role === 'buyer' ? 'seller' : 'buyer',
                    };
                    return [...prev, newMessage] as MessageProp[];
                  } else {
                    // Find the index of the message to update
                    const indexToChange = prev.findIndex((m) => m.id === id);

                    if (indexToChange !== -1) {
                      // Update the existing message using splice
                      const updatedMessages = [...prev];
                      updatedMessages.splice(indexToChange, 1, {
                        ...prev[indexToChange],
                        message: inputState.trim(),
                      });
                      return updatedMessages;
                    }
                  }

                  // Return the unchanged array in other cases
                  return prev;
                });

                // Clear the input state after processing
                setInputState('');
              }
            }}
          />

          {showDeleteButton && (
            <Tooltip>
              <TooltipContent
                side="top"
                sideOffset={10}
                className="text-xs text-muted-foreground bg-white border border-zinc-200 rounded-md p-2 mr-2"
              >
                {tooltipContent}
              </TooltipContent>
              <TooltipTrigger>
                <Button
                  onClick={(e) => {
                    setCurrentMessages((previous) => {
                      // Find the index of the message with the matching id
                      const indexToRemove = previous.findIndex(
                        (m) => m.id === id,
                      );

                      // If the message is the last one, only remove it
                      if (indexToRemove === previous.length - 1) {
                        return previous.filter(
                          (_, index) => index !== indexToRemove,
                        );
                      }

                      // Otherwise, remove the matching message and the preceding one
                      return previous.filter(
                        (_, index) =>
                          index !== indexToRemove &&
                          index !== indexToRemove - 1,
                      );
                    });
                  }}
                  variant="ghost"
                  className="ml-2 text-muted-foreground"
                >
                  <Trash2 size={18} className="text-zinc-500" />
                </Button>
              </TooltipTrigger>
            </Tooltip>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default TranscriptMessage;
