import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { UploadTranscriptDnd } from '@/common/DragAndDropModal';
import { Tabs, TabsList, TabsContent, TabsTrigger } from '@/components/ui/tabs';
import PasteModal from './PasteTranscript';
import { MAX_TRANSCRIPT_LENGTH } from '@/common/DragAndDropModal/utils';

const ErrorMessage = ({
  error,
  inputValue,
}: {
  error: boolean;
  inputValue: string;
}) => {
  return error ? (
    <>
      <span className="text-red-500 text-sm">
        The transcript is too long.
        {` ${inputValue.length}/${MAX_TRANSCRIPT_LENGTH} characters`}
      </span>
    </>
  ) : (
    <div />
  );
};

interface IUploadTabContentProps {
  inputState: [
    inputValue: string,
    setInputValue: Dispatch<SetStateAction<string>>,
  ];
  errorState: [
    errorValue: boolean,
    setErrorValue: Dispatch<SetStateAction<boolean>>,
  ];
}

enum LOAD_TRANSCRIPT_METHODS {
  UPLOAD = 'UPLOAD',
  PASTE = 'PASTE',
}

export default function UploadTabContent({
  inputState,
  errorState,
}: IUploadTabContentProps) {
  const [inputValue] = inputState;
  const [error, setError] = errorState;
  const [loadTranscriptMethod, setLoadTranscriptMethod] = useState(
    LOAD_TRANSCRIPT_METHODS.UPLOAD,
  );

  const [, setInputValue] = inputState;

  useEffect(() => {
    setInputValue('');
  }, []);

  useEffect(() => {
    const lengthError = inputValue?.length > MAX_TRANSCRIPT_LENGTH;

    setError(lengthError);
  }, [inputValue]);

  return (
    <Tabs
      className="w-full"
      defaultValue={LOAD_TRANSCRIPT_METHODS.PASTE}
      onValueChange={(value) =>
        setLoadTranscriptMethod(value as LOAD_TRANSCRIPT_METHODS)
      }
    >
      <TabsList className="flex p-1 bg-zinc-100 rounded-lg">
        <TabsTrigger
          value={LOAD_TRANSCRIPT_METHODS.UPLOAD}
          className={`flex-1 px-4 text-sm font-medium text-center rounded-md transition`}
        >
          Upload
        </TabsTrigger>
        <TabsTrigger
          value={LOAD_TRANSCRIPT_METHODS.PASTE}
          className={`flex-1 px-4  text-sm font-medium text-center rounded-md transition`}
        >
          Paste
        </TabsTrigger>
      </TabsList>
      <ErrorMessage inputValue={inputValue} error={error} />

      <TabsContent value={LOAD_TRANSCRIPT_METHODS.UPLOAD} className="mt-4 ">
        <UploadTranscriptDnd inputState={inputState} />
      </TabsContent>
      <TabsContent value={LOAD_TRANSCRIPT_METHODS.PASTE} className="mt-4 h-40">
        <PasteModal inputState={inputState} error={error} />
      </TabsContent>
    </Tabs>
  );
}
