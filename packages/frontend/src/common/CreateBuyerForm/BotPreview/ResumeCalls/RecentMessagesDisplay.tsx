import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import useUserSession from '@/hooks/useUserSession';
import { useContext, useMemo, useState } from 'react';
import TranscriptMessage from './TranscriptMessage';
import useTranscript from '@/hooks/useTransctipts';
import ScrollableContent from '@/components/ui/Hyperbound/DialogFullScreen/ScrollableContent';
import { Button } from '@/components/ui/button';
import ConfirmationModal from '@/components/ConfirmationModal';
import { ParsedTranscriptMessage } from '@/lib/Ai/types';

export interface MessageProp extends ParsedTranscriptMessage {
  avatar: string;
  altText: string;
  isInput?: boolean;
}

export const RecentMessagesDisplay = ({ isInput }: { isInput?: boolean }) => {
  const { form, setCurrentMessages, currentMessages, setResumeCalls } =
    useContext(CreateBuyerBotEditFormContext);

  const { user } = useUserSession();

  const { avatar: botAvatar } = form.getValues();
  const botAvatarSrc = botAvatar;
  const userAvatarSrc = `${user?.pictureUrl}`;
  const [swapModalOpen, setSwapModalOpen] = useState(false);
  const [clearModalOpen, setClearModalOpen] = useState(false);

  const { transcript, switchRoles } = useTranscript();
  const last = transcript?.[transcript.length - 1];

  const inputMessage = useMemo((): MessageProp[] => {
    if (!last) {
      return [
        {
          id: 1,
          role: 'seller',
          message: '',
          avatar: userAvatarSrc,
          altText: "User's Avatar",
          isInput: true,
        },
      ];
    }

    const nextMessage: MessageProp = {
      id: last.id + 1,
      role: last.role === 'buyer' ? 'seller' : 'buyer',
      message: '',
      avatar: last.role === 'buyer' ? userAvatarSrc : botAvatarSrc,
      altText: last.role === 'buyer' ? "User's Avatar" : "Bot's Avatar",
      isInput: true,
    };

    return [nextMessage];
  }, [last, transcript, botAvatarSrc, userAvatarSrc]);

  const source = isInput ? inputMessage : transcript;

  return (
    <>
      <ScrollableContent>
        <div className="flex flex-col gap-4">
          {source.map((message) => (
            <TranscriptMessage key={message.id} {...message} />
          ))}

          {!isInput && transcript.length > 0 && (
            <div className="flex flex-col justify-center items-center p-1">
              <div className="text-zinc-500 text-sm font-normal leading-tight text-center">
                This will be the opening line for the Bot when you start a call
              </div>
              <div className="w-full flex justify-between m-4">
                <Button
                  onClick={() => setSwapModalOpen(true)}
                  disabled={currentMessages?.length < 3}
                >
                  Swap roles
                </Button>
                <Button
                  onClick={() => setClearModalOpen(true)}
                  variant={'outline'}
                  className="bg-red-500 text-white"
                  disabled={currentMessages?.length < 2}
                >
                  Clear messages
                </Button>
              </div>
            </div>
          )}
        </div>
      </ScrollableContent>
      <ConfirmationModal
        open={swapModalOpen}
        description={
          'Swap the speakers in the transcript. This will delete the first and last messages.'
        }
        onCancel={() => setSwapModalOpen(false)}
        onConfirm={() => {
          switchRoles();
          setSwapModalOpen(false);
        }}
        title={'Swap message roles in transcript.'}
      ></ConfirmationModal>
      <ConfirmationModal
        open={clearModalOpen}
        description={
          'This will delete all current messages and implement the opener line.'
        }
        onCancel={() => setClearModalOpen(false)}
        onConfirm={() => {
          setCurrentMessages(() => [
            {
              id: 1,
              role: 'buyer',
              message: `${form?.getValues()?.openerLine}`,
            },
          ]);
          setResumeCalls([`${form?.getValues()?.openerLine}`]);

          setClearModalOpen(false);
        }}
        title={'Clear transcript messages'}
      ></ConfirmationModal>
    </>
  );
};
