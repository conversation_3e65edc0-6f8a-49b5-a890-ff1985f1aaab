import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, LightbulbIcon } from 'lucide-react';

interface ResumeCallsInfoPanelProps {
  children?: any;
}

const ResumeCallsInfoPanel: React.FC<ResumeCallsInfoPanelProps> = () => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);

  const togglePanel = () => {
    setIsExpanded((prevState) => !prevState);
  };

  return (
    <div className="self-stretch p-2 bg-[#3c82f6]/10 rounded-lg flex-col justify-start items-start  flex">
      <div className="self-stretch justify-between items-center inline-flex">
        <div className="justify-start items-center gap-2 flex mb-2">
          <LightbulbIcon className="w-4 h-4 text-blue-600" />

          <div className="text-zinc-950 h-4 text-sm font-medium ">
            What are the resume calls?
          </div>
        </div>
        {/* Chevron Icon for toggling */}
        <button onClick={togglePanel} className="ml-2">
          {isExpanded ? (
            <ChevronUpIcon className="w-5 h-5 " />
          ) : (
            <ChevronDownIcon className="w-5 h-5 " />
          )}
        </button>
      </div>

      {/* Conditionally rendered content */}
      {isExpanded && (
        <div className="self-stretch px-6 flex-col justify-center items-start gap-2 flex">
          <div className="self-stretch text-[#2e3035] text-sm font-normal leading-tight">
            Resume calls provide a quick summary of previous conversations,
            helping sales reps continue smoothly from where they left off.{' '}
            <br />
            The script will be accessible for Reps to see.
          </div>
        </div>
      )}
    </div>
  );
};

export default ResumeCallsInfoPanel;
