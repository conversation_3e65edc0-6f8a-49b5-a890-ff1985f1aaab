import { Button } from '@/components/ui/button';
import React, { useContext, useState } from 'react';
import ResumeCallSidePanel from './ResumeCallSidePanel';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';

const ResumeCallSelector = () => {
  const { currentMessages } = useContext(CreateBuyerBotEditFormContext);
  const sidePanelState = useState<boolean>(false);
  const [, setDialogueOpen] = sidePanelState;

  return (
    <>
      <div className="self-stretch px-3 flex-col justify-start items-start gap-2 flex">
        <div className=" text-[#2e3035] text-sm font-medium  leading-tight">
          Resume call
        </div>
        <div className=" text-zinc-500 text-sm font-normal  leading-tight">
          Resume call allows you to save the transcript of a call up to a
          specific point, enabling users to resume from that point onward.
        </div>

        <Button
          className="self-stretch h-[34px] px-4 bg-white rounded-lg shadow border border-zinc-200 justify-center items-center gap-2 inline-flex mt-4"
          variant="ghost"
          onClick={() => {
            setDialogueOpen(true);
          }}
        >
          <div className="text-center text-zinc-950 text-sm font-medium  leading-tight">
            {currentMessages.length > 1 ? 'Edit' : 'Add'}
          </div>
        </Button>
      </div>
      <ResumeCallSidePanel sidePanelState={sidePanelState} />
    </>
  );
};

export default ResumeCallSelector;
