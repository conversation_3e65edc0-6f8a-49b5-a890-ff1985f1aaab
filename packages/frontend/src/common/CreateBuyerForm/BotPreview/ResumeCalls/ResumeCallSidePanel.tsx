import {
  Sheet,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON>it<PERSON>,
  She<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>ooter,
  SheetDescription,
} from '@/components/ui/sheet';
import UploadTranscriptDialogue from './UploadTranscriptDialogue';
import React, {
  useState,
  Dispatch,
  SetStateAction,
  useContext,
  useRef,
  useMemo,
} from 'react';
import { Button } from '@/components/ui/button';
import { UploadIcon } from 'lucide-react';
import { MessageProp, RecentMessagesDisplay } from './RecentMessagesDisplay';
import ResumeCallsInfoPanel from './ResumeCallInfoPanel';
import { ScrollArea } from '@radix-ui/react-scroll-area';
import { Separator } from '@radix-ui/react-separator';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { Id, toast } from 'react-toastify';
import TranscriptMessage from './TranscriptMessage';
import useUserSession from '@/hooks/useUserSession';
import useTranscript from '@/hooks/useTransctipts';
import {
  areMessagesEqual,
  convertToParsedTranscriptMessages,
} from '../../Main/utils';

interface IProps {
  sidePanelState: [boolean, Dispatch<SetStateAction<boolean>>];
}

export default function ResumeCallSidePanel({ sidePanelState }: IProps) {
  const {
    form,
    setCurrentMessages,
    resumeCalls,
    setResumeCalls,
    currentMessages,
  } = useContext(CreateBuyerBotEditFormContext);
  const backupMessages = useMemo(
    () => convertToParsedTranscriptMessages(resumeCalls),
    [resumeCalls],
  );

  const [sidePanelOpen, setSidePanelOpen] = sidePanelState;
  const [isDialogueOpen, setDialogueOpen] = useState(false);
  const errorToastId = useRef<Id | null>(null);

  const onEsc = () => {
    setSidePanelOpen(false);
    setDialogueOpen(false);
  };

  const handleAccept = () => {
    if (currentMessages?.[currentMessages.length - 1]?.role === 'seller') {
      errorToastId.current = toast.error(
        'Resume calls must end with a buyer message. Please add a buyer message to the end of the list.',
        {
          style: { zIndex: 9999, marginLeft: '50px' },
          position: 'top-left',
        },
      );
    } else {
      if (currentMessages.length === 1) {
        form?.setValue('openerLine', currentMessages[0]?.message);
      }
      setResumeCalls(currentMessages.map((m) => m.message));
      setSidePanelOpen(false);
    }
  };

  const handleCancel = () => {
    setCurrentMessages(backupMessages);
    setSidePanelOpen(false);
  };

  const { user } = useUserSession();

  const { avatar: botAvatar } = form.getValues();
  const botAvatarSrc = botAvatar;
  const userAvatarSrc = `${user?.pictureUrl}`;
  const { transcript } = useTranscript();
  const last = useMemo(() => transcript?.[transcript.length - 1], [transcript]);

  const inputConsistent = useMemo((): MessageProp => {
    if (!last) {
      return {
        id: 1,
        role: 'buyer',
        message: '',
        avatar: botAvatarSrc,
        altText: "Bot's Avatar",
      };
    }

    const nextMessage: MessageProp = {
      id: last.id + 1,
      role: last.role === 'buyer' ? 'seller' : 'buyer',
      message: '',
      avatar: last.role === 'buyer' ? userAvatarSrc : botAvatarSrc,
      altText: last.role === 'buyer' ? "User's Avatar" : "Bot's Avatar",
    };

    return nextMessage;
  }, [last, transcript, botAvatarSrc, userAvatarSrc]);

  return (
    <Sheet open={sidePanelOpen} onOpenChange={onEsc}>
      <SheetContentLight className="flex flex-col h-full min-w-3xl bg-zinc-50 pb-0">
        <SheetHeader className="border-b sticky top-0">
          <SheetTitle className="flex items-center">Resume Call</SheetTitle>
        </SheetHeader>
        <SheetDescription />

        <ResumeCallsInfoPanel />

        <ScrollArea className="flex-1 overflow-y-auto">
          <RecentMessagesDisplay />
          <Separator />
        </ScrollArea>

        <div className="flex w-full">
          <TranscriptMessage {...inputConsistent} />
        </div>

        <SheetFooter className="sticky bottom-0 z-10 p-2 flex items-center w-full border-t">
          <Button
            variant="outline"
            className="bg-white"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setDialogueOpen(true);
            }}
          >
            <UploadIcon size={16} className="mr-1" />
            Upload transcript
          </Button>
          <div className="flex-1" />
          <Button variant="outline" className="bg-white" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            disabled={
              areMessagesEqual(backupMessages, currentMessages) ||
              last?.role === 'seller'
            }
            onClick={handleAccept}
          >
            Accept
          </Button>
        </SheetFooter>
      </SheetContentLight>

      <UploadTranscriptDialogue
        isDialogueOpen={isDialogueOpen}
        setDialogueOpen={setDialogueOpen}
        setSidePanelOpen={setSidePanelOpen}
      />
    </Sheet>
  );
}
