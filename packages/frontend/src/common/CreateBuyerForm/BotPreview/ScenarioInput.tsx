import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';

import { useContext } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { Input } from '@/components/ui/input';

export default function ScenarioInput() {
  const { form } = useContext(CreateBuyerBotEditFormContext);

  return (
    <Form {...form}>
      <div className="self-stretch flex-col justify-start items-start gap-2 flex mb-2">
        <div className="self-stretch text-[#2e3035] text-sm font-medium leading-tight">
          <FormField
            control={form.control}
            name="scenarioName"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="text-[#2e3035] text-sm font-medium  leading-tight">
                  Scenario Name
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Type the name of the multi-party scenario"
                    {...field}
                    className="p-1 h-[30px] bg-white rounded-lg border border-zinc-200 text-[#2e3035] font-normal text-sm leading-tight forcus:border-zinc-900"
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    </Form>
  );
}
