import { CALL_TYPE_OPTIONS, CALL_TYPE_TO_ICON } from '../constants';
import { AgentCallType } from '@/lib/Agent/types';

export default function CalltypeBadge({
  callType,
}: {
  callType: AgentCallType;
}) {
  const CallTypeIcon =
    CALL_TYPE_TO_ICON?.[callType as keyof typeof CALL_TYPE_TO_ICON]?.Icon;

  return (
    <div className="pl-1.5 pr-2 py-0.5 bg-zinc-100 border border-zinc-200 rounded-[100px] justify-center items-center gap-1 flex">
      <div className="w-4 h-4 relative">
        {CallTypeIcon && <CallTypeIcon className="mr-1 h-4 w-4" />}
      </div>
      <div className="text-zinc-700 text-sm font-medium leading-tight">
        {CALL_TYPE_OPTIONS.find((option) => option.value === callType)?.label ||
          callType}
      </div>
    </div>
  );
}
