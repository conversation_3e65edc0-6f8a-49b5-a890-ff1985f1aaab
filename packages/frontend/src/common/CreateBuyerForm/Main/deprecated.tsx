/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import AddBulletsField from '@/components/AddBulletsField';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { AutoExpandingTextarea, Textarea } from '@/components/ui/textarea';
import { useAgent } from '@/hooks/useAgent';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import AgentService from '@/lib/Agent';
import {
  AgentCallType,
  AgentDto,
  AgentEmotionalState,
  AgentGender,
  AgentLanguage,
  AgentLanguagesLabels,
  AgentStatus,
  AgentVoice,
  GatekeeperTemplateDto,
} from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuthInfo } from '@propelauth/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  AudioLinesIcon,
  BotIcon,
  BrainIcon,
  BriefcaseIcon,
  Building2Icon,
  CheckCircleIcon,
  ChevronRight,
  DicesIcon,
  Edit2,
  FlagTriangleRightIcon,
  InfoIcon,
  LightbulbIcon,
  Loader2Icon,
  Settings2Icon,
  SparklesIcon,
  TargetIcon,
  Trash2,
  UploadCloudIcon,
  User2Icon,
  Wand2Icon,
  X,
  XCircleIcon,
} from 'lucide-react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Id, toast, ToastContainer } from 'react-toastify';
import * as z from 'zod';
import { CreateBuyerBotContext } from '../../../contexts/CreateBuyerBotContext';
import {
  AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE,
  AGENT_GENDER_OPTIONS,
  AVATAR_OPTIONS,
  VOICE_OPTIONS,
} from '../constants';
import _ from 'lodash';
import ScorecardPreview from './ScorecardPreview';
import UserService from '@/lib/User';
import MultiStringsInput from '@/components/ui/Hyperbound/multiStringsInput';

import useAgentGatekeepersTemplates from '@/hooks/useAgentGatekeeperTemplates';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useUserSession from '@/hooks/useUserSession';
import useAvatars from '@/hooks/useAvatars';
import { randomAvatar } from './utils';

export interface StepOneData {
  // Basic details
  description: string;
  warm_call_context?: string;
  discovery_call_context?: string;

  // Personal details
  firstName: string;
  lastName: string;
  gender: AgentGender;
  avatar: string;
  avatarUrl?: string;
  jobTitle: string;
  personalDetails: string[];
  voice: AgentVoice;
  companyName: string;
  emotionalState: AgentEmotionalState;

  // Company details
  companyDetails: string[];
  companyOrgStructure: string[];

  // Priorities & objections
  goals: string[];
  objections: string[];

  // Opinions
  callScenario: string;
  opinions: string[];
  incumbent_solution_info?: string;
  problem_aware_info?: string;
  solution_aware_info?: string;
  pre_existing_champion_info?: string;

  // Scorecard
  scorecardConfigId: string;

  // Advanced settings
  public_presence?: string;

  language: AgentLanguage;

  genBotDraftId?: number;

  keywords?: string[];
}

export const BasicDetailsFormSchema = z.object({
  description: z.string().min(1, 'This field is required'),
  warm_call_context: z.string().optional(),
  discovery_call_context: z.string().optional(),
});

export const PersonalDetailsFormSchema = z.object({
  firstName: z.string().min(1, 'This field is required'),
  lastName: z.string().min(1, 'This field is required'),
  gender: z.string().min(1, 'This field is required'),
  avatar: z.string().min(1, 'This field is required'),
  jobTitle: z.string().min(1, 'This field is required'),
  personalDetails: z.array(z.string()).min(1, 'This field is required'),
  voice: z.string().min(1, 'This field is required'),
  companyName: z.string().min(1, 'This field is required'),
  emotionalState: z.string().min(1, 'This field is required'),
});

export const CompanyDetailsFormSchema = z.object({
  companyDetails: z.array(z.string()).min(1, 'This field is required'),
  companyOrgStructure: z.array(z.string()).min(1, 'This field is required'),
});

export const PrioritiesAndObjectionsFormSchema = z.object({
  objections: z.array(z.string()).min(1, 'This field is required'),
  goals: z.array(z.string()),
});

export const OpinionsFormSchema = z.object({
  callScenario: z.string().min(1, 'This field is required'),
  opinions: z.array(z.string()).min(1, 'This field is required'),
  problem_aware_info: z.string().optional(),
  solution_aware_info: z.string().optional(),
  incumbent_solution_info: z.string().optional(),
  pre_existing_champion_info: z.string().optional(),
});

export const ScorecardFormSchema = z.object({
  scorecardConfigId: z.string().min(1, 'This field is required'),
});

export const AdvancedSettingsFormSchema = z.object({
  public_presence: z.string().optional(),
  language: z.string().optional(),
});

export const formSchema = z.object({
  // Basic details
  ...BasicDetailsFormSchema.shape,

  // Personal details
  ...PersonalDetailsFormSchema.shape,

  // Company details
  ...CompanyDetailsFormSchema.shape,

  // Call settings
  ...PrioritiesAndObjectionsFormSchema.shape,

  // Professional opinions
  ...OpinionsFormSchema.shape,

  // Scorecard
  ...ScorecardFormSchema.shape,

  // Advanced settings
  ...AdvancedSettingsFormSchema.shape,
});

export function getZodSchemaFieldsShallow(schema: z.ZodSchema) {
  const fields: Record<string, true> = {};
  const proxy = new Proxy(fields, {
    get(_, key) {
      if (key === 'then' || typeof key !== 'string') {
        return;
      }
      fields[key] = true;
    },
  });
  schema.safeParse(proxy);
  return Object.keys(fields);
}

function Main() {
  const { CALL_SCENARIO_OPTIONS } = useUserSession();
  const router = useRouter();
  const authInfo = useAuthInfo();
  const { setForms, forms, defaultValues, isEditMode, baseRoute } = useContext(
    CreateBuyerBotContext,
  );
  const params = useParams();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);
  const callType = searchParams.get('callType') as AgentCallType;
  const cloneBuyerId = searchParams.get('cloneBuyerId');
  const isDemo = !authInfo?.isLoggedIn;
  const { data: existingAgent } = useAgent(params?.id as string, isEditMode);
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const { data: gatekeeperTemplates } = useAgentGatekeepersTemplates();

  const queryString =
    searchParams.size > 0 ? `?${searchParams.toString()}` : '';

  const { data: scorecardConfigOptions } = useScorecardConfigsForOrg();

  const startingFormValues = forms.main?.getValues() || defaultValues.main;
  const { data: avatarOptions } = useAvatars(startingFormValues?.gender);
  const [randomAvatarPath, randomAvatarUrl] = randomAvatar(avatarOptions);
  // 1. Define your form.
  const form = useForm<StepOneData>({
    mode: 'onTouched',
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...startingFormValues,
      avatar: startingFormValues?.avatar
        ? startingFormValues?.avatar
        : randomAvatarPath,
      avatarUrl: startingFormValues?.avatarUrl
        ? startingFormValues?.avatarUrl
        : randomAvatarUrl,
      voice: startingFormValues?.voice
        ? startingFormValues?.voice
        : VOICE_OPTIONS?.[startingFormValues?.gender!]?.[0]?.value,
    },
  });

  const filteredScorecardGatekeeperConfigOptions = useMemo(() => {
    if (!scorecardConfigOptions) {
      return [];
    }
    return scorecardConfigOptions.filter((sc) => {
      return sc?.callTypes
        ?.map((c) => c.callType)
        .includes(AgentCallType.GATEKEEPER);
    });
  }, [scorecardConfigOptions]);

  const filteredScorecardConfigOptions = useMemo(() => {
    if (!scorecardConfigOptions) {
      return [];
    }
    return scorecardConfigOptions.filter((sc) => {
      if (!sc.callTypes?.length) {
        // if they created the scorecard but without specifying call types,
        // assume its applicable to all call types
        return true;
      }
      return sc.callTypes.map((c) => c.callType).includes(callType);
    });
  }, [scorecardConfigOptions]);

  useEffect(() => {
    if (isEditMode) {
      return;
    }
    let scorecardConfigId = '';
    for (const scorecardConfig of filteredScorecardConfigOptions) {
      if (!!scorecardConfig.callTypes && !!scorecardConfig.id) {
        const callTypeOpt = scorecardConfig.callTypes.find(
          (ct) => ct.callType === callType,
        );
        if (callTypeOpt) {
          scorecardConfigId = scorecardConfig.id.toString();
          if (callTypeOpt.isDefaultForCallType) {
            // found default, break now
            break;
          }
        }
      }
    }
    if (scorecardConfigId) {
      form.setValue('scorecardConfigId', scorecardConfigId);
    }
  }, [isEditMode, filteredScorecardConfigOptions]);

  let _keywords = form.getValues().keywords;
  if (typeof _keywords === 'string') {
    _keywords = [_keywords];
  }
  const [keywords, setKeywords] = useState<string[]>(_keywords || []);
  const [gatekeeperFor, setGatekeeperFor] = useState<AgentDto[]>(
    existingAgent?.guardedAgents || [],
  );
  const [gatekeepers, setGatekeepers] = useState<AgentDto[]>(
    existingAgent?.gatekeepers || [],
  );
  const [editGatekeepers, setEditGatekeepers] = useState<boolean>(false);
  const [selectedGatekeeperTemplate, setSelectedGatekeeperTemplate] = useState<
    GatekeeperTemplateDto | undefined
  >();
  const [removeGatekeeper, setRemoveGatekeeper] = useState<boolean>(false);
  const [gatekeeperScorecardConfigId, setGatekeeperScorecardConfigId] =
    useState<number>();

  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => updateForm());
    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    updateForm();
  }, [form.formState.isValid]);

  const updateForm = () => {
    //@ts-ignore
    setForms((prev) => ({ ...prev, main: form }));
  };

  const renderWarmCallContextField = () => {
    return (
      <FormField
        control={form.control}
        name="warm_call_context"
        render={({ field }) => (
          <FormItem className="w-full">
            <FormLabel>Warm Call Context</FormLabel>
            <p className="text-muted-foreground">
              &quot;This call is a warm call because
              &#123;description&#125;&quot;
            </p>
            <FormControl>
              <Textarea
                placeholder="This call is a warm call because..."
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  const renderDiscoveryCallContextField = () => {
    return (
      <FormField
        control={form.control}
        name="discovery_call_context"
        render={({ field }) => (
          <FormItem className="w-full">
            <FormLabel>Discovery Call Context</FormLabel>
            <p className="text-muted-foreground">
              &quot;This call is a discovery call because
              &#123;description&#125;&quot;
            </p>
            <FormControl>
              <Textarea
                placeholder="This call is a discovery call because..."
                {...field}
              />
            </FormControl>
            {/* <FormMessage /> */}
          </FormItem>
        )}
      />
    );
  };

  const createAgentMutation = useMutation({
    mutationFn: AgentService.createAgent,
    onSuccess: async (agent, params) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({
        queryKey: ['callDurationByRepAndBuyer'],
      });
      queryClient.removeQueries({ queryKey: ['draftAgent'] });

      if (!removeGatekeeper && selectedGatekeeperTemplate) {
        if (agent.id && selectedGatekeeperTemplate.id) {
          await AgentService.addGatekeeperToAgent(
            agent.id,
            selectedGatekeeperTemplate.id,
            gatekeeperScorecardConfigId,
          );
        }
      }

      if (defaultValues.main?.genBotDraftId) {
        await UserService.markGenerateAgentDraftsAsUsed(
          defaultValues.main?.genBotDraftId,
          agent.id,
        );
      }
      queryClient.invalidateQueries({ queryKey: ['GenBotDrafts'] });

      if (isDemo) {
        router.push(`/buyers/${agent.vapiId}`);
      } else {
        router.push(`/buyers?agentId=${agent.vapiId}`);
      }
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error creating the buyer. Please try again.',
        );
      }
    },
  });

  const createDemoAgentMutation = useMutation({
    mutationFn: AgentService.createDemoAgent,
    onSuccess: (agent, params) => {
      queryClient.invalidateQueries({ queryKey: ['demoAgents'] });
      queryClient.removeQueries({ queryKey: ['demoDraftAgent'] });
      let agentRoute = `/buyers/${agent.vapiId}`;
      if (agent?.affiliateInviteCode) {
        agentRoute += `?invite=${agent.affiliateInviteCode}`;
      }
      router.push(agentRoute);
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error creating the buyer. Please try again.',
        );
      }
    },
  });

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
    onSuccess: async (agent, params) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
      queryClient.removeQueries({ queryKey: ['draftAgent'] });
      queryClient.invalidateQueries({
        queryKey: ['callDurationByRepAndBuyer'],
      });

      if (removeGatekeeper) {
        await AgentService.deleteGatekeeperFromAgent(agent.id);
      } else if (selectedGatekeeperTemplate) {
        if (agent.id && selectedGatekeeperTemplate.id) {
          await AgentService.addGatekeeperToAgent(
            agent.id,
            selectedGatekeeperTemplate.id,
            gatekeeperScorecardConfigId,
          );
        }
      }

      if (isDemo) {
        router.push(`/buyers/${agent.vapiId}`);
      } else {
        router.push(`/buyers?agentId=${agent.vapiId}`);
      }
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error editing the buyer. Please try again.',
        );
      }
    },
  });

  const onSubmit = async (data: StepOneData) => {
    //verify resume calls

    let go = true;
    let resume_calls_message = '';
    const tmp = [...resumeCalls];
    tmp.pop();
    if (tmp.length > 0) {
      if (tmp.length % 2) {
        for (const m of tmp) {
          if (m != '') {
            resume_calls_message += m + '\n';
          } else {
            go = false;
          }
        }
        if (!go) {
          errorToastId.current = toast.error(
            'Resume calls contains empty messages.',
          );
        }
      } else {
        errorToastId.current = toast.error(
          'Resume calls must end with a buyer message. Please add a buyer message to the end of the list.',
        );
        go = false;
      }
    }

    //console.log(data);
    //@ts-ignore
    if (form.formState.isValid && go) {
      if (isEditMode) {
        updateAgentMutation.mutate({
          id: existingAgent?.id,

          // Personal details
          firstName: data?.firstName,
          lastName: data?.lastName,
          gender: data?.gender,
          avatar: data?.avatar,
          jobTitle: data?.jobTitle,
          personalDetails: data?.personalDetails,
          voice: data.voice,
          companyName: data.companyName,

          // Company details
          companyDetails: data.companyDetails,
          companyOrgStructure: data.companyOrgStructure,
          callType,
          emotionalState: data?.emotionalState,
          research: JSON.stringify({
            public_presence: data?.public_presence || '',
            incumbent_solution_info: data?.incumbent_solution_info || '',
            problem_aware_info: data?.problem_aware_info || '',
            solution_aware_info: data?.solution_aware_info || '',
            pre_existing_champion_info: data?.pre_existing_champion_info || '',
            cold_call_scenario:
              callType === AgentCallType.COLD
                ? data?.callScenario
                : 'None - Default',
            warm_call_scenario:
              callType === AgentCallType.WARM
                ? data?.callScenario
                : 'None - Default',
            discovery_call_scenario:
              callType === AgentCallType.DISCOVERY
                ? data?.callScenario
                : 'None - Default',
            discovery_call_context: data?.discovery_call_context || '',
            warm_call_context: data?.warm_call_context || '',
            language: data.language,
            messages: resume_calls_message,
            keywords: keywords,
            // urgency?: string;
            // qualified_prospect?: string;
            // gatekeeper_name?: string;
          }),
          goals: data?.goals,
          objections: data?.objections,
          opinions: data?.opinions,
          scorecardConfigId: Number(data?.scorecardConfigId),
          status: AgentStatus.ACTIVE,
          openerLine: 'Hello, who is this?',
          description:
            data?.description ||
            `Start a cold call simulation with ${data?.firstName || ''} from ${
              data?.companyName || ''
            }, and learn how to do a cold call effectively. Wear headphones for the best experience.`,
        });
      } else {
        const companyName = data?.companyName;
        const firstName = data?.firstName;

        const agentBody = {
          // Personal details
          firstName: data?.firstName,
          lastName: data?.lastName,
          gender: data?.gender,
          avatar: data?.avatar,
          jobTitle: data?.jobTitle,
          personalDetails: data?.personalDetails,
          voice: data.voice,
          companyName: data.companyName,

          // Company details
          companyDetails: data.companyDetails,
          companyOrgStructure: data.companyOrgStructure,
          callType,
          emotionalState: data?.emotionalState,
          research: JSON.stringify({
            public_presence: data?.public_presence || '',
            incumbent_solution_info: data?.incumbent_solution_info || '',
            problem_aware_info: data?.problem_aware_info || '',
            solution_aware_info: data?.solution_aware_info || '',
            pre_existing_champion_info: data?.pre_existing_champion_info || '',
            cold_call_scenario:
              callType === AgentCallType.COLD
                ? data?.callScenario
                : 'None - Default',
            warm_call_scenario:
              callType === AgentCallType.WARM
                ? data?.callScenario
                : 'None - Default',
            discovery_call_scenario:
              callType === AgentCallType.DISCOVERY
                ? data?.callScenario
                : 'None - Default',
            discovery_call_context: data?.discovery_call_context || '',
            warm_call_context: data?.warm_call_context || '',
            language: data.language,
            messages: resume_calls_message,
            keywords: keywords,
            // urgency?: string;
            // qualified_prospect?: string;
            // gatekeeper_name?: string;
          }),
          goals: data?.goals,
          objections: data?.objections,
          opinions: data?.opinions,
          scorecardConfigId: Number(data?.scorecardConfigId),
          status: AgentStatus.ACTIVE,
          ownerDemoInboundFormResponseId: hbDemoInboundForm?.id,
          openerLine: 'Hello, who is this?',
          description:
            data?.description ||
            `Start a cold call simulation with ${firstName} from ${companyName}, and learn how to do a cold call effectively. Wear headphones for the best experience.`,
        };

        //console.log(forms.step2?.getValues(), defaultValues.step2, agentBody)
        if (isDemo) {
          // is demo
          // @ts-ignore
          createDemoAgentMutation.mutate({
            // @ts-ignore
            ...agentBody,
          });
        } else {
          // @ts-ignore
          createAgentMutation.mutate({
            // @ts-ignore
            ...agentBody,
          });
        }
      }
    }
  };

  const isBasicDetailsValid = BasicDetailsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(BasicDetailsFormSchema),
    ),
  ).success;
  const isPersonalDetailsValid = PersonalDetailsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(PersonalDetailsFormSchema),
    ),
  ).success;
  const isCompanyDetailsValid = CompanyDetailsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(CompanyDetailsFormSchema),
    ),
  ).success;
  const isPrioritiesAndObjectionsValid =
    PrioritiesAndObjectionsFormSchema.safeParse(
      _.pick(
        forms.main?.getValues() || defaultValues.main,
        getZodSchemaFieldsShallow(PrioritiesAndObjectionsFormSchema),
      ),
    ).success;
  const isOpinionsValid = OpinionsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(OpinionsFormSchema),
    ),
  ).success;
  const isScorecardValid = ScorecardFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(ScorecardFormSchema),
    ),
  ).success;
  const isAdvancedSettingsValid = AdvancedSettingsFormSchema.safeParse(
    _.pick(
      forms.main?.getValues() || defaultValues.main,
      getZodSchemaFieldsShallow(AdvancedSettingsFormSchema),
    ),
  ).success;

  useEffect(() => {
    form.trigger();
  }, [
    isBasicDetailsValid,
    isPersonalDetailsValid,
    isCompanyDetailsValid,
    isPrioritiesAndObjectionsValid,
    isOpinionsValid,
    isScorecardValid,
    isAdvancedSettingsValid,
  ]);

  const renderProblemAwareAndSolutionAware = () => {
    const problemAwareField = (
      <FormField
        control={form.control}
        name="problem_aware_info"
        render={({ field }) => (
          <FormItem className="w-full">
            <FormLabel>What does the buyer know about the problem?</FormLabel>
            <p className="text-muted-foreground">
              If this prospect was aware that they have painpoint with their
              current situation (as it relates to a problem that your product
              solves), explain this what this painpoint is, how they came to be
              aware of the painpoint. Try to include numbers, or anything the
              prospect has measured in getting to this conclusion.
            </p>
            <FormControl>
              <Textarea placeholder="" {...field} />
            </FormControl>
            {/* <FormMessage /> */}
          </FormItem>
        )}
      />
    );

    const solutionAwareField = (
      <FormField
        control={form.control}
        name="solution_aware_info"
        render={({ field }) => (
          <FormItem className="w-full">
            <FormLabel>What does the buyer know about the solution?</FormLabel>
            <p className="text-muted-foreground">
              If the user was aware of solutions (internal or external) for this
              problem described above, what solutions are they aware of, and
              what are their opinions of those solutions?
            </p>
            <FormControl>
              <Textarea placeholder="" {...field} />
            </FormControl>
            {/* <FormMessage /> */}
          </FormItem>
        )}
      />
    );

    const incumbentField = (
      <FormField
        control={form.control}
        name="incumbent_solution_info"
        render={({ field }) => (
          <FormItem className="w-full">
            <FormLabel>
              What is the buyer&apos;s existing solution and what do they think
              of it?
            </FormLabel>
            <p className="text-muted-foreground">
              Why did they select this solution over other options, what are
              their opinions of this solution, what problems does it solve for
              you?
            </p>
            <FormControl>
              <Textarea required placeholder="" {...field} />
            </FormControl>
            {/* <FormMessage /> */}
          </FormItem>
        )}
      />
    );

    if (callType === AgentCallType.COLD || callType === AgentCallType.WARM) {
      // None - None
      // Problem Aware - PRoblem Aware
      // Solution Aware - Solution Aware & Problem Aware
      // Uses an incumbent - Incumbent Solution Aware & Problem Aware
      // Pre-existing Champion - Pre-existing Champion & Problem Aware
      return (
        <>
          {(form.getValues().callScenario?.toLowerCase() ===
            'is aware of problem' ||
            form.getValues().callScenario?.toLowerCase() ===
              'is aware of solution' ||
            form.getValues().callScenario?.toLowerCase() ===
              'uses a competitor' ||
            form.getValues().callScenario?.toLowerCase() ===
              'pre-existing champion') &&
            problemAwareField}
          {form.getValues().callScenario?.toLowerCase() ===
            'is aware of solution' && solutionAwareField}
          {form.getValues().callScenario?.toLowerCase() ===
            'uses a competitor' && incumbentField}
        </>
      );
    } else if (callType === AgentCallType.DISCOVERY) {
      return (
        <>
          {problemAwareField}
          {solutionAwareField}
          {form
            .getValues()
            .callScenario?.toLowerCase()
            .includes('using competitor') && incumbentField}
        </>
      );
    }
  };

  //RESUME CALLS:
  let currentMessages: string[] = [];
  if (existingAgent && existingAgent.research) {
    const tmp = JSON.parse(existingAgent.research);
    if (tmp.messages) {
      currentMessages = tmp.messages.split('\n');
    } else {
      currentMessages.push(''); //to support new
    }
  } else {
    currentMessages.push(''); //to support new
  }
  const [resumeCalls, setResumeCalls] = useState<string[]>(currentMessages);
  // ---------- END RESUME CALLS

  const canSubmit = useMemo(() => {
    let state = form.formState.isValid;
    if (!state) {
      return state;
    }
    state = isEditMode
      ? updateAgentMutation.isPending || updateAgentMutation.isSuccess
      : isDemo
        ? createDemoAgentMutation.isPending || createDemoAgentMutation.isSuccess
        : createAgentMutation.isPending || createAgentMutation.isSuccess;
    return !state;
  }, [
    isEditMode,
    isDemo,
    form?.formState.isValid,
    updateAgentMutation.isPending,
    updateAgentMutation.isSuccess,
    createDemoAgentMutation.isPending,
    createDemoAgentMutation.isSuccess,
  ]);

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} noValidate>
          <div className="flex justify-between items-start">
            <div>
              <div className="flex items-center">
                <BotIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <h3 className="text-base font-semibold">
                  {isEditMode ? 'Edit buyer bot' : 'Create new buyer bot'}
                </h3>
              </div>
              <p className="text-muted-foreground">
                * You can always edit the bot{isEditMode ? ' again' : ''} even
                after publishing
              </p>
            </div>
            <div className="flex space-x-4">
              {!isEditMode && !cloneBuyerId && (
                <Button
                  onClick={() =>
                    router.push(
                      `${baseRoute}/create/ai-generator${queryString}`,
                    )
                  }
                  className="flex items-center justify-center"
                  variant="outline"
                  type="button"
                >
                  <SparklesIcon className="w-4 h-4 text-primary mr-2" />
                  Regenerate draft with AI
                </Button>
              )}
              <Button
                type="submit"
                disabled={!canSubmit} // here
                className={cn('disabled:opacity-50', {
                  'pointer-events-none': !canSubmit,
                })}
              >
                {(
                  isEditMode
                    ? updateAgentMutation.isPending ||
                      updateAgentMutation.isSuccess
                    : isDemo
                      ? createDemoAgentMutation.isPending ||
                        createDemoAgentMutation.isSuccess
                      : createAgentMutation.isPending ||
                        createAgentMutation.isSuccess
                ) ? (
                  <Loader2Icon className="animate-spin" />
                ) : (
                  <>
                    Publish <UploadCloudIcon className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
          <Separator className="mt-4 mb-8" />

          <div className="mb-8">
            <div className="space-y-8"></div>
            <Accordion
              type="single"
              defaultValue="item-1"
              collapsible
              className="mt-4"
            >
              {/* ================= Basic details: START =========================== */}
              <AccordionItem value="item-1">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <InfoIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">
                        Basic Details
                      </h3>
                      <p className="text-muted-foreground">
                        Basic details about the call
                      </p>
                    </div>
                    <div className="flex space-x-4 items-center">
                      {isBasicDetailsValid ? (
                        <CheckCircleIcon className="text-green-500" />
                      ) : (
                        <XCircleIcon className="text-red-500" />
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-8 my-4">
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>Instructions for your reps *</FormLabel>
                          <p className="text-muted-foreground">
                            These will be displayed to your rep before they
                            start every call with this bot
                          </p>
                          <FormControl>
                            <AutoExpandingTextarea
                              required
                              placeholder="Start a call simulation with..."
                              {...field}
                            />
                          </FormControl>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                    {callType === AgentCallType.WARM &&
                      renderWarmCallContextField()}
                    {callType === AgentCallType.DISCOVERY &&
                      renderDiscoveryCallContextField()}
                  </div>
                </AccordionContent>
              </AccordionItem>
              {/* ================= Basic details: END =========================== */}

              {/* ================= Personal details: START =========================== */}
              <AccordionItem value="item-2">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <User2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">
                        Personal Details
                      </h3>
                      <p className="text-muted-foreground">
                        Personal info about the buyer
                      </p>
                    </div>
                    <div className="flex space-x-4 items-center">
                      {isPersonalDetailsValid ? (
                        <CheckCircleIcon className="text-green-500" />
                      ) : (
                        <XCircleIcon className="text-red-500" />
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-8 my-4">
                    <div className="w-full grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <FormLabel>First name *</FormLabel>
                            <FormControl>
                              <Input required placeholder="Jane" {...field} />
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <FormLabel>Last name *</FormLabel>
                            <FormControl>
                              <Input required placeholder="Bowen" {...field} />
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="w-full grid grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="gender"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <FormLabel>Gender *</FormLabel>
                            <Select
                              required
                              onValueChange={(value: AgentGender) => {
                                field.onChange(value);
                                const [avatar, avatarUrl] =
                                  randomAvatar(avatarOptions);
                                form.setValue('avatar', avatar || '');
                                form.setValue('avatarUrl', avatarUrl);
                                form.setValue(
                                  'voice',
                                  VOICE_OPTIONS[value][0].value,
                                );
                                updateForm();
                              }}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Choose an option" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {AGENT_GENDER_OPTIONS.map((option) => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="avatar"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <FormLabel>Avatar *</FormLabel>
                            <br />
                            <FormControl>
                              <Button
                                onClick={() => {
                                  const [avatar, avatarUrl] =
                                    randomAvatar(avatarOptions);
                                  form.setValue('avatar', avatar || '');
                                  form.setValue('avatarUrl', avatarUrl);
                                  // field.onChange(avatar[1]);
                                  updateForm();
                                }}
                                className="w-full"
                                type="button"
                                variant={'outline'}
                              >
                                Generate random{' '}
                                <DicesIcon className="ml-2 h-4 w-4" />
                              </Button>
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="voice"
                        render={({ field }) => (
                          <FormItem className="w-full mt-1">
                            <FormLabel className="flex items-center">
                              <AudioLinesIcon className="w-3 h-3 mr-1" />
                              Voice *
                            </FormLabel>
                            <Select
                              required
                              onValueChange={(value: string) => {
                                field.onChange(value);
                                updateForm();
                              }}
                              value={field.value}
                              // defaultValue={
                              //   VOICE_OPTIONS[form.getValues("gender")]?.[0]?.value
                              // }
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Choose an option" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {VOICE_OPTIONS[form.getValues()?.gender].map(
                                  (option) => (
                                    <SelectItem
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </SelectItem>
                                  ),
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="jobTitle"
                        render={({ field }) => (
                          <FormItem className="w-full mt-[6px]">
                            <FormLabel className="flex items-center">
                              <BriefcaseIcon className="w-3 h-3 mr-1" />
                              Job title *
                            </FormLabel>
                            <FormControl>
                              <Input
                                required
                                placeholder="i.e. Director of Sales"
                                {...field}
                              />
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="companyName"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <FormLabel>Company name *</FormLabel>
                            <FormControl>
                              <Input
                                required
                                placeholder="Agile Solutions"
                                {...field}
                              />
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                    </div>
                    <FormField
                      control={form.control}
                      name="emotionalState"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel className="flex items-center">
                            <BrainIcon className="w-3 h-3 mr-1" /> Emotional
                            state *
                          </FormLabel>
                          <p className="text-muted-foreground">
                            What is the buyer&apos; personality and how do they
                            behave?
                          </p>
                          <Select
                            required
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Choose an option" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
                                callType as keyof typeof AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE
                              ].map((option: any) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.description}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="personalDetails"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Personal details *</FormLabel>
                          <p className="text-muted-foreground">
                            Type and hit enter (i.e. age, location, hobbies,
                            passions, family, etc.)
                          </p>
                          <FormControl>
                            <AddBulletsField
                              field={field}
                              placeholder="Please type here..."
                            />
                          </FormControl>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                  </div>
                </AccordionContent>
              </AccordionItem>
              {/* ================= Personal details: END =========================== */}

              {/* ================= Company details: START =========================== */}
              <AccordionItem value="item-3">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Building2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">
                        Company Details
                      </h3>
                      <p className="text-muted-foreground">
                        Info about the buyer&apos;s company
                      </p>
                    </div>
                    <div className="flex space-x-4 items-center">
                      {isCompanyDetailsValid ? (
                        <CheckCircleIcon className="text-green-500" />
                      ) : (
                        <XCircleIcon className="text-red-500" />
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-8 my-4">
                    <FormField
                      control={form.control}
                      name="companyDetails"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Company details *</FormLabel>
                          <p className="text-muted-foreground">
                            Type and hit enter (i.e. # of employees, industry,
                            funding round, etc.)
                          </p>
                          <FormControl>
                            <AddBulletsField
                              field={field}
                              placeholder="Please type here..."
                            />
                          </FormControl>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="companyOrgStructure"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Company org structure *</FormLabel>
                          <p className="text-muted-foreground">
                            Type and hit enter (i.e. who they report to, who
                            works under them, how is their team organized, etc.)
                          </p>
                          <FormControl>
                            <AddBulletsField
                              field={field}
                              placeholder="Please type here..."
                            />
                          </FormControl>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                  </div>
                </AccordionContent>
              </AccordionItem>
              {/* ================= Company details: END =========================== */}

              {/* ================= Priorities & objections: START =========================== */}
              <AccordionItem value="item-5">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <FlagTriangleRightIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">
                        Priorities &amp; Objections
                      </h3>
                      <p className="text-muted-foreground">
                        The buyer&apos;s priorities and common objections
                      </p>
                    </div>
                    <div className="flex space-x-4 items-center">
                      {isPrioritiesAndObjectionsValid ? (
                        <CheckCircleIcon className="text-green-500" />
                      ) : (
                        <XCircleIcon className="text-red-500" />
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-8 my-4">
                    <FormField
                      control={form.control}
                      name="goals"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Priorities</FormLabel>
                          <p className="text-muted-foreground">
                            Type and hit enter (i.e. what they care about
                            achieving, their priorities and goals, etc.)
                          </p>
                          <FormControl>
                            <AddBulletsField
                              field={field}
                              placeholder="Please type here..."
                            />
                          </FormControl>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="objections"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Objections *</FormLabel>
                          <p className="text-muted-foreground">
                            Type and hit enter (i.e. specific objections they
                            have when called)
                          </p>
                          <FormControl>
                            <AddBulletsField
                              field={field}
                              placeholder="Please type here..."
                            />
                          </FormControl>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                  </div>
                </AccordionContent>
              </AccordionItem>
              {/* ================= Priorities & objections: END =========================== */}

              {/* ================= Professional opinions: START =========================== */}
              <AccordionItem value="item-6">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <LightbulbIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">Opinions</h3>
                      <p className="text-muted-foreground">
                        What the buyer thinks based on their situations
                      </p>
                    </div>
                    <div className="flex space-x-4 items-center">
                      {isOpinionsValid ? (
                        <CheckCircleIcon className="text-green-500" />
                      ) : (
                        <XCircleIcon className="text-red-500" />
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-8 my-4">
                    <FormField
                      control={form.control}
                      name="callScenario"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>Call scenario *</FormLabel>
                          <p className="text-muted-foreground">
                            A call scenario dictates the state of the buyer
                            currently
                          </p>
                          <Select
                            required
                            onValueChange={(value: AgentCallType) => {
                              field.onChange(value);
                              updateForm();
                            }}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Choose an option" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {/* @ts-ignore */}
                              {CALL_SCENARIO_OPTIONS[callType]?.map(
                                (option: any) => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                ),
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="opinions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>General opinions *</FormLabel>
                          <p className="text-muted-foreground">
                            Add this prospect’s opinions about their current
                            processes, details on how their processes were set
                            up, opinions about their leadership, opinions
                            related to their industry, and any other facts that
                            may be helpful when doing discovery on this
                            prospect.
                            <br />
                            <br />
                            Do not include specific painpoints your product
                            solves here, add that below.
                          </p>
                          <FormControl>
                            <AddBulletsField
                              field={field}
                              placeholder="Please type here..."
                            />
                          </FormControl>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                    {renderProblemAwareAndSolutionAware()}
                    {form.getValues().callScenario ===
                      'Pre-existing champion' && (
                      <FormField
                        control={form.control}
                        name="pre_existing_champion_info"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <FormLabel>
                              When did the buyer previously champion your
                              product and what did they think about it?
                            </FormLabel>
                            <p className="text-muted-foreground">
                              &quot;You used to work at &#123;company&#125; and
                              you &#123;explanation about how they are a
                              champion of your product&#125;&quot;
                            </p>
                            <FormControl>
                              <Textarea placeholder="" {...field} />
                            </FormControl>
                            {/* <FormMessage /> */}
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
              {/* ================= Professional opinions: END =========================== */}

              {/* ================= Scorecard: START =========================== */}
              <AccordionItem value="item-4">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <TargetIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">
                        Scorecard
                      </h3>
                      <p className="text-muted-foreground">
                        Custom scorecards for the calls
                      </p>
                    </div>
                    <div className="flex space-x-4 items-center">
                      {isScorecardValid ? (
                        <CheckCircleIcon className="text-green-500" />
                      ) : (
                        <XCircleIcon className="text-red-500" />
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-8 my-4">
                    <FormField
                      control={form.control}
                      name="scorecardConfigId"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>Scorecard *</FormLabel>
                          <p className="text-muted-foreground">
                            Select between the default or custom scorecards to
                            use for this call
                          </p>
                          <Select
                            required
                            onValueChange={(value: string) => {
                              field.onChange(value);
                              updateForm();
                            }}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Choose a scorecard" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {filteredScorecardConfigOptions && (
                                <>
                                  {filteredScorecardConfigOptions.map(
                                    (option) => {
                                      return (
                                        <SelectItem
                                          key={option.id}
                                          value={String(option.id)}
                                        >
                                          {option.tag}
                                        </SelectItem>
                                      );
                                    },
                                  )}
                                </>
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {filteredScorecardConfigOptions && (
                    <>
                      {filteredScorecardConfigOptions.map((option) => {
                        if (option.id) {
                          if (
                            String(option.id) ==
                            form.getValues().scorecardConfigId
                          ) {
                            return (
                              <ScorecardPreview
                                key={option.id}
                                scorecard={option}
                              />
                            );
                          }
                        }
                      })}
                    </>
                  )}
                </AccordionContent>
              </AccordionItem>
              {/* ================= Scorecard: END =========================== */}

              {/* ================= Advanced settings: START =========================== */}
              <AccordionItem value="item-7">
                <AccordionTrigger>
                  <div className="flex items-center">
                    <Settings2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">
                        Advanced Settings
                      </h3>
                      <p className="text-muted-foreground">
                        Additional settings that are not required
                      </p>
                    </div>
                    <div className="flex space-x-4 items-center">
                      {isAdvancedSettingsValid ? (
                        <CheckCircleIcon className="text-green-500" />
                      ) : (
                        <XCircleIcon className="text-red-500" />
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-8 my-4">
                    <FormField
                      control={form.control}
                      name="language"
                      render={({ field }) => {
                        return (
                          <FormItem className="w-full">
                            <FormLabel>Language *</FormLabel>
                            <p className="text-muted-foreground">
                              Select the language you want to use for this call
                            </p>
                            <Select
                              required
                              onValueChange={(value: string) => {
                                field.onChange(value);
                                updateForm();
                              }}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Choose a language" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {Object.keys(AgentLanguagesLabels).map(
                                  (langKey) => {
                                    const lbl = AgentLanguagesLabels[langKey];
                                    return (
                                      <SelectItem
                                        key={langKey}
                                        value={String(langKey)}
                                        defaultValue={AgentLanguage.EN_US}
                                      >
                                        {lbl}
                                      </SelectItem>
                                    );
                                  },
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        );
                      }}
                    />
                  </div>
                  <div className="space-y-8 my-4">
                    <FormField
                      control={form.control}
                      name="keywords"
                      render={({ field }) => {
                        return (
                          <FormItem className="w-full">
                            <FormLabel>Keywords</FormLabel>
                            <p className="text-muted-foreground">
                              List of keywords to be boosted (press enter to add
                              a new word). We recommend only adding proper nouns
                              to this section.
                            </p>
                            <MultiStringsInput
                              current={keywords}
                              onChange={setKeywords}
                            />
                          </FormItem>
                        );
                      }}
                    />
                  </div>
                  {callType == AgentCallType.GATEKEEPER && gatekeeperFor && (
                    <div className="my-4">
                      <FormLabel>Gatekeeper for</FormLabel>
                      <p className="text-muted-foreground mt-2">
                        This agent is a gatekeeper for the following buyers
                      </p>
                      <div className="mt-2">
                        {gatekeeperFor.length > 0 && (
                          <div className="lex items-center">
                            <Avatar className="w-[52px] h-[52px] relative">
                              <AvatarImage
                                src={`/images/${gatekeeperFor[0].avatar}`}
                              />
                              <AvatarFallback className="text-sm">
                                {gatekeeperFor[0]?.firstName?.charAt(0) || ''}
                                {gatekeeperFor[0]?.lastName?.charAt(0) || ''}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 flex flex-col">
                              <div>
                                {gatekeeperFor[0]?.firstName}{' '}
                                {gatekeeperFor[0]?.lastName}
                              </div>
                              <div className="text-muted-foreground text-xs">
                                {gatekeeperFor[0]?.jobTitle} @{' '}
                                {gatekeeperFor[0]?.companyName}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  {[AgentCallType.GATEKEEPER, AgentCallType.FOCUS].includes(callType) && (
                    <div className="my-4">
                      <FormLabel>Gatekeeper:</FormLabel>
                      <p className="text-muted-foreground mt-2 mb-2">
                        Assign a gatekeeper for this buyer. Select a template
                        and a gatekeeper bot will be created to answer any call
                        for this buyer.
                      </p>
                      {!editGatekeepers && (
                        <div className="flex items-center text-sm">
                          {selectedGatekeeperTemplate && (
                            <div className="mr-2">
                              <div>
                                {selectedGatekeeperTemplate.firstName}{' '}
                                {selectedGatekeeperTemplate.lastName}
                              </div>
                            </div>
                          )}
                          {!selectedGatekeeperTemplate &&
                            !removeGatekeeper &&
                            gatekeepers &&
                            gatekeepers.length > 0 &&
                            gatekeepers.map((gk: AgentDto) => {
                              if (gk.research) {
                                const info = JSON.parse(gk.research);
                                return (
                                  <div className="mr-2" key={gk.id}>
                                    <div>{info['gatekeeper_name']}</div>
                                  </div>
                                );
                              }
                            })}
                          <Button
                            variant={'outline'}
                            onClick={() => {
                              setEditGatekeepers(true);
                            }}
                          >
                            <Edit2 size={16} />
                          </Button>
                          {(selectedGatekeeperTemplate ||
                            (gatekeepers && gatekeepers.length > 0)) &&
                            !removeGatekeeper && (
                              <Button
                                variant={'outline'}
                                className="ml-2"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setSelectedGatekeeperTemplate(undefined);
                                  setRemoveGatekeeper(true);
                                }}
                              >
                                <Trash2 size={16} />
                              </Button>
                            )}
                        </div>
                      )}
                      {editGatekeepers && (
                        <div className="mx-1  my-4">
                          <div className="text-muted-foreground font-semibold mb-1">
                            Select gatekeeper template:
                          </div>
                          <div className="flex items-center">
                            <Select
                              onValueChange={(value: string) => {
                                gatekeeperTemplates?.map(
                                  (gk: GatekeeperTemplateDto) => {
                                    if (String(gk.id) == value) {
                                      setRemoveGatekeeper(false);
                                      setSelectedGatekeeperTemplate(gk);
                                    }
                                  },
                                );
                              }}
                              value={String(selectedGatekeeperTemplate?.id)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Choose a template" />
                              </SelectTrigger>
                              <SelectContent>
                                {gatekeeperTemplates?.map(
                                  (gk: GatekeeperTemplateDto) => {
                                    return (
                                      <SelectItem
                                        key={gk.id}
                                        value={String(gk.id)}
                                      >
                                        {gk.firstName} {gk.lastName}
                                      </SelectItem>
                                    );
                                  },
                                )}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="mt-2">
                            {selectedGatekeeperTemplate &&
                              !selectedGatekeeperTemplate?.scorecardConfigId && (
                                <>
                                  <div className="text-muted-foreground font-semibold mb-1">
                                    Use scorecard for gatekeeper calls:
                                  </div>
                                  <Select
                                    onValueChange={(value: string) => {
                                      if (value) {
                                        setGatekeeperScorecardConfigId(
                                          parseInt(value),
                                        );
                                      }
                                    }}
                                    value={String(gatekeeperScorecardConfigId)}
                                  >
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Choose a scorecard" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      {filteredScorecardGatekeeperConfigOptions && (
                                        <>
                                          {filteredScorecardGatekeeperConfigOptions.map(
                                            (option) => {
                                              return (
                                                <SelectItem
                                                  key={option.id}
                                                  value={String(option.id)}
                                                >
                                                  {option.tag}
                                                </SelectItem>
                                              );
                                            },
                                          )}
                                        </>
                                      )}
                                    </SelectContent>
                                  </Select>
                                </>
                              )}
                          </div>
                          <div className="flex items-center mt-2">
                            <div>
                              <Button
                                variant={'outline'}
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setSelectedGatekeeperTemplate(undefined);
                                  setEditGatekeepers(false);
                                }}
                              >
                                <X size={16} className="mr-1" />
                                Cancel
                              </Button>
                            </div>
                            <div className="flex-1" />
                            <div>
                              <Button
                                variant={'outline'}
                                className=""
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  if (
                                    selectedGatekeeperTemplate &&
                                    selectedGatekeeperTemplate.id
                                  ) {
                                    setEditGatekeepers(false);
                                  }
                                }}
                              >
                                Apply
                                <ChevronRight size={16} className="ml-1" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  <div className=" my-4">
                    <FormField
                      control={form.control}
                      name="public_presence"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>
                            What did you rep discover about this buyer during
                            their research?
                          </FormLabel>
                          <p className="text-muted-foreground">
                            Include information that your rep might have
                            discovered about this prospect from searching them
                            up on the internet. Useful for practicing specific
                            openers
                          </p>
                          <FormControl>
                            <Textarea placeholder="" {...field} />
                          </FormControl>
                          {/* <FormMessage /> */}
                        </FormItem>
                      )}
                    />
                    {callType !== AgentCallType.FOCUS && <div className="mt-4">
                      <FormLabel>Resume call</FormLabel>
                      <p className="text-muted-foreground mt-2">
                        Resume call allows you to save the transcript of a call
                        up to a specific point, enabling users to resume from
                        that point onward.
                      </p>
                      <div className="mt-2">
                        {resumeCalls.map((s, i) => {
                          const isAssistant = i % 2 === 0;
                          let label = 'Rep';
                          let bg = 'bg-muted/40';
                          if (isAssistant) {
                            label = 'Buyer';
                            bg = 'bg-muted';
                          }

                          let isLast = false;
                          if (i == resumeCalls.length - 1) {
                            isLast = true;
                          }
                          let opacity = '';
                          if (isLast) {
                            opacity = 'opacity-50';
                          }

                          return (
                            <div
                              key={i}
                              className={
                                'flex items-center border rounded-lg p-2 mb-2 ' +
                                opacity +
                                ' ' +
                                bg
                              }
                              onClick={(e) => {
                                if (isLast) {
                                  resumeCalls.push('');
                                  setResumeCalls([...resumeCalls]);
                                }
                              }}
                            >
                              <div className="w-[50px] text-xs text-muted-foreground ml-2">
                                {label}
                              </div>
                              <div className="flex-1 h-[80px]">
                                <textarea
                                  id={'txt-' + i}
                                  className="outline-none resize-none p-2 rounded-lg w-full h-[80px] box-border"
                                  value={s}
                                  onChange={(e) => {
                                    resumeCalls[i] = e.target.value;
                                    setResumeCalls([...resumeCalls]);
                                  }}
                                  onKeyDown={(evt: any) => {
                                    const keyCode = evt.keyCode;
                                    if (keyCode === 13) evt.preventDefault(); //enter
                                    if (keyCode === 9) {
                                      evt.preventDefault(); //tab
                                      const next = i + 1;
                                      if (next == resumeCalls.length - 1) {
                                        resumeCalls.push('');
                                        setResumeCalls([...resumeCalls]);
                                      }
                                      const tmp = document.getElementById(
                                        'txt-' + next,
                                      );
                                      if (tmp) {
                                        tmp.focus();
                                      }
                                    }
                                  }}
                                />
                              </div>
                              <div>
                                <Button
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    if (!isLast) {
                                      resumeCalls.splice(i, 1);
                                      setResumeCalls([...resumeCalls]);
                                    }
                                  }}
                                  variant="outline"
                                  className="ml-2 text-muted-foreground border-0"
                                >
                                  <Trash2 size={18} />
                                </Button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>}
                    {/* {callType !== AgentCallType.DISCOVERY &&
                    callType !== AgentCallType.WARM ? (
                      <>
                        {renderWarmCallContextField()}
                        {renderDiscoveryCallContextField()}
                      </>
                    ) : callType === AgentCallType.DISCOVERY ? (
                      renderWarmCallContextField()
                    ) : (
                      renderDiscoveryCallContextField()
                    )} */}
                  </div>
                </AccordionContent>
              </AccordionItem>
              {/* ================= Advanced settings: END =========================== */}
            </Accordion>
          </div>
        </form>
      </Form>

      <ToastContainer />
    </div>
  );
}

export default Main;
