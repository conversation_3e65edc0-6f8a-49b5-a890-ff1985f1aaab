import { Accordion } from '@/components/ui/accordion';
import { Card } from '@/components/ui/card';
import AIBotCreatorFormPersonalDetails from './AIBotCreatorForm/PersonalDetails';
import AIBotCreatorFormCompanyDetails from './AIBotCreatorForm/CompanyDetails';
import AIBotCreatorFormPrioritiesAndObjections from './AIBotCreatorForm/PrioritiesAndObjections';
import { AgentCallType } from '@/lib/Agent/types';
import { useContext } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import AIBotCreatorFormProfessionalOpinions from './AIBotCreatorForm/ProfessionalOpinions';
import AIBotCreatorFormScorecards from './AIBotCreatorForm/Scorecard';
import AIBotCreatorFormAdvancedSettings from './AIBotCreatorForm/AdvancedSettings';
import useUserSession from '@/hooks/useUserSession';
import MultiParty from './AIBotCreatorForm/MultiParty';
import BotPanel from './AIBotCreatorForm/BotPanel';

export default function AIBotCreatorFullForm() {
  const { useMultiParty } = useUserSession();
  const { callType, currentTab } = useContext(CreateBuyerBotEditFormContext);
  return (
    <>
      {useMultiParty ? (
        <>
          {currentTab == 'individual' && <BotPanel />}
          {currentTab == 'multi-party' && <MultiParty />}
        </>
      ) : (
        <Card className="px-4 py-2">
          <Accordion type="single" collapsible>
            <AIBotCreatorFormPersonalDetails />

            {callType != AgentCallType.FOCUS ? (
              <AIBotCreatorFormCompanyDetails />
            ) : (
              <></>
            )}

            {callType != AgentCallType.FOCUS ? (
              <AIBotCreatorFormPrioritiesAndObjections />
            ) : (
              <></>
            )}

            {callType != AgentCallType.FOCUS ? (
              <AIBotCreatorFormProfessionalOpinions />
            ) : (
              <></>
            )}

            <AIBotCreatorFormScorecards />

            <AIBotCreatorFormAdvancedSettings />
          </Accordion>
        </Card>
      )}
    </>
  );
}
