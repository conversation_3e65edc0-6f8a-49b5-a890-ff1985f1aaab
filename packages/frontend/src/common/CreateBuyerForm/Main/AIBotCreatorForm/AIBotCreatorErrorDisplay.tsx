import { CircleX } from 'lucide-react';
import { ControllerFieldState } from 'react-hook-form';

export default function AIBotCreatorErrorDisplay({
  fieldState,
}: {
  fieldState: ControllerFieldState;
}) {
  return (
    fieldState?.invalid && (
      <div className="mt-4">
        <div className="mt-4 h-[92px] px-2 py-3 bg-red-500/5 rounded-lg border border-red-500 flex-col justify-start items-start gap-2 inline-flex">
          <div className="self-stretch justify-between items-center inline-flex">
            <div className="justify-start items-center gap-2 flex">
              <CircleX className="w-4 h-4 relative text-red-500 overflow-hidden" />

              <div className="text-zinc-950 text-sm font-medium leading-tight">
                {fieldState.error?.message}
              </div>
            </div>
            <div className="w-4 h-4 p-[3.33px] opacity-0 justify-center items-center flex overflow-hidden"></div>
          </div>
          <div className="self-stretch h-10 px-6 flex-col justify-center items-start gap-2 flex">
            <div className="self-stretch text-[#2e3035] text-sm font-normal leading-tight">
              {
                'It’s better to have unique input values for your bots for better recognition, if you’re looking to create a variation you can do it from the All bots page.'
              }
            </div>
          </div>
        </div>
      </div>
    )
  );
}
