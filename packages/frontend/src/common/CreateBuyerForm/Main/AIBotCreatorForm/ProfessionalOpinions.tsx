import AddBulletsField from '@/components/AddBulletsField';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { AgentCallType } from '@/lib/Agent/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useContext } from 'react';
import ProblemAwareAndSolutionAwareFields from './ProblemAndSolutionAwareFields';
import useUserSession from '@/hooks/useUserSession';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import { LightbulbIcon } from 'lucide-react';
import AIBotCreatorErrorDisplay from './AIBotCreatorErrorDisplay';

export default function AIBotCreatorFormProfessionalOpinions() {
  const { form, callType, isOpinionsValid } = useContext(
    CreateBuyerBotEditFormContext,
  );
  const { CALL_SCENARIO_OPTIONS } = useUserSession();

  return (
    <AccordionItem value="Opinions" className="border border-none">
      <AIBotCreatorFormAccordionTrigger
        Icon={LightbulbIcon}
        isValid={isOpinionsValid}
        heading={'Opinions'}
      />
      <AccordionContent>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="callScenario"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Call scenario</FormLabel>
                <p className="text-muted-foreground">
                  A call scenario dictates the state of the buyer currently
                </p>
                <Select
                  required
                  onValueChange={(value: AgentCallType) => {
                    field.onChange(value);
                  }}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue>
                        {CALL_SCENARIO_OPTIONS[
                          callType as keyof typeof CALL_SCENARIO_OPTIONS
                        ]?.find((option) => option.value === field.value)
                          ?.label || 'Choose an option'}
                      </SelectValue>
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {CALL_SCENARIO_OPTIONS[
                      callType as keyof typeof CALL_SCENARIO_OPTIONS
                    ]?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                        <div className="text-zinc-500 text-sm font-normal">
                          {option.description}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="opinions"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>General opinions</FormLabel>
                <p className="text-muted-foreground">
                  Add this prospect’s opinions about their current processes,
                  details on how their processes were set up, opinions about
                  their leadership, opinions related to their industry, and any
                  other facts that may be helpful when doing discovery on this
                  prospect.
                  {callType !== AgentCallType.MANAGER_ONE_ON_ONE && (
                    <>
                      <br />
                      <br />
                      Do not include specific painpoints your product solves
                      here, add that below.
                    </>
                  )}
                </p>
                <FormControl>
                  <AddBulletsField field={field} placeholder="Enter" />
                </FormControl>
                <AIBotCreatorErrorDisplay fieldState={fieldState} />
              </FormItem>
            )}
          />
          <ProblemAwareAndSolutionAwareFields form={form} callType={callType} />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}
