import AddBull<PERSON>Field from '@/components/AddBulletsField';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { useContext } from 'react';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import { Flag } from 'lucide-react';
import AIBotCreatorErrorDisplay from './AIBotCreatorErrorDisplay';
import { AgentDto } from '@/lib/Agent/types';
import AddBulletsValue from '@/components/AddBulletsValue';
import { updateSupportingAgentInfo } from './MultiParty/updateSupportUtils';

export interface IAIBotCreatorFormPrioritiesAndObjectionsProps {
  supportingAgent?: AgentDto;
  supportingAgentIndex?: number;
}

export default function AIBotCreatorFormPrioritiesAndObjections({
  supportingAgent,
  supportingAgentIndex,
}: IAIBotCreatorFormPrioritiesAndObjectionsProps) {
  const { form, isPrioritiesAndObjectionsValid, setSupportingAgentInfo } =
    useContext(CreateBuyerBotEditFormContext);

  return (
    <AccordionItem
      value="Priorities & Objections"
      className="border border-none"
    >
      <AIBotCreatorFormAccordionTrigger
        Icon={Flag}
        isValid={supportingAgent ? true : isPrioritiesAndObjectionsValid}
        heading={'Priorities & Objections'}
      />
      <AccordionContent>
        <div className="space-y-4">
          {supportingAgent ? (
            <FormItem>
              <FormLabel>Priorities</FormLabel>
              <p className="text-muted-foreground">
                i.e. what they care about achieving, their priorities and goals,
                etc.
              </p>
              <FormControl>
                <AddBulletsValue
                  valueList={supportingAgent.goals}
                  updateList={(newList: string[]) => {
                    if (
                      setSupportingAgentInfo &&
                      supportingAgentIndex !== undefined
                    ) {
                      updateSupportingAgentInfo(
                        setSupportingAgentInfo,
                        supportingAgentIndex,
                        { goals: newList },
                      );
                    }
                  }}
                  placeholder="Enter"
                />
              </FormControl>
            </FormItem>
          ) : (
            <FormField
              control={form.control}
              name="goals"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Priorities</FormLabel>
                  <p className="text-muted-foreground">
                    i.e. what they care about achieving, their priorities and
                    goals, etc.
                  </p>
                  <FormControl>
                    <AddBulletsField field={field} placeholder="Enter" />
                  </FormControl>
                </FormItem>
              )}
            />
          )}
          {supportingAgent ? (
            <FormItem>
              <FormLabel>Objections</FormLabel>
              <p className="text-muted-foreground">
                i.e. specific objections they have when called
              </p>
              <FormControl>
                <AddBulletsValue
                  valueList={supportingAgent.objections}
                  updateList={(newList: string[]) => {
                    if (
                      setSupportingAgentInfo &&
                      supportingAgentIndex !== undefined
                    ) {
                      updateSupportingAgentInfo(
                        setSupportingAgentInfo,
                        supportingAgentIndex,
                        { objections: newList },
                      );
                    }
                  }}
                  placeholder="Enter"
                />
              </FormControl>
            </FormItem>
          ) : (
            <FormField
              control={form.control}
              name="objections"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>Objections</FormLabel>
                  <p className="text-muted-foreground">
                    i.e. specific objections they have when called
                  </p>
                  <FormControl>
                    <AddBulletsField field={field} placeholder="Enter" />
                  </FormControl>
                  <AIBotCreatorErrorDisplay fieldState={fieldState} />
                </FormItem>
              )}
            />
          )}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}
