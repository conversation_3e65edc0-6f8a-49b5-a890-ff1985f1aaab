import AddBulletsField from '@/components/AddBulletsField';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { useContext } from 'react';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import { Flag } from 'lucide-react';
import AIBotCreatorErrorDisplay from './AIBotCreatorErrorDisplay';

export default function AIBotCreatorFormPrioritiesAndObjections() {
  const { form, isPrioritiesAndObjectionsValid } = useContext(
    CreateBuyerBotEditFormContext,
  );

  return (
    <AccordionItem
      value="Priorities & Objections"
      className="border border-none"
    >
      <AIBotCreatorFormAccordionTrigger
        Icon={Flag}
        isValid={isPrioritiesAndObjectionsValid}
        heading={'Priorities & Objections'}
      />
      <AccordionContent>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="goals"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Priorities</FormLabel>
                <p className="text-muted-foreground">
                  i.e. what they care about achieving, their priorities and
                  goals, etc.
                </p>
                <FormControl>
                  <AddBulletsField field={field} placeholder="Enter" />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="objections"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>Objections</FormLabel>
                <p className="text-muted-foreground">
                  i.e. specific objections they have when called
                </p>
                <FormControl>
                  <AddBulletsField field={field} placeholder="Enter" />
                </FormControl>
                <AIBotCreatorErrorDisplay fieldState={fieldState} />
              </FormItem>
            )}
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}
