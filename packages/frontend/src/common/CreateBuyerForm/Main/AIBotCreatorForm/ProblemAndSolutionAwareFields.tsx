import { AgentCallType } from '@/lib/Agent/types';
import ProblemAndSolutionAware from './ProblemAndSolutionAware';

const AiBotCreatorFormFieldData = {
  problemAware: {
    fieldName: 'problem_aware_info',
    label: 'What does the buyer know about the problem?',
    explanation:
      'If this prospect was aware that they have painpoint with their \
              current situation (as it relates to a problem that your product \
              solves), explain this what this painpoint is, how they came to be \
              aware of the painpoint. Try to include numbers, or anything the \
              prospect has measured in getting to this conclusion.',
  },
  solutionmAware: {
    fieldName: 'solution_aware_info',
    label: 'What does the buyer know about the solution?',
    explanation:
      'If the user was aware of solutions (internal or external) for this \
              problem described above, what solutions are they aware of, and \
              what are their opinions of those solutions?',
  },

  incumbent: {
    fieldName: 'incumbent_solution_info',
    label:
      "What is the buyer's existing solution and what do they think of it?",
    explanation:
      'Why did they select this solution over other options, what are their \
            opinions of this solution, what problems does it solve for you?',
  },
  peChampion: {
    fieldName: 'pre_existing_champion_info',
    label:
      'When did the buyer previously champion your product and what did they think about it?',
    explanation:
      '"You used to work at {company} and you {explanation about how they are a champion of your product}"',
  },
};

interface IProps {
  form: any;
  callType: AgentCallType;
}

export default function ProblemAwareAndSolutionAwareFields({
  form,
  callType,
}: IProps) {
  const callScenario = form.getValues().callScenario?.toLowerCase();
  if (
    callType === AgentCallType.COLD ||
    callType === AgentCallType.WARM ||
    callType === AgentCallType.CHECKIN ||
    callType === AgentCallType.RENEWAL ||
    callType === AgentCallType.DEMO
  ) {
    // None - None
    // Problem Aware - PRoblem Aware
    // Solution Aware - Solution Aware & Problem Aware
    // Uses an incumbent - Incumbent Solution Aware & Problem Aware
    // Pre-existing Champion - Pre-existing Champion & Problem Aware

    return (
      <>
        {(callScenario === 'is aware of problem' ||
          callScenario === 'is aware of solution' ||
          callScenario === 'uses a competitor' ||
          callScenario === 'pre-existing champion' ||
          callType === AgentCallType.DEMO) && (
          <ProblemAndSolutionAware
            form={form}
            {...AiBotCreatorFormFieldData.problemAware}
          />
        )}
        {(callScenario === 'is aware of solution' ||
          callType === AgentCallType.CHECKIN ||
          callType === AgentCallType.RENEWAL ||
          callType === AgentCallType.DEMO) && (
          <ProblemAndSolutionAware
            form={form}
            {...AiBotCreatorFormFieldData.solutionmAware}
          />
        )}
        {(callScenario === 'uses a competitor' ||
          callType === AgentCallType.CHECKIN ||
          callType === AgentCallType.RENEWAL ||
          callType === AgentCallType.DEMO) && (
          <ProblemAndSolutionAware
            form={form}
            {...AiBotCreatorFormFieldData.incumbent}
          />
        )}
        {callScenario === 'pre-existing champion' && (
          <ProblemAndSolutionAware
            form={form}
            {...AiBotCreatorFormFieldData.peChampion}
          />
        )}
      </>
    );
  } else if (callType === AgentCallType.DISCOVERY) {
    return (
      <>
        <ProblemAndSolutionAware
          form={form}
          {...AiBotCreatorFormFieldData.problemAware}
        />
        <ProblemAndSolutionAware
          form={form}
          {...AiBotCreatorFormFieldData.solutionmAware}
        />
        {callScenario.includes('using competitor') && (
          <ProblemAndSolutionAware
            form={form}
            {...AiBotCreatorFormFieldData.incumbent}
          />
        )}
        {callScenario === 'pre-existing champion' && (
          <ProblemAndSolutionAware
            form={form}
            {...AiBotCreatorFormFieldData.peChampion}
          />
        )}
      </>
    );
  }
}
