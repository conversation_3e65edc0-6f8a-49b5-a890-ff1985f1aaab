import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import React, { useContext, useRef, useState } from 'react';
import { AutoSizeTextarea } from '@/components/ui/textarea';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { CreateBuyerBotContext } from '@/contexts/CreateBuyerBotContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import AgentService from '@/lib/Agent';
import { Id, toast } from 'react-toastify';
import { AIBotCreatorData } from '..';
import { AgentDto, AgentGender, AgentVoice } from '@/lib/Agent/types';
import { generateMultiPartyVoice, randomAvatar } from '../../utils';
import useAvatars from '@/hooks/useAvatars';
import { processEmotionalState } from '@/common/CreateBuyerForm';
import UserService from '@/lib/User';
import { useParams } from 'next/navigation';

interface IProps {
  open: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  title: string;
}

export default function AddSecondaryModal({
  open,
  onCancel,
  onConfirm,
  title,
}: IProps) {
  const params = useParams();
  const providerAgentId = params.id as string;
  const { data: maleAvatarOptions } = useAvatars(AgentGender.MALE);
  const { data: femaleAvatarOptions } = useAvatars(AgentGender.FEMALE);
  const { callType, supportingAgentInfo, setSupportingAgentInfo } = useContext(
    CreateBuyerBotEditFormContext,
  );
  const errorToastId = useRef<Id | null>(null);
  const { form } = useContext(CreateBuyerBotEditFormContext);
  const { defaultValues } = useContext(CreateBuyerBotContext);
  const queryClient = useQueryClient();
  const [isLoadingAIDraft, setIsLoadingAIDraft] = useState(false);
  const [secondaryBotInfo, setSecondaryBotInfo] = useState({
    jobTitle: '',
    buyerContext: '',
  });
  const closeModal = (isOpen: boolean) => {
    if (!isOpen) {
      setSecondaryBotInfo({
        jobTitle: '',
        buyerContext: '',
      });
      onCancel();
    }
  };

  const cancel = () => {
    setSecondaryBotInfo({
      jobTitle: '',
      buyerContext: '',
    });
    onCancel();
  };

  const createAIAgentDraft = useMutation({
    mutationFn: AgentService.createAIAgentDraft,
    onSuccess: (agent, params) => {
      const gender = agent.gender;
      const avatarOptions =
        gender === AgentGender.MALE ? maleAvatarOptions : femaleAvatarOptions;
      const aiGenerator = {
        ...defaultValues.aiGenerator,
      };
      // Store the supporting agent in the draftAgent inside supportingAgentInfo
      const primaryAgent = { ...form.getValues() };
      // Ensure agent comes with avatar, voice, gender
      let processedAgent = {
        ...agent,
      };
      // Get the previous list of avatars and voices
      let voiceList = [primaryAgent?.voice as AgentVoice];
      let avatarList = [primaryAgent?.avatar];
      supportingAgentInfo?.forEach((supportingAgent: AgentDto) => {
        voiceList.push(supportingAgent.voice);
        avatarList.push(supportingAgent.avatar);
      });

      // Get avatar
      const filteredAvatarOptions =
        avatarOptions &&
        Object.fromEntries(
          Object.entries(avatarOptions).filter(
            ([key]) => !avatarList.includes(key),
          ),
        );
      // Process avatar options
      const [avatar, avatarUrl] = randomAvatar(filteredAvatarOptions);
      // Generate voice
      const voice = generateMultiPartyVoice(gender, voiceList);
      processedAgent = {
        ...processedAgent,
        gender: gender,
        avatar: String(avatar),
        avatarUrl: avatarUrl,
        voice: voice,
        companyName: primaryAgent.companyName, // Keep company name common
        emotionalState: processEmotionalState(callType, undefined),
        callScenario: 'Asks lot of questions - smart',
      };
      let cacheSupportingAgentInfo =
        supportingAgentInfo !== undefined
          ? [...supportingAgentInfo, processedAgent]
          : [processedAgent];
      setSupportingAgentInfo &&
        setSupportingAgentInfo((prev) => {
          return [...prev, processedAgent];
        });
      // Store multi party in cache
      const draftAgent = {
        ...primaryAgent,
        aiGenerator: aiGenerator,
        supportingAgentInfo: cacheSupportingAgentInfo,
      };
      queryClient.setQueryData(['draftAgent'], draftAgent);
      setSecondaryBotInfo({
        jobTitle: '',
        buyerContext: '',
      });
      onConfirm();
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error creating a draft buyer bot. Please try again.',
        );
      }
    },
  });

  const confirm = async () => {
    let data: AIBotCreatorData = {
      ...secondaryBotInfo,
      sellerContext: '',
      callScenario: 'Asks lot of questions - smart',
    };
    if (defaultValues.aiGenerator) {
      const { sellerContext } = defaultValues.aiGenerator;
      if (sellerContext) {
        data = {
          ...data,
          sellerContext: sellerContext,
        };
        createAIAgentDraft.mutate(data);
      } else {
        // Won't exist for edit case
        setIsLoadingAIDraft(true);
        // Fetch the previously used draft to get sellerContext
        const draftData =
          await UserService.getGenBotDraftByProviderId(providerAgentId);
        setIsLoadingAIDraft(false);
        data = {
          ...data,
          sellerContext: draftData.draft?.sellerContext,
          callScenario: 'Asks lot of questions - smart',
        };
        createAIAgentDraft.mutate(data);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="close-btn top-[50%] translate-y-[-50%]">
        <DialogHeader>
          <DialogTitle className="flex items-center">{title}</DialogTitle>
        </DialogHeader>
        <div className="">
          <div className="text-[#2E3035] font-medium leading-5 text-sm ">
            Job Title *
          </div>
          <div className="text-[#71717A] text-sm leading-5 my-2">
            The job title of this AI bot
          </div>
          <Input
            placeholder="VP of Sales"
            value={secondaryBotInfo.jobTitle}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setSecondaryBotInfo((prev) => {
                return {
                  ...prev,
                  jobTitle: e.target.value,
                };
              });
            }}
            className="p-1 h-[34px] bg-white rounded-lg border border-zinc-200 text-[#2e3035] font-normal text-sm leading-tight forcus:border-zinc-900"
          />
        </div>
        <div className="">
          <div className="text-[#2E3035] font-medium leading-5 text-sm ">
            Describe your employee and their situation (aka the brain of the
            employee bot) *
          </div>
          <div className="text-[#71717A] text-sm leading-5 my-2">
            Describe a specific scenario based on your target employee profile.
            Doesn't have to be complete sentences.(i.e. pain points, common
            objections, etc.)
            <br />
            <br />
            The more specific and less generic, the higher the quality of the
            bot.
          </div>
          <div></div>
          <AutoSizeTextarea
            placeholder="Brain dump..."
            value={secondaryBotInfo.buyerContext}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
              setSecondaryBotInfo((prev) => {
                return {
                  ...prev,
                  buyerContext: e.target.value,
                };
              });
            }}
            className="min-h-[76px] max-h-[150px] overflow-y-auto"
          />
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={cancel}
            disabled={createAIAgentDraft.isPending || isLoadingAIDraft}
          >
            Cancel
          </Button>
          <Button
            variant={'default'}
            onClick={confirm}
            disabled={
              createAIAgentDraft.isPending ||
              isLoadingAIDraft ||
              !secondaryBotInfo.jobTitle ||
              !secondaryBotInfo.buyerContext
            }
          >
            {createAIAgentDraft.isPending || isLoadingAIDraft ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Generating...
              </>
            ) : (
              'Save'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
