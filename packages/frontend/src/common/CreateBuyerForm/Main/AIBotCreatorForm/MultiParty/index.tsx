import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import BotPanel from '../BotPanel';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { AvatarComponent } from '@/components/Avatars/AvatarComponent';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { PlusIcon } from 'lucide-react';
import AddSecondaryModal from './AddSecondayModal';
import { AgentDto } from '@/lib/Agent/types';
import AIBotCreatorFormScorecards from '../Scorecard';
import AIBotCreatorFormAdvancedSettings from '../AdvancedSettings';
import { Accordion } from '@/components/ui/accordion';

export default function MultiParty() {
  const {
    form,
    supportingAgentInfo,
    isPersonalDetailsValid,
    isCompanyDetailsValid,
    isPrioritiesAndObjectionsValid,
    isOpinionsValid,
    isScorecardValid,
    isAdvancedSettingsValid,
  } = useContext(CreateBuyerBotEditFormContext);
  const totalValidPrimary = useMemo(() => {
    return (
      Number(isPersonalDetailsValid) +
      Number(isCompanyDetailsValid) +
      Number(isPrioritiesAndObjectionsValid) +
      Number(isOpinionsValid) +
      Number(isScorecardValid) +
      Number(isAdvancedSettingsValid)
    );
  }, [
    isPersonalDetailsValid,
    isCompanyDetailsValid,
    isPrioritiesAndObjectionsValid,
    isOpinionsValid,
    isScorecardValid,
    isAdvancedSettingsValid,
  ]);
  const supportingAgentPrevLengthRef = useRef<number>(
    supportingAgentInfo?.length ?? 0,
  );
  const tabListRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<Record<string, HTMLButtonElement | null>>({});
  const [indicatorLeft, setIndicatorLeft] = useState(0);
  const [indicatorWidth, setIndicatorWidth] = useState(0);
  const [openAddSecondaryBotModal, setOpenAddSecondaryBotModal] =
    useState(false);
  const { firstName, lastName, avatarUrl } = form.getValues();
  const [selectedBot, setSelectedBot] = useState('primary');
  const onTabChange = (tab: string) => {
    setSelectedBot(tab);
  };

  const handleConfirm = () => {
    setOpenAddSecondaryBotModal(false);
  };
  const parseSelectedBotNumber = () => {
    if (selectedBot !== 'primary') {
      const selectedBotNumber = selectedBot.split('-')[1];
      return Number(selectedBotNumber);
    } else {
      return -1;
    }
  };

  useEffect(() => {
    const el = tabRefs.current[selectedBot];
    if (el) {
      setIndicatorLeft(el.offsetLeft);
      setIndicatorWidth(el.offsetWidth);
    }
  }, [selectedBot, supportingAgentInfo]);

  useEffect(() => {
    if (supportingAgentInfo?.length) {
      const currentLength = supportingAgentInfo?.length ?? 0;
      if (currentLength > supportingAgentPrevLengthRef.current) {
        // Length increased
        setSelectedBot(`supporting-${supportingAgentInfo?.length - 1}`);
        // Update the ref for next render
        supportingAgentPrevLengthRef.current = currentLength;
      }
      // Handle delete case
    }
  }, [supportingAgentInfo]);
  return (
    <>
      <AddSecondaryModal
        open={openAddSecondaryBotModal}
        onCancel={() => {
          setOpenAddSecondaryBotModal(false);
        }}
        onConfirm={handleConfirm}
        title="Add AI bot"
      />
      <div className="flex flex-col bg-[#FFFFFF] rounded-[8px] border border-[#E4E4E7] p-3">
        <Tabs
          defaultValue={selectedBot}
          value={selectedBot}
          className="flex flex-col h-full"
        >
          <div className="flex items-center">
            <div className="flex items-center w-full">
              <TabsList
                ref={tabListRef}
                className="relative mr-2 h-full p-0 rounded-none bg-[#FFFFFF] flex-wrap justify-between gap-2"
              >
                <TabsTrigger
                  value="primary"
                  onClick={() => onTabChange('primary')}
                  ref={(el) => {
                    tabRefs.current['primary'] = el;
                  }}
                  className="h-[36px] bg-transparent data-[state=active]:bg-[#EBF2FE] data-[state=active]:text-[#105AD4] text-[#71717A] p-2 border data-[state=active]:border-[#105AD4] border-[#E4E4E7] rounded-[8px] text-sm font-medium leading-5"
                >
                  <div className="flex items-center">
                    <div className="mr-2">
                      <AvatarComponent
                        imageUrl={avatarUrl}
                        className="w-5 h-5"
                      />
                    </div>
                    <div className="mr-4">
                      {firstName} {lastName} (Primary)
                    </div>
                    <div
                      className={cn(
                        'py-[2px] px-1 text-[12px] leading-4 border border-[#E4E4E7] rounded-[6px] bg-[#FBFBFB]',
                        {
                          'text-[#2E3035]': selectedBot === 'primary',
                          'text-[#71717A]': selectedBot !== 'primary',
                        },
                      )}
                    >
                      {`${totalValidPrimary}/6`}
                    </div>
                  </div>
                </TabsTrigger>
                {supportingAgentInfo?.map(
                  (supportingAgent: AgentDto, index: number) => {
                    return (
                      <TabsTrigger
                        key={String(index)}
                        value={`supporting-${String(index)}`}
                        onClick={() =>
                          onTabChange(`supporting-${String(index)}`)
                        }
                        ref={(el) => {
                          tabRefs.current[`supporting-${String(index)}`] = el;
                        }}
                        className="h-[36px] bg-transparent data-[state=active]:bg-[#EBF2FE] data-[state=active]:border-[#105AD4] border-[#E4E4E7] text-[#71717A] p-2 border border-[#E4E4E7] rounded-[8px] text-sm font-medium leading-5"
                      >
                        <div className="flex items-center">
                          <div className="mr-2">
                            <AvatarComponent
                              imageUrl={supportingAgent?.avatarUrl}
                              className="w-5 h-5"
                            />
                          </div>
                          <div className="mr-4">
                            {supportingAgent?.firstName}{' '}
                            {supportingAgent?.lastName}
                          </div>
                          <div
                            className={cn(
                              'py-[2px] px-1 text-[12px] leading-4 border border-[#E4E4E7] rounded-[6px] bg-[#FBFBFB]',
                              {
                                'text-[#2E3035]': selectedBot === String(index),
                                'text-[#71717A]': selectedBot !== String(index),
                              },
                            )}
                          >
                            4/4
                          </div>
                        </div>
                      </TabsTrigger>
                    );
                  },
                )}
                <div
                  className="absolute z-10 bottom-[-17px] h-[2px] bg-[#105AD4] transition-all duration-300 ease-in-out"
                  style={{ width: indicatorWidth, left: indicatorLeft }}
                />
              </TabsList>
              <div>
                {supportingAgentInfo && supportingAgentInfo?.length < 3 && (
                  <Button
                    type="button"
                    variant={'outline'}
                    className="text-[#71717A]"
                    onClick={() => setOpenAddSecondaryBotModal(true)}
                  >
                    <PlusIcon className="w-[14px] h-[14px] text-[#71717A] mr-2" />
                    Add
                  </Button>
                )}
              </div>
            </div>
          </div>
          <hr className="border-t border-[#E4E4E7] h-[1px] mt-4" />
          <div className="mt-4">
            <BotPanel
              supportingAgent={
                selectedBot !== 'primary' && supportingAgentInfo
                  ? supportingAgentInfo[parseSelectedBotNumber()]
                  : undefined
              }
              supportingAgentIndex={parseSelectedBotNumber()}
              setSelectedBot={setSelectedBot}
            />
          </div>
        </Tabs>
      </div>
      <div className="mt-[10px] flex flex-col bg-[#FFFFFF] rounded-[8px] border border-[#E4E4E7] p-3">
        <div className="bg-[#FFFFFF] rounded-[8px] border border-[#E4E4E7] p-3">
          <Accordion type="single" collapsible>
            <AIBotCreatorFormScorecards />

            <AIBotCreatorFormAdvancedSettings />
          </Accordion>
        </div>
      </div>
    </>
  );
}
