import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useContext } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';

import { AutoSizeTextarea } from '@/components/ui/textarea';

interface IProps {
  fieldName:
    | 'warm_call_context'
    | 'discovery_call_context'
    | 'demo_call_context'
    | 'checkin_call_context'
    | 'manager_one_on_one_call_context';
  label: string;
  placeholder: string;
  instructions?: string;
}

export default function CallContext({
  fieldName,
  label,
  placeholder,
  instructions = 'Give the bot a brief setup so it knows how to respond in the role-play',
}: IProps) {
  const { form } = useContext(CreateBuyerBotEditFormContext);

  return (
    <Form {...form}>
      <FormField
        control={form.control}
        name={fieldName}
        render={({ field }) => (
          <FormItem className="w-full pt-4">
            <FormLabel>{label}</FormLabel>
            <p className="self-stretch text-zinc-500 text-sm font-normal gap-2 leading-tight">
              {instructions}
            </p>
            <FormControl>
              <AutoSizeTextarea
                placeholder={placeholder}
                {...field}
                className="min-h-20"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </Form>
  );
}
