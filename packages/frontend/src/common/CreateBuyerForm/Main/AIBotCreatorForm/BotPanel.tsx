import { Accordion } from '@/components/ui/accordion';
import { Card } from '@/components/ui/card';
import AIBotCreatorFormPersonalDetails from './PersonalDetails';
import AIBotCreatorFormCompanyDetails from './CompanyDetails';
import AIBotCreatorFormPrioritiesAndObjections from './PrioritiesAndObjections';
import AIBotCreatorFormProfessionalOpinions from './ProfessionalOpinions';
import AIBotCreatorFormScorecards from './Scorecard';
import AIBotCreatorFormAdvancedSettings from './AdvancedSettings';
import { AgentCallType, AgentDto } from '@/lib/Agent/types';
import { useContext } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { useSearchParams } from 'next/navigation';

export interface IBotPanel {
  supportingAgent?: AgentDto | undefined;
  supportingAgentIndex?: number | undefined;
  setSelectedBot?: React.Dispatch<React.SetStateAction<string>>;
}

export default function BotPanel({
  supportingAgent,
  supportingAgentIndex,
  setSelectedBot,
}: IBotPanel) {
  const { callType } = useContext(CreateBuyerBotEditFormContext);
  const { currentTab } = useContext(CreateBuyerBotEditFormContext);
  return (
    <Card className="px-4 py-2">
      <Accordion type="single" collapsible>
        <AIBotCreatorFormPersonalDetails
          supportingAgent={supportingAgent}
          supportingAgentIndex={supportingAgentIndex}
          setSelectedBot={setSelectedBot}
        />

        {callType != AgentCallType.FOCUS ? (
          <AIBotCreatorFormCompanyDetails
            supportingAgent={supportingAgent}
            supportingAgentIndex={supportingAgentIndex}
          />
        ) : (
          <></>
        )}

        {callType != AgentCallType.FOCUS ? (
          <AIBotCreatorFormPrioritiesAndObjections
            supportingAgent={supportingAgent}
            supportingAgentIndex={supportingAgentIndex}
          />
        ) : (
          <></>
        )}

        {callType != AgentCallType.FOCUS ? (
          <AIBotCreatorFormProfessionalOpinions
            supportingAgent={supportingAgent}
            supportingAgentIndex={supportingAgentIndex}
          />
        ) : (
          <></>
        )}

        {currentTab !== 'multi-party' && <AIBotCreatorFormScorecards />}

        {currentTab !== 'multi-party' && <AIBotCreatorFormAdvancedSettings />}
      </Accordion>
    </Card>
  );
}
