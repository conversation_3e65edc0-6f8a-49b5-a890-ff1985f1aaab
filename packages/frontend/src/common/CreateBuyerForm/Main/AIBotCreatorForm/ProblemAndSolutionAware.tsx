import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { AutoSizeTextarea } from '@/components/ui/textarea';

interface IProps {
  form: any;
  label: string;
  explanation: string;
  fieldName: string;
}
export default function ProblemAndSolutionAware({
  form,
  label,
  explanation,
  fieldName,
}: IProps) {
  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem className="w-full">
          <FormLabel>{label}</FormLabel>
          <p className="text-muted-foreground">{explanation}</p>
          <FormControl>
            <AutoSizeTextarea placeholder="" {...field} />
          </FormControl>
        </FormItem>
      )}
    />
  );
}
