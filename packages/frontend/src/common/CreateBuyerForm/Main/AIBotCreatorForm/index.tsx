import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { CreateBuyerBotContext } from '@/contexts/CreateBuyerBotContext';
import useGenBotDrafts from '@/hooks/useGenBotDrafts';
import AgentService from '@/lib/Agent';
import UserService from '@/lib/User';
import { GenBotDraftDto, GenBotDraftDtoStatusEnum } from '@/lib/User/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuthInfo } from '@propelauth/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Loader2Icon,
  MoveLeftIcon,
  SparkleIcon,
  SparklesIcon,
  Wand2Icon,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useContext, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Id, toast } from 'react-toastify';
import { z } from 'zod';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import dayjs from 'dayjs';
import { AgentCallType } from '@/lib/Agent/types';
import useUserSession from '@/hooks/useUserSession';
import { generatePersona } from '../utils';
import AgentAvatar from '@/components/Avatars/Agent';

export interface AIBotCreatorData {
  jobTitle: string;
  buyerContext: string;
  sellerContext: string;
  callScenario: string;
}

const formSchema = z.object({
  jobTitle: z.string().min(1, 'This field is required'),
  buyerContext: z.string().min(1, 'This field is required'),
  sellerContext: z.string().min(1, 'This field is required'),
  callScenario: z.string().min(1, 'This field is required'),
});

interface IAIBotCreatorFormProps {
  onGenerate: () => void;
}

const BRAIN_DUMP_MAX_LENGTH = 30000;
const SELLER_CONTEXT_MAX_LENGTH = 3000;

function AIBotCreatorForm({ onGenerate }: IAIBotCreatorFormProps) {
  const { CALL_SCENARIO_OPTIONS } = useUserSession();


  const searchParams = useSearchParams();
  const callType = searchParams.get('callType') as AgentCallType;

  const { setForms, forms, defaultValues, baseRoute } = useContext(
    CreateBuyerBotContext,
  );

  const authInfo = useAuthInfo();
  const queryClient = useQueryClient();
  const router = useRouter();
  const errorToastId = useRef<Id | null>(null);

  //useQuery is not invalidating this next fecth....creates all sorts of problems
  //const { data: draftsDb, isLoading: isLoadingDrafts } = useGenBotDrafts(0, 50, authInfo?.isLoggedIn);
  const [prevDrafts, setPrevDrafts] = useState<GenBotDraftDto[]>([]);
  const [currentDraft, setCurrentDraft] = useState<GenBotDraftDto>({
    status: GenBotDraftDtoStatusEnum.LATEST,
    updatedAt: new Date(),
  } as GenBotDraftDto);

  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);
  const latestFormStatus = useRef<any>(
    forms.aiGenerator?.getValues() || defaultValues.aiGenerator,
  );
  const updateLatestFormStatus = useRef<boolean>(true);
  const [currentDraftId, setCurrentDraftId] = useState<number>(); //currently selected

  const [isLoadingDrafts, setIsLoadingDrafts] = useState<boolean>(true);
  const initDrafts = async () => {
    const draftsDb = await UserService.getGenerateAgentDrafts(0, 100);
    const tmp: GenBotDraftDto[] = [];
    let latest: GenBotDraftDto = {} as GenBotDraftDto;
    draftsDb.forEach((d) => {
      if (d.status === GenBotDraftDtoStatusEnum.LATEST) {
        latest = d;
        setCurrentDraftId(d.id);
        setCurrentDraft(d);
        if (d.draft) {
          form.reset(d.draft);
        }
      } else {
        tmp.push(d);
      }
    });
    if (latest.id) {
      latestFormStatus.current = latest.draft;
      setPrevDrafts([latest, ...tmp]);
      form.reset(latest.draft);
    } else {
      setPrevDrafts([currentDraft, ...tmp]);
      form.reset(currentDraft.draft);
    }
    setIsLoadingDrafts(false);
  };

  useEffect(() => {
    initDrafts();
  }, []);

  //-----end alternative

  // 1. Define your form.
  const form = useForm<AIBotCreatorData>({
    mode: 'onTouched',
    resolver: zodResolver(formSchema),
    defaultValues: forms.aiGenerator?.getValues() || defaultValues.aiGenerator,
  });

  const isDemo = !authInfo?.isLoggedIn;

  useEffect(() => {
    let subscription: any;
    if (!isLoadingDrafts) {
      subscription = form.watch((value, { name, type }) => updateForm());
    }

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [isLoadingDrafts]);

  useEffect(() => {
    updateForm();
  }, [form.formState.isValid]);

  const [sellerContextLength, setSellerContextLength] = useState<number>(0);
  const [buyerContextLength, setBuyerContextLength] = useState<number>(0);

  useEffect(() => {
    const cv = form.getValues();

    setBuyerContextLength(cv.buyerContext.length);
    setSellerContextLength(cv.sellerContext.length);
  }, [form.getValues()]);

  const updateForm = () => {
    //@ts-ignore
    setForms((prev) => ({ ...prev, aiGenerator: form }));

    if (authInfo?.isLoggedIn && !isLoadingDrafts) {
      if (timeoutSearchRes.current) {
        clearTimeout(timeoutSearchRes.current);
      }

      timeoutSearchRes.current = setTimeout(async () => {
        const d = await UserService.upsertGenerateAgentDrafts({
          ...currentDraft,
          draft: form.getValues(),
        });
        setCurrentDraft(d);
      }, 800);

      if (updateLatestFormStatus.current) {
        latestFormStatus.current = form.getValues();
      }
    }
  };

  const loadPrevDraft = (id: string) => {
    const draft = prevDrafts.find((d) => String(d.id) === id);
    if (draft) {
      setCurrentDraftId(draft.id);
      if (draft.status === GenBotDraftDtoStatusEnum.LATEST) {
        updateLatestFormStatus.current = true;
        form.reset(latestFormStatus.current);
      } else {
        updateLatestFormStatus.current = false;
        form.reset(draft.draft);
      }
    }
  };

  const createAIAgentDraft = useMutation({
    mutationFn: AgentService.createAIAgentDraft,
    onSuccess: (agent, params) => {
      queryClient.setQueryData(['draftAgent'], agent);
      queryClient.setQueryData(['genBotDraftId'], currentDraft.id);
      forms.main?.reset({
        ...generatePersona(),
        ...defaultValues.main,
        ...forms.aiGenerator?.getValues(),
      });
      setCurrentDraftId(0);
      setCurrentDraft({
        status: GenBotDraftDtoStatusEnum.LATEST,
        updatedAt: new Date(),
      } as GenBotDraftDto);
      setPrevDrafts([]);
      onGenerate();
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error creating a draft buyer bot. Please try again.',
        );
      }
    },
  });

  const createDemoAIAgentDraft = useMutation({
    mutationFn: AgentService.createDemoAIAgentDraft,
    onSuccess: (agent, params) => {
      queryClient.setQueryData(['demoDraftAgent'], agent);
      forms.main?.reset({
        ...generatePersona(),
        ...defaultValues.main,
        ...forms.aiGenerator?.getValues(),
      });
      onGenerate();
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error creating a draft buyer bot. Please try again.',
        );
      }
    },
  });

  const onSubmit = async (data: AIBotCreatorData) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    await UserService.upsertGenerateAgentDrafts({
      ...currentDraft,
      draft: form.getValues(),
    });

    if (isDemo) {
      await createDemoAIAgentDraft.mutate(data);
    } else {
      await createAIAgentDraft.mutate(data);
    }
  };

  return (
    <div className="mb-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} noValidate>
          <div className="flex justify-between items-center">
            <div>
              <div className="flex items-center space-x-2">
                <SparklesIcon className="h-4 w-4" />
                <h3 className="text-base font-semibold">AI Bot Creator</h3>
              </div>
              <p className="text-muted-foreground">
                Automatically creates a draft of your bot using AI
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.push(`${baseRoute}/create/init`)}
                variant="outline"
                type="button"
              >
                <MoveLeftIcon className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                type="submit"
                className="font-semibold drop-shadow-lg hover:opacity-90 duration-100 transition-opacity"
                style={{
                  background:
                    'linear-gradient(180deg, #3DC3E6 0%, #49C8CF 33.33%, #36C4BF 98.96%)',
                }}
                disabled={
                  isDemo
                    ? createDemoAIAgentDraft.isPending
                    : createAIAgentDraft.isPending
                }
                // disabled={!form.formState.isValid} // here
                variant={'default'}
              >
                {(
                  isDemo
                    ? createDemoAIAgentDraft.isPending
                    : createAIAgentDraft.isPending
                ) ? (
                  <div className="flex items-center">
                    <Loader2Icon className="animate-spin mr-2" />
                    Generating bot draft...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <SparklesIcon className="mr-2 h-4 w-4" />
                    Generate AI draft
                  </div>
                )}
              </Button>
            </div>
          </div>
          <Separator className="mt-4 " />
          {isLoadingDrafts && (
            <div className="flex items-center mt-2">
              <Loader2Icon className="animate-spin mr-2" size={18} />
              Loading previous drafts...
            </div>
          )}
          {prevDrafts.length > 1 ? (
            <div className="flex items-center mt-2 mb-8">
              <div>Previous drafts:</div>
              <div className="flex-1 ml-2">
                <Select
                  onValueChange={(value: string) => {
                    loadPrevDraft(value);
                  }}
                  value={String(currentDraftId)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a draft" />
                  </SelectTrigger>
                  <SelectContent>
                    {prevDrafts.map((d) => {
                      if (d.status == GenBotDraftDtoStatusEnum.LATEST) {
                        return (
                          <SelectItem key={d.id} value={String(d.id)}>
                            <div className="flex items-center w-full">
                              <div>{d.id ? 'Latest' : 'Current'} draft </div>
                              <div className="ml-2">
                                [{dayjs(d.updatedAt).format('MMM D, YYYY')}]
                              </div>
                            </div>
                          </SelectItem>
                        );
                      } else {
                        const agent = d.agent;
                        return (
                          <SelectItem key={d.id} value={String(d.id)}>
                            <div className="flex items-center w-full">
                              <AgentAvatar
                                className="w-6 h-6 mr-2"
                                agent={agent}
                              />
                              <div className="capitalize">
                                {agent?.firstName} {agent?.lastName}
                              </div>
                              <div className="ml-2">-</div>
                              <div className="ml-2">{agent?.jobTitle}</div>
                              <div className="ml-2">
                                [{dayjs(d.updatedAt).format('MMM D, YYYY')}]
                              </div>
                            </div>
                          </SelectItem>
                        );
                      }
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>
          ) : (
            <div className="mb-8"></div>
          )}

          <div className="space-y-8">
            <div className="w-full">
              <FormField
                control={form.control}
                name="jobTitle"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>Job Title *</FormLabel>
                    <p className="text-muted-foreground">
                      The job title of your{' '}
                      {callType === AgentCallType.MANAGER_ONE_ON_ONE
                        ? 'employee'
                        : 'target buyer'}
                    </p>
                    <FormControl>
                      <Input
                        required
                        placeholder="i.e. VP of Sales"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="callScenario"
                render={({ field }) => (
                  <FormItem className="mt-8">
                    <FormLabel>Call scenario *</FormLabel>
                    <p className="text-muted-foreground">
                      A call scenario dictates the state of the{' '}
                      {callType === AgentCallType.MANAGER_ONE_ON_ONE
                        ? 'employee'
                        : 'buyer'}{' '}
                      currently
                    </p>
                    <Select
                      required
                      onValueChange={(value: AgentCallType) => {
                        field.onChange(value);
                        updateForm();
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* @ts-ignore */}
                        {CALL_SCENARIO_OPTIONS[callType]?.map((option: any) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="buyerContext"
                render={({ field }) => (
                  <FormItem className="mt-8">
                    <FormLabel>
                      Describe your{' '}
                      {callType === AgentCallType.MANAGER_ONE_ON_ONE
                        ? 'employee'
                        : 'buyer'}{' '}
                      and their situation (aka the brain of the{' '}
                      {callType === AgentCallType.MANAGER_ONE_ON_ONE
                        ? 'employee'
                        : 'buyer'}{' '}
                      bot) *
                    </FormLabel>
                    <p className="text-muted-foreground">
                      Describe a specific scenario based on your target{' '}
                      {callType === AgentCallType.MANAGER_ONE_ON_ONE
                        ? 'employee'
                        : 'buyer'}{' '}
                      profile. Doesn&apos;t have to be complete sentences.
                      <br />
                      {callType === AgentCallType.MANAGER_ONE_ON_ONE
                        ? '(i.e. pain points, common objections, etc.).'
                        : '(i.e. where they work, pain points, existing tools they use, common objections, etc.).'}{' '}
                      <br />
                      <br />
                      The more specific and less generic, the higher the quality
                      of the bot.
                    </p>
                    <FormControl>
                      <Textarea
                        required
                        placeholder="Brain dump..."
                        {...field}
                        className="min-h-40"
                        maxLength={BRAIN_DUMP_MAX_LENGTH}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex items-center text-xs text-muted-foreground">
                <div className="flex-1"></div>
                <div>
                  {buyerContextLength}/{BRAIN_DUMP_MAX_LENGTH}
                </div>
              </div>
              <FormField
                control={form.control}
                name="sellerContext"
                render={({ field }) => (
                  <FormItem className="mt-8">
                    <FormLabel>
                      {callType == AgentCallType.MANAGER_ONE_ON_ONE
                        ? 'Describe what the manager wants to address with their team member specifically'
                        : 'Describe the product you will be selling *'}
                    </FormLabel>
                    <p className="text-muted-foreground">
                      {callType == AgentCallType.MANAGER_ONE_ON_ONE
                        ? '1-2 sentences is sufficient, i.e. "The manager wants to discusses strategies that the rep can use to improve their progress towards meeting their quota and/or pipeline targets'
                        : '1-2 sentences is sufficient, i.e. &quot;Sales training platform called Hyperbound that simulates real sales calls to help reps practice and improve their skills. Helps reduce ramp time for new reps and improve the performance of existing reps.&quot;'}
                    </p>
                    <FormControl>
                      <Textarea
                        required
                        placeholder="Let it flow..."
                        {...field}
                        className="min-h-16"
                        maxLength={SELLER_CONTEXT_MAX_LENGTH}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex items-center text-xs text-muted-foreground">
                <div className="flex-1"></div>
                <div>
                  {sellerContextLength}/{SELLER_CONTEXT_MAX_LENGTH}
                </div>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}

export default AIBotCreatorForm;
