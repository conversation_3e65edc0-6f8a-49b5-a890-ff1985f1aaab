import { LucideProps } from 'lucide-react';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
import { AccordionTriggerAlt } from '@/components/ui/accordion';

interface IProps {
  isValid?: boolean;

  heading: string;
  headingClassName?: string;
  subheading?: string;
  subheadingClassName?: string;
  Icon?: ForwardRefExoticComponent<
    Omit<LucideProps, 'ref'> & RefAttributes<SVGSVGElement>
  >;
  iconClassName?: string;
}

export default function AIBotCreatorFormAccordionTrigger({
  isValid,
  heading,
  subheading,
  Icon,
  headingClassName,
  subheadingClassName,
  iconClassName,
}: IProps) {
  return (
    <AccordionTriggerAlt className="heading py-2.5" isValid={isValid}>
      {Icon && (
        <div className="flex items-center text-zinc-500">
          <Icon
            className={
              iconClassName ?? 'heading w-4 h-4 mr-2 text-muted-foreground'
            }
          />
        </div>
      )}
      <div className="flex justify-between w-full mr-4">
        <div className="flex items-center space-x-8">
          <h3
            className={
              headingClassName ??
              `heading text-zinc-500 font-semibold text-left`
            }
          >
            {heading}
          </h3>
          {subheading && (
            <p
              className={
                subheadingClassName ??
                `text-muted-foreground ${subheadingClassName}`
              }
            >
              {subheading}
            </p>
          )}
        </div>
      </div>
    </AccordionTriggerAlt>
  );
}
