import { LucideProps } from 'lucide-react';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
import { AccordionTriggerAlt } from '@/components/ui/accordion';

interface IProps {
  isValid?: boolean;

  heading: string;
  subheading?: string;
  Icon: ForwardRefExoticComponent<
    Omit<LucideProps, 'ref'> & RefAttributes<SVGSVGElement>
  >;
}

export default function AIBotCreatorFormAccordionTrigger({
  isValid,
  heading,
  subheading,
  Icon,
}: IProps) {
  return (
    <AccordionTriggerAlt className="heading py-2.5" isValid={isValid}>
      <div className="flex items-center text-zinc-500">
        <Icon className="heading w-4 h-4 mr-2 text-muted-foreground" />
      </div>
      <div className="flex justify-between w-full mr-4">
        <div className="flex items-center space-x-8">
          <h3 className="heading text-zinc-500  font-semibold text-left">
            {heading}
          </h3>
          {subheading && <p className="text-muted-foreground">{subheading}</p>}
        </div>
      </div>
    </AccordionTriggerAlt>
  );
}
