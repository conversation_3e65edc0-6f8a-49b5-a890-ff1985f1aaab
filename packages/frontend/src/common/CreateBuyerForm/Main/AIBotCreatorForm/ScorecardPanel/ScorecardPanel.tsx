import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  useLayoutEffect,
  useContext,
} from 'react';
import {
  Dialog,
  DialogPortal,
  DialogOverlay,
} from '@/components/ui/Hyperbound/dialog-for-calls';
import { Target, X } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import { CreateScorecard } from '@/common/Scorecards/CreateScorecard';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { EditScorecard } from '@/common/Scorecards/EditScorecard';

interface ICallPanelProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSaved: (scorecardId: number) => void;
}

function ScorecardPanel({ open, onOpenChange, onSaved }: ICallPanelProps) {
  /***********************************/
  /************* FE EVENTS ***********/
  /***********************************/

  const { isCreatingScorecard, isEditingScorecard, form } = useContext(
    CreateBuyerBotEditFormContext,
  );
  const [exitingPnl, setExitingPnl] = useState<boolean>(false);
  const scorecardId = form.getValues('scorecardConfigId');

  const closeDialog = () => {
    setExitingPnl(true);
    setTimeout(() => {
      onOpenChange(false);
    }, 160);
  };

  //ESC key to close dialog
  const escFunction = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' || event.key === 'Esc' || event.keyCode === 27) {
      closeDialog();
    }
  }, []);

  // MOUSE SCROLL - with position:fixed container, mouse wheel wont work, we need to use JS:

  useEffect(() => {
    document.addEventListener('keydown', escFunction, false);

    return () => {
      document.removeEventListener('keydown', escFunction, false);
    };
  }, [escFunction]);

  const scrollableContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener('wheel', scrollContent);

    return () => {
      //setPageStatus("profile");
      window.removeEventListener('wheel', scrollContent);
    };
  }, []);

  const scrollContent = (e: WheelEvent) => {
    if (scrollableContainer.current) {
      scrollableContainer.current.scrollTop += e.deltaY;
    }
  };

  useLayoutEffect(() => {
    function updateSize() {
      if (scrollableContainer.current) {
        const s = scrollableContainer.current.getBoundingClientRect();
        scrollableContainer.current.style.height =
          window.innerHeight - s.y - 1 + 'px';
      }
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  /***********************************/
  /************ RENDERING ************/
  /***********************************/

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogPortal>
        <DialogOverlay>
          <div className="fixed top-0 left-0 right-0 bottom-0 w-full h-full z-50 overflow-hidden">
            <div className="flex items-center">
              <div className="flex-grow"></div>
              <div className="text-sm text-white flex items-center">
                <Target size={18} className="mr-2" />
                {isCreatingScorecard
                  ? 'Creating new scorecard'
                  : 'Editing scorecard'}
              </div>
              <div className="flex-grow"></div>
              <div
                className="p-2 cursor-pointer text-white"
                onClick={closeDialog}
              >
                <X size={20} />
              </div>
            </div>
            <AnimatePresence>
              {!exitingPnl && (
                <motion.div
                  initial={{ opacity: 0, y: 700 }}
                  exit={{ opacity: 0, y: 700 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2, ease: 'easeOut' }}
                  className="flex flex-col h-screen"
                  onAnimationComplete={() => {
                    if (scrollableContainer.current) {
                      const s =
                        scrollableContainer.current.getBoundingClientRect();
                      scrollableContainer.current.style.height =
                        window.innerHeight - s.y - 1 + 'px';
                    }
                  }}
                  ref={scrollableContainer}
                >
                  {isCreatingScorecard && (
                    <CreateScorecard
                      onCancel={() => {
                        onOpenChange(false);
                      }}
                      onSaved={(scorecardId: number) => {
                        onSaved(scorecardId);
                      }}
                      showSaveAsDraft={false}
                    />
                  )}
                  {isEditingScorecard && (
                    <EditScorecard
                      scorecardId={parseInt(scorecardId)}
                      onCancel={() => {
                        onOpenChange(false);
                      }}
                      onSaved={(scorecardId: number) => {
                        onSaved(scorecardId);
                      }}
                      showSaveAsDraft={false}
                    />
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </DialogOverlay>
      </DialogPortal>
    </Dialog>
  );
}

export default ScorecardPanel;
