import AddBulletsField from '@/components/AddBulletsField';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { useContext } from 'react';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import { Building2Icon, CircleX } from 'lucide-react';
import AIBotCreatorErrorDisplay from './AIBotCreatorErrorDisplay';

export default function AIBotCreatorFormCompanyDetails() {
  const { form, isCompanyDetailsValid } = useContext(
    CreateBuyerBotEditFormContext,
  );

  return (
    <AccordionItem value="Company Details" className="border border-none">
      <AIBotCreatorFormAccordionTrigger
        Icon={Building2Icon}
        isValid={isCompanyDetailsValid}
        heading={'Company Details'}
      />
      <AccordionContent>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="companyDetails"
            render={({ field, fieldState }) => {
              return (
                <FormItem>
                  <FormLabel>Company details</FormLabel>
                  <p className="text-muted-foreground">
                    i.e. # of employees, industry, funding round, etc.
                  </p>
                  <FormControl>
                    <AddBulletsField field={field} placeholder="Enter" />
                  </FormControl>
                  <AIBotCreatorErrorDisplay fieldState={fieldState} />
                </FormItem>
              );
            }}
          />
          <FormField
            control={form.control}
            name="companyOrgStructure"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company org structure</FormLabel>
                <p className="text-muted-foreground">
                  i.e. who they report to, who works under them, how is their
                  team organized, etc.
                </p>
                <FormControl>
                  <AddBulletsField field={field} placeholder="Enter" />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}
