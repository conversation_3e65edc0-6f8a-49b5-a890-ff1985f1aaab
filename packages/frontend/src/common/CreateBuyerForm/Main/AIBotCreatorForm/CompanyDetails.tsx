import AddBulletsField from '@/components/AddBulletsField';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { useContext } from 'react';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import { Building2Icon } from 'lucide-react';
import AIBotCreatorErrorDisplay from './AIBotCreatorErrorDisplay';
import { AgentDto } from '@/lib/Agent/types';
import AddBulletsValue from '@/components/AddBulletsValue';
import { updateSupportingAgentInfo } from './MultiParty/updateSupportUtils';
import { useSearchParams } from 'next/navigation';

export interface IAIBotCreatorFormCompanyDetailsProps {
  supportingAgent?: AgentDto;
  supportingAgentIndex?: number;
}

export default function AIBotCreatorFormCompanyDetails({
  supportingAgent,
  supportingAgentIndex,
}: IAIBotCreatorFormCompanyDetailsProps) {
  const searchParams = useSearchParams();
  const tab = searchParams.get('tab');
  const { form, isCompanyDetailsValid, setSupportingAgentInfo } = useContext(
    CreateBuyerBotEditFormContext,
  );

  return (
    <AccordionItem value="Company Details" className="border border-none">
      <AIBotCreatorFormAccordionTrigger
        Icon={Building2Icon}
        isValid={supportingAgent ? true : isCompanyDetailsValid}
        heading={'Company Details'}
      />
      <AccordionContent>
        <div className="space-y-4">
          {/* Common across all bots */}
          <FormField
            control={form.control}
            name="companyDetails"
            render={({ field, fieldState }) => {
              return (
                <FormItem>
                  <FormLabel>
                    {tab === 'multi-party'
                      ? 'Company details (common across all bots)'
                      : 'Company details'}{' '}
                  </FormLabel>
                  <p className="text-muted-foreground">
                    i.e. # of employees, industry, funding round, etc.
                  </p>
                  <FormControl>
                    <AddBulletsField field={field} placeholder="Enter" />
                  </FormControl>
                  <AIBotCreatorErrorDisplay fieldState={fieldState} />
                </FormItem>
              );
            }}
          />
          {supportingAgent ? (
            <FormItem>
              <FormLabel>Company org structure</FormLabel>
              <p className="text-muted-foreground">
                i.e. who they report to, who works under them, how is their team
                organized, etc.
              </p>
              <FormControl>
                <AddBulletsValue
                  valueList={supportingAgent.companyOrgStructure}
                  updateList={(newList: string[]) => {
                    if (
                      setSupportingAgentInfo &&
                      supportingAgentIndex !== undefined
                    ) {
                      updateSupportingAgentInfo(
                        setSupportingAgentInfo,
                        supportingAgentIndex,
                        { companyOrgStructure: newList },
                      );
                    }
                  }}
                  placeholder="Enter"
                />
              </FormControl>
            </FormItem>
          ) : (
            <FormField
              control={form.control}
              name="companyOrgStructure"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company org structure</FormLabel>
                  <p className="text-muted-foreground">
                    i.e. who they report to, who works under them, how is their
                    team organized, etc.
                  </p>
                  <FormControl>
                    <AddBulletsField field={field} placeholder="Enter" />
                  </FormControl>
                </FormItem>
              )}
            />
          )}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}
