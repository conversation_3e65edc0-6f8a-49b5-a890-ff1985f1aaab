import { <PERSON>Field, FormItem, FormLabel } from '@/components/ui/form';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';

import { useContext, useEffect, useMemo, useState } from 'react';

import MultiStringsInput from '@/components/ui/Hyperbound/multiStringsInput';
import { Settings2Icon } from 'lucide-react';

import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import { getResearchData } from '../utils';
import { AgentFolderDto } from '@/lib/Agent/types';

export default function AIBotCreatorFormAdvancedSettings() {
  const { form, isAdvancedSettingsValid, existingAgent } = useContext(
    CreateBuyerBotEditFormContext,
  );

  const research = useMemo(
    () => getResearchData(existingAgent),
    [existingAgent],
  );

  const formValues = (form.getValues<'keywords'>('keywords') as string[]) || [];
  const initialKeywords: string[] = research?.keywords ?? formValues ?? [];

  const [keywords, setKeywords] = useState<string[]>(
    Array.isArray(initialKeywords)
      ? initialKeywords
      : initialKeywords
        ? [initialKeywords]
        : [],
  );

  const stableExistingAgent: Partial<AgentFolderDto> = useMemo(() => {
    return existingAgent ? { id: existingAgent.id } : {};
  }, [existingAgent]);

  // Sync form state when keywords change
  useEffect(() => {
    form.setValue<'keywords'>('keywords', [...keywords]);
  }, [keywords, stableExistingAgent]);

  return (
    <AccordionItem value="Advanced Settings" className="border border-none">
      <AIBotCreatorFormAccordionTrigger
        Icon={Settings2Icon}
        isValid={isAdvancedSettingsValid}
        heading="Advanced Settings"
      />
      <AccordionContent>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="keywords"
            render={() => (
              <FormItem className="w-full">
                <FormLabel>Keywords</FormLabel>
                <p className="text-muted-foreground">
                  List of keywords to be boosted (press enter to add a new
                  word). We recommend only adding proper nouns to this section.
                </p>
                <MultiStringsInput current={keywords} onChange={setKeywords} />
              </FormItem>
            )}
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}
