import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import { SparklesIcon } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarImage } from '@/components/ui/avatar';

const defaultAIAssistantMessage =
  'Hi there! Use this field to express all information about your buyer, such as age, personal details, etc';

const AssistantHelper = ({
  img = 'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/org-images/kota.png',
  message = defaultAIAssistantMessage,
  subtext = 'Hyperhound, AI assistant',
}: {
  img?: string;
  message?: string;
  subtext?: string;
}) => {
  return (
    <div className="flex items-start w-full">
      {/* Speech Bubble */}
      <div className="relative w-full max-w-3xl p-4 bg-zinc-100 rounded-lg shadow-lg">
        {/* Message */}
        <div className="text-zinc-950 text-sm font-normal  leading-tight mb-2">
          {message}
        </div>
        {/* Subtext */}
        <div className="text-zinc-500 text-xs font-normal ">{subtext}</div>

        {/* Speech Bubble Tail */}
        <div className="absolute top-1/2 left-full transform -translate-y-1/2 w-0 h-0 border-t-[10px] border-t-zinc-100 border-r-[10px] border-r-transparent"></div>
      </div>

      <Avatar className="w-12 h-12 rounded-full border border-zinc-200 ml-1 mb-">
        <AvatarImage src={img} alt="Assistant Avatar" />
      </Avatar>
    </div>
  );
};

export default function AIBotCreatorFormAIAissistant() {
  const buyerContextLength = 1000;
  const BRAIN_DUMP_MAX_LENGTH = 30000;
  const SELLER_CONTEXT_MAX_LENGTH = 3000;
  const placeholder =
    'Describe a specific scenario based on your target buyer profile. (i.e. where they work, pain points, existing tools they use, common objections, etc.).';
  return (
    <AccordionItem value="AI Assistant" className="border border-none">
      <AIBotCreatorFormAccordionTrigger
        Icon={SparklesIcon}
        heading={'AI Assistant'}
      />
      <AccordionContent>
        <div className="space-y-4">
          <>
            <FormField
              name="AIAssistantInputField"
              render={({ field }) => (
                <FormItem className="mt-8">
                  <FormLabel>AI information</FormLabel>
                  <div>
                    <AssistantHelper />
                  </div>
                  <FormControl>
                    <Textarea
                      required
                      placeholder={placeholder}
                      {...field}
                      className="min-h-40"
                      maxLength={BRAIN_DUMP_MAX_LENGTH}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex items-center text-xs text-muted-foreground">
              <div className="flex-1"></div>
              <div>
                {buyerContextLength}/{BRAIN_DUMP_MAX_LENGTH}
              </div>
            </div>
          </>
        </div>
        {/* TODO: Submit */}
        <div className="h-[34px] pl-3 pr-4 m-2 bg-zinc-100 rounded-lg  float-end items-center gap-2 inline-flex">
          <SparklesIcon className="w-4 h-4 text-[#b8b8bc]" />
          <div className="text-center text-[#b8b8bc] text-sm font-medium leading-tight">
            Submit info to AI
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}
