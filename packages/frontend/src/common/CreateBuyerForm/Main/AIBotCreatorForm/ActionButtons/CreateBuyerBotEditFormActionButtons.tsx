import CreateBuyerBotEditFormSubmitButton from './SubmitButton';
import ValidityBadges from './ValidityBadges';
import { useContext, useMemo } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import CreateBuyerBotEditFormMoreButton from './MoreButton';
import { useSearchParams } from 'next/navigation';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export function validateFlags(flags: Record<string, boolean>): string[] {
  return Object.entries(flags)
    .filter(([_, isValid]) => !isValid)
    .map(([key]) => key);
}

export default function CreateBuyerBotEditFormActiontButtons() {
  const searchParams = useSearchParams();
  const tab = searchParams.get('tab');
  const {
    isPersonalDetailsValid,
    isCompanyDetailsValid,
    isPrioritiesAndObjectionsValid,
    isOpinionsValid,
    isScorecardValid,
    isAdvancedSettingsValid,
    isConfigDetailsValid,
    isScenarioNameValid,
  } = useContext(CreateBuyerBotEditFormContext);

  const validityCheck = useMemo(() => {
    const base = {
      isConfigDetailsValid,
      isPersonalDetailsValid,
      isCompanyDetailsValid,
      isPrioritiesAndObjectionsValid,
      isOpinionsValid,
      isScorecardValid,
      isAdvancedSettingsValid,
    };

    if (tab === 'multi-party') {
      return {
        ...base,
        isScenarioNameValid,
      };
    }

    return base;
  }, [
    isPersonalDetailsValid,
    isCompanyDetailsValid,
    isPrioritiesAndObjectionsValid,
    isOpinionsValid,
    isScorecardValid,
    isAdvancedSettingsValid,
    isConfigDetailsValid,
    isScenarioNameValid,
    tab,
  ]);

  const isValid = useMemo(
    () => validateFlags(validityCheck),
    [
      isConfigDetailsValid,
      isScenarioNameValid,
      isPersonalDetailsValid,
      isCompanyDetailsValid,
      isPrioritiesAndObjectionsValid,
      isOpinionsValid,
      isScorecardValid,
      isAdvancedSettingsValid,
      tab,
    ],
  );
  const checks = useMemo(
    () => Object.keys(validityCheck).length,
    [validityCheck],
  );
  const handleTooltipListCopy = (copy: string) => {
    switch (copy) {
      case 'isScenarioNameValid':
        return 'Scenario Name';
      case 'isConfigDetailsValid':
        return 'Folders';
      case 'isPersonalDetailsValid':
        return 'Personal Details';
      case 'isCompanyDetailsValid':
        return 'Company Details';
      case 'isPrioritiesAndObjectionsValid':
        return 'Priorities & Objections';
      case 'isOpinionsValid':
        return 'Opinions';
      case 'isScorecardValid':
        return 'Scorecard';
      case 'isAdvancedSettingsValid':
        return 'Advanced Settings';
    }
  };
  return (
    <>
      {isValid.length ? (
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger asChild className="">
              <div>
                <ValidityBadges
                  isValid={!isValid.length}
                  negativeText={`${checks - isValid.length}/${checks} completed`}
                />
              </div>
            </TooltipTrigger>
            <TooltipContent
              side="bottom"
              className="bg-white text-[#2E3035] rounded-[12px] border border-[#E4E4E7]"
            >
              <div className="font-medium mb-1 text-sm">Fill in: </div>
              <div>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {isValid.map((key: string) => {
                    return <li key={key}>{handleTooltipListCopy(key)}</li>;
                  })}
                </ul>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        <ValidityBadges
          isValid={!isValid.length}
          negativeText={`${checks - isValid.length}/${checks} completed`}
        />
      )}
      <CreateBuyerBotEditFormMoreButton />
      <CreateBuyerBotEditFormSubmitButton />
    </>
  );
}
