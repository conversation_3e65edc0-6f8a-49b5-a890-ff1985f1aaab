import CreateBuyerBotEditFormSubmitButton from './SubmitButton';
import ValidityBadges from './ValidityBadges';
import { useContext, useMemo } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import CreateBuyerBotEditFormMoreButton from './MoreButton';

function validateFlags(flags: Record<string, boolean>): string[] {
  return Object.entries(flags)
    .filter(([_, isValid]) => !isValid)
    .map(([key]) => key);
}

export default function CreateBuyerBotEditFormActiontButtons() {
  const {
    isPersonalDetailsValid,
    isCompanyDetailsValid,
    isPrioritiesAndObjectionsValid,
    isOpinionsValid,
    isScorecardValid,
    isAdvancedSettingsValid,
    isConfigDetailsValid,
  } = useContext(CreateBuyerBotEditFormContext);

  const validityCheck = {
    isPersonalDetailsValid,
    isCompanyDetailsValid,
    isPrioritiesAndObjectionsValid,
    isOpinionsValid,
    isScorecardValid,
    isAdvancedSettingsValid,
    isConfigDetailsValid,
  };

  const isValid = useMemo(
    () => validateFlags(validityCheck),
    [
      isPersonalDetailsValid,
      isCompanyDetailsValid,
      isPrioritiesAndObjectionsValid,
      isOpinionsValid,
      isScorecardValid,
      isAdvancedSettingsValid,
      isConfigDetailsValid,
    ],
  );
  const checks = useMemo(
    () => Object.keys(validityCheck).length,
    [validityCheck],
  );
  return (
    <>
      <ValidityBadges
        isValid={!isValid.length}
        negativeText={`${checks - isValid.length}/${checks} completed`}
      />
      <CreateBuyerBotEditFormMoreButton />
      <CreateBuyerBotEditFormSubmitButton />
    </>
  );
}
