import { Button } from '@/components/ui/button';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { cn } from '@/lib/utils';
import { Loader2Icon } from 'lucide-react';
import React, { useContext } from 'react';

const CreateBuyerBotEditFormSubmitButton = () => {
  const {
    form,
    createBuyerBotEditFormSubmitButtonState: { disabled, loading },
  } = useContext(CreateBuyerBotEditFormContext);
  return (
    <Button
      form="buyerBotForm" // Associate the Button with the buyerBotForm
      type={form.formState.isValid ? 'submit' : 'button'}
      disabled={disabled}
      className={cn('disabled:bg-zinc-600', {})}
      onClick={form.formState.isValid ? undefined : () => form.trigger()}
    >
      {loading ? (
        <span className="flex items-center">
          <Loader2Icon className="animate-spin" />{' '}
          <p className="ml-2 align-middle">Publishing...</p>
        </span>
      ) : (
        'Publish'
      )}
    </Button>
  );
};

export default CreateBuyerBotEditFormSubmitButton;
