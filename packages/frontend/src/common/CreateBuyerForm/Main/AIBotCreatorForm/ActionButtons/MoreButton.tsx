import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CreateBuyerBotContext } from '@/contexts/CreateBuyerBotContext';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { MoreVerticalIcon } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useContext, useState } from 'react';

export default function CreateBuyerBotEditFormMoreButton() {
  const router = useRouter();
  const { queryString, existingAgent, baseRoute } = useContext(
    CreateBuyerBotEditFormContext,
  );

  const { isEditMode } = useContext(CreateBuyerBotContext);
  const searchParams = useSearchParams();
  const cloneBuyerId = searchParams.get('cloneBuyerId');

  {
    /* TODO: Implement Save as Draft */
  }

  return (
    <div className="mx-2 gap-2 inline-flex">
      {(existingAgent || isEditMode) && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size={'icon'}
              className="rounded-lg border border-zinc-200 bg-white"
            >
              <MoreVerticalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center" className="w-64">
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                router.push(`${baseRoute}/${existingAgent?.vapiId}`);
              }}
            >
              Call bot
            </DropdownMenuItem>
            {!isEditMode && !cloneBuyerId && (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(`${baseRoute}/create/ai-generator${queryString}`);
                }}
              >
                Save as Draft
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}
