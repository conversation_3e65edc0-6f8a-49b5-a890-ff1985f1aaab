import { Badge } from '@/components/ui/badge';

export default function ValidityBadges({
  isValid,
  affirmativeText = 'Completed',
  negativeText = 'Not completed',
}: {
  isValid: boolean;
  affirmativeText?: string;
  negativeText?: string;
}) {
  return isValid ? (
    <Badge
      variant="outline"
      className="h-6 px-2  bg-[#3c82f6]/10 rounded-[100px] border border-[#3c82f6]/20 justify-center items-center  inline-flex "
    >
      <div className="text-[#105ad4] text-xs font-medium  leading-none">
        {affirmativeText}
      </div>
    </Badge>
  ) : (
    <Badge
      variant="outline"
      className="h-6 px-2  bg-red-500/10 rounded-[100px] border border-red-500/20 justify-center items-center  inline-flex"
    >
      <div className="text-red-500 text-xs font-medium leading-none">
        {negativeText}
      </div>
    </Badge>
  );
}
