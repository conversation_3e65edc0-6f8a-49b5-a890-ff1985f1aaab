import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { useContext } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { AgentCallType } from '@/lib/Agent/types';
import { Textarea } from '@/components/ui/textarea';
import CallContext from './CallContext';

export default function AIBotCreatorFormBasicDetails() {
  const { form, callType, isBasicDetailsValid } = useContext(
    CreateBuyerBotEditFormContext,
  );

  return (
    <Form {...form}>
      <div className="self-stretch  px-3 flex-col justify-start items-start gap-2 flex">
        <div className="self-stretch text-[#2e3035] text-sm font-medium leading-tight">
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="text-[#2e3035] text-sm font-medium  leading-tight">
                  Instructions for your reps
                </FormLabel>
                <p className="self-stretch text-zinc-500 text-sm font-normal gap-2 leading-tight">
                  These will be displayed to your rep before they start every
                  call with this bot
                </p>
                <FormControl>
                  <Textarea
                    required
                    placeholder="Wear headphones for the best experience."
                    {...field}
                    className="p-2 bg-white rounded-lg border border-zinc-200 text-[#2e3035] font-normal text-sm leading-tight resize-none min-h-24 forcus:border-zinc-900"
                  />
                </FormControl>
              </FormItem>
            )}
          />
          {/* Additional Fields Based on Call Type */}
          {callType === AgentCallType.WARM && (
            <CallContext
              fieldName={'warm_call_context'}
              label={'Warm Call Context'}
              placeholder={
                'You previously downloaded a case study about this product. You’re open to hearing more but want to understand how it applies to your team...'
              }
            />
          )}
          {callType === AgentCallType.DISCOVERY && (
            <CallContext
              fieldName={'discovery_call_context'}
              label={'Discovery Call Context'}
              placeholder={
                'You recently signed up for a free trial, and now a sales rep is reaching out to learn more about your needs and see if their solution is a fit...'
              }
            />
          )}
          {callType === AgentCallType.DEMO && (
            <CallContext
              fieldName={'demo_call_context'}
              label={'Demo Call Context'}
              placeholder={''}
            />
          )}
          {callType === AgentCallType.CHECKIN && (
            <CallContext
              fieldName={'checkin_call_context'}
              label={'Check-In Call Context'}
              placeholder={''}
            />
          )}
        </div>
      </div>
    </Form>
  );
}
