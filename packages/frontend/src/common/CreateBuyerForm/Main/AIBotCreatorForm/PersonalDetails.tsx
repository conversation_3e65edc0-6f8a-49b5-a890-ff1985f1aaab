import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { DicesIcon, Upload, User2Icon } from 'lucide-react';
import { ChangeEvent, useContext, useEffect, useMemo, useState } from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import { StepOneData } from '@/lib/Agent/types';
import {
  AgentGender,
  AgentLanguage,
  AgentLanguagesLabels,
} from '@/lib/Agent/types';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE,
  AGENT_GENDER_OPTIONS,
  DEFAULT_OPENING_LINE,
  VOICE_OPTIONS,
} from '../../constants';
import { Button } from '@/components/ui/button';
import AddBulletsField from '@/components/AddBulletsField';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import { generatePersona, getRandomSurnames, randomAvatar } from '../utils';
import EmotionBadge from '../../BotPreview/EmotionBadge';
import { Textarea } from '@/components/ui/textarea';
import AudioPreviewComponent from '@/components/ui/audioclip';
import AgentAvatar from '@/components/Avatars/Agent';
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import useAvatars from '@/hooks/useAvatars';
import AIBotCreatorErrorDisplay from './AIBotCreatorErrorDisplay';
import { cn } from '@/lib/utils';

export default function AIBotCreatorFormPersonalDetails() {
  const {
    flags: { dev },
  } = useFeatureFlags();
  const {
    form,
    callType,
    isPersonalDetailsValid,
    resumeCalls,
    setCurrentMessages,
    setResumeCalls,
  } = useContext(CreateBuyerBotEditFormContext);
  const gender = form.watch('gender');

  const emotionalState = form.watch('emotionalState');
  const voice = form.watch('voice');
  const [selectedGender, setSelectedGender] = useState<AgentGender | undefined>(
    undefined,
  );
  const { data: avatarOptions } = useAvatars(gender);

  const suggestions = useMemo(() => getRandomSurnames(), []);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(
    null,
  );

  const openerLineIsDisabled = useMemo(() => {
    return resumeCalls.length > 1;
  }, [resumeCalls]);

  const handleGenerateRandomAvatar = () => {
    const [avatar, avatarUrl] = randomAvatar(avatarOptions);
    form.setValue('avatar', avatar || '');
    form.setValue('avatarUrl', avatarUrl);
  };

  useEffect(() => {
    if (selectedGender && avatarOptions) {
      const persona = generatePersona(selectedGender, avatarOptions);
      Object.entries(persona).forEach(([key, value]) => {
        form.setValue(key as keyof StepOneData, value);
      });
    }
  }, [selectedGender]); // Runs when gender or avatars update

  return (
    <AccordionItem value="Personal Details" className="border border-none">
      <AIBotCreatorFormAccordionTrigger
        Icon={User2Icon}
        isValid={isPersonalDetailsValid}
        heading={'Personal Details'}
      />
      <AccordionContent className="pb-2 border border-none shadow-none">
        <div className="space-y-4">
          <div className="w-full grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="avatar"
              render={() => {
                return (
                  <FormItem className="w-full">
                    <div className="flex items-start gap-4">
                      <AgentAvatar className="w-16 h-16" />

                      <div className="flex flex-col">
                        <FormLabel>Profile photo</FormLabel>
                        <div className="grid grid-cols-2 gap-4 mt-2">
                          <FormControl>
                            <div className="mt-1 flex flex-row gap-2">
                              {dev && (
                                <Button
                                  onClick={() => {}}
                                  className="w-full"
                                  type="button"
                                  variant={'outline'}
                                  disabled={true}
                                >
                                  <Upload className="mr-2 h-4 w-4" /> Upload new
                                </Button>
                              )}

                              <Button
                                onClick={() => {
                                  handleGenerateRandomAvatar();
                                }}
                                className="w-full"
                                type="button"
                                variant={'outline'}
                              >
                                <DicesIcon className="mr-2 h-4 w-4" /> Generate
                              </Button>
                            </div>
                          </FormControl>
                        </div>
                      </div>
                    </div>
                  </FormItem>
                );
              }}
            />
            <br />

            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>First name</FormLabel>
                  <FormControl>
                    <Input required placeholder="Jane" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Last name</FormLabel>
                  <FormControl>
                    <Input required placeholder="Bowen" {...field} />
                  </FormControl>
                  {
                    /* Suggestions Block */
                    dev && (
                      <div className="flex mt-2 space-x-2 text-sm">
                        <div className="text-zinc-500">Suggestions:</div>
                        <div className="flex space-x-2">
                          {suggestions.map((suggestion) => (
                            <button
                              key={suggestion}
                              type="button"
                              onClick={() => field.onChange(suggestion)} // Update the value
                              className="text-[#2091ae] font-medium hover:underline"
                            >
                              {suggestion}
                            </button>
                          ))}
                        </div>
                      </div>
                    )
                  }
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="jobTitle"
              render={({ field }) => (
                <FormItem className="w-full mt-[6px]">
                  <FormLabel className="flex items-center">Job title</FormLabel>
                  <FormControl>
                    <Input
                      required
                      placeholder="i.e. Director of Sales"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="companyName"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Company name </FormLabel>
                  <FormControl>
                    <Input required placeholder="Agile Solutions" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <div className="w-full grid grid-cols-4 gap-4">
            <FormField
              control={form.control}
              name="gender"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Gender </FormLabel>
                  <Select
                    required
                    onValueChange={(gender: AgentGender) => {
                      field.onChange(gender);
                      setSelectedGender(gender);
                    }}
                    value={gender}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an option" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {AGENT_GENDER_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="language"
              render={({ field }) => {
                return (
                  <FormItem className="w-full">
                    <FormLabel>Language</FormLabel>
                    <Select
                      required
                      onValueChange={(value: string) => {
                        field.onChange(value);
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a language" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.keys(AgentLanguagesLabels).map((langKey) => {
                          const lbl = AgentLanguagesLabels[langKey];
                          return (
                            <SelectItem
                              key={langKey}
                              value={String(langKey)}
                              defaultValue={AgentLanguage.EN_US}
                            >
                              {lbl}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />
            <FormField
              control={form.control}
              name="voice"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Voice</FormLabel>
                  <Select
                    required
                    onValueChange={(value: string) => {
                      if (value) {
                        field.onChange(value);
                      }
                    }}
                    value={voice}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an option" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {VOICE_OPTIONS[gender].map(({ value, label, sample }) => (
                        <div
                          key={value}
                          className="flex items-center justify-between"
                        >
                          <SelectItem value={value}>{label}</SelectItem>
                          <AudioPreviewComponent
                            audioSrc={sample}
                            currentAudio={currentAudio}
                            setCurrentAudio={setCurrentAudio}
                          />
                        </div>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="emotionalState"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel>Emotional state</FormLabel>

                  <Select
                    required
                    onValueChange={field.onChange}
                    value={emotionalState}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an option" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
                        callType as keyof typeof AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE
                      ].map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <EmotionBadge emotionalState={option.value} />
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="personalDetails"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>Personal details </FormLabel>
                <p className="text-muted-foreground">
                  i.e. age, location, hobbies, passions, family, etc.
                </p>
                <FormControl>
                  <AddBulletsField field={field} placeholder="Enter" />
                </FormControl>
                <AIBotCreatorErrorDisplay fieldState={fieldState} />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="openerLine"
          render={({ field }) => (
            <FormItem className="pt-3">
              <FormLabel
                className={cn(openerLineIsDisabled && 'text-zinc-500')}
              >
                Opener line
              </FormLabel>

              <FormControl>
                <Textarea
                  onChange={(e: ChangeEvent<HTMLTextAreaElement>) => {
                    const value = e.target.value;
                    field.onChange(value);

                    setCurrentMessages(() => [
                      {
                        id: 1,
                        role: 'buyer',
                        message: `${value}`,
                      },
                    ]);
                    setResumeCalls([value]);
                  }}
                  className="p-2 w-full h-20 rounded-lg  border border-zinc-200 grow shrink  text-sm font-normal leading-tight resize-none"
                  placeholder={DEFAULT_OPENING_LINE}
                  value={
                    openerLineIsDisabled
                      ? 'Using resume calls in lieu of Opener line. Clear resume calls to edit this field.'
                      : field.value
                  }
                  disabled={openerLineIsDisabled}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </AccordionContent>
    </AccordionItem>
  );
}
