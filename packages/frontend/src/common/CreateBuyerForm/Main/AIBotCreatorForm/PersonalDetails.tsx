import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { DicesIcon, Trash2, Upload, User2Icon } from 'lucide-react';
import {
  ChangeEvent,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import {
  AgentCallType,
  AgentDto,
  AgentEmotionalState,
  AgentVoice,
  StepOneData,
} from '@/lib/Agent/types';
import {
  AgentGender,
  AgentLanguage,
  AgentLanguagesLabels,
} from '@/lib/Agent/types';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE,
  AGENT_GENDER_OPTIONS,
  DEFAULT_OPENING_LINE,
  MULTI_PARTY_VOICE_OPTIONS,
  VOICE_OPTIONS,
} from '../../constants';
import { Button } from '@/components/ui/button';
import AddBulletsField from '@/components/AddBulletsField';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';
import {
  generateMultiPartyVoice,
  generatePersona,
  getRandomFirstNames,
  getRandomSurnames,
  randomAvatar,
} from '../utils';
import EmotionBadge from '../../BotPreview/EmotionBadge';
import { Textarea } from '@/components/ui/textarea';
import AudioPreviewComponent from '@/components/ui/audioclip';
import AgentAvatar from '@/components/Avatars/Agent';
import useAvatars from '@/hooks/useAvatars';
import AIBotCreatorErrorDisplay from './AIBotCreatorErrorDisplay';
import { cn } from '@/lib/utils';
import { AvatarComponent } from '@/components/Avatars/AvatarComponent';
import { updateSupportingAgentInfo } from './MultiParty/updateSupportUtils';
import AddBulletsValue from '@/components/AddBulletsValue';
import ConfirmationModal from '@/components/ConfirmationModal';
import { useQueryClient } from '@tanstack/react-query';
import { CreateBuyerBotContext } from '@/contexts/CreateBuyerBotContext';
import { useDropzone } from 'react-dropzone';
import { toast } from 'react-toastify';

export interface IAIBotCreatorFormPersonalDetailsProps {
  supportingAgent?: AgentDto;
  supportingAgentIndex?: number;
  setSelectedBot?: React.Dispatch<React.SetStateAction<string>>;
}

export default function AIBotCreatorFormPersonalDetails({
  supportingAgent,
  supportingAgentIndex,
  setSelectedBot,
}: IAIBotCreatorFormPersonalDetailsProps) {
  const {
    form,
    callType,
    isPersonalDetailsValid,
    resumeCalls,
    setCurrentMessages,
    setResumeCalls,
    supportingAgentInfo,
    setSupportingAgentInfo,
    currentTab,
  } = useContext(CreateBuyerBotEditFormContext);
  const { defaultValues } = useContext(CreateBuyerBotContext);
  const queryClient = useQueryClient();
  const gender = form.watch('gender');
  const [openDeleteConfModal, setOpenDeleteConfModal] = useState(false);
  const emotionalState = form.watch('emotionalState');
  const voice = form.watch('voice');
  const { data: maleAvatarOptions } = useAvatars(AgentGender.MALE);
  const { data: femaleAvatarOptions } = useAvatars(AgentGender.FEMALE);

  const avatarOptions = useMemo(() => {
    const currentGender = supportingAgent ? supportingAgent.gender : gender;
    return currentGender === AgentGender.MALE
      ? maleAvatarOptions
      : femaleAvatarOptions;
  }, [gender, supportingAgentIndex]);

  const surnameSuggestions = useMemo(() => getRandomSurnames(), []);
  const firstNameSuggestions = useMemo(
    () =>
      getRandomFirstNames(supportingAgent ? supportingAgent?.gender : gender),
    [gender, supportingAgentIndex],
  );
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(
    null,
  );

  const openerLineIsDisabled = useMemo(() => {
    return resumeCalls.length > 1;
  }, [resumeCalls]);

  const FINAL_VOICE_OPTIONS = useMemo(() => {
    return currentTab === 'multi-party'
      ? MULTI_PARTY_VOICE_OPTIONS
      : VOICE_OPTIONS;
  }, [currentTab]);

  // activates on refresh / initial load / tab switch to handle default voice selection
  useEffect(() => {
    if (currentTab === 'multi-party') {
      const currentVoice = form.getValues('voice');
      if (
        currentVoice &&
        !MULTI_PARTY_VOICE_OPTIONS[gender].some(
          (option) => option.value === currentVoice,
        )
      ) {
        // Get existing voice options
        const supportingVoiceList = supportingAgentInfo?.map(
          (agent) => agent.voice,
        );
        const generatedVoice = generateMultiPartyVoice(
          gender,
          supportingVoiceList,
        ) as AgentVoice;
        // Any is important here: removes deep recursive zod + ts type checking, have to downgrade zod to fix
        (form.setValue as any)('voice', generatedVoice);
      }
    } else {
      const currentVoice = form.getValues('voice');
      if (
        currentVoice &&
        !VOICE_OPTIONS[gender].some((option) => option.value === currentVoice)
      ) {
        // Set it as the draft voice
        const defaultVoice =
          gender === AgentGender.MALE ? 'chris-playht' : 'jennifer-playht';
        (form.setValue as any)('voice', defaultVoice);
      }
    }
  }, [currentTab, gender, supportingAgentInfo]);

  const handleGenerateRandomAvatar = () => {
    const [avatar, avatarUrl] = randomAvatar(avatarOptions);
    if (
      supportingAgent &&
      supportingAgentIndex !== undefined &&
      avatar &&
      avatarUrl
    ) {
      if (setSupportingAgentInfo) {
        updateSupportingAgentInfo(
          setSupportingAgentInfo,
          supportingAgentIndex,
          { avatar: avatar, avatarUrl: avatarUrl },
        );
      }
    } else {
      form.setValue('avatar', avatar || '');
      form.setValue('avatarUrl', avatarUrl);
    }
  };

  const loadFile = useCallback(async (files: File[]) => {
    if (files) {
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > 1024 * 1024 * 2) {
          //2MB
          toast.error('File size must be less than 2MB');
          return;
        }
      }
    }

    if (files[0]) {
      const reader = new FileReader();
      reader.onload = (e) => {
        form.setValue('avatarBase64', e.target?.result as string);
      };
      reader.readAsDataURL(files[0]);
    }
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: loadFile,
  });

  const handleGenderChangeForPrimaryBot = (newGender: AgentGender) => {
    if (currentTab === 'multi-party') {
      const avatarList = supportingAgentInfo?.map((agent) => agent.avatar);
      const voiceList = supportingAgentInfo?.map((agent) => agent.voice);
      // Get avatar
      const avatarOptions =
        newGender === AgentGender.MALE
          ? maleAvatarOptions
          : femaleAvatarOptions;
      const filteredAvatarOptions =
        avatarOptions &&
        Object.fromEntries(
          Object.entries(avatarOptions).filter(
            ([key]) => !avatarList?.includes(key),
          ),
        );
      // Process avatar options
      const [avatar, avatarUrl] = randomAvatar(filteredAvatarOptions);
      // Generate voice
      const voice = generateMultiPartyVoice(newGender, voiceList);
      const persona = {
        avatar: avatar,
        avatarUrl: avatarUrl,
        voice: voice,
        gender: newGender,
      };
      Object.entries(persona).forEach(([key, value]) => {
        form.setValue(key as keyof StepOneData, value);
      });
    } else {
      const avatarOptions =
        newGender === AgentGender.MALE
          ? maleAvatarOptions
          : femaleAvatarOptions;
      let persona = generatePersona(newGender, avatarOptions);
      const finalPersona = {
        ...persona,
        gender: newGender,
      };
      Object.entries(finalPersona).forEach(([key, value]) => {
        form.setValue(key as keyof StepOneData, value);
      });
    }
  };

  const handleGenderChangeForSupportingBot = (newGender: AgentGender) => {
    // Get the previous list of avatars and voices
    const primaryVoice = form.getValues('voice');
    const primaryAvatar = form.getValues('avatar');
    const voiceList = [primaryVoice];
    const avatarList = [primaryAvatar];
    // Get avatar
    const avatarOptions =
      newGender === AgentGender.MALE ? maleAvatarOptions : femaleAvatarOptions;
    const filteredAvatarOptions =
      avatarOptions &&
      Object.fromEntries(
        Object.entries(avatarOptions).filter(
          ([key]) => !avatarList.includes(key),
        ),
      );
    // Process avatar options
    const [avatar, avatarUrl] = randomAvatar(filteredAvatarOptions);
    // Generate voice
    const voice = generateMultiPartyVoice(newGender, voiceList);
    if (setSupportingAgentInfo && supportingAgentIndex !== undefined) {
      updateSupportingAgentInfo(setSupportingAgentInfo, supportingAgentIndex, {
        gender: newGender,
        avatar: avatar,
        avatarUrl: avatarUrl,
        voice: voice,
      });
    }
  };
  return (
    <>
      <ConfirmationModal
        open={openDeleteConfModal}
        onCancel={() => setOpenDeleteConfModal(false)}
        onConfirm={() => {
          // Update local state
          if (
            setSupportingAgentInfo &&
            supportingAgentIndex !== undefined &&
            setSelectedBot &&
            supportingAgentInfo
          ) {
            // Update local storage
            const cacheSupportingAgentInfo = supportingAgentInfo.filter(
              (_: AgentDto, index: number) => index !== supportingAgentIndex,
            );
            setSupportingAgentInfo((prev) => {
              const updatedBotList = prev.filter(
                (_: AgentDto, index: number) => index !== supportingAgentIndex,
              );
              return updatedBotList;
            });
            // Update selectedBot
            const newSelectedBot =
              supportingAgentIndex === 0
                ? 'primary'
                : `supporting-${supportingAgentIndex - 1}`;
            setSelectedBot(newSelectedBot);
            const aiGenerator = {
              ...defaultValues.aiGenerator,
            };
            const primaryAgent = { ...form.getValues() };
            // Store multi party in cache
            const draftAgent = {
              ...primaryAgent,
              aiGenerator: aiGenerator,
              supportingAgentInfo: cacheSupportingAgentInfo,
            };
            queryClient.setQueryData(['draftAgent'], draftAgent);
            setOpenDeleteConfModal(false);
          }
        }}
        title="Remove bot"
        description={'Are you sure you want to remove this bot?'}
      />
      <AccordionItem value="Personal Details" className="border border-none">
        <AIBotCreatorFormAccordionTrigger
          Icon={User2Icon}
          isValid={supportingAgent ? true : isPersonalDetailsValid}
          heading={'Personal Details'}
        />
        <AccordionContent className="pb-2 border border-none shadow-none">
          <div className="space-y-4">
            <div className="w-full grid grid-cols-2 gap-4 relative">
              {supportingAgent ? (
                <>
                  <div className="flex items-start gap-4">
                    <FormItem className="w-full">
                      <div className="flex items-start gap-4">
                        <AvatarComponent
                          imageUrl={supportingAgent?.avatarUrl}
                          fallbackText={`${supportingAgent?.firstName?.charAt(0)} ${supportingAgent?.lastName?.charAt(0)}`}
                          className={'w-16 h-16'}
                        />

                        <div className="flex flex-col">
                          <FormLabel>Profile photo</FormLabel>
                          <div className="grid grid-cols-2 gap-4 mt-2">
                            <FormControl>
                              <div className="mt-1 flex flex-row gap-2">
                                <div {...getRootProps()}>
                                  <input {...getInputProps()} />
                                  <Button
                                    className="w-full"
                                    type="button"
                                    variant={'outline'}
                                  >
                                    <Upload className="mr-2 h-4 w-4" /> Upload
                                    new
                                  </Button>
                                </div>
                                <Button
                                  onClick={() => {
                                    handleGenerateRandomAvatar();
                                  }}
                                  className="w-full"
                                  type="button"
                                  variant={'outline'}
                                >
                                  <DicesIcon className="mr-2 h-4 w-4" />{' '}
                                  Generate
                                </Button>
                              </div>
                            </FormControl>
                          </div>
                        </div>
                      </div>
                    </FormItem>
                    <div className="absolute top-0 right-0">
                      <Button
                        variant="outline"
                        className="text-[#EF4444] flex items-center hover:text-red-500"
                        onClick={() => setOpenDeleteConfModal(true)}
                        type="button"
                      >
                        <Trash2 className="w-4 h-4 mr-2 hover:text-red-500" />
                        Remove Bot
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <FormField
                  control={form.control}
                  name="avatar"
                  render={() => {
                    return (
                      <FormItem className="w-full">
                        <div className="flex items-start gap-4">
                          <AgentAvatar className="w-16 h-16" />

                          <div className="flex flex-col">
                            <FormLabel>Profile photo</FormLabel>
                            <div className="grid grid-cols-2 gap-4 mt-2">
                              <FormControl>
                                <div className="mt-1 flex flex-row gap-2">
                                  <div {...getRootProps()}>
                                    <input {...getInputProps()} />
                                    <Button
                                      onClick={() => {}}
                                      className="w-full"
                                      type="button"
                                      variant={'outline'}
                                    >
                                      <Upload className="mr-2 h-4 w-4" /> Upload
                                      new
                                    </Button>
                                  </div>

                                  <Button
                                    onClick={() => {
                                      handleGenerateRandomAvatar();
                                    }}
                                    className="w-full"
                                    type="button"
                                    variant={'outline'}
                                  >
                                    <DicesIcon className="mr-2 h-4 w-4" />{' '}
                                    Generate
                                  </Button>
                                </div>
                              </FormControl>
                            </div>
                          </div>
                        </div>
                      </FormItem>
                    );
                  }}
                />
              )}
              <br />
              {supportingAgent ? (
                <>
                  <FormItem className="w-full">
                    <FormLabel>First name</FormLabel>
                    <FormControl>
                      <Input
                        required
                        placeholder="Jane"
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          if (
                            setSupportingAgentInfo &&
                            supportingAgentIndex !== undefined
                          ) {
                            updateSupportingAgentInfo(
                              setSupportingAgentInfo,
                              supportingAgentIndex,
                              { firstName: e.target.value },
                            );
                          }
                        }}
                        value={supportingAgent.firstName}
                        className="mt-2"
                      />
                    </FormControl>
                    <div className="flex mt-2 space-x-2 text-sm">
                      <div className="text-zinc-500">Suggestions:</div>
                      <div className="flex space-x-2">
                        {firstNameSuggestions.map((suggestion) => (
                          <button
                            key={suggestion}
                            type="button"
                            onClick={() => {
                              if (
                                setSupportingAgentInfo &&
                                supportingAgentIndex !== undefined
                              ) {
                                updateSupportingAgentInfo(
                                  setSupportingAgentInfo,
                                  supportingAgentIndex,
                                  { firstName: suggestion },
                                );
                              }
                            }}
                            className="text-[#2091ae] font-medium hover:underline"
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    </div>
                  </FormItem>
                </>
              ) : (
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>First name</FormLabel>
                      <FormControl>
                        <Input required placeholder="Jane" {...field} />
                      </FormControl>
                      <div className="flex mt-2 space-x-2 text-sm">
                        <div className="text-zinc-500">Suggestions:</div>
                        <div className="flex space-x-2">
                          {firstNameSuggestions.map((suggestion) => (
                            <button
                              key={suggestion}
                              type="button"
                              onClick={() => field.onChange(suggestion)}
                              className="text-[#2091ae] font-medium hover:underline"
                            >
                              {suggestion}
                            </button>
                          ))}
                        </div>
                      </div>
                    </FormItem>
                  )}
                />
              )}
              {supportingAgent ? (
                <>
                  <FormItem className="w-full">
                    <FormLabel>Last name</FormLabel>
                    <FormControl>
                      <Input
                        required
                        placeholder="Bowen"
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          if (
                            setSupportingAgentInfo &&
                            supportingAgentIndex !== undefined
                          ) {
                            updateSupportingAgentInfo(
                              setSupportingAgentInfo,
                              supportingAgentIndex,
                              { lastName: e.target.value },
                            );
                          }
                        }}
                        value={supportingAgent.lastName}
                        className="mt-2"
                      />
                    </FormControl>
                    {
                      /* Suggestions Block */
                      <div className="flex mt-2 space-x-2 text-sm">
                        <div className="text-zinc-500">Suggestions:</div>
                        <div className="flex space-x-2">
                          {surnameSuggestions.map((suggestion) => (
                            <button
                              key={suggestion}
                              type="button"
                              onClick={() => {
                                if (
                                  setSupportingAgentInfo &&
                                  supportingAgentIndex !== undefined
                                ) {
                                  updateSupportingAgentInfo(
                                    setSupportingAgentInfo,
                                    supportingAgentIndex,
                                    { lastName: suggestion },
                                  );
                                }
                              }}
                              className="text-[#2091ae] font-medium hover:underline"
                            >
                              {suggestion}
                            </button>
                          ))}
                        </div>
                      </div>
                    }
                  </FormItem>
                </>
              ) : (
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Last name</FormLabel>
                      <FormControl>
                        <Input required placeholder="Bowen" {...field} />
                      </FormControl>
                      {
                        /* Suggestions Block */
                        <div className="flex mt-2 space-x-2 text-sm">
                          <div className="text-zinc-500">Suggestions:</div>
                          <div className="flex space-x-2">
                            {surnameSuggestions.map((suggestion) => (
                              <button
                                key={suggestion}
                                type="button"
                                onClick={() => field.onChange(suggestion)}
                                className="text-[#2091ae] font-medium hover:underline"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        </div>
                      }
                    </FormItem>
                  )}
                />
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              {supportingAgent ? (
                <>
                  <FormItem className="w-full">
                    <FormLabel>Job title</FormLabel>
                    <FormControl>
                      <Input
                        required
                        placeholder="i.e. Director of Sales"
                        onChange={(e) => {
                          if (
                            setSupportingAgentInfo &&
                            supportingAgentIndex !== undefined
                          ) {
                            updateSupportingAgentInfo(
                              setSupportingAgentInfo,
                              supportingAgentIndex,
                              {
                                jobTitle: e.target.value,
                              },
                            );
                          }
                        }}
                        value={supportingAgent?.jobTitle}
                      />
                    </FormControl>
                  </FormItem>
                </>
              ) : (
                <FormField
                  control={form.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem className="w-full mt-[6px]">
                      <FormLabel className="flex items-center">
                        Job title
                      </FormLabel>
                      <FormControl>
                        <Input
                          required
                          placeholder="i.e. Director of Sales"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}
              {/* Use common company name across all bots */}
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      {currentTab === 'multi-party'
                        ? 'Company name (common across all bots)'
                        : 'Company name'}
                    </FormLabel>
                    <FormControl>
                      <Input
                        required
                        placeholder="Agile Solutions"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="w-full grid grid-cols-4 gap-4">
              {supportingAgent ? (
                <>
                  <FormItem className="w-full">
                    <FormLabel>Gender </FormLabel>
                    <Select
                      required
                      onValueChange={(gender: AgentGender) => {
                        handleGenderChangeForSupportingBot(gender);
                      }}
                      value={supportingAgent.gender}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {AGENT_GENDER_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                </>
              ) : (
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Gender </FormLabel>
                      <Select
                        required
                        onValueChange={(gender: AgentGender) => {
                          handleGenderChangeForPrimaryBot(gender);
                        }}
                        value={gender}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an option" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {AGENT_GENDER_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              {currentTab !== 'multi-party' && (
                <FormField
                  control={form.control}
                  name="language"
                  render={({ field }) => {
                    return (
                      <FormItem className="w-full">
                        <FormLabel>Language</FormLabel>
                        <Select
                          required
                          onValueChange={(value: string) => {
                            field.onChange(value);
                          }}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Choose a language" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.keys(AgentLanguagesLabels).map(
                              (langKey) => {
                                const lbl = AgentLanguagesLabels[langKey];
                                return (
                                  <SelectItem
                                    key={langKey}
                                    value={String(langKey)}
                                    defaultValue={AgentLanguage.EN_US}
                                  >
                                    {lbl}
                                  </SelectItem>
                                );
                              },
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              )}

              {supportingAgent ? (
                <>
                  <FormItem className="w-full">
                    <FormLabel>Voice</FormLabel>
                    <Select
                      required
                      onValueChange={(value: string) => {
                        if (value) {
                          if (
                            setSupportingAgentInfo &&
                            supportingAgentIndex !== undefined
                          ) {
                            updateSupportingAgentInfo(
                              setSupportingAgentInfo,
                              supportingAgentIndex,
                              { voice: value as AgentVoice },
                            );
                          }
                        }
                      }}
                      value={supportingAgent?.voice}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {FINAL_VOICE_OPTIONS[supportingAgent.gender].map(
                          ({ value, label, sample }) => (
                            <div
                              key={value}
                              className="flex items-center justify-between"
                            >
                              <SelectItem value={value}>{label}</SelectItem>
                              <AudioPreviewComponent
                                audioSrc={sample}
                                currentAudio={currentAudio}
                                setCurrentAudio={setCurrentAudio}
                              />
                            </div>
                          ),
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                </>
              ) : (
                form.watch('language') === AgentLanguage.EN_US && (
                  <FormField
                    control={form.control}
                    name="voice"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>Voice</FormLabel>
                        <Select
                          required
                          onValueChange={(value: string) => {
                            if (value) {
                              field.onChange(value);
                            }
                          }}
                          value={voice}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Choose an option" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {FINAL_VOICE_OPTIONS[gender].map(
                              ({ value, label, sample }) => (
                                <div
                                  key={value}
                                  className="flex items-center justify-between"
                                >
                                  <SelectItem value={value}>{label}</SelectItem>
                                  <AudioPreviewComponent
                                    audioSrc={sample}
                                    currentAudio={currentAudio}
                                    setCurrentAudio={setCurrentAudio}
                                  />
                                </div>
                              ),
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )
              )}
              {supportingAgent
                ? callType !== AgentCallType.FOCUS && (
                    <FormItem className="w-full">
                      <FormLabel>Emotional state</FormLabel>
                      <Select
                        required
                        onValueChange={(value: string) => {
                          if (value) {
                            if (
                              setSupportingAgentInfo &&
                              supportingAgentIndex !== undefined
                            ) {
                              updateSupportingAgentInfo(
                                setSupportingAgentInfo,
                                supportingAgentIndex,
                                {
                                  emotionalState: value as AgentEmotionalState,
                                },
                              );
                            }
                          }
                        }}
                        value={supportingAgent.emotionalState}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an option" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
                            callType as keyof typeof AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE
                          ]?.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <EmotionBadge emotionalState={option.value} />
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )
                : callType !== AgentCallType.FOCUS && (
                    <FormField
                      control={form.control}
                      name="emotionalState"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>Emotional state</FormLabel>

                          <Select
                            required
                            onValueChange={field.onChange}
                            value={emotionalState}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Choose an option" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
                                callType as keyof typeof AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE
                              ]?.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  <EmotionBadge emotionalState={option.value} />
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  )}
            </div>
            {supportingAgent
              ? callType !== AgentCallType.FOCUS && (
                  <FormItem>
                    <FormLabel>Personal details </FormLabel>
                    <p className="text-muted-foreground">
                      i.e. age, location, hobbies, passions, family, etc.
                    </p>
                    <FormControl>
                      <AddBulletsValue
                        valueList={supportingAgent.personalDetails}
                        updateList={(newList: string[]) => {
                          if (
                            setSupportingAgentInfo &&
                            supportingAgentIndex !== undefined
                          ) {
                            updateSupportingAgentInfo(
                              setSupportingAgentInfo,
                              supportingAgentIndex,
                              { personalDetails: newList },
                            );
                          }
                        }}
                        placeholder="Enter"
                      />
                    </FormControl>
                  </FormItem>
                )
              : callType !== AgentCallType.FOCUS && (
                  <FormField
                    control={form.control}
                    name="personalDetails"
                    render={({ field, fieldState }) => (
                      <FormItem>
                        <FormLabel>Personal details </FormLabel>
                        <p className="text-muted-foreground">
                          i.e. age, location, hobbies, passions, family, etc.
                        </p>
                        <FormControl>
                          <AddBulletsField field={field} placeholder="Enter" />
                        </FormControl>
                        <AIBotCreatorErrorDisplay fieldState={fieldState} />
                      </FormItem>
                    )}
                  />
                )}
          </div>

          {currentTab === 'multi-party' ? (
            <></>
          ) : (
            <FormField
              control={form.control}
              name="openerLine"
              render={({ field }) => (
                <FormItem className="pt-3">
                  <FormLabel
                    className={cn(openerLineIsDisabled && 'text-zinc-500')}
                  >
                    Opener line
                  </FormLabel>

                  <FormControl>
                    <Textarea
                      onChange={(e: ChangeEvent<HTMLTextAreaElement>) => {
                        const value = e.target.value;
                        field.onChange(value);

                        setCurrentMessages(() => [
                          {
                            id: 1,
                            role: 'buyer',
                            message: `${value}`,
                          },
                        ]);
                        setResumeCalls([value]);
                      }}
                      className="p-2 w-full h-20 rounded-lg  border border-zinc-200 grow shrink  text-sm font-normal leading-tight resize-none"
                      placeholder={DEFAULT_OPENING_LINE}
                      value={
                        openerLineIsDisabled
                          ? 'Using resume calls in lieu of Opener line. Clear resume calls to edit this field.'
                          : field.value
                      }
                      disabled={openerLineIsDisabled}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          )}
        </AccordionContent>
      </AccordionItem>
    </>
  );
}
