import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useContext, useEffect, useState } from 'react';
import { AccordionContent, AccordionItem } from '@/components/ui/accordion';
import AIBotCreatorFormAccordionTrigger from './AIBotCreatorFormAccordionTrigger';

import { Pencil, PlusIcon, TargetIcon } from 'lucide-react';
import ScorecardPreviewRepView from '@/common/Scorecards/scorecardPreview/repView';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';

export default function AIBotCreatorFormScorecards() {
  const {
    form,
    filteredScorecardConfigOptions,
    isScorecardValid,
    callType,
    existingAgent,
  } = useContext(CreateBuyerBotEditFormContext);
  const router = useRouter();

  const formScorecards = form.watch('scorecardConfigId');
  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);

  const init = async () => {
    let scorecardConfigId = '';
    let found = false;
    const _allScorecards = [];
    if (filteredScorecardConfigOptions) {
      for (const scorecardConfig of filteredScorecardConfigOptions) {
        _allScorecards.push(scorecardConfig);
        if (scorecardConfig.id === existingAgent?.scorecardConfigId) {
          found = true;
        }
        if (!!scorecardConfig.callTypes && !!scorecardConfig.id) {
          const callTypeOpt = scorecardConfig.callTypes.find(
            (ct) => ct.callType === callType,
          );
          if (callTypeOpt) {
            scorecardConfigId = scorecardConfig.id.toString();
          }
        }
      }
    }
    if (!found && existingAgent?.scorecardConfigId) {
      const sc = await ScorecardConfigService.getScorecardConfigById(
        existingAgent.scorecardConfigId,
      );
      if (sc) {
        _allScorecards.push(sc);
      }
    }

    setAllScorecards(_allScorecards);

    if (formScorecards) {
      return;
    } else {
      if (scorecardConfigId) {
        form.setValue('scorecardConfigId', scorecardConfigId);
      }
    }
  };

  useEffect(() => {
    init();
  }, [filteredScorecardConfigOptions, existingAgent, formScorecards]);

  return (
    <AccordionItem value="Scorecard" className="border border-none">
      <AIBotCreatorFormAccordionTrigger
        Icon={TargetIcon}
        isValid={isScorecardValid}
        heading={'Scorecard'}
      />
      <AccordionContent>
        <div className="flex items-center">
          <div className="flex-1">
            <FormField
              control={form.control}
              name="scorecardConfigId"
              render={({ field, fieldState }) => (
                <FormItem className="w-full" onBlur={field.onBlur}>
                  <FormLabel>Scorecard</FormLabel>
                  <p className="text-muted-foreground">
                    Select between the default or custom scorecards to use for
                    this call
                  </p>
                  <Select
                    required
                    onValueChange={(value: string) => {
                      field.onChange(value);
                    }}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a scorecard" />
                      </SelectTrigger>
                    </FormControl>

                    <SelectContent className="relative">
                      {/* Scrollable List of Scorecards */}
                      <div className="max-h-60 overflow-y-auto">
                        {allScorecards?.map((option) => (
                          <SelectItem key={option.id} value={String(option.id)}>
                            {option.tag}
                          </SelectItem>
                        ))}
                      </div>

                      {/* Fixed Button at the Bottom */}
                      <div className="sticky bottom-0 bg-white border-t border-gray-200 mt-1 pt-1">
                        <Button
                          variant="ghost"
                          onClick={(e) => {
                            e.preventDefault();
                            router.push('/scorecards/new');
                          }}
                          className="font-normal w-full h-8 px-2 py-1.5 hover:bg-gray-50 justify-start gap-2 inline-flex text-left"
                        >
                          <PlusIcon className="w-4 h-4 overflow-hidden" />
                          <div className="grow shrink basis-0 text-sm leading-tight text-left">
                            Create a new scorecard
                          </div>
                        </Button>
                      </div>
                    </SelectContent>
                  </Select>
                  <FormMessage>{fieldState.error?.message}</FormMessage>
                </FormItem>
              )}
            />
          </div>
          <div className="ml-2 mt-14">
            <Button
              variant={'outline'}
              onClick={(e) => {
                e.preventDefault();
                router.push('/scorecards/' + formScorecards + '/edit'); //edit
                //router.push('/scorecards?id=' + formScorecards); //view
              }}
            >
              <Pencil size={16} />
            </Button>
          </div>
        </div>

        {allScorecards && (
          <>
            {allScorecards.map((option) => {
              if (option.id) {
                if (String(option.id) == form.getValues().scorecardConfigId) {
                  return (
                    <ScorecardPreviewRepView
                      key={option.id}
                      scorecard={option}
                    />
                  );
                }
              }
            })}
          </>
        )}
      </AccordionContent>
    </AccordionItem>
  );
}
