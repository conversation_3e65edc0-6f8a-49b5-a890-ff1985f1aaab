import {
  DEFAULT_OPENER_LINE,
  VOICE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import {
  Agent<PERSON><PERSON>,
  AgentFolderDto,
  AgentGender,
  AgentVoice,
  AnyAgentDto,
  CreateAgentDto,
  CreateBuyerDefaultFormValues,
  ResearchData,
  StepOneData,
  TagDto,
  UpdateAgentDto,
} from '@/lib/Agent/types';

import * as z from 'zod';

import { Id, toast as toastType } from 'react-toastify';
import { UseMutationResult } from '@tanstack/react-query';
import { random } from '@/lib/utils';

interface BuildStepOneData extends StepOneData {
  [key: string]: unknown;
}

export const surnames: string[] = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Wright',
];

export function getRandomSurnames(): string[] {
  const shuffled = surnames.sort(() => 0.5 - random()); // Shuffle array
  return shuffled.slice(0, 3); // Get first 3 elements
}

export const getResearchData = (agentData?: AnyAgentDto): ResearchData => {
  if (!agentData?.research) return {} as ResearchData;
  try {
    return JSON.parse(agentData.research) as ResearchData;
  } catch {
    return {} as ResearchData;
  }
};

export function getCurrentMessages(agent?: AgentDto): string[] {
  const { messages, openerLine } = getResearchData(agent);
  const messageArray = messages?.split('\n').filter((m) => !!m);

  // Check if messageArray exists and has length
  if (messageArray && messageArray.length > 0) {
    return messageArray;
  }

  // Return openerLine if it exists, otherwise DEFAULT_OPENER_LINE
  return openerLine ? [openerLine] : [DEFAULT_OPENER_LINE];
}

export function areMessagesEqual(
  arr1: ParsedTranscriptMessage[],
  arr2: ParsedTranscriptMessage[],
): boolean {
  // Check if both arrays have the same length
  if (arr1.length !== arr2.length) return false;

  // Compare each message object at the same index in both arrays
  return arr1.every((message, index) => {
    const otherMessage = arr2[index];
    return (
      message.id === otherMessage.id &&
      message.message === otherMessage.message &&
      message.role === otherMessage.role
    );
  });
}

export function convertToParsedTranscriptMessages(
  arr: string[],
): ParsedTranscriptMessage[] {
  return arr.map((message, index) => {
    return {
      id: index + 1, // Using index + 1 for a simple ID (starting from 1)
      message: message,
      role: !(index % 2) ? 'buyer' : 'seller', // Alternating roles for simplicity
    };
  });
}

export function getRandomGender(): AgentGender {
  const genders = [AgentGender.MALE, AgentGender.FEMALE];
  const randomIndex = Math.floor(random() * genders.length); // Get a random index
  return genders[randomIndex];
}

export const randomAvatar = (
  avatars: Record<string, string> = {},
): [string, string] | [undefined, undefined] => {
  const entries = Object.entries(avatars);
  return entries.length
    ? entries[Math.floor(random() * entries.length)]
    : [undefined, undefined];
};

export const generateVoice = (
  gender: AgentGender = getRandomGender(),
): AgentVoice =>
  VOICE_OPTIONS[gender][Math.floor(random() * VOICE_OPTIONS[gender].length)]
    .value;

export const generatePersona = (
  gender?: AgentGender,
  avatarOptions?: Record<string, string>,
) => {
  const [avatar, avatarUrl] = randomAvatar(avatarOptions);
  return {
    avatar,
    avatarUrl,
    voice: generateVoice(gender),
  };
};

export const ConfigDetailsFormSchema = z.object({
  folders: z.array(z.any()).min(1, 'Select a folder to publish a bot'),
  tags: z.array(z.any()).optional(),
});

export const BasicDetailsFormSchema = z.object({
  description: z.string().min(1, 'This field is required'),
  warm_call_context: z.string().optional(),
  discovery_call_context: z.string().optional(),
  checkin_call_context: z.string().optional(),
  renewal_call_context: z.string().optional(),
  demo_call_context: z.string().optional(),
});

export const PersonalDetailsFormSchema = z.object({
  firstName: z.string().min(1, 'This field is required'),
  lastName: z.string().min(1, 'This field is required'),
  gender: z.string().min(1, 'This field is required'),
  language: z.string().optional(),
  avatar: z.string().min(1, 'This field is required'),
  jobTitle: z.string().min(1, 'This field is required'),
  personalDetails: z.array(z.string()).min(1, 'This field is required'),
  voice: z.string().min(1, 'This field is required'),
  companyName: z.string().min(1, 'This field is required'),
  emotionalState: z.string().min(1, 'This field is required'),
  openerLine: z.string().optional(),
});

export const CompanyDetailsFormSchema = z.object({
  companyDetails: z
    .array(z.string())
    .min(1, 'Provide at least 1 company detail'),
  companyOrgStructure: z.array(z.string()).min(1, 'This field is required'),
});

export const PrioritiesAndObjectionsFormSchema = z.object({
  objections: z.array(z.string()).min(1, 'This field is required'),
  goals: z.array(z.string()),
});

export const OpinionsFormSchema = z.object({
  callScenario: z.string().min(1, 'This field is required'),
  opinions: z.array(z.string()).min(1, 'This field is required'),
  problem_aware_info: z.string().optional(),
  solution_aware_info: z.string().optional(),
  incumbent_solution_info: z.string().optional(),
  pre_existing_champion_info: z.string().optional(),
});

export const ScorecardFormSchema = z.object({
  scorecardConfigId: z
    .string({
      required_error: 'Please select a scorecard',
      invalid_type_error: 'Invalid scorecard selection',
    })
    .min(1, 'Please select a scorecard'),
});

export const AdvancedSettingsFormSchema = z.object({
  public_presence: z.string().optional(),
});

export const formSchema = z.object({
  ...ConfigDetailsFormSchema.shape,
  // Basic details
  ...BasicDetailsFormSchema.shape,

  // Personal details
  ...PersonalDetailsFormSchema.shape,

  // Company details
  ...CompanyDetailsFormSchema.shape,

  // Call settings
  ...PrioritiesAndObjectionsFormSchema.shape,

  // Professional opinions
  ...OpinionsFormSchema.shape,

  // Scorecard
  ...ScorecardFormSchema.shape,

  // Advanced settings
  ...AdvancedSettingsFormSchema.shape,
});

export function getZodSchemaFieldsShallow(schema: z.ZodSchema) {
  const fields: Record<string, true> = {};
  const proxy = new Proxy(fields, {
    get(_, key) {
      if (key === 'then' || typeof key !== 'string') {
        return;
      }
      fields[key] = true;
    },
  });
  schema.safeParse(proxy);
  return Object.keys(fields);
}

export function getStartingFormValues(
  defaultValues: CreateBuyerDefaultFormValues,
  formValues: BuildStepOneData,
): StepOneData {
  // Create a new object for starting form values by combining defaultValues and formValues
  if (!defaultValues?.main) {
    return formValues;
  }

  const startingFormValues = Object.entries(
    defaultValues?.main,
  ).reduce<BuildStepOneData>((acc, [key, value]) => {
    acc[key as keyof BuildStepOneData] = formValues[key] || value;
    return acc;
  }, {} as BuildStepOneData);

  return startingFormValues;
}

export const showErrorToast = (
  toast: typeof toastType,
  errorToastId: React.MutableRefObject<Id | null>,
  message: string = 'There was an error updating the buyer. Please try again.',
) => {
  if (errorToastId.current && !toast.isActive(errorToastId.current)) {
    errorToastId.current = toast.error(message);
  }
};

export function diffTags(previousTags: TagDto[] = [], tags: TagDto[] = []) {
  // Convert lists to maps for faster lookups
  const previousTagMap = new Map(previousTags.map((tag) => [tag.id, tag]));
  const currentTagMap = new Map(tags.map((tag) => [tag.id, tag]));

  // Find added tags (present in 'tags' but not in 'previousTags')
  const addTags = tags
    .filter((tag) => !previousTagMap.has(tag.id))
    .map((t) => t.id);

  // Find removed tags (present in 'previousTags' but not in 'tags')
  const removeTags = previousTags
    .filter((tag) => !currentTagMap.has(tag.id))
    .map((t) => t.id);

  return { addTags, removeTags };
}

export function diffFolders(
  previousFolders: AgentFolderDto[] = [],
  formFolders: AgentFolderDto[] = [],
) {
  // Find added folders (present in 'formFolders' but not in 'previousFolders')
  const addFolders = formFolders.filter(
    (formFolder) =>
      !previousFolders.some(
        (prevFolder) => prevFolder.parentFolderId === formFolder.parentFolderId,
      ),
  );

  // Find removed folders (present in 'previousFolders' but not in 'formFolders')
  const removeFolders = previousFolders.filter(
    (prevFolder) =>
      !formFolders.some(
        (formFolder) => formFolder.parentFolderId === prevFolder.parentFolderId,
      ),
  );

  return { addFolders, removeFolders };
}

export function checkLoading(
  createAgentMutation: UseMutationResult<
    AgentDto,
    Error,
    CreateAgentDto,
    unknown
  >,
  updateAgentMutation: UseMutationResult<
    AgentDto,
    Error,
    UpdateAgentDto,
    unknown
  >,
) {
  return (
    updateAgentMutation.isPending ||
    updateAgentMutation.isSuccess ||
    createAgentMutation.isPending ||
    createAgentMutation.isSuccess
  );
}

export const handleAvatarMap = (
  signedUrl: string,
  avatars?: { key: string; signedUrl: string }[],
) => {
  // Find the original key from the signed URL
  return avatars?.find((avatar) => avatar.signedUrl === signedUrl)?.key;
};
