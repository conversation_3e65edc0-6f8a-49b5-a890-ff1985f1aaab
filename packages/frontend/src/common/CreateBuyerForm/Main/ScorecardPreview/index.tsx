import ColdCallScorecard from '@/components/ColdCallScorecard';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto, {
  Criterion,
  ScorecardConfigInnerConfig,
} from '@/lib/ScorecardConfig/types';
import { UserDto } from '@/lib/User/types';
import { motion } from 'framer-motion';

interface IProps {
  scorecard: ScorecardConfigDto;
}

export default function ScorecardPreview({ scorecard }: IProps) {
  const cards: {
    sectionTitle: string;
    passedCriteriaCount: number;
    totalCriteriaCount: number;
    criteria: Criterion[];
  }[] = [];
  if (scorecard?.config) {
    const scorecardKeysSorted = ScorecardConfigService.getSortedKeys(scorecard);
    scorecardKeysSorted.forEach((sectionTitle: string) => {
      const configTyped = scorecard.config as ScorecardConfigInnerConfig;
      const obj = configTyped[sectionTitle];
      obj.forEach((o) => {
        (o as Criterion & { passed?: boolean }).passed = true;
      });
      cards.push({
        sectionTitle,
        passedCriteriaCount: obj.length,
        totalCriteriaCount: obj.length,
        criteria: obj,
      });
    });
  }

  return (
    <div className="grid grid-cols-1 items-stretch gap-4 pb-4 md:grid-cols-2">
      {cards.map((scorecard, i: number) => (
        <motion.div
          key={scorecard?.sectionTitle}
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ y: -50 }}
          transition={{
            duration: 0.4,
            delay: i * 0.1,
          }}
        >
          <ColdCallScorecard
            caller={{ id: 0 } as UserDto}
            scorecard={scorecard}
            loading={false}
            coachingInfo={undefined}
            disableToggleCriteria={true}
          />
        </motion.div>
      ))}
    </div>
  );
}
