import { Form } from '@/components/ui/form';
import { CreateBuyerBotContext } from '@/contexts/CreateBuyerBotContext';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import AgentService, { FoldersService, TagsService } from '@/lib/Agent';
import {
  AgentCallType,
  AgentDto,
  AgentFolderDto,
  AgentStatus,
} from '@/lib/Agent/types';
import UserService from '@/lib/User';
import { useAuthInfo } from '@propelauth/react';
import { QueryKey, useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useContext, useEffect, useMemo, useRef } from 'react';
import { FieldValues, SubmitHandler } from 'react-hook-form';
import { Id, toast, ToastContainer } from 'react-toastify';
import { checkLoading, diffFolders, diffTags, showErrorToast } from './utils';
import { DEFAULT_OPENER_LINE } from '../constants';
import AIBotCreatorFullForm from './AIBotCreatorFullForm';

function Main() {
  const router = useRouter();
  const authInfo = useAuthInfo();
  const { defaultValues, isEditMode } = useContext(CreateBuyerBotContext);
  const {
    form,
    callType,
    existingAgent,
    currentMessages,
    setCreateBuyerBotEditFormSubmitButtonState,
    supportingAgentInfo,
    currentTab,
  } = useContext(CreateBuyerBotEditFormContext);

  const keywords = form.watch('keywords');
  const queryClient = useQueryClient();
  const errorToastId = useRef<Id | null>(null);

  const isDemo = !authInfo?.isLoggedIn;
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const [, currentGatekeeper] = useMemo(
    () => [existingAgent?.gatekeepers?.[0], form?.getValues().gatekeepers],
    [existingAgent?.gatekeepers, form?.getValues().gatekeepers],
  );

  const { addTags, removeTags } = useMemo(
    () => diffTags(existingAgent?.tags, form?.getValues().tags),
    [form?.getValues().tags, existingAgent?.tags],
  );

  const { addFolders, removeFolders } = useMemo(
    () => diffFolders(existingAgent?.folders, form?.getValues().folders),
    [form?.getValues().folders, existingAgent?.folders, form],
  );

  const invalidateRelevantQueries = (agentId: number) => {
    // Get all query keys in the cache
    const existingKeys: QueryKey[] = queryClient
      .getQueryCache()
      .getAll()
      .map((query) => query.queryKey);

    // Define queries to invalidate (including agent-specific queries)
    const keysToInvalidate: (string | QueryKey)[] = [
      ['agent', agentId],
      'agents-folders',
      'agentsTags',
      'aiService',
      'scorecardConfigsOptions',
    ];

    // Filter out only the queries that actually exist in the cache
    const relevantKeys: QueryKey[] = existingKeys.filter((key) =>
      keysToInvalidate.some((k) =>
        Array.isArray(k)
          ? JSON.stringify(key) === JSON.stringify(k)
          : key.includes(k as string),
      ),
    );

    // Invalidate only the relevant queries
    relevantKeys.forEach((key) =>
      queryClient.invalidateQueries({ queryKey: key }),
    );
  };

  const onSuccesses = async (agent: AgentDto) => {
    invalidateRelevantQueries(agent.id);

    //we dont really need this since add gatekeeper to agent will delete the existing gatekeepers if necessary
    // if (currentGatekeeperFirstName !== previousGatekeeperFirstName) {
    //   await AgentService.deleteGatekeeperFromAgent(agent.id);
    // }

    if (currentGatekeeper && agent.id && currentGatekeeper?.id) {
      await AgentService.addGatekeeperToAgent(
        agent.id,
        currentGatekeeper.id,
        currentGatekeeper.scorecardConfigId,
      );
    }

    //wait until gatekeeper are changed so there's no mismatching data on call panel
    const agentId = agent.providerAgentId
      ? agent.providerAgentId
      : agent.vapiId;
    if (isDemo) {
      router.push(`/buyers/${agentId}`);
    } else {
      if (!currentGatekeeper || !currentGatekeeper?.id) {
        if (isEditMode) {
          // just router.push does not work for edit mode unless router is refreshed because it's part of the same route segment tree
          router.push(`/buyers/${agentId}`);
          router.refresh();
        } else {
          router.push(`/buyers/${agentId}`);
        }
      } else {
        // Route to buyers page if there's gatekeeper
        router.push(`/buyers?agentId=${agentId}`);
      }
    }

    if (removeFolders.length > 0) {
      await Promise.all(
        removeFolders.map((f: AgentFolderDto) =>
          FoldersService.deleteFolder(f.id),
        ),
      );
    }
    // Wait for all removeFolder updates to complete before proceeding
    if (addFolders.length > 0) {
      await Promise.all(
        addFolders.map((f: AgentFolderDto) =>
          FoldersService.linkAgentToFolder(agent.id, f.id, f.sort, f?.teamId),
        ),
      );
    }

    if (agent?.id) {
      await TagsService.editTagsAgentsLinks({
        addTags,
        removeTags,
        forAgents: [agent.id],
      });
    }
  };

  const createAgentMutation = useMutation({
    mutationFn: AgentService.createAgent,
    onSuccess: async (agent) => {
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({
        queryKey: ['callDurationByRepAndBuyer'],
      });
      queryClient.removeQueries({ queryKey: ['draftAgent'] });

      if (defaultValues.main?.genBotDraftId) {
        await UserService.markGenerateAgentDraftsAsUsed(
          defaultValues.main?.genBotDraftId,
          agent.id,
        );
      }
      queryClient.invalidateQueries({ queryKey: ['GenBotDrafts'] });

      onSuccesses(agent);
    },
    onError: () => {
      showErrorToast(toast, errorToastId);
    },
  });

  const createFocusAgentMutation = useMutation({
    mutationFn: AgentService.createFocusAgent,
    onSuccess: (agent) => {
      onSuccesses(agent);
      const agentId = agent.providerAgentId
        ? agent.providerAgentId
        : agent.vapiId;
      if (isDemo) {
        router.push(`/buyers/${agentId}`);
      } else {
        router.push(`/buyers?agentId=${agentId}`);
      }
    },
    onError: () => {
      showErrorToast(toast, errorToastId);
    },
  });

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
    onSuccess: async (agent) => {
      onSuccesses(agent);
      const agentId = agent.providerAgentId
        ? agent.providerAgentId
        : agent.vapiId;
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agentId] });
      queryClient.removeQueries({ queryKey: ['draftAgent'] });
      queryClient.invalidateQueries({
        queryKey: ['callDurationByRepAndBuyer'],
      });
    },
    onError: () => {
      showErrorToast(toast, errorToastId);
    },
  });

  const isLoading = useMemo(() => {
    return checkLoading(
      createAgentMutation,
      updateAgentMutation,
      createFocusAgentMutation,
    );
  }, [createAgentMutation, updateAgentMutation, createFocusAgentMutation]);

  const onSubmit: SubmitHandler<FieldValues> = async (data) => {
    //verify resume calls
    let go = true;

    if (
      currentGatekeeper &&
      currentGatekeeper.firstName !== 'None' &&
      currentMessages.length > 1
    ) {
      errorToastId.current = toast.error(
        'Cannot have both gatekeeper and transcript for resuming calls. Please remove gatekeeper or clear Resume call transcript.',
      );
      return;
    }
    let resume_calls_message = '';
    const tmp = currentMessages?.map((m) => m.message);
    if (tmp.length > 0) {
      if (currentMessages[currentMessages.length - 1]?.role === 'buyer') {
        resume_calls_message = tmp.join('\n');
        if (!go) {
          errorToastId.current = toast.error(
            'Resume calls contains empty messages.',
          );
        }
      } else {
        errorToastId.current = toast.error(
          'Resume calls must end with a buyer message. Please add a buyer message to the end of the list.',
        );
        go = false;
      }
    }

    const agentBody = {
      // TODO: Handle Config Details in single API call?
      // folders: data?.folders,
      // tags: data?.tags,
      // gatekeepers: {
      //   id: formValues.gatekeepers?.id,
      //   scorecardConfigId: formValues.gatekeepers?.scorecardConfigId,
      // },
      // Personal details
      firstName: data?.firstName,
      lastName: data?.lastName,
      gender: data?.gender,
      avatar: data?.avatar,
      avatarBase64: data?.avatarBase64,
      jobTitle: data?.jobTitle,
      personalDetails: data?.personalDetails,
      voice: data?.voice,
      companyName: data?.companyName,

      // Company details
      companyDetails: data?.companyDetails,
      companyOrgStructure: data?.companyOrgStructure,
      callType,
      emotionalState: data?.emotionalState,
      research: JSON.stringify({
        public_presence: data?.public_presence || '',
        incumbent_solution_info: data?.incumbent_solution_info || '',
        problem_aware_info: data?.problem_aware_info || '',
        solution_aware_info: data?.solution_aware_info || '',
        pre_existing_champion_info: data?.pre_existing_champion_info || '',
        cold_call_scenario:
          callType === AgentCallType.COLD
            ? data?.callScenario
            : AgentCallType.NONE,
        warm_call_scenario:
          callType === AgentCallType.WARM
            ? data?.callScenario
            : AgentCallType.NONE,
        discovery_call_scenario:
          callType === AgentCallType.DISCOVERY
            ? data?.callScenario
            : AgentCallType.NONE,
        checkin_call_scenario:
          callType === AgentCallType.CHECKIN
            ? data?.callScenario
            : AgentCallType.NONE,
        renewal_call_scenario:
          callType === AgentCallType.RENEWAL
            ? data?.callScenario
            : AgentCallType.NONE,
        demo_call_scenario:
          callType === AgentCallType.DEMO
            ? data?.callScenario
            : AgentCallType.NONE,
        manager_one_on_one_call_scenario:
          callType === AgentCallType.MANAGER_ONE_ON_ONE
            ? data?.callScenario
            : AgentCallType.NONE,
        gatekeeper_call_scenario:
          callType === AgentCallType.GATEKEEPER
            ? data?.callScenario
            : AgentCallType.NONE,
        discovery_call_context: data?.discovery_call_context || '',
        warm_call_context: data?.warm_call_context || '',
        checkin_call_context: data?.checkin_call_context || '',
        renewal_call_context: data?.renewal_call_context || '',
        demo_call_context: data?.demo_call_context || '',
        manager_one_on_one_call_context:
          data?.manager_one_on_one_call_context || '',
        language: data?.language,
        messages: resume_calls_message,
        keywords,
        openerLine: data?.openerLine || DEFAULT_OPENER_LINE,
        // urgency?: string;
        // qualified_prospect?: string;
        // gatekeeper_name?: string;
      }),
      goals: data?.goals,
      objections: data?.objections,
      opinions: data?.opinions,
      scorecardConfigId: Number(data?.scorecardConfigId),
      status: AgentStatus.ACTIVE,
      openerLine: data?.openerLine || DEFAULT_OPENER_LINE,
      description:
        data?.description ||
        `Start a cold call simulation with ${data?.firstName || ''} from ${
          data?.companyName || ''
        }, and learn how to do a cold call effectively. Wear headphones for the best experience.`,
      scenarioName:
        currentTab === 'multi-party' ? data?.scenarioName : undefined,
      supportingAgentInfo:
        currentTab === 'multi-party' ? supportingAgentInfo : undefined,
    };

    if (form?.formState.isValid && go) {
      if (isEditMode) {
        updateAgentMutation.mutate({
          ...agentBody,
          id: existingAgent?.id,
        });
      } else {
        if (callType === AgentCallType.FOCUS) {
          createFocusAgentMutation.mutate({
            ...agentBody,
          });
        } else {
          const createAgentBody = {
            ...agentBody,
            ownerDemoInboundFormResponseId: hbDemoInboundForm?.id,
          };

          createAgentMutation.mutate(createAgentBody);
        }
      }
    }
  };

  useEffect(() => {
    // Use the Submit button outside the form
    setCreateBuyerBotEditFormSubmitButtonState({
      disabled:
        (!form?.formState.isValid && form?.formState.isDirty) || isLoading,
      loading: isLoading,
    });
  }, [form?.formState.isValid, form?.formState.isDirty, isLoading]);
  return (
    <div className="bg-zinc-50">
      <Form {...form}>
        <form
          id="buyerBotForm"
          onSubmit={form?.handleSubmit(onSubmit)}
          // noValidate
        >
          {/* {dev && (
            <Card className="px-4 py-2  mb-4">
              <Accordion type="single" collapsible>
                <AIBotCreatorFormAIAissistant />
              </Accordion>
            </Card>
          )} */}
          <AIBotCreatorFullForm />
          <button
            type="submit"
            // Hidden submit button
            style={{ display: 'none' }}
          />
        </form>
      </Form>

      <ToastContainer />
    </div>
  );
}

export default Main;
