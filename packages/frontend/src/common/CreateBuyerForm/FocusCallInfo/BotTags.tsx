import {
  CheckIcon,
  ChevronDownIcon,
  Loader2Icon,
  PlusIcon,
  FilterX,
  XIcon,
} from 'lucide-react';
import React, { useState, useMemo } from 'react';
import CreateNewTagDialog from '@/common/Buyers/ProdSite/multiactionsBar/tagsMenu/createNew';
import { cn } from '@/lib/utils';
import useTags from '@/hooks/useTags';
import {
  Command,
  CommandInput,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { TagDto } from '@/lib/Agent/types';

// Insert the following interface before the BotTags component
interface BotTagsProps {
  tags: TagDto[] | undefined;
  setTags: (tags: TagDto[]) => void;
}

// Replace the component declaration with the following:
const BotTags: React.FC<BotTagsProps> = ({ tags, setTags }) => {

  const [open, setOpen] = useState(false);
  const [searchString, setSearchString] = useState('');

  const { data: allTags } = useTags(true, 0, 0, '');
  const [loadingTags, setLoadingTags] = useState<{ [key: number]: boolean }>(
    {},
  );
  const [isCreatenewDialogOpen, setIsCreatenewDialogOpen] =
    useState<boolean>(false);

  const toggleTag = async (
    tag: TagDto
  ) => {
    const currentTags = tags ?? [];
    const hasTag = currentTags.some((t) => t.id === tag.id);

    const newTags = hasTag
        ? currentTags.filter((t) => t.id !== tag.id)
        : [...currentTags, tag];
    setTags(newTags);
  };

  const filterResults = (value: string) => {
    setSearchString(value);
  };

  const clearAll = () => {
    setTags([] as TagDto[]);
  };

  const filteredTags = useMemo(() => {
    if (!searchString.trim()) return allTags;

    return allTags?.filter((tag) =>
      tag.name.toLowerCase().includes(searchString.toLowerCase()),
    );
  }, [allTags, searchString]);

  const TagsDisplay = useMemo(
    () => () => {
      return (tags ?? []).map((tag: TagDto) => (
        <div
          key={tag.id}
          className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md "
        >
          <span className="text-xs font-semibold flex items-center h-4 rounded-lg">
            #{tag.name}
            <span className="inline-flex pl-1">
              <XIcon
                className="w-3 h-3 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  toggleTag(tag);
                }}
              />
            </span>
          </span>
        </div>
      ));
    },
    [tags],
  );

  return (
    <div>
      <div className="w-full mt-2">
        <span className="font-medium">Tags</span>
        <div className="mt-2">
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger
              onClick={(e) => {
                e.stopPropagation();
              }}
              className="flex w-full items-center justify-between rounded-md border border-input bg-background p-1 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <div className="flex flex-wrap items-center gap-2">
                <TagsDisplay />
                {!tags?.length && (
                  <>
                    <PlusIcon className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Add tags</span>
                  </>
                )}
              </div>
              <ChevronDownIcon className="h-4 w-4 text-muted-foreground" />
            </PopoverTrigger>

            <PopoverContent
              className="p-0"
              style={{ width: 'var(--radix-popover-trigger-width)' }}
            >
              <Command>
                <CommandInput
                  placeholder="Search tags..."
                  className="h-9"
                  value={searchString}
                  onValueChange={filterResults}
                />
                {Number(tags?.length) > 0 && (
                  <CommandGroup>
                    <CommandItem
                      onSelect={() => {
                        clearAll();
                        setOpen(false);
                      }}
                    >
                      <FilterX className="w-4 h-4 mr-2 text-muted-foreground" />
                      <span>Clear all tags</span>
                    </CommandItem>
                  </CommandGroup>
                )}

                <CommandSeparator />
                <CommandList>
                  <CommandGroup heading="Tags">
                    {filteredTags?.map((t) => (
                      <CommandItem
                        key={t.id}
                        value={String(t.id)}
                        onSelect={() => {
                          toggleTag(t);
                        }}
                      >
                        <div
                          className={cn(
                            'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                            tags?.some((tag: TagDto) => tag.id === t.id)
                              ? 'bg-primary text-primary-foreground'
                              : 'opacity-50 [&_svg]:invisible',
                          )}
                        >
                          <CheckIcon className={cn('h-4 w-4')} />
                        </div>
                        <div className="flex space-x-2 items-center">
                          <div className="capitalize flex-1">{t.name}</div>
                          {loadingTags[t.id] && (
                            <Loader2Icon className="animate-spin" size={14} />
                          )}
                        </div>
                      </CommandItem>
                    ))}

                    {filteredTags?.length === 0 && (
                      <CommandItem className="justify-center text-center">
                        No tag found
                      </CommandItem>
                    )}
                  </CommandGroup>
                </CommandList>

                <CommandSeparator />

                {/* Existing New Tag Option */}
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setOpen(false);
                      setIsCreatenewDialogOpen(true);
                    }}
                  >
                    <PlusIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span>New tag</span>
                  </CommandItem>
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <CreateNewTagDialog
        open={isCreatenewDialogOpen}
        onClose={(t: TagDto | undefined) => {
          if (t) {
            toggleTag(t);
          }
          setIsCreatenewDialogOpen(false);
        }}
      />
    </div>
  );
};

export default BotTags;
