'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { AutoExpandingTextarea, Textarea } from '@/components/ui/textarea';
import { useAgent } from '@/hooks/useAgent';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import AgentService from '@/lib/Agent';
import {
  AgentDto,
  AgentGender,
  AgentLanguage,
  AgentLanguagesLabels,
  CreateFocusAgentDto,
  TagDto,
} from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  AudioLinesIcon,
  DicesIcon,
  Loader2Icon,
  UploadCloudIcon,
  User2Icon,
} from 'lucide-react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Id, toast } from 'react-toastify';
import * as z from 'zod';
import { AGENT_GENDER_OPTIONS, VOICE_OPTIONS } from '../constants';
import BotTagInput from './BotTags';
import { randomAvatar } from '../Main/utils';
import useAvatars from '@/hooks/useAvatars';
import AgentAvatar from '@/components/Avatars/Agent';

const formSchema = z.object({
  firstName: z.string().min(1, 'This field is required'),
  lastName: z.string().min(1, 'This field is required'),
  gender: z.string().min(1, 'This field is required'),
  avatar: z.string().min(1, 'This field is required'),
  jobTitle: z.string().min(1, 'This field is required'),
  companyName: z.string().min(1, 'This field is required'),
  voice: z.string().min(1, 'This field is required'),
  openerLine: z.string().min(1, 'This field is required'),
  description: z.string().min(1, 'This field is required'),
  scorecardConfigId: z.number().min(1, 'This field is required'),
  language: z.string().optional(),
});

function FocusCallInfo() {
  const searchParams = useSearchParams();
  const params = useParams();
  const editAgentId = params?.id;

  let { data: scorecardConfigOptions } = useScorecardConfigsForOrg();
  if (!scorecardConfigOptions) {
    scorecardConfigOptions = [];
  }

  const scorecardConfigOptionsObject: any = {};
  for (const o of scorecardConfigOptions) {
    if (o.id) {
      scorecardConfigOptionsObject[o.id] = o.tag;
    }
  }

  const {
    data: editAgent,
    isLoading: isLoadingMyAgent,
    isSuccess,
  } = useAgent(editAgentId as string);
  const [scorecardConfigId, setScorecardConfigId] = useState<string>(
    String(editAgent?.scorecardConfigId),
  );

  const [processPending, setProcessPending] = useState<boolean>(false);
  const router = useRouter();

  const errorToastId = useRef<Id | null>(null);

  // if (editAgent?.gender) {
  //   console.log(VOICE_OPTIONS[editAgent?.gender].map((option: any) => { if (option.value === editAgent?.voice) { return option.label } }));
  // }

  const form = useForm<CreateFocusAgentDto>({
    mode: 'onTouched',
    resolver: zodResolver(formSchema),
    defaultValues: editAgent || new CreateFocusAgentDto(),
  });
  const gender = form.watch('gender');
  const { data: avatarOptions } = useAvatars(gender);

  const handleRandomAvatar = () => {
    const [avatar, avatarUrl] = randomAvatar(avatarOptions);
    form.setValue('avatar', avatar || '');
    form.setValue('avatarUrl', avatarUrl);
  };
  useEffect(() => {
    if (isSuccess) {
      form.reset({
        ...editAgent,
      });
    }
  }, [isSuccess]);

  useEffect(() => {
    if (!isLoadingMyAgent) {
      if (editAgent) {
        if (!editAgent?.avatar) {
          handleRandomAvatar();
        }
      } else {
        handleRandomAvatar();
      }
    }
  }, [editAgent, isLoadingMyAgent]);

  const upsertAgentMutation = useMutation({
    mutationFn: AgentService.createFocusAgent,
    onSuccess: (agent, params) => {
      setProcessPending(false);
      const agentRoute = `/buyers/${agent.vapiId}`;
      router.push(agentRoute);
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error creating the buyer. Please try again.',
        );
      }
    },
  });

  const queryClient = useQueryClient();

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
    onSuccess: (agent, params) => {
      setProcessPending(false);
      queryClient.invalidateQueries({ queryKey: ['orgAgents'] });
      queryClient.invalidateQueries({ queryKey: ['agent', agent.vapiId] });
      queryClient.removeQueries({ queryKey: ['draftAgent'] });
      queryClient.invalidateQueries({
        queryKey: ['callDurationByRepAndBuyer'],
      });
      router.push(`/buyers/${agent.vapiId}`);
    },
    onError: (err) => {
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error editing the buyer. Please try again.',
        );
      }
    },
  });

  const onSubmit = () => {
    if (form.formState.isValid) {
      setProcessPending(true);
      if (editAgentId && editAgent?.id) {
        const data = form.getValues();
        // console.log(data);
        updateAgentMutation.mutate({
          id: editAgent?.id,
          ...data,
          tagsIds: data.tags?.map((t) => t.id),
          research: JSON.stringify({
            language: data.language,
          }),
        });
      } else {
        const data = form.getValues();
        //BE will set language in research:
        upsertAgentMutation.mutate({
          ...data,
          tagsIds: data.tags?.map((t) => t.id),
        });
      }
    }
  };

  return (
    <div className="overflow-hidden">
      <div className="flex flex-col md:flex-row space-x-0 md:space-x-10 justify-between px-4 md:px-0 overflow-hidden">
        <div className="w-full pb-8 overflow-y-auto">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} noValidate>
              <div className="flex justify-between items-center">
                <div>
                  <div className="flex items-center">
                    <User2Icon className="w-4 h-4 mr-2 text-muted-foreground" />
                    <h3 className="text-base font-semibold">Info</h3>
                  </div>
                  <p className="text-muted-foreground max-w-[420px]">
                    A focus scenario allows your reps to focus on practicing
                    their responses to a single prompt from the buyer bot.
                  </p>
                </div>
                <div className="flex space-x-4">
                  <Button
                    type="submit"
                    disabled={
                      !form.formState.isValid ||
                      upsertAgentMutation?.isPending ||
                      updateAgentMutation?.isPending
                    }
                    className={cn('', {
                      'pointer-events-none': !form.formState.isValid,
                    })}
                    variant={'default'}
                  >
                    {processPending ? (
                      <Loader2Icon className="animate-spin" />
                    ) : (
                      <>
                        Publish <UploadCloudIcon className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </div>
              </div>
              <Separator className="mt-4 mb-8" />
              <div className="space-y-8 min-h-[500px]">
                <div className="flex space-x-4 w-full">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>First name *</FormLabel>
                        <FormControl>
                          <Input required placeholder="Jane" {...field} />
                        </FormControl>
                        {/* <FormMessage /> */}
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>Last name *</FormLabel>
                        <FormControl>
                          <Input required placeholder="Bowen" {...field} />
                        </FormControl>
                        {/* <FormMessage /> */}
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex space-x-4 w-full">
                  <FormField
                    control={form.control}
                    name="gender"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>Gender *</FormLabel>
                        <Select
                          required
                          onValueChange={(value: AgentGender) => {
                            field.onChange(value);
                            const [avatar, avatarUrl] =
                              randomAvatar(avatarOptions);
                            form.setValue('avatar', avatar || '');
                            form.setValue('avatarUrl', avatarUrl);
                            form.setValue(
                              'voice',
                              VOICE_OPTIONS[value][0].value,
                            );
                          }}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Choose an option" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {AGENT_GENDER_OPTIONS.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="jobTitle"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>Job title *</FormLabel>
                        <FormControl>
                          <Input
                            required
                            placeholder="i.e. Director of Sales"
                            {...field}
                          />
                        </FormControl>
                        {/* <FormMessage /> */}
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex space-x-4 items-center w-full">
                  <FormField
                    control={form.control}
                    name="avatar"
                    render={({ field }) => {
                      const { firstName, lastName, avatarUrl } =
                        form?.getValues() || {};
                      return (
                        <FormItem className="w-full">
                          <FormLabel>Avatar *</FormLabel>
                          <br />
                          <div className="flex items-center">
                            <AgentAvatar
                              className="w-16 h-16 mr-2"
                              agent={
                                { firstName, lastName, avatarUrl } as AgentDto
                              }
                            />
                            <FormControl>
                              <Button
                                onClick={handleRandomAvatar}
                                className="w-full"
                                type="button"
                                variant={'outline'}
                              >
                                Generate random{' '}
                                <DicesIcon className="ml-2 h-4 w-4" />
                              </Button>
                            </FormControl>
                          </div>
                        </FormItem>
                      );
                    }}
                  />
                  {form.watch('language') === AgentLanguage.EN_US && (<FormField
                    control={form.control}
                    name="voice"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel className="flex items-center mb-3">
                          <AudioLinesIcon className="w-3 h-3 mr-1" />
                          Voice *
                        </FormLabel>
                        <Select
                          required
                          onValueChange={(value: AgentGender) => {
                            field.onChange(value);
                          }}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Choose an option" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {VOICE_OPTIONS[form.getValues()?.gender].map(
                              (option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ),
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />)}
                </div>
                <div className="space-y-8 my-4">
                  <FormField
                    control={form.control}
                    name="language"
                    render={({ field }) => {
                      return (
                        <FormItem className="w-full">
                          <FormLabel>Language *</FormLabel>
                          <p className="text-muted-foreground">
                            Select the language you want to use for this call
                          </p>
                          <Select
                            required
                            onValueChange={(value: string) => {
                              field.onChange(value);
                            }}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Choose a language" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.keys(AgentLanguagesLabels).map(
                                (langKey) => {
                                  const lbl = AgentLanguagesLabels[langKey];
                                  return (
                                    <SelectItem
                                      key={langKey}
                                      value={String(langKey)}
                                      defaultValue={AgentLanguage.EN_US}
                                    >
                                      {lbl}
                                    </SelectItem>
                                  );
                                },
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="companyName"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Company name *</FormLabel>
                      <p className="text-muted-foreground">
                        Where do they work?
                      </p>
                      <FormControl>
                        <Input
                          required
                          placeholder="i.e. Agile Solutions"
                          {...field}
                        />
                      </FormControl>
                      {/* <FormMessage /> */}
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="openerLine"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Opener line *</FormLabel>
                      <p className="text-muted-foreground">
                        The prompt that the bot will say to the rep when the
                        call starts
                      </p>
                      <FormControl>
                        <Textarea
                          required
                          placeholder="i.e. What does your company do?"
                          {...field}
                        />
                      </FormControl>
                      {/* <FormMessage /> */}
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Call instructions *</FormLabel>
                      <p className="text-muted-foreground">
                        Instructions for reps about this simulation
                      </p>
                      <FormControl>
                        <AutoExpandingTextarea
                          required
                          placeholder="Start a call simulation with..."
                          {...field}
                        />
                      </FormControl>
                      {/* <FormMessage /> */}
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="scorecardConfigId"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Scorecard Config *</FormLabel>
                      <Select
                        required
                        onValueChange={(value: string) => {
                          field.onChange(parseInt(value));
                        }}
                        value={String(field.value)}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose a configuration" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {scorecardConfigOptions && (
                            <>
                              {scorecardConfigOptions.map((option) => {
                                return (
                                  <SelectItem
                                    key={option.id}
                                    value={String(option.id)}
                                  >
                                    {option.tag}
                                  </SelectItem>
                                );
                              })}
                            </>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <BotTagInput
                  tags={form.watch('tags')}
                  setTags={(tags: TagDto[]) => {
                    form.setValue('tags', tags);
                  }}
                />
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}

export default FocusCallInfo;
