'use client';

import DashboardNav<PERSON>, { BreadcrumbItem } from '@/common/DashboardNavbar';
import { CreateBuyerBotProvider } from '@/contexts/CreateBuyerBotContext';
import { useAgent } from '@/hooks/useAgent';
import {
  AdvancedSettings,
  AgentCallType,
  AgentDto,
  AgentEmotionalState,
  AgentLanguage,
  BasicDetails,
  ConfigDetails,
  CompanyDetails,
  CreateBuyerDefaultFormValues,
  Opinions,
  PersonalDetails,
  PrioritiesAndObjections,
  Scorecard,
  StepOneData,
} from '@/lib/Agent/types';
import { useAuthInfo } from '@propelauth/react';
import { useQueryClient } from '@tanstack/react-query';
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { CALL_TYPE_TO_ICON } from '../Sidebar/OldSidebar';
import BotPreview from './BotPreview';
import {
  AGENT_DEFAULT_EMOTIONAL_STATE,
  AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE,
  CALL_TYPE_OPTIONS,
  DEFAULT_OPENER_LINE,
} from './constants';
import useUserSession from '@/hooks/useUserSession';
import CreateBuyerBotEditFormProvider from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import {
  generateVoice,
  getRandomGender,
  getResearchData,
  randomAvatar,
} from './Main/utils';
import { AIBotCreatorData } from './Main/AIBotCreatorForm';
import CreateBuyerBotEditFormActiontButtons from './Main/AIBotCreatorForm/ActionButtons/CreateBuyerBotEditFormActionButtons';
import BuyerBotPreview from './BotPreview/deprecated';
import useAvatars from '@/hooks/useAvatars';

const withFolders = true;
interface ICreateBuyerFormProps {
  title: string;
  isEditMode?: boolean;
  children: React.ReactNode;
}

function CreateBuyerForm({
  title,
  isEditMode = false,
  children,
}: ICreateBuyerFormProps) {
  const baseRoute = '/buyers';

  const router = useRouter();
  const searchParams = useSearchParams();
  const curSearchParams = new URLSearchParams(searchParams);
  const params = useParams();
  const queryClient = useQueryClient();
  const authInfo = useAuthInfo();
  const pathname = usePathname();

  const { org, CALL_SCENARIO_OPTIONS, useBotBuilderV2 } = useUserSession();

  const cloneBuyerId = searchParams.get('cloneBuyerId');
  const cloneBuyerToType = searchParams.get('cloneBuyerToType');
  const callTypeFromQuery = searchParams.get('callType') as AgentCallType;
  const callType = searchParams.get('callType') as AgentCallType;

  const agentRequired = !!cloneBuyerId || !!params?.id || isEditMode;
  const { data: agent, isSuccess } = useAgent(
    (cloneBuyerId || params?.id) as string,
    agentRequired,
    withFolders,
  );

  const draftAgent = isEditMode
    ? undefined
    : (queryClient.getQueryData([
        authInfo?.isLoggedIn ? 'draftAgent' : 'demoDraftAgent',
      ]) as AgentDto);

  const currentAgent = useMemo(() => agent || draftAgent, [agent, draftAgent]);
  const initialGender = useMemo(
    () => currentAgent?.gender || getRandomGender(),
    [currentAgent],
  );
  const { data: avatarOptions } = useAvatars(initialGender);

  const [initialAvatar, initialAvatarUrl] = useMemo(() => {
    return randomAvatar(avatarOptions);
  }, [initialGender, currentAgent]);

  useEffect(() => {
    if (isSuccess) {
      curSearchParams.set(
        'callType',
        agent?.callType || callTypeFromQuery || '',
      );
      router.replace(`${pathname}?${curSearchParams.toString()}`);
    }
  }, [isSuccess]);

  const defineCallScenario = useMemo(
    () => (): string => {
      const researchData = getResearchData(currentAgent);

      let callScenario = '';

      if (agent) {
        if (cloneBuyerToType) {
          agent.callType = cloneBuyerToType as AgentCallType;
        }
        if (agent.callType == AgentCallType.COLD) {
          callScenario = researchData.cold_call_scenario;
        } else if (agent.callType == AgentCallType.WARM) {
          callScenario = researchData.warm_call_scenario;
        } else if (agent.callType == AgentCallType.DISCOVERY) {
          callScenario = researchData.discovery_call_scenario;
        } else if (callType == AgentCallType.CHECKIN) {
          callScenario = CALL_SCENARIO_OPTIONS[AgentCallType.CHECKIN][0].value;
        } else if (callType == AgentCallType.RENEWAL) {
          callScenario = researchData.renewal_call_scenario;
        } else if (agent.callType == AgentCallType.DEMO) {
          callScenario = researchData.demo_call_scenario;
        } else if (agent.callType == AgentCallType.MANAGER_ONE_ON_ONE) {
          callScenario = researchData.manager_one_on_one_call_scenario;
        }
      } else if (draftAgent) {
        if (callType == AgentCallType.COLD) {
          callScenario = researchData.cold_call_scenario;
        } else if (callType == AgentCallType.WARM) {
          callScenario = researchData.warm_call_scenario;
        } else if (callType == AgentCallType.DISCOVERY) {
          callScenario = researchData.discovery_call_scenario;
        } else if (callType == AgentCallType.CHECKIN) {
          callScenario = CALL_SCENARIO_OPTIONS[AgentCallType.CHECKIN][0].value;
        } else if (callType == AgentCallType.RENEWAL) {
          callScenario = researchData.renewal_call_scenario;
        } else if (callType == AgentCallType.DEMO) {
          callScenario = researchData.demo_call_scenario;
        } else if (callType == AgentCallType.MANAGER_ONE_ON_ONE) {
          callScenario = researchData.manager_one_on_one_call_scenario;
        }
      }

      return callScenario;
    },
    [currentAgent],
  );
  const research = useMemo(() => getResearchData(currentAgent), [currentAgent]);

  const defaultValues: CreateBuyerDefaultFormValues = useMemo(() => {
    const configDetails: ConfigDetails = {
      folders: currentAgent?.folders,
      tags: currentAgent?.tags,
      gatekeepers: currentAgent?.gatekeepers?.[0],
    };

    const basicDetails: BasicDetails = {
      description:
        currentAgent?.description || 'Wear headphones for the best experience.',
      warm_call_context: research?.warm_call_context || '',
      discovery_call_context: research?.discovery_call_context || '',
      demo_call_context: research?.demo_call_context || '',
      checkin_call_context: research?.checkin_call_context || '',
      manager_one_on_one_call_context:
        research?.manager_one_on_one_call_context || '',
    };
    const processEmotionalState = (
      agentEmotionalState: AgentEmotionalState | undefined,
    ) => {
      if (callType) {
        if (agentEmotionalState) {
          const availableEmotionalStatesForCallType =
            AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
              callType as keyof typeof AGENT_DEFAULT_EMOTIONAL_STATE
            ];
          if (
            availableEmotionalStatesForCallType?.some(
              (emotionalState) => emotionalState.value === agentEmotionalState,
            )
          ) {
            return agentEmotionalState;
          } else {
            return AGENT_DEFAULT_EMOTIONAL_STATE[
              callType as keyof typeof AGENT_DEFAULT_EMOTIONAL_STATE
            ];
          }
        } else {
          return AGENT_DEFAULT_EMOTIONAL_STATE[
            callType as keyof typeof AGENT_DEFAULT_EMOTIONAL_STATE
          ];
        }
      } else {
        return AgentEmotionalState.RUDE;
      }
    };
    const personalDetails: PersonalDetails = {
      firstName: currentAgent?.firstName || '',
      lastName: currentAgent?.lastName || '',
      gender: currentAgent?.gender || initialGender,
      avatar: currentAgent?.avatar || initialAvatar || '',
      avatarUrl: currentAgent?.avatarUrl || initialAvatarUrl || '',
      jobTitle: currentAgent?.jobTitle || '',
      personalDetails: currentAgent?.personalDetails || [],
      voice: currentAgent?.voice || generateVoice(initialGender),
      language: research?.language || AgentLanguage.EN_US,
      companyName: currentAgent?.companyName || '',
      emotionalState: processEmotionalState(currentAgent?.emotionalState),
      openerLine: currentAgent?.openerLine || DEFAULT_OPENER_LINE,
    };

    const companyDetails: CompanyDetails = {
      companyDetails: currentAgent?.companyDetails || [],
      companyOrgStructure: currentAgent?.companyOrgStructure || [],
    };

    const prioritiesAndObjections: PrioritiesAndObjections = {
      goals: currentAgent?.goals || [],
      objections: currentAgent?.objections || [],
    };

    const opinions: Opinions = {
      callScenario: defineCallScenario(),
      opinions: currentAgent?.opinions || [],
      incumbent_solution_info: research?.incumbent_solution_info || '',
      problem_aware_info: research?.problem_aware_info || '',
      solution_aware_info: research?.solution_aware_info || '',
      pre_existing_champion_info: research?.pre_existing_champion_info || '',
    };

    const scorecard: Scorecard = {
      scorecardConfigId: currentAgent?.scorecardConfigId?.toString() || '',
    };

    const advancedSettings: AdvancedSettings = {
      public_presence: research?.public_presence || '',
      genBotDraftId: queryClient.getQueryData(['genBotDraftId']),
      keywords: research?.keywords || [org?.orgName],
    };

    const aiGenerator: AIBotCreatorData = {
      jobTitle: '',
      buyerContext: '',
      sellerContext: '',
      callScenario: '',
    };
    const main: StepOneData = {
      ...configDetails,
      ...basicDetails,
      ...personalDetails,
      ...companyDetails,
      ...prioritiesAndObjections,
      ...opinions,
      ...scorecard,
      ...advancedSettings,
    };
    return {
      aiGenerator,
      main,
    };
  }, [
    research,
    cloneBuyerId,
    agent?.id,
    draftAgent,
    org?.orgName,
    pathname,
    currentAgent,
    initialGender,
  ]);
  const [forms, setForms] = useState({});

  const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Buyer Bots', href: baseRoute },
    { title, href: !isEditMode ? `${baseRoute}/create/init` : undefined },
  ];

  if (callTypeFromQuery) {
    const callTypeLabel = CALL_TYPE_OPTIONS.find(
      (c) => c.value === callTypeFromQuery,
    )?.label;

    if (callTypeLabel) {
      const Icon =
        CALL_TYPE_TO_ICON?.[callTypeFromQuery as keyof typeof CALL_TYPE_TO_ICON]
          ?.Icon;
      breadcrumbs.push({
        title: (
          <div className="flex items-center">
            <Icon className="w-4 h-4 mr-2" />
            <span>{callTypeLabel}</span>
          </div>
        ),
      });
    }
  }
  return useBotBuilderV2 ? (
    <div className="bg-zinc-50 min-h-screen">
      <CreateBuyerBotProvider
        value={{
          defaultValues,
          forms,
          setForms,
          isEditMode,
          baseRoute,
        }}
      >
        <CreateBuyerBotEditFormProvider baseRoute={baseRoute}>
          <DashboardNavbar
            className="bg-zinc-50 border-none"
            breadcrumbs={breadcrumbs}
            // TODO: the presence of this button depends on the URL, that is not a good practice
            rightContent={
              (pathname.includes(`${baseRoute}/create/main`) ||
                pathname.includes(`${baseRoute}/${agent?.vapiId}/edit/main`) ||
                pathname.includes(`${baseRoute}/create/focus`) ||
                pathname.includes(
                  `${baseRoute}/${agent?.vapiId}/edit/focus`,
                )) && <CreateBuyerBotEditFormActiontButtons />
            }
          />
          <div className="flex flex-col md:flex-row space-x-0 justify-between px-4 md:px-0">
            <div className="w-full md:w-[60%] pt-3 pb-8 md:pl-8 md:pr-0 overflow-y-auto">
              {agentRequired ? agent && <>{children}</> : <>{children}</>}
            </div>
            {!pathname.includes(`${baseRoute}/create/init`) &&
              !pathname.includes(`${baseRoute}/create/ai-generator`) && (
                <div
                  className="flex w-full md:w-[40%] border-0 px-4 pl-10 
                md:fixed md:top-13 md:right-0 md:h-screen 
                max-h-[calc(100vh-3rem)] overflow-y-auto"
                >
                  <BotPreview />
                </div>
              )}
          </div>
        </CreateBuyerBotEditFormProvider>
      </CreateBuyerBotProvider>
    </div>
  ) : (
    <div className="">
      <CreateBuyerBotProvider
        value={{
          defaultValues,
          forms,
          setForms,
          isEditMode,
          baseRoute,
        }}
      >
        <DashboardNavbar breadcrumbs={breadcrumbs} />
        <div className="flex flex-col md:flex-row space-x-0 md:space-x-10 justify-between px-4 md:px-0 overflow-hidden">
          <div className="w-full md:w-[60%] pt-6 pb-8 md:pl-8 md:pr-0 overflow-y-auto">
            {agentRequired ? agent && <>{children}</> : <>{children}</>}
          </div>
          {!pathname.includes('/buyers/create/init') &&
            !pathname.includes('/buyers/create/ai-generator') && (
              <div className="flex w-full md:w-[40%] border-0 md:border-l px-2">
                <BuyerBotPreview />
              </div>
            )}
        </div>
      </CreateBuyerBotProvider>
    </div>
  );
}

export default CreateBuyerForm;
