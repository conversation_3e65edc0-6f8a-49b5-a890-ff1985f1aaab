import {
  AgentCallType,
  AgentEmotionalState,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@/lib/Agent/types';
import {
  CircleHelpIcon,
  RefreshCwIcon,
  ScanEyeIcon,
  UserCheckIcon,
  DoorOpenIcon,
  FlameKindlingIcon,
  SearchIcon,
  SnowflakeIcon,
  LaptopIcon,
  Users,
} from 'lucide-react';

export const DEFAULT_OPENING_LINE = 'Hi, who is this?';

export const AGENT_GENDER_OPTIONS = [
  {
    label: 'Female',
    value: AgentGender.FEMALE,
  },
  {
    label: 'Male',
    value: AgentGender.MALE,
  },
];

export const DEFAULT_OPENER_LINE = '';

export const AVATAR_OPTIONS = {
  [AgentGender.MALE]: [
    'male-demo/jon.jpg',
    'male-demo/drew.png',
    'male-demo/ryan.png',
    'male-demo/curt.jpg',
    'male-demo/champ.jpg',
    'male-demo/male-10.jpg',
    'male-demo/male-14.jpg',
    'male-demo/male-15.jpg',
    'male-demo/male-16.jpg',
    'male-demo/male-17.jpg',
    'male-demo/male-22.jpg',
    'male-demo/male-24.jpg',
    'male-demo/male-25.jpg',
    'male-demo/male-26.jpg',
    'male-demo/male-27.jpg',
    'male-demo/male-29.jpg',
    'male-demo/male-30.jpg',
    'male-demo/male-31.jpg',
    'male-demo/male-35.jpg',
    'male-demo/male-4.jpg',
    'male-demo/male-41.jpg',
    'male-demo/male-42.jpg',
    'male-demo/male-43.jpg',
    'male-demo/male-44.jpg',
    'male-demo/male-5.jpg',
    'male-demo/male-50.jpg',
    'male-demo/male-51.jpg',
    'male-demo/male-52.jpg',
    'male-demo/male-53.jpg',
    'male-demo/male-54.jpg',
    'male-demo/male-55.jpg',
    'male-demo/male-58.jpg',
    'male-demo/male-6.jpg',
    'male-demo/male-61.jpg',
    'male-demo/male-62.jpg',
    'male-demo/male-63.jpg',
    'male-demo/male-64.jpg',
    'male-demo/male-67.jpg',
    'male-demo/male-7.jpg',
    'male-demo/male-70.jpg',
    'male-demo/male-71.jpg',
    'male-demo/male-72.jpg',
    'male-demo/male-8.jpg',
    'male-demo/male-9.jpg',
  ],
  [AgentGender.FEMALE]: [
    'female-demo/jane.png',
    'female-demo/sarah.png',
    'female-demo/megan.jpg',
    'female-demo/devon.jpg',
    'female-demo/female-1.jpg',
    'female-demo/female-18.jpg',
    'female-demo/female-21.jpg',
    'female-demo/female-32.jpg',
    'female-demo/female-37.jpg',
    'female-demo/female-45.jpg',
    'female-demo/female-49.jpg',
    'female-demo/female-60.jpg',
    'female-demo/female-69.jpg',
    'female-demo/female-11.jpg',
    'female-demo/female-19.jpg',
    'female-demo/female-23.jpg',
    'female-demo/female-33.jpg',
    'female-demo/female-38.jpg',
    'female-demo/female-46.jpg',
    'female-demo/female-56.jpg',
    'female-demo/female-65.jpg',
    'female-demo/female-73.jpg',
    'female-demo/female-12.jpg',
    'female-demo/female-2.jpg',
    'female-demo/female-28.jpg',
    'female-demo/female-34.jpg',
    'female-demo/female-39.jpg',
    'female-demo/female-47.jpg',
    'female-demo/female-57.jpg',
    'female-demo/female-66.jpg',
    'female-demo/female-74.jpg',
    'female-demo/female-13.jpg',
    'female-demo/female-20.jpg',
    'female-demo/female-3.jpg',
    'female-demo/female-36.jpg',
    'female-demo/female-40.jpg',
    'female-demo/female-48.jpg',
    'female-demo/female-59.jpg',
    'female-demo/female-68.jpg',
  ],
};

export const MULTI_PARTY_VOICE_OPTIONS = {
  [AgentGender.MALE]: [
    {
      label: 'Finn (American)',
      value: AgentVoice.FINN_11LABS,
      // TODO: add sample
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Will%20(American).wav?t=2025-01-08T00%3A49%3A45.332Z',
    },
    {
      label: 'Will (American)',
      value: AgentVoice.WILL_11LABS,
      // TODO: add sample
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Will%20(American).wav?t=2025-01-08T00%3A49%3A45.332Z',
    },
    {
      label: 'Kevin (American)',
      value: AgentVoice.KEVIN_11LABS,
      // TODO: add sample
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Will%20(American).wav?t=2025-01-08T00%3A49%3A45.332Z',
    },
    {
      label: 'Joe (American)',
      value: AgentVoice.JOE_11LABS,
      // TODO: add sample
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Will%20(American).wav?t=2025-01-08T00%3A49%3A45.332Z',
    },
  ],
  [AgentGender.FEMALE]: [
    {
      label: 'Angela (American)',
      value: AgentVoice.ANGELA_11LABS,
      // TODO: add sample
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Donna%20(American).wav?t=2025-01-08T00%3A43%3A26.843Z',
    },
    {
      label: 'Clara (American)',
      value: AgentVoice.CLARA_11LABS,
      // TODO: add sample
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Donna%20(American).wav?t=2025-01-08T00%3A43%3A26.843Z',
    },
    {
      label: 'Jennifer (American)',
      value: AgentVoice.JENNIFER_11LABS,
      // TODO: add sample
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Donna%20(American).wav?t=2025-01-08T00%3A43%3A26.843Z',
    },
    {
      label: 'Chelsea (American)',
      value: AgentVoice.CHELSEA_11LABS,
      // TODO: add sample
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Donna%20(American).wav?t=2025-01-08T00%3A43%3A26.843Z',
    },
  ],
};

export const VOICE_OPTIONS = {
  [AgentGender.MALE]: [
    // {
    //   label: 'Arnold (American)',
    //   value: AgentVoice.ARNOLD_11LABS,
    //   sample:
    //     'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Arnold%20(American).wav?t=2025-01-08T00%3A42%3A56.091Z',
    // },
    // {
    //   label: 'Charlie (Australian)',
    //   value: AgentVoice.CHARLIE_11LABS,
    //   sample:
    //     'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Charlie%20(Australian).wav?t=2025-01-08T00%3A46%3A58.853Z',
    // },
    {
      label: 'Ben (French)',
      value: AgentVoice.BEN_11LABS_FRENCH_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Ben%20(French11labs).wav',
    },
    {
      label: 'Bogdan (German)',
      value: AgentVoice.BOGDAN_11LABS_GERMAN_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Bogdan%20(German11labs).wav',
    },
    {
      label: 'Chris (American)',
      value: AgentVoice.CHRIS_PLAYHT,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Chris%20(American).wav?t=2025-01-08T00%3A45%3A43.305Z',
    },
    {
      label: 'Davis (American)',
      value: AgentVoice.DAVIS_PLAYHT,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Charlie%20(Australian).wav?t=2025-01-08T00%3A46%3A58.853Z',
    },
    {
      label: 'Giovanni (American-Italian)',
      value: AgentVoice.GIOVANNI_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Giovanni%20%20(American-Italian).wav',
    },
    {
      label: 'Jack (American)',
      value: AgentVoice.JACK_PLAYHT,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Jack%20(American).wav?t=2025-01-08T00%3A48%3A33.242Z',
    },
    {
      label: 'Jason (Australian)',
      value: AgentVoice.JASON_CLONED,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Jason%20(Australian).wav?t=2025-01-08T00%3A48%3A43.086Z',
    },
    {
      label: 'Jake (Singaporean)',
      value: AgentVoice.JAKE_11LABS_SINGAPOREAN_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Jake%20(Singaporean11labs).wav',
    },
    {
      label: 'Jessie (American-Southern)',
      value: AgentVoice.JESSIE_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Jessie%20(American-Southern).wav?t=2025-01-08T00%3A48%3A50.987Z',
    },
    {
      label: 'Ken (Japanese)',
      value: AgentVoice.KEN_11LABS_JAPANESE_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Ken%20(Japanese11labs).wav',
    },
    {
      label: 'Leo (Spanish)',
      value: AgentVoice.LEO_11LABS_SPANISH_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Leo%20(Spanish11labs).wav',
    },
    {
      label: 'Liam (American)',
      value: AgentVoice.LIAM_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Liam%20(American).wav',
    },
    {
      label: 'Matt (British)',
      value: AgentVoice.MATT_PLAYHT,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Matt%20(British).wav?t=2025-01-08T00%3A49%3A09.867Z',
    },
    {
      label: 'Mike (Dutch)',
      value: AgentVoice.MIKE_11LABS_DUTCH_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Mike%20(Dutch11labs).wav',
    },
    {
      label: 'Niklas (Central European)',
      value: AgentVoice.NIKLAS_11LABS_CENTRAL_EUROPEAN_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Niklas%20(CentralEurope11labs).wav',
    },
    // {
    //   label: 'Michael (American)',
    //   value: AgentVoice.MICHAEL_PLAYHT,
    //   sample:
    //     'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Michael%20(American).wav?t=2025-01-08T00%3A49%3A21.709Z',
    // },
    // {
    //   label: 'Thomas (American, Soft Spoken)',
    //   value: AgentVoice.THOMAS_11LABS,
    //   sample:
    //     'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Thomas%20(American,%20Soft%20Spoken).wav?t=2025-01-08T00%3A49%3A31.567Z',
    // },
    {
      label: 'Ronnie (Asian)',
      value: AgentVoice.RONNIE_11LABS_ASIAN_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Ronnie%20(Asian11labs).wav',
    },
    {
      label: 'Raju (Indian)',
      value: AgentVoice.RAJU_11LABS_INDIAN_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Raju%20(Indan).wav',
    },
    {
      label: 'Santiago (Spanish)',
      value: AgentVoice.SANTIAGO_11LABS_SPANISH_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Santiago%20(Spanish11labs).wav',
    },

    {
      label: 'Sid (Indian)',
      value: AgentVoice.SID_11LABS_INDIAN_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Sid%20(Indan).wav',
    },
    {
      label: 'Simon (Polish)',
      value: AgentVoice.SIMON_11LABS_POLISH_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Simon%20(Polish11labs).wav',
    },
    {
      label: 'Tatsuya (Japanese)',
      value: AgentVoice.TATSUYA_11LABS_JAPANESE_MALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Tatsuya%20(Japanese11labs).wav',
    },
    {
      label: 'Will (American)',
      value: AgentVoice.WILL_PLAYHT,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Will%20(American).wav?t=2025-01-08T00%3A49%3A45.332Z',
    },

    // {
    //   label: 'Yu (Taiwanese)',
    //   value: AgentVoice.YU_11LABS_TAIWANESE_MALE,
    //   sample:
    //     'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/male-voices/Male%20Voice_Yu%20(Taiwanese11labs).wav'
    // },
  ],
  [AgentGender.FEMALE]: [
    {
      label: 'Claire (French)',
      value: AgentVoice.CLAIRE_11LABS_FRENCH_FEMALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Claire%20(French11labs).wav',
    },
    {
      label: 'Donna (American)',
      value: AgentVoice.DONNA_PLAYHT,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Donna%20(American).wav?t=2025-01-08T00%3A43%3A26.843Z',
    },

    {
      label: 'Dorothy (British)',
      value: AgentVoice.DOROTHY_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Dorothy%20(British).wav?t=2025-01-08T01%3A00%3A53.633Z',
    },
    {
      label: 'Freya (American)',
      value: AgentVoice.FREYA_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Freya%20(American).wav?t=2025-01-08T01%3A01%3A03.149Z',
    },
    {
      label: 'Grace (American-Southern)',
      value: AgentVoice.GRACE_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Grace%20(American-Southern).wav?t=2025-01-08T01%3A01%3A12.147Z',
    },
    {
      label: 'Jennifer (American)',
      value: AgentVoice.JENNIFER_PLAYHT,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Jennifer%20(American).wav',
    },
    {
      label: 'Jessica (Australian)',
      value: AgentVoice.JESSICA_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Jessica%20(Australian).wav?t=2025-01-08T01%3A01%3A37.016Z',
    },
    {
      label: 'Leonie (German)',
      value: AgentVoice.LEONIE_11LABS_GERMAN_FEMALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Leonie%20(German11labs).wav',
    },
    {
      label: 'Mandy (Malay)',
      value: AgentVoice.MANDY_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Mandy%20(Malay).wav?t=2025-01-08T01%3A01%3A26.308Z',
    },
    // {
    //   label: 'Melissa (American)',
    //   value: AgentVoice.MELISSA_PLAYHT,
    //   sample:
    //     'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Melissa%20(American).wav?t=2025-01-08T01%3A01%3A47.330Z',
    // },
    {
      label: 'Mimi (American)',
      value: AgentVoice.MIMI_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Mimi%20(American).wav?t=2025-01-08T01%3A01%3A53.832Z',
    },
    {
      label: 'Monika (Indian)',
      value: AgentVoice.MONIKA_11LABS_INDIAN_FEMALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Monika%20(Indan).wav',
    },
    {
      label: 'Naina (Indian)',
      value: AgentVoice.NAINA_11LABS_INDIAN_FEMALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Naina%20(Indan).wav',
    },
    {
      label: 'Rachel (American)',
      value: AgentVoice.RACHEL_11LABS,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Rachel%20(American).wav?t=2025-01-08T01%3A02%3A00.880Z',
    },
    {
      label: 'Ruby (American)',
      value: AgentVoice.RUBY_PLAYHT,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Ruby%20(American).wav?t=2025-01-08T01%3A02%3A07.881Z',
    },
    {
      label: 'Sakura (Japanese)',
      value: AgentVoice.SAKURA_11LABS_JAPANESE_FEMALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Sakura%20(Japanese11labs).wav',
    },
    {
      label: 'Valeria (Spanish)',
      value: AgentVoice.VALERIA_11LABS_SPANISH_FEMALE,
      sample:
        'https://copwjmrddotaxcdlpdsz.supabase.co/storage/v1/object/public/audio-assets/voice-samples/female-voices/Female%20Voice_Valeria%20(Spanish11labs).wav',
    },

    // { label: "Xiaoyi (Chinese)", value: AgentVoice.XIAOYI_AZURE },   # azure does not work in voice map
    // { label: "Ananya (Indian)", value: AgentVoice.ANANYA_AZURE },    # azure does not work in voice map
  ],
};

// export const VOICE_OPTIONS = {
//     [AgentGender.MALE]: [
//         // { label: "Hudson (american)", value: AgentVoice.HUDSON },
//         // { label: "Samuel (american)", value: AgentVoice.SAMUEL },
//         { label: "Dylan (british)", value: AgentVoice.DYLAN },
//         { label: "Furio (italian)", value: AgentVoice.FURIO },
//         // { label: "Adolfo (american)", value: AgentVoice.ADOLFO },
//         // { label: "Atlas (american)", value: AgentVoice.ATLAS },
//         { label: "Julian (british)", value: AgentVoice.JULIAN },
//         { label: "Hunter (british)", value: AgentVoice.HUNTER },
//         { label: "Theodore (american)", value: AgentVoice.THEODORE },
//         { label: "Bryan (american)", value: AgentVoice.BRYAN },
//         { label: "Billy (american)", value: AgentVoice.BILLY },
//         { label: "Nolan (british)", value: AgentVoice.NOLAN },
//         // { label: "Darnell (american)", value: AgentVoice.DARNELL },
//         { label: "Davis (american)", value: AgentVoice.DAVIS },
//         { label: "Jack (american)", value: AgentVoice.JACK },
//         { label: "Mason (american)", value: AgentVoice.MASON },
//         // { label: "Russell (australian)", value: AgentVoice.RUSSELL },
//         { label: "Chris (american)", value: AgentVoice.CHRIS },
//         // { label: "Flynn (british)", value: AgentVoice.FLYNN },
//         // { label: "Chuck (british)", value: AgentVoice.CHUCK },
//         { label: "George (british)", value: AgentVoice.GEORGE },
//         // { label: "Wilbert (british)", value: AgentVoice.WILBERT },
//         // { label: "Dick (american)", value: AgentVoice.DICK },
//         // { label: "Donovan (american)", value: AgentVoice.DONOVAN },
//         // { label: "Harris (british)", value: AgentVoice.HARRIS },
//         // { label: "Daniel (canadian)", value: AgentVoice.DANIEL },
//         // { label: "Dudley (american)", value: AgentVoice.DUDLEY },
//         { label: "Richie (american)", value: AgentVoice.RICHIE },
//         // { label: "Hook (american)", value: AgentVoice.HOOK },
//         { label: "Leroy (american)", value: AgentVoice.LEROY },
//         // { label: "Alfonso (american)", value: AgentVoice.ALFONSO },
//         { label: "Archie (australian)", value: AgentVoice.ARCHIE },
//         { label: "Waylon (american)", value: AgentVoice.WAYLON },
//         // { label: "Carter (american)", value: AgentVoice.CARTER },
//         { label: "Michael (american)", value: AgentVoice.MICHAEL },
//         { label: "Matt (american)", value: AgentVoice.MATT },
//         // { label: "Adrian (american)", value: AgentVoice.ADRIAN },
//         // { label: "Frederick (british)", value: AgentVoice.FREDERICK },
//         // { label: "Clark (british)", value: AgentVoice.CLARK },
//         // { label: "Sarge (american)", value: AgentVoice.SARGE },
//         // { label: "Benton (american)", value: AgentVoice.BENTON },
//         // { label: "Arthur (british)", value: AgentVoice.ARTHUR },
//         // { label: "Clifton (american)", value: AgentVoice.CLIFTON },
//         { label: "Owen (american)", value: AgentVoice.OWEN },
//         { label: "Erasmo (american)", value: AgentVoice.ERASMO },
//         { label: "Frankie (british)", value: AgentVoice.FRANKIE },
//         { label: "Ada (south)", value: AgentVoice.ADA },
//         { label: "Darrell (british)", value: AgentVoice.DARRELL },
//         { label: "Nigel (australian)", value: AgentVoice.NIGEL },
//         // { label: "Ranger (american)", value: AgentVoice.RANGER },
//         { label: "Axel (american)", value: AgentVoice.AXEL },
//         // { label: "Earle (british)", value: AgentVoice.EARLE },
//         // { label: "Logan (british)", value: AgentVoice.LOGAN },
//         { label: "Alessandro (italian)", value: AgentVoice.ALESSANDRO },
//         // { label: "Jordan (american)", value: AgentVoice.JORDAN },
//         { label: "Alex (british)", value: AgentVoice.ALEX },
//         { label: "Baptiste (french)", value: AgentVoice.BAPTISTE },
//         // { label: "Wilbur (american)", value: AgentVoice.WILBUR },
//         // { label: "Mitch (australian)", value: AgentVoice.MITCH },
//         { label: "Teddy (australian)", value: AgentVoice.TEDDY },
//         // { label: "Mark (british)", value: AgentVoice.MARK },
//         // { label: "Charles (american)", value: AgentVoice.CHARLES },
//         { label: "Lachlan (australian)", value: AgentVoice.LACHLAN },
//         { label: "Joseph (american)", value: AgentVoice.JOSEPH },
//         // { label: "Lance (british)", value: AgentVoice.LANCE },
//         // { label: "Finley (british)", value: AgentVoice.FINLEY },
//         { label: "Will (american)", value: AgentVoice.WILL },
//         // { label: "Oliver (british)", value: AgentVoice.OLIVER },
//         { label: "Calvin (american)", value: AgentVoice.CALVIN },
//         // { label: "Anthony (american)", value: AgentVoice.ANTHONY },
//         // { label: "William (american)", value: AgentVoice.WILLIAM },
//         // { label: "Larry (american)", value: AgentVoice.LARRY },
//     ],
//     [AgentGender.FEMALE]: [
//         // { label: "Phoebe (british)", value: AgentVoice.PHOEBE },
//         { label: "Amelia (british)", value: AgentVoice.AMELIA },
//         // { label: "Eleanor (british)", value: AgentVoice.ELEANOR },
//         { label: "Navya (indian)", value: AgentVoice.NAVYA },
//         // { label: "Autumn (american)", value: AgentVoice.AUTUMN },
//         { label: "Samara (american)", value: AgentVoice.SAMARA },
//         { label: "Siobhán (irish)", value: AgentVoice.SIOBHÁN },
//         // { label: "Sarah (british)", value: AgentVoice.SARAH },
//         // { label: "April (british)", value: AgentVoice.APRIL },
//         // { label: "Abigail (american)", value: AgentVoice.ABIGAIL },
//         // { label: "Micah (british)", value: AgentVoice.MICAH },
//         { label: "Melissa (american)", value: AgentVoice.MELISSA },
//         // { label: "Scarlett (british)", value: AgentVoice.SCARLETT },
//         { label: "Sumita (indian)", value: AgentVoice.SUMITA },
//         // { label: "Indigo (british)", value: AgentVoice.INDIGO },
//         // { label: "Madison (irish)", value: AgentVoice.MADISON },
//         // { label: "Nova (american)", value: AgentVoice.NOVA },
//         // { label: "Nicole (american)", value: AgentVoice.NICOLE },
//         // { label: "Isabella (british)", value: AgentVoice.ISABELLA },
//         { label: "Ariana (american)", value: AgentVoice.ARIANA },
//         // { label: "Sophia (american)", value: AgentVoice.SOPHIA },
//         // { label: "Susan (american)", value: AgentVoice.SUSAN },
//         { label: "Pia (australian)", value: AgentVoice.PIA },
//         // { label: "Eileen (american)", value: AgentVoice.EILEEN },
//         { label: "Aaliyah (american)", value: AgentVoice.AALIYAH },
//         { label: "Lumi (finnish)", value: AgentVoice.LUMI },
//         // { label: "Samantha (american)", value: AgentVoice.SAMANTHA },
//         // { label: "Audrey (american)", value: AgentVoice.AUDREY },
//         { label: "Adelaide (australian)", value: AgentVoice.ADELAIDE },
//         { label: "Carmen (mexican)", value: AgentVoice.CARMEN },
//         { label: "Niamh (irish)", value: AgentVoice.NIAMH },
//         // { label: "Delilah (american)", value: AgentVoice.DELILAH },
//         // { label: "Madelyn (british)", value: AgentVoice.MADELYN },
//         // { label: "Luna (american)", value: AgentVoice.LUNA },
//         { label: "Evelyn (american)", value: AgentVoice.EVELYN },
//         // { label: "Charlotte (canadian)", value: AgentVoice.CHARLOTTE },
//         { label: "Jennifer (american)", value: AgentVoice.JENNIFER },
//         // { label: "Ruby (australian)", value: AgentVoice.RUBY },
//         // { label: "Aurora (british)", value: AgentVoice.AURORA },
//     ],
// };

export const CALL_TYPE_LABELS = {
  [AgentCallType.COLD]: 'Cold Call',
  [AgentCallType.WARM]: 'Warm Call',
  [AgentCallType.DISCOVERY]: 'Discovery Call',
  [AgentCallType.GATEKEEPER]: 'Gatekeeper Call',
  [AgentCallType.RENEWAL]: 'Renewal Call',
  [AgentCallType.CHECKIN]: 'Check-In Call',
  [AgentCallType.FOCUS]: 'Focus Call',
  [AgentCallType.DEMO]: 'Demo Call',
  [AgentCallType.CUSTOM]: 'Custom Call',
  [AgentCallType.MANAGER_ONE_ON_ONE]: 'Manager 1:1',
};

export const CALL_TYPE_OPTIONS = [
  {
    label: 'Cold Call',
    value: AgentCallType.COLD,
    description: "Prospects that aren't expecting a call from you",
    Icon: SnowflakeIcon,
  },
  {
    label: 'Warm Call',
    value: AgentCallType.WARM,
    description:
      'Interested prospects who you or your company had some prior contact (i.e. MQLs, Existing users on a free plan)',
    Icon: FlameKindlingIcon,
  },
  {
    label: 'Discovery Call',
    value: AgentCallType.DISCOVERY,
    description:
      'Interested prospects who scheduled a time to speak with you (i.e. SQLs)',
    Icon: SearchIcon,
  },
  {
    label: 'Gatekeeper Call',
    value: AgentCallType.GATEKEEPER,
    description:
      'Frontdesk or assistant who is responsible for routing your call to the prospect (i.e. executive assistant)',
    Icon: DoorOpenIcon,
  },
  {
    label: 'Renewal Call',
    value: AgentCallType.RENEWAL,
    description:
      'Reach out to existing customers to renew their subscription. ',
    Icon: DoorOpenIcon,
  },
  {
    label: 'Check-In Call',
    value: AgentCallType.CHECKIN,
    description:
      'Check-in with existing customer for product adoption, churn risk, issue resolution, or relationship building.',
    Icon: DoorOpenIcon,
  },
  {
    label: 'Focus Call',
    value: AgentCallType.FOCUS,
    description:
      'Focus on practicing responses to a single prompt from the buyer bot (i.e. monologuing, pitch practice, single objection handling)',
    Icon: DoorOpenIcon,
  },
  {
    label: 'Demo Call',
    value: AgentCallType.DEMO,
    description:
      'Conduct a demo call to showcase the product with an interactive walkthrough of key features and benefits, engaging prospective buyers effectively.',
    Icon: LaptopIcon,
  },
  {
    label: 'Manager 1:1',
    value: AgentCallType.MANAGER_ONE_ON_ONE,
    description: 'Conduct a simulated one-on-one meeting with your reps',
    Icon: Users,
  },
];

export const CALL_TYPE_TO_ICON = {
  [AgentCallType.COLD]: {
    Icon: SnowflakeIcon,
  },
  [AgentCallType.GATEKEEPER]: {
    Icon: DoorOpenIcon,
  },
  [AgentCallType.WARM]: {
    Icon: FlameKindlingIcon,
  },
  [AgentCallType.DISCOVERY]: {
    Icon: SearchIcon,
  },
  [AgentCallType.FOCUS]: {
    Icon: ScanEyeIcon,
  },
  [AgentCallType.RENEWAL]: {
    Icon: RefreshCwIcon,
  },
  [AgentCallType.CHECKIN]: {
    Icon: UserCheckIcon,
  },
  [AgentCallType.DEMO]: {
    Icon: LaptopIcon,
  },
  [AgentCallType.CUSTOM]: {
    Icon: CircleHelpIcon,
  },
};

export const CALL_SCENARIO_OPTIONS = {
  // [AgentCallType.MANAGER_ONE_ON_ONE]: [
  //   {
  //     value: 'one-on-one-check-in',
  //     label: 'One-on-one Check-In',
  //     description:
  //       'A regular sync between a manager and rep to discuss progress, challenges, and support needs.',
  //   },
  //   {
  //     value: 'forecast-review',
  //     label: 'Forecast Review',
  //     description:
  //       'A focused conversation to evaluate current pipeline health and validate revenue projections.',
  //   },
  //   {
  //     value: 'deal-coaching',
  //     label: 'Deal Coaching',
  //     description:
  //       'A strategic session to dissect key deals and improve tactics for closing successfully.',
  //   },
  //   {
  //     value: 'performance-improvement-plan',
  //     label: 'Performance Improvement Plan',
  //     description:
  //       'A structured discussion to address performance gaps and align on clear improvement goals.',
  //   },
  // ],
  [AgentCallType.MANAGER_ONE_ON_ONE]: [
    {
      value: 'General Check-in',
      label: 'General Check-in',
      description:
        'A routine one-on-one meeting to maintain alignment, surface issues, and build rapport.',
    },
    {
      value: 'Deal Coaching',
      label: 'Deal Coaching',
      description:
        'A strategic session to dissect key deals and improve tactics for closing successfully.',
    },
    {
      value: 'Forecast Meeting',
      label: 'Forecast Meeting',
      description:
        'A conversation focused on current pipeline and projections to meet or exceed targets.',
    },
    {
      value: 'Performance Alignment Meeting',
      label: 'Performance Alignment Meeting',
      description:
        'A proactive check-in to discuss progress toward goals and resolve early performance concerns.',
    },
    {
      value: 'Performance Improvement Plan',
      label: 'Performance Improvement Plan',
      description:
        'A structured discussion to address performance gaps and align on clear improvement goals.',
    },
  ],

  [AgentCallType.COLD]: [
    {
      value: AgentCallType.NONE,
      label: 'None',
      description: 'Buyer does not have any existing solution in place.',
    },
    {
      value: 'Uses a Competitor',
      label: 'Uses a competitor / has another solution',
      description:
        'Buyer is currently using an alternative solution to address their needs.',
    },
    {
      value: 'Is aware of problem',
      description: 'Buyer does not have any existing solution in place.',

      label: 'Is aware of problem',
    },
    {
      value: 'Is aware of solution',
      description:
        'Buyer is aware of the problem and knows that solutions exist but has not yet taken action.',

      label: 'Is aware of problem & solution',
    },
    {
      value: 'Pre-existing champion',
      description: 'Buyer does not have any existing solution in place.',

      label: 'Is pre-existing champion',
    },
  ],
  [AgentCallType.WARM]: [
    {
      value: AgentCallType.NONE,
      description: 'Buyer does not have any existing solution in place.',

      label: 'None',
    },
    {
      value: 'Uses a Competitor',
      description:
        'Buyer is currently using an alternative solution to address their needs.',

      label: 'Uses a competitor / has another solution',
    },
    {
      value: 'Is aware of problem',
      description:
        'Buyer is aware of the issue but has not yet acted to address it.',

      label: 'Is aware of problem',
    },
    {
      value: 'Is aware of solution',
      description:
        'Buyer is aware of the problem and knows that solutions exist but has not yet taken action.',

      label: 'Is aware of problem & solution',
    },
    {
      value: 'Pre-existing champion',
      description:
        'Buyer is already knowledgeable and advocates for a solution in this area.',

      label: 'Is pre-existing champion',
    },
  ],
  [AgentCallType.GATEKEEPER]: [
    {
      value: 'Easy',
      description: '',

      label: 'None',
    },
    {
      value: 'Medium',
      description: '',

      label: 'None',
    },
    {
      value: 'Hard',
      description: '',

      label: 'None',
    },
  ],
  [AgentCallType.RENEWAL]: [
    // { value: 'None - Default', description: '', label: 'None - Default' },
    {
      value: 'Account Strategy Alignment',
      description: '',
      label: 'Account Strategy Alignment',
    },
    {
      value: 'Upsell or Cross Sell',
      description: '',
      label: 'Upsell or Cross Sell',
    },
    {
      value: 'Renewal Churn Risk',
      description: '',
      label: 'Renewal Churn Risk',
    },
  ],
  [AgentCallType.CHECKIN]: [
    // { value: 'None - Default', description: '', label: 'None - Default' },
    { value: 'Product Adoption', description: '', label: 'Product Adoption' },
    { value: 'Churn Risk', description: '', label: 'Churn Risk' },
    {
      value: 'Issue Resolution and Support',
      description: '',
      label: 'Issue Resolution and Support',
    },
    {
      value: 'Relationship Building',
      description: '',
      label: 'Relationship Building',
    },
  ],
  [AgentCallType.CUSTOM]: [
    {
      value: AgentCallType.NONE,
      description: '',

      label: 'None',
    },
  ],
  [AgentCallType.DISCOVERY]: [
    {
      value: 'Asks lot of questions - smart',
      description: '',

      label: 'Solution Aware, Asks lot of questions - smart',
    },
    {
      value: 'Asks lot of questions - clueless',
      description: '',

      label: 'Solution Aware, Asks lot of questions - clueless',
    },
    {
      value: 'Tire Kicker',
      description: '',

      label: 'Solution Aware, Tire Kicker',
    },
    {
      value: 'Wants solution and wants to help you build business case',
      description: '',

      label:
        'Solution Aware, Wants solution and wants to help you build business case',
    },
    {
      value: 'Skeptical of solution',
      label: 'Solution Aware, Skeptical of solution',
    },
    {
      value: 'Skeptical of solution, needs you to build business case',
      description: '',

      label:
        'Solution Aware, Skeptical of solution, needs you to build business case',
    },
    {
      value: 'Boss forced them to take a meeting and get a quote',
      description: '',

      label:
        'Solution Aware, Boss forced them to take a meeting and get a quote',
    },
    {
      value: 'Using Competitor, None - Default',
      description: '',

      label: 'Using Competitor, None',
    },
    {
      value: 'Using Competitor, Asks lot of questions - smart',
      description: '',

      label: 'Using Competitor, Asks lot of questions - smart',
    },
    {
      value: 'Using Competitor, Asks lot of questions - clueless',
      description: '',

      label: 'Using Competitor, Asks lot of questions - clueless',
    },
    {
      value: 'Using Competitor, Tire Kicker',
      description: '',

      label: 'Using Competitor, Tire Kicker',
    },
    {
      value:
        'Using Competitor, Wants solution and wants to help you build business case',
      description: '',

      label:
        'Using Competitor, Wants solution and wants to help you build business case',
    },
    {
      value: 'Using Competitor, Skeptical of solution',
      label: 'Using Competitor, Skeptical of solution',
    },
    {
      value:
        'Using Competitor, Skeptical of solution, needs you to build business case',
      description: '',

      label:
        'Using Competitor, Skeptical of solution, needs you to build business case',
    },
    {
      value:
        'Using Competitor, Boss forced them to take a meeting and get a quote',
      description: '',

      label:
        'Using Competitor, Boss forced them to take a meeting and get a quote',
    },
    // {
    //     value: "Using Competitor, Wants to build their business and needs your help",
    //     label: "Using Competitor, Wants to build their business and needs your help",
    // },
  ],
  [AgentCallType.DEMO]: [
    {
      value: 'Asks lot of questions - smart',
      description: '',

      label: 'Solution Aware, Asks lot of questions - smart',
    },
    {
      value: 'Asks lot of questions - clueless',
      description: '',

      label: 'Solution Aware, Asks lot of questions - clueless',
    },
    {
      value: 'Tire Kicker',
      description: '',

      label: 'Solution Aware, Tire Kicker',
    },
    {
      value: 'Wants solution and wants to help you build business case',
      description: '',

      label:
        'Solution Aware, Wants solution and wants to help you build business case',
    },
    {
      value: 'Skeptical of solution',
      label: 'Solution Aware, Skeptical of solution',
    },
    {
      value: 'Skeptical of solution, needs you to build business case',
      description: '',

      label:
        'Solution Aware, Skeptical of solution, needs you to build business case',
    },
    {
      value: 'Boss forced them to take a meeting and get a quote',
      description: '',

      label:
        'Solution Aware, Boss forced them to take a meeting and get a quote',
    },
    {
      value: 'Using Competitor, None - Default',
      description: '',

      label: 'Using Competitor, None',
    },
    {
      value: 'Using Competitor, Asks lot of questions - smart',
      description: '',

      label: 'Using Competitor, Asks lot of questions - smart',
    },
    {
      value: 'Using Competitor, Asks lot of questions - clueless',
      description: '',

      label: 'Using Competitor, Asks lot of questions - clueless',
    },
    {
      value: 'Using Competitor, Tire Kicker',
      description: '',

      label: 'Using Competitor, Tire Kicker',
    },
    {
      value:
        'Using Competitor, Wants solution and wants to help you build business case',
      description: '',

      label:
        'Using Competitor, Wants solution and wants to help you build business case',
    },
    {
      value: 'Using Competitor, Skeptical of solution',
      label: 'Using Competitor, Skeptical of solution',
    },
    {
      value:
        'Using Competitor, Skeptical of solution, needs you to build business case',
      description: '',

      label:
        'Using Competitor, Skeptical of solution, needs you to build business case',
    },
    {
      value:
        'Using Competitor, Boss forced them to take a meeting and get a quote',
      description: '',

      label:
        'Using Competitor, Boss forced them to take a meeting and get a quote',
    },
  ],
};

export const AGENT_EMOTIONAL_STATE_OPTIONS = [
  {
    label: 'Rude',
    description: 'Rude - Prefers painpoint discovery before the pitch (HARD)',
    value: AgentEmotionalState.RUDE,
    color: 'bg-red-50',
    textColor: 'text-red-600',
    borderColor: 'border-red-300',
  },
  {
    label: 'Nice (Low Hangup Rate)',
    description: 'Kind - Low Hangup Rate (EASY)',
    value: AgentEmotionalState.NICE_NO_HANGUP,
    color: 'bg-green-50',
    textColor: 'text-green-600',
    borderColor: 'border-green-300',
  },
  {
    label: 'Nice',
    description: 'Kind - Busy (EASY)',
    value: AgentEmotionalState.NICE,
    color: 'bg-green-50',
    textColor: 'text-green-600',
    borderColor: 'border-green-300',
  },
  {
    label: 'Nice (Less Inquisitive)',
    description: 'Kind - Busy, Less Inquisitive (EASY)',
    value: AgentEmotionalState.NICE_LESS_INQUISITIVE,
    color: 'bg-green-50',
    textColor: 'text-green-600',
    borderColor: 'border-green-300',
  },
  {
    label: 'Rude (European Style Call)',
    description: 'Rude - Wants the pitch right away (HARD)',
    value: AgentEmotionalState.RUDE_FLIP,
    color: 'bg-red-50',
    textColor: 'text-red-600',
    borderColor: 'border-red-300',
  },
  {
    label: 'Less Rude',
    description:
      'Less Rude - Prefers painpoint discovery before the pitch (MEDIUM)',
    value: AgentEmotionalState.LESS_RUDE,
    color: 'bg-yellow-50',
    textColor: 'text-yellow-700',
    borderColor: 'border-yellow-500',
  },
  {
    label: 'Less Rude (Less Inquisitive)',
    description:
      'Less Rude and Less Inquisitive - Asks least questions, but responds with more detailed answers. (MEDIUM)',
    value: AgentEmotionalState.LESS_RUDE_LESS_INQUISITIVE,
    color: 'bg-yellow-50',
    textColor: 'text-yellow-700',
    borderColor: 'border-yellow-500',
  },
  {
    label: 'Sassy',
    description: 'Sassy - Has some fun personality quirks (HARD)',
    value: AgentEmotionalState.SASSY,
    color: 'bg-purple-50',
    textColor: 'text-purple-600',
    borderColor: 'border-purple-300',
  },
  {
    label: 'Rude (Less Inquisitive)',
    description:
      'Rude - Less inquisitive and prefers discovery before the pitch (HARD)',
    value: AgentEmotionalState.RUDE_NOT_INQUISITIVE,
    color: 'bg-red-50',
    textColor: 'text-red-600',
    borderColor: 'border-red-300',
  },
];

// Discovery also refers to rude emtional state but needs another description
export const AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE = {
  [AgentCallType.COLD]: [
    {
      label: 'Nice (Low Hangup Rate)',
      description: 'Kind - Low Hangup Rate (EASY)',
      value: AgentEmotionalState.NICE_NO_HANGUP,
    },
    {
      label: 'Nice',
      description: 'Kind - Busy (EASY)',
      value: AgentEmotionalState.NICE,
    },
    {
      label: 'Less Rude',
      description:
        'Less Rude - Prefers painpoint discovery before the pitch (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE,
    },
    {
      label: 'Less Rude (Less Inquisitive)',
      description:
        'Less Rude and Less Inquisitive - Asks least questions, but responds with more detailed answers. (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE_LESS_INQUISITIVE,
    },
    {
      label: 'Rude',
      description: 'Rude - Prefers painpoint discovery before the pitch (HARD)',
      value: AgentEmotionalState.RUDE,
    },
    {
      label: 'Rude (Less Inquisitive)',
      description:
        'Rude - Less inquisitive and prefers discovery before the pitch (HARD)',
      value: AgentEmotionalState.RUDE_NOT_INQUISITIVE,
    },
    {
      label: 'Rude (European Style Call)',
      description: 'Rude - Wants the pitch right away (HARD)',
      value: AgentEmotionalState.RUDE_FLIP,
    },
    {
      label: 'Sassy',
      description: 'Sassy - Has some fun personality quirks (HARD)',
      value: AgentEmotionalState.SASSY,
    },
  ],
  [AgentCallType.WARM]: [
    // {
    //   label: 'Rude',
    //   description: 'Rude - Prefers painpoint discovery before the pitch (HARD)',
    //   value: AgentEmotionalState.RUDE,
    // },
    // {
    //   label: 'Less Rude',
    //   description:
    //     'Less Rude - Prefers painpoint discovery before the pitch (MEDIUM)',
    //   value: AgentEmotionalState.LESS_RUDE,
    // },
    // {
    //   label: 'Nice',
    //   description: 'Kind - Busy (EASY)',
    //   value: AgentEmotionalState.NICE,
    // },
    // {
    //   label: 'Rude (European Style Call)',
    //   description: 'Rude - Wants the pitch right away (HARD)',
    //   value: AgentEmotionalState.RUDE_FLIP,
    // },
    {
      label: 'Nice (Low Hangup Rate)',
      description: 'Kind - Low Hangup Rate (EASY)',
      value: AgentEmotionalState.NICE_NO_HANGUP,
    },
    {
      label: 'Nice',
      description: 'Kind - Busy (EASY)',
      value: AgentEmotionalState.NICE,
    },
    {
      label: 'Less Rude',
      description:
        'Less Rude - Prefers painpoint discovery before the pitch (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE,
    },
    {
      label: 'Less Rude (Less Inquisitive)',
      description:
        'Less Rude and Less Inquisitive - Asks least questions, but responds with more detailed answers. (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE_LESS_INQUISITIVE,
    },
    {
      label: 'Rude',
      description: 'Rude - Prefers painpoint discovery before the pitch (HARD)',
      value: AgentEmotionalState.RUDE,
    },
    {
      label: 'Rude (Less Inquisitive)',
      description:
        'Rude - Less inquisitive and prefers discovery before the pitch (HARD)',
      value: AgentEmotionalState.RUDE_NOT_INQUISITIVE,
    },
    {
      label: 'Rude (European Style Call)',
      description: 'Rude - Wants the pitch right away (HARD)',
      value: AgentEmotionalState.RUDE_FLIP,
    },
    {
      label: 'Sassy',
      description: 'Sassy - Has some fun personality quirks (HARD)',
      value: AgentEmotionalState.SASSY,
    },
  ],
  [AgentCallType.GATEKEEPER]: [
    {
      label: 'Rude',
      description: 'Rude - Prefers painpoint discovery before the pitch (HARD)',
      value: AgentEmotionalState.RUDE,
    },
  ],
  [AgentCallType.DISCOVERY]: [
    {
      label: 'Nice',
      description: 'Kind - Busy (EASY)',
      value: AgentEmotionalState.NICE,
    },
    {
      label: 'Nice (Less Inquisitive)',
      description: 'Kind - Busy, Less Inquisitive (EASY)',
      value: AgentEmotionalState.NICE_LESS_INQUISITIVE,
    },
    {
      label: 'Less Rude',
      description:
        'Less Rude - Prefers painpoint discovery before the pitch (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE,
    },
    {
      label: 'Less Rude (Less Inquisitive)',
      description:
        'Less Rude - Does not want to build rapport, wants to get to the point (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE_LESS_INQUISITIVE,
    },

    {
      label: 'Rude',
      description:
        'Rude - Condescending, wants to get to the point, willing to hang up (HARD)',
      value: AgentEmotionalState.RUDE,
    },
  ],
  [AgentCallType.RENEWAL]: [
    {
      label: 'Nice',
      description: 'Kind - Busy (EASY)',
      value: AgentEmotionalState.NICE,
    },
  ],
  [AgentCallType.CHECKIN]: [
    {
      label: 'Nice',
      description: 'Kind - Busy (EASY)',
      value: AgentEmotionalState.NICE,
    },
  ],
  [AgentCallType.DEMO]: [
    {
      label: 'Nice',
      description: 'Kind - Busy (EASY)',
      value: AgentEmotionalState.NICE,
    },
    {
      label: 'Nice (Less Inquisitive)',
      description: 'Kind - Busy, Less Inquisitive (EASY)',
      value: AgentEmotionalState.NICE_LESS_INQUISITIVE,
    },
    {
      label: 'Less Rude',
      description:
        'Less Rude - Prefers painpoint discovery before the pitch (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE,
    },
    {
      label: 'Less Rude (Less Inquisitive)',
      description:
        'Less Rude - Does not want to build rapport, wants to get to the point (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE_LESS_INQUISITIVE,
    },

    // {
    //   label: 'Rude',
    //   description:
    //     'Rude - Condescending, wants to get to the point, willing to hang up (HARD)',
    //   value: AgentEmotionalState.RUDE,
    // },
  ],
  [AgentCallType.CUSTOM]: [
    {
      label: 'Less Rude',
      description:
        'Less Rude - Prefers painpoint discovery before the pitch (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE,
    },
  ],
  [AgentCallType.MANAGER_ONE_ON_ONE]: [
    {
      label: 'Nice',
      description: 'Kind - Busy (EASY)',
      value: AgentEmotionalState.NICE,
    },
    {
      label: 'Less Rude',
      description:
        'Less Rude - Prefers painpoint discovery before the pitch (MEDIUM)',
      value: AgentEmotionalState.LESS_RUDE,
    },
    {
      label: 'Rude',
      description:
        'Rude - Condescending, wants to get to the point, willing to hang up (HARD)',
      value: AgentEmotionalState.RUDE,
    },
  ],
};

export const AGENT_DEFAULT_EMOTIONAL_STATE = {
  [AgentCallType.COLD]: AgentEmotionalState.RUDE,
  [AgentCallType.WARM]: AgentEmotionalState.LESS_RUDE,
  [AgentCallType.GATEKEEPER]: AgentEmotionalState.LESS_RUDE,
  [AgentCallType.DISCOVERY]: AgentEmotionalState.NICE,
  [AgentCallType.RENEWAL]: AgentEmotionalState.NICE,
  [AgentCallType.CHECKIN]: AgentEmotionalState.NICE,
  [AgentCallType.DEMO]: AgentEmotionalState.NICE,
  [AgentCallType.MANAGER_ONE_ON_ONE]: AgentEmotionalState.NICE,
};
