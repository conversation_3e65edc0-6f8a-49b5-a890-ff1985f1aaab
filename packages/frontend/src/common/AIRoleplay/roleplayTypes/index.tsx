import useAgentStatsByCallType from '@/hooks/useAgentStatsByCallType';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import { AgentCallType } from '@/lib/Agent/types';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { PlusIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';

export const { Icon: ColdCallIcon } = CALL_TYPE_TO_ICON[AgentCallType.COLD];
export const { Icon: GatekeeperCallIcon } =
  CALL_TYPE_TO_ICON[AgentCallType.GATEKEEPER];
export const { Icon: WarmCallIcon } = CALL_TYPE_TO_ICON[AgentCallType.WARM];
export const { Icon: DiscoveryCallIcon } =
  CALL_TYPE_TO_ICON[AgentCallType.DISCOVERY];
export const { Icon: FocusCallIcon } = CALL_TYPE_TO_ICON[AgentCallType.FOCUS];
export const { Icon: CheckinCallIcon } =
  CALL_TYPE_TO_ICON[AgentCallType.CHECKIN];
export const { Icon: RenewalCallIcon } =
  CALL_TYPE_TO_ICON[AgentCallType.RENEWAL];

export default function RoleplayTypes() {
  const router = useRouter();
  const { callTypeOptions } = useCallTypeOptions();
  const { data: stats } = useAgentStatsByCallType();

  const totalNumBots: number = Number(
    Object.values(stats || {}).reduce((acc, val: any) => acc + val, 0),
  );

  return (
    <div>
      <div className="flex items-center">
        <div className="text-base font-semibold flex-1">Roleplay Types</div>
        <div>
          <Button variant={'outline'} onClick={() => router.push('/buyers')}>
            View all bots ({totalNumBots || 0})
          </Button>
        </div>
      </div>

      <div className="flex items-stretch mt-4 space-x-2">
        {callTypeOptions.map((ct, index) => {
          if (ct.value === AgentCallType.GATEKEEPER) return null;
          const Icon =
            CALL_TYPE_TO_ICON[ct.value as keyof typeof CALL_TYPE_TO_ICON].Icon;
          let forceNew = false;
          let linkUrl = `/buyers?callType=${ct.value}`;
          if (!stats?.[ct.value] || stats?.[ct.value] == 0) {
            forceNew = true;
            if (ct.value === AgentCallType.FOCUS) {
              linkUrl = '/buyers/create/focus?callType=' + ct.value;
            } else {
              linkUrl = '/buyers/create/ai-generator?callType=' + ct.value;
            }
          }

          return (
            <div
              key={'ct-' + index}
              className={cn(
                'flex flex-col items-center justify-center p-3 bg-white hover:bg-secondary transition-all duration-300 border rounded-lg flex-1 cursor-pointer',
                {
                  'bg-transparent': !stats?.[ct.value],
                },
              )}
              onClick={() => router.push(linkUrl)}
            >
              {forceNew && (
                <div className="font-semibold mb-2">
                  <PlusIcon size={18} className="text-muted-foreground" />
                </div>
              )}

              {!forceNew && (
                <div className="font-semibold mb-2">
                  {stats?.[ct.value] || '0'}
                </div>
              )}

              <div className="text-xs text-muted-foreground flex items-center">
                <div className="mr-1">
                  <Icon size={12} />
                </div>
                <div>
                  {stats?.[ct.value]
                    ? `${ct.label.replace('Call', 'Bot')}s`
                    : `Create a ${ct.label.replace('Call', 'Bot')}`}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
