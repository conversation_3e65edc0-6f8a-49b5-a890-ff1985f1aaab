import {
  Dialog,
  DialogPortal,
  DialogOverlay,
} from '@/components/ui/Hyperbound/dialog-for-calls';
import { Button } from '@/components/ui/button';
import { AgentDto, AgentFolderDto } from '@/lib/Agent/types';
import AgentService, { FoldersService } from '@/lib/Agent';
import { ChevronLeft, Loader2Icon, X } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';
import { Id, toast } from 'react-toastify';
import useOrgAgents from '@/hooks/useOrgAgents';
import BuyerCard, { CardType } from '@/components/BuyerCard';
import Filters from '@/common/Buyers/ProdSite/filters';
import { PageState } from '@/common/Buyers/ProdSite';
import { AnimatePresence, motion } from 'framer-motion';
import useOrgAgentsFiltered from '@/hooks/useOrgAgentsFiltered';
import useOrgAgentsbyId from '@/hooks/useOrgAgentsById';
import { useQueryClient } from '@tanstack/react-query';

interface IProps {
  parentFolder: AgentFolderDto;
  isOpen: boolean;
  cancel: () => void;
  onNewAgents: (nf: AgentFolderDto[]) => void;
  teamId?: number;
}

export default function AddBuyersToFolder({
  parentFolder,
  isOpen,
  cancel,
  onNewAgents,
  teamId,
}: IProps) {
  const queryClient = useQueryClient();

  const [isSaving, setIsSaving] = useState<boolean>(false);
  const toastId = useRef<Id | null>(null);
  const [selectedAgentsIds, setSelectedAgentsIds] = useState<number[]>([]);
  const [selectedAgentsByIds, setSelectedAgentsByIds] = useState<{
    [key: number]: boolean;
  }>({});
  const [selectedAgents, setSelectedAgents] = useState<{
    [key: number]: AgentDto;
  }>({});
  const numberOfResults = 30;
  const [showVariations, setShowVariations] = useState<boolean>(false);
  const [isLoadingVariations, setIsLoadingVariations] =
    useState<boolean>(false);
  const [variations, setVariations] = useState<AgentDto[]>([]);

  const [showAgentsTags, setShowAgentsTags] = useState<boolean>(false);
  const toggleShowTags = () => {
    setShowAgentsTags(!showAgentsTags);
  };

  const [pageState, setPageState] = useState<PageState>(new PageState());

  const updatePageState = (ps: PageState) => {
    setPageState(ps);
  };

  /************************************/
  /*************** INIT ***************/
  /************************************/

  const { data: tmpAgents } = useOrgAgents(
    pageState.callType,
    true,
    0,
    numberOfResults,
    pageState.searchString,
  );

  let useComboFilterVariationsTags = false;
  if (
    pageState.tags &&
    pageState.tags.length > 0 &&
    pageState.variations &&
    pageState.variations.length > 0
  ) {
    useComboFilterVariationsTags = true;
  } else if (pageState.tags && pageState.tags.length > 0) {
    useComboFilterVariationsTags = true;
  }

  const { data: agentsWithVariations } = useOrgAgentsbyId(
    pageState.variations.map(Number),
    !useComboFilterVariationsTags,
  );

  const { data: agentsFiltered } = useOrgAgentsFiltered(
    useComboFilterVariationsTags,
    pageState.searchString,
    pageState.variations.map(Number),
    pageState.tags.map(Number),
  );

  let agents: AgentDto[] | undefined = [];
  if (useComboFilterVariationsTags) {
    agents = agentsFiltered;
  } else if (pageState.variations.length > 0) {
    agents = agentsWithVariations;
  } else {
    agents = tmpAgents;
  }

  /************************************/
  /*************** ACTIONS ***************/
  /************************************/
  const toggleAgent = (agent: AgentDto) => {
    const selected = !selectedAgentsByIds[agent.id];
    setSelectedAgentsByIds({ ...selectedAgentsByIds, [agent.id]: selected });
    setSelectedAgents({ ...selectedAgents, [agent.id]: agent });

    if (selected) {
      setSelectedAgentsIds((o) => {
        return [...o, agent.id];
      });
    } else {
      setSelectedAgentsIds((o) => {
        return o.filter((id) => id !== agent.id);
      });
    }
  };

  const openVariations = async (agent: AgentDto) => {
    setShowVariations(true);
    setIsLoadingVariations(true);
    const v = await AgentService.getOrgAgentVariations(agent.vapiId);
    setVariations([agent, ...v]);
    setIsLoadingVariations(false);
  };
  /************************************/
  /*************** SAVE ***************/
  /************************************/

  const save = async () => {
    setIsSaving(true);

    let ok = false;
    const newAgents: AgentFolderDto[] = [];
    let position = 0;

    if (parentFolder && parentFolder.children) {
      let maxP = 0;
      for (const f of parentFolder.children) {
        if (f.sort > maxP) {
          maxP = f.sort;
        }
      }
      maxP++;
      position = maxP;
    }

    try {
      for (const agentId of selectedAgentsIds) {
        const nf = await FoldersService.linkAgentToFolder(
          agentId,
          parentFolder.id,
          position,
          teamId,
        );
        nf.agent = selectedAgents[agentId];
        position++;
        newAgents.push(nf);
      }
      ok = true;
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    if (ok) {
      onNewAgents(newAgents);
    }

    queryClient.invalidateQueries({ queryKey: ['agents-folders'] });
    setIsSaving(false);
  };

  /*****************************************/
  /*************** RENDERING ***************/
  /*****************************************/

  const [exitingPnl, setExitingPnl] = useState<boolean>(false);

  const closeDialog = () => {
    setExitingPnl(true);
    setTimeout(() => {
      cancel();
      setExitingPnl(false);
    }, 160);
  };

  const scrollableContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener('wheel', scrollContent);

    return () => {
      //setPageStatus("profile");
      window.removeEventListener('wheel', scrollContent);
    };
  }, []);

  const scrollContent = (e: WheelEvent) => {
    if (scrollableContainer.current) {
      scrollableContainer.current.scrollTop += e.deltaY;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={closeDialog}>
      <DialogPortal>
        <DialogOverlay />
        <div className="fixed top-0 left-0 right-0 bottom-0 w-full h-full z-50 overflow-hidden">
          <AnimatePresence>
            {!exitingPnl && (
              <motion.div
                initial={{ opacity: 0, y: 700 }}
                exit={{ opacity: 0, y: 700 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, ease: 'easeOut' }}
                className="flex flex-col h-screen"
              >
                <div className="bg-white m-10 rounded-2xl h-full overflow-hidden flex flex-col text-xs">
                  <div className="flex items-center px-4 pt-4">
                    <div className="flex items-center font-semibold text-lg">
                      Add buyer bots
                    </div>
                    <div className="flex-grow"></div>
                    <div className="cursor-pointer " onClick={closeDialog}>
                      <X size={20} />
                    </div>
                  </div>

                  <Filters
                    isShowingTags={showAgentsTags}
                    toggleShowTags={toggleShowTags}
                    filters={pageState}
                    onFiltersUpdated={updatePageState}
                    hideAddNewTagBtn={true}
                    hideStatusFilter={true}
                    showCallTypeFilter={true}
                    disabled={showVariations}
                  />

                  <div
                    className="grid  grid-cols-4 gap-2 mt-2 pl-2 pr-2 mb-10 overflow-auto flex-1"
                    ref={scrollableContainer}
                  >
                    {showVariations &&
                      (isLoadingVariations ? (
                        <div>
                          <Loader2Icon className="animate-spin" />
                        </div>
                      ) : (
                        variations?.map((a) => {
                          return (
                            <motion.div
                              key={a.id}
                              animate={{ x: 0, opacity: 1 }}
                              initial={{ x: -20, opacity: 0 }}
                            >
                              <BuyerCard
                                key={a.id}
                                agent={a}
                                isSelected={selectedAgentsByIds[a.id]}
                                type={CardType.SELECT_ONLY}
                                onCardClick={toggleAgent}
                                showAgentsTags={showAgentsTags}
                              />
                            </motion.div>
                          );
                        })
                      ))}
                    {!showVariations &&
                      agents?.map((a) => {
                        return (
                          <motion.div
                            key={a.id}
                            animate={{ x: 0, opacity: 1 }}
                            initial={{ x: -20, opacity: 0 }}
                          >
                            <BuyerCard
                              key={a.id}
                              agent={a}
                              isSelected={selectedAgentsByIds[a.id]}
                              type={CardType.SELECT_ONLY}
                              onCardClick={toggleAgent}
                              showAgentsTags={showAgentsTags}
                              onShowVariations={openVariations}
                            />
                          </motion.div>
                        );
                      })}
                  </div>

                  <div className="flex items-center mr-4 mb-4 ml-4">
                    {showVariations && (
                      <Button
                        variant={'outline'}
                        onClick={() => {
                          setShowVariations(false);
                        }}
                      >
                        <ChevronLeft />
                        Back
                      </Button>
                    )}
                    <div className="flex-grow"></div>
                    {!showVariations &&
                      (isSaving ? (
                        <Loader2Icon className="animate-spin" />
                      ) : (
                        <Button
                          disabled={selectedAgentsIds.length < 1}
                          onClick={save}
                        >
                          Add{' '}
                          {selectedAgentsIds.length > 0 &&
                            `${selectedAgentsIds.length}`}{' '}
                          buyer bots
                        </Button>
                      ))}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
