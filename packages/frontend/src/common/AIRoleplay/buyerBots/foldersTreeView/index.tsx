import useUserSession from '@/hooks/useUserSession';
import FolderContent from './folderContent';
import FoldersNavigator from './foldersNavigator';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AgentFolderDto } from '@/lib/Agent/types';
import {
  useAgentsFolders,
  useAgentsFoldersFavourite,
} from '@/hooks/useAgentsFolders';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';
import NewFolderModal from './newFolderModal';
import useDragAndDrop from '@/hooks/ui/useDragAndDrop';
import { ChevronLeft, FolderIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FoldersService } from '@/lib/Agent';
import { Id, toast } from 'react-toastify';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { StarFilledIcon } from '@radix-ui/react-icons';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import AgentAvatar from '@/components/Avatars/Agent';

interface IProps {
  filterByTeamId?: number; //if undefined, shows the folders assigned to the orgId with teamId=undefined
  mode?: 'default' | 'selection';
  onFolderSelected?: (folder: AgentFolderDto, selected: boolean) => void;
  selectedFolders?: AgentFolderDto[];
}

export default function FoldersTreeView({
  filterByTeamId,
  mode = 'default',
  onFolderSelected,
  selectedFolders,
}: IProps) {
  const { isAdmin } = useUserSession();
  const [folders, setFolders] = useState<AgentFolderDto[]>([]);
  const [folderForContent, setFolderForContent] = useState<AgentFolderDto>();
  const [showingFavourites, setShowingFavourites] = useState<boolean>(false);

  const {
    data: dbFolders,
    isLoading: isLoadingFolders,
    refetch: refetchTree,
    isRefetching: isRefetchingFolders,
  } = useAgentsFolders(filterByTeamId);
  const {
    data: dbFav,
    isLoading: isLoadingFavs,
    refetch: refetchFavourites,
    isRefetching: isRefetchingFavourites,
  } = useAgentsFoldersFavourite(filterByTeamId);
  const toastId = useRef<Id | null>(null);

  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [curSearchParams, setCurSearchParams] = useState<URLSearchParams>(
    new URLSearchParams(searchParams.toString()),
  );
  const openFolderId = curSearchParams.get('folder');

  useEffect(() => {
    setCurSearchParams(new URLSearchParams(searchParams.toString()));
  }, [searchParams]);

  /*********************************/
  /************ INIT ***************/
  /*********************************/

  useEffect(() => {
    if (!isLoadingFolders && !isRefetchingFolders && dbFolders) {
      // if (folderForContent && folderForContent.teamId == filterByTeamId) {
      //   console.log('not in');
      //   //to udpate the content in case it changed
      //   const nf = findFolder(dbFolders, folderForContent?.id);
      //   setFolderForContent(nf);
      // } else

      if (dbFolders.length > 0) {
        let found = false;
        if (openFolderId) {
          const f = findFolder(dbFolders, parseInt(openFolderId));
          if (f) {
            setFolderForContent(f);
            found = true;
          }
        }

        if (!found) {
          //open first folder
          for (const f of dbFolders) {
            if (!f.agent) {
              setFolderForContent(f);
              break;
            }
          }
        }
      }

      if (!showingFavourites) {
        setFolders(dbFolders);
        setFreezeFolders(false);
      }
    }
  }, [isLoadingFolders, dbFolders, isRefetchingFolders, openFolderId]);

  useEffect(() => {
    if (!isLoadingFavs && !isRefetchingFavourites && dbFav) {
      if (showingFavourites) {
        setFolders(dbFav);
        setFreezeFolders(false);
      }
    }
  }, [isLoadingFavs, dbFav, isRefetchingFavourites]);

  /***********************************/
  /************ ACTIONS ***************/
  /***********************************/

  const [newFolderParentFolder, setNewFolderParentFolder] = useState<
    AgentFolderDto | undefined
  >();
  const [newFolderModalOpen, setNewFolderModalOpen] = useState<boolean>(false);

  const cancelAddFolder = () => {
    setNewFolderParentFolder(undefined);
    setNewFolderModalOpen(false);
  };

  const addNewFolder = (nf: AgentFolderDto) => {
    if (newFolderParentFolder) {
      if (!newFolderParentFolder.children) {
        newFolderParentFolder.children = [];
      }
      newFolderParentFolder.children.push(nf);
    } else {
      folders.push(nf);
    }
    refetchTree();
    refetchFavourites();
    cancelAddFolder();
  };

  const startAddNewFolder = (parentFolder: AgentFolderDto | undefined) => {
    setNewFolderParentFolder(parentFolder);
    setNewFolderModalOpen(true);
  };

  const showFolderContent = (f: AgentFolderDto) => {
    // setFolderForContent(f);
    // setUrlParameter("folder", String(f.id));
    curSearchParams.set('folder', String(f.id));
    router.push(`${pathname}?${curSearchParams.toString()}`);
  };

  const toggleFavourites = () => {
    if (showingFavourites) {
      setFolders(dbFolders || []);
      if (dbFolders && dbFolders.length > 0) {
        setFolderForContent(dbFolders[0]);
      } else {
        setFolderForContent(undefined);
      }

      setShowingFavourites(false);
    } else {
      setFolders(dbFav || []);
      if (dbFav && dbFav.length > 0) {
        setFolderForContent(dbFav[0]);
      } else {
        setFolderForContent(undefined);
      }
      setShowingFavourites(true);
    }
  };

  /***********************************/
  /******** DRAG AND DROP ************/
  /***********************************/

  const [isDNDActive, setIsDNDActive] = useState<boolean>(false);
  const [currentDraggedFolder, setCurrentDraggedFolder] =
    useState<AgentFolderDto>();
  const [freezeFolders, setFreezeFolders] = useState<boolean>(false);

  //DND REARRANGE FUNCTIONS:

  const findFolder = (
    currentFolders: AgentFolderDto[],
    id: number,
  ): AgentFolderDto | undefined => {
    for (const f of currentFolders) {
      if (f.id == id) {
        return f;
      }
      if (f.children) {
        const r = findFolder(f.children, id);
        if (r) {
          return r;
        }
      }
    }
  };

  const removeFolder = (
    currentFolders: AgentFolderDto[],
    id: number,
  ): AgentFolderDto[] => {
    return currentFolders
      .filter((f) => f.id != id) // Remove if it matches the id
      .map((folder) => {
        if (folder.children) {
          return {
            ...folder,
            children: removeFolder(folder.children, id),
          };
        }
        return folder;
      });
  };

  //parents cannot be dropped inside "themself"
  const preventDropOnChild = useCallback(
    (movingObj: AgentFolderDto, targetObjId: number): boolean => {
      if (movingObj) {
        if (movingObj.children) {
          for (const f of movingObj.children) {
            if (f.id == targetObjId) {
              return true;
            } else {
              if (preventDropOnChild(f, targetObjId)) {
                return true;
              }
            }
          }
        }
      }
      return false;
    },
    [folders],
  );

  const updateDb = async (
    movingObj: AgentFolderDto,
    updateFolders: AgentFolderDto[],
  ) => {
    try {
      await FoldersService.updateFolder(
        movingObj.id,
        movingObj.name,
        movingObj.description,
        movingObj.parentFolderId,
        movingObj.sort,
        movingObj.isPersonal,
        movingObj.isForAdminOnly,
      );
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }
    for (const f of updateFolders) {
      await FoldersService.updateFolder(
        f.id,
        f.name,
        f.description,
        f.parentFolderId,
        f.sort,
        f.isPersonal,
        f.isForAdminOnly,
      );
    }
  };

  const updateTreeView_DropInsideFolder = useCallback(
    (movingObj: any, targetObj: any) => {
      const newFolders = removeFolder(folders, movingObj.id);
      const f = findFolder(newFolders, targetObj.id);
      f?.children?.push(movingObj);
      setFolders([...newFolders]);
      if (folderForContent) {
        const fc = findFolder(newFolders, folderForContent?.id);
        setFolderForContent({ ...fc } as AgentFolderDto);
      }
    },
    [folders, folderForContent],
  );

  const updateTreeView_DropAroundFolder = useCallback(
    (movingObj: any, siblings: any) => {
      let newFolders = removeFolder(folders, movingObj.id);
      if (!movingObj.parentFolderId) {
        const tmp = [];
        let added = false;
        for (const s of siblings) {
          if (s.sort > movingObj.sort && !added) {
            tmp.push(movingObj);
            added = true;
          }
          // newFolders contains updated folders with the moving element removed
          // use elements from newFolders instead to ensure removal
          const updatedSibling = newFolders.find((f) => f.id === s.id) || s;
          tmp.push(updatedSibling);
        }
        if (!added) {
          tmp.push(movingObj);
        }
        newFolders = tmp;
      } else {
        const f = findFolder(newFolders, movingObj.parentFolderId);
        const tmp = [];
        let added = false;
        for (const s of siblings) {
          if (s.sort > movingObj.sort && !added) {
            tmp.push(movingObj);
            added = true;
          }
          tmp.push(s);
        }
        if (!added) {
          tmp.push(movingObj);
        }
        if (f) {
          f.children = tmp;
        }
      }
      setFolders([...newFolders]);
      if (folderForContent) {
        const fc = findFolder(newFolders, folderForContent?.id);
        setFolderForContent({ ...fc } as AgentFolderDto);
      }
    },
    [folders, folderForContent],
  );

  const dropInsideFolder = useCallback(
    async (movingObj: any, targetObj: any) => {
      if (folders && !preventDropOnChild(movingObj, targetObj.id)) {
        setFreezeFolders(true);
        let siblings: AgentFolderDto[] = [];
        if (
          targetObj.parentFolderId == undefined ||
          targetObj.parentFolderId == null
        ) {
          siblings = folders;
        } else {
          const targetFolder = findFolder(folders, targetObj.parentFolderId);
          if (targetFolder && targetFolder.children) {
            siblings = targetFolder.children;
          }
        }

        movingObj.parentFolderId = targetObj.id;

        let newMovingPositon = 0;
        if (siblings.length > 0) {
          for (const f of siblings) {
            if (f.sort >= newMovingPositon) {
              newMovingPositon = f.sort;
            }
          }
          newMovingPositon++;
        }
        //added for async FE update
        movingObj.sort = newMovingPositon;
        updateTreeView_DropInsideFolder(movingObj, targetObj);
        setFreezeFolders(false);
        updateDb(movingObj, []);
        //---end added for async FE update

        // let ok = false;

        // try {
        //   await FoldersService.updateFolder(movingObj.id, movingObj.name, movingObj.description, movingObj.parentFolderId, newMovingPositon, movingObj.isPersonal, movingObj.isForAdminOnly);
        //   ok = true;
        // } catch (e) {
        //   console.log(e);
        //   toastId.current = toast.error(
        //     "There was an error. Please try again later."
        //   );
        // }

        // if (ok) {
        //   refetchTree();
        //   refetchFavourites();
        // }
      }
    },
    [preventDropOnChild, folders, updateTreeView_DropInsideFolder],
  );

  const dropAroundFolder = useCallback(
    async (after: boolean, movingObj: any, targetObj: any) => {
      if (folders && !preventDropOnChild(movingObj, targetObj.id)) {
        setFreezeFolders(true);
        let siblings: AgentFolderDto[] = [];
        if (
          targetObj.parentFolderId == undefined ||
          targetObj.parentFolderId == null
        ) {
          siblings = folders;
        } else {
          const targetFolder = findFolder(folders, targetObj.parentFolderId);
          if (targetFolder && targetFolder.children) {
            siblings = targetFolder.children;
          }
        }

        if (movingObj.parentFolderId != targetObj.parentFolderId) {
          movingObj.parentFolderId = targetObj.parentFolderId;
        }
        let counter = 0;
        let newMovingPositon = 0;
        const updateFolders: AgentFolderDto[] = [];
        for (const f of siblings) {
          if (f.id != movingObj.id) {
            if (f.id == targetObj.id) {
              if (after) {
                updateFolders.push({ ...f, sort: counter });
                counter++;
                newMovingPositon = counter;
              } else {
                newMovingPositon = counter;
                counter++;
                updateFolders.push({ ...f, sort: counter });
              }
            } else {
              updateFolders.push({ ...f, sort: counter });
            }
            counter++;
          }
        }

        //added for async FE update
        movingObj.sort = newMovingPositon;
        updateTreeView_DropAroundFolder(movingObj, updateFolders);
        setFreezeFolders(false);
        updateDb(movingObj, updateFolders);
        //---end added for async FE update

        // let ok = false;

        // try {
        //   await FoldersService.updateFolder(movingObj.id, movingObj.name, movingObj.description, movingObj.parentFolderId, newMovingPositon, movingObj.isPersonal, movingObj.isForAdminOnly);
        //   ok = true;
        // } catch (e) {
        //   console.log(e);
        //   toastId.current = toast.error(
        //     "There was an error. Please try again later."
        //   );
        // }

        // for (const f of updateFolders) {
        //   await FoldersService.updateFolder(f.id, f.name, f.description, f.parentFolderId, f.sort, f.isPersonal, f.isForAdminOnly);
        // }

        // if (ok) {
        //   refetchTree();
        // }
      }
    },
    [preventDropOnChild, folders, updateTreeView_DropAroundFolder],
  );
  // ------- END REARRANGE FUNCTIONS

  const onDrop = useCallback(
    (movingObj: any, targetObj: any) => {
      if (movingObj.id != targetObj.id) {
        if (targetObj.isSpacerDown) {
          //place movingObj right after targetObj
          dropAroundFolder(true, movingObj, targetObj);
        } else if (targetObj.isSpacerUp) {
          //place movingObj right before targetObj
          dropAroundFolder(false, movingObj, targetObj);
        } else {
          if (!targetObj.agent) {
            //move movingObj inside targetObj
            //open and show targetObj content
            dropInsideFolder(movingObj, targetObj);
          }
        }
      }
    },
    [dropAroundFolder, dropInsideFolder],
  );

  const {
    dragPanelRef,
    onMouseDown,
    onMouseEnter,
    onMouseLeave,
    getDraggedObject,
  } = useDragAndDrop<AgentFolderDto, AgentFolderDto>(setIsDNDActive, onDrop);

  const startDragging = (f: AgentFolderDto, e: React.MouseEvent) => {
    setCurrentDraggedFolder(f);
    onMouseDown(f, e);
  };

  /***********************************/
  /************ RENDER ***************/
  /***********************************/

  if (folders.length == 0 && !showingFavourites && !isLoadingFolders) {
    return (
      <div className="py-14 flex flex-col items-center justify-center bg-white rounded-lg">
        <div className="rounded-full bg-gray-100 p-2 text-muted-foreground">
          <FolderIcon size={16} />
        </div>
        <div className="mt-6 font-medium text-base">No folder found</div>
        <div className="mt-2 text-muted-foreground text-sm text-wrap">
          {isAdmin
            ? 'Organize all your bots for your reps here'
            : 'Contact your admin to organize your bots into folders'}
        </div>
        {isAdmin && (
          <Button
            variant="default"
            className="mt-8"
            onClick={() => {
              startAddNewFolder(undefined);
            }}
          >
            Create a new folder
          </Button>
        )}

        <NewFolderModal
          isOpen={newFolderModalOpen}
          cancel={cancelAddFolder}
          onNewFolder={addNewFolder}
          numberOfRoots={folders.length}
          parentFolder={newFolderParentFolder}
          teamId={filterByTeamId}
        />
      </div>
    );
  }

  if (folders.length == 0 && showingFavourites && !isLoadingFavs) {
    return (
      <div className="py-14 flex flex-col items-center justify-center bg-white rounded-lg">
        <div className="rounded-full bg-gray-100 p-2 text-muted-foreground">
          <StarFilledIcon className="text-yellow-500 " />
        </div>
        <div className="mt-6 font-medium text-base">
          No favorite folders yet
        </div>
        <div className="mt-2 text-muted-foreground text-sm text-wrap">
          Add any folder to your favorites, and it will appear here.
        </div>
        <Button variant="outline" className="mt-8" onClick={toggleFavourites}>
          <ChevronLeft size={16} className="mr-2" />
          Back
        </Button>
      </div>
    );
  }

  return (
    <div
      className={cn('flex items-stretch', {
        'select-none': isDNDActive,
        'opacity-60 pointer-events-none': freezeFolders || isLoadingFolders,
      })}
    >
      <ScrollablePage className="mr-2 min-w-[25%]" marginBottom={20}>
        <FoldersNavigator
          folders={folders}
          showFolderContent={showFolderContent}
          showingContentForFolderId={folderForContent?.id}
          openAddFolderModal={startAddNewFolder}
          toggleFavourites={toggleFavourites}
          showingFavourites={showingFavourites}
          onMouseDown={startDragging}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          isDNDActive={isDNDActive}
          getDraggedObject={getDraggedObject}
          teamId={filterByTeamId}
          mode={mode}
          onFolderSelected={onFolderSelected}
          selectedFolders={selectedFolders}
        />
      </ScrollablePage>

      <ScrollablePage className="flex-1 overflow-hidden" marginBottom={20}>
        <FolderContent
          folder={folderForContent}
          folders={folders}
          openAddFolderModal={startAddNewFolder}
          showFolderContent={showFolderContent}
          onMouseDown={startDragging}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          isDNDActive={isDNDActive}
          getDraggedObject={getDraggedObject}
          teamId={filterByTeamId}
          mode={mode}
        />
      </ScrollablePage>

      <NewFolderModal
        isOpen={newFolderModalOpen}
        cancel={cancelAddFolder}
        onNewFolder={addNewFolder}
        numberOfRoots={folders.length}
        parentFolder={newFolderParentFolder}
        teamId={filterByTeamId}
      />

      <div
        className="absolute p-2 border border-gray-700 bg-black/60 text-white rounded-lg"
        style={{ display: 'none' }}
        ref={dragPanelRef}
      >
        {currentDraggedFolder &&
          (currentDraggedFolder?.agent ? (
            <div className="flex items-center px-2">
              <div className="mr-2">
                <AgentAvatar
                  className="w-[22px] h-[22px]"
                  agent={currentDraggedFolder?.agent}
                />
              </div>
              <div>
                {currentDraggedFolder.agent.firstName}{' '}
                {currentDraggedFolder.agent.lastName}
              </div>
            </div>
          ) : (
            <div className="flex items-center px-2">
              <div className="mr-2">
                <Image
                  src="/images/icons/folderClosed.svg"
                  alt="Folder Closed"
                  width={16}
                  height={16}
                />
              </div>
              <div>{currentDraggedFolder.name}</div>
            </div>
          ))}
      </div>
    </div>
  );
}
