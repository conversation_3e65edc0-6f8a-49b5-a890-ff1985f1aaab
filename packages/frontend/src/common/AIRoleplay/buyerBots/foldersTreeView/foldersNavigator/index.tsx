import { AgentFolderDto } from '@/lib/Agent/types';
import { FolderPlusIcon, Plus } from 'lucide-react';
import Folder from './folder';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { StarFilledIcon } from '@radix-ui/react-icons';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  folders: AgentFolderDto[];
  showFolderContent: (f: AgentFolderDto) => void;
  showingContentForFolderId: number | undefined;
  openAddFolderModal: (parentFolder: AgentFolderDto | undefined) => void;
  toggleFavourites: () => void;
  showingFavourites: boolean;
  onMouseDown: (f: AgentFolderDto, e: React.MouseEvent) => void;
  onMouseEnter: (f: any) => void;
  onMouseLeave: () => void;
  isDNDActive: boolean;
  getDraggedObject: () => any;
  teamId?: number;
  mode?: 'default' | 'selection';
  onFolderSelected?: (folder: AgentFolderDto, selected: boolean) => void;
  selectedFolders?: AgentFolderDto[];
}

export default function FoldersNavigator({
  folders,
  showFolderContent,
  showingContentForFolderId,
  openAddFolderModal,
  toggleFavourites,
  showingFavourites,
  onMouseDown,
  onMouseEnter,
  onMouseLeave,
  isDNDActive,
  getDraggedObject,
  teamId,
  mode = 'default',
  onFolderSelected,
  selectedFolders,
}: IProps) {
  const { isAdmin } = useUserSession();
  /***********************************/
  /************ FE INIT **************/
  /***********************************/
  const panelRef = useRef<HTMLDivElement>(null);

  const updateSize = () => {
    if (panelRef.current) {
      const s = panelRef.current.getBoundingClientRect();
      if (panelRef.current.parentElement) {
        const t = panelRef.current.parentElement.getBoundingClientRect();
        panelRef.current.style.height = `${t.bottom - s.top - 10}px`;
      }
    }
  };

  useLayoutEffect(() => {
    setTimeout(() => {
      updateSize();
    });
  }, []);

  useEffect(() => {
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  /***********************************/
  /************ RENDER ***************/
  /***********************************/

  return (
    <div
      className={cn('border rounded-lg bg-white overflow-hidden h-full', {
        'select-none cursor-grabbing': isDNDActive,
      })}
    >
      <div className="flex items-center bg-gray-50 p-2 border-b">
        <div className="text-muted-foreground text-sm mr-2 flex-1">Folders</div>

        {mode === 'default' && (
          <>
            <TooltipProvider delayDuration={50}>
              <Tooltip>
                <TooltipTrigger>
                  <div
                    className={cn(
                      'text-muted-foreground text-sm cursor-pointer hover:text-black ',
                    )}
                    onClick={() => {
                      toggleFavourites();
                    }}
                  >
                    <StarFilledIcon
                      className={cn('w-[14px]', {
                        'text-yellow-500': showingFavourites,
                      })}
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  {showingFavourites ? 'Hide favorites' : 'Show favorites'}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {isAdmin && (
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className="text-muted-foreground text-sm cursor-pointer hover:text-black ml-3"
                      onClick={() => {
                        openAddFolderModal(undefined);
                      }}
                    >
                      <FolderPlusIcon size={14} />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">New folder</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </>
        )}
      </div>
      <div ref={panelRef} className="overflow-y-auto">
        {folders.map((f, i) => {
          const isLastChildren = folders.length == i + 1;
          if (!f.agent) {
            return (
              <Folder
                key={f.id}
                folder={f}
                onShowContent={showFolderContent}
                showingContentForFolderId={showingContentForFolderId}
                openAddFolderModal={openAddFolderModal}
                onMouseDown={onMouseDown}
                onMouseEnter={onMouseEnter}
                onMouseLeave={onMouseLeave}
                isDNDActive={isDNDActive}
                getDraggedObject={getDraggedObject}
                isLastChildren={isLastChildren}
                teamId={teamId}
                preventDragAndDrop={showingFavourites}
                mode={mode}
                onFolderSelected={onFolderSelected}
                selectedFolders={selectedFolders}
              />
            );
          }
        })}
      </div>
    </div>
  );
}
