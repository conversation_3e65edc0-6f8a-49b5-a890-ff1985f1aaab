import { AgentFolderDto } from '@/lib/Agent/types';
import { ChevronRight, GripVertical, StarIcon } from 'lucide-react';
import { StarFilledIcon } from '@radix-ui/react-icons';
import { useEffect, useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import DropDownMenu from './dropDownMenu';
import { cn } from '@/lib/utils';
import EditFolderInfoModal from '../../editFolderInfoModal';
import AddBuyersToFolder from '../../addBuyersToFolder';
import { useQueryClient } from '@tanstack/react-query';
import { useAgentsFoldersFavourite } from '@/hooks/useAgentsFolders';
import Image from 'next/image';
import { FoldersService } from '@/lib/Agent';
import { Id, toast } from 'react-toastify';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  folder: AgentFolderDto;
  onShowContent: (f: AgentFolderDto) => void;
  showingContentForFolderId: number | undefined;
  openAddFolderModal: (parentFolder: AgentFolderDto | undefined) => void;
  onMouseDown: (f: AgentFolderDto, e: React.MouseEvent) => void;
  onMouseEnter: (f: any) => void;
  onMouseLeave: () => void;
  isDNDActive: boolean;
  getDraggedObject: () => any;
  isLastChildren: boolean;
  teamId?: number;
  preventDragAndDrop: boolean;
}

export default function Folder({
  folder,
  onShowContent,
  showingContentForFolderId,
  openAddFolderModal,
  onMouseDown,
  onMouseEnter,
  onMouseLeave,
  isDNDActive,
  getDraggedObject,
  isLastChildren,
  teamId,
  preventDragAndDrop,
}: IProps) {
  const { isAdmin } = useUserSession();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [keepFocus, setKeepFocus] = useState<boolean>(false);
  const [isContentShown, setIsContentShown] = useState<boolean>(
    showingContentForFolderId == folder.id,
  );
  const queryClient = useQueryClient();

  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const { data: dbFav, isLoading: isLoadingFavs } =
    useAgentsFoldersFavourite(teamId);
  const [isMovingAgent, setIsMovingAgent] = useState<boolean>(false);

  let hasSubFolders = false;
  if (folder && folder.children) {
    folder.children.map((f) => {
      if (!f.agent) {
        hasSubFolders = true;
      }
    });
  }

  useEffect(() => {
    if (!isLoadingFavs && dbFav) {
      let isFav = false;
      dbFav.forEach((f: AgentFolderDto) => {
        if (f.id == folder.id) {
          isFav = true;
        }
      });
      setIsFavorite(isFav);
    }
  }, [isLoadingFavs, dbFav]);

  useEffect(() => {
    if (showingContentForFolderId == folder.id) {
      setIsContentShown(true);
    } else {
      openIfContentOpenIsChild(folder.children || []);
      setIsContentShown(false);
    }
  }, [showingContentForFolderId]);

  useEffect(() => {
    let _ma = false;
    if (isDNDActive) {
      const mo = getDraggedObject();
      if (mo.agent) {
        _ma = true;
      }
    }
    setIsMovingAgent(_ma);
  }, [isDNDActive]);

  /***********************************/
  /************ ACTIONS **************/
  /***********************************/

  const openIfContentOpenIsChild = (children: AgentFolderDto[]) => {
    children.forEach((f) => {
      if (f.id == showingContentForFolderId) {
        setIsOpen(true);
      } else {
        openIfContentOpenIsChild(f.children || []);
      }
    });
  };

  const toggleOpenFolder = () => {
    setIsOpen(!isOpen);
  };

  const showContent = () => {
    if (hasSubFolders) {
      toggleOpenFolder();
    }
    setIsContentShown(true);
    if (onShowContent) {
      onShowContent(folder);
    }
  };

  const startAddFolder = () => {
    openAddFolderModal(folder);
    showContent();
  };

  /********** EDIT FOLDER INFO **********/

  const [isEditFolderModalOpen, setIsEditFolderModalOpen] = useState(false);

  const startEditFolderInfo = () => {
    setIsEditFolderModalOpen(true);
  };

  const cancelEditFolderInfo = () => {
    setIsEditFolderModalOpen(false);
  };

  const onFolderEdited = (nf: AgentFolderDto) => {
    folder.name = nf.name;
    folder.description = nf.description;
    cancelEditFolderInfo();
  };

  /********** ADD BUYER **********/

  const [isNewBuyerModalOpen, setIsNewBuyerModalOpen] = useState(false);

  const startAddBuyer = () => {
    setIsNewBuyerModalOpen(true);
  };

  const cancelAddBuyer = () => {
    setIsNewBuyerModalOpen(false);
  };

  const addAgents = (agents: AgentFolderDto[]) => {
    if (!folder.children) {
      folder.children = [];
    }
    folder.children.concat(agents);
    queryClient.invalidateQueries({ queryKey: ['agents-folders'] });
    cancelAddBuyer();
    showContent();
  };

  /********** Drag and Drop **********/

  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();

  const startHoverCounter = () => {
    let delay = 1600;
    if (isDNDActive) {
      delay = 800;
    }

    timeoutRef.current = setTimeout(async () => {
      setIsOpen(true);
    }, delay);
  };

  const cancelHoverCounter = () => {
    clearTimeout(timeoutRef.current);
  };

  /************* FAVOURITES *******************/

  const toastId = useRef<Id | null>(null);

  const addToFavourites = async () => {
    try {
      await FoldersService.addFolderToFav(folder.id);
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    queryClient.invalidateQueries({ queryKey: ['agents-folders-favourites'] });
  };

  const removeFromFavourites = async () => {
    try {
      await FoldersService.removeFolderFromFav(folder.id);
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    queryClient.invalidateQueries({ queryKey: ['agents-folders-favourites'] });
  };

  /***********************************/
  /************ RENDER ***************/
  /***********************************/

  return (
    <div className="text-sm">
      {/* SPACER FOR DND */}
      <div
        className={cn('bg-white  w-full h-[6px]', {
          'hover:bg-gray-200': isDNDActive && !isMovingAgent,
        })}
        onMouseEnter={(e) => {
          if (isDNDActive && !isMovingAgent && !preventDragAndDrop) {
            onMouseEnter({ ...folder, isSpacerUp: true });
          }
        }}
        onMouseLeave={(e) => {
          if (isDNDActive && !isMovingAgent && !preventDragAndDrop) {
            onMouseLeave();
          }
        }}
      ></div>

      <div
        className="flex items-center ml-2"
        onMouseEnter={(e) => {
          if (isDNDActive && !preventDragAndDrop) {
            onMouseEnter({ ...folder });
            startHoverCounter();
          }
        }}
        onMouseLeave={() => {
          if (isDNDActive && !preventDragAndDrop) {
            onMouseLeave();
            cancelHoverCounter();
          }
        }}
      >
        <div
          className={cn(
            'flex-1 flex items-center p-2 cursor-pointer rounded-lg group',
            {
              'bg-gray-50': isContentShown,
              'hover:bg-gray-50':
                !isContentShown && !(isDNDActive && preventDragAndDrop),
              'cursor-grabbing': isDNDActive,
            },
          )}
        >
          <motion.div
            animate={{
              rotate: isOpen ? 90 : 0,
            }}
            transition={{
              ease: 'easeOut',
              duration: 0.2,
            }}
            className={cn('text-muted-foreground mr-2', {
              'opacity-0': !hasSubFolders,
            })}
            onClick={() => {
              if (!isDNDActive) {
                if (hasSubFolders) {
                  toggleOpenFolder();
                } else {
                  showContent();
                }
              }
            }}
          >
            <ChevronRight size={16} />
          </motion.div>
          <div className="text-muted-foreground pr-2" onClick={showContent}>
            {isContentShown ? (
              <Image
                src="/images/icons/folderOpen.svg"
                alt="Folder Open"
                width={16}
                height={16}
              />
            ) : (
              <Image
                src="/images/icons/folderClosed.svg"
                alt="Folder Closed"
                width={16}
                height={16}
              />
            )}
          </div>
          <div className="flex-1 mr-3 flex items-center" onClick={showContent}>
            {folder.name}{' '}
            {isFavorite && <StarFilledIcon className="text-yellow-500 ml-2 " />}
          </div>
          {!preventDragAndDrop && isAdmin && (
            <div
              className={cn(
                'invisible group-hover:visible text-muted-foreground mr-2',
                {
                  'cursor-grabbing': isDNDActive,
                  'cursor-grab': !isDNDActive,
                },
              )}
              onMouseDown={(e) => {
                onMouseDown(folder, e);
              }}
            >
              <GripVertical size={16} />
            </div>
          )}

          <div
            className={cn('invisible group-hover:visible flex items-center')}
          >
            <TooltipProvider delayDuration={50}>
              <Tooltip>
                <TooltipTrigger>
                  {isFavorite ? (
                    <StarFilledIcon
                      className="mr-2 text-yellow-500 w-[15px] h-[15px]"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeFromFavourites();
                      }}
                    />
                  ) : (
                    <StarIcon
                      className="mr-2 text-yellow-500 w-[15px] h-[15px]"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        addToFavourites();
                      }}
                    />
                  )}
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  {isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <div className={cn({ 'invisible group-hover:visible': !keepFocus })}>
            <DropDownMenu
              folder={folder}
              startAddFolder={startAddFolder}
              startAddBuyer={startAddBuyer}
              startEditFolderInfo={startEditFolderInfo}
              onOpenChange={(e) => setKeepFocus(e)}
            />
          </div>
        </div>
        {isContentShown ? (
          <div className="w-2 h-[30px] bg-transparent flex items-center">
            <div className="flex-1" />
            <div className="w-[3px] h-full bg-black rounded-tl-lg rounded-bl-lg"></div>
          </div>
        ) : (
          <div className="w-2 h-[10px] bg-transparent"></div>
        )}
      </div>

      <AnimatePresence>
        {folder.children && isOpen && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ duration: 0.2 }}
            className="ml-6 overflow-hidden"
          >
            {folder.children.map((f, i) => {
              const isLast = (folder?.children?.length || 0) == i + 1;
              if (!f.agent) {
                return (
                  <Folder
                    key={f.id}
                    folder={f}
                    onShowContent={onShowContent}
                    showingContentForFolderId={showingContentForFolderId}
                    openAddFolderModal={openAddFolderModal}
                    onMouseDown={onMouseDown}
                    onMouseEnter={onMouseEnter}
                    onMouseLeave={onMouseLeave}
                    isDNDActive={isDNDActive}
                    getDraggedObject={getDraggedObject}
                    isLastChildren={isLast}
                    teamId={teamId}
                    preventDragAndDrop={preventDragAndDrop}
                  />
                );
              }
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {isLastChildren && (
        <div
          className={cn('bg-white  w-full h-[6px]', {
            'hover:bg-gray-200': isDNDActive && !isMovingAgent,
          })}
          onMouseEnter={(e) => {
            if (isDNDActive && !isMovingAgent) {
              onMouseEnter({ ...folder, isSpacerDown: true });
            }
          }}
          onMouseLeave={() => {
            if (isDNDActive && !isMovingAgent) {
              onMouseLeave();
            }
          }}
        ></div>
      )}

      <EditFolderInfoModal
        key={'efim' + folder.id}
        isOpen={isEditFolderModalOpen}
        cancel={cancelEditFolderInfo}
        folder={folder}
        onEdit={onFolderEdited}
      />
      <AddBuyersToFolder
        key={'abtf' + folder.id}
        isOpen={isNewBuyerModalOpen}
        cancel={cancelAddBuyer}
        parentFolder={folder}
        onNewAgents={addAgents}
        teamId={teamId}
      />
    </div>
  );
}
