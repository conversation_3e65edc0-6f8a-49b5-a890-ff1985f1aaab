import DeleteConfirmationModal from '@/components/ConfirmationModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAgentsFoldersFavourite } from '@/hooks/useAgentsFolders';
import useUserSession from '@/hooks/useUserSession';
import { FoldersService } from '@/lib/Agent';
import { AgentFolderDto } from '@/lib/Agent/types';
import { useQueryClient } from '@tanstack/react-query';
import {
  Bookmark,
  BookmarkX,
  Bot,
  FolderPlus,
  Link,
  MoreHorizontal,
  MoreHorizontalIcon,
  MoreVerticalIcon,
  Pencil,
  Trash2,
  Zap,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import { StarFilledIcon, StarIcon } from '@radix-ui/react-icons';
import CallBlitzService from '@/lib/CallBlitz';
import { CallBlitzDto } from '@/lib/CallBlitz/types';
import { useRouter } from 'next/navigation';
import ConfirmationModal from '@/components/ConfirmationModal';
import useRouting from '@/hooks/useRouting';

interface IProps {
  folder: AgentFolderDto;
  startAddFolder: () => void;
  startAddBuyer: () => void;
  startEditFolderInfo: () => void;
  onOpenChange: (isOpen: boolean) => void;
}

export default function DropDownMenu({
  folder,
  startAddFolder,
  startAddBuyer,
  startEditFolderInfo,
  onOpenChange,
}: IProps) {
  const router = useRouter();

  const { isAdmin } = useUserSession();
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [isCallBlitzModalOpen, setIsCallBlitzModalOpen] =
    useState<boolean>(false);
  const { data: dbFav, isLoading: isLoadingFavs } = useAgentsFoldersFavourite();
  const { appendToUrlAndSaveToClipboard } = useRouting();

  /*******************************/
  /********** ADD TO FAV ********/
  /*******************************/

  const toastId = useRef<Id | null>(null);
  const queryClient = useQueryClient();

  const addToFavourites = async () => {
    try {
      await FoldersService.addFolderToFav(folder.id);
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    queryClient.invalidateQueries({ queryKey: ['agents-folders-favourites'] });
  };

  const removeFromFavourites = async () => {
    try {
      await FoldersService.removeFolderFromFav(folder.id);
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    queryClient.invalidateQueries({ queryKey: ['agents-folders-favourites'] });
  };

  const startCallBlitz = async (allNestedAgents: boolean) => {
    console.log('getFolderAgents', folder.id);
    try {
      const callBlitzDto: CallBlitzDto = {
        name: folder.name,
        folderId: folder.id,
        allNestedAgents,
      };
      const response = await CallBlitzService.upsertSession(callBlitzDto);
      console.log(response);
      console.log('response.id', response.id);
      if (response.id) {
        router.push(`/call-blitz/${response.id}`);
      }
    } catch (error) {
      console.error('Error fetching folder agents:', error);
      return [];
    }
  };

  useEffect(() => {
    if (!isLoadingFavs && dbFav) {
      let isFav = false;
      dbFav.forEach((f: AgentFolderDto) => {
        if (f.id == folder.id) {
          isFav = true;
        }
      });
      setIsFavorite(isFav);
    }
  }, [isLoadingFavs, dbFav]);

  /*******************************/
  /********** DELETE ********/
  /*******************************/

  const [openDeleteConfModal, setOpenDeleteConfModal] =
    useState<boolean>(false);

  const startDelete = async () => {
    setOpenDeleteConfModal(true);
  };

  const cancelDelete = async () => {
    setOpenDeleteConfModal(false);
  };

  const doDelete = async () => {
    setOpenDeleteConfModal(false);
    try {
      await FoldersService.deleteFolder(folder.id);
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    queryClient.invalidateQueries({ queryKey: ['agents-folders'] });
  };

  /****************************/

  const shareFolderUrl = () => {
    appendToUrlAndSaveToClipboard([
      { name: 'folder', value: String(folder.id) },
    ]);
    toastId.current = toast.success('Sharable link saved to clipboard');
  };

  /*******************************/
  /********** RENDERING ********/
  /*******************************/

  return (
    <>
      <DropdownMenu onOpenChange={onOpenChange}>
        <DropdownMenuTrigger asChild>
          <MoreHorizontal className="text-muted-foreground" size={16} />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="center">
          {isAdmin && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                startEditFolderInfo();
              }}
            >
              <Pencil className=" mr-2 text-muted-foreground" size={16} />
              <span>Rename</span>
            </DropdownMenuItem>
          )}

          {isAdmin && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                startAddFolder();
              }}
            >
              <FolderPlus className=" mr-2 text-muted-foreground" size={16} />
              <span>New folder</span>
            </DropdownMenuItem>
          )}

          {isAdmin && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                startAddBuyer();
              }}
            >
              <Bot className=" mr-2 text-muted-foreground" size={16} />
              <span>Add buyer bots</span>
            </DropdownMenuItem>
          )}

          {isFavorite ? (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                removeFromFavourites();
              }}
            >
              <StarFilledIcon className=" mr-2 text-yellow-500" />
              <span>Remove from favorites</span>
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                addToFavourites();
              }}
            >
              <StarIcon className=" mr-2 text-muted-foreground" />
              <span>Add to favorites</span>
            </DropdownMenuItem>
          )}

          <DropdownMenuItem
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              // TODO: insert make Call Blitz
              // open dialogue
              setIsCallBlitzModalOpen(true);
              // startCallBlitz();
            }}
          >
            <Zap className=" mr-2 text-muted-foreground" size={16} />
            <span>Start Call Blitz</span>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              shareFolderUrl();
            }}
          >
            <Link className=" mr-2 text-muted-foreground" size={16} />
            <span>Share link</span>
          </DropdownMenuItem>

          {isAdmin && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="cursor-pointer text-red-500"
                onClick={(e) => {
                  e.stopPropagation();
                  startDelete();
                }}
              >
                <Trash2 className=" mr-2" size={16} />
                <span>Delete</span>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <DeleteConfirmationModal
        open={openDeleteConfModal}
        onConfirm={doDelete}
        onCancel={cancelDelete}
        title={'Delete Folder'}
        description={`Do you really want to delete "${folder.name}" and its content?`}
      />
      <ConfirmationModal
        useBtnBlack={true}
        open={isCallBlitzModalOpen}
        onCancel={() => {
          setIsCallBlitzModalOpen(false);
          startCallBlitz(false);
        }}
        onConfirm={() => {
          startCallBlitz(true);
        }}
        title={'Start new call blitz'}
        description="Do you want to include all agents in current subfolders?"
      />
    </>
  );
}
