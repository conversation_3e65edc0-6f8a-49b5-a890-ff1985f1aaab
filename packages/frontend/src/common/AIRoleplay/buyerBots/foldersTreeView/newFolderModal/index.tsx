import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FoldersService } from '@/lib/Agent';
import { AgentFolderDto } from '@/lib/Agent/types';
import { Loader2Icon } from 'lucide-react';
import { useState, useRef } from 'react';
import { Id, toast } from 'react-toastify';

interface IProps {
  parentFolder?: AgentFolderDto;
  isOpen: boolean;
  cancel: () => void;
  onNewFolder: (nf: AgentFolderDto) => void;
  numberOfRoots?: number;
  teamId?: number;
}

export default function NewFolderModal({
  parentFolder,
  isOpen,
  cancel,
  onNewFolder,
  numberOfRoots,
  teamId,
}: IProps) {
  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [canSave, setCanSave] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const toastId = useRef<Id | null>(null);

  const save = async () => {
    setIsSaving(true);

    let ok = false;
    let newFolder: AgentFolderDto | undefined = undefined;
    let position = 0;
    if (parentFolder && parentFolder.children) {
      let maxP = 0;
      for (const f of parentFolder.children) {
        if (f.sort > maxP) {
          maxP = f.sort;
        }
      }
      maxP++;
      position = maxP;
    } else if (numberOfRoots) {
      position = numberOfRoots;
    }
    try {
      newFolder = await FoldersService.createFolder(
        name,
        description,
        parentFolder?.id || undefined,
        position,
        false,
        false,
        teamId,
      );
      ok = true;
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    if (ok && newFolder) {
      onNewFolder(newFolder);
    }
    setIsSaving(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={cancel}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle>New folder</DialogTitle>
        </DialogHeader>
        <div>
          <div>
            <Label>Name</Label>
            <div>
              <Input
                value={name}
                onChange={(e) => {
                  if (e.target.value != '') {
                    setCanSave(true);
                  } else {
                    setCanSave(false);
                  }
                  setName(e.target.value);
                }}
                className=""
                placeholder=""
              />
            </div>
          </div>

          <div className="mt-4">
            <Label>Description</Label>
            <div>
              <Textarea
                value={description}
                onChange={(e) => {
                  setDescription(e.target.value);
                }}
                className=""
                placeholder=""
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          {isSaving ? (
            <Loader2Icon className="animate-spin" />
          ) : (
            <Button disabled={!canSave} onClick={save}>
              Save
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
