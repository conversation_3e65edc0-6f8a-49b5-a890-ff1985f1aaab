import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FoldersService } from '@/lib/Agent';
import { AgentFolderDto } from '@/lib/Agent/types';
import { Loader2Icon } from 'lucide-react';
import { useState, useRef } from 'react';
import { Id, toast } from 'react-toastify';

interface IProps {
  folder: AgentFolderDto;
  isOpen: boolean;
  cancel: () => void;
  onEdit: (nf: AgentFolderDto) => void;
}

export default function EditFolderInfoModal({
  folder,
  isOpen,
  cancel,
  onEdit,
}: IProps) {
  const [name, setName] = useState<string>(folder.name);
  const [description, setDescription] = useState<string>(folder.description);
  const [canSave, setCanSave] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const toastId = useRef<Id | null>(null);

  const save = async () => {
    setIsSaving(true);

    let ok = false;
    let editedFolder: AgentFolderDto | undefined = undefined;
    try {
      editedFolder = await FoldersService.updateFolder(
        folder.id,
        name,
        description,
        folder.parentFolderId || undefined,
        folder.sort,
        folder.isPersonal,
        folder.isForAdminOnly,
      );
      ok = true;
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    if (ok && editedFolder) {
      onEdit(editedFolder);
    }
    setIsSaving(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={cancel}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle>Edit Folder</DialogTitle>
        </DialogHeader>
        <div>
          <div>
            <Label>Name</Label>
            <div>
              <Input
                value={name}
                onChange={(e) => {
                  if (e.target.value != '') {
                    setCanSave(true);
                  } else {
                    setCanSave(false);
                  }
                  setName(e.target.value);
                }}
                className=""
                placeholder=""
              />
            </div>
          </div>

          <div className="mt-4">
            <Label>Description</Label>
            <div>
              <Textarea
                value={description}
                onChange={(e) => {
                  setDescription(e.target.value);
                }}
                className=""
                placeholder=""
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          {isSaving ? (
            <Loader2Icon className="animate-spin" />
          ) : (
            <Button disabled={!canSave} onClick={save}>
              Save
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
