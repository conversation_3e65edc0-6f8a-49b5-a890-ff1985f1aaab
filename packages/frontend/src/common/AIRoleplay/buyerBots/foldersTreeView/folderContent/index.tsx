import {
  AGENT_EMOTIONAL_STATE_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { FoldersService } from '@/lib/Agent';
import {
  AgentCallType,
  AgentDto,
  AgentFolderDto,
  AgentStatus,
} from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { useQueryClient } from '@tanstack/react-query';
import {
  BotIcon,
  BrainIcon,
  ChevronRightIcon,
  FolderIcon,
  FolderPlusIcon,
  GripVertical,
  Link,
  PencilIcon,
  Phone,
  Share2,
  Trash2,
  Zap,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Id, toast, ToastContainer } from 'react-toastify';
import AddBuyersToFolder from '../addBuyersToFolder';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import ConfirmationModal from '@/components/ConfirmationModal';
import { CallBlitzDto } from '@/lib/CallBlitz/types';
import CallBlitzService from '@/lib/CallBlitz';
import useAvatars from '@/hooks/useAvatars';
import AgentAvatar from '@/components/Avatars/Agent';
import { capitalize } from 'lodash';

function getFolderById(
  id: number,
  folders: AgentFolderDto[],
): AgentFolderDto | undefined {
  for (const folder of folders) {
    if (folder.id === id) {
      return folder;
    }
    const result = getFolderById(id, folder?.children || []);
    if (result) {
      return result;
    }
  }
  return undefined;
}

function getFolderPath(
  subfolder: AgentFolderDto,
  allFolders: AgentFolderDto[],
): AgentFolderDto[] {
  const path: AgentFolderDto[] = [];
  let currentFolder: AgentFolderDto | undefined = subfolder;

  // Traverse up the folder structure
  while (currentFolder) {
    path.unshift(currentFolder);
    if (currentFolder.parentFolderId === null) {
      break; // We've reached the root folder
    }
    currentFolder = getFolderById(currentFolder.parentFolderId!, allFolders);
  }

  return path;
}

interface IProps {
  folder?: AgentFolderDto;
  folders: AgentFolderDto[];
  openAddFolderModal: (parentFolder: AgentFolderDto | undefined) => void;
  showFolderContent: (folder: AgentFolderDto) => void;
  onMouseDown: (f: AgentFolderDto, e: React.MouseEvent) => void;
  onMouseEnter: (f: any) => void;
  onMouseLeave: () => void;
  isDNDActive: boolean;
  teamId?: number;
  getDraggedObject: () => any;
}

export default function FolderContent({
  folder,
  folders,
  openAddFolderModal,
  showFolderContent,
  onMouseDown,
  onMouseEnter,
  onMouseLeave,
  isDNDActive,
  teamId,
  getDraggedObject,
}: IProps) {
  const panelRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const router = useRouter();
  const toastId = useRef<Id | null>(null);
  const [children, setChildren] = useState<AgentFolderDto[]>([]);
  const { goToPage } = useRouting();
  const { isAdmin, isObserver } = useUserSession();
  const { appendToUrlAndSaveToClipboard } = useRouting();
  const { data: avatarOptions } = useAvatars();

  /***********************************/
  /************ FE INIT **************/
  /***********************************/

  const updateSize = () => {
    if (panelRef.current) {
      const s = panelRef.current.getBoundingClientRect();

      if (panelRef.current.parentElement) {
        const t = panelRef.current.parentElement.getBoundingClientRect();
        panelRef.current.style.height = `${t.bottom - s.top - 10}px`;
      }
    }
  };

  useLayoutEffect(() => {
    setTimeout(() => {
      updateSize();
    });
  }, []);

  useEffect(() => {
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  useEffect(() => {
    toggleOffAllHighlightGroup();
    setChildren(folder?.children || []);
  }, [folder]);

  const FolderPathWithChevrons = ({ path }: { path: AgentFolderDto[] }) => {
    return (
      <div className="flex items-center text-muted-foreground">
        {path.map((folder, index) => (
          <React.Fragment key={index}>
            <a
              onClick={(e) => {
                openFolderContent(getFolderById(folder!.id, folders)!);
              }}
              className={cn('hover:underline hover:cursor-pointer', {
                'text-black': index === path.length - 1,
              })}
            >
              {folder.name || ''}
            </a>
            {index < path.length - 1 && (
              <ChevronRightIcon size={16} className="mx-1" />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  /***********************************/
  /************ ACTIONS **************/
  /***********************************/

  /********** AGENTS ACTIONS ********/
  const [isDeletingAgent, setIdDeletingAgent] = useState<number | null>(null);

  const removeAgent = async (idAgentFolder: number) => {
    setIdDeletingAgent(idAgentFolder);
    try {
      await FoldersService.deleteFolder(idAgentFolder);
    } catch (e) {
      console.log(e);
      toastId.current = toast.error(
        'There was an error. Please try again later.',
      );
    }

    queryClient.invalidateQueries({ queryKey: ['agents-folders'] });
    setIdDeletingAgent(null);
  };

  const callAgent = (vapiId: string) => {
    router.push('/buyers/' + vapiId);
  };

  /********** FOLDER ACTIONS ********/

  const [openDeleteConfModal, setOpenDeleteConfModal] =
    useState<boolean>(false);
  const [folderToDeleteId, setFolderToDeleteId] = useState<number | null>(null);
  const [deleteFolderName, setDeleteFolderName] = useState<string>();
  const [isDeletingFolder, setIdDeletingFolder] = useState<number | null>(null);

  const startDelete = async (folderId: number, folderName: string) => {
    setFolderToDeleteId(folderId);
    setDeleteFolderName(folderName);
    setOpenDeleteConfModal(true);
  };

  const cancelDelete = async () => {
    setOpenDeleteConfModal(false);
  };

  const doDelete = async () => {
    if (folderToDeleteId) {
      setIdDeletingFolder(folderToDeleteId);
      setOpenDeleteConfModal(false);
      try {
        await FoldersService.deleteFolder(folderToDeleteId);
      } catch (e) {
        console.log(e);
        toastId.current = toast.error(
          'There was an error. Please try again later.',
        );
      }

      queryClient.invalidateQueries({ queryKey: ['agents-folders'] });
      setIdDeletingFolder(null);
    }
  };

  const openFolderContent = (f: AgentFolderDto) => {
    if (showFolderContent) {
      showFolderContent(f);
    }
  };

  const shareFolderUrl = (folderId: number | undefined) => {
    appendToUrlAndSaveToClipboard([
      { name: 'folder', value: String(folderId) },
    ]);
    toastId.current = toast.success('Sharable link saved to clipboard');
  };

  /********** ADD BUYER **********/

  const [isNewBuyerModalOpen, setIsNewBuyerModalOpen] = useState(false);

  const startAddBuyer = () => {
    setIsNewBuyerModalOpen(true);
  };

  const cancelAddBuyer = () => {
    setIsNewBuyerModalOpen(false);
  };

  const addAgents = (agents: AgentFolderDto[]) => {
    queryClient.invalidateQueries({ queryKey: ['agents-folders'] });
    cancelAddBuyer();

    // if (folder) {
    //   setChildren(c => {
    //     return c.concat(agents);
    //   });
    //   console.log('added buyers');
    //   queryClient.invalidateQueries({ queryKey: ["agents-folders"] });
    //   cancelAddBuyer();
    // }
  };

  /*********** CALL BLITZ  *********/

  const [isCallBlitzModalOpen, setIsCallBlitzModalOpen] =
    useState<boolean>(false);

  const startCallBlitz = async (allNestedAgents: boolean) => {
    if (folder) {
      try {
        const callBlitzDto: CallBlitzDto = {
          name: folder.name,
          folderId: folder.id,
          allNestedAgents,
        };
        const response = await CallBlitzService.upsertSession(callBlitzDto);
        console.log(response);
        console.log('response.id', response.id);
        if (response.id) {
          router.push(`/call-blitz/${response.id}`);
        }
      } catch (error) {
        console.error('Error fetching folder agents:', error);
        return [];
      }
    }
  };

  /***********************************/
  /************ RENDER ***************/
  /***********************************/

  //isDNDActive = true;
  const folderSiblings = useRef<any>({});

  let prev = 0;
  for (const f of children) {
    if (prev > 0 && folderSiblings.current[prev]) {
      folderSiblings.current[prev].next = f.id;
    }

    folderSiblings.current[f.id] = {
      previouos: prev,
    };

    prev = f.id;
  }

  const toggleHighlight = (e: HTMLElement) => {
    if (e) {
      if (e.style.backgroundColor == 'rgb(230, 230, 230)') {
        e.style.backgroundColor = 'transparent';
      } else {
        e.style.backgroundColor = 'rgb(230, 230, 230)';
      }
    }
  };

  const toggleHighlightGroup = (isBefore: boolean, folderId: number) => {
    if (isDNDActive) {
      const draggedObj = getDraggedObject();
      if (draggedObj.id != folderId) {
        if (isBefore) {
          const e = document.getElementById('spacer-before-' + folderId);
          if (e) {
            if (
              folderSiblings.current[folderId] &&
              folderSiblings.current[folderId].previouos
            ) {
              const prevFolderId = folderSiblings.current[folderId].previouos;
              if (draggedObj.id != prevFolderId) {
                const e2 = document.getElementById(
                  'spacer-after-' + prevFolderId,
                );
                if (e2) {
                  toggleHighlight(e2);
                }
              }
            }
            toggleHighlight(e);
          }
        } else {
          const e = document.getElementById('spacer-after-' + folderId);
          if (e) {
            if (
              folderSiblings.current[folderId] &&
              folderSiblings.current[folderId].next
            ) {
              const prevFolderId = folderSiblings.current[folderId].next;
              if (draggedObj.id != prevFolderId) {
                const e2 = document.getElementById(
                  'spacer-before-' + prevFolderId,
                );
                if (e2) {
                  toggleHighlight(e2);
                }
              }
            }
            toggleHighlight(e);
          }
        }
      }
    } else {
      //force off
      if (isBefore) {
        const e = document.getElementById('spacer-before-' + folderId);
        if (e) {
          e.style.backgroundColor = 'transparent';
          if (
            folderSiblings.current[folderId] &&
            folderSiblings.current[folderId].previouos
          ) {
            const prevFolderId = folderSiblings.current[folderId].previouos;
            const e2 = document.getElementById('spacer-after-' + prevFolderId);
            if (e2) {
              e2.style.backgroundColor = 'transparent';
            }
          }
        }
      } else {
        const e = document.getElementById('spacer-after-' + folderId);
        if (e) {
          e.style.backgroundColor = 'transparent';
          if (
            folderSiblings.current[folderId] &&
            folderSiblings.current[folderId].next
          ) {
            const prevFolderId = folderSiblings.current[folderId].next;
            const e2 = document.getElementById('spacer-before-' + prevFolderId);
            if (e2) {
              e2.style.backgroundColor = 'transparent';
            }
          }
        }
      }
    }
  };

  const toggleOffAllHighlightGroup = () => {
    children.map((f) => {
      const e = document.getElementById('spacer-before-' + f.id);
      if (e) {
        e.style.backgroundColor = 'transparent';
      }
      const e2 = document.getElementById('spacer-after-' + f.id);
      if (e2) {
        e2.style.backgroundColor = 'transparent';
      }
    });
  };

  //console.log(folder, folders);

  return (
    <div
      className={cn('border rounded-lg bg-white h-full overflow-hidden mb-10', {
        'cursor-grabbing': isDNDActive,
      })}
    >
      <div className="flex items-center bg-gray-50 border-b">
        <div className="text-sm p-2 w-[10px]">&nbsp;</div>
        <Image
          src="/images/icons/folderClosed.svg"
          alt="Folder Closed"
          width={16}
          height={16}
        />
        <div className="text-muted-foreground text-sm p-1 flex-1 ml-2">
          {<FolderPathWithChevrons path={getFolderPath(folder!, folders)} />}
        </div>
        <div className="text-muted-foreground text-sm p-2 w-[40%] pl-16">
          Type
        </div>
        <div className="text-sm p-2 w-[60px]">&nbsp;</div>
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger>
              <div
                className="text-muted-foreground text-sm cursor-pointer hover:text-black mr-3"
                onClick={() => {
                  setIsCallBlitzModalOpen(true);
                }}
              >
                <Zap size={14} />
              </div>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              Start a new call blitz with <br />
              the bots in this folder
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider delayDuration={50}>
          <Tooltip>
            <TooltipTrigger>
              <div
                className="text-muted-foreground text-sm cursor-pointer hover:text-black mr-3"
                onClick={() => {
                  shareFolderUrl(folder?.id);
                }}
              >
                <Link size={14} />
              </div>
            </TooltipTrigger>
            <TooltipContent side="bottom">Share folder</TooltipContent>
          </Tooltip>
        </TooltipProvider>
        {isAdmin && (
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger>
                <div
                  className="text-muted-foreground text-sm cursor-pointer hover:text-black mr-3"
                  onClick={() => {
                    startAddBuyer();
                  }}
                >
                  <BotIcon size={14} />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">Add buyer bots</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
        {isAdmin && (
          <TooltipProvider delayDuration={50}>
            <Tooltip>
              <TooltipTrigger>
                <div
                  className="text-muted-foreground text-sm cursor-pointer hover:text-black mr-4"
                  onClick={() => {
                    openAddFolderModal(undefined);
                  }}
                >
                  <FolderPlusIcon size={14} />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">New folder</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      <div ref={panelRef} className="overflow-y-auto">
        {children.map((f) => {
          if (f.agent) {
            const a: AgentDto = f.agent;
            const Icon =
              CALL_TYPE_TO_ICON?.[a?.callType as keyof typeof CALL_TYPE_TO_ICON]
                ?.Icon;

            return (
              <div key={'f-' + f.id}>
                <div
                  className={cn('bg-white  w-full h-[6px]')}
                  onMouseEnter={(e) => {
                    toggleHighlightGroup(true, f.id);
                    onMouseEnter({ ...f, isSpacerUp: true });
                  }}
                  onMouseLeave={(e) => {
                    toggleHighlightGroup(true, f.id);
                    onMouseLeave();
                  }}
                  id={'spacer-before-' + f.id}
                ></div>
                <div className="group">
                  <div
                    className={cn('flex items-center mx-2 rounded-lg', {
                      'opacity-50': isDeletingAgent == f.id || isDNDActive,
                      'cursor-pointer group-hover:bg-gray-50':
                        isDeletingAgent != f.id && !isDNDActive,
                    })}
                    onClick={() => {
                      if (isDeletingAgent != f.id && !isDNDActive) {
                        callAgent(a.vapiId);
                      }
                    }}
                  >
                    {isAdmin && (
                      <div
                        className={cn(
                          'text-sm px-0 text-muted-foreground invisible w-[12px]',
                          {
                            'hover:cursor-grab group-hover:visible':
                              !isDNDActive,
                            'cursor-grabbing': isDNDActive,
                          },
                        )}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onMouseDown={(e) => {
                          onMouseDown(f, e);
                        }}
                      >
                        <GripVertical size={16} />
                      </div>
                    )}

                    <div className="text-sm px-2 flex-1">
                      <div className="flex items-center">
                        <AgentAvatar className="w-[22px] h-[22px]" agent={a} />
                        <div className="ml-1 mr-2">
                          {a.firstName} {a.lastName}
                        </div>
                        <div className="text-[0.76rem] mr-2 text-muted-foreground">
                          {a.jobTitle}{' '}
                          <span style={{ whiteSpace: 'wrap' }}>
                            {'@  ' + a.companyName}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-sm px-2 w-[40%]">
                      <div className="flex items-center">
                        <div className="ml-1 mr-2 max-w-[120px] overflow-hidden">
                          {a.callType && (
                            <div className="rounded-full flex items-center bg-gray-100 py-1 px-2 text-nowrap text-xs">
                              {Icon && <Icon className="mr-1" size={14} />}
                              {CALL_TYPE_OPTIONS.find(
                                (item) => item.value === a?.callType,
                              )?.label ||
                                (a.callType === 'focus'
                                  ? 'Focus Call'
                                  : a.callType)}
                            </div>
                          )}
                        </div>
                        <div className="ml-1 mr-2 overflow-hidden">
                          {a.emotionalState && (
                            <div className="rounded-full flex items-center bg-green-50 py-1 px-2 text-nowrap text-green-600 border border-green-300 text-xs">
                              <BrainIcon className="mr-1" size={14} />{' '}
                              {AGENT_EMOTIONAL_STATE_OPTIONS.find(
                                (item) => item.value === a?.emotionalState,
                              )?.label ||
                                a.emotionalState ||
                                ''}
                            </div>
                          )}
                        </div>
                        {(isAdmin || isObserver) && (
                          <div className="ml-1 mr-2 overflow-hidden">
                            <div
                              className={`rounded-full flex items-center bg-${a.status === AgentStatus.ACTIVE ? 'green' : 'gray'}-50 py-1 px-2 text-nowrap text-${a.status === AgentStatus.ACTIVE ? 'green' : 'gray'}-600 border border-${a.status === AgentStatus.ACTIVE ? 'green' : 'gray'}-300 text-xs`}
                            >
                              {capitalize(a.status?.toLowerCase())}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    <div
                      className={cn(
                        'text-sm p-2 w-[100px] flex items-center text-muted-foreground invisible',
                        {
                          'group-hover:visible': !isDNDActive,
                          'w-[60px]': !isAdmin,
                        },
                      )}
                    >
                      <TooltipProvider delayDuration={50}>
                        <Tooltip>
                          <TooltipTrigger>
                            <Phone
                              size={16}
                              className="cursor-pointer hover:text-black"
                            />
                          </TooltipTrigger>
                          <TooltipContent side="bottom">Call</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      {isAdmin && (
                        <TooltipProvider delayDuration={50}>
                          <Tooltip>
                            <TooltipTrigger>
                              <PencilIcon
                                size={16}
                                className="ml-3 cursor-pointer hover:text-black"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  goToPage(
                                    a?.callType === AgentCallType.FOCUS
                                      ? `/buyers/${a.vapiId}/edit/focus?callType=${
                                          a.callType || ''
                                        } `
                                      : `/buyers/${a.vapiId}/edit/main?callType=${
                                          a?.callType || ''
                                        } `,
                                  );
                                }}
                              />
                            </TooltipTrigger>
                            <TooltipContent side="bottom">Edit</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                      {isAdmin && (
                        <TooltipProvider delayDuration={50}>
                          <Tooltip>
                            <TooltipTrigger>
                              <Trash2
                                size={16}
                                className="ml-3 cursor-pointer hover:text-black"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  removeAgent(f.id);
                                }}
                              />
                            </TooltipTrigger>
                            <TooltipContent side="bottom">
                              Remove from folder
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </div>
                </div>
                <div
                  className={cn('bg-white  w-full h-[6px] border-b')}
                  onMouseEnter={(e) => {
                    if (isDNDActive) {
                      toggleHighlightGroup(false, f.id);
                      onMouseEnter({ ...f, isSpacerDown: true });
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (isDNDActive) {
                      toggleHighlightGroup(false, f.id);
                      onMouseLeave();
                    }
                  }}
                  id={'spacer-after-' + f.id}
                ></div>
              </div>
            );
          } else {
            return (
              <div key={'f-' + f.id}>
                <div
                  className={cn('bg-white  w-full h-[6px]')}
                  onMouseEnter={(e) => {
                    if (isDNDActive) {
                      toggleHighlightGroup(true, f.id);
                      onMouseEnter({ ...f, isSpacerUp: true });
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (isDNDActive) {
                      toggleHighlightGroup(true, f.id);
                      onMouseLeave();
                    }
                  }}
                  id={'spacer-before-' + f.id}
                ></div>
                <div
                  className={cn('flex items-center py-[5px] mx-2 rounded-lg', {
                    'opacity-50': isDeletingFolder == f.id,
                    'group cursor-pointer hover:bg-gray-50':
                      isDeletingFolder != f.id && !isDNDActive,
                    'hover:bg-gray-50': isDNDActive,
                  })}
                  onClick={() => {
                    if (isDeletingFolder != f.id && !isDNDActive) {
                      openFolderContent(f);
                    }
                  }}
                  onMouseEnter={(e) => {
                    onMouseEnter(f);
                  }}
                  onMouseLeave={onMouseLeave}
                >
                  {isAdmin && (
                    <div
                      className={cn(
                        'text-sm px-0 text-muted-foreground invisible w-[12px]',
                        {
                          'hover:cursor-grab group-hover:visible': !isDNDActive,
                          'cursor-grabbing': isDNDActive,
                        },
                      )}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onMouseDown={(e) => {
                        onMouseDown(f, e);
                      }}
                    >
                      <GripVertical size={16} />
                    </div>
                  )}

                  <div className="text-sm px-2 flex-1">
                    <div className="flex items-center">
                      <Image
                        src="/images/icons/folderClosed.svg"
                        alt="Folder Closed"
                        width={16}
                        height={16}
                        className="mr-2"
                      />
                      {f.name}
                    </div>
                  </div>
                  <div className="text-sm px-2 w-[40%]">{f.description}</div>
                  <div
                    className={cn(
                      'flex items-center text-sm px-2 w-[60px] text-muted-foreground invisible group-hover:visible',
                    )}
                  >
                    <Phone size={16} className="invisible" />
                    {isAdmin && (
                      <TooltipProvider delayDuration={50}>
                        <Tooltip>
                          <TooltipTrigger>
                            <Trash2
                              size={16}
                              className="ml-3 cursor-pointer hover:text-black"
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                startDelete(f.id, f.name);
                              }}
                            />
                          </TooltipTrigger>
                          <TooltipContent side="bottom">
                            Delete folder
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </div>
                <div
                  className={cn('bg-white  w-full h-[6px] border-b')}
                  onMouseEnter={(e) => {
                    if (isDNDActive) {
                      toggleHighlightGroup(false, f.id);
                      onMouseEnter({ ...f, isSpacerDown: true });
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (isDNDActive) {
                      toggleHighlightGroup(false, f.id);
                      onMouseLeave();
                    }
                  }}
                  id={'spacer-after-' + f.id}
                ></div>
              </div>
            );
          }
        })}

        {/********************************/}
        {/********** ACTIONS *************/}
        {/********************************/}
        {isAdmin && (
          <div
            className={cn(
              'flex items-center text-muted-foreground text-sm py-2 border-b pl-1',
            )}
          >
            <Button
              variant="ghost"
              size="sm"
              className="mr-2"
              onClick={() => {
                startAddBuyer();
              }}
            >
              <BotIcon size={16} className="mr-2" />
              Add buyer bots
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="mr-2"
              onClick={() => {
                openAddFolderModal(folder);
              }}
            >
              <FolderPlusIcon size={16} className="mr-2" />
              New folder
            </Button>
          </div>
        )}
      </div>

      <DeleteConfirmationModal
        open={openDeleteConfModal}
        onConfirm={doDelete}
        onCancel={cancelDelete}
        title={'Delete Folder'}
        description={`Do you really want to delete "${deleteFolderName}" and its content?`}
      />
      {folder && isNewBuyerModalOpen && (
        <AddBuyersToFolder
          key={'abtf' + folder.id}
          isOpen={isNewBuyerModalOpen}
          cancel={cancelAddBuyer}
          parentFolder={folder}
          onNewAgents={addAgents}
          teamId={teamId}
        />
      )}

      <ConfirmationModal
        useBtnBlack={true}
        open={isCallBlitzModalOpen}
        onCancel={() => {
          setIsCallBlitzModalOpen(false);
          startCallBlitz(false);
        }}
        onConfirm={() => {
          startCallBlitz(true);
        }}
        title={'Start new call blitz'}
        description="Do you want to include all agents in current subfolders?"
      />
      <ToastContainer />
    </div>
  );
}
