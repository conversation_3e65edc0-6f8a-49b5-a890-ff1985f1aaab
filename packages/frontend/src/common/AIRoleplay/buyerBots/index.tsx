import useTeams, { useUserTeams } from '@/hooks/useTeams';
import useUserSession from '@/hooks/useUserSession';
import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import FoldersTreeView from './foldersTreeView';
import { TeamDto } from '@/lib/User/types';
import { Loader2Icon } from 'lucide-react';
import { AppPermissions } from '@/lib/permissions';

export default function BuyerBots() {
  const { canAccess } = useUserSession();

  const { data: allUserTeams, isLoading: isLoadingUserTeams } = useUserTeams(
    0,
    1000,
    '',
    !canAccess(AppPermissions.VIEW_TEAMS),
  );
  const { data: allAdminTeams, isLoading: isLoadingAdminTeams } = useTeams(
    0,
    1000,
    '',
    canAccess(AppPermissions.VIEW_TEAMS),
  );
  const [selectedTeam, setSelectedTeam] = useState<TeamDto | undefined>(
    undefined,
  );
  const [selectedTeamId, setSelectedTeamId] = useState<string>('all');

  const teams = canAccess(AppPermissions.VIEW_TEAMS)
    ? allAdminTeams
    : allUserTeams;
  const isLoading = canAccess(AppPermissions.VIEW_TEAMS)
    ? isLoadingAdminTeams
    : isLoadingUserTeams;

  return (
    <div>
      <div className="flex items-center">
        <div className="text-base font-semibold">AI Buyer Bots</div>
        {canAccess(AppPermissions.VIEW_TEAMS) && (
          <>
            <div className="w-[250px] ml-2">
              <Select
                onValueChange={(v: string) => {
                  if (v === 'all') {
                    setSelectedTeamId('all');
                    setSelectedTeam(undefined);
                  } else {
                    setSelectedTeamId(v);
                    teams?.map((t) => {
                      if (t.id === parseInt(v)) {
                        setSelectedTeam(t);
                      }
                    });
                  }
                }}
                value={selectedTeamId}
              >
                <SelectTrigger>
                  {selectedTeam ? selectedTeam.name : 'All teams'}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={'all'}>All teams</SelectItem>
                  {teams?.map((v) => {
                    return (
                      <SelectItem key={v.id} value={String(v.id)}>
                        {v.name}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="ml-2">
              {isLoading && <Loader2Icon className="animate-spin" />}
            </div>
          </>
        )}
        <div className="flex-1"></div>
      </div>
      <div className="mt-4">
        <FoldersTreeView filterByTeamId={selectedTeam?.id} />
      </div>
    </div>
  );
}
