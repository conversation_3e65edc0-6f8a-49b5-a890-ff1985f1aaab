import PageHeader from '@/components/PageHeader';
import { But<PERSON> } from '@/components/ui/button';
import useUserSession from '@/hooks/useUserSession';
import { Lock, PlusIcon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import BuyerBots from './buyerBots';
import RoleplayTypes from './roleplayTypes';
import { AppPermissions } from '@/lib/permissions';

export default function AIRoleplay() {
  const { blurAiRoleplayPage, canAccess } = useUserSession();

  if (blurAiRoleplayPage) {
    return (
      <div
        className={
          'bg-[#FBFBFB] relative h-[100vh] block overflow-hidden px-4 py-4'
        }
      >
        <div className="h-[100vh] w-full overflow-hidden flex flex-col">
          <div className="px-6">
            <PageHeader title="AI Roleplays" />
          </div>
          <div className="w-full relative flex-1 mt-4">
            <Image
              src={'/images/ai-roleplay.png'}
              fill
              className="object-contain object-top"
              alt="ai-roleplay"
            />
          </div>
        </div>
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock the power of AI Roleplay
            </div>
            <div className="text-base mt-2 text-muted-foreground max-w-[50%] text-center">
              Complete your onboarding to manage assignments, interact with
              bots, and engage with AI Buyer bots.
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#FBFBFB] h-[100vh] px-6 py-4">
      <PageHeader
        title="AI Roleplays"
        rightComponent={
          canAccess(AppPermissions.CREATE_BOT) ? (
            <Link href="/buyers/create/init">
              <Button variant={'default'} size={'lg'}>
                <PlusIcon className="w-4 h-4 mr-2" /> Create new bot
              </Button>
            </Link>
          ) : null
        }
      />

      {canAccess(AppPermissions.CREATE_BOT) && (
        <div className="mt-8">
          <RoleplayTypes />
        </div>
      )}
      <div className="mt-10">
        <BuyerBots />
      </div>
    </div>
  );
}
