import { Skeleton } from '@/components/ui/skeleton';
import { useAdminAssignmentsByUser } from '@/hooks/useAdminAssignments';
import useAssignmentsByUser from '@/hooks/useAssignmentsByUser';
import useUserSession from '@/hooks/useUserSession';
import {
  AssignmentDto,
  AssignmentManagerViewPerUserDto,
} from '@/lib/Assignment/types';
import { useEffect, useState } from 'react';
import AdminAssignment from './adminAssigment';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import RepAssignment from './repAssigment';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import useTeams from '@/hooks/useTeams';
import { Loader2Icon } from 'lucide-react';
import { AppPermissions } from '@/lib/permissions';

export default function Assignments() {
  const { dbUser, canAccess } = useUserSession();

  const [filterByTeam, setFilterByTeam] = useState<any>({
    id: 'all',
    name: 'All teams',
  });
  const { data: allTeams } = useTeams(
    0,
    1000,
    '',
    canAccess(AppPermissions.VIEW_TEAMS),
  );
  const teams = allTeams || [];
  const [teamFilterId, setTeamFilterId] = useState<number | undefined>();

  const { data: repAssignments, isLoading: isLoadingRepAssigments } =
    useAssignmentsByUser(
      dbUser?.id as number,
      !canAccess(AppPermissions.ADMIN_LEARNING_MODULES),
    );
  const { data: adminAssignments, isLoading: isLoadingAdminAssignments } =
    useAdminAssignmentsByUser(
      teamFilterId,
      canAccess(AppPermissions.ADMIN_LEARNING_MODULES),
    );
  const router = useRouter();

  const [allAssignments, setAllAssignments] = useState<
    AssignmentDto[] | AssignmentManagerViewPerUserDto[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [numberOfAssigments, setNumberOfAssigments] = useState<number>(0);

  useEffect(() => {
    if (!isLoadingRepAssigments || !isLoadingAdminAssignments) {
      if (canAccess(AppPermissions.ADMIN_LEARNING_MODULES)) {
        setNumberOfAssigments(adminAssignments?.length as number);
        setAllAssignments(
          adminAssignments?.slice(0, 4) as AssignmentManagerViewPerUserDto[],
        );
      } else {
        setNumberOfAssigments(repAssignments?.length as number);
        setAllAssignments(repAssignments?.slice(0, 4) as AssignmentDto[]);
      }
      setIsLoading(false);
    }
  }, [
    isLoadingRepAssigments,
    isLoadingAdminAssignments,
    adminAssignments,
    repAssignments,
  ]);

  return (
    <div>
      <div className="flex items-center">
        <div className="text-base font-semibold">
          {canAccess(AppPermissions.ADMIN_LEARNING_MODULES)
            ? 'Team Assignments'
            : 'My Assignments'}
        </div>
        {canAccess(AppPermissions.ADMIN_LEARNING_MODULES) && (
          <>
            <div className="w-[250px] ml-2">
              <Select
                onValueChange={(v: string) => {
                  if (v === 'all') {
                    setTeamFilterId(undefined);
                    setFilterByTeam({
                      id: 'all',
                      name: 'All teams',
                    });
                  } else {
                    setTeamFilterId(parseInt(v));
                    teams?.map((t) => {
                      if (String(t.id) == v) {
                        setFilterByTeam({
                          id: String(t.id),
                          name: t.name,
                        });
                      }
                    });
                  }
                }}
                value={filterByTeam.id}
              >
                <SelectTrigger>
                  <div>{filterByTeam.name}</div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={'all'}>All teams</SelectItem>
                  {teams?.map((v) => {
                    return (
                      <SelectItem key={v.id} value={String(v.id)}>
                        {v.name}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="ml-2">
              {isLoading && <Loader2Icon className="animate-spin" />}
            </div>
          </>
        )}
        <div className="flex-1"></div>
        <div>
          <Button
            variant={'outline'}
            onClick={() => router.push('/coaching/assignments')}
          >
            View all assignments ({numberOfAssigments})
          </Button>
        </div>
      </div>

      {isLoading && (
        <div className="flex items-stretch mt-4 space-x-2">
          <Skeleton className="flex-1 h-[100px]" />
          <Skeleton className="flex-1" />
          <Skeleton className="flex-1" />
        </div>
      )}
      {!isLoading && (!allAssignments || allAssignments.length === 0) && (
        <div className="mb-10 h-[116px] mt-4">No assignments</div>
      )}
      {!isLoading && allAssignments && allAssignments.length > 0 && (
        <div className="grid grid-cols-4 mt-4 space-x-2">
          {allAssignments.map((a, index) => {
            if (canAccess(AppPermissions.ADMIN_LEARNING_MODULES)) {
              return (
                <AdminAssignment
                  key={'a-' + a.id}
                  assignment={a as AssignmentManagerViewPerUserDto}
                />
              );
            }

            return (
              <RepAssignment
                key={'a-' + index}
                assignment={a as AssignmentDto}
              />
            );
          })}
        </div>
      )}
    </div>
  );
}
