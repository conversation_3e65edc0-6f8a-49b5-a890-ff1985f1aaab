import {
  AdminAssignmentDto,
  AssignmentManagerViewPerUserDto,
} from '@/lib/Assignment/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CircleProgress } from '@/components/ui/progress';
import dayjs from 'dayjs';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import { Check } from 'lucide-react';

interface IProps {
  assignment: AssignmentManagerViewPerUserDto;
}

export default function AdminAssignment({ assignment }: IProps) {
  const assigments = assignment.assignments;
  let completedTasks = 0;
  let overdueTasks = 0;
  const numberOfTasks = assigments.length;
  const today = dayjs();
  let overdue = false;
  let progressIndicatorColor = '#73D0A4';
  let allDone = false;

  for (const a of assigments) {
    if (a.numCompletedCalls == a.numAssignedCalls) {
      completedTasks++;
    } else {
      const dueDate = dayjs(a.dueDate);
      if (dueDate.isAfter(today, 'day') || dueDate.isSame(today, 'day')) {
        overdueTasks++;
        overdue = true;
        progressIndicatorColor = '#E90000';
      }
    }
  }

  if (completedTasks == numberOfTasks) {
    allDone = true;
  }

  const completedRatio = Math.round(100 * (completedTasks / numberOfTasks));

  return (
    <div className="flex-1 border rounded-lg p-2 bg-white">
      <div className="flex items-center">
        <div className="relative flex justify-center">
          <CircleProgress
            value={completedRatio}
            className="h-12 w-12"
            indicatorColor={progressIndicatorColor}
          >
            <Avatar className="h-9 w-9 m-2">
              <AvatarImage src={`${assignment.avatar}`} alt="Avatar" />
              <AvatarFallback className="text-muted-foreground">
                {assignment?.firstName?.charAt(0) || ''}
                {assignment?.lastName?.charAt(0) || ''}
              </AvatarFallback>
            </Avatar>
          </CircleProgress>
          {overdue && (
            <div className="text-[10px] bg-[#E90000] text-white absolute mt-[34px] z-[99] font-semibold rounded-full px-[8px] py-[3px] leading-none border-2 border-white">
              DUE
            </div>
          )}
          {allDone && (
            <div className="text-[10px] bg-[#73D0A4] text-white absolute mt-[30px] ml-[30px] z-[99] font-semibold rounded-full  leading-none border-2 border-white">
              <Check size={16} />
            </div>
          )}
        </div>

        <div className="ml-2 flex-1">
          <div className="text-xs ">
            {assignment?.firstName} {assignment?.lastName}
          </div>
          {/* <div className="text-xs text-muted-foreground">{assignment?.agent?.jobTitle}{" @ "} {assignment?.agent?.companyName}</div> */}
        </div>
      </div>
      <Separator className="mt-4" />

      <div className="flex items-center mt-3 mb-1">
        {overdue && (
          <>
            <div className={cn('text-xs text-[#E90000]')}>
              {overdueTasks} task{overdueTasks != 1 && 's'} due
            </div>
            <Separator orientation={'vertical'} className="h-4 mx-2" />
            <div className={cn('text-xs text-muted-foreground')}>
              {numberOfTasks} total
            </div>
          </>
        )}

        {!overdue && (
          <>
            <div className={cn('text-xs')}>
              <span>
                {completedTasks}{' '}
                <span className="text-muted-foreground">
                  /{numberOfTasks} roleplays completed
                </span>
              </span>
            </div>
          </>
        )}
        <div className="flex-1"></div>
        <div className="text-xs text-muted-foreground">{completedRatio}%</div>
      </div>
    </div>
  );
}
