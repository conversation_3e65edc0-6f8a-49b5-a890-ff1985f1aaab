import { AssignmentDto } from '@/lib/Assignment/types';
import { CircleProgress } from '@/components/ui/progress';
import dayjs from 'dayjs';
import { Separator } from '@/components/ui/separator';
import { Check, Phone } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import AgentAvatar from '@/components/Avatars/Agent';

interface IProps {
  assignment: AssignmentDto;
}

export default function RepAssignment({ assignment }: IProps) {
  const completedRatio = Math.round(
    100 * (assignment.numCompletedCalls / assignment.numAssignedCalls),
  );

  const router = useRouter();

  const dueDate = dayjs(assignment.dueDate);
  const today = dayjs();
  const allDone = completedRatio === 100;
  let progressIndicatorColor = '#73D0A4';
  let overdue = false;
  if (!allDone) {
    if (dueDate.isBefore(today, 'day') || dueDate.isSame(today, 'day')) {
      overdue = true;
      progressIndicatorColor = '#E90000';
    }
  }

  const callAgent = () => {
    if (assignment.agent?.vapiId) {
      router.push('/buyers/' + assignment.agent?.vapiId);
    }
  };
  return (
    <div
      className="flex-1 border rounded-lg p-2 cursor-pointer hover:bg-gray-100 group bg-white"
      onClick={callAgent}
    >
      <div className="flex items-center">
        <div className="relative flex justify-center">
          <CircleProgress
            value={completedRatio}
            className="h-12 w-12"
            indicatorColor={progressIndicatorColor}
          >
            <AgentAvatar className="h-9 w-9 m-2" agent={assignment?.agent} />
          </CircleProgress>
          {overdue && (
            <div className="text-[10px] bg-[#E90000] text-white absolute mt-[34px] z-[99] font-semibold rounded-full px-[8px] py-[3px] leading-none border-2 border-white">
              DUE
            </div>
          )}
          {allDone && (
            <div className="text-[10px] bg-[#73D0A4] text-white absolute mt-[30px] ml-[30px] z-[99] font-semibold rounded-full  leading-none border-2 border-white">
              <Check size={16} />
            </div>
          )}
        </div>

        <div className="ml-2 flex-1">
          <div className="text-xs ">
            {assignment?.agent?.firstName} {assignment?.agent?.lastName}
          </div>
          <div className="text-xs text-muted-foreground">
            {assignment?.agent?.jobTitle}
            {' @ '} {assignment?.agent?.companyName}
          </div>
        </div>
        <div className="group-hover:visible invisible pr-2">
          <Phone size={16} className="text-muted-foreground" />
        </div>
      </div>
      <Separator className="mt-4" />

      <div className="flex items-center mt-3 mb-1">
        <div
          className={cn('text-xs', {
            'text-muted-foreground': !overdue,
            'text-[#E90000]': overdue,
          })}
        >
          {overdue ? 'Due today' : `Due ${dueDate.format('MMM D')}`}
        </div>
        <Separator orientation={'vertical'} className="h-4 mx-2" />
        <div className={cn('text-xs')}>
          {!overdue && (
            <span>
              {assignment.numCompletedCalls}{' '}
              <span className="text-muted-foreground">
                /{assignment.numAssignedCalls} call
                {assignment.numAssignedCalls != 1 && 's'}
              </span>
            </span>
          )}
          {overdue && (
            <span>
              <span className="text-muted-foreground">
                {assignment.numAssignedCalls - assignment.numCompletedCalls}{' '}
                call
                {assignment.numAssignedCalls - assignment.numCompletedCalls !=
                  1 && 's'}
              </span>
            </span>
          )}
        </div>
        <div className="flex-1"></div>
        <div className="text-xs text-muted-foreground">{completedRatio}%</div>
      </div>
    </div>
  );
}
