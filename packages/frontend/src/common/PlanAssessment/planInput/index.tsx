import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2Icon, SparklesIcon } from 'lucide-react';
import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';

interface IProps {
  onScoreEnded: (plan: string) => void;
}

export default function PlanInput({ onScoreEnded }: IProps) {
  const [isScoring, setIsScoring] = useState<boolean>(false);
  const [scoringMsg, setScoringMsg] = useState<string>('Scoring...');
  const [currentPlan, setcurrentPlan] =
    useState<string>(`Sales Plan Using RAIN Methodology

Objective: To engage with mid-sized tech companies and provide cloud-based project management software tailored to improving team collaboration and operational efficiency.

1. Building Rapport (R)
Target: The key decision-makers include CTOs, operations managers, and project leads.
Action: Begin with a personalized outreach that demonstrates knowledge of their industry and specific challenges (e.g., managing remote teams). Engage in thoughtful conversations, expressing genuine curiosity about their current project management processes, showing empathy for their struggles, and establishing credibility through relevant case studies. Use social media channels and industry events to connect personally.

2. Uncovering Aspirations and Afflictions (A)
Target: Identify the pain points these companies face, such as lack of visibility in project timelines, communication breakdowns, or inefficiencies in task management.
Action: Through discovery calls, ask probing questions about their biggest project management challenges and goals. For instance, “What’s the biggest challenge your team faces when managing multiple projects?” or “What would seamless collaboration across teams look like for you?” By focusing on both their aspirations (e.g., improved efficiency, smoother workflows) and afflictions (e.g., missed deadlines, communication gaps), create a sense of urgency around solving these issues.

3. Demonstrating Impact (I)
Target: Clearly show how our project management software addresses their specific challenges and aligns with their goals.
Action: Present case studies and data-driven examples of how similar companies have used the software to achieve measurable improvements (e.g., reducing project timelines by 20%, improving team productivity by 30%). During product demos, tailor the experience to their needs, showcasing features that solve their specific afflictions and meet their aspirations. Emphasize the cost savings and performance benefits they’ll achieve by implementing the software.

4. Next Steps (N)
Target: Guide them through the decision-making process with clear, actionable steps.
Action: After presenting the solution, agree on a mutual plan for moving forward. For example, offer a pilot program where they can test the software for 30 days with a dedicated support team. Set a timeline for follow-up meetings to review the pilot results, and outline the purchasing process and implementation plan. Ensure that both parties are aligned on deadlines and next steps to keep the sales process moving forward.`);

  const startScoring = () => {
    setIsScoring(true);

    setTimeout(() => {
      onScoreEnded(currentPlan || '');
      setIsScoring(false);
      setcurrentPlan('');
    }, 2000);
  };
  return (
    <div className="mt-6">
      <div className="mb-2 font-semibold">Your plan:</div>
      <div className="flex items-stretch  h-[100%]">
        <div className="flex-1 mr-4 w-[50%]">
          <Textarea
            className="h-[100%]"
            value={currentPlan}
            onChange={(e) => {
              setcurrentPlan(e.target.value);
            }}
          />
        </div>
        <div className="flex-1 pt-2">
          <div className="font-semibold">Scorecard RAIN Standard:</div>
          <div className="mt-4">
            <div className="mb-2">
              <span className="font-medium">Rapport:</span>{' '}
              <span className="text-muted-foreground">
                Building strong relationships with buyers is important and more
                relevant than ever
              </span>
            </div>
            <div className="mb-2">
              <span className="font-medium">Aspirations and Afflictions:</span>{' '}
              <span className="text-muted-foreground">
                Uncovering buyer needs and desires
              </span>
            </div>
            <div className="mb-2">
              <span className="font-medium">Impact:</span>{' '}
              <span className="text-muted-foreground">
                Identifying and articulating the value of a company&apos;s
                solution
              </span>
            </div>
            <div>
              <span className="font-medium">Next Steps/New Reality:</span>{' '}
              <span className="text-muted-foreground">
                Inspiring buyer action and influencing their agenda
              </span>
            </div>
            <div className="flex items-center  mt-24">
              <div className="flex items-center">
                <div className="mr-2">Scorecard:</div>
                <Select
                  onValueChange={(v: string) => {}}
                  value={'RAIN'}
                  disabled={isScoring}
                >
                  <SelectTrigger>RAIN Standard</SelectTrigger>
                  <SelectContent>
                    <SelectItem value={'RAIN'}>RAIN Standard</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1 mr-4 flex justify-end">
                {isScoring && (
                  <div className="flex items-center">
                    <div>
                      <Loader2Icon
                        size={20}
                        className="animate-spin text-teal-500"
                      />
                    </div>
                    <div className="text-muted-foreground text-xs ml-1">
                      {scoringMsg}
                    </div>
                  </div>
                )}
              </div>
              <div>
                <Button onClick={startScoring} disabled={isScoring}>
                  <SparklesIcon size={16} className="mr-2" />
                  Score plan
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
