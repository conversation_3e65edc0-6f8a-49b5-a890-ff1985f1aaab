import ScorecardSection from '@/common/Calls/AIRoleplay/Summary/tabs/scorecard/scorecardSection';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import { useEffect, useLayoutEffect, useRef } from 'react';

interface IProps {
  plan: string;
  onBack: () => void;
}

export default function ScoringResults({ plan, onBack }: IProps) {
  const scorecard = [
    {
      criteria: [
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Build Trust',
          explanation:
            'Establish a connection by being authentic, showing empathy, and being relatable.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Understand the Buyer’s World',
          explanation:
            'Show genuine curiosity about the buyer’s business, industry, and personal concerns.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Active Listening',
          explanation:
            'Ask insightful questions and listen attentively to uncover their needs and challenges.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Emotional Connection',
          explanation:
            'Create a comfortable environment for dialogue, where the buyer feels understood.',
        },
      ],
      description:
        'Building strong relationships with buyers is important and more relevant than ever',
      sectionTitle: 'Rapport',
      totalCriteriaCount: 4,
      passedCriteriaCount: 0,
    },
    {
      criteria: [
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Identify Aspirations',
          explanation:
            "Understand the buyer's goals, dreams, and what they hope to achieve (long-term success).",
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Uncover Afflictions',
          explanation:
            'Explore the buyer’s pain points, challenges, and what’s preventing them from reaching their aspirations.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Frame Your Solution',
          explanation:
            'Link your product or service as a way to help the buyer overcome afflictions and reach aspirations.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Prioritize Issues',
          explanation:
            'Help the buyer rank the importance of their challenges and goals, emphasizing those that your solution addresses.',
        },
      ],
      description: 'Uncovering buyer needs and desires',
      sectionTitle: 'Aspirations and Afflictions',
      totalCriteriaCount: 4,
      passedCriteriaCount: 0,
    },
    {
      criteria: [
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Quantify Impact',
          explanation:
            'Demonstrate the financial, operational, and emotional impact your solution will have on the buyer.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Present Value',
          explanation:
            'Show how your solution directly relates to the buyer’s desired outcomes and their current issues.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Create a Vision',
          explanation:
            'Help the buyer visualize how their business or life will improve with your solution.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Tailored Solutions',
          explanation:
            "Ensure the solution is customized to the buyer's specific needs, making it feel relevant and actionable.",
        },
      ],
      description:
        "Identifying and articulating the value of a company's solution",
      sectionTitle: 'Impact',
      totalCriteriaCount: 4,
      passedCriteriaCount: 0,
    },
    {
      criteria: [
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Commit to Action',
          explanation:
            'Ensure both you and the buyer are clear on the next steps to move forward in the process.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Mutual Agreement',
          explanation:
            'Gain agreement from the buyer on actions they need to take (e.g., trials, meetings with stakeholders).',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Set Clear Timelines',
          explanation:
            'Establish deadlines for follow-ups, demos, or decision-making.',
        },
        {
          passed: Math.random() < 0.6,
          coaching: '',
          criterion: 'Maintain Momentum',
          explanation:
            'Keep the process moving forward by checking in regularly and providing value throughout the sales journey.',
        },
      ],
      description: 'Inspiring buyer action and influencing their agenda',
      sectionTitle: 'Next Steps',
      totalCriteriaCount: 4,
      passedCriteriaCount: 0,
    },
  ];

  const panelRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  const updateSize = () => {
    if (panelRef.current && gridRef.current) {
      const s = panelRef.current.getBoundingClientRect();
      const w = s.width * 0.7;
      gridRef.current.style.width = `${w}px`;
    }
  };

  useLayoutEffect(() => {
    setTimeout(() => {
      updateSize();
    });
  }, []);

  useEffect(() => {
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  const goToCoaching = () => {};

  return (
    <div className="mt-6 flex items-start" ref={panelRef}>
      <div className="flex-1">
        <div className="font-semibold mb-4">Scorecard RAIN Standard:</div>
        <div className="grid grid-cols-2 gap-4 w-[900px]" ref={gridRef}>
          {scorecard.map((sc, i) => {
            return (
              <ScorecardSection
                key={i}
                delayBy={i}
                section={sc}
                callerId={1}
                callId={''}
                goToCoaching={goToCoaching}
              />
            );
          })}
        </div>
        <div>
          <Button variant={'outline'} onClick={onBack} className="mt-4">
            <ChevronLeft size={16} className="mr-2" /> Back
          </Button>
        </div>
      </div>
      <div className="ml-4 border rounded-lg bg-white p-4 whitespace-pre-wrap">
        {plan}
      </div>
    </div>
  );
}
