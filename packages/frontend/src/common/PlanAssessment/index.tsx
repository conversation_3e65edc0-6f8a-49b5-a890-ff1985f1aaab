import PageHeader from '@/components/PageHeader';
import { useState } from 'react';
import PlanInput from './planInput';
import ScoringResults from './scoringResults';

export default function PlanAssessment() {
  const [pageState, setPageState] = useState<string>('show-input'); //'scoring-results'); //
  const [currentPlan, setcurrentPlan] = useState<string>();

  const onScoreEnded = (plan: string) => {
    setcurrentPlan(plan);
    setPageState('scoring-results');
  };
  return (
    <div className="py-4 px-6 bg-[#FBFBFB] h-[100vh] overflow-auto">
      <PageHeader title="Plan Assessment" />

      {pageState == 'show-input' && <PlanInput onScoreEnded={onScoreEnded} />}

      {pageState == 'scoring-results' && (
        <ScoringResults
          plan={currentPlan || ''}
          onBack={() => {
            setPageState('show-input');
          }}
        />
      )}
    </div>
  );
}
