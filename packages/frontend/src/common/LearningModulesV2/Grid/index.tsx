import { LearningModuleTemplate } from '@/lib/LearningModule/types';
import LearningModuleCardV2 from './Card';
import {
  useArchiveTemplate,
  useCloneTemplate,
  useDeleteTemplate,
  useUnarchiveTemplate,
} from '@/hooks/useLearningModulesV2';
import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface GridViewV2Props {
  modules: LearningModuleTemplate[];
}

export default function GridViewV2({ modules }: GridViewV2Props) {
  const [templateToDelete, setTemplateToDelete] =
    useState<LearningModuleTemplate | null>(null);

  const deleteTemplate = useDeleteTemplate();
  const archiveTemplate = useArchiveTemplate();
  const unarchiveTemplate = useUnarchiveTemplate();
  const cloneTemplate = useCloneTemplate();

  const handleDelete = (template: LearningModuleTemplate) => {
    setTemplateToDelete(template);
  };

  const confirmDelete = () => {
    if (templateToDelete) {
      deleteTemplate.mutate(templateToDelete.id);
      setTemplateToDelete(null);
    }
  };

  const handleArchive = (template: LearningModuleTemplate) => {
    archiveTemplate.mutate(template.id);
  };

  const handleUnarchive = (template: LearningModuleTemplate) => {
    unarchiveTemplate.mutate(template.id);
  };

  const handleClone = (template: LearningModuleTemplate) => {
    cloneTemplate.mutate(template.id);
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {modules.map((module) => (
          <LearningModuleCardV2
            key={module.id}
            module={module}
            onDelete={handleDelete}
            onArchive={handleArchive}
            onUnarchive={handleUnarchive}
            onClone={handleClone}
          />
        ))}
      </div>

      <AlertDialog
        open={!!templateToDelete}
        onOpenChange={() => setTemplateToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Template</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{templateToDelete?.name}
              &quot;? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
