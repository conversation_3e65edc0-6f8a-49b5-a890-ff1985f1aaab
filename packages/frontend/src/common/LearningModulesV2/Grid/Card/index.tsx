import {
  LearningModuleTemplate,
  LearningModuleTemplateStatus,
} from '@/lib/LearningModule/types';
import { BG_COLORS } from '../../Edit/ModuleDetails/EditBackground';
import { CSSProperties, useEffect, useState } from 'react';
import { BadgeCheck } from 'lucide-react';
import { cn } from '@/lib/utils';
import { blurhashAsGradients } from 'blurhash-gradients';
import useRouting from '@/hooks/useRouting';
import LinksManager from '@/lib/linksManager';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  Archive,
  Copy,
  Edit,
  MoreHorizontal,
  Trash2,
  Users,
} from 'lucide-react';

interface IProps {
  module: LearningModuleTemplate;
  blockNavigation?: boolean;
  comingSoon?: boolean;
  onEdit?: (template: LearningModuleTemplate) => void;
  onDelete?: (template: LearningModuleTemplate) => void;
  onArchive?: (template: LearningModuleTemplate) => void;
  onUnarchive?: (template: LearningModuleTemplate) => void;
  onClone?: (template: LearningModuleTemplate) => void;
  onAssign?: (template: LearningModuleTemplate) => void;
}

export default function LearningModuleCardV2({
  module,
  blockNavigation,
  onEdit,
  onDelete,
  onArchive,
  onUnarchive,
  onClone,
  onAssign,
}: IProps) {
  const lm = module;
  const { goToPage } = useRouting();

  /******************************/
  /*********** SPLASH **********/
  /******************************/

  const loadImage = (imageUrl: string) => {
    const image = new Image();
    image.src = imageUrl;

    image.onload = () => {
      setSplashStyle({
        backgroundImage: `url('${imageUrl}')`,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        backgroundSize: 'cover',
        imageRendering: '-webkit-optimize-contrast',
      });
    };
  };

  const [spashStyle, setSplashStyle] = useState<CSSProperties>({
    backgroundColor: BG_COLORS[0],
  });

  useEffect(() => {
    if (lm.splashImageInfo != '') {
      const splash = JSON.parse(lm.splashImageInfo);

      if (splash.type == 'image' && splash.blur_hash) {
        const css = blurhashAsGradients(splash.blur_hash);
        setSplashStyle(css);
        loadImage(splash.url);
      } else if (splash.type == 'image') {
        setSplashStyle({
          backgroundImage: `url('${splash.url}')`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundSize: 'cover',
        });
      } else {
        setSplashStyle({
          backgroundColor: splash.color,
        });
      }
    }
  }, []);

  /******************************/
  /*********** ACTIONS **********/
  /******************************/

  const openDetails = (lmId: string) => {
    if (!blockNavigation) {
      goToPage(LinksManager.learningModules(lmId));
    }
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(lm);
    } else {
      goToPage(LinksManager.learningModulesEdit(lm.id));
    }
  };

  const handleAssign = () => {
    if (onAssign) {
      onAssign(lm);
    } else {
      goToPage(LinksManager.learningModulesEdit(lm.id + '?assign=true'));
    }
  };

  /******************************/
  /*********** RENDER ***********/
  /******************************/

  return (
    <div
      onClick={() => {
        openDetails(lm.id);
      }}
      className={cn('border rounded-lg overflow-hidden w-full bg-white', {
        'hover:bg-gray-50 cursor-pointer': !blockNavigation,
      })}
    >
      <div className="w-full h-[110px]" style={spashStyle}>
        <div className="flex items-center p-3">
          <div className="flex-1" />
          {lm.isCertification && (
            <div className="bg-black/20 rounded-full text-white px-2 py-1 font-medium flex items-center">
              <BadgeCheck size={18} className={cn('mr-1 ')} />
              Certificate
            </div>
          )}
          {!blockNavigation && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 bg-black/20 hover:bg-black/30 text-white"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleAssign}>
                  <Users className="mr-2 h-4 w-4" />
                  Assign
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onClone?.(lm)}>
                  <Copy className="mr-2 h-4 w-4" />
                  Clone
                </DropdownMenuItem>
                {lm.archived ? (
                  <DropdownMenuItem onClick={() => onUnarchive?.(lm)}>
                    <Archive className="mr-2 h-4 w-4" />
                    Unarchive
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem onClick={() => onArchive?.(lm)}>
                    <Archive className="mr-2 h-4 w-4" />
                    Archive
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onClick={() => onDelete?.(lm)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
      <div className="h-[85px] p-3 flex flex-col">
        <div className="flex items-start">
          <div className="flex-1">
            <div
              className={cn('text-sm font-medium flex items-center', {
                'text-blue-500': lm.isCertification,
              })}
            >
              {lm.name}
              {lm.isCertification && (
                <BadgeCheck size={18} className={cn('ml-1 text-blue-500')} />
              )}
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              {lm.description}
            </div>
          </div>
          <div>
            {lm.status == LearningModuleTemplateStatus.DRAFT && (
              <div className="text-xs text-muted-foreground">Draft</div>
            )}
            {lm.archived && (
              <div className="text-xs text-muted-foreground">Archived</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
