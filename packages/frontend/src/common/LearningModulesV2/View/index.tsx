import { Skeleton } from '@/components/ui/skeleton';
import useUserSession from '@/hooks/useUserSession';
import ViewCompetition from './competition';
import ViewLearningModuleV2 from './learningModule';
import { useLearningModuleV2 } from '@/hooks/useLearningModulesV2';

interface IProps {
  id: string;
}

export default function ViewLearningModuleOrCompetitionV2({ id }: IProps) {
  const { isAdmin } = useUserSession();

  const { data: lm, isLoading: isLoadingLearningModule } = useLearningModuleV2(
    id,
    isAdmin,
  );

  /****************************************/
  /*************** RENDER *****************/
  /****************************************/

  const isLoading = isLoadingLearningModule; //true; //

  if (isLoading || !lm) {
    return (
      <div className="bg-[#FBFBFB] px-6 py-4 h-[100vh] flex flex-col ">
        <div>
          <Skeleton className="w-full h-20" />
        </div>
        <div className="flex items-stretch pt-3 flex-1">
          <div className="w-[35%] mr-6">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>

          <div className="flex-1">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>
        </div>
      </div>
    );
  }

  if (lm.isCompetition) {
    return <ViewCompetition id={id} />;
  } else {
    return <ViewLearningModuleV2 id={id} />;
  }
}
