import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  useLayoutEffect,
} from 'react';
import {
  Dialog,
  DialogPortal,
  DialogOverlay,
} from '@/components/ui/Hyperbound/dialog-for-calls';
import { Trophy, X } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import CallBlitz from '@/common/CallBlitz/blitz';

interface ICallBlitzPanelProps {
  sessionId: number;
  sessionName?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function CallBlitzPanel({
  open,
  sessionId,
  sessionName,
  onOpenChange,
}: ICallBlitzPanelProps) {
  /***********************************/
  /************* FE EVENTS ***********/
  /***********************************/

  const [exitingPnl, setExitingPnl] = useState<boolean>(false);

  const closeDialog = () => {
    setExitingPnl(true);
    setTimeout(() => {
      onOpenChange(false);
    }, 160);
  };

  //ESC key to close dialog
  const escFunction = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' || event.key === 'Esc' || event.keyCode === 27) {
      closeDialog();
    }
  }, []);

  // MOUSE SCROLL - with position:fixed container, mouse wheel wont work, we need to use JS:

  useEffect(() => {
    document.addEventListener('keydown', escFunction, false);

    return () => {
      document.removeEventListener('keydown', escFunction, false);
    };
  }, [escFunction]);

  const scrollableContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener('wheel', scrollContent);

    return () => {
      window.removeEventListener('wheel', scrollContent);
    };
  }, []);

  const scrollContent = (e: WheelEvent) => {
    if (scrollableContainer.current) {
      scrollableContainer.current.scrollTop += e.deltaY;
    }
  };

  useLayoutEffect(() => {
    function updateSize() {
      if (scrollableContainer.current) {
        const s = scrollableContainer.current.getBoundingClientRect();
        scrollableContainer.current.style.height =
          window.innerHeight - s.y - 1 + 'px';
      }
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  /***********************************/
  /************ RENDERING ************/
  /***********************************/

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogPortal>
        <DialogOverlay />
        <div className="fixed top-0 left-0 right-0 bottom-0 w-full h-full z-50 overflow-hidden">
          <div className="flex items-center">
            <div className="flex-grow"></div>
            <div className="text-sm text-white flex items-center">
              <Trophy size={18} className="mr-2" />
              Call Blitz: {sessionName || 'Training Session'}
            </div>
            <div className="flex-grow"></div>
            <div
              className="p-2 cursor-pointer text-white"
              onClick={closeDialog}
            >
              <X size={20} />
            </div>
          </div>
          <AnimatePresence>
            {!exitingPnl && (
              <motion.div
                initial={{ opacity: 0, y: 700 }}
                exit={{ opacity: 0, y: 700 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, ease: 'easeOut' }}
                className="flex flex-col h-screen bg-white"
                onAnimationComplete={() => {
                  if (scrollableContainer.current) {
                    const s =
                      scrollableContainer.current.getBoundingClientRect();
                    scrollableContainer.current.style.height =
                      window.innerHeight - s.y - 1 + 'px';
                  }

                  // Animation completed
                }}
                ref={scrollableContainer}
              >
                <CallBlitz
                  sessionId={sessionId}
                  onClose={closeDialog}
                  hideViewCallHistory
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </DialogPortal>
    </Dialog>
  );
}

export default CallBlitzPanel;
