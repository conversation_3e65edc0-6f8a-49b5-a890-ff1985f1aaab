import dayjs from 'dayjs';
import { AlarmClock } from 'lucide-react';
import { useEffect, useRef, useState, memo } from 'react';

interface IProps {
  startDate: Date | undefined;
  dueDate: Date | undefined;
}

const Countdown = ({ startDate, dueDate }: IProps) => {
  useEffect(() => {
    manageCountdown();

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const [isStarted, setIsStarted] = useState(false);
  const [isEnded, setIsEnded] = useState(false);
  const [countdown, setCountdown] = useState<string>('');

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const manageCountdown = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    const _isStarted =
      dayjs(startDate).isBefore(dayjs(), 'day') ||
      dayjs(startDate).isSame(dayjs(), 'day');

    const _isEnded =
      dayjs(dueDate).add(1, 'day').isBefore(dayjs(), 'day') ||
      dayjs(dueDate).add(1, 'day').isSame(dayjs(), 'day');

    setIsStarted(_isStarted);
    setIsEnded(_isEnded);

    if (!_isEnded) {
      if (!_isStarted) {
        startCountdown(startDate);
      } else {
        startCountdown(dueDate);
      }
    }
  };

  const startCountdown = (date: Date | undefined) => {
    if (date) {
      const day = 86400;
      const hour = 3600;
      const minute = 60;

      const now = dayjs();
      const endTime = dayjs(date);

      const diffInSeconds = endTime.diff(now, 'second');

      if (diffInSeconds < 0) {
        setIsEnded(true);
        return;
      }

      const days = Math.floor(diffInSeconds / day);
      const hours = Math.floor((diffInSeconds - days * day) / hour);
      const minutes = Math.floor(
        (diffInSeconds - days * day - hours * hour) / minute,
      );
      const seconds =
        diffInSeconds - days * day - hours * hour - minutes * minute;

      setCountdown(
        `${days < 10 ? '0' : ''}${days}d ${hours < 10 ? '0' : ''}${hours}h ${minutes < 10 ? '0' : ''}${minutes}m ${seconds < 10 ? '0' : ''}${seconds}s`,
      );

      timeoutRef.current = setTimeout(async () => {
        startCountdown(date);
      }, 1000);
    }
  };

  if (isEnded) {
    return;
  }

  return (
    <div className="flex flex-col items-center">
      <div>
        <AlarmClock size={18} />
      </div>
      {!isStarted && !isEnded && (
        <div className="text-xs text-muted-foreground mt-3">Starts in</div>
      )}

      {isStarted && !isEnded && (
        <div className="text-xs text-muted-foreground mt-3">Ends in</div>
      )}

      <div>{countdown}</div>
    </div>
  );
};

export default memo(Countdown);
