/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  AiRoleplayTask,
  LearningModuleTaskType,
  Task,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Phone } from 'lucide-react';
import { AgentDto } from '@/lib/Agent/types';
import AgentAvatar from '@/components/Avatars/Agent';
import { UserDto } from '@/lib/User/types';
interface IProps {
  task: Task;
  onStartCall?: (agent: AgentDto) => void;
  isPreview?: boolean;
  currentAssignee?: UserDto;
  learningModuleId?: string;
}

export default function AiRoleplayTaskViewV2({
  task,
  onStartCall,
  isPreview,
}: IProps) {
  if (task.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
    const t: AiRoleplayTask = task.info;
    console.log(t?.agent);

    const stats: string[] = [];
    for (const s of t.stats || []) {
      stats.push(String(s.name));
    }

    const startCall = () => {
      if (t.agent && onStartCall) {
        onStartCall(t.agent);
      }
    };

    return (
      <div className={cn('rounded-lg', { 'border py-3 mb-3': !isPreview })}>
        <div className="flex items-center mb-4 px-3">
          <div className="border rounded-full flex items-center ">
            <AgentAvatar
              className="w-[26px] h-[26px] border-white border-2"
              agent={t?.agent}
            />
            <div className="capitalize mx-1 font-medium mr-2">
              {t.agent?.firstName || ''} {t.agent?.lastName || ''}
            </div>
          </div>
          <div className="flex-1" />
          {onStartCall && (
            <div>
              <Button size="sm" variant="outline" onClick={startCall}>
                <Phone size={16} className="mr-2" />
                Make a call
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-end">
          <div className="flex-1">
            {t.minNumberOfAttempts && t.minNumberOfAttempts > 0 && (
              <div className="flex items-center text-xs mt-2 px-3">
                <div className="text-muted-foreground">
                  Required successful attempts:
                </div>
                <div className="ml-2">
                  {t.minNumberOfAttempts == 0
                    ? 'unlimited'
                    : t.minNumberOfAttempts}
                </div>
              </div>
            )}

            {t.minScorecardScore != undefined && t.minScorecardScore > 0 && (
              <div className="flex items-center text-xs mt-2 px-3">
                <div className="text-muted-foreground ">
                  Min scorecard score:
                </div>
                <div className="ml-2">
                  {t.minScorecardScore == 0 ? 'Any' : t.minScorecardScore}
                </div>
              </div>
            )}

            {t.criterions && t.criterions.length > 0 && (
              <div className="flex items-start text-xs mt-2 px-3">
                <div className="text-muted-foreground text-nowrap">
                  Pass criteria:
                </div>
                <div className="ml-2 max-w-[600px] pr-4">
                  {t.criterions.map((c: any, i: number) => {
                    return (
                      <span key={'cr' + i}>
                        {i > 0 && ', '}
                        {c.criterion}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {!isPreview && (
            <div className="flex items-center px-3">
              <div className="text-muted-foreground text-xs">AI Buyer Bot</div>
            </div>
          )}
        </div>
      </div>
    );
  }
}
