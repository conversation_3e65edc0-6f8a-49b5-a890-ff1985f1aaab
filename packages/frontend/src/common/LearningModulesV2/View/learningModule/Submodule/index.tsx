import { LearningModuleTemplateSubModule } from '@/lib/LearningModule/types';
import { AgentDto } from '@/lib/Agent/types';
import SubModuleDetailsPanelV2 from './DetailsPanel';
import LearningModule from '@/lib/LearningModule/types';

interface IProps {
  sm: LearningModuleTemplateSubModule;
  onStartCall?: (agent: AgentDto) => void;
  learningModuleId?: string;
  lmWithStats?: LearningModule;
}

export default function SubModuleDetailsV2({
  sm,
  onStartCall,
  learningModuleId,
  lmWithStats,
}: IProps) {
  return (
    <div className="w-full border bg-white rounded-lg p-3 pb-0">
      <div className="flex items-start mb-4 flex-col">
        <div className="font-semibold flex-1 mb-4">{sm.name}</div>
        <div className="w-full">
          <SubModuleDetailsPanelV2
            sm={sm}
            onStartCall={onStartCall}
            learningModuleId={learningModuleId}
            lmWithStats={lmWithStats}
          />
        </div>
      </div>
    </div>
  );
}
