import {
  LearningModuleTemplateSubModule,
  Task,
  TaskStats,
} from '@/lib/LearningModule/types';
import LearningModule from '@/lib/LearningModule/types';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';
import useUserSession from '@/hooks/useUserSession';
import { useEffect, useState } from 'react';
import TaskViewV2 from '../TaskView';

interface IProps {
  sm: LearningModuleTemplateSubModule;
  onStartCall?: (agent: AgentDto) => void;
  learningModuleId?: string;
  lmWithStats?: LearningModule;
}

export default function SubModuleDetailsPanelV2({
  sm,
  onStartCall,
  learningModuleId,
  lmWithStats,
}: IProps) {
  const { isAdmin, dbUser } = useUserSession();

  const [shownAssignee, setShownAssignee] = useState<UserDto>({
    id: 0,
  } as UserDto);

  useEffect(() => {
    if (!isAdmin) {
      if (dbUser) {
        setShownAssignee(dbUser);
      }
    }
  }, [isAdmin]);

  const statsPerTask: { [taskId: string]: TaskStats } = {};
  if (lmWithStats && shownAssignee?.id) {
    for (const smWithStats of lmWithStats.subModules) {
      const taskIdMatch = sm.tasks.some((task) =>
        smWithStats.tasks.some((taskWithStats) => taskWithStats.id === task.id),
      );

      if (taskIdMatch) {
        for (const assigneeStats of smWithStats.assigneesStats) {
          if (assigneeStats.userId === shownAssignee.id) {
            for (const taskStats of assigneeStats.tasks) {
              statsPerTask[taskStats.taskId] = taskStats;
            }
            break;
          }
        }
        break;
      }
    }
  }

  return (
    <div>
      {learningModuleId &&
        sm?.tasks &&
        sm?.tasks?.length > 0 &&
        sm?.tasks?.map((t: Task) => {
          const userStats = statsPerTask[t.id];
          return (
            <TaskViewV2
              task={t}
              key={t.id}
              onStartCall={onStartCall}
              currentAssignee={shownAssignee}
              learningModuleId={learningModuleId}
              userStats={userStats}
            />
          );
        })}
    </div>
  );
}
