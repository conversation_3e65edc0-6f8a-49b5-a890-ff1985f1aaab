'use client';

import { Badge } from '@/components/ui/badge';
import { Trophy } from 'lucide-react';
import {
  CallBlitzTask,
  LearningModuleTaskType,
  TaskStats,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';

interface IProps {
  task: {
    id: string;
    type: LearningModuleTaskType.CALL_BLITZ;
    info: CallBlitzTask;
  };
  userStats?: TaskStats;
  isPreview?: boolean;
  learningModuleId?: string;
}

export default function CallBlitzTaskView({
  task,
  userStats,
  isPreview,
}: IProps) {
  if (task.type == LearningModuleTaskType.CALL_BLITZ) {
    const t: CallBlitzTask = task.info;

    const userHasPassed = userStats?.passed || false;
    const userAttempts = userStats?.attempts || [];
    const lastAttempt = userAttempts[userAttempts.length - 1];

    return (
      <>
        <div className={cn('rounded-lg', { 'border py-3 mb-3': !isPreview })}>
          <div className="flex items-center mb-4 px-3">
            <div className="border rounded-full flex items-center p-0.5">
              <Trophy size={20} className="text-blue-600 mx-1" />
              <div className="capitalize mx-1 font-medium mr-2">
                {t.sessionName || 'Call Blitz'}
              </div>
            </div>
            <div className="flex-1" />
          </div>

          <div className="flex items-end">
            <div className="flex-1">
              {t.minNumberOfCalls !== undefined && t.minNumberOfCalls > 0 && (
                <div className="flex items-center text-xs mt-2 px-3">
                  <div className="text-muted-foreground">
                    Min calls required:
                  </div>
                  <div className="ml-2">{t.minNumberOfCalls}</div>
                </div>
              )}
              {t.maxNumberOfCalls !== undefined && t.maxNumberOfCalls > 0 && (
                <div className="flex items-center text-xs mt-2 px-3">
                  <div className="text-muted-foreground">
                    Max calls allowed:
                  </div>
                  <div className="ml-2">{t.maxNumberOfCalls}</div>
                </div>
              )}
              {/* {t.minScorecardScore !== undefined && t.minScorecardScore > 0 && (
                <div className="flex items-center text-xs mt-2 px-3">
                  <div className="text-muted-foreground">
                    Min average score:
                  </div>
                  <div className="ml-2">{t.minScorecardScore}</div>
                </div>
              )} */}
            </div>
            {!isPreview && (
              <div className="flex items-center px-3">
                <div className="text-muted-foreground text-xs">Call Blitz</div>
              </div>
            )}
          </div>

          {!isPreview && userStats && (
            <div className="space-y-2 mt-4 px-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Status:</span>
                <Badge variant={userHasPassed ? 'default' : 'secondary'}>
                  {userHasPassed ? 'Completed' : 'In Progress'}
                </Badge>
              </div>
              {userAttempts.length > 0 && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Attempts:</span>{' '}
                  {userAttempts.length}
                </div>
              )}
              {lastAttempt && (
                <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                  <div className="font-medium">Last Result:</div>
                  <div className="whitespace-pre-wrap">
                    {lastAttempt.results}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </>
    );
  }
  return null;
}
