import AiRoleplayTaskViewV2 from './AiRoleplayTaskView';
import CallBlitzTaskView from './CallBlitzTaskView';
import {
  LearningModuleTaskType,
  Task,
  TaskStats,
} from '@/lib/LearningModule/types';
import { AgentDto } from '@/lib/Agent/types';
import { UserDto } from '@/lib/User/types';

interface IProps {
  task: Task;
  onStartCall?: (agent: AgentDto) => void;
  isPreview?: boolean;
  currentAssignee?: UserDto;
  learningModuleId?: string;
  userStats?: TaskStats;
}

export default function TaskViewV2({
  task,
  onStartCall,
  isPreview,
  currentAssignee,
  learningModuleId,
  userStats,
}: IProps) {
  if (task.type == LearningModuleTaskType.AI_BUYER_ROLEPLAY) {
    return (
      <AiRoleplayTaskViewV2
        task={task}
        onStartCall={onStartCall}
        isPreview={isPreview}
        currentAssignee={currentAssignee}
        learningModuleId={learningModuleId}
      />
    );
  }

  if (task.type == LearningModuleTaskType.CALL_BLITZ) {
    return (
      <CallBlitzTaskView
        task={task}
        isPreview={isPreview}
        learningModuleId={learningModuleId}
        userStats={userStats}
      />
    );
  }

  return null;
}
