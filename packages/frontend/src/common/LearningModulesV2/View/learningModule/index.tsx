import PageHeader from '@/components/PageHeader';
import { Skeleton } from '@/components/ui/skeleton';
import LinksManager from '@/lib/linksManager';
import { useEffect, useState } from 'react';
import { LearningModuleTemplateSubModule } from '@/lib/LearningModule/types';
import { AgentDto } from '@/lib/Agent/types';
import CallPanel from '../callPanel';
import { Button } from '@/components/ui/button';
import useUserSession from '@/hooks/useUserSession';
import useRouting from '@/hooks/useRouting';
import {
  FilePenLine,
  Loader2,
  MoreHorizontalIcon,
  Plus,
  Share2Icon,
  Users,
} from 'lucide-react';
import { AppPermissions } from '@/lib/permissions';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import LearningModuleService from '@/lib/LearningModule';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ModuleDetailsV2 from './ModuleDetails';
import SubModuleDetailsV2 from './Submodule';
import { useLearningModuleV2 } from '@/hooks/useLearningModulesV2';
import { useLearningModule } from '@/hooks/useLearningModules';
import AssignmentsTable from '../../Edit/Assignments';
import ManualAssignmentModal from '../../Edit/ManualAssignmentModal';
import AssignmentRulesPanel from '../../Edit/Assignments/components/AssignmentRulesPanel';
import { UserDto } from '@/lib/User/types';
import { useQueryClient } from '@tanstack/react-query';
import { useCommonQueryKeys } from '@/hooks/useCommonQueryKeys';

interface IProps {
  id: string;
}

export default function ViewLearningModuleV2({ id }: IProps) {
  const { isAdmin, canAccess, isScormEnabled } = useUserSession();

  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  const { goToPage } = useRouting();

  const { data: lm, isLoading: isLoadingLearningModule } =
    useLearningModuleV2(id);

  // Get learning module with user stats for displaying attempts
  const { data: lmWithStats } = useLearningModule(id, isAdmin);

  const [openSubModule, setOpenSubModule] = useState<
    LearningModuleTemplateSubModule | undefined
  >(lm?.subModules[0] || undefined);
  const [openOverallProgress, setOpenOverallProgress] =
    useState<boolean>(false);

  useEffect(() => {
    if (lm) {
      if (lm.subModules && lm.subModules.length > 0) {
        setOpenSubModule(lm.subModules[0]);
      }
    }
  }, [lm]);

  /****************************************/
  /*************** ACTIONS ****************/
  /****************************************/

  const [isUserOnCall, setIsUserOnCall] = useState<boolean>(false);
  const [agentOnCall, setAgentOnCall] = useState<AgentDto>();
  const [isExportingSCORM, setIsExportingSCORM] = useState(false);
  const [currentTab, setCurrentTab] = useState<'details' | 'assignments'>(
    'details',
  );
  const [isCreateNewDropdownOpen, setIsCreateNewDropdownOpen] =
    useState<boolean>(false);
  const [showManualAssignmentModal, setShowManualAssignmentModal] =
    useState<boolean>(false);
  const [manuallyAssignedUsers, setManuallyAssignedUsers] = useState<UserDto[]>(
    [],
  );
  const [manuallyAssignedDueDate, setManuallyAssignedDueDate] = useState<Date>(
    new Date(),
  );
  const [isAssigning, setIsAssigning] = useState<boolean>(false);
  const [openAssignmentRulesPanel, setOpenAssignmentRulesPanel] =
    useState<boolean>(false);

  const closeCallPanel = (o: boolean) => {
    setIsUserOnCall(o);
    setAgentOnCall(undefined);
    console.log('REFRESH');
  };

  const editModule = () => {
    goToPage(LinksManager.learningModulesEdit(id));
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const exportSCORM = async (e: React.MouseEvent<any>) => {
    e.stopPropagation();
    e.preventDefault();
    try {
      setIsExportingSCORM(true);
      await LearningModuleService.exportSCORMPackage(id);
    } catch (error) {
      console.error('Failed to export SCORM package:', error);
    } finally {
      setIsExportingSCORM(false);
    }
  };

  const onSaveManualAssignment = async () => {
    try {
      setIsAssigning(true);
      await LearningModuleService.assignTemplate(
        id,
        manuallyAssignedUsers.map((u) => u.id),
        manuallyAssignedDueDate,
      );
      queryClient.invalidateQueries({
        queryKey: ['learning-module-assignments', id, ...commonQueryKeys],
      });
    } catch (error) {
      console.error('Error saving manual assignment:', error);
    } finally {
      setIsAssigning(false);
      setShowManualAssignmentModal(false);
    }
  };

  /****************************************/
  /*************** RENDER *****************/
  /****************************************/

  const isLoading = isLoadingLearningModule; //true; //

  if (isLoading || !lm) {
    return (
      <div className="bg-[#FBFBFB] px-6 py-4 h-[100vh] flex flex-col ">
        <div>
          <Skeleton className="w-full h-24 mt-2" />
        </div>
        <div className="flex items-stretch pt-1 flex-1">
          <div className="w-[35%] mr-6">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>

          <div className="flex-1">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#FBFBFB] py-4 min-h-[100vh]">
      <PageHeader
        className="px-6 border-b border-gray-200"
        title={[
          { title: 'Learning Modules', href: LinksManager.learningModules() },
          { title: lm?.name || '' },
        ]}
        rightComponent={
          <div className="flex items-center">
            {isAdmin && (
              <Button variant={'outline'} onClick={editModule}>
                Edit
              </Button>
            )}
            {canAccess(AppPermissions.EXPORT_BOTS) && isScormEnabled && (
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Button variant={'outline'} className="ml-2">
                    <MoreHorizontalIcon className="w-4 h-4 mr-2" />
                    More
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={exportSCORM}
                    disabled={isExportingSCORM}
                  >
                    {isExportingSCORM ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Share2Icon className="w-4 h-4 mr-2" />
                    )}
                    <span>
                      {isExportingSCORM
                        ? 'Exporting SCORM package...'
                        : 'Export SCORM package'}
                    </span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        }
      />

      <div className="px-6">
        <Tabs
          className="mt-4 flex flex-col"
          defaultValue="details"
          value={currentTab}
          onValueChange={(value) =>
            setCurrentTab(value as 'details' | 'assignments')
          }
        >
          <div className="flex flex-row flex-1">
            <TabsList className="flex flex-row items-center justify-start">
              <TabsTrigger
                value="details"
                onClick={() => setCurrentTab('details')}
              >
                <FilePenLine size={16} className="mr-1" />
                Details
              </TabsTrigger>
              <TabsTrigger
                value="assignments"
                onClick={() => setCurrentTab('assignments')}
              >
                <Users size={16} className="mr-1" />
                Assignments
              </TabsTrigger>
            </TabsList>
            {currentTab === 'assignments' && (
              <div className="flex flex-1 items-center justify-end">
                <DropdownMenu
                  open={isCreateNewDropdownOpen}
                  onOpenChange={setIsCreateNewDropdownOpen}
                >
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant={'outline'}
                      onClick={() => setIsCreateNewDropdownOpen(true)}
                    >
                      <Plus size={16} className="mr-2" />
                      Create new
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-64">
                    <DropdownMenuItem
                      onClick={() => setShowManualAssignmentModal(true)}
                      className="flex flex-col items-start cursor-pointer"
                    >
                      <div className="font-medium">
                        Create a manual assignment
                      </div>
                      <div className="text-muted-foreground mt-1">
                        Configure a team and a due date manually
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setOpenAssignmentRulesPanel(true)}
                      className="flex flex-col items-start cursor-pointer"
                    >
                      <div className="font-medium">Create a rule</div>
                      <div className="text-muted-foreground mt-1">
                        Configure automatic rules that will be assigning this
                        learning module
                      </div>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>

          <TabsContent value="details">
            <div className="flex items-start mt-6 space-x-3">
              <ModuleDetailsV2
                lm={lm}
                selectSubmodule={setOpenSubModule}
                openSubModuleId={openSubModule?.id}
                openOverallProgress={setOpenOverallProgress}
              />

              <div className="flex-1">
                {openSubModule && !openOverallProgress && (
                  <SubModuleDetailsV2
                    sm={openSubModule}
                    learningModuleId={id}
                    lmWithStats={lmWithStats}
                  />
                )}

                {/* CALL PANEL */}
                {agentOnCall && (
                  <CallPanel
                    key={'call-panel-' + agentOnCall?.id}
                    open={isUserOnCall}
                    agent={agentOnCall}
                    onOpenChange={closeCallPanel}
                  />
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {currentTab === 'assignments' && (
          <div className="mt-6">
            <AssignmentsTable templateId={lm.id} edit={false} />
          </div>
        )}
      </div>

      <ManualAssignmentModal
        open={showManualAssignmentModal}
        onOpenChange={setShowManualAssignmentModal}
        manuallyAssignedUsers={manuallyAssignedUsers}
        setManuallyAssignedUsers={setManuallyAssignedUsers}
        manuallyAssignedDueDate={manuallyAssignedDueDate}
        setManuallyAssignedDueDate={setManuallyAssignedDueDate}
        onSave={onSaveManualAssignment}
        isAssigning={isAssigning}
      />

      <AssignmentRulesPanel
        open={openAssignmentRulesPanel}
        onOpenChange={() => {
          setOpenAssignmentRulesPanel(false);
        }}
        learningModuleTemplate={lm}
      />
    </div>
  );
}
