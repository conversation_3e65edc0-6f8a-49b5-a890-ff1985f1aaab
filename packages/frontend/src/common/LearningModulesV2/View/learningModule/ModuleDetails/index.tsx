import {
  LearningModuleTemplate,
  LearningModuleTemplateSubModule,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import LearningModuleCard from '../../../Grid/Card';

interface IProps {
  lm: LearningModuleTemplate;
  selectSubmodule: (sm: LearningModuleTemplateSubModule | undefined) => void;
  openSubModuleId?: string;
  openOverallProgress: (sm: boolean) => void;
}

export default function ModuleDetailsV2({
  lm,
  openSubModuleId,
  selectSubmodule,
  openOverallProgress,
}: IProps) {
  return (
    <div className="min-w-[400px] max-w-[500px]">
      <LearningModuleCard module={lm} blockNavigation={true} />

      <div className="mt-3">
        {lm.subModules.map((sm) => {
          return (
            <div
              key={sm.id}
              className={cn(
                'border bg-white p-3 cursor-pointer rounded-lg mb-3',
                openSubModuleId == sm.id ? 'border-black' : '',
              )}
              onClick={() => {
                selectSubmodule(sm);
                openOverallProgress(false);
              }}
            >
              <div className={cn('flex items-center')}>
                <div className="text-sm">{sm.name}</div>
                <div className="flex-1" />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
