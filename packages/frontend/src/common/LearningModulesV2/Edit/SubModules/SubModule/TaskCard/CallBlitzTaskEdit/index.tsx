'use client';

import {
  CallBlitzTask,
  LearningModuleTaskType,
} from '@/lib/LearningModule/types';
import { cn } from '@/lib/utils';
import { GripVertical, Pencil, Trash2, Trophy } from 'lucide-react';
import { DndFunctions } from '../../..';
import { useEffect, useState } from 'react';

interface IProps {
  task: {
    id: string;
    type: LearningModuleTaskType.CALL_BLITZ;
    info: CallBlitzTask;
  };
  doEdit: () => void;
  doDelete: () => void;
  dnd: DndFunctions;
  isCompetition?: boolean;
}

export default function CallBlitzTaskEdit({
  task,
  doEdit,
  doDelete,
  dnd,
  isCompetition,
}: IProps) {
  const [isDragged, setIsDragged] = useState<boolean>(false);

  useEffect(() => {
    let done = false;
    if (dnd.draggedObject && task) {
      if (dnd.draggedObject.id == task.id && dnd.isActive) {
        setIsDragged(true);
        done = true;
      }
    }
    if (!done) {
      setIsDragged(false);
    }
  }, [dnd, task]);

  if (task.type === LearningModuleTaskType.CALL_BLITZ) {
    const t: CallBlitzTask = task.info;
    return (
      <div
        className={cn('border-t', {
          'opacity-50': isDragged,
          'select-none': dnd.isActive,
        })}
        onMouseEnter={() => {
          dnd.onMouseEnter(task);
        }}
        onMouseLeave={() => {
          dnd.onMouseLeave();
        }}
      >
        <div className="flex items-center p-2">
          <div
            className={cn({
              'hover:cursor-grab': !dnd.isActive,
            })}
            onMouseDown={(e) => {
              dnd.setCurrentDraggedObject(task);
              dnd.onMouseDown(task, e);
            }}
          >
            <GripVertical size={16} className="text-muted-foreground" />
          </div>
          <div className="border rounded-full p-1 flex items-center ml-2 ">
            <Trophy size={20} className="text-blue-600 mx-2" />
            <div className="capitalize mx-1 font-medium mr-2">
              {t.sessionName}
            </div>
          </div>
          <div className="flex-1" />
          <div
            className="rounded-lg hover:bg-gray-50 cursor-pointer p-2"
            onClick={doEdit}
          >
            <Pencil size={16} className="text-muted-foreground" />
          </div>
          <div
            className="rounded-lg hover:bg-gray-50 cursor-pointer p-2"
            onClick={doDelete}
          >
            <Trash2 size={16} className="text-muted-foreground" />
          </div>
        </div>

        {isCompetition && (
          <>
            {t.maxNumberOfCalls !== undefined && (
              <div className="flex items-center ml-8 mb-2 text-xs">
                <div className="text-muted-foreground ">
                  Maximum number of calls:
                </div>
                <div className="ml-2">{t.maxNumberOfCalls}</div>
              </div>
            )}
            <div className="flex items-center ml-8 mb-2 text-xs">
              <div className="text-muted-foreground ">Filler word score:</div>
              <div className="ml-2">
                {t.incorporateFillerWordsScore ? 'Enabled' : 'Disabled'}
              </div>
            </div>
          </>
        )}

        {!isCompetition && (
          <>
            {t.minNumberOfCalls !== undefined && t.minNumberOfCalls > 0 && (
              <div className="flex items-center ml-8 mb-2 text-xs">
                <div className="text-muted-foreground ">Required calls:</div>
                <div className="ml-2">
                  {t.minNumberOfCalls === 0 ? 'unlimited' : t.minNumberOfCalls}
                </div>
              </div>
            )}
            {t.maxNumberOfCalls !== undefined && t.maxNumberOfCalls > 0 && (
              <div className="flex items-center ml-8 mb-2 text-xs">
                <div className="text-muted-foreground ">Max calls allowed:</div>
                <div className="ml-2">{t.maxNumberOfCalls}</div>
              </div>
            )}
            {/* {t.minScorecardScore !== undefined && t.minScorecardScore > 0 && (
              <div className="flex items-center ml-8 mb-2 text-xs">
                <div className="text-muted-foreground ">Min average score:</div>
                <div className="ml-2">{t.minScorecardScore === 0 ? 'Any' : t.minScorecardScore}</div>
              </div>
            )} */}
          </>
        )}
      </div>
    );
  }
  return null;
}
