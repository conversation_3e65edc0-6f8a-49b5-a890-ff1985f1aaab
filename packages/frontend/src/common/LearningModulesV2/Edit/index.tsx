import PageHeader from '@/components/PageHeader';
import { Button } from '@/components/ui/button';
import useRouting from '@/hooks/useRouting';
import LearningModule, {
  LearningModuleTemplate,
  LearningModuleTemplateStatus,
  LearningModuleTemplateSubModule,
} from '@/lib/LearningModule/types';
import LinksManager from '@/lib/linksManager';
import { useEffect, useRef, useState } from 'react';
import LearningModuleService from '@/lib/LearningModule';
import { useQueryClient } from '@tanstack/react-query';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Archive,
  Copy,
  FilePenLine,
  Loader2Icon,
  MoreHorizontalIcon,
  Plus,
  SaveIcon,
  Users,
} from 'lucide-react';
import { BG_COLORS } from './ModuleDetails/EditBackground';
import { Id, toast, ToastContainer } from 'react-toastify';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { generateShortId } from '@/lib/utils';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ModuleDetailsV2 from './ModuleDetails';
import { Switch } from '@/components/ui/switch';
import { useLearningModuleV2 } from '@/hooks/useLearningModulesV2';
import SubmodulesV2 from './SubModules';
import AssignmentsTable from './Assignments';
import { formatDistance } from 'date-fns';
import ManualAssignmentModal from './ManualAssignmentModal';
import { UserDto } from '@/lib/User/types';
import PublishConfirmationModalV2 from './publishConfirmationModal';
import AssignmentRulesPanel from './Assignments/components/AssignmentRulesPanel';
import { useCommonQueryKeys } from '@/hooks/useCommonQueryKeys';

interface IProps {
  id?: string;
  isCompetition?: boolean; //its true only when creating a new competition
}

function LastSaved({ lastSaved }: { lastSaved: Date }) {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center mr-2 text-muted-foreground text-xs">
      {lastSaved &&
        `Saved ${formatDistance(lastSaved, currentTime, { addSuffix: true })}`}
    </div>
  );
}

export default function EditLearningModuleV2({ id, isCompetition }: IProps) {
  const errorToastId = useRef<Id | null>(null);

  const commonQueryKeys = useCommonQueryKeys();
  const queryClient = useQueryClient();

  const randomColor = (): string => {
    return BG_COLORS[Math.floor(Math.random() * BG_COLORS.length)];
  };

  const learningModuleId = id || '';

  const { goToPage, appendToUrl } = useRouting();

  const { data: lmDB, isLoading: isLoadingLearningModule } =
    useLearningModuleV2(learningModuleId);

  const [currentTab, setCurrentTab] = useState<'details' | 'assignments'>(
    'details',
  );
  const [isNew, setIsNew] = useState<boolean>(true);
  const [isCreateNewDropdownOpen, setIsCreateNewDropdownOpen] =
    useState<boolean>(false);
  const [showManualAssignmentModal, setShowManualAssignmentModal] =
    useState<boolean>(false);
  const [manuallyAssignedUsers, setManuallyAssignedUsers] = useState<UserDto[]>(
    [],
  );
  const [manuallyAssignedDueDate, setManuallyAssignedDueDate] = useState<Date>(
    new Date(),
  );
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [lm, setLM] = useState<LearningModuleTemplate>({
    id: '',
    orgId: 0,
    createdAt: new Date(),
    createdBy: 0,
    name: '',
    description: '',
    status: LearningModuleTemplateStatus.DRAFT,
    isCertification: false,
    isCompetition: isCompetition || false,
    archived: false,
    subModules: [],
    lockProgress: false,
    splashImageInfo: JSON.stringify({
      type: 'color',
      color: randomColor(),
    }),
    assignmentRules: [],
  } as LearningModuleTemplate);
  const [openAssignmentRulesPanel, setOpenAssignmentRulesPanel] =
    useState<boolean>(false);
  const [isAssigning, setIsAssigning] = useState<boolean>(false);

  useEffect(() => {
    if (!isLoadingLearningModule) {
      if (lmDB) {
        setIsNew(false);
        setLM(lmDB);
      } else {
        const submodules = [];
        let startDate = undefined;
        if (isCompetition) {
          startDate = new Date();
          let idSM = generateShortId();
          let isUnique = true;

          do {
            isUnique = true;
            lm.subModules.map((s: LearningModuleTemplateSubModule) => {
              if (s.id == idSM) {
                isUnique = false;
              }
            });
            if (!isUnique) {
              idSM = generateShortId();
            }
          } while (!isUnique);

          //competitions have only 1 submodule hidden from the users
          submodules.push({
            id: idSM,
            name: '',
            description: '',
            progress: 0,
            tasks: [],
            assigneesStats: [],
          });
        }
        setIsNew(true);
        setLM({
          id: '',
          orgId: 0,
          createdAt: new Date(),
          createdBy: 0,
          name: '',
          description: '',
          dueDate: new Date(),
          startDate: startDate,
          status: LearningModuleTemplateStatus.DRAFT,
          isCertification: false,
          isCompetition: isCompetition || false,
          archived: false,
          subModules: submodules,
          lockProgress: false,
          splashImageInfo: JSON.stringify({
            type: 'color',
            color: randomColor(),
          }),
        } as LearningModuleTemplate);
      }
    }
  }, [isLoadingLearningModule, lmDB]);

  const update = (l: LearningModuleTemplate) => {
    setLM(l);
    startAutosave(l);
  };

  /****************************************/
  /***************  SAVE ******************/
  /****************************************/

  ////// AUTOSAVE
  const [isAutosavingPending, setIsAutosavingPending] =
    useState<boolean>(false);

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const startAutosave = (l: LearningModuleTemplate) => {
    setIsAutosavingPending(true);
    clearAutosave();

    const delay = 2000;

    timeoutRef.current = setTimeout(async () => {
      forceSave(l);
    }, delay);
  };

  const clearAutosave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  //----------- END AUTOSAVE

  const [isSaving, setIsSaving] = useState<boolean>(false);

  const forceSave = (updatedLearningModule: LearningModuleTemplate) => {
    clearAutosave();
    console.log('updatedLearningModule', updatedLearningModule);
    doSave(updatedLearningModule);
  };

  const save = async () => {
    await doSave(lm);
    goToPage(LinksManager.learningModules(lm.id));
  };

  const doSave = async (
    tmpLM: LearningModuleTemplate,
    updatingArchive = false,
  ) => {
    if (!tmpLM.archived || updatingArchive) {
      setIsAutosavingPending(false);
      setIsSaving(true);

      let nlm: LearningModuleTemplate | undefined = undefined;
      try {
        nlm = await LearningModuleService.upsertV2(tmpLM);
        setLastSaved(new Date());
      } catch (e) {
        console.log(e);
      }

      if (nlm) {
        if (isNew) {
          setLM(nlm);
          setIsNew(false);
          // goToPage(LinksManager.learningModulesEdit(nlm.id));
          appendToUrl(nlm.id);
        }
      }

      await queryClient.invalidateQueries({ queryKey: ['learning-modules'] });
      await queryClient.invalidateQueries({ queryKey: ['learning-module'] });
      setIsSaving(false);
    }
  };

  const [showPublishModal, setShowPublishModal] = useState<boolean>(false);

  const publish = () => {
    if (verifyLearningModuleBeforePublish()) {
      setShowPublishModal(true);
    }
  };

  const verifyLearningModuleBeforePublish = (): boolean => {
    let errorMessage =
      'Some fields are missing or invalid. Please check the form and try again.\n';
    let error = false;

    if (lm.name == '') {
      errorMessage += '\n  Name is required';
      error = true;
    }
    if (lm.subModules.length == 0) {
      errorMessage += '\n  At least one submodule is required';
      error = true;
    } else {
      let submoduleError = false;
      let taskError = false;
      lm.subModules.forEach((sm) => {
        if (sm.name == '' && !submoduleError && !lm.isCompetition) {
          errorMessage += '\n  Submodule name is required';
          submoduleError = true;
        }
        if (sm.tasks.length == 0 && !taskError) {
          errorMessage += '\n  At least one task is required in each submodule';
          taskError = true;
        }
      });
      if (submoduleError || taskError) {
        error = true;
      }
    }
    if (error && !toast.isActive(errorToastId.current as Id)) {
      errorToastId.current = toast.error(errorMessage);
    }

    return !error;
  };

  const doPublish = async () => {
    setLM({
      ...lm,
      status: LearningModuleTemplateStatus.PUBLISHED,
    });
    await doSave({
      ...lm,
      status: LearningModuleTemplateStatus.PUBLISHED,
    });
  };

  const closePublishConfModalAndSave = async (publish: boolean) => {
    setShowPublishModal(false);
    if (publish) {
      await doPublish();
      goToPage(LinksManager.learningModules(lm.id));
    } else {
      setLM({ ...lm, status: LearningModuleTemplateStatus.DRAFT });
      await doSave({ ...lm, status: LearningModuleTemplateStatus.DRAFT });
    }
  };

  const archive = async () => {
    setLM({ ...lm, archived: true });
    await doSave({ ...lm, archived: true }, true);
    history.go(-2);
  };

  const clone = async () => {
    setIsSaving(true);

    let nlm: LearningModule | undefined = undefined;
    try {
      nlm = await LearningModuleService.clone(lm.id);
    } catch (e) {
      console.log(e);
    }

    await queryClient.invalidateQueries({ queryKey: ['learning-modules'] });
    await queryClient.invalidateQueries({ queryKey: ['learning-module'] });

    if (nlm) {
      goToPage(LinksManager.learningModulesEdit(nlm.id));
    }

    setIsSaving(false);
  };

  const onSaveManualAssignment = async () => {
    try {
      setIsAssigning(true);
      const nlm = await LearningModuleService.assignTemplate(
        lm.id,
        manuallyAssignedUsers.map((u) => u.id),
        manuallyAssignedDueDate,
      );
      setLM({ ...lm, id: nlm.id } as LearningModuleTemplate);
      queryClient.invalidateQueries({
        queryKey: ['learning-module-assignments', lm.id, ...commonQueryKeys],
      });
    } catch (error) {
      console.error('Error saving manual assignment:', error);
    } finally {
      setIsAssigning(false);
      setShowManualAssignmentModal(false);
    }
  };

  /****************************************/
  /*************** RENDER *****************/
  /****************************************/

  // let isLoading = true; //isLoadingLearningModule

  if (isLoadingLearningModule) {
    return (
      <div className="bg-[#FBFBFB] px-6 py-4 h-[100vh] flex flex-col ">
        <div>
          <Skeleton className="w-full h-20" />
        </div>
        <div className="flex items-stretch pt-3 flex-1">
          <div className="w-[35%] mr-6">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>

          <div className="flex-1">
            &nbsp;
            <Skeleton className="w-full h-[96%]" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#FBFBFB] px-6 py-4 min-h-[100vh]">
      <PageHeader
        title={[
          {
            title: lm.isCompetition ? 'Competitions' : 'Learning Modules',
            href: LinksManager.learningModules(),
          },
          { title: isNew ? 'Create new' : lm?.name || '' },
        ]}
        rightComponent={
          <div className="flex items-center">
            {(isSaving || lastSaved) && (
              <div className="flex items-center mr-2 text-muted-foreground text-xs">
                {isSaving ? (
                  <>
                    Saving...{' '}
                    <Loader2Icon className="animate-spin ml-2" size={16} />
                  </>
                ) : (
                  lastSaved && <LastSaved lastSaved={lastSaved} />
                )}
              </div>
            )}
            {!lm.archived && (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant={'outline'}
                      disabled={isSaving || isAutosavingPending}
                      className="mr-2 px-2"
                    >
                      <MoreHorizontalIcon size={16} />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={clone}
                      disabled={isSaving || isAutosavingPending}
                    >
                      <Copy size={16} className="mr-2" />
                      Clone
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={archive}
                      disabled={isSaving || isAutosavingPending}
                    >
                      <Archive size={16} className="mr-2" />
                      Archive
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button
                  variant={'outline'}
                  onClick={save}
                  disabled={isSaving}
                  className="mr-2"
                >
                  <SaveIcon size={16} className="mr-2" />
                  Save
                </Button>
                <Button onClick={publish} disabled={isSaving}>
                  <Switch
                    className="h-4 w-7 pr-2 mr-2 data-[state=checked]:bg-blue-500"
                    thumbClassName="h-3 w-3"
                    checked={
                      lm.status === LearningModuleTemplateStatus.PUBLISHED
                    }
                    onChange={() => {
                      if (lm.status === LearningModuleTemplateStatus.DRAFT) {
                        setLM({
                          ...lm,
                          status: LearningModuleTemplateStatus.PUBLISHED,
                        });
                      } else {
                        setLM({
                          ...lm,
                          status: LearningModuleTemplateStatus.DRAFT,
                        });
                      }
                    }}
                  />
                  Publish
                </Button>
              </>
            )}
          </div>
        }
      />

      <Tabs
        className="flex flex-1 w-full mt-4"
        defaultValue="details"
        value={currentTab}
      >
        <TabsList>
          <TabsTrigger value="details" onClick={() => setCurrentTab('details')}>
            <FilePenLine size={16} className="mr-1" />
            Details
          </TabsTrigger>
          <TabsTrigger
            value="assignments"
            onClick={() => setCurrentTab('assignments')}
          >
            <Users size={16} className="mr-1" />
            Assignments
          </TabsTrigger>
        </TabsList>

        {currentTab === 'assignments' && (
          <div className="flex flex-1 items-center justify-end">
            <DropdownMenu
              open={isCreateNewDropdownOpen}
              onOpenChange={setIsCreateNewDropdownOpen}
            >
              <DropdownMenuTrigger asChild>
                <Button
                  variant={'outline'}
                  onClick={() => setIsCreateNewDropdownOpen(true)}
                >
                  <Plus size={16} className="mr-2" />
                  Create new
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                <DropdownMenuItem
                  onClick={() => setShowManualAssignmentModal(true)}
                  className="flex flex-col items-start cursor-pointer"
                >
                  <div className="font-medium">Create a manual assignment</div>
                  <div className="text-muted-foreground mt-1">
                    Configure a team and a due date manually
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setOpenAssignmentRulesPanel(true)}
                  className="flex flex-col items-start cursor-pointer"
                >
                  <div className="font-medium">Create a rule</div>
                  <div className="text-muted-foreground mt-1">
                    Configure automatic rules that will be assigning this
                    learning module
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </Tabs>

      {currentTab === 'details' && (
        <div className="flex items-start mt-6 space-x-3">
          <ModuleDetailsV2 lm={lm} setLM={update} />

          <SubmodulesV2 lm={lm} setLM={update} />
        </div>
      )}

      {currentTab === 'assignments' && (
        <div className="mt-6">
          <AssignmentsTable templateId={lm.id} edit />
        </div>
      )}

      <PublishConfirmationModalV2
        isPublished={lm.status === LearningModuleTemplateStatus.PUBLISHED}
        modalOpen={showPublishModal}
        setModalOpen={setShowPublishModal}
        doSave={closePublishConfModalAndSave}
      />
      <ToastContainer />

      <ManualAssignmentModal
        open={showManualAssignmentModal}
        onOpenChange={setShowManualAssignmentModal}
        manuallyAssignedUsers={manuallyAssignedUsers}
        setManuallyAssignedUsers={setManuallyAssignedUsers}
        manuallyAssignedDueDate={manuallyAssignedDueDate}
        setManuallyAssignedDueDate={setManuallyAssignedDueDate}
        onSave={onSaveManualAssignment}
        isAssigning={isAssigning}
      />

      <AssignmentRulesPanel
        open={openAssignmentRulesPanel}
        onOpenChange={() => {
          setOpenAssignmentRulesPanel(false);
        }}
        learningModuleTemplate={lm}
      />
    </div>
  );
}
