import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { format } from 'date-fns';
import { MoreHorizontal, Eye, Edit, ChevronRight } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UserDto } from '@/lib/User/types';
import { LearningModuleStatus } from '@/lib/LearningModule/types';
import useRouting from '@/hooks/useRouting';
import LinksManager from '@/lib/linksManager';
import { ProgressCircle } from '@tremor/react';
import { useLearningModuleAssignments } from '@/hooks/useLearningModulesV2';

interface IProps {
  templateId: string;
  edit: boolean;
}

export default function AssignmentsTable({ templateId, edit }: IProps) {
  const { data: assignments, isLoading } =
    useLearningModuleAssignments(templateId);
  const { goToPage } = useRouting();

  const formatDate = (date: Date | undefined) => {
    return format(date ? new Date(date) : new Date(), 'MMM dd, yyyy');
  };

  const formatDateTime = (date: Date | undefined) => {
    return format(date ? new Date(date) : new Date(), 'MMM dd, yyyy HH:mm');
  };

  const getAssigneeNames = (users: UserDto[]) => {
    if (!users?.length) return 'No assignees';
    if (users?.length === 1)
      return `${users[0].firstName} ${users[0].lastName}`;
    if (users?.length === 2)
      return `${users[0].firstName} ${users[0].lastName}, ${users[1].firstName} ${users[1].lastName}`;
    return `${users[0].firstName} ${users[0].lastName}, ${users[1].firstName} ${users[1].lastName} +${users.length - 2} more`;
  };

  const handleViewAssignment = (assignmentId: string) => {
    goToPage(LinksManager.learningModules(templateId, { assignmentId }));
  };

  const handleEditAssignment = (assignmentId: string) => {
    goToPage(LinksManager.learningModulesEdit(templateId, { assignmentId }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-muted-foreground">Loading assignments...</div>
      </div>
    );
  }

  if (!templateId || templateId === '') {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="text-lg font-medium mb-2">Save the template first</div>
        <div className="text-muted-foreground mb-4">
          You need to save this learning module template before you can view its
          assignments.
        </div>
      </div>
    );
  }

  if (assignments && assignments.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="text-lg font-medium mb-2">No assignments yet</div>
        <div className="text-muted-foreground mb-4">
          When you assign this learning module template to users, the
          assignments will appear here.
        </div>
        <Button variant="outline">Create Assignment</Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Assignment Date</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Rule & Assignee</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Assigned To</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {assignments &&
              assignments.map((assignment) => (
                <TableRow key={assignment.id}>
                  <TableCell>
                    <div className="text-sm">
                      {formatDateTime(assignment.createdAt)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(assignment.dueDate)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {getAssigneeNames(assignment.assignees)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      {assignment.status ==
                        LearningModuleStatus.IN_PROGRESS && (
                        <ProgressCircle
                          color={'blue'}
                          value={assignment.progress}
                          size="xs"
                        ></ProgressCircle>
                      )}
                      {assignment.status == LearningModuleStatus.COMPLETED && (
                        <div className="text-xs p-1 px-2 bg-blue-100 text-blue-600 rounded-full border">
                          Completed
                        </div>
                      )}
                      {assignment.status ==
                        LearningModuleStatus.IN_PREPARATION && (
                        <div className="text-xs text-muted-foreground">
                          Draft
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {assignment.assignees?.length || 0} reps
                    </div>
                  </TableCell>
                  <TableCell className="flex flex-row gap-2">
                    {edit && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => handleViewAssignment(assignment.id)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => handleEditAssignment(assignment.id)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleViewAssignment(assignment.id)}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
