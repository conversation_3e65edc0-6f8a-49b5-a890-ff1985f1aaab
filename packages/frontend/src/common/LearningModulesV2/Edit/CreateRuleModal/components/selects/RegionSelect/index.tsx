import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  LearningModuleAssignmentRuleCriteria,
  LearningModuleAssignmentRuleRegionCriteriaValue,
  LearningModuleAssignmentRuleType,
} from '@/lib/LearningModule/types';
import { ruleTypes } from '../../..';
import { Button } from '@/components/ui/button';
import { XIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';

export const RegionSelect = ({
  rule,
  setSelectedRuleType,
  index,
  onRemoveRule,
}: {
  rule: LearningModuleAssignmentRuleCriteria;
  setSelectedRuleType: (
    rule: LearningModuleAssignmentRuleCriteria,
    index: number,
  ) => void;
  index: number;
  onRemoveRule: (index: number) => void;
}) => {
  return (
    <div className="flex flex-row">
      <Select
        value={rule.type}
        onValueChange={(value) => {
          setSelectedRuleType(
            { ...rule, type: value as LearningModuleAssignmentRuleType },
            index,
          );
        }}
      >
        <SelectTrigger
          className="rounded-r-none border-r-0 cursor-pointer font-medium w-fit"
          hideIcon
        >
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {ruleTypes.map((item) => (
            <SelectItem
              key={item.value}
              value={item.value}
              onClick={() => {
                setSelectedRuleType({ ...rule, type: item.value }, index);
              }}
            >
              {item.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {rule.type && (
        <Input
          className="rounded-l-none border-l-0 cursor-text flex-1"
          placeholder="Enter a region"
          value={
            (rule.value as LearningModuleAssignmentRuleRegionCriteriaValue)
              ?.region as string
          }
          onChange={(e) => {
            setSelectedRuleType(
              {
                ...rule,
                value: {
                  ...(rule.value as LearningModuleAssignmentRuleRegionCriteriaValue),
                  region: e.target.value,
                },
              },
              index,
            );
          }}
        />
      )}
      <Button
        variant="outline"
        size="icon"
        className="rounded-l-none border-l-0 px-2"
        onClick={() => {
          onRemoveRule(index);
        }}
      >
        <XIcon className="w-4 h-4" />
      </Button>
    </div>
  );
};
