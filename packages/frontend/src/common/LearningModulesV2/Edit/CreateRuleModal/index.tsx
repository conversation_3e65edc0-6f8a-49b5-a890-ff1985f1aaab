import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { InfoIcon, Loader2, PlusIcon } from 'lucide-react';
import {
  LearningModuleAssignmentRule,
  LearningModuleAssignmentRuleCriteria,
  LearningModuleAssignmentRuleCriteriaStatus,
  LearningModuleAssignmentRuleStatus,
  LearningModuleAssignmentRuleType,
  LearningModuleTemplate,
} from '@/lib/LearningModule/types';
import { useEffect, useState } from 'react';
import { TeamSelect } from './components/selects/TeamSelect';
import { SignUpDateSelect } from './components/selects/SignUpDateSelect';
import { CompanyStartDateSelect } from './components/selects/CompanyStartDateSelect';
import { RegionSelect } from './components/selects/RegionSelect';
import { cn } from '@/lib/utils';
import { RoleSelect } from './components/selects/RoleSelect';
import { Input } from '@/components/ui/input';
import LearningModuleService from '@/lib/LearningModule';
import { useCommonQueryKeys } from '@/hooks/useCommonQueryKeys';
import { useQueryClient } from '@tanstack/react-query';
import Checkbox from '@/components/ui/Hyperbound/checkbox';
import {
  Tooltip,
  TooltipProvider,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export const ruleTypes = [
  {
    label: 'Team',
    value: LearningModuleAssignmentRuleType.TEAM,
  },
  {
    label: 'Sign Up Date',
    value: LearningModuleAssignmentRuleType.SIGN_UP_DATE,
  },
  {
    label: 'Company Start Date',
    value: LearningModuleAssignmentRuleType.COMPANY_START_DATE,
  },
  {
    label: 'Region',
    value: LearningModuleAssignmentRuleType.REGION,
  },
  {
    label: 'Role',
    value: LearningModuleAssignmentRuleType.ROLE,
  },
];

export const defaultValues = {
  [LearningModuleAssignmentRuleType.TEAM]: {
    type: LearningModuleAssignmentRuleType.TEAM,
    value: {
      operator: 'in',
    },
  },
  [LearningModuleAssignmentRuleType.SIGN_UP_DATE]: {
    type: LearningModuleAssignmentRuleType.SIGN_UP_DATE,
    value: {
      operator: 'in',
    },
  },
  [LearningModuleAssignmentRuleType.COMPANY_START_DATE]: {
    type: LearningModuleAssignmentRuleType.COMPANY_START_DATE,
    value: {
      operator: 'in',
    },
  },
  [LearningModuleAssignmentRuleType.REGION]: {
    type: LearningModuleAssignmentRuleType.REGION,
    value: {
      operator: 'in',
    },
  },
  [LearningModuleAssignmentRuleType.ROLE]: {
    type: LearningModuleAssignmentRuleType.ROLE,
    value: {
      operator: 'in',
    },
  },
};

const COMP_BY_TYPE = {
  [LearningModuleAssignmentRuleType.TEAM]: TeamSelect,
  [LearningModuleAssignmentRuleType.SIGN_UP_DATE]: SignUpDateSelect,
  [LearningModuleAssignmentRuleType.COMPANY_START_DATE]: CompanyStartDateSelect,
  [LearningModuleAssignmentRuleType.REGION]: RegionSelect,
  [LearningModuleAssignmentRuleType.ROLE]: RoleSelect,
};

export default function CreateRuleModal({
  open,
  onOpenChange,
  template,
  rule: _rule,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template: LearningModuleTemplate;
  rule?: LearningModuleAssignmentRule;
}) {
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  const [isSaving, setIsSaving] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [rule, setRule] = useState<LearningModuleAssignmentRule>({
    id: 0,
    learningModuleTemplateId: '',
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 0,
    updatedBy: 0,
    status: LearningModuleAssignmentRuleStatus.ACTIVE,
    criterias: [],
    dueDate: undefined,
    executions: [],
    settings: {
      frequency: 'daily',
      unit: 'days',
      value: 1,
      recurring: false,
    },
  });

  useEffect(() => {
    if (_rule) {
      setRule({
        ..._rule,
        settings: {
          frequency: 'daily',
          unit: 'days',
          value: 1,
          recurring: false,
        },
      });
    }
  }, [_rule]);

  useEffect(() => {
    const isThereEnoughCriterias = rule.criterias.length > 0;
    const isThereDueDate =
      !!rule.settings?.unit &&
      !!rule.settings?.value &&
      Number(rule.settings?.value) >= 0;
    setIsValid(isThereEnoughCriterias && isThereDueDate);
  }, [rule]);

  const onRuleValueChange = (
    ruleCriteria: LearningModuleAssignmentRuleCriteria,
    index: number,
  ) => {
    setRule({
      ...rule,
      criterias: rule.criterias.map((item, i) =>
        i === index ? ruleCriteria : item,
      ),
    });
  };

  const onRemoveRule = (index: number) => {
    setRule({
      ...rule,
      criterias: rule.criterias.filter((_, i) => i !== index),
    });
  };

  const onSave = async () => {
    try {
      setIsSaving(true);
      if (_rule) {
        await LearningModuleService.updateRule(template.id, _rule.id, rule);
      } else {
        await LearningModuleService.createRule(template.id, rule);
      }
      onOpenChange(false);
      queryClient.invalidateQueries({
        queryKey: [
          'learning-module-assignment-rules',
          template.id,
          ...commonQueryKeys,
        ],
      });
    } catch (err) {
      console.log(err);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Set up a new rule</DialogTitle>
        </DialogHeader>
        <DialogDescription className="mb-4 text-primary">
          Select which settings you want to include
        </DialogDescription>
        <div className="flex flex-col gap-8">
          <div className="flex flex-col gap-2">
            Rules
            <div
              className={cn(
                'flex flex-wrap',
                rule.criterias.length > 0 && ' gap-2',
              )}
            >
              <div className="flex flex-wrap gap-2">
                {rule.criterias.map((ruleType, index) => {
                  const Component =
                    COMP_BY_TYPE[ruleType.type as keyof typeof COMP_BY_TYPE];
                  return (
                    <Component
                      key={index}
                      index={index}
                      rule={ruleType}
                      setSelectedRuleType={onRuleValueChange}
                      onRemoveRule={onRemoveRule}
                    />
                  );
                })}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-fit">
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Add filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="min-w-[260px]">
                  {ruleTypes.map((ruleType) => (
                    <DropdownMenuItem
                      disabled={rule.criterias.some(
                        (rule) => rule.type === ruleType.value,
                      )}
                      key={ruleType.value}
                      className="cursor-pointer text-primary text-medium "
                      onClick={() => {
                        setRule({
                          ...rule,
                          criterias: [
                            ...rule.criterias,
                            {
                              ...(defaultValues[
                                ruleType.value as keyof typeof defaultValues
                              ] as LearningModuleAssignmentRuleCriteria),
                              status:
                                LearningModuleAssignmentRuleCriteriaStatus.ACTIVE,
                            },
                          ],
                        });
                      }}
                    >
                      {ruleType.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <div className="flex flex-col gap-3">
            What should be the due date for this the learning module after the
            assignment?
            <div className="flex flex-row">
              <Select
                defaultValue="days"
                value={rule.settings?.unit as string}
                onValueChange={(value) => {
                  setRule({
                    ...rule,
                    settings: { ...rule.settings, unit: value },
                  });
                }}
              >
                <SelectTrigger
                  className="rounded-r-none border-r-0 cursor-pointer  w-fit"
                  hideIcon
                >
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  {['days', 'weeks', 'months', 'years'].map((item) => (
                    <SelectItem
                      key={item}
                      value={item}
                      onClick={() => {
                        setRule({
                          ...rule,
                          settings: {
                            ...rule.settings,
                            unit: item,
                            value: 1,
                          },
                        });
                      }}
                    >
                      {item.charAt(0).toUpperCase() + item.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                className="rounded-l-none border-l-0 cursor-text min-w-[50px] w-24"
                type="number"
                value={String(rule.settings?.value ?? '')}
                defaultValue={1}
                min={1}
                onChange={(e) => {
                  const value = e.target.value;
                  setRule({
                    ...rule,
                    settings: {
                      ...rule.settings,
                      value: value === '' ? 1 : Number(value),
                    },
                  });
                }}
              />
              <span className="ml-4 flex items-center justify-center text-primary text-medium">
                After assignment
              </span>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <div className="flex flex-row items-center">
              <Checkbox
                checked={!!rule.settings?.recurring}
                onToggle={() => {
                  setRule({
                    ...rule,
                    settings: {
                      ...rule.settings,
                      recurring: !rule.settings?.recurring,
                      frequency: !rule.settings?.recurring ? 'daily' : 'once',
                    },
                  });
                }}
              />
              <span className="text-primary text-medium flex flex-row items-center">
                Do you want to reassign this learning module recurringly?
                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger>
                      <InfoIcon className="w-4 h-4 ml-2" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        The learning module is assigned everyday at the end of
                        the day.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </span>
            </div>

            {rule.settings?.recurring && (
              <div className="mt-4 flex flex-col gap-2">
                <span className="text-primary text-medium">
                  How often do you want to execute this rule?
                </span>
                <Select
                  defaultValue="daily"
                  value={rule.settings?.frequency as string}
                  onValueChange={(value) => {
                    setRule({
                      ...rule,
                      settings: { ...rule.settings, frequency: value },
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    {[
                      'once',
                      'daily',
                      'weekly',
                      'every 2 weeks',
                      'monthly',
                      'yearly',
                    ].map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.charAt(0).toUpperCase() + item.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>
        <DialogFooter className="mt-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button disabled={!isValid || isSaving} onClick={onSave}>
            {isSaving ? 'Saving...' : 'Save'}
            {isSaving && <Loader2 className="w-4 h-4 ml-2 animate-spin" />}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
