import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { Badge<PERSON>heck, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import useRouting from '@/hooks/useRouting';
import LinksManager from '@/lib/linksManager';
import useUserSession from '@/hooks/useUserSession';
import { LearningModuleTemplate } from '@/lib/LearningModule/types';

interface IProps {
  modules: LearningModuleTemplate[];
  comingSoon?: boolean;
}

export default function ListViewV2({ modules, comingSoon }: IProps) {
  const { isAdmin } = useUserSession();
  const isDisabled = comingSoon && !isAdmin;
  const { goToPage } = useRouting();

  const edit = (lmId: string) => {
    goToPage(LinksManager.learningModules(lmId));
  };

  return (
    <Table className="w-full">
      <TableContent>
        <TableRow>
          <TableCellHead>Name</TableCellHead>
          <TableCellHead className="w-[20px]">&nbsp;</TableCellHead>
        </TableRow>
        {modules.map((lm: LearningModuleTemplate) => {
          return (
            <TableRow
              key={lm.id}
              className={cn('hover:bg-gray-50 cursor-pointer group', {
                'opacity-50 cursor-default': isDisabled,
              })}
              onClick={() => {
                if (!isDisabled) {
                  edit(lm.id);
                }
              }}
            >
              <TableCell className="">
                <div className="">
                  <div
                    className={cn('text-sm font-medium flex items-center', {
                      'text-blue-500': lm.isCertification,
                    })}
                  >
                    {lm.name}
                    {lm.isCertification && (
                      <BadgeCheck
                        size={18}
                        className={cn('ml-1 text-blue-500')}
                      />
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1 text-wrap">
                    {lm.description}
                  </div>
                </div>
              </TableCell>
              <TableCell className="w-[20px]">
                <ChevronRight
                  className="text-muted-foreground group-hover:visible invisible"
                  size={16}
                />
              </TableCell>
            </TableRow>
          );
        })}
      </TableContent>
      <TableFooter className="invisible">
        <div>&nbsp;</div>
      </TableFooter>
    </Table>
  );
}
