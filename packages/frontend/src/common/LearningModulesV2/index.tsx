import PageHeader from '@/components/PageHeader';
import { But<PERSON> } from '@/components/ui/button';
import useRouting from '@/hooks/useRouting';
import useUserSession from '@/hooks/useUserSession';
import {
  LearningModuleTemplate,
  LearningModuleTemplateStatus,
} from '@/lib/LearningModule/types';
import LinksManager from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import {
  BookOpenText,
  Loader2Icon,
  Lock,
  Plus,
  Search,
  Trophy,
} from 'lucide-react';
import { useMemo, useRef, useState } from 'react';
import FiltersPanel from './Filters';
import Image from 'next/image';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import useLearningModulesV2 from '@/hooks/useLearningModulesV2';
import ListViewV2 from './List';
import GridViewV2 from './Grid';

export class FiltersState {
  searchString: string;
  orderByDate: string; //'asc' | 'desc';
  assignees: number[];
  status: LearningModuleTemplateStatus;
  useView: string; // grid | list
  start: number;
  numberOfResults: number;
  archived: boolean;
  isCompetition: boolean;

  constructor(
    status?: LearningModuleTemplateStatus,
    assignees?: number[],
    order?: string,
    isCompetition?: boolean,
  ) {
    this.searchString = '';

    if (order && (order == 'asc' || order == 'desc')) {
      this.orderByDate = order;
    } else {
      this.orderByDate = 'asc';
    }

    if (assignees) {
      this.assignees = assignees;
    } else {
      this.assignees = [];
    }

    if (status) {
      this.status = status;
    } else {
      this.status = LearningModuleTemplateStatus.DRAFT;
    }

    if (isCompetition) {
      this.isCompetition = isCompetition;
    } else {
      this.isCompetition = false;
    }

    this.useView = 'grid';
    this.start = 0;
    this.numberOfResults = 100;
    this.archived = false;
  }
}

interface IProps {
  status?: LearningModuleTemplateStatus;
  assignees?: number[];
  order?: string;
  openTab?: string;
}

export default function LearningModulesV2({
  status,
  assignees,
  order,
  openTab,
}: IProps) {
  const { isAdmin, isCompetitionOrg, canAccessInternalCompetitions } =
    useUserSession();
  const { goToPage, setUrlParameter, deleteUrlParameter } = useRouting();

  const [filtersState, setFiltersState] = useState<FiltersState>(
    new FiltersState(
      status as LearningModuleTemplateStatus,
      assignees,
      order,
      openTab == 'competitions' ? true : false,
    ),
  );

  const { data: paginatedResults, isLoading: isLoadingModules } =
    useLearningModulesV2(
      filtersState.status || undefined,
      filtersState.archived,
      filtersState.searchString,
      filtersState.start,
      filtersState.numberOfResults,
    );

  const { learningModules } = useMemo(() => {
    if (isAdmin && filtersState.isCompetition) {
      return {
        learningModules: paginatedResults?.learningModules || [],
      };
    }

    const learningModules: LearningModuleTemplate[] = [];

    paginatedResults?.learningModules.forEach((lm: LearningModuleTemplate) => {
      learningModules.push(lm);
    });

    return {
      learningModules,
    };
  }, [paginatedResults]);

  const createNew = () => {
    goToPage(LinksManager.learningModulesEdit());
  };

  const createNewCompetition = () => {
    goToPage(LinksManager.learningModulesEdit('?competition=true'));
  };

  /***********************************/
  /************** SEARCH *************/
  /***********************************/

  const [searchString, setSearchString] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);

  const search = async (s: string) => {
    setSearchString(s);

    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      setFiltersState({ ...filtersState, searchString: s });
    }, 400);
  };

  /**************************************/
  /**************   RENDER  *************/
  /**************************************/

  if (isCompetitionOrg) {
    return (
      <div
        className={
          'bg-[#FBFBFB] relative h-[100vh] block overflow-hidden px-4 py-4'
        }
      >
        <div className="h-[100vh] w-full overflow-hidden flex flex-col">
          <div className="px-6">
            <PageHeader title="Learning Modules" />
          </div>
          <div className="w-full relative flex-1 mt-4">
            <Image
              src={'/images/learning.png'}
              fill
              className="object-contain object-top"
              alt="learning modules"
            />
          </div>
        </div>
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock the power of Learning Modules
            </div>
            <div className="text-base mt-2 text-muted-foreground max-w-[50%] text-center">
              Complete your onboarding to unlock tailored Learning Modules and
              empower your team with streamlined training and skill-building
              through Hyperbound.
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#FBFBFB] px-6 py-4 min-h-[100vh]">
      <PageHeader
        title="Learning Modules"
        rightComponent={
          isAdmin && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button>
                  <Plus size={16} className="mr-2" />
                  Create new
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  className="cursor-pointer border-b"
                  onClick={createNew}
                >
                  <div>
                    <div className="font-semibold">Create learning module</div>
                    <div className="text-muted-foreground">
                      For training your reps
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={createNewCompetition}
                  disabled={!canAccessInternalCompetitions}
                >
                  <div>
                    <div className="font-semibold">Create competition</div>
                    <div className="text-muted-foreground">
                      Run a competition to see who is the best
                    </div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )
        }
      />

      <div className="mt-6 flex items-center">
        <div className="">
          {canAccessInternalCompetitions && (
            <Tabs
              defaultValue={
                filtersState.isCompetition ? 'competitions' : 'learning-modules'
              }
              onValueChange={(e) => {
                if (e == 'competitions') {
                  setUrlParameter('show', 'competitions');
                  setFiltersState({ ...filtersState, isCompetition: true });
                } else {
                  deleteUrlParameter('show');
                  setFiltersState({ ...filtersState, isCompetition: false });
                }
              }}
            >
              <TabsList>
                <TabsTrigger value="learning-modules">
                  <BookOpenText size={16} className="mr-2" />
                  Learning Modules
                </TabsTrigger>
                <TabsTrigger value="competitions">
                  <Trophy size={16} className="mr-2" />
                  Competitions
                </TabsTrigger>
              </TabsList>
            </Tabs>
          )}
        </div>
        <div className="flex-1" />
        <div className="border rounded-[6px] p-[6px] text-sm flex items-center bg-white w-[20vw]">
          <div className="mr-2">
            <Search size={16} className="text-muted-foreground" />
          </div>
          <div className="flex-1 mr-2">
            <input
              value={searchString}
              onChange={(e) => {
                search(e.target.value);
              }}
              className="outline-none w-full "
              placeholder="Search"
            />
          </div>
          <div>
            <Loader2Icon
              className={cn('animate-spin text-muted-foreground', {
                invisible: !isLoadingModules,
              })}
              size={16}
            />
          </div>
        </div>
        <FiltersPanel state={filtersState} setFiltersState={setFiltersState} />
      </div>

      {!isLoadingModules && (
        <div className="mt-8">
          {learningModules.length == 0 ? (
            <div className="mt-20 flex justify-center">
              <div className="flex flex-col justify-center">
                <div className="font-semibold text-center">
                  {filtersState.isCompetition
                    ? 'No competitions yet'
                    : 'No Learning Modules yet'}
                </div>
                <div className="text-muted-foreground mt-3 mb-6 text-center">
                  {isAdmin
                    ? filtersState.isCompetition
                      ? 'Create a first competition to start training your team.'
                      : 'Create a first learning module to start training your team.'
                    : filtersState.isCompetition
                      ? 'Once your admin creates a competition, you will be able to access it here.'
                      : 'Once your admin creates a learning module, you will be able to access it here.'}
                </div>
                {isAdmin && (
                  <div className=" text-center">
                    <Button
                      onClick={
                        filtersState.isCompetition
                          ? createNewCompetition
                          : createNew
                      }
                    >
                      <Plus size={16} className="mr-2" />
                      Create new
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <>
              {filtersState.useView == 'grid' &&
                !filtersState.isCompetition && (
                  <GridViewV2 modules={learningModules || []} />
                )}

              {(filtersState.useView == 'list' ||
                filtersState.isCompetition) && (
                <ListViewV2 modules={learningModules || []} />
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}
