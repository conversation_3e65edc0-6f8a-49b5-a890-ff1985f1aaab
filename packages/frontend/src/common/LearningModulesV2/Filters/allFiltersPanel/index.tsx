import { Button } from '@/components/ui/button';
import { FiltersState } from '../..';
import { Sheet, SheetContentLight } from '@/components/ui/sheet';
import { useState } from 'react';
import {
  LearningModuleStatus,
  LearningModuleTemplateStatus,
} from '@/lib/LearningModule/types';
import RepsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/RepsFilter';
import OrdeSelector from '../OrderSelector';
import useRouting from '@/hooks/useRouting';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';

interface IProps {
  state: FiltersState;
  setFiltersState: (fs: FiltersState) => void;
  onClose: () => void;
  open: boolean;
}

export default function AllFiltersPanel({
  state,
  setFiltersState,
  onClose,
  open,
}: IProps) {
  const [cs, setCs] = useState<FiltersState>({ ...state });

  let tmp = String(state.status);
  if (tmp == '') {
    tmp = 'all';
  }
  const [status, setStatus] = useState<string>(tmp);

  const { setUrlParameter, deleteUrlParameter } = useRouting();

  const save = () => {
    if (cs.status) {
      setUrlParameter('status', cs.status);
    } else {
      deleteUrlParameter('status');
    }

    deleteUrlParameter('assignees');
    if (cs.assignees.length > 0) {
      setUrlParameter('assignees', cs.assignees.join(','));
    }
    setUrlParameter('order', cs.orderByDate);
    setFiltersState({ ...cs });
    onClose();
  };

  const renderStatusLabel = (param: string) => {
    switch (param) {
      case LearningModuleStatus.IN_PROGRESS:
        return 'In Progress';
      case LearningModuleStatus.COMPLETED:
        return 'Completed';
      case LearningModuleStatus.IN_PREPARATION:
        return 'Drafts';
      default:
        return 'All';
    }
  };

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContentLight side={'rightFull'} className="p-0">
        <div className="h-screen  overflow-auto w-[400px]">
          <div className="flex flex-col h-full">
            <div className="text-base font-semibold mx-1 p-3 ">Filters</div>

            <div className="p-3">
              <div className="text-sm font-medium mx-1">Status</div>
              <div className="mt-2 mx-2">
                <Select
                  onValueChange={(v: string) => {
                    if (v == 'all') {
                      setCs({
                        ...cs,
                        status: LearningModuleTemplateStatus.DRAFT,
                      });
                    } else {
                      setCs({
                        ...cs,
                        status: v as LearningModuleTemplateStatus,
                      });
                    }
                    setStatus(v);
                  }}
                  value={status}
                >
                  <SelectTrigger>{renderStatusLabel(status)}</SelectTrigger>
                  <SelectContent>
                    <SelectItem value={'all'}>All</SelectItem>
                    <SelectItem value={LearningModuleStatus.IN_PROGRESS}>
                      In Progress
                    </SelectItem>
                    <SelectItem value={LearningModuleStatus.COMPLETED}>
                      Completed
                    </SelectItem>
                    <SelectItem value={LearningModuleStatus.IN_PREPARATION}>
                      Drafts
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="p-3 pt-4">
              <div className="text-sm font-medium mx-1">Assignees</div>
              <div className="mt-2">
                <RepsFilter
                  current={state.assignees}
                  onRepsUpdated={(s: string[]) => {
                    setCs({
                      ...cs,
                      assignees: s.map((id: string) => parseInt(id)),
                    });
                  }}
                />
              </div>
            </div>

            <div className="pt-6 px-3">
              <div className="text-sm font-medium mx-1 mb-2">Sort by</div>
              <OrdeSelector
                current={cs.orderByDate}
                update={(s: string) => {
                  setCs({ ...cs, orderByDate: s });
                }}
              />
            </div>

            <div className="flex-1"></div>
            <div className="flex items-center border-t px-4 py-4">
              <Button variant={'outline'} className="mr-2" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={save}>Save</Button>
            </div>
          </div>
        </div>
      </SheetContentLight>
    </Sheet>
  );
}
