import useUserSession from '@/hooks/useUserSession';
import { FiltersState } from '..';
import ViewSelector from './viewSelector';
import { Button } from '@/components/ui/button';
import { CalendarArrowDown, CalendarArrowUp, Filter } from 'lucide-react';
import { useState } from 'react';
import AllFiltersPanel from './allFiltersPanel';
import OrdeSelector from './OrderSelector';

interface IProps {
  state: FiltersState;
  setFiltersState: (fs: FiltersState) => void;
}

export default function FiltersPanel({ state, setFiltersState }: IProps) {
  const { isAdmin } = useUserSession();

  const [openFiltersPanel, setOpenFiltersPanel] = useState<boolean>(false);

  const setView = (s: string) => {
    setFiltersState({ ...state, useView: s });
  };

  const setSort = (s: string) => {
    setFiltersState({ ...state, orderByDate: s });
  };

  return (
    <div className="flex items-center">
      {!state.isCompetition ? (
        <ViewSelector current={state.useView} setView={setView} />
      ) : (
        <div className="w-[6px]" />
      )}

      {isAdmin && (
        <Button
          variant={'outline'}
          onClick={() => {
            setOpenFiltersPanel(true);
          }}
          className="bg-white"
        >
          <Filter size={16} className="mr-2" />
          Filters
        </Button>
      )}
      {!isAdmin && (
        <OrdeSelector
          current={state.orderByDate}
          update={(s: string) => {
            setSort(s);
          }}
        />
      )}

      {openFiltersPanel && (
        <AllFiltersPanel
          open={openFiltersPanel}
          state={state}
          setFiltersState={setFiltersState}
          onClose={() => {
            setOpenFiltersPanel(false);
          }}
        />
      )}
    </div>
  );
}
