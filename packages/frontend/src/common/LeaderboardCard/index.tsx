import { PARTNER_ORG_IDS } from '@/app/(dashboard)/layout';
import AgentAvatar from '@/components/Avatars/Agent';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import useOrg from '@/hooks/useOrg';
import useOrgLeaderboard from '@/hooks/useOrgLeaderboard';
import useUserSession from '@/hooks/useUserSession';
import { AgentDto } from '@/lib/Agent/types';
import LinksManager from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import dayjs from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';

interface ILeaderboardCardProps {
  agent?: AgentDto;
}

function LeaderboardCard({ agent }: ILeaderboardCardProps) {
  const fromDate = useMemo(() => dayjs().subtract(1, 'month'), []);
  const toDate = useMemo(() => dayjs(), []);
  const router = useRouter();
  const authInfo = useAuthInfo();
  const { data: org } = useOrg();
  const { blurLeaderboard } = useUserSession();

  const { data: leaderboard, isLoading } = useOrgLeaderboard(
    fromDate.toDate(),
    toDate.toDate(),
    agent?.id,
  );
  const activeOrg = useActiveOrg();

  return (
    <Card className="max-w-[450px] h-min">
      <CardHeader>
        <CardTitle>Monthly Leaderboard</CardTitle>
        <CardDescription>
          {!!agent && (
            <div className="flex items-center space-x-2 mt-2">
              <p>for </p>
              <Button
                variant={'outline'}
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(
                    authInfo?.isLoggedIn
                      ? `/buyers/${agent?.vapiId || ''}`
                      : `/buyers?id=${agent?.vapiId || ''}`,
                  );
                }}
                className="space-x-2 pl-2 pr-3 py-1 rounded-full hover:bg-muted/80 hover:transition-all duration-300"
              >
                <AgentAvatar className="w-6 h-6" agent={agent} />
                <div className="capitalize text-primary">
                  {agent?.firstName || ''} {agent?.lastName || ''}
                </div>
              </Button>
            </div>
          )}
          <p className="mt-2">
            Scroll to see more.{' '}
            {!!agent &&
              `Click a row to see rep's calls. Tiebreaks calculated based on lowest filler words. ${
                org?.pilotDetails?.expiryDate &&
                org?.pilotDetails?.perLeaderboardGiftAmount
                  ? `Winner receives a $${org?.pilotDetails?.perLeaderboardGiftAmount} Amazon gift card 💰`
                  : ''
              }`}
          </p>
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2 max-h-96 overflow-auto">
            {[1, 2, 3].map((num) => (
              <div key={num} className="flex justify-between items-center">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
                <Skeleton className="h-4 w-[50px]" />
              </div>
            ))}
          </div>
        ) : (
          <div
            className={
              'space-y-2 max-h-96 overflow-auto ' + (blurLeaderboard && 'blur')
            }
          >
            {!leaderboard?.leaderboard?.length && <p>No active reps found.</p>}
            {(leaderboard?.leaderboard || []).map((rep, i) => (
              <Link
                key={i}
                href={
                  !blurLeaderboard
                    ? agent
                      ? LinksManager.trainingCalls(
                          `?buyers=${agent.id}&reps=${rep.userId}`,
                        )
                      : LinksManager.members(`${rep.userId}`)
                    : ''
                }
                className="h-min"
              >
                <Button
                  variant={'ghost'}
                  className="flex w-full text-left items-center py-8 px-4 rounded-lg"
                >
                  <p
                    className={cn(
                      'text-xl text-muted-foreground/50 mr-4 font-bold',
                      {
                        'text-green-600': rep.rank <= 3,
                      },
                    )}
                  >
                    {rep.rank === 1 ? '⭐️' : rep.rank}
                  </p>
                  <Avatar className="h-9 w-9">
                    {rep?.avatar && (
                      <AvatarImage src={rep.avatar} alt="Avatar" />
                    )}
                    <AvatarFallback className="text-muted-foreground">
                      {rep?.firstName?.charAt(0) || ''}
                      {rep?.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {rep?.firstName || ''} {rep?.lastName || ''}
                    </p>
                    {!PARTNER_ORG_IDS.includes(activeOrg?.orgId as string) && (
                      <p className="text-xs text-muted-foreground">
                        {rep.email}
                      </p>
                    )}
                  </div>
                  <div className="ml-auto font-semibold text-xl">
                    {Number(rep.score || 0).toLocaleString('en-US', {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    })}
                    <span className="text-xs"> / 100</span>
                  </div>
                </Button>
              </Link>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default LeaderboardCard;
