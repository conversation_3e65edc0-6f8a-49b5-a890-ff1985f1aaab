import CreateAssignmentModal from '@/common/CreateAssignment/CreateAssignmentModal';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import useAdminAssignments from '@/hooks/useAdminAssignments';
import { AdminAssignmentDto } from '@/lib/Assignment/types';
import { cn } from '@/lib/utils';
import { ProgressCircle, Tab } from '@tremor/react';
import dayjs from 'dayjs';
import { MoreVerticalIcon, Trash2, CirclePlusIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import AssignmentService from '@/lib/Assignment';
import { useQueryClient } from '@tanstack/react-query';
import { AssignmentDto, AssignmentStatus } from '@/lib/Assignment/types';
import useOrg from '@/hooks/useOrg';
import AgentAvatar from '@/components/Avatars/Agent';

interface IAdminAssignmentsCardProps {
  showHeader?: boolean;
  startCreateNew?: () => void;
}

export default function AdminAssignmentsCard({
  showHeader,
  startCreateNew,
}: IAdminAssignmentsCardProps) {
  const router = useRouter();
  const { data: adminAssignments, isLoading, refetch } = useAdminAssignments();
  const { data: org } = useOrg();
  //************ DELETE ASSIGMENTS  ************/

  const queryClient = useQueryClient();
  const [requestDeleteConfirmation, setRequestDeleteConfirmation] =
    useState(false);
  const [deleteAssignmentForAgent, setDeleteAssignmentsForAgent] = useState<
    number | null
  >(null);

  const startDeleteAssignment = async (agentId: number) => {
    setDeleteAssignmentsForAgent(agentId);
    setRequestDeleteConfirmation(true);
  };

  const handleDeleteAssignment = async () => {
    setRequestDeleteConfirmation(false);
    if (deleteAssignmentForAgent) {
      await AssignmentService.deleteAssignmentForAgent(
        deleteAssignmentForAgent,
      );
      queryClient.invalidateQueries({ queryKey: ['adminAssignments'] });
      queryClient.invalidateQueries({
        queryKey: ['adminAssignmentsByAgentId'],
      });
      queryClient.invalidateQueries({ queryKey: ['assignments'] });
      queryClient.invalidateQueries({ queryKey: ['adminAssignmentsByUser'] });
      if (refetch) {
        refetch();
      }
    }
  };

  const handleCancelDeleteAssignment = async () => {
    setRequestDeleteConfirmation(false);
    setDeleteAssignmentsForAgent(null);
  };

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <div className="flex justify-center">
      <div>
        <Card className="">
          {showHeader && (
            <CardHeader>
              <CardTitle>Assignments</CardTitle>
              <CardDescription>Active assignments by buyer bot</CardDescription>
            </CardHeader>
          )}
          <CardContent className={showHeader ? '' : 'mt-4'}>
            {(adminAssignments || [])?.length == 0 ? (
              <div className="flex flex-col justify-center mt-4 w-[300px]">
                <p className="text-muted-foreground">No assignments yet.</p>
                {startCreateNew && (
                  <Button
                    variant={'default'}
                    onClick={startCreateNew}
                    className="mt-4"
                    disabled={isPilotEnded}
                  >
                    <CirclePlusIcon className="w-4 h-4 mr-2" />
                    New
                  </Button>
                )}
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Buyer bot</TableHead>
                    <TableHead>Assigned to</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody
                  style={{
                    opacity: isLoading ? 0 : 1,
                    transition: 'opacity 0.5s ease-in-out',
                  }}
                >
                  {adminAssignments?.map((assignment, i) => {
                    const completedRatio =
                      assignment.numCompletedCalls /
                      assignment.numAssignedCalls;

                    return (
                      <TableRow
                        className="cursor-pointer"
                        onClick={() =>
                          router.push(
                            `/coaching/assignments/${assignment.agentId}`,
                          )
                        }
                        key={i}
                      >
                        <TableCell className="flex items-center">
                          <AgentAvatar
                            className="h-9 w-9"
                            agent={assignment?.agent}
                          />
                          <div className="ml-2 text-sm font-medium">
                            {assignment?.agent?.firstName}{' '}
                            {assignment?.agent?.lastName}
                          </div>
                        </TableCell>
                        <TableCell>
                          {assignment.numTotalUsers} rep
                          {assignment?.numTotalUsers === 1 ? '' : 's'}
                        </TableCell>
                        <TableCell className="flex items-center">
                          <div style={{ width: '40px' }}>
                            <ProgressCircle
                              color={
                                completedRatio >= 0.75
                                  ? 'emerald'
                                  : completedRatio >= 0.4
                                    ? 'yellow'
                                    : 'red'
                              }
                              value={completedRatio * 100}
                              size="sm"
                            >
                              <span className="text-xs text-gray-700 font-medium">
                                {Number(completedRatio).toLocaleString(
                                  'en-US',
                                  {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0,
                                    style: 'percent',
                                  },
                                )}
                              </span>
                            </ProgressCircle>
                          </div>
                          <div className="text-xs text-muted-foreground ml-1">
                            {assignment.numCompletedCalls}/
                            {assignment.numAssignedCalls} calls done
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size={'sm'}
                                className="w-8 h-8 rounded-full p-0 top-4 right-4"
                              >
                                <MoreVerticalIcon className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="center">
                              <DropdownMenuItem
                                className="cursor-pointer"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  startDeleteAssignment(assignment.agentId);
                                }}
                              >
                                <Trash2 className="w-4 h-4 mr-2 text-muted-foreground" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
      <DeleteConfirmationModal
        open={requestDeleteConfirmation}
        onCancel={handleCancelDeleteAssignment}
        onConfirm={handleDeleteAssignment}
        title={'Delete assignments'}
        description={
          'Are you sure you want to delete all the assignments for this bot?'
        }
      />
    </div>
  );
}
