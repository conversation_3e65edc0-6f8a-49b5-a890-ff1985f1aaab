import { useIntegrations } from '@/hooks/useIntegrations';
import { AgentCallType } from '@/lib/Agent/types';
import IntegrationService, { RealCallsService } from '@/lib/Integrations';
import { IntegrationTeamSettings } from '@/lib/Integrations/types';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { TeamDto } from '@/lib/User/types';
import { useEffect, useRef, useState } from 'react';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import Image from 'next/image';
import { Loader2Icon, Phone, TargetIcon } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Id, toast } from 'react-toastify';
import { RealCallAutoEmailTarget } from '@/lib/Integrations/RealCalls/types';
import { RealCallAutoEmailTargetSelector } from '@/common/RealCallAutoEmailTargetSelector';
import useUserSession from '@/hooks/useUserSession';
import { AppPermissions } from '@/lib/permissions';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';

interface IProps {
  team: TeamDto;
  className?: string;
}

export default function RealCallsSettings({ team, className }: IProps) {
  console.log(team);

  const { canAccess } = useUserSession();
  const toastId = useRef<Id | null>(null);
  const { callTypeOptions } = useCallTypeOptions();
  const [tm, setTm] = useState<TeamDto>();

  const { data: integrations } = useIntegrations();
  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);
  const [isLoadingScorecards, setIsLoadingScorecards] =
    useState<boolean>(false);
  const [canSaveStatus, setCanSaveStatus] = useState<any>({});
  const [savingStatus, setSavingStatus] = useState<any>({});

  useEffect(() => {
    setTm(team);
  }, [team]);

  const fetchData = async () => {
    setIsLoadingScorecards(true);

    const scs = await ScorecardConfigService.getAllScorecardConfigsForOrg();
    setAllScorecards(scs);

    setIsLoadingScorecards(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  /***********************************/
  /************* ACTIONS *************/
  /***********************************/

  const upsertSettings = async (integrationId: number) => {
    setCanSaveStatus({ ...canSaveStatus, [integrationId]: false });
    setSavingStatus({ ...savingStatus, [integrationId]: true });
    let settings: IntegrationTeamSettings | undefined;
    tm?.integrationsSettings?.forEach((s: IntegrationTeamSettings) => {
      if (s.integrationId === integrationId) {
        settings = s;
      }
    });
    if (settings) {
      try {
        settings.teamId = team.id;
        await IntegrationService.upsertTeamSettings(settings);
        // queryClient.invalidateQueries({ queryKey: ["orgUsers", user.id] });
      } catch (e) {
        console.log(e);
      }
    }
    toastId.current = toast.success('Settings successfully saved');
    setSavingStatus({ ...savingStatus, [integrationId]: false });
  };

  const updateSettings = (integrationId: number, key: string, v: string) => {
    setCanSaveStatus({ ...canSaveStatus, [integrationId]: true });
    let found = false;
    tm?.integrationsSettings?.forEach((s: IntegrationTeamSettings) => {
      if (s.integrationId === integrationId) {
        if (key == 'callType') {
          s.callType = v as AgentCallType;
        } else if (key == 'scorecardConfigId') {
          if (v == 'from-team') {
            s.scorecardConfigId = 0;
          } else {
            s.scorecardConfigId = parseInt(v);
          }
        }

        found = true;
      }
    });

    if (!found) {
      if (tm) {
        if (!tm.integrationsSettings) {
          tm.integrationsSettings = [];
        }
        const settings: IntegrationTeamSettings = {
          teamId: tm.id,
          integrationId: integrationId,
          scorecardConfigId: 0,
          callType: AgentCallType.COLD,
        };

        if (key == 'callType') {
          settings.callType = v as AgentCallType;
        } else if (key == 'scorecardConfigId') {
          if (v == 'from-team') {
            settings.scorecardConfigId = 0;
          } else {
            settings.scorecardConfigId = parseInt(v);
          }
        }

        tm.integrationsSettings.push(settings);
      }
    }
    setTm({ ...tm } as TeamDto);
  };

  /********************************/
  /************ RENDER ************/
  /********************************/

  const onChangeAutoEmailTarget = async (
    target: RealCallAutoEmailTarget | '-',
  ) => {
    if (tm) {
      if (target === '-') {
        await RealCallsService.deleteRealCallTeamConfig(tm.id);
        return;
      }
      await RealCallsService.upsertRealCallTeamConfig(tm.id, target);
    }
  };

  return (
    <div className={className}>
      <div className="font-semibold text-base mb-1">Real call settings</div>
      <div className="text-xs text-muted-foreground mb-6">
        Manage how real calls are scored for this team
      </div>
      {canAccess(AppPermissions.MANAGE_REAL_CALLS) && (
        <RealCallAutoEmailTargetSelector
          defaultValue={tm?.realCallConfig?.autoEmailTarget || '-'}
          onSelect={onChangeAutoEmailTarget}
          isLoading={!tm}
        />
      )}
      {integrations?.map((i) => {
        if (i.provider?.needsUserTeamSettings) {
          let settings: IntegrationTeamSettings | undefined;
          tm?.integrationsSettings?.forEach((s: IntegrationTeamSettings) => {
            if (s.integrationId == i.id) {
              settings = s;
            }
          });
          if (!settings) {
            settings = {} as IntegrationTeamSettings;
          }

          if (settings) {
            let selectedCallType: any = 'Select call type';
            callTypeOptions.map((ct) => {
              if (ct.value == settings?.callType) {
                const Icon =
                  CALL_TYPE_TO_ICON[ct.value as keyof typeof CALL_TYPE_TO_ICON]
                    .Icon;
                selectedCallType = (
                  <div className="flex items-center">
                    <div className="mr-1">
                      <Icon size={12} />
                    </div>
                    <div>{ct.label}</div>
                  </div>
                );
              }
            });

            let selectedScorecard = "Use team's default";
            allScorecards.map((option) => {
              if (option.id == settings?.scorecardConfigId) {
                selectedScorecard = option.tag;
              }
            });

            return (
              <div key={i.id} className="mt-6">
                <div className="flex items-center text-base font-semibold mb-4">
                  <Image
                    src={IntegrationService.getProviderLogoUrl(
                      i.provider.logoUrl,
                    )}
                    alt={`${i.provider.companyName} Logo`}
                    width={20}
                    height={20}
                    className="mr-1"
                  />
                  <div>{i.name}</div>
                </div>

                <div className="ml-6">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="font-semibold flex items-center w-[120px]">
                      <Phone size={16} className="mr-2" />
                      Call type
                    </div>
                    <div className="w-[400px]">
                      <Select
                        onValueChange={(v: string) => {
                          updateSettings(i.id, 'callType', v);
                        }}
                        value={settings?.callType}
                      >
                        <SelectTrigger>
                          <div className="mr-2">{selectedCallType}</div>
                        </SelectTrigger>
                        <SelectContent>
                          {callTypeOptions.map((ct) => {
                            const Icon =
                              CALL_TYPE_TO_ICON[
                                ct.value as keyof typeof CALL_TYPE_TO_ICON
                              ].Icon;

                            return (
                              <SelectItem key={ct.value} value={ct.value}>
                                <div className="flex items-center">
                                  <div className="mr-1">
                                    <Icon size={12} />
                                  </div>
                                  <div>{ct.label}</div>
                                </div>
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mb-4">
                    <div className="font-semibold flex items-center w-[120px]">
                      <TargetIcon size={16} className="mr-2" />
                      Scorecard
                    </div>
                    <div className="w-[400px]">
                      <Select
                        onValueChange={(v: string) => {
                          updateSettings(i.id, 'scorecardConfigId', v);
                        }}
                        value={String(settings?.scorecardConfigId)}
                      >
                        <SelectTrigger>
                          <div className="mr-2">{selectedScorecard}</div>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={'from-team'}>
                            Use team&apos;s default
                          </SelectItem>
                          {allScorecards && (
                            <>
                              {allScorecards.map((option) => {
                                return (
                                  <SelectItem
                                    key={option.id}
                                    value={String(option.id)}
                                  >
                                    {option.tag}
                                  </SelectItem>
                                );
                              })}
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="ml-2">
                      {isLoadingScorecards && (
                        <Loader2Icon
                          className="animate-spin text-muted-foreground"
                          size={16}
                        />
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Button
                    disabled={!canSaveStatus[i.id]}
                    onClick={() => upsertSettings(i.id)}
                  >
                    Save {i.name} Settings
                    {savingStatus[i.id] && (
                      <Loader2Icon
                        className="animate-spin text-white ml-2"
                        size={18}
                      />
                    )}
                  </Button>
                </div>
              </div>
            );
          }
        }
      })}
    </div>
  );
}
