import React, { useState, useEffect, useRef } from 'react';
import { TeamDto, TeamRoleEnum, UserTeamDto } from '@/lib/User/types';
import useTeam from '@/hooks/useTeam';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import useOrgUsers from '@/hooks/useOrgUsers';
import { UserDto } from '@/lib/User/types';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Shield,
  CircleX,
  Loader2Icon,
  ArrowDownToLine,
  ShieldCheck,
  ArrowRight,
  ChevronLeft,
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import TeamsService from '@/lib/User/Team';
import { useQueryClient } from '@tanstack/react-query';
import { Id, toast } from 'react-toastify';
import RealCallsSettings from './realCallsSettings';
import { useIntegrations } from '@/hooks/useIntegrations';

interface IEditTeamProps {
  team: TeamDto;
  onClose: () => void;
}

const DEFAULT_NUMBER_OF_RESULTS = 10;

export default function EditTeam({ team, onClose }: IEditTeamProps) {
  const queryClient = useQueryClient();

  if (!team.users) {
    team.users = [];
  }

  const { data: integrations, isLoading: isLoadingIntegrations } =
    useIntegrations();
  const [requiresRealCallsettings, setRequiresRealCallSettings] =
    useState<boolean>(false);

  const [showIntegrationSettings, setShowIntegrationSettings] =
    useState<boolean>(false);
  const [name, setName] = useState<string>(team.name);
  const [description, setDescription] = useState<string>(
    team.description || '',
  );
  const [tm, setTm] = useState<TeamDto>(team);
  const [teamUsers, setTeamUsers] = useState<{ [key: number]: boolean }>({});
  const [numberOfResults, setNumberOfResults] = useState(
    DEFAULT_NUMBER_OF_RESULTS,
  );
  const [canLoadMore, setCanLoadMore] = useState<boolean>(true);
  const [users, setUsers] = useState<UserDto[]>([]);
  const [searchUserStr, setSearchUserStr] = useState<string>('');
  const [searchUserLabel, setSearchUserLabel] = useState<string>('');
  const [canSave, setCanSave] = useState<boolean>(false);
  const errorToastId = useRef<Id | null>(null);

  /**************************************/
  /**************** INIT ****************/
  /**************************************/

  const { data: dbTeam, isLoading: isLoadingTeam } = useTeam(team.id);
  const { data: result, isLoading: isLoadingOrgUsers } = useOrgUsers(
    true,
    0,
    numberOfResults,
    searchUserStr,
  );

  const orgUsers = result?.data || [];

  useEffect(() => {
    if (!isLoadingTeam && dbTeam && team.id) {
      setTm(dbTeam);
      setTeamUsers((old) => {
        const _new: { [key: number]: boolean } = {};
        dbTeam.users?.map((u) => {
          _new[u.id] = true;
        });
        return _new;
      });
    }
  }, [isLoadingTeam, dbTeam, team]);

  useEffect(() => {
    if (!isLoadingOrgUsers && orgUsers) {
      if (orgUsers.length < numberOfResults) {
        setCanLoadMore(false);
      }
      setUsers(orgUsers);
    }
  }, [isLoadingOrgUsers, orgUsers]);

  useEffect(() => {
    if (!isLoadingIntegrations && integrations) {
      let needsIntSettings = false;
      integrations?.map((i) => {
        if (i.provider?.needsUserTeamSettings) {
          needsIntSettings = true;
        }
      });
      setRequiresRealCallSettings(true);
    }
  }, [isLoadingIntegrations, integrations]);

  /**************************************/
  /************** ACTIONS ***************/
  /**************************************/

  const evalCanSave = () => {
    if (tm.name != '') {
      setCanSave(true);
    } else {
      setCanSave(false);
    }
  };

  const updateName = (n: string) => {
    if (n && n != '') {
      setCanSave(true);
    } else {
      setCanSave(false);
    }
    setName(n);
    tm.name = n;
  };

  const updateDescription = (n: string) => {
    setDescription(n);
    tm.description = n;
    evalCanSave();
  };

  const addUser = (user: UserDto) => {
    if (!teamUsers[user.id]) {
      const tUser: UserTeamDto = { ...user, teamRole: TeamRoleEnum.MEMBER };
      tm.users?.push(tUser);
      teamUsers[user.id] = true;
      setTm({ ...tm });
      setTeamUsers({ ...teamUsers });
      evalCanSave();
    }
  };

  const removeUser = (user: UserDto) => {
    if (teamUsers[user.id]) {
      tm.users?.splice(
        tm.users?.findIndex((u) => u.id === user.id),
        1,
      );
      teamUsers[user.id] = false;
      setTm({ ...tm });
      setTeamUsers({ ...teamUsers });
      evalCanSave();
    }
  };

  const toggleRole = (userId: number) => {
    tm.users?.forEach((u) => {
      if (u.id == userId) {
        if (u.teamRole === TeamRoleEnum.ADMIN) {
          u.teamRole = TeamRoleEnum.MEMBER;
        } else {
          u.teamRole = TeamRoleEnum.ADMIN;
        }
      }
    });
    setTm({ ...tm });
    evalCanSave();
  };

  const exit = () => {
    setShowIntegrationSettings(false);
    if (onClose) {
      onClose();
    }
  };

  const save = async () => {
    let ok = true;
    try {
      if (tm.id) {
        await TeamsService.updateTeam(tm);
      } else {
        await TeamsService.createTeam(tm);
      }
    } catch (e) {
      console.log(e);
      ok = false;
      errorToastId.current = toast.error(
        'There was an error. Please try again.',
      );
    }

    if (ok) {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['teams-by-id'] });
      queryClient.invalidateQueries({ queryKey: ['orgUsers'] });

      onClose();
    }
  };

  /**************************************/
  /************** SEARCH USER ***********/
  /**************************************/

  const timeoutSerch = useRef<ReturnType<typeof setTimeout> | null>(null);

  const searchUser = (s: string) => {
    if (timeoutSerch.current) {
      clearTimeout(timeoutSerch.current);
    }

    setSearchUserLabel(s);

    timeoutSerch.current = setTimeout(async () => {
      setSearchUserStr(s);
    }, 200);
  };

  const loadMore = () => {
    setNumberOfResults((old) => old + DEFAULT_NUMBER_OF_RESULTS);
  };

  /**************************************/
  /************** RENDERING *************/
  /**************************************/

  return (
    <div
      className="mt-4"
      style={{
        opacity: isLoadingTeam ? 0 : 1,
        transition: 'opacity 0.5s ease-in-out',
      }}
    >
      {!showIntegrationSettings && (
        <>
          {/**************************/}
          {/******* BASIC INFO *******/}
          {/**************************/}
          <div className="w-3/5">
            <div>
              <Label>Name *</Label>
              <Input
                value={name}
                onChange={(e) => updateName(e.target.value)}
              />
            </div>

            <div className="mt-4">
              <Label>Description</Label>
              <Textarea
                value={description}
                onChange={(e) => updateDescription(e.target.value)}
              />
            </div>
          </div>

          <Separator className="my-8" />

          {/**************************/}
          {/********** USERS *********/}
          {/**************************/}
          <div className="table w-full pb-4">
            {/********** USERS **********/}
            <div className="table-cell w-1/2">
              <div className="flex items-center justify-between mr-6">
                <div>
                  <Label>All members</Label>
                </div>
                <div className=" w-2/5">
                  <Input
                    value={searchUserLabel}
                    onChange={(e) => searchUser(e.target.value)}
                    placeholder="Search members..."
                  />
                </div>
              </div>
              <div className="mt-1 h-[30vh] overflow-auto mr-6">
                {users.length === 0 && !isLoadingOrgUsers ? (
                  <div className="text-center text-muted-foreground">
                    No member found
                  </div>
                ) : (
                  <div>
                    {users.map((user) => {
                      if (!teamUsers[user.id]) {
                        return (
                          <div
                            key={user.id}
                            className="flex items-center hover:bg-muted cursor-pointer group rounded-sm"
                            onClick={() => addUser(user)}
                          >
                            <div
                              key={user.id}
                              className="flex flex-1 items-center space-x-2 p-2  text-sm"
                            >
                              <Avatar className="w-6 h-6">
                                {user?.avatar && (
                                  <AvatarImage src={user?.avatar} />
                                )}
                                <AvatarFallback className="text-xs text-muted-foreground">
                                  {user?.firstName?.charAt(0) || ''}
                                  {user?.lastName?.charAt(0) || ''}
                                </AvatarFallback>
                              </Avatar>
                              <p>
                                {user.firstName} {user.lastName}
                              </p>
                            </div>
                            <div className="invisible group-hover:visible mr-2">
                              <ArrowRight size={18} />
                            </div>
                          </div>
                        );
                      }
                    })}
                    <div>
                      {canLoadMore && (
                        <Button
                          className="text-sm cursor-pointer hover:bg-muted/80 px-2 py-2 flex items-center"
                          onClick={() => {
                            loadMore();
                          }}
                          variant={'ghost'}
                        >
                          {isLoadingOrgUsers ? (
                            <Loader2Icon
                              size={14}
                              className="animate-spin mr-2"
                            />
                          ) : (
                            <ArrowDownToLine size={14} className="mr-2" />
                          )}
                          Load More
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/********** MEMBERS **********/}
            <div className="table-cell">
              <div>
                <Label>Team members</Label>
              </div>
              <div className="mt-1 h-[30vh] overflow-auto">
                {tm.users?.length === 0 && !isLoadingTeam ? (
                  <div className="text-center text-muted-foreground text-sm mt-6">
                    <div>No member found.</div>
                    <div>Click on a user to add them to the team.</div>
                  </div>
                ) : (
                  <div>
                    {tm.users?.map((user) => {
                      let isAdmin = false;
                      if (user.teamRole === TeamRoleEnum.ADMIN) {
                        isAdmin = true;
                      }
                      return (
                        <div key={user.id} className="flex items-center">
                          <div className="flex flex-1 items-center space-x-2 p-2 overflow-hidden text-sm">
                            <Avatar className="w-6 h-6">
                              {user?.avatar && (
                                <AvatarImage src={user?.avatar} />
                              )}
                              <AvatarFallback className="text-xs text-muted-foreground">
                                {user?.firstName?.charAt(0) || ''}
                                {user?.lastName?.charAt(0) || ''}
                              </AvatarFallback>
                            </Avatar>
                            <p>
                              {user.firstName} {user.lastName}
                            </p>
                          </div>
                          <div
                            className="cursor-pointer hover:bg-muted rounded-full p-1"
                            onClick={() => {
                              toggleRole(user.id);
                            }}
                          >
                            {isAdmin ? (
                              <TooltipProvider delayDuration={200}>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <ShieldCheck className="w-6 h-6 text-green-500" />
                                  </TooltipTrigger>
                                  <TooltipContent side="bottom">
                                    <p>Revoke admin privileges</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            ) : (
                              <TooltipProvider delayDuration={500}>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Shield className="w-6 h-6 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent side="bottom">
                                    <p>Grant admin privileges</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )}
                          </div>
                          <div
                            className="mr-2 cursor-pointer hover:bg-muted rounded-full p-1"
                            onClick={() => {
                              removeUser(user as unknown as UserDto);
                            }}
                          >
                            <TooltipProvider delayDuration={200}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <CircleX className="w-6 h-6 text-red-400" />
                                </TooltipTrigger>
                                <TooltipContent side="bottom">
                                  <p>Remove</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}

      {showIntegrationSettings && (
        <RealCallsSettings className="my-4" team={team} />
      )}

      <div className="flex items-center space-x-2">
        {!showIntegrationSettings && (
          <>
            {requiresRealCallsettings && (
              <Button
                variant="outline"
                onClick={() => {
                  setShowIntegrationSettings(true);
                }}
              >
                Real call settings
              </Button>
            )}
            <div className="flex-1" />
            <Button variant="outline" onClick={exit}>
              Back
            </Button>
            <Button disabled={!canSave} onClick={save}>
              Save
            </Button>
          </>
        )}

        {showIntegrationSettings && (
          <>
            <Button
              variant="outline"
              onClick={() => {
                setShowIntegrationSettings(false);
              }}
            >
              <ChevronLeft size={16} className="mr-2" />
              Team settings
            </Button>
            <div className="flex-1" />
          </>
        )}
      </div>
    </div>
  );
}
