import { useState, useRef, useEffect, useCallback } from 'react';
import { Users, Edit2, Loader2Icon, PlusCircle, X, Trash2 } from 'lucide-react';
import {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogFooter,
} from '@/components/ui/dialog';
import useTeams from '@/hooks/useTeams';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TeamDto } from '@/lib/User/types';
import EditTeam from './EditTeam';
import TeamsService from '@/lib/User/Team';
import { useQueryClient } from '@tanstack/react-query';
import { Id, toast } from 'react-toastify';

interface ITeamsDialogProps {
  open: boolean;
  close: () => void;
}

const newTeamSkeleton = {
  name: '',
  description: '',
  numberOfUsers: 0,
} as TeamDto;

export default function TeamsDialog({ open, close }: ITeamsDialogProps) {
  const queryClient = useQueryClient();
  const [pageTitle, setPageTitle] = useState<string>('Teams');
  const [searchStr, setSearchStr] = useState<string>('');
  const [searchLabel, setSearchLabel] = useState<string>('');
  const { data: allTeams, isLoading: isLoadingTeams } = useTeams(
    0,
    10,
    searchStr,
    true,
  );
  const [teams, setTeams] = useState<TeamDto[]>([]);
  const [editingTeam, setEditingTeam] = useState<TeamDto | null>(null);
  const [deleteTeam, setDeleteTeam] = useState<TeamDto | null>(null);
  const errorToastId = useRef<Id | null>(null);

  useEffect(() => {
    if (!isLoadingTeams && allTeams) {
      setTeams(allTeams);
      if (allTeams.length === 0) {
        setPageTitle('Create your first team');
      }
    }
  }, [isLoadingTeams, allTeams]);

  /**************************************/
  /*************** SERCH ***************/
  /**************************************/

  const timeoutSerch = useRef<ReturnType<typeof setTimeout>>();

  const search = (s: string) => {
    if (timeoutSerch.current) {
      clearTimeout(timeoutSerch.current);
    }

    setSearchLabel(s);

    timeoutSerch.current = setTimeout(async () => {
      setSearchStr(s);
    }, 200);
  };

  /**************************************/
  /*************** EDITING **************/
  /**************************************/

  const startEditTeam = (team: TeamDto) => {
    setPageTitle(team.name);
    setEditingTeam({ ...team });
  };

  const closeEditTeam = () => {
    setPageTitle('Teams');
    setEditingTeam(null);
  };

  const createNewTeam = () => {
    setPageTitle('New Team');
    setEditingTeam({ ...newTeamSkeleton });
  };

  const startDeleteTeam = (team: TeamDto) => {
    setDeleteTeam(team);
  };

  const cancelDeleteTeam = () => {
    setDeleteTeam(null);
  };

  const doDeleteTeam = async () => {
    let ok = true;
    try {
      if (deleteTeam && deleteTeam.id) {
        await TeamsService.deleteTeam(deleteTeam.id);
      }
    } catch (e) {
      console.log(e);
      ok = false;
      errorToastId.current = toast.error(
        'There was an error. Please try again.',
      );
    }

    if (ok) {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['teams-by-id'] });
      queryClient.invalidateQueries({ queryKey: ['orgUsers'] });
      setDeleteTeam(null);
    }
  };

  /***********************************/
  /********** DIALOG EVENTS **********/
  /***********************************/

  const closeDialog = () => {
    close();
    setEditingTeam(null);
    setPageTitle('Teams');
  };

  //ESC key to close dialog
  const escFunction = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' || event.key === 'Esc' || event.keyCode === 27) {
      closeDialog();
    }
  }, []);

  // MOUSE SCROLL - with position:fixed container, mouse wheel wont work, we need to use JS:

  useEffect(() => {
    document.addEventListener('keydown', escFunction, false);

    return () => {
      document.removeEventListener('keydown', escFunction, false);
    };
  }, [escFunction]);

  const scrollableContainer = useRef<HTMLDivElement>(null);
  useEffect(() => {
    window.addEventListener('wheel', scrollContent);

    return () => {
      window.removeEventListener('wheel', scrollContent);
    };
  }, []);

  const scrollContent = (e: WheelEvent) => {
    if (scrollableContainer.current) {
      scrollableContainer.current.scrollTop += e.deltaY;
    }
  };

  /**************************************/
  /************** RENDERING *************/
  /**************************************/

  return (
    <Dialog open={open}>
      <DialogPortal>
        <DialogOverlay />

        <div className="py-4 px-6 w-[50vw] fixed items-center left-[50%] top-[50%] z-50 translate-x-[-50%] translate-y-[-50%] border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg">
          {/* HEADER */}
          <div className="px-2 flex pt-2">
            <div className="flex-1 flex items-center font-semibold text-lg">
              <Users className="mr-2 " />
              {pageTitle}
            </div>
            <div className="cursor-pointer" onClick={closeDialog}>
              <X size={18} />
            </div>
          </div>

          {/* CONTENT */}
          <div className="px-1 pb-2">
            {teams.length > 0 && !editingTeam && !deleteTeam ? (
              <div className="mt-6">
                {/*****************************/}
                {/********** CMD LINE *********/}
                {/*****************************/}
                <div className="flex items-center">
                  <div className="max-w-[50%] flex-1">
                    <Input
                      value={searchLabel}
                      onChange={(e) => search(e.target.value)}
                      placeholder="Search..."
                    />
                  </div>
                  <div className="ml-2">
                    {isLoadingTeams && <Loader2Icon className="animate-spin" />}
                  </div>
                  <div className="flex-1"></div>
                  <Button variant={'default'} onClick={createNewTeam}>
                    <PlusCircle className="w-4 h-4 mr-2" />
                    New
                  </Button>
                </div>

                {/*****************************/}
                {/************ TABLE **********/}
                {/*****************************/}
                <div
                  className="max-h-[50vh] overflow-auto mb-4 mt-4"
                  ref={scrollableContainer}
                >
                  <Table className="">
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Number of members</TableHead>
                        <TableHead>&nbsp;</TableHead>
                      </TableRow>
                    </TableHeader>
                    {/*****************************/}
                    {/************ BODY ***********/}
                    {/*****************************/}
                    <TableBody
                      style={{
                        opacity: isLoadingTeams ? 0 : 1,
                        transition: 'opacity 0.5s ease-in-out',
                      }}
                    >
                      {teams.map((team) => {
                        return (
                          <TableRow key={team.id}>
                            <TableCell>{team.name}</TableCell>
                            <TableCell>{team.description}</TableCell>
                            <TableCell>{team.numberOfUsers}</TableCell>
                            <TableCell className="flex items-center justify-end">
                              <Button
                                variant={'ghost'}
                                onClick={() => {
                                  startEditTeam(team);
                                }}
                              >
                                <Edit2 className="w-4 h-4" />
                              </Button>
                              <Button
                                variant={'ghost'}
                                onClick={() => {
                                  startDeleteTeam(team);
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </div>
            ) : (
              !editingTeam &&
              !deleteTeam && (
                <EditTeam team={newTeamSkeleton} onClose={closeEditTeam} />
              )
            )}

            {/*****************************/}
            {/******** EDIT TEAM **********/}
            {/*****************************/}
            {editingTeam && !deleteTeam && (
              <>
                <EditTeam team={editingTeam} onClose={closeEditTeam} />
              </>
            )}

            {/*****************************/}
            {/******** DELETE TEAM ********/}
            {/*****************************/}
            {!editingTeam && deleteTeam && (
              <>
                <div className="my-10">
                  Do you really want to delete {deleteTeam.name}?
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={cancelDeleteTeam}>
                    Back
                  </Button>
                  <Button variant="destructive" onClick={doDeleteTeam}>
                    Delete
                  </Button>
                </DialogFooter>
              </>
            )}
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
