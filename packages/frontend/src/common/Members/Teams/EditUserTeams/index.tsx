import React, { useState, useEffect, useRef } from 'react';
import { TeamDto, TeamRoleEnum } from '@/lib/User/types';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Edit2, Plus, Shield } from 'lucide-react';
import useTeams from '@/hooks/useTeams';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import AddNew from './AddNew';
import { cn } from '@/lib/utils';
import TeamsService from '@/lib/User/Team';
import SelectTeam from './SelectTeam';
import { Id, toast } from 'react-toastify';
import { useQueryClient } from '@tanstack/react-query';
import { Badge } from '@/components/ui/badge';
import useUserSession from '@/hooks/useUserSession';

interface IEditUserTeamsProps {
  currentTeams: TeamDto[];
  userId: number;
}

function EditUserTeams({ currentTeams, userId }: IEditUserTeamsProps) {
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);

  const [teams, setTeams] = useState<TeamDto[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<TeamDto[]>([]);

  const sessionUser = useUserSession();

  const [selectedTeamsById, setSelectedTeamsById] = useState<{
    [key: number]: boolean;
  }>({});
  const [searchStr, setSearchStr] = useState<string>('');
  const [searchLabel, setSearchLabel] = useState<string>('');

  const errorToastId = useRef<Id | null>(null);

  useEffect(() => {
    const selecteById: { [key: number]: boolean } = {};
    const selected: TeamDto[] = [];
    currentTeams?.forEach((t) => {
      if (t) {
        selecteById[t.id] = true;
        selected.push(t);
      }
    });

    setSelectedTeams(selected);
    setSelectedTeamsById(selecteById);
  }, [currentTeams]);

  /**************************************/
  /**************** INIT ****************/
  /**************************************/

  const { data: allTeams, isLoading: isLoadingTeams } = useTeams(
    0,
    10,
    searchStr,
    true,
  );

  useEffect(() => {
    if (!isLoadingTeams && allTeams) {
      setTeams(allTeams);
    }
  }, [isLoadingTeams, allTeams]);

  /**************************************/
  /*************** SERCH ***************/
  /**************************************/

  const timeoutSerch = useRef<ReturnType<typeof setTimeout>>();

  const search = (s: string) => {
    if (timeoutSerch.current) {
      clearTimeout(timeoutSerch.current);
    }

    setSearchLabel(s);

    timeoutSerch.current = setTimeout(async () => {
      setSearchStr(s);
    }, 200);
  };

  /**************************************/
  /************** ACTIONS **************/
  /**************************************/

  const pushNewTeam = (team: TeamDto) => {
    setTeams((old) => [...old, team]);
  };

  const toggleTeam = async (team: TeamDto, isAdmin: boolean) => {
    if (selectedTeamsById[team.id]) {
      setSelectedTeams((old) => {
        return old.filter((t) => t.id !== team.id);
      });
      setSelectedTeamsById((old) => ({ ...old, [team.id]: false }));
      try {
        await TeamsService.removeUserFromTeam({
          userId,
          teamId: team.id,
        });
      } catch (err) {
        console.log(err);
        errorToastId.current = toast.error(
          'There was an error processing your request. Please try again.',
        );
      }
    } else {
      setSelectedTeams((old) => [...old, team]);
      setSelectedTeamsById((old) => ({ ...old, [team.id]: true }));

      let role = TeamRoleEnum.MEMBER;
      if (isAdmin) {
        role = TeamRoleEnum.ADMIN;
      }

      try {
        await TeamsService.addUserToTeam({
          userId,
          teamId: team.id,
          role,
        });
      } catch (err) {
        console.log(err);
        errorToastId.current = toast.error(
          'There was an error processing your request. Please try again.',
        );
      }
    }
    queryClient.invalidateQueries({ queryKey: ['teams'] });
    queryClient.invalidateQueries({ queryKey: ['teams-by-id'] });
    queryClient.invalidateQueries({ queryKey: ['orgUsers'] });
  };

  /**************************************/
  /*************** RENDER ***************/
  /**************************************/

  if (!sessionUser.isAdmin) {
    return (
      <div className="flex items-center flex-wrap">
        {selectedTeams.map((team, i) => {
          let icon = undefined;
          if (team.role == TeamRoleEnum.ADMIN) {
            icon = <Shield size={16} className="text-muted-foreground ml-1" />;
          }
          return (
            <Badge
              key={team.id}
              variant={'secondary'}
              className="flex items-center mr-1"
            >
              {team.name}
              {icon}
            </Badge>
          );
        })}
      </div>
    );
  } else {
    return (
      <div
        onClick={(e) => {
          console.log('click');
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger
            asChild
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            {selectedTeams.length == 0 ? (
              <Button variant={'ghost'} className="flex justify-center">
                <Plus size={'9.3px'} className="text-[#71717A] mr-1" />
                <span className="text-[#71717A] text-sm">Add to teams</span>
              </Button>
            ) : (
              <div className="flex items-center flex-wrap">
                {selectedTeams.map((team, i) => {
                  let icon = undefined;
                  if (team.role == TeamRoleEnum.ADMIN) {
                    icon = (
                      <Shield
                        size={16}
                        className="text-muted-foreground ml-1"
                      />
                    );
                  }
                  return (
                    <Badge
                      key={team.id}
                      variant={'secondary'}
                      className="flex items-center mr-1 mb-1"
                    >
                      {team.name}
                      {icon}
                    </Badge>
                  );
                })}
              </div>
            )}
          </PopoverTrigger>
          <PopoverContent align="start" className="w-[240px] p-0">
            <div className="">
              <Input
                value={searchLabel}
                onChange={(e) => search(e.target.value)}
                placeholder="Search..."
                className="border-0 shadow-none focus-visible:ring-0"
              />
            </div>
            <Separator />
            {teams.length == 0 ? (
              <div className="text-center text-muted-foreground text-sm py-2">
                No team found
              </div>
            ) : (
              <div className="py-1">
                {teams.map((team) => {
                  return (
                    <SelectTeam
                      key={team.id}
                      team={team}
                      selected={selectedTeamsById[team.id]}
                      toggleTeam={toggleTeam}
                    />
                  );
                })}
              </div>
            )}
            <Separator />
            <AddNew addTeam={pushNewTeam} />
          </PopoverContent>
        </Popover>
      </div>
    );
  }
}

export default EditUserTeams;
