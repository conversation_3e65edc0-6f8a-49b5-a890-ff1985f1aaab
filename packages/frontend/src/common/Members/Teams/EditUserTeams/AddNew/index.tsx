import { useState, useEffect, useRef } from 'react';
import {
  Plus,
  Loader2Icon,
  ChevronLeft,
  ChevronRight,
  ArrowRight,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import TeamsService from '@/lib/User/Team';
import { Id, toast } from 'react-toastify';
import { TeamDto } from '@/lib/User/types';
import { useQueryClient } from '@tanstack/react-query';

interface IAddNewProps {
  addTeam: (team: TeamDto) => void;
}

export default function AddNew({ addTeam }: IAddNewProps) {
  const queryClient = useQueryClient();

  const inputRef = useRef<HTMLInputElement>(null);

  const [current, setCurrent] = useState<number>(0);
  const [name, setName] = useState<string>('');
  const [isNameEmpty, setIsNameEmpty] = useState<boolean>(false);
  const errorToastId = useRef<Id | null>(null);

  const previous = () => {
    if (current !== 0) {
      setCurrent(current - 1);
    }
  };

  const next = () => {
    if (current === 0) {
      setCurrent(current + 1);
      //important, wait for the animation to finish
      setTimeout(() => {
        inputRef.current?.focus();
      }, 700);
    } else if (current == 1 && name === '') {
      setIsNameEmpty(true);
    } else {
      save();
    }
  };

  const save = async () => {
    try {
      const nt = await TeamsService.createTeam({
        name,
      });
      if (addTeam) {
        addTeam(nt);
      }
    } catch (err) {
      console.log(err);
      errorToastId.current = toast.error(
        'There was an error creating this team. Please try again.',
      );
    }

    queryClient.invalidateQueries({ queryKey: ['teams'] });
    queryClient.invalidateQueries({ queryKey: ['teams-by-id'] });
    queryClient.invalidateQueries({ queryKey: ['orgUsers'] });
    setCurrent(0);
    setName('');
  };

  return (
    <div className="overflow-hidden">
      <div
        className={`flex text-sm transition ease-out duration-600`}
        style={{ transform: `translateX(-${current * 100}%)` }}
      >
        {/********************/}
        {/*** DEFAULT BTN ****/}
        {/********************/}
        <div
          className="text-muted-foreground flex items-center pl-2 py-2 hover:bg-muted cursor-pointer shrink-0 w-full"
          onClick={next}
        >
          <Plus className="mr-1" size={18} /> New team
        </div>

        {/********************/}
        {/***** EDIT NAME ****/}
        {/********************/}
        <div className="flex items-center shrink-0 w-full ">
          <div
            onClick={previous}
            className="hover:bg-muted cursor-pointer h-full flex items-center px-1"
          >
            <ChevronLeft size={18} />
          </div>
          <div className="flex-1 flex items-center">
            <div
              className={`text-muted-foreground ${isNameEmpty && 'text-red-500'}`}
            >
              Name:
            </div>
            <Input
              ref={inputRef}
              value={name}
              onChange={(e) => {
                setIsNameEmpty(false);
                setName(e.target.value);
              }}
              className="border-0 shadow-none focus-visible:ring-0"
              onKeyDown={(event) => {
                if (event.key === 'Enter') {
                  if (event.code === 'Enter') {
                    next();
                  }
                }
              }}
            />
          </div>
          <div
            onClick={next}
            className="hover:bg-muted cursor-pointer h-full flex items-center px-1"
          >
            <ArrowRight size={18} />
          </div>
        </div>

        {/**********************/}
        {/******* SAVING *******/}
        {/**********************/}
        <div className="flex justify-center items-center shrink-0 w-full ">
          <Loader2Icon size={18} className="animate-spin" />
        </div>
      </div>
    </div>
  );
}
