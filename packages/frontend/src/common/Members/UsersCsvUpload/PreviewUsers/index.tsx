/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from '@/components/ui/button';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { ChevronLeft, ChevronRight, Send, Trash2, Undo } from 'lucide-react';
import { useEffect, useState } from 'react';
import { cn, isEmailAddress } from '@/lib/utils';
import Checkbox from '@/components/ui/Hyperbound/checkbox-with-label';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  cancel: () => void;
  columnsMapping: any;
  csv: any[];
  send: (list: any[]) => void;
}

export default function PreviewUsers({
  columnsMapping,
  csv,
  cancel,
  send,
}: IProps) {
  const [allRows, setAllRows] = useState<any[]>([]);
  const [skip, setSkip] = useState<number[]>([]);
  const [forceSkip, setForceSkip] = useState<number[]>([]);
  const [start, setStart] = useState<number>(0);
  const usersPerPage = 6;
  const [skipInviteProcess, setSkipInviteProcess] = useState<boolean>(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const { isManagerHierarchyEnabled } = useUserSession();

  const validateParentTeamConsistency = (
    csvData: any[],
    columnsMapping: any,
  ) => {
    if (!isManagerHierarchyEnabled) {
      return [];
    }

    const teamParentMap: { [teamName: string]: string | null } = {};
    const conflicts: string[] = [];

    for (const row of csvData) {
      const team = row[columnsMapping.team]?.trim();
      const parentTeam = row[columnsMapping.parentTeam]?.trim() || null;

      if (team) {
        if (teamParentMap[team]) {
          const existingParent = teamParentMap[team];
          if (existingParent !== parentTeam) {
            const existingParentDisplay = existingParent || 'no parent team';
            const currentParentDisplay = parentTeam || 'no parent team';
            conflicts.push(
              `Team "${team}" has conflicting parent teams: "${existingParentDisplay}" and "${currentParentDisplay}"`,
            );
          }
        } else {
          teamParentMap[team] = parentTeam;
        }
      }
    }

    return conflicts;
  };

  useEffect(() => {
    setStart(0);
    const tmp: number[] = [];
    let i = 0;

    // Validate parent team consistency
    const errors = validateParentTeamConsistency(csv, columnsMapping);
    setValidationErrors(errors);

    for (const e of csv) {
      const email = e[columnsMapping.email];
      let doSkip = true;
      if (email) {
        if (isEmailAddress(email)) {
          doSkip = false;
        }
      }

      if (doSkip) {
        tmp.push(i);
      }

      i++;
    }
    setForceSkip(tmp);
    setAllRows(csv);
  }, [csv, columnsMapping, isManagerHierarchyEnabled]);

  const toggleSkip = (i: number) => {
    const tmp: number[] = [];
    let found = false;
    for (const k of skip) {
      if (k != i) {
        tmp.push(k);
      } else {
        found = true;
      }
    }

    if (!found) {
      tmp.push(i);
    }
    setSkip([...tmp]);
  };

  const next = () => {
    const ns = start + usersPerPage;
    if (ns <= allRows.length) {
      setStart(ns);
    }
  };

  const prev = () => {
    const ns = start - usersPerPage;
    if (ns > 0) {
      setStart(ns);
    } else {
      setStart(0);
    }
  };

  const prepAndSend = () => {
    const tmp: any[] = [];
    let i = 0;
    for (const e of csv) {
      if (skip.indexOf(i) < 0) {
        if (forceSkip.indexOf(i) < 0) {
          const email = e[columnsMapping.email];
          const team = e[columnsMapping.team];
          const parentTeam = e[columnsMapping?.parentTeam];
          const role = e[columnsMapping.roles];
          const region = e[columnsMapping?.region];
          const companyJoiningDate = new Date(
            e[columnsMapping?.companyJoiningDate],
          );
          tmp.push({
            email,
            team,
            parentTeam,
            role,
            region,
            companyJoiningDate,
            skipInviteProcess,
          });
        }
      }
      i++;
    }
    send(tmp);
  };

  return (
    <div>
      <div className="text-muted-foreground text-sm mb-6">
        Review and edit the content of your file
      </div>

      {validationErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center mb-2">
            <span className="text-red-600 font-semibold">
              ❌ Parent Team Conflicts Detected:
            </span>
          </div>
          <ul className="list-disc list-inside text-red-700">
            {validationErrors.map((error, index) => (
              <li key={index} className="text-sm">
                {error}
              </li>
            ))}
          </ul>
          <p className="text-red-600 text-sm mt-2">
            Please fix these conflicts in your CSV before proceeding.
          </p>
        </div>
      )}

      <div className="max-h-[400px] overflow-x-auto">
        <Table fitToContent={true}>
          <TableContent>
            <TableRow>
              <TableCellHead>&nbsp;</TableCellHead>
              <TableCellHead>Email</TableCellHead>
              <TableCellHead>Team</TableCellHead>
              {isManagerHierarchyEnabled && (
                <TableCellHead>Parent Team</TableCellHead>
              )}
              <TableCellHead>Roles</TableCellHead>
              <TableCellHead>Region</TableCellHead>
              <TableCellHead>Company Joining Date</TableCellHead>
              <TableCellHead>&nbsp;</TableCellHead>
            </TableRow>

            {allRows.map((r: string[], i: number) => {
              if (i >= start && i < start + usersPerPage) {
                const email = r[columnsMapping.email];
                let skipUser = false;
                if (skip.indexOf(i) > -1) {
                  skipUser = true;
                }
                let forceSkipUser = false;
                if (forceSkip.indexOf(i) > -1) {
                  forceSkipUser = true;
                  skipUser = false;
                }
                if (email) {
                  return (
                    <TableRow key={email}>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {i + 1}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn({
                            'text-muted-foreground line-through': skipUser,
                            'text-muted-foreground': forceSkipUser,
                          })}
                        >
                          {r[columnsMapping.email]}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn({
                            'text-muted-foreground line-through': skipUser,
                            'text-muted-foreground': forceSkipUser,
                          })}
                        >
                          {r[columnsMapping.team]}
                        </span>
                      </TableCell>
                      {isManagerHierarchyEnabled && (
                        <TableCell>
                          <span
                            className={cn({
                              'text-muted-foreground line-through': skipUser,
                              'text-muted-foreground': forceSkipUser,
                            })}
                          >
                            {r[columnsMapping.parentTeam]}
                          </span>
                        </TableCell>
                      )}
                      <TableCell>
                        <span
                          className={cn({
                            'text-muted-foreground line-through': skipUser,
                            'text-muted-foreground': forceSkipUser,
                          })}
                        >
                          {r[columnsMapping.roles]}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn({
                            'text-muted-foreground line-through': skipUser,
                            'text-muted-foreground': forceSkipUser,
                          })}
                        >
                          {r[columnsMapping.region]}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn({
                            'text-muted-foreground line-through': skipUser,
                            'text-muted-foreground': forceSkipUser,
                          })}
                        >
                          {r[columnsMapping.companyJoiningDate]}
                        </span>
                      </TableCell>
                      <TableCell>
                        {!forceSkipUser && (
                          <span
                            onClick={() => {
                              toggleSkip(i);
                            }}
                            className="cursor-pointer hover:text-muted-foreground"
                          >
                            {skipUser ? (
                              <Undo size={16} />
                            ) : (
                              <Trash2 size={16} />
                            )}
                          </span>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                }
              }
            })}
          </TableContent>
          <TableFooter>
            <div className="flex items-center select-none">
              <div className="flex-1"></div>
              <div className="flex items-center">
                <div
                  className="cursor-pointer border p-2 rounded-lg hover:bg-gray-50"
                  onClick={prev}
                >
                  <ChevronLeft size={16} />
                </div>
                <div className="text-xs text-muted-foreground mx-2">
                  {start + 1} to {start + usersPerPage} of {allRows.length}
                </div>
                <div
                  className="cursor-pointer border p-2 rounded-lg hover:bg-gray-50"
                  onClick={next}
                >
                  <ChevronRight size={16} />
                </div>
              </div>
              <div className="flex-1"></div>
            </div>
          </TableFooter>
        </Table>
      </div>
      <Checkbox
        checked={skipInviteProcess}
        onToggle={() => {
          setSkipInviteProcess(!skipInviteProcess);
        }}
        className="my-3 text-muted-foreground"
      >
        <div className="flex flex-col -mb-3">
          <span>Skip invite process</span>
          <span className="text-xs">
            (user won&apos;t need to accept the invitation)
          </span>
        </div>
      </Checkbox>
      <div className="mb-4 flex items-center justify-end mt-6">
        <Button
          className="mr-2"
          variant={'outline'}
          onClick={() => {
            cancel();
          }}
        >
          Cancel
        </Button>
        <Button onClick={prepAndSend} disabled={validationErrors.length > 0}>
          <Send size={16} className="mr-2" />
          Invite
        </Button>
      </div>
    </div>
  );
}
