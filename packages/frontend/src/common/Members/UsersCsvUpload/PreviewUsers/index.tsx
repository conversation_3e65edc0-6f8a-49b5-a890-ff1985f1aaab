import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import {
  ChevronLeft,
  ChevronRight,
  Send,
  Trash2,
  Undo,
  Undo2,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { cn, isEmailAddress } from '@/lib/utils';

interface IProps {
  cancel: () => void;
  columnsMapping: any;
  csv: any[];
  send: (list: any[]) => void;
}

export default function PreviewUsers({
  columnsMapping,
  csv,
  cancel,
  send,
}: IProps) {
  const [allRows, setAllRows] = useState<any[]>([]);
  const [skip, setSkip] = useState<number[]>([]);
  const [forceSkip, setForceSkip] = useState<number[]>([]);
  const [start, setStart] = useState<number>(0);
  const usersPerPage = 6;

  useEffect(() => {
    setStart(0);
    const tmp: number[] = [];
    let i = 0;
    for (const e of csv) {
      const email = e[columnsMapping.email];
      let doSkip = true;
      if (email) {
        if (isEmailAddress(email)) {
          doSkip = false;
        }
      }

      if (doSkip) {
        tmp.push(i);
      }

      i++;
    }
    setForceSkip(tmp);
    setAllRows(csv);
  }, [csv]);

  const toggleSkip = (i: number) => {
    const tmp: number[] = [];
    let found = false;
    for (const k of skip) {
      if (k != i) {
        tmp.push(k);
      } else {
        found = true;
      }
    }

    if (!found) {
      tmp.push(i);
    }
    setSkip([...tmp]);
  };

  const next = () => {
    const ns = start + usersPerPage;
    if (ns <= allRows.length) {
      setStart(ns);
    }
  };

  const prev = () => {
    const ns = start - usersPerPage;
    if (ns > 0) {
      setStart(ns);
    } else {
      setStart(0);
    }
  };

  const prepAndSend = () => {
    const tmp: any[] = [];
    let i = 0;
    for (const e of csv) {
      if (skip.indexOf(i) < 0) {
        if (forceSkip.indexOf(i) < 0) {
          const email = e[columnsMapping.email];
          const team = e[columnsMapping.team];
          const role = e[columnsMapping.roles];
          tmp.push({ email, team, role });
        }
      }
      i++;
    }
    send(tmp);
  };

  return (
    <div>
      <div className="text-muted-foreground text-sm mb-6">
        Review and edit the content of your file
      </div>

      <div className="max-h-[400px] overflow-hidden">
        <Table fitToContent={true}>
          <TableContent>
            <TableRow>
              <TableCellHead>&nbsp;</TableCellHead>
              <TableCellHead>Email</TableCellHead>
              <TableCellHead>Team</TableCellHead>
              <TableCellHead>Roles</TableCellHead>
              <TableCellHead>&nbsp;</TableCellHead>
            </TableRow>

            {allRows.map((r: string[], i: number) => {
              if (i >= start && i < start + usersPerPage) {
                const email = r[columnsMapping.email];
                let skipUser = false;
                if (skip.indexOf(i) > -1) {
                  skipUser = true;
                }
                let forceSkipUser = false;
                if (forceSkip.indexOf(i) > -1) {
                  forceSkipUser = true;
                  skipUser = false;
                }
                if (email) {
                  return (
                    <TableRow key={email}>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {i + 1}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn({
                            'text-muted-foreground line-through': skipUser,
                            'text-muted-foreground': forceSkipUser,
                          })}
                        >
                          {r[columnsMapping.email]}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn({
                            'text-muted-foreground line-through': skipUser,
                            'text-muted-foreground': forceSkipUser,
                          })}
                        >
                          {r[columnsMapping.team]}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn({
                            'text-muted-foreground line-through': skipUser,
                            'text-muted-foreground': forceSkipUser,
                          })}
                        >
                          {r[columnsMapping.roles]}
                        </span>
                      </TableCell>
                      <TableCell>
                        {!forceSkipUser && (
                          <span
                            onClick={() => {
                              toggleSkip(i);
                            }}
                            className="cursor-pointer hover:text-muted-foreground"
                          >
                            {skipUser ? (
                              <Undo size={16} />
                            ) : (
                              <Trash2 size={16} />
                            )}
                          </span>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                }
              }
            })}
          </TableContent>
          <TableFooter>
            <div className="flex items-center select-none">
              <div className="flex-1"></div>
              <div className="flex items-center">
                <div
                  className="cursor-pointer border p-2 rounded-lg hover:bg-gray-50"
                  onClick={prev}
                >
                  <ChevronLeft size={16} />
                </div>
                <div className="text-xs text-muted-foreground mx-2">
                  {start + 1} to {start + usersPerPage} of {allRows.length}
                </div>
                <div
                  className="cursor-pointer border p-2 rounded-lg hover:bg-gray-50"
                  onClick={next}
                >
                  <ChevronRight size={16} />
                </div>
              </div>
              <div className="flex-1"></div>
            </div>
          </TableFooter>
        </Table>
      </div>
      <div className="mb-4 flex items-center justify-end mt-6">
        <Button
          className="mr-2"
          variant={'outline'}
          onClick={() => {
            cancel();
          }}
        >
          Cancel
        </Button>
        <Button onClick={prepAndSend}>
          <Send size={16} className="mr-2" />
          Invite
        </Button>
      </div>
    </div>
  );
}
