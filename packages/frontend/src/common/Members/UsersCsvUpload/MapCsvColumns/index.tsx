/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';
import useUserSession from '@/hooks/useUserSession';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';

interface IProps {
  cancel: () => void;
  onMap: (mapping: any) => void;
  csv: any[];
}

export default function MapCsvColumns({ cancel, onMap, csv }: IProps) {
  const [canSave, setCanSave] = useState<boolean>(false);
  const [columns, setColumns] = useState<string[]>([]);
  const [columnsMapping, setColumnsMapping] = useState<any>({});
  const { isManagerHierarchyEnabled } = useUserSession();

  useEffect(() => {
    if (csv && csv.length > 0) {
      setColumns(csv[0]);
    }
  }, [csv]);

  const verifyCanSave = (cf: any) => {
    if (cf.email || (cf.email == 0 && cf.team)) {
      setCanSave(true);
    }
  };

  return (
    <div>
      <div className="text-muted-foreground text-sm mb-6">
        Please match column names from your .CSV accordingly to our types.
      </div>
      <div className="">
        <Table fitToContent={true} hideFooter={true}>
          <TableContent className="text-sm">
            <TableRow>
              <TableCellHead>Hyperbound field names</TableCellHead>
              <TableCellHead>Column name</TableCellHead>
            </TableRow>
            <TableRow>
              <TableCell>Email</TableCell>
              <TableCell>
                <Select
                  onValueChange={(value: string) => {
                    setColumnsMapping({
                      ...columnsMapping,
                      email: value,
                    });
                    verifyCanSave({
                      ...columnsMapping,
                      email: value,
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Map to csv" />
                  </SelectTrigger>
                  <SelectContent>
                    {columns.map((c: string, i: number) => {
                      return (
                        <SelectItem key={i} value={String(i)}>
                          {c}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Team</TableCell>
              <TableCell>
                <Select
                  onValueChange={(value: string) => {
                    setColumnsMapping({
                      ...columnsMapping,
                      team: parseInt(value),
                    });
                    verifyCanSave({ ...columnsMapping, team: parseInt(value) });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Map to csv" />
                  </SelectTrigger>
                  <SelectContent>
                    {columns.map((c: string, i: number) => {
                      return (
                        <SelectItem key={i} value={String(i)}>
                          {c}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </TableCell>
            </TableRow>
            {isManagerHierarchyEnabled && (
              <TableRow>
                <TableCell>Parent Team</TableCell>
                <TableCell>
                  <Select
                    onValueChange={(value: string) => {
                      setColumnsMapping({
                        ...columnsMapping,
                        parentTeam: parseInt(value),
                      });
                      verifyCanSave({
                        ...columnsMapping,
                        parentTeam: parseInt(value),
                      });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Map to csv" />
                    </SelectTrigger>
                    <SelectContent>
                      {columns.map((c: string, i: number) => {
                        return (
                          <SelectItem key={i} value={String(i)}>
                            {c}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </TableCell>
              </TableRow>
            )}
            <TableRow>
              <TableCell>Roles</TableCell>
              <TableCell>
                <Select
                  onValueChange={(value: string) => {
                    setColumnsMapping({
                      ...columnsMapping,
                      roles: value,
                    });
                    verifyCanSave({
                      ...columnsMapping,
                      roles: value,
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Map to csv" />
                  </SelectTrigger>
                  <SelectContent>
                    {columns.map((c: string, i: number) => {
                      return (
                        <SelectItem key={i} value={String(i)}>
                          {c}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Region</TableCell>
              <TableCell>
                <Select
                  onValueChange={(value: string) => {
                    setColumnsMapping({
                      ...columnsMapping,
                      region: value,
                    });
                    verifyCanSave({ ...columnsMapping, region: value });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Map to csv" />
                  </SelectTrigger>
                  <SelectContent>
                    {columns.map((c: string, i: number) => {
                      return (
                        <SelectItem key={i} value={String(i)}>
                          {c}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Company Joining Date</TableCell>
              <TableCell>
                <Select
                  onValueChange={(value: string) => {
                    setColumnsMapping({
                      ...columnsMapping,
                      companyJoiningDate: value,
                    });
                    verifyCanSave({
                      ...columnsMapping,
                      companyJoiningDate: value,
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Map to csv" />
                  </SelectTrigger>
                  <SelectContent>
                    {columns.map((c: string, i: number) => {
                      return (
                        <SelectItem key={i} value={String(i)}>
                          {c}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </TableCell>
            </TableRow>
          </TableContent>
          <TableFooter>
            <span />
          </TableFooter>
        </Table>
      </div>
      <div className="mb-4 flex items-center justify-end mt-6">
        <Button
          className="mr-2"
          variant={'outline'}
          onClick={() => {
            cancel();
          }}
        >
          Cancel
        </Button>
        <Button
          disabled={!canSave}
          onClick={() => {
            onMap({ ...columnsMapping });
          }}
        >
          Next
        </Button>
      </div>
    </div>
  );
}
