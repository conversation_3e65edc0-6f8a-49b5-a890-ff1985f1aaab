import DialogFullScreen from '@/components/ui/Hyperbound/DialogFullScreen';
import Header from '@/components/ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '@/components/ui/Hyperbound/DialogFullScreen/ScrollableContent';
import { useState } from 'react';
import UpoadCsv from './UploadCsv';
import MapCsvColumns from './MapCsvColumns';
import PreviewUsers from './PreviewUsers';
import { Loader2Icon } from 'lucide-react';
import OrganizationService from '@/lib/Organization';
import { UserInvitation } from '@/lib/Organization/types';

interface IProps {
  open: boolean;
  onSave: () => void;
  onCancel: () => void;
}

export default function UsersCsvUpload({ open, onCancel, onSave }: IProps) {
  const [show, setShow] = useState<string>('upload-csv');

  const [csv, setCsv] = useState<any[]>([]);

  const [usersInfos, setUsersInfos] = useState<any[]>([]);

  const [columnsMapping, setColumnsMapping] = useState<any>({
    email: 0,
  });

  const onNewUpload = (rows: any[]) => {
    setCsv(rows);
    setShow('map-columns');
  };

  const updateColumnsMapping = (cm: any) => {
    setColumnsMapping(cm);
    setShow('preview');
  };

  const sendInvitations = async (list: UserInvitation[]) => {
    setShow('send');
    setUsersInfos(list);
    await OrganizationService.sendBulkInvitations(list);
    onSave();
  };

  let w = 'w-[500px]';
  if (show == 'preview') {
    w = 'w-[50%]';
  }

  return (
    <DialogFullScreen
      open={open}
      onOpenChange={onCancel}
      width={w}
      fitHeightToContent={true}
    >
      {show == 'upload-csv' && (
        <Header title="Upload a .CSV" onClose={onCancel} className="p-6" />
      )}
      {show == 'map-columns' && (
        <Header
          title="Match column names"
          onClose={onCancel}
          className="p-6 "
        />
      )}
      {show == 'preview' && (
        <Header title="Invite users" onClose={onCancel} className="p-6 " />
      )}
      {show == 'send' && (
        <Header title="Inviting users" onClose={() => {}} className="p-6 " />
      )}

      <ScrollableContent className="px-6">
        {show == 'upload-csv' && (
          <UpoadCsv cancel={onCancel} onUpload={onNewUpload} />
        )}
        {show == 'map-columns' && (
          <MapCsvColumns
            cancel={onCancel}
            onMap={updateColumnsMapping}
            csv={csv}
          />
        )}
        {show == 'preview' && (
          <PreviewUsers
            columnsMapping={columnsMapping}
            csv={csv}
            cancel={onCancel}
            send={sendInvitations}
          />
        )}
        {show == 'send' && (
          <div className="mb-6 flex items-center">
            <Loader2Icon size={16} className="animate-spin mr-2" />
            Sending {usersInfos.length} invitations...
          </div>
        )}
      </ScrollableContent>
    </DialogFullScreen>
  );
}
