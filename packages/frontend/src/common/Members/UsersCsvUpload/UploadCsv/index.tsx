import { useCallback, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { CloudUpload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import <PERSON> from 'papaparse';

interface IProps {
  cancel: () => void;
  onUpload: (rows: any[]) => void;
}

export default function UpoadCsv({ onUpload, cancel }: IProps) {
  const [error, setError] = useState<string>();

  const onDrop = useCallback((files: File[]) => {
    setError('');
    if (files) {
      for (let i = 0; i < files.length; i++) {
        if (files[i].size > 1024 * 1024 * 2) {
          //2MB
          setError('File size must be less than 2MB');
          return;
        }

        const reader = new FileReader();
        reader.onload = () => {
          const text: string = reader.result as string;

          try {
            const csv = Papa.parse(text, { skipEmptyLines: true });
            if (onUpload) {
              onUpload(csv.data);
            }
          } catch (e: any) {
            setError(e.message);
          }
        };
        reader.readAsText(files[i]);
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'text/csv': ['.csv'],
    },
    onDrop,
  });

  const downloadCsvExample = () => {
    const element = document.createElement('a');
    element.setAttribute(
      'href',
      'data:text/plain;charset=utf-8,' +
        encodeURIComponent(
          'email,team (Optional),role (Optional - default is Member - it can be Member or Admin),region (Optional),companyJoiningDate (Optional)\n',
        ),
    );
    element.setAttribute(
      'download',
      'HyperboundImportMembersInBulkExample.csv',
    );

    element.style.display = 'none';
    document.body.appendChild(element);

    element.click();

    document.body.removeChild(element);
  };
  const trasncriptContent = '';

  return (
    <div>
      <div className="text-muted-foreground text-sm mb-6">
        Upload a CSV file to quickly invite multiple members in bulk. The CSV needs to have at least an email column. 
        Optionally you can also specify team, role, region and company joining date for each user. 
        Role can assume the values Member or Admin. Download this{' '}
        <span
          className="text-blue-400 cursor-pointer hover:underline"
          onClick={downloadCsvExample}
        >
          CSV
        </span>{' '}
        to get started.
      </div>
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        <div className="bg-muted rounded-lg p-2 cursor-pointer">
          <div className="border border-dashed border-slate-300 p-3 flex flex-col items-center justify-center rounded-lg">
            {trasncriptContent == '' && (
              <div className="mb-2">
                <CloudUpload className="w-5 h-5 text-muted-foreground" />
              </div>
            )}
            {isDragActive ? (
              <>
                <div className="text-sm text-muted-foreground mb-2">
                  Drop file here...
                </div>
                <div className="text-xs text-muted-foreground">&nbsp;</div>
              </>
            ) : trasncriptContent != '' ? (
              <div>tr</div>
            ) : (
              <>
                <div className="text-sm text-muted-foreground mb-2">
                  Click to select or drag&apos;n&apos;drop a file here
                </div>
                <div className="text-xs text-muted-foreground">
                  You can upload only *.csv files
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      <div className="flex flex-col w-full mt-6 mb-4">
        <Button onClick={() => {}} className="mb-2">
          Save
        </Button>
        <Button
          variant={'outline'}
          onClick={() => {
            cancel();
          }}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
}
