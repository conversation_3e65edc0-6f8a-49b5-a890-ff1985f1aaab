import useUserSession from '@/hooks/useUserSession';
import { UserDto } from '@/lib/User/types';
import { useQueryClient } from '@tanstack/react-query';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { Trash2Icon } from 'lucide-react';
import ConfirmationModal from '@/components/ConfirmationModal';
import { useState } from 'react';
import OrganizationService from '@/lib/Organization';

interface IProps {
  user: UserDto;
  onOpenChange: (isOpen: boolean) => void;
}

export default function UserDropDownMenu({ user, onOpenChange }: IProps) {
  const { isAdmin } = useUserSession();
  const [isOpen, setIsOpen] = useState(false);

  const queryClient = useQueryClient();

  const manageMenu = (open: boolean) => {
    setIsOpen(open);
    onOpenChange(open);
  };

  /*********************************/
  /*********** ACTIONS *************/
  /*********************************/

  const [confirmDeactivation, setConfirmDeactivation] = useState(false);
  const startDeactivateUser = () => {
    setConfirmDeactivation(true);
  };

  const doDeactivateUser = async () => {
    try {
      await OrganizationService.deactivateUser(user.id);
    } catch (e) {
      console.error(e);
    }

    queryClient.invalidateQueries({ queryKey: ['get-all-org-users'] });
    queryClient.invalidateQueries({ queryKey: ['users'] });
    queryClient.invalidateQueries({ queryKey: ['orgUsers'] });

    setConfirmDeactivation(false);
    manageMenu(false); //close menu
  };

  /*********************************/
  /************ RENDER *************/
  /*********************************/

  if (isAdmin) {
    return (
      <>
        <DropdownMenu onOpenChange={manageMenu} open={isOpen}>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="p-0 rounded-full">
              <span className="sr-only">Open menu</span>
              <DotsHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              className="cursor-pointer text-red-500"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                startDeactivateUser();
              }}
            >
              <Trash2Icon className="w-4 h-4 mr-2 " />
              <span>Deactivate</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <ConfirmationModal
          open={confirmDeactivation}
          onCancel={() => {
            setConfirmDeactivation(false);
            manageMenu(false);
          }}
          onConfirm={doDeactivateUser}
          title={'Deactivate user'}
          description={
            'Are you sure you want to deactivate the account for ' +
            user.firstName +
            ' ' +
            user.lastName +
            '? All data for this account will not be lost.'
          }
        />
      </>
    );
  }
}
