import { MultiSelect } from '@/components/ui/Hyperbound/multi-select';
import useTeams from '@/hooks/useTeams';
import { TeamDto } from '@/lib/User/types';
import React from 'react';

interface MultiselectTeamsProps {
  selectedTeams: TeamDto[];
  setSelectedTeams: (teams: TeamDto[]) => void;
}
const MultiselectTeams: React.FC<MultiselectTeamsProps> = ({
  selectedTeams,
  setSelectedTeams,
}) => {
  const { data: teams } = useTeams(0, 20, '');
  return (
    <MultiSelect
      maxCount={2}
      options={
        (teams?.map((team) => ({
          label: team.name,
          value: team,
        })) as any[]) || []
      }
      defaultValue={selectedTeams.map((team) => team) as any[]}
      onValueChange={(selectedValues) => {
        setSelectedTeams(selectedValues as any[]);
      }}
      placeholder="Select teams"
      className="ml-6"
      popoverContentClassName="min-w-[400px]"
    />
  );
};

export default MultiselectTeams;
