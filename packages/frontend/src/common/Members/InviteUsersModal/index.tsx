import { Button } from '@/components/ui/button';
import DialogFullScreen from '@/components/ui/Hyperbound/DialogFullScreen';
import Header from '@/components/ui/Hyperbound/DialogFullScreen/Header';
import ScrollableContent from '@/components/ui/Hyperbound/DialogFullScreen/ScrollableContent';
import { Input } from '@/components/ui/input';
import useTeams from '@/hooks/useTeams';
import OrganizationService from '@/lib/Organization';
import { RoleEnum } from '@/lib/User/types';
import { Loader2Icon, Send } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import MultiStringsInput from '@/components/ui/Hyperbound/multiStringsInput';
import {
  RoleEnumIcon,
  RoleEnumSubheading,
} from '../MembersBulkEdit/MembersBulkEditDropDown';
import DatePicker from '@/common/AnalyticsOld/DashboardTab/Filters/DatePicker';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  open: boolean;
  onCancel: () => void;
  onSave: () => void;
}

export default function InviteUsersModal({ open, onCancel, onSave }: IProps) {
  const [canSend, setCanSend] = useState<boolean>(false);
  const [sending, setSending] = useState<boolean>(false);
  const [emails, setEmails] = useState<string[]>([]);
  const [emailsErrorMsg, setEmailsErrorMsg] = useState<string>('');
  const [team, setTeam] = useState<string>('');
  const [role, setRole] = useState<RoleEnum>(RoleEnum.MEMBER);
  const [region, setRegion] = useState<string>('');
  const [companyJoiningDate, setCompanyJoiningDate] = useState<Date>();

  const { hiddenRolesAllowed } = useUserSession();

  const { data: allTeams } = useTeams(0, 1000, '', true);

  useEffect(() => {
    if (emails.length > 0) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      let problems = false;
      let emailsWitProblems = '';
      for (const e of emails) {
        if (!emailPattern.test(e)) {
          emailsWitProblems += e + ', ';
          problems = true;
        }
      }

      if (problems) {
        setCanSend(false);
        setEmailsErrorMsg('Invalid emails: ' + emailsWitProblems);
      } else {
        setEmailsErrorMsg('');
        setCanSend(true);
      }
    } else {
      setEmailsErrorMsg('');
      setCanSend(false);
    }
  }, [emails]);
  const prepAndSend = async () => {
    setSending(true);

    const data = [];
    for (const e of emails) {
      data.push({
        email: e,
        role,
        team,
        region,
        companyJoiningDate,
      });
    }

    try {
      await OrganizationService.sendBulkInvitations(data);
    } catch (e) {
      console.log(e);
    }

    setSending(false);
    onSave();
  };

  return (
    <DialogFullScreen
      open={open}
      onOpenChange={onCancel}
      width={'w-[500px]'}
      fitHeightToContent={true}
    >
      <Header title="Invite user" onClose={onCancel} className="p-6" />

      <ScrollableContent className="px-6 pb-6 pt-0">
        <div className="flex flex-col">
          <div className="text-sm">Email(s)</div>
          <div className="">
            <MultiStringsInput
              current={emails}
              onChange={setEmails}
              placeholder="Email(s)"
            />
          </div>
          <div className="text-xs text-muted-foreground">
            (press enter to validate email address)
          </div>
        </div>
        <div className="flex flex-col my-4">
          <div className="text-sm">Team (optional)</div>
          <div className="">
            <Select
              onValueChange={(v: string) => {
                setTeam(v);
              }}
              value={team}
            >
              <SelectTrigger>
                <div>{team}</div>
              </SelectTrigger>
              <SelectContent>
                {allTeams?.map((v) => {
                  return (
                    <SelectItem key={v.id} value={String(v.name)}>
                      {v.name}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex flex-col my-4">
          <div className="text-sm">Region (optional)</div>
          <div className="">
            <Input
              value={region}
              onChange={(e) => {
                setRegion(e.target.value);
              }}
            />
          </div>
        </div>
        <div className="flex flex-col my-4">
          <div className="text-sm">Company Joining Date (optional)</div>
          <div className="">
            <DatePicker
              current={companyJoiningDate}
              onUpdate={(d: Date) => {
                setCompanyJoiningDate(d);
              }}
            />
          </div>
        </div>
        <div className="flex flex-col">
          <div className="text-sm">Role</div>
          <div className="">
            <Select
              onValueChange={(v: string) => {
                setRole(v as RoleEnum);
              }}
              value={role}
            >
              <SelectTrigger>
                <div>{role}</div>
              </SelectTrigger>
              <SelectContent>
                {Object.values(RoleEnum)
                  .filter((v) => {
                    return (
                      (v !== RoleEnum.OWNER &&
                        v !== RoleEnum.CUSTOM &&
                        v !== RoleEnum.TEMP) ||
                      hiddenRolesAllowed.some((role) => role === v.toString())
                    );
                  })
                  .map((v) => {
                    return (
                      <SelectItem key={v} value={v}>
                        <div className="flex flex-col">
                          <div className="flex items-center space-x-2">
                            {RoleEnumIcon[v]()}
                            <span className="text-base font-medium">{v}</span>
                          </div>
                          <div className="text-gray-500 ml-9 mt-1">
                            {RoleEnumSubheading[v]}
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="mb-0 flex items-center mt-6">
          <div className="text-red-600 text-xs">{emailsErrorMsg}</div>
          <div className="flex-1"></div>
          <Button onClick={prepAndSend} disabled={!canSend || sending}>
            {sending ? (
              <Loader2Icon size={16} className="animate-spin mr-2" />
            ) : (
              <Send size={16} className="mr-2" />
            )}
            Invite
          </Button>
        </div>
      </ScrollableContent>
    </DialogFullScreen>
  );
}
