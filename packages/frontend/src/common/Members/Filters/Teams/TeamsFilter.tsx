import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, FilterX, UsersIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import { TeamDto } from '@/lib/User/types';
import useTeams from '@/hooks/useTeams';

interface ITeamsFilterProps {
  selectedTeams: TeamDto[];
  onTeamSelect: (teams: TeamDto[]) => void;
}

function TeamsFilter({ onTeamSelect, selectedTeams }: ITeamsFilterProps) {
  const { data: teams } = useTeams(0, 20, '');
  const [open, setOpen] = useState(false);

  const clearAll = () => {
    if (onTeamSelect) {
      onTeamSelect([]);
    }
    setOpen(false);
  };

  const toggleTeam = (team: TeamDto) => {
    const updatedTeams = selectedTeams.includes(team)
      ? selectedTeams.filter((t) => t !== team)
      : [...selectedTeams, team];
    onTeamSelect(updatedTeams);
  };

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border"
        >
          <UsersIcon className="mr-2 h-4 w-4" />
          <p className="text-sm h-4 -mt-[1.1px]">Teams</p>
          {selectedTeams.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {selectedTeams.length} selected
                </Badge>
              </div>
            </>
          )}
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandGroup heading="Teams" className="max-h-[50vh">
              {teams?.map((team) => (
                <CommandItem
                  key={team.id}
                  value={String(team.id)}
                  onSelect={() => toggleTeam(team)}
                >
                  <div
                    className={cn(
                      'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                      selectedTeams?.some((t) => t.id === team.id)
                        ? 'bg-primary text-primary-foreground'
                        : 'opacity-50 [&_svg]:invisible',
                    )}
                  >
                    <CheckIcon className={cn('h-4 w-4')} />
                  </div>
                  <div className="flex space-x-2 items-center">
                    <div className="capitalize">{team.name}</div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
          {selectedTeams.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export default React.memo(TeamsFilter);
