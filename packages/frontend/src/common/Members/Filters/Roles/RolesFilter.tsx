import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CheckIcon, FilterX } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CaretSortIcon } from '@radix-ui/react-icons';
import { Separator } from '@/components/ui/separator';
import { RoleEnum } from '@/lib/User/types';
import { RoleEnumIcon } from '../../MembersBulkEdit/MembersBulkEditDropDown';

interface IRolesFilterProps {
  selectedRoles: RoleEnum[];
  onRoleSelect: (roles: RoleEnum[]) => void;
}

function RolesFilter({ onRoleSelect, selectedRoles }: IRolesFilterProps) {
  const [open, setOpen] = useState(false);

  const clearAll = () => {
    if (onRoleSelect) {
      onRoleSelect([]);
    }
    setOpen(false);
  };

  const toggleRole = (role: RoleEnum) => {
    const updatedRoles = selectedRoles.includes(role)
      ? selectedRoles.filter((r) => r !== role)
      : [...selectedRoles, role];
    onRoleSelect(updatedRoles);
  };

  return (
    <Popover
      open={open}
      onOpenChange={(o) => {
        setOpen(o);
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="border"
        >
          {RoleEnumIcon['Admin']({ className: 'mr-2 h-4 w-4' })}
          <p className="text-sm h-4 -mt-[1.1px]">Roles</p>
          {selectedRoles.length > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="space-x-1 lg:flex">
                <Badge
                  variant="secondary"
                  className="rounded-sm px-1 font-normal"
                >
                  {selectedRoles.length} selected
                </Badge>
              </div>
            </>
          )}
          <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandGroup heading="Roles" className="max-h-[50vh]">
              {Object.values(RoleEnum)
                .filter(
                  (r) =>
                    r !== RoleEnum.OWNER &&
                    r !== RoleEnum.CUSTOM &&
                    r !== RoleEnum.TEMP,
                )
                .map((r) => ({
                  label: r,
                  value: r,
                  ...(RoleEnumIcon[r] && {
                    icon: RoleEnumIcon[r] as React.ComponentType<{
                      className?: string;
                    }>,
                  }),
                }))
                .map((v) => (
                  <CommandItem
                    key={v.value}
                    value={String(v.value)}
                    onSelect={() => toggleRole(v.value)}
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        selectedRoles?.includes(v.value)
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    <div className="flex space-x-2 items-center">
                      <div className="capitalize">{v.label}</div>
                    </div>
                  </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
          {selectedRoles.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={clearAll}
                  className="justify-center text-center"
                >
                  <FilterX size="14" />
                  &nbsp;Clear filters
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export default React.memo(RolesFilter);
