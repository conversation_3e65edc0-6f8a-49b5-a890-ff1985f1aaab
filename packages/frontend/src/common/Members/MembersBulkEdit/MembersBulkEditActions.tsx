import { CheckedState } from '@radix-ui/react-checkbox';
import { Dispatch, SetStateAction, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import MembersBulkEditDropDown from './MembersBulkEditDropDown';
import { UserDto } from '@/lib/User/types';

interface IProps {
  selectedUsers: Record<string, CheckedState>;
  setEditState: Dispatch<SetStateAction<boolean>>;
  setUsers: Dispatch<SetStateAction<UserDto[]>>;
  confirmHyperboundLoginLinkSent: () => void;
}
export default function MembersBulkEditActions({
  selectedUsers,
  setEditState,
  setUsers,
  confirmHyperboundLoginLinkSent,
}: IProps) {
  const selectedUserIds: number[] = useMemo(() => {
    return Object.keys(selectedUsers)
      .filter((userId) => selectedUsers[userId] === true)
      .map((userIdString) => parseInt(userIdString));
  }, [selectedUsers]);

  const uiMessaging = useMemo(() => {
    const L = selectedUserIds.length;

    return `${L} member${L == 1 ? '' : 's'} selected`;
  }, [selectedUsers]);

  return (
    <div>
      <span className="m-1 text-muted-foreground text-xs mr-2">
        {uiMessaging}
      </span>
      <MembersBulkEditDropDown
        selectedUsers={selectedUserIds}
        setUsers={setUsers}
        setEditState={setEditState}
        confirmHyperboundLoginLinkSent={confirmHyperboundLoginLinkSent}
      />
      <Button
        variant={'outline'}
        onClick={() => {
          setEditState(false);
        }}
        className="ml-2"
      >
        Discard
      </Button>
    </div>
  );
}
