import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import useUserSession from '@/hooks/useUserSession';
import { cn } from '@/lib/utils';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import {
  BanIcon,
  ChevronDown,
  CircleUserRound,
  Clock1,
  Mail,
  Shield,
  Trash2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, {
  Dispatch,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button } from '@/components/ui/button';
import { RoleEnum, TeamDto, UserDto } from '@/lib/User/types';
import BulkEditTeams from '../Teams/BulkEditTeams';
import OrganizationService from '@/lib/Organization';
import { Id, toast } from 'react-toastify';
import ConfirmationModal from '@/components/ConfirmationModal';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  selectedUsers: number[];
  setUsers: Dispatch<SetStateAction<UserDto[]>>;
  setEditState: Dispatch<SetStateAction<boolean>>;
  confirmHyperboundLoginLinkSent: () => void;
}

export const RoleEnumSubheading = {
  [RoleEnum.OWNER]: 'Full access to everything',
  [RoleEnum.ADMIN]: 'Full access to everything',
  [RoleEnum.OBSERVER]: 'Full access to everything, but no editing rights',
  [RoleEnum.TEMP]: 'Demo access',
  [RoleEnum.CUSTOM]: 'Custom',
  [RoleEnum.MEMBER]: 'Full access but no editing rights',
  [RoleEnum.MEMBER_PLUS]: 'A member that can also edit/add bots',
} as const;

export const RoleEnumIcon = {
  [RoleEnum.OWNER]: (
    { className } = { className: 'm-1 w-4 h-4 fill-[#2e3035]' },
  ) => <Shield className={className} />,
  [RoleEnum.ADMIN]: (
    { className } = { className: 'm-1 w-4 h-4 fill-[#2e3035]' },
  ) => <Shield className={className} />,
  [RoleEnum.TEMP]: (
    { className } = { className: 'm-1 w-4 h-4 fill-[#2e3035] text-white' },
  ) => <Clock1 className={className} />,
  [RoleEnum.CUSTOM]: (
    { className } = { className: 'm-1 w-4 h-4 text-[#2e3035]' },
  ) => <CircleUserRound className={className} />,
  [RoleEnum.MEMBER]: (
    { className } = { className: 'm-1 w-4 h-4 text-[#2e3035]' },
  ) => <CircleUserRound className={className} />,
  [RoleEnum.OBSERVER]: (
    { className } = { className: 'm-1 w-4 h-4 text-[#2e3035]' },
  ) => <CircleUserRound className={className} />,
  [RoleEnum.MEMBER_PLUS]: (
    { className } = { className: 'm-1 w-4 h-4 text-[#2e3035]' },
  ) => <CircleUserRound className={className} />,
} as const;

export default function MembersBulkEditDropDown({
  selectedUsers,
  setUsers,
  setEditState,
  confirmHyperboundLoginLinkSent,
}: IProps) {
  const queryClient = useQueryClient();

  const { isLoggedIn, canAccess } = useUserSession();
  const errorToastId = useRef<Id | null>(null);
  async function addRolesToUsers(
    userIds: number[],
    role: RoleEnum,
  ): Promise<any> {
    try {
      const res = await OrganizationService.bulkUpdateUserRoles(userIds, role);
      if (res?.ok) {
        return res?.users;
      }
    } catch (error) {
      console.error('Error assigning roles to users:', error);
      throw new Error('Failed to assign roles to some or all users.');
    }
  }

  const [selectedTeams, setSelectedTeams] = useState<TeamDto[]>([]);
  const [selectedTeamsById, setSelectedTeamsById] = useState<{
    [key: number]: boolean;
  }>({});

  async function handleBulkAddRolesToUsers(
    selectedRole: RoleEnum,
    selectedUsers: number[],
  ) {
    if (selectedRole) {
      const updatedUsers: UserDto[] = await addRolesToUsers(
        selectedUsers,
        selectedRole,
      );

      if (updatedUsers?.length) {
        setUsers((users) => {
          const newUsersArray = users.map((user) => {
            // Find a matching updated user
            const updatedUser = updatedUsers.find((u) => u.uid === user.uid);

            // If an updated user exists, overwrite the role; otherwise, keep the original user
            return updatedUser ? { ...user, role: updatedUser.role } : user;
          });

          return newUsersArray;
        });
      }
      setEditState(false);
    } else {
      console.log('No role selected');
      errorToastId.current = toast.error('No role selected');
    }
  }

  async function handleSendLoginLink() {
    if (selectedUsers.length === 0) {
      setEditState(false);
      return;
    }
    try {
      await OrganizationService.sendLoginInfoToSelectedUsers(selectedUsers);
    } catch (error) {
      console.error('Error sending login info:', error);
    }

    setEditState(false);
    if (confirmHyperboundLoginLinkSent) {
      confirmHyperboundLoginLinkSent();
    }
  }

  const [confirmDeactivation, setConfirmDeactivation] = useState(false);
  const handleDeactivateAccounts = () => {
    setConfirmDeactivation(true);
  };
  const doDeactivateUsers = async () => {
    setConfirmDeactivation(false);

    for (const uid of selectedUsers) {
      try {
        await OrganizationService.deactivateUser(uid);
      } catch (e) {
        console.error(e);
      }
    }

    queryClient.invalidateQueries({ queryKey: ['get-all-org-users'] });
    queryClient.invalidateQueries({ queryKey: ['users'] });
    queryClient.invalidateQueries({ queryKey: ['orgUsers'] });
  };

  function handleResendInvites(): void {
    throw new Error('Function not implemented.');
  }

  return (
    <>
      <DropdownMenu>
        <Button disabled={!selectedUsers.length}>
          <DropdownMenuTrigger disabled={!selectedUsers.length}>
            <div className={cn('flex items-center cursor-pointer')}>
              <div className={cn('mr-2 max-h-[23px]')}>Actions</div>
              <motion.div
                transition={{
                  duration: 0.02,
                  delay: 0.05,
                }}
                className="self-center"
              >
                <ChevronDown size={14} />
              </motion.div>
            </div>
          </DropdownMenuTrigger>
        </Button>
        <DropdownMenuContent align="start" className="w-[250px]">
          <DropdownMenuSub>
            <DropdownMenuSubTrigger
              disabled={!selectedUsers.length}
              className={cn('flex items-center cursor-pointer')}
            >
              <p className=" disabled:text-gray-200 disabled:bg-gray-500">
                Select Role
              </p>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent className="max-h-[600px] overflow-y-auto">
                {Object.values(RoleEnum).map((r, i) => {
                  if (
                    r !== RoleEnum.OWNER &&
                    r !== RoleEnum.CUSTOM &&
                    r !== RoleEnum.TEMP
                  ) {
                    return (
                      <div key={r}>
                        <DropdownMenuItem
                          onClick={() => {
                            handleBulkAddRolesToUsers(r, selectedUsers);
                          }}
                        >
                          <div className="flex flex-col">
                            <div className="flex items-center space-x-2">
                              {RoleEnumIcon[r]()}
                              <span className="text-base font-medium">{r}</span>
                            </div>
                            <div className="text-gray-500 ml-9 mt-1">
                              {RoleEnumSubheading[r]}
                            </div>
                          </div>
                        </DropdownMenuItem>
                        {i !== Object.values(RoleEnum).length - 1 && (
                          <DropdownMenuSeparator />
                        )}{' '}
                      </div>
                    );
                  }
                })}
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>

          <DropdownMenuSub>
            <DropdownMenuSubTrigger
              disabled={!selectedUsers.length}
              className={cn('flex items-center cursor-pointer')}
            >
              <p>Select Team</p>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <BulkEditTeams
                userIds={selectedUsers}
                selectedTeams={selectedTeams}
                setSelectedTeams={setSelectedTeams}
                selectedTeamsById={selectedTeamsById}
                setSelectedTeamsById={setSelectedTeamsById}
                postBuldEditCallback={() => {
                  if (selectedUsers?.length || selectedTeams?.length) {
                    setUsers((users) =>
                      users.map((user) => {
                        // Check if the user's ID is in the selectedUsers list
                        const isUserSelected = selectedUsers.includes(user.id);

                        // Check if the user's team is in the selectedTeams list
                        const isTeamSelected = user.teams.some((team) =>
                          selectedTeams.includes(team),
                        );

                        // If either condition matches, update the user's role; otherwise, keep the original user
                        return isUserSelected || isTeamSelected
                          ? {
                              ...user,
                              teams: [...user.teams, ...selectedTeams],
                            } // Replace "UpdatedRole" with the desired new role
                          : user;
                      }),
                    );
                  }
                  // setUsers((users) => {...users, selectedUsers})
                  setEditState(false);
                  queryClient.invalidateQueries({ queryKey: ['users'] });
                  queryClient.invalidateQueries({ queryKey: ['teams'] });
                  queryClient.invalidateQueries({ queryKey: ['teams-by-id'] });
                  queryClient.invalidateQueries({ queryKey: ['orgUsers'] });
                }}
              />
            </DropdownMenuPortal>
          </DropdownMenuSub>

          {/* {(PermissionsUtils.hasPermission(
            AppPermissions.MANAGE_USERS,
            role,
          )) && (
            <DropdownMenuItem
              disabled={!isLoggedIn}
              className={cn(
                "flex items-center cursor-pointer justify-between disabled:text-gray-600"
              )}
              onClick={() => { handleResendInvites() }}
            >
              <p>Resend Invites</p>
              <Mail size={"13.3px"} />
            </DropdownMenuItem>
          )} */}

          {canAccess(AppPermissions.MANAGE_USERS) && (
            <DropdownMenuItem
              disabled={!isLoggedIn}
              className={cn(
                'flex items-center cursor-pointer justify-between disabled:text-gray-600',
              )}
              onClick={() => {
                handleSendLoginLink();
              }}
            >
              <p>Send Hyperbound Login Link</p>
              <Mail size={'13.3px'} />
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuItem
            disabled={!isLoggedIn || !selectedUsers.length}
            onClick={() => {
              handleDeactivateAccounts();
            }}
            className={
              'flex items-center space-x-2 cursor-pointer text-red-500 justify-between disabled:text-gray-600'
            }
          >
            <p>Deactivate Accounts</p>
            <Trash2 size={'13.3px'} />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ConfirmationModal
        open={confirmDeactivation}
        onCancel={() => {
          setConfirmDeactivation(false);
        }}
        onConfirm={doDeactivateUsers}
        title={'Deactivate users'}
        description={`Are you sure you want to deactivate ${selectedUsers.length} account${selectedUsers.length > 1 ? 's' : ''}? Data for this account${selectedUsers.length > 1 ? 's' : ''} will not be lost.`}
      />
    </>
  );
}
