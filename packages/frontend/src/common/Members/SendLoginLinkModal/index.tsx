import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import OrganizationService from '@/lib/Organization';

interface IProps {
  open: boolean;
  onCancel: () => void;
  openBulkEdit: () => void;
  confirmSent: () => void;
}

const DAY_OPTIONS = [2, 5, 10, 15, 30, 60, 90];
export default function SendLoginLinkModal({
  open,
  onCancel,
  openBulkEdit,
  confirmSent,
}: IProps) {
  const [numberOfDays, setNumberOfDays] = useState<number>(10);

  const closeModal = (isOpen: boolean) => {
    if (!isOpen) {
      onCancel();
    }
  };

  const sendToSlackers = async () => {
    // console.log(numberOfDays);
    try {
      await OrganizationService.sendLoginInfoToSlackers(numberOfDays);
    } catch (e) {
      console.log(e);
    }

    confirmSent();
  };

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="close-btn">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Send login link to...
          </DialogTitle>
          <div className="py-2 text-sm">
            <div>
              Send login link to users that have not made any call in the last
            </div>
            <div className="mt-6 flex items-center">
              <div className="mr-2">
                <Select
                  onValueChange={(v: string) => {
                    setNumberOfDays(parseInt(v));
                  }}
                  value={String(numberOfDays)}
                >
                  <SelectTrigger>
                    <div>{numberOfDays}</div>
                  </SelectTrigger>
                  <SelectContent>
                    {DAY_OPTIONS?.map((v) => {
                      return (
                        <SelectItem key={v} value={String(v)}>
                          {v}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div>days</div>
            </div>
            <div className="mt-6 flex items-center">
              <div className="mr-2">or</div>
              <div>
                <Button
                  variant="outline"
                  onClick={() => {
                    closeModal(false);
                    openBulkEdit();
                  }}
                >
                  Select users...
                </Button>
              </div>
            </div>
          </div>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              closeModal(false);
            }}
          >
            Close
          </Button>
          <Button variant={'default'} onClick={sendToSlackers}>
            Send
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
