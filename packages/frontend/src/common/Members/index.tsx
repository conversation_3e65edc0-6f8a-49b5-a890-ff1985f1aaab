import { useState, useEffect, useRef } from 'react';
import useUserSession from '@/hooks/useUserSession';
import useOrgUsers from '@/hooks/useOrgUsers';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { RoleEnum, TeamDto, UserDto, UserStatus } from '@/lib/User/types';
import dayjs from 'dayjs';
import {
  Loader2Icon,
  UsersIcon,
  User,
  CloudUpload,
  ChevronDown,
  Plus,
  Edit,
  Send,
  MailIcon,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
  TablePaginationFooter,
} from '@/components/ui/Hyperbound/table';
import { PARTNER_ORG_IDS } from '@/app/(dashboard)/layout';
import { useRouter } from 'next/navigation';
import EditUserTeams from './Teams/EditUserTeams';
import TeamsDialog from './Teams';
import LinksManager from '@/lib/linksManager';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import UsersCsvUpload from './UsersCsvUpload';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import PageHeader from '@/components/PageHeader';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';
import Checkbox from '@/components/ui/Hyperbound/checkbox';
import { CheckedState } from '@radix-ui/react-checkbox';
import MembersBulkEditActions from './MembersBulkEdit/MembersBulkEditActions';
import { RoleEnumIcon } from './MembersBulkEdit/MembersBulkEditDropDown';
import { Id, toast, ToastContainer } from 'react-toastify';
import { cn } from '@/lib/utils';
import OrganizationService from '@/lib/Organization';
import SendLoginLinkModal from './SendLoginLinkModal';
import InviteUsersModal from './InviteUsersModal';
import UserDropDownMenu from './UserDropDownMenu';
import SearchBox from '@/components/ui/Hyperbound/search-box';
import SelectRoleDropDown from './SelectRoleDropDown';
import {
  CaretDownIcon,
  CaretSortIcon,
  CaretUpIcon,
} from '@radix-ui/react-icons';
import RolesFilter from './Filters/Roles/RolesFilter';
import TeamsFilter from './Filters/Teams/TeamsFilter';
import StatusFilter from './Filters/Status/StatusFilter';
import { AppPermissions } from '@/lib/permissions';
import { useOrgAccountsUsedPerRole } from '@/hooks/useOrg';

const DEFAULT_NUMBER_OF_RESULTS = 20;

// const StatusDisplayLabel = {
//   [UserStatus.ACTIVE]: 'Active',
//   [UserStatus.DEPROVISIONED]: 'Deactivated',
//   [UserStatus.INVITED]: 'Invited',
// };

export default function Members() {
  const router = useRouter();

  const queryClient = useQueryClient();
  const { dbOrg, orgId, canAccess } = useUserSession();

  const [numberOfResults, setNumberOfResults] = useState(
    DEFAULT_NUMBER_OF_RESULTS,
  );

  const [totNumberOfUsers, setTotNumberOfUsers] = useState<number>(0);
  const [loadFrom, setLoadFrom] = useState<number>(0);
  const [users, setUsers] = useState<UserDto[]>([]);
  const [searchUserStr, setSearchUserStr] = useState<string>('');
  const [searchUserLabel, setSearchUserLabel] = useState<string>('');
  const [selectedRoles, setSelectedRoles] = useState<RoleEnum[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<UserStatus[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<TeamDto[]>([]);
  const [editTeamsDialogOpen, setEditTeamsDialogOpen] =
    useState<boolean>(false);
  const [selectedUsers, setSelectedUsers] = useState<
    Record<string, CheckedState>
  >({});
  const toastId = useRef<Id | null>(null);
  const [ordering, setOrdering] = useState<Record<string, 'asc' | 'desc'>>({});
  const [availableAccountsPerRole, setAvailableAccountsPerRole] = useState<{
    [role: string]: { used: number; cap: number };
  }>();

  /**************************************/
  /**************** INIT ****************/
  /**************************************/

  const { data: result, isLoading: isLoadingOrgUsers } = useOrgUsers(
    true,
    loadFrom,
    numberOfResults,
    searchUserStr,
    selectedRoles?.length > 0 &&
      !selectedRoles?.every((r) => r === RoleEnum.MEMBER)
      ? false
      : true,
    ordering,
    selectedTeams?.map((t) => t.id),
    selectedRoles,
    selectedStatuses,
  );

  useEffect(() => {
    if (!ordering || Object.keys(ordering)?.length === 0) {
      const tmp = result?.data.sort((a: UserDto, b: UserDto) => {
        // Ensure ADMIN roles come first
        if (a.role === RoleEnum.ADMIN && b.role !== RoleEnum.ADMIN) {
          return -1;
        } else if (b.role === RoleEnum.ADMIN && a.role !== RoleEnum.ADMIN) {
          return 1;
        }

        // Sort by createdAt descending within the same role
        if (a.role === b.role) {
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        }

        // Optionally handle MEMBER and other roles (if needed)
        return 0; // Keep the same order for other roles
      });

      setTotNumberOfUsers(result?.totalCount || 0);
      setUsers(tmp || []); // Use the sorted array
    }
  }, [ordering, result]);

  useEffect(() => {
    if (!isLoadingOrgUsers && result) {
      setTotNumberOfUsers(result.totalCount);
      setUsers(result.data);
    }
  }, [isLoadingOrgUsers, result]);

  const { data: accountsUsedPerRole, isLoading: isLoadingAccountsUsedPerRole } =
    useOrgAccountsUsedPerRole();

  useEffect(() => {
    if (!isLoadingAccountsUsedPerRole && accountsUsedPerRole) {
      setAvailableAccountsPerRole(accountsUsedPerRole);
    }
  }, [isLoadingAccountsUsedPerRole, accountsUsedPerRole]);
  /**************************************/
  /*************** ACTIONS **************/
  /**************************************/

  const [allSelected, setAllSelected] = useState<boolean>(false);
  const [anySelected, setAnySelected] = useState<boolean>(false);

  const clearSelectedUsers = () => {
    setAllSelected(false);
    setAnySelected(false);
    setSelectedUsers({});
  };

  // Toggle a specific user's selection state
  const handleSelectUserChange = (userId: string) => {
    setSelectedUsers((prevState) => {
      let anys = false;
      for (const key in prevState) {
        if (prevState[key] && key !== userId) {
          anys = true;
          break;
        }
      }
      if (!anys && !prevState[userId]) {
        anys = true;
      }
      setAnySelected(anys);
      return {
        ...prevState,
        [userId]: !prevState[userId], // Toggle the selected state for the user
      };
    });
  };

  // Handle select all/deselect all functionality based on the header checkbox state
  const handleSelectAll = () => {
    const checked = !allSelected;
    setSelectedUsers(() => {
      const newState: Record<string, CheckedState> = {};
      users.forEach((user) => {
        newState[user.id] = checked; // Select or deselect all users
      });
      return newState;
    });
    if (!checked) {
      setAnySelected(false);
    } else {
      setAnySelected(true);
    }
    setAllSelected(checked);
  };

  // const loadMore = () => {
  //   setNumberOfResults((old) => old + DEFAULT_NUMBER_OF_RESULTS);
  // };

  const openEditTeams = () => {
    router.push(LinksManager.teams());
  };

  const closeEditTeams = () => {
    setEditTeamsDialogOpen(false);
    //refetch();
  };

  /// -------------- SEND LOGIN INFOS

  const [showSendLoginLinkModal, setShowSendLoginLinkModal] =
    useState<boolean>(false);

  const startSendLoginLink = () => {
    setShowSendLoginLinkModal(true);
  };

  const confirmSentLoginLink = () => {
    toastId.current = toast.success(`Link correctly sent`);
    setShowSendLoginLinkModal(false);
  };

  /**************************************/
  /*************** SEARCH ***************/
  /**************************************/

  const timeoutSerch = useRef<ReturnType<typeof setTimeout> | null>(null);

  const searchUser = (s: string) => {
    if (timeoutSerch.current) {
      if (timeoutSerch.current) {
        clearTimeout(timeoutSerch.current);
      }
    }

    setSearchUserLabel(s);

    timeoutSerch.current = setTimeout(async () => {
      setSearchUserStr(s);
    }, 100);
  };

  const updatePagination = (from: number, numberOfResults: number) => {
    setLoadFrom(from);
    setNumberOfResults(numberOfResults);
  };

  const inviteMembersMutation = useMutation({
    mutationFn: ({
      orgId,
      emails,
      role,
    }: {
      orgId: number;
      emails: string[];
      role: RoleEnum;
    }) => OrganizationService.inviteMembers(orgId, emails, role),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['get-all-org-users'] });
    },
  });

  /**************************************/
  /************ INVITE USER *************/
  /**************************************/

  const [showUploadUsersCsv, setShowUploadUsersCsv] = useState<boolean>(false);
  const [showInviteUsersModal, setInviteUsersModal] = useState<boolean>(false);
  const [disabledButtons, setDisabledButtons] = useState<{
    [key: string]: boolean;
  }>({});

  const resendInvite = (email: string, role: RoleEnum) => {
    inviteMembersMutation.mutate({
      orgId: dbOrg?.id as number,
      emails: [email],
      role,
    });

    setDisabledButtons((prev) => ({ ...prev, [email]: true }));
    setTimeout(() => {
      setDisabledButtons((prev) => ({ ...prev, [email]: false }));
    }, 10000);
  };

  /**************************************/
  /********* CHANGE USER ROLE ***********/
  /**************************************/

  const changeUserRole = async (userId: number, role: RoleEnum) => {
    let ok = false;
    try {
      await OrganizationService.bulkUpdateUserRoles([userId], role);
      ok = true;
    } catch (error) {
      ok = false;
      console.error('Error assigning roles to users:', error);
      toastId.current = toast.error(`Error assigning roles to user`);
    }

    if (ok) {
      queryClient.invalidateQueries({ queryKey: ['get-all-org-users'] });
      toastId.current = toast.success(`Role correctly changed`);
    }
  };

  /**************************************/
  /******* REACTIVATE USER ROLE *********/
  /**************************************/

  const reactivateUser = async (userId: number) => {
    try {
      await OrganizationService.reactivateUser(userId);
    } catch (e) {
      console.error(e);
    }

    queryClient.invalidateQueries({ queryKey: ['get-all-org-users'] });
    queryClient.invalidateQueries({ queryKey: ['users'] });
    queryClient.invalidateQueries({ queryKey: ['orgUsers'] });

    toastId.current = toast.success(`User successfully reactivated`);
  };

  /**************************************/

  const onSort = (field: string) => {
    setOrdering((prevOrdering) => {
      const currentOrder = prevOrdering[field];

      let newOrder: 'asc' | 'desc' | undefined;
      if (!currentOrder) {
        newOrder = 'asc';
      } else if (currentOrder === 'asc') {
        newOrder = 'desc';
      } else {
        newOrder = undefined;
      }

      const newOrdering = { ...prevOrdering };
      if (newOrder) {
        newOrdering[field] = newOrder;
      } else {
        delete newOrdering[field];
      }

      return newOrdering;
    });
  };

  const getSortIcon = (field: string) => {
    const order = ordering[field];

    if (!order) {
      return <CaretSortIcon className="ml-2 h-4 w-4" />;
    }

    return order === 'desc' ? (
      <CaretDownIcon className="ml-2 h-4 w-4" />
    ) : (
      <CaretUpIcon className="ml-2 h-4 w-4" />
    );
  };

  /**************************************/
  /*************** RENDER ***************/
  /**************************************/

  const renderActionButtons = (user: UserDto) => {
    if (canAccess(AppPermissions.MANAGE_USERS) && user.status == UserStatus.ACTIVE) {
      return (
        <TableCell>
          <UserDropDownMenu user={user} onOpenChange={() => {}} />
        </TableCell>
      );
    } else if (canAccess(AppPermissions.MANAGE_USERS) && user.status == UserStatus.DEPROVISIONED) {
      return (
        <TableCell>
          <Button
            size={'sm'}
            variant={'outline'}
            onClick={() => {
              reactivateUser(user.id);
            }}
          >
            Reactivate
          </Button>
        </TableCell>
      );
    } else {
      return (
        <TableCell
          onClick={() => {
            router.push(LinksManager.members(`/${user.id}`));
          }}
        >
          &nbsp;
        </TableCell>
      );
    }
  };

  return (
    <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6 overflow-auto">
      <PageHeader
        title="Members"
        rightComponent={
          canAccess(AppPermissions.MANAGE_USERS) && (
            <div className="flex space-x-2">
              <div className="flex space-x-4">
                <Button variant={'outline'} onClick={openEditTeams}>
                  <UsersIcon className="w-4 h-4 mr-2" />
                  Edit teams
                </Button>

                {!anySelected && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant={'default'}>
                        <Plus size={16} className="mr-2" />
                        Invite
                        <ChevronDown size={16} className="ml-2" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          startSendLoginLink();
                        }}
                      >
                        <Send size={16} className="mr-1" />
                        <span>Send login link to...</span>
                      </DropdownMenuItem>

                      {/* <DropdownMenuItem
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          openBulkEdit();
                        }}
                      >
                        <UserPen size={16} className="mr-1" />
                        <span>Select users and edit...</span>
                      </DropdownMenuItem> */}

                      {/* <DropdownMenuSeparator /> */}

                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger className="cursor-pointer">
                          <Plus size={16} className="mr-2" />
                          Invite
                        </DropdownMenuSubTrigger>
                        <DropdownMenuPortal>
                          <DropdownMenuSubContent className="w-64">
                            <DropdownMenuItem
                              className="cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                setInviteUsersModal(true);
                              }}
                            >
                              <User size={16} className="mr-1" />
                              <span>Add new member</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowUploadUsersCsv(true);
                              }}
                            >
                              <CloudUpload size={16} className="mr-1" />
                              <span>Upload .CSV</span>
                            </DropdownMenuItem>
                          </DropdownMenuSubContent>
                        </DropdownMenuPortal>
                      </DropdownMenuSub>

                      {/* <DropdownMenuSeparator /> */}

                      <DropdownMenuItem className="cursor-pointer">
                        <div
                          onClick={() => {
                            window.location.href = 'mailto:<EMAIL>';
                          }}
                          className="border text-center w-[200px] rounded-lg p-1"
                        >
                          Contact us
                        </div>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          )
        }
      />

      <div className="w-[92vw] mt-6">
        {/*********************************/}
        {/*********** CMD LINE ************/}
        {/*********************************/}

        <div className="flex items-center mb-1">
          <div className="flex items-center">
            <SearchBox
              value={searchUserLabel}
              onChange={searchUser}
              className="w-56 m-3 ml-0"
              placeholder="Search..."
            />
          </div>
          <div className="ml-2">
            <RolesFilter
              selectedRoles={selectedRoles}
              onRoleSelect={(selectedValues) => {
                setSelectedRoles(selectedValues);
              }}
            />
          </div>
          <div className="ml-4">
            <TeamsFilter
              selectedTeams={selectedTeams}
              onTeamSelect={(selectedValues) => {
                setSelectedTeams(selectedValues);
              }}
            />
          </div>
          <div className="ml-4">
            <StatusFilter
              selectedStatuses={selectedStatuses}
              onStatusSelect={(selectedValues) => {
                setSelectedStatuses(selectedValues);
              }}
            />
          </div>
          <div className="flex-1" />

          {anySelected ? (
            <MembersBulkEditActions
              selectedUsers={selectedUsers}
              setEditState={clearSelectedUsers}
              setUsers={setUsers}
              confirmHyperboundLoginLinkSent={confirmSentLoginLink}
            />
          ) : (
            availableAccountsPerRole && (
              <div className="text-xs text-muted-foreground flex items-center">
                {availableAccountsPerRole[RoleEnum.ADMIN] && (
                  <div className="mr-2">
                    Admins: {availableAccountsPerRole[RoleEnum.ADMIN].used}
                    {availableAccountsPerRole[RoleEnum.ADMIN].cap > 0 &&
                      `/${availableAccountsPerRole[RoleEnum.ADMIN].cap}`}
                  </div>
                )}
                {availableAccountsPerRole[RoleEnum.MEMBER] && (
                  <div className="mr-2">
                    Members: {availableAccountsPerRole[RoleEnum.MEMBER].used}
                    {availableAccountsPerRole[RoleEnum.MEMBER].cap > 0 &&
                      `/${availableAccountsPerRole[RoleEnum.MEMBER].cap}`}
                  </div>
                )}
                {availableAccountsPerRole[RoleEnum.OBSERVER] && (
                  <div>
                    Observers:{' '}
                    {availableAccountsPerRole[RoleEnum.OBSERVER].used}
                    {availableAccountsPerRole[RoleEnum.OBSERVER].cap > 0 &&
                      `/${availableAccountsPerRole[RoleEnum.OBSERVER].cap}`}
                  </div>
                )}
              </div>
            )
          )}
        </div>

        {/*********************************/}
        {/************ USERS **************/}
        {/*********************************/}

        {users.length === 0 ? (
          <div className="text-muted-foreground ml-0 text-sm">
            {!result ||
              (users?.length === 0 && !isLoadingOrgUsers && 'No members found')}
          </div>
        ) : (
          <Table>
            <TableContent>
              <TableRow className="sticky top-0 z-50">
                {canAccess(AppPermissions.MANAGE_USERS) && (
                  <TableCellHead className="w-[40px]">
                    <Checkbox
                      className="ml-2"
                      checked={allSelected}
                      onToggle={handleSelectAll}
                    />
                  </TableCellHead>
                )}

                <TableCellHead
                  onClick={() => onSort('createdAt')}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    <div className="mr-2"> Sign Up Date </div>{' '}
                    <div className="group-hover:text-black">
                      {getSortIcon('createdAt')}
                    </div>
                  </div>
                </TableCellHead>

                <TableCellHead
                  onClick={() => onSort('name')}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    <div className="mr-2">Name</div>
                    <div className="group-hover:text-black">
                      {getSortIcon('name')}
                    </div>
                  </div>
                </TableCellHead>
                {!PARTNER_ORG_IDS.includes(orgId as string) && (
                  <TableCellHead>Email</TableCellHead>
                )}
                <TableCellHead>Role</TableCellHead>
                <TableCellHead>Teams</TableCellHead>
                <TableCellHead
                  onClick={() => onSort('status')}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    <div className="mr-2">Status</div>
                    <div className="group-hover:text-black">
                      {getSortIcon('status')}
                    </div>
                  </div>
                </TableCellHead>
                <TableCellHead>Last active</TableCellHead>
                <TableCellHead>&nbsp;</TableCellHead>
                <TableCellHead
                  onClick={() => onSort('companyJoiningDate')}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    <div className="mr-2"> Company Joining Date </div>{' '}
                    <div className="group-hover:text-black">
                      {getSortIcon('companyJoiningDate')}
                    </div>
                  </div>
                </TableCellHead>
                <TableCellHead>Region</TableCellHead>
               
              </TableRow>

              {users.map((user) => {
                let active = true;
                if (user.status == UserStatus.INVITED) {
                  active = false;
                }
                return (
                  <TableRow
                    className={cn(
                      'h-10 cursor-pointer hover:bg-gray-50 group',
                      {
                        'bg-gray-100': user.status == UserStatus.INVITED,
                        'bg-gray-50': user.status == UserStatus.DEPROVISIONED,
                      },
                    )}
                    key={user.id}
                  >
                    {canAccess(AppPermissions.MANAGE_USERS) && (
                      <TableCell className="font-medium">
                        <Checkbox
                          // onClick={(e) => {
                          //   e.stopPropagation();
                          // }}
                          checked={!!selectedUsers[user.id] || false}
                          onToggle={() => {
                            handleSelectUserChange(`${user.id}`);
                          }}
                          className="ml-2"
                        />
                      </TableCell>
                    )}
                    <TableCell
                      className="font-medium"
                      onClick={() => {
                        router.push(LinksManager.members(`/${user.id}`));
                      }}
                    >
                      {active && dayjs(user.createdAt).format('MMM D, YYYY')}
                    </TableCell>
                    <TableCell
                      onClick={() => {
                        router.push(LinksManager.members(`/${user.id}`));
                      }}
                    >
                      {active && (
                        <div className="flex items-center space-x-2">
                          <Avatar className="w-6 h-6">
                            {user?.avatar && <AvatarImage src={user?.avatar} />}
                            <AvatarFallback className="text-xs text-muted-foreground">
                              {user?.firstName?.charAt(0) || ''}
                              {user?.lastName?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <p>
                            {user.firstName} {user.lastName}
                          </p>
                        </div>
                      )}
                    </TableCell>
                    {!PARTNER_ORG_IDS.includes(orgId as string) && (
                      <TableCell
                        onClick={() => {
                          router.push(LinksManager.members(`/${user.id}`));
                        }}
                      >
                        {user.email}
                      </TableCell>
                    )}
                    <TableCell>
                      {canAccess(AppPermissions.MANAGE_USERS) ? (
                        <SelectRoleDropDown
                          currentRole={user.role}
                          onChange={(nr: RoleEnum) => {
                            changeUserRole(user.id, nr);
                          }}
                        />
                      ) : (
                        <Badge>
                          {RoleEnumIcon[user.role]({
                            className: 'mr-2 w-4 h-4 text-white',
                          })}
                          {user.role}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell
                      className="max-w-[260px]"
                      onClick={() => {
                        router.push(LinksManager.members(`/${user.id}`));
                      }}
                    >
                      <EditUserTeams
                        currentTeams={user.teams}
                        userId={user.id}
                      />
                    </TableCell>
                    <TableCell
                      onClick={() => {
                        router.push(LinksManager.members(`/${user.id}`));
                      }}
                    >
                      {canAccess(AppPermissions.MANAGE_USERS) && user.status == UserStatus.INVITED && (
                        <div className="">
                          {/* <div className="text-xs">Pending...</div> */}
                          <Button
                            variant="ghost"
                            className="text-xs -ml-3"
                            size={'sm'}
                            onClick={(e) => {
                              e.stopPropagation();
                              resendInvite(user.email, user.role);
                            }}
                            disabled={!dbOrg || disabledButtons[user.email]}
                          >
                            {inviteMembersMutation.isPending ? (
                              <Loader2Icon className="w-4 h-4 mr-2 animate-spin" />
                            ) : (
                              <MailIcon className="w-4 h-4 mr-2" />
                            )}{' '}
                            {disabledButtons[user.email]
                              ? 'Sent invite'
                              : 'Resend invite'}
                          </Button>
                          {/* <p className="text-xs text-muted-foreground mt-1">
                          Last invitation sent{" "}
                          {dayjs(user.updatedAt).format("MMM D, YYYY")}
                        </p> */}
                        </div>
                      )}

                      {!canAccess(AppPermissions.MANAGE_USERS) && user.status == UserStatus.INVITED && (
                        <span className="text-xs font-semibold">Invited</span>
                      )}

                      {user.status == UserStatus.ACTIVE && (
                        <span className="text-xs text-green-400 font-semibold">
                          Active
                        </span>
                      )}

                      {user.status == UserStatus.DEPROVISIONED && (
                        <span className="text-xs text-gray-500 font-semibold">
                          Deactivated
                        </span>
                      )}
                    </TableCell>
                    <TableCell
                      className="max-w-[260px] text-xs"
                      onClick={() => {
                        router.push(LinksManager.members(`/${user.id}`));
                      }}
                    >
                      {user.lastActive &&
                        dayjs(user.lastActive).format('MMM D, YYYY h:mm A')}
                    </TableCell>
                    {renderActionButtons(user)}
                    <TableCell>
                      {user.companyJoiningDate && dayjs(user.companyJoiningDate).format('MMM D, YYYY')}
                    </TableCell>
                    <TableCell>
                      {user.region}
                    </TableCell>
                   
                  </TableRow>
                );
              })}
            </TableContent>
            <TableFooter>
              <TablePaginationFooter
                from={loadFrom}
                numberOfResults={numberOfResults}
                totNumberOfRows={totNumberOfUsers}
                updatePagination={updatePagination}
              />
            </TableFooter>
          </Table>
        )}
      </div>
      <TeamsDialog open={editTeamsDialogOpen} close={closeEditTeams} />
      {showUploadUsersCsv && (
        <UsersCsvUpload
          open={showUploadUsersCsv}
          onCancel={() => {
            setShowUploadUsersCsv(false);
          }}
          onSave={() => {
            queryClient.invalidateQueries({ queryKey: ['get-all-org-users'] });
            setShowUploadUsersCsv(false);
          }}
        />
      )}

      {showInviteUsersModal && (
        <InviteUsersModal
          open={showInviteUsersModal}
          onCancel={() => {
            setInviteUsersModal(false);
          }}
          onSave={() => {
            queryClient.invalidateQueries({ queryKey: ['get-all-org-users'] });
            setInviteUsersModal(false);
          }}
        />
      )}

      {showSendLoginLinkModal && (
        <SendLoginLinkModal
          open={showSendLoginLinkModal}
          onCancel={() => {
            setShowSendLoginLinkModal(false);
          }}
          openBulkEdit={() => {}}
          confirmSent={confirmSentLoginLink}
        />
      )}
      <ToastContainer />
    </ScrollablePage>
  );
}
