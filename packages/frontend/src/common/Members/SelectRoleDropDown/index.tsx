import { RoleEnum } from '@/lib/User/types';
import {
  RoleEnumIcon,
  RoleEnumSubheading,
} from '../MembersBulkEdit/MembersBulkEditDropDown';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  currentRole: RoleEnum;
  onChange: (role: RoleEnum) => void;
}

export default function SelectRoleDropDown({ currentRole, onChange }: IProps) {
  // return (
  //   <DropdownMenu>
  //     <DropdownMenuTrigger>
  //       <Badge>
  //         {RoleEnumIcon[currentRole]({
  //           className: 'mr-2 w-4 h-4 text-white',
  //         })}
  //         {currentRole}
  //       </Badge>
  //     </DropdownMenuTrigger>
  //     <DropdownMenuContent align="start" className="w-[250px]">
  //       {Object.values(RoleEnum).map((r, i) => {
  //         if (r !== RoleEnum.OWNER && r !== RoleEnum.CUSTOM && r !== RoleEnum.TEMP) {
  //           return (
  //             <div key={r}>
  //               <DropdownMenuItem
  //                 onClick={() => {
  //                   console.log(r);
  //                 }}
  //               >
  //                 <div className="flex flex-col">
  //                   <div className="flex items-center space-x-2">
  //                     {RoleEnumIcon[r]()}
  //                     <span className="text-base font-medium">{r}</span>
  //                   </div>
  //                   <div className="text-gray-500 ml-9 mt-1">
  //                     {RoleEnumSubheading[r]}
  //                   </div>
  //                 </div>
  //               </DropdownMenuItem>
  //               {i !== Object.values(RoleEnum).length - 1 && (
  //                 <DropdownMenuSeparator />
  //               )}{' '}
  //             </div>
  //           )
  //         }
  //       })}
  //     </DropdownMenuContent>
  //   </DropdownMenu>
  // )

  const { hiddenRolesAllowed } = useUserSession();

  return (
    <Select
      onValueChange={(v: string) => {
        onChange(v as RoleEnum);
      }}
      value={currentRole}
    >
      <SelectTrigger>
        <div className="flex items-center">
          <div className="mr-1">{RoleEnumIcon[currentRole]?.()}</div>
          <div className="text-base font-medium text-sm">{currentRole}</div>
        </div>
      </SelectTrigger>
      <SelectContent>
        {Object.values(RoleEnum).map((r, i) => {
          if (
            (r !== RoleEnum.OWNER &&
              r !== RoleEnum.CUSTOM &&
              r !== RoleEnum.TEMP) ||
            hiddenRolesAllowed.some((role) => role === r.toString())
          ) {
            return (
              <div key={r}>
                <SelectItem key={i} value={r}>
                  <div className="flex flex-col">
                    <div className="flex items-center space-x-1">
                      {RoleEnumIcon[r]()}
                      <span className="text-base font-medium text-sm">{r}</span>
                    </div>
                    <div className="text-gray-500 ml-2 mt-1">
                      {RoleEnumSubheading[r]}
                    </div>
                  </div>
                </SelectItem>
              </div>
            );
          }
        })}
      </SelectContent>
    </Select>
  );
}
