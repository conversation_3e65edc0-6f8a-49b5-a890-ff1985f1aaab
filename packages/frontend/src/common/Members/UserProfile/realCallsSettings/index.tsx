import { useIntegrations } from '@/hooks/useIntegrations';
import IntegrationService, { RealCallsService } from '@/lib/Integrations';
import { UserDto } from '@/lib/User/types';
import { Loader2Icon, User } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import {
  ExternalUserDto,
  IntegrationUserSettings,
} from '@/lib/Integrations/types';
import { AgentCallType } from '@/lib/Agent/types';
import { CALL_TYPE_TO_ICON } from '@/common/Sidebar/OldSidebar';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import ScorecardConfigService from '@/lib/ScorecardConfig';
import { Button } from '@/components/ui/button';
import { useQueryClient } from '@tanstack/react-query';
import { Id, toast } from 'react-toastify';
import { RealCallAutoEmailTarget } from '@/lib/Integrations/RealCalls/types';
import { RealCallAutoEmailTargetSelector } from '@/common/RealCallAutoEmailTargetSelector';
import useUserSession from '@/hooks/useUserSession';
import { AppPermissions } from '@/lib/permissions';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';

interface IProps {
  user: UserDto;
  className?: string;
}

export default function RealCallsSettings({ user, className }: IProps) {
  const { canAccess } = useUserSession();
  const queryClient = useQueryClient();
  const toastId = useRef<Id | null>(null);
  const { callTypeOptions } = useCallTypeOptions();
  const [usr, setUsr] = useState<UserDto>();

  const { data: integrations, isLoading: isLoadingIntegrations } =
    useIntegrations();

  const [usersPerService, setUsersPerService] = useState<any>({});
  const [isLoadingExternalUsers, setIsLoadingExternalUsers] =
    useState<boolean>(true);
  const [allScorecards, setAllScorecards] = useState<ScorecardConfigDto[]>([]);
  const [isLoadingScorecards, setIsLoadingScorecards] =
    useState<boolean>(false);
  const [canSaveStatus, setCanSaveStatus] = useState<any>({});
  const [savingStatus, setSavingStatus] = useState<any>({});

  useEffect(() => {
    if (!isLoadingIntegrations && integrations) {
      loadExternalUsers();
    }
  }, [isLoadingIntegrations, integrations]);

  useEffect(() => {
    setUsr(user);
  }, [user]);

  const fetchData = async () => {
    setIsLoadingScorecards(true);

    const scs = await ScorecardConfigService.getAllScorecardConfigsForOrg();
    setAllScorecards(scs);

    setIsLoadingScorecards(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  /********************************/
  /************* INIT *************/
  /********************************/

  const loadExternalUsers = async () => {
    if (integrations) {
      const tmp: any = {};
      for (const i of integrations) {
        if (i.provider?.needsUserTeamSettings) {
          const usrs = await IntegrationService.getUsersForService(
            i.provider.id,
          );
          if (!tmp[i.provider.id]) {
            tmp[i.provider.id] = usrs;
          }
        }
      }
      setUsersPerService(tmp);
    }
    setIsLoadingExternalUsers(false);
  };

  /***********************************/
  /************* ACTIONS *************/
  /***********************************/

  const upsertSettings = async (integrationId: number) => {
    setCanSaveStatus({ ...canSaveStatus, [integrationId]: false });
    setSavingStatus({ ...savingStatus, [integrationId]: true });
    let settings: IntegrationUserSettings | undefined;
    usr?.integrationsSettings?.forEach((s: IntegrationUserSettings) => {
      if (s.integrationId === integrationId) {
        settings = s;
      }
    });
    if (settings) {
      try {
        settings.userId = user.id;
        await IntegrationService.upsertUserSettings(settings);
        queryClient.invalidateQueries({ queryKey: ['orgUsers', user.id] });
      } catch (e) {
        console.log(e);
      }
    }
    toastId.current = toast.success('Settings successfully saved');
    setSavingStatus({ ...savingStatus, [integrationId]: false });
  };

  const updateSettings = (integrationId: number, key: string, v: string) => {
    setCanSaveStatus({ ...canSaveStatus, [integrationId]: true });
    let found = false;
    usr?.integrationsSettings?.forEach((s: IntegrationUserSettings) => {
      if (s.integrationId === integrationId) {
        if (key == 'externalUserId') {
          s.externalUserId = v;
        } else if (key == 'callType') {
          s.callType = v as AgentCallType;
        } else if (key == 'scorecardConfigId') {
          if (v == 'from-team') {
            s.scorecardConfigId = 0;
          } else {
            s.scorecardConfigId = parseInt(v);
          }
        }

        found = true;
      }
    });
    if (!found) {
      if (usr) {
        if (!usr.integrationsSettings) {
          usr.integrationsSettings = [];
        }
        const settings: IntegrationUserSettings = {
          userId: usr.id,
          integrationId: integrationId,
          externalUserId: '',
          scorecardConfigId: 0,
          callType: AgentCallType.COLD,
        };

        if (key == 'externalUserId') {
          settings.externalUserId = v;
        } else if (key == 'callType') {
          settings.callType = v as AgentCallType;
        } else if (key == 'scorecardConfigId') {
          if (v == 'from-team') {
            settings.scorecardConfigId = 0;
          } else {
            settings.scorecardConfigId = parseInt(v);
          }
        }
        usr.integrationsSettings.push(settings);
      }
    }
    setUsr({ ...usr } as UserDto);
  };

  /********************************/
  /************ RENDER ************/
  /********************************/

  const onChangeAutoEmailTarget = async (
    target: RealCallAutoEmailTarget | '-',
  ) => {
    if (usr) {
      if (target === '-') {
        await RealCallsService.deleteRealCallUserConfig(usr.id);
        return;
      }
      await RealCallsService.upsertRealCallUserConfig(usr.id, target);
    }
  };

  return (
    <div className={className}>
      <div className="font-semibold text-base mb-1">Real call settings</div>
      <div className="text-xs text-muted-foreground mb-6">
        Manage how real calls are scored for this user
      </div>
      {canAccess(AppPermissions.MANAGE_REAL_CALLS) && (
        <RealCallAutoEmailTargetSelector
          defaultValue={usr?.realCallConfig?.autoEmailTarget || '-'}
          onSelect={onChangeAutoEmailTarget}
          isLoading={!usr}
        />
      )}
      {integrations?.map((i) => {
        if (i.provider?.needsUserTeamSettings) {
          let settings: IntegrationUserSettings | undefined;
          usr?.integrationsSettings?.forEach((s: IntegrationUserSettings) => {
            if (s.integrationId == i.id) {
              settings = s;
            }
          });

          if (!settings) {
            settings = {} as IntegrationUserSettings;
          }

          if (settings) {
            const users = usersPerService[i.provider.id];
            let selectedUserLabel = `Select user from ${i.name}`;
            users?.map((u: ExternalUserDto) => {
              if (u.userId == settings?.externalUserId) {
                if (u.name) {
                  selectedUserLabel = u.name;
                  if (u.email) {
                    selectedUserLabel += ` (${u.email})`;
                  }
                } else if (u.email) {
                  selectedUserLabel = u.email;
                } else {
                  selectedUserLabel = u.userId;
                }
              }
            });

            let selectedCallType: any = 'Select call type';
            callTypeOptions.map((ct) => {
              if (ct.value == settings?.callType) {
                const Icon =
                  CALL_TYPE_TO_ICON[ct.value as keyof typeof CALL_TYPE_TO_ICON]
                    .Icon;
                selectedCallType = (
                  <div className="flex items-center">
                    <div className="mr-1">
                      <Icon size={12} />
                    </div>
                    <div>{ct.label}</div>
                  </div>
                );
              }
            });

            let selectedScorecard = "Use team's default";
            allScorecards.map((option) => {
              if (option.id == settings?.scorecardConfigId) {
                selectedScorecard = option.tag;
              }
            });

            return (
              <div key={i.id} className="mt-6">
                <div className="flex items-center text-base font-semibold mb-4">
                  <Image
                    src={IntegrationService.getProviderLogoUrl(
                      i.provider.logoUrl,
                    )}
                    alt={`${i.provider.companyName} Logo`}
                    width={20}
                    height={20}
                    className="mr-1"
                  />
                  <div>{i.name}</div>
                </div>

                <div className="ml-6">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="font-semibold flex items-center w-[120px]">
                      <User size={16} className="mr-2" />
                      User
                    </div>
                    <div className="w-[400px]">
                      <Select
                        onValueChange={(v: string) => {
                          updateSettings(i.id, 'externalUserId', v);
                        }}
                        value={settings?.externalUserId}
                      >
                        <SelectTrigger>
                          <div className="mr-2">{selectedUserLabel}</div>
                        </SelectTrigger>
                        <SelectContent>
                          {users?.map((u: ExternalUserDto) => {
                            return (
                              <SelectItem key={u.userId} value={u.userId}>
                                {u.name} {u.email && `(${u.email})`}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="ml-2">
                      {isLoadingExternalUsers && (
                        <Loader2Icon
                          className="animate-spin text-muted-foreground"
                          size={16}
                        />
                      )}
                    </div>
                  </div>
                  {/*
                  <div className="flex items-center space-x-2 mb-4 invisible">
                    <div className="font-semibold flex items-center w-[120px]">
                      <Phone size={16} className="mr-2" />
                      Call type
                    </div>
                    <div className="w-[400px]">
                      <Select
                        onValueChange={(v: string) => {
                          updateSettings(i.id, 'callType', v);
                        }}
                        value={settings?.callType}
                      >
                        <SelectTrigger>
                          <div className="mr-2">{selectedCallType}</div>
                        </SelectTrigger>
                        <SelectContent>
                          {CALL_TYPE_OPTIONS.map((ct, index) => {
                            const Icon =
                              CALL_TYPE_TO_ICON[
                                ct.value as keyof typeof CALL_TYPE_TO_ICON
                              ].Icon;

                            return (
                              <SelectItem key={ct.value} value={ct.value}>
                                <div className="flex items-center">
                                  <div className="mr-1">
                                    <Icon size={12} />
                                  </div>
                                  <div>{ct.label}</div>
                                </div>
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mb-4 invisible">
                    <div className="font-semibold flex items-center w-[120px]">
                      <Target size={16} className="mr-2" />
                      Scorecard
                    </div>
                    <div className="w-[400px]">
                      <Select
                        onValueChange={(v: string) => {
                          updateSettings(i.id, 'scorecardConfigId', v);
                        }}
                        value={String(settings?.scorecardConfigId)}
                      >
                        <SelectTrigger>
                          <div className="mr-2">{selectedScorecard}</div>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={'from-team'}>
                            Use team&apos;s default
                          </SelectItem>
                          {allScorecards && (
                            <>
                              {allScorecards.map((option) => {
                                return (
                                  <SelectItem
                                    key={option.id}
                                    value={String(option.id)}
                                  >
                                    {option.tag}
                                  </SelectItem>
                                );
                              })}
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="ml-2">
                      {isLoadingScorecards && (
                        <Loader2Icon
                          className="animate-spin text-muted-foreground"
                          size={16}
                        />
                      )}
                    </div>
                  </div> */}
                </div>
                <div className="flex items-center">
                  <Button
                    disabled={!canSaveStatus[i.id]}
                    onClick={() => upsertSettings(i.id)}
                  >
                    Save {i.name} Settings
                    {savingStatus[i.id] && (
                      <Loader2Icon
                        className="animate-spin text-white ml-2"
                        size={18}
                      />
                    )}
                  </Button>
                </div>
              </div>
            );
          }
        }
      })}
    </div>
  );
}
