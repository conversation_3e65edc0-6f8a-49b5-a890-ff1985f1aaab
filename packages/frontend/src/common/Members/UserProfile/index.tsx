import { Button } from '@/components/ui/button';
import useOrgUserbyId from '@/hooks/useOrgUserById';
import useRouting from '@/hooks/useRouting';
import LinksManager from '@/lib/linksManager';
import {
  Bell,
  Building2,
  CalendarClockIcon,
  ChevronLeft,
  GlobeIcon,
  Mail,
  PhoneIcon,
  Shield,
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import dayjs from 'dayjs';
import { Separator } from '@/components/ui/separator';
import Link from 'next/link';
import useUserSession from '@/hooks/useUserSession';
import RealCallsSettings from './realCallsSettings';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  userId: number;
}

export default function UserProfile({ userId }: IProps) {
  const {
    canAccessRealCallsScoring,
    canAccess,
    userId: currentUserId,
  } = useUserSession();

  const { data: user, isLoading: isLoadingUser } = useOrgUserbyId(userId);

  const { goToPage } = useRouting();

  const openAllMembersPage = () => {
    goToPage(LinksManager.members());
  };

  /********************************/
  /************ RENDER ************/
  /********************************/

  return (
    <div className="bg-[#FBFBFB] h-full flex flex-col p-4 overflow-y-auto">
      <div>
        <Button
          variant={'ghost'}
          className="text-xs"
          onClick={openAllMembersPage}
        >
          <ChevronLeft size={16} className="mr-2" />
          Members
        </Button>
      </div>
      <div className="mt-6 mx-6">
        {isLoadingUser && (
          <div className="flex items-center">
            <Skeleton className="w-20 h-20 rounded-full" />
            <div className="ml-4">
              <Skeleton className="w-40 h-6" />
              <Skeleton className="w-40 h-6 mt-2" />
            </div>
          </div>
        )}
        {!isLoadingUser && (
          <div className="flex items-center">
            <div className="flex items-center">
              <div>
                <Avatar className="w-20 h-20">
                  {user?.avatar && <AvatarImage src={user?.avatar} />}
                  <AvatarFallback className="text-lg text-muted-foreground">
                    {user?.firstName?.charAt(0) || ''}{' '}
                    {user?.lastName?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
              </div>
              <div className="ml-4">
                <div className="font-medium text-xl">
                  {user?.firstName} {user?.lastName}
                </div>
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <CalendarClockIcon className="w-4 h-4" />
                  <p className="text-sm">
                    Joined {dayjs(user?.createdAt).format('MMM D, YYYY')}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex-1" />
            <div>
              <Link
                href={LinksManager.trainingCalls(`?reps=${user?.id}`)}
                className="h-min"
              >
                <Button variant={'default'}>
                  <PhoneIcon className="w-4 h-4 mr-2" />
                  View calls
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
      <div className="mt-6 mx-6">
        <Separator />
        <div className="my-6">
          <div className="flex items-center space-x-2 mb-2">
            <div className="font-semibold flex items-center w-[80px]">
              <Mail size={16} className="mr-2" />
              Email
            </div>
            <div className="">
              {isLoadingUser ? <Skeleton className="w-40 h-6" /> : user?.email}
            </div>
          </div>

          <div className="flex items-center space-x-2 mb-2">
            <div className="font-semibold flex items-center w-[80px]">
              <Shield size={16} className="mr-2" />
              Role
            </div>
            <div className="">
              {isLoadingUser ? <Skeleton className="w-40 h-6" /> : user?.role}
            </div>
          </div>

          <div className="flex items-center space-x-2 mb-2">
            <div className="font-semibold flex items-center w-[80px]">
              <Bell size={16} className="mr-2" />
              Status
            </div>
            <div className="">
              <div className="">
                {isLoadingUser ? (
                  <Skeleton className="w-40 h-6" />
                ) : (
                  user?.status
                )}
              </div>
            </div>
          </div>
          {user?.region ? (
            <div className="flex items-center space-x-2 mb-2">
              <div className="font-semibold flex items-center w-[80px]">
                <GlobeIcon size={16} className="mr-2" />
                Region
              </div>
              <div className="">
                <div className="">
                  {isLoadingUser ? (
                    <Skeleton className="w-40 h-6" />
                  ) : (
                    user?.region
                  )}
                </div>
              </div>
            </div>
          ) : (
            <></>
          )}
          {user?.companyJoiningDate ? (
            <div className="flex items-center space-x-2 mb-2">
              <div className="font-semibold flex items-center w-[164px]">
                <Building2 size={16} className="mr-2" />
                Company Start Date
              </div>
              <div className="">
                <div className="">
                  {isLoadingUser ? (
                    <Skeleton className="w-40 h-6" />
                  ) : (
                    dayjs(user?.companyJoiningDate).format('MMM D, YYYY')
                  )}
                </div>
              </div>
            </div>
          ) : (
            <></>
          )}
        </div>
        <Separator />
      </div>

      {canAccessRealCallsScoring &&
        (canAccess(AppPermissions.MANAGE_REAL_CALLS) ||
          userId == currentUserId) &&
        user && <RealCallsSettings user={user} className="mt-6 mx-6" />}
    </div>
  );
}
