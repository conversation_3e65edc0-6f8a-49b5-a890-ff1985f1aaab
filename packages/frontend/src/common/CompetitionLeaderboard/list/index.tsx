import { AggregateScoreBreakdownModal } from '@/common/AggregateScoreBreakdownModal';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FilterType } from '@/common/Calls/AIRoleplay/List/common';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import useCallAggregatedScorecards from '@/hooks/useCallAggregatedScorecards';
import useOrgCalls from '@/hooks/useOrgCalls';
import useUserSession from '@/hooks/useUserSession';
import { AgentDto } from '@/lib/Agent/types';
import {
  CallAggregatedScorecardDto,
  CallAggregatedScorecardListDto,
  CallScorecardInnerScorecard,
  CallStatsDto,
} from '@/lib/Call/types';
import {
  CompetitionLeaderboardDto,
  CompetitionLeaderboardItem,
} from '@/lib/Competition/Leaderboard/types';
import LinksManager from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import { LinkedInLogoIcon } from '@radix-ui/react-icons';
import { ExternalLink, SearchIcon } from 'lucide-react';
import Link from 'next/link';
import { useMemo, useRef, useState } from 'react';

const stats = [
  {
    name: 'Talk Speed',
    key: 'talkSpeed',
    suffix: 'wpm',
    info: 'How fast did you speak? Recommended range is between 150 WPM and 180 WPM.',
  },
  {
    name: 'Talk/Listen Ratio',
    key: 'talkListenRatio',
    info: 'How much you talked vs. how much you listened. Recommended range is between 0.5 and 0.65',
  },
  {
    name: 'Filler Words',
    key: 'fillerWords',
    suffix: 'wpm',
    info: "How many filler words like 'um' and 'uh' did you use per min?",
  },
  {
    name: 'Longest Monologue',
    key: 'longestMonologue',
    suffix: 'secs',
    info: 'How many secs was your longest monologue? Recommended range is between 35 - 45 secs',
  },
];

interface IProps {
  leaderboard?: CompetitionLeaderboardDto;
  agent: AgentDto;
  isAgentLoading: boolean;
}

export const cleanName = (name: string) =>
  name
    .toLowerCase()
    .replaceAll(/\s+/g, ' ')
    .replaceAll(/[^a-z0-9]/g, '');

type FilterParamsBase = Record<FilterType, string[] | number[]> & {
  [FilterType.BUYERS]: number[];
  [FilterType.REPS]: number[];
};

export default function List({ leaderboard, agent, isAgentLoading }: IProps) {
  const { dbUser } = useUserSession();
  const first = leaderboard?.items[0];
  const allSections = (
    (first?.highestScoreCallDetails?.aggregatedScorecardDto?.scorecards ||
      []) as unknown as Array<{ tag?: string }>
  ).map((section) => ({
    criteria: [],
    description: '',
    sectionTitle: section.tag || '',
    totalCriteriaCount: 0,
    passedCriteriaCount: 0,
  })) as unknown as CallScorecardInnerScorecard[];

  const [modalOpen, setModalOpen] = useState(false);

  const [nameFilter, setNameFilter] = useState('');
  const [expertsFilter, setExpertsFilter] = useState(false);

  const nameCleanedLeaderboardItems = useMemo(() => {
    return [...(leaderboard?.items || [])].map((item) => ({
      ...item,
      nameClean: cleanName(`${item?.firstName || ''} ${item?.lastName || ''}`),
    }));
  }, [leaderboard?.items]);

  const filteredLeaderboardItems = useMemo(() => {
    let mappedItems = nameCleanedLeaderboardItems.map((item, idx) => ({
      ...item,
      ogIdx: idx,
    }));
    if (expertsFilter) {
      mappedItems = mappedItems.filter((i) => i.isInfluencer);
    }
    if (nameFilter) {
      const nameFilterClean = cleanName(nameFilter);
      mappedItems = mappedItems.filter((i) =>
        i.nameClean.includes(nameFilterClean),
      );
    }
    return mappedItems;
  }, [nameCleanedLeaderboardItems, expertsFilter, nameFilter]);

  const { data: userCalls, isLoading: isCallLoading } = useOrgCalls(
    0,
    1000,
    [],
    {
      [FilterType.BUYERS]: [agent?.id],
      [FilterType.REPS]: [dbUser?.id],
    } as FilterParamsBase,
  );
  const {
    data: userCallAggregatedScorecardList,
    isLoading: isCallAggregatedScorecardLoading,
  } = useCallAggregatedScorecards(
    userCalls?.map((c) => c.vapiId as string) || [],
    !!userCalls?.length,
  );

  const [
    selectedCompetitionLeaderboardItem,
    setSelectedCompetitionLeaderboardItem,
  ] = useState<CompetitionLeaderboardItem | null>(null);
  const [isUsersBreakdown, setIsUsersBreakdown] = useState(false);
  const [calls, setCalls] = useState<
    {
      id: number;
      createdAt: Date;
      vapiId: string;
    }[]
  >([]);
  const [callAggregatedScorecardList, setCallAggregatedScorecardList] =
    useState<CallAggregatedScorecardListDto>({});

  const userRank = useMemo(() => {
    if (!leaderboard || !dbUser) {
      return '-';
    }
    return (
      leaderboard.items.find((i) => i.userId === dbUser.id)?.rank?.toString() ||
      '-'
    );
  }, [leaderboard]);
  const userRowRef = useRef<HTMLTableRowElement>(null);

  const onClickRow = (item: CompetitionLeaderboardItem) => {
    setSelectedCompetitionLeaderboardItem(item);
    const isUser = item.userId === dbUser?.id;
    setIsUsersBreakdown(isUser);
    if (!isUser) {
      const callId = item?.highestScoreCallDetails?.id || 0;
      const callVapiId = item?.highestScoreCallDetails?.vapiId || '';
      setCalls([
        {
          id: callId,
          vapiId: callVapiId,
          createdAt: new Date(),
        },
      ]);
      setCallAggregatedScorecardList({
        [callVapiId]: item.highestScoreCallDetails.aggregatedScorecardDto,
      });
    }
    setModalOpen(true);
  };

  const displayStat = (key: string, score: number) => {
    if (key === 'fillerWords') {
      return `${score.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      })} wpm`;
    }

    if (key === 'talkListenRatio') {
      return `${score.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
        style: 'percent',
      })}`;
    }

    if (key === 'talkSpeed') {
      return `${score.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      })} wpm`;
    }

    if (key === 'longestMonologue') {
      return `${score.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      })} secs`;
    }

    return score;
  };

  const colorCodeStat = (key: string, score: number, isInRange: boolean) => {
    if (key === 'fillerWords') {
      if (isInRange) {
        return score >= 0 && score <= 1;
      } else {
        return !(score >= 0 && score <= 1);
      }
    }

    if (key === 'talkListenRatio') {
      if (isInRange) {
        return score >= 0.45 && score <= 0.65;
      } else {
        return !(score >= 0.45 && score <= 0.65);
      }
    }

    if (key === 'talkSpeed') {
      if (isInRange) {
        return score >= 150 && score <= 180;
      } else {
        return !(score >= 150 && score <= 180);
      }
    }

    if (key === 'longestMonologue') {
      if (isInRange) {
        return score >= 35 && score <= 45;
      } else {
        return !(score >= 35 && score <= 45);
      }
    }

    return false;
  };

  const renderScore = (
    score: number,
    key: string,
    isStat: boolean,
    useIcon = false,
  ) => {
    let metric: 'low' | 'medium' | 'high' = 'low';
    if (isStat) {
      metric = colorCodeStat(key, score, true) ? 'high' : 'low';
    } else {
      metric = score >= 80 ? 'high' : score >= 60 ? 'medium' : 'low';
    }
    return (
      <div className="flex flex-row justify-center items-center">
        {useIcon && (
          <img
            src={`/images/competition/stars-${metric === 'high' ? 'blue' : metric === 'medium' ? 'gold' : 'red'}.svg`}
            className="w-4 h-4 mr-2"
          />
        )}
        <p
          className={`${
            metric === 'high'
              ? 'text-[#45C6D9]'
              : metric === 'medium'
                ? 'text-[#A7820F]'
                : 'text-[#9D1C1D]'
          } text-center font-semibold ${useIcon ? 'text-lg' : ''}`}
        >
          {isStat
            ? displayStat(key, score)
            : score.toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 1,
              })}
        </p>
      </div>
    );
  };

  return (
    <div className="flex flex-col pt-4">
      <div className="flex flex-row items-center">
        {!!dbUser && (
          <div className="flex flex-row items-center">
            <p className="font-bold text-muted-foreground text-lg">My rank:</p>
            <div className="relative mt-[4px]">
              <img
                src="/images/competition/my-rank-badge.svg"
                className="w-16"
                style={{
                  aspectRatio: 56 / 60,
                }}
              />
              <div className="absolute left-0 right-0 top-0 bottom-0 flex flex-row justify-center items-center">
                <span className="text-xl text-muted-foreground font-bold mb-[4px] mr-[2px]">
                  {userRank}
                </span>
              </div>
            </div>
            {userRank !== '-' && (
              <p
                className="underline font-medium text-muted-foreground underline-offset-4 cursor-pointer"
                onClick={() => {
                  userRowRef?.current?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                  });
                }}
              >
                Show in the leaderboard
              </p>
            )}
          </div>
        )}
        <div className="flex-1" />
        <Tabs
          value={expertsFilter ? 'experts' : 'all'}
          onValueChange={(v) => setExpertsFilter(v === 'experts')}
          className="mr-4"
        >
          <TabsList className="bg-white">
            <TabsTrigger value="all" className="data-[state=active]:border">
              All Participants
            </TabsTrigger>
            <TabsTrigger value="experts" className="data-[state=active]:border">
              <img
                src="/images/competition/check-black.svg"
                className="mr-2"
                style={{
                  filter: expertsFilter ? '' : 'invert(.5)',
                }}
              />
              <p>Experts</p>
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="relative">
          <div className="absolute left-0 top-0 bottom-0 flex flex-col pl-2.5 justify-center">
            <SearchIcon className="text-muted-foreground w-4 h-4" />
          </div>
          <Input
            className="text-black w-[200px] lg:w-[300px] bg-white/70 pl-8"
            placeholder="Search"
            value={nameFilter}
            onChange={(e) => {
              setNameFilter(e.target.value);
            }}
          />
        </div>
      </div>
      <div className="border rounded-xl mt-4">
        <Table className="bg-white/70 rounded-xl">
          <TableHeader>
            <TableRow className="bg-muted/40 h-16">
              <TableHead className="sticky left-0 bg-[#FBFBFB] z-50">
                <p className="w-[50px]">Rank</p>
              </TableHead>
              <TableHead className="sticky left-[66px] bg-[#FBFBFB] z-40 drop-shadow-md">
                <p className="w-[250px] lg:w-[350px]">Player</p>
              </TableHead>
              <TableHead className="text-center">High Score</TableHead>
              {allSections.map((section, index) => {
                return (
                  <TableHead
                    key={index}
                    className="whitespace-pre-wrap text-center"
                  >
                    {section.sectionTitle.split(/\s+/).join('\n')}
                  </TableHead>
                );
              })}
              {stats.map((stat, index) => {
                return (
                  <TableHead
                    key={index}
                    className="whitespace-pre-wrap text-center"
                  >
                    {stat.name.split(/\s+/).join('\n')}
                  </TableHead>
                );
              })}
              <TableHead>&nbsp;</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLeaderboardItems.map((item) => {
              const isUser = item?.userId === dbUser?.id;
              const aggScorePerc =
                (item.highestScoreCallDetails?.aggregatedScorecardDto
                  ?.aggregateScore || 0) * 100;
              return (
                <TableRow
                  key={item.ogIdx}
                  className={cn('group cursor-pointer hover:bg-[#F5FCFC]', {
                    'bg-teal-100 hover:bg-teal-200': isUser,
                  })}
                  {...(isUser ? { ref: userRowRef } : {})}
                  onClick={() => onClickRow(item)}
                >
                  <TableCell
                    className={cn(
                      'sticky left-0 px-[8px] z-50 bg-white group-hover:bg-[#F5FCFC]',
                      {
                        'bg-teal-100 group-hover:bg-teal-200': isUser,
                      },
                    )}
                  >
                    <div className="w-[50px] text-primary text-base flex flex-row justify-center">
                      {item.ogIdx < 3 ? (
                        <div>
                          <img
                            src={`/images/competition/medals/medal-${item.ogIdx + 1}.svg`}
                          />
                        </div>
                      ) : (
                        <p>{item.ogIdx + 1}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell
                    className={cn(
                      'sticky left-[66px] z-40 bg-white group-hover:bg-[#F5FCFC] drop-shadow-md',
                      {
                        'bg-teal-100 group-hover:bg-teal-200': isUser,
                      },
                    )}
                  >
                    <div className="w-[250px] lg:w-[350px] flex flex-col lg:flex-row space-y-2 lg:space-y-0 lg:space-x-2 items-start justify-center lg:justify-start lg:items-center">
                      <Link
                        href={item.linkedInUrl || '#'}
                        {...(item.linkedInUrl
                          ? {
                              target: '_blank',
                              rel: 'noopener noreferrer',
                            }
                          : {})}
                      >
                        <Button
                          variant={'outline'}
                          className="space-x-2 pl-1 pr-3 py-0 h-[29px] text-base rounded-full bg-white text-primary hover:bg-muted/80 hover:transition-all duration-300"
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <Avatar className="w-5 h-5">
                            <AvatarImage src={item.avatar} />
                            <AvatarFallback className="text-xs">
                              {item?.firstName?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <div className="capitalize">
                            {item?.firstName || ''} {item?.lastName || ''}
                          </div>
                          {!!item.linkedInUrl && <LinkedInLogoIcon />}
                        </Button>
                      </Link>
                      {item.isInfluencer && (
                        <div className="flex items-center space-x-1 px-1.5 rounded-full py-1 bg-[#3C82F626] text-sm font-medium m-0">
                          <img src="/images/competition/check-blue.svg" />
                          <span className="text-[#105AD4]">{'Expert'}</span>
                        </div>
                      )}
                      {isUser && (
                        <div className="flex items-center space-x-1 px-3 rounded-full py-1 bg-green-300 text-sm font-medium">
                          <span className="text-black">{'You'}</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className={cn(' text-center')}>
                    {renderScore(aggScorePerc, '', false, true)}
                  </TableCell>
                  {allSections.map((section, index) => {
                    let cr: CallScorecardInnerScorecard | undefined;
                    for (const c of (item?.highestScoreCallDetails
                      ?.aggregatedScorecardDto?.scorecards ||
                      []) as unknown as CallScorecardInnerScorecard[]) {
                      if (c.sectionTitle === section.sectionTitle) {
                        cr = c;
                        break;
                      }
                    }
                    let score = 0;
                    if (cr) {
                      if (
                        cr.passedCriteriaCount > 0 &&
                        cr.totalCriteriaCount > 0
                      ) {
                        score = Math.round(
                          (cr.passedCriteriaCount / cr.totalCriteriaCount) *
                            100,
                        );
                      }
                    }
                    return (
                      <TableHead key={index} className={cn('text-center')}>
                        {renderScore(score, '', false, false)}
                      </TableHead>
                    );
                  })}
                  {stats.map((stat, index) => {
                    const statKey = stat.key as keyof CallStatsDto;
                    const score: number =
                      (
                        item?.highestScoreCallDetails
                          ?.aggregatedScorecardDto as CallAggregatedScorecardDto
                      )?.[statKey]?.value || 0;
                    return (
                      <TableHead key={index} className={cn('text-center')}>
                        {renderScore(score, stat.key, true, false)}
                      </TableHead>
                    );
                  })}

                  <TableCell>
                    <Link
                      className={cn('text-muted-foreground', {
                        'pointer-events-none opacity-0':
                          item.userId !== dbUser?.id,
                      })}
                      href={LinksManager.trainingCalls(
                        `${item.highestScoreCallDetails?.vapiId}`,
                      )}
                      target="_blank"
                    >
                      <ExternalLink size={18} />
                    </Link>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        <AggregateScoreBreakdownModal
          modalOpen={modalOpen}
          setModalOpen={setModalOpen}
          calls={isUsersBreakdown ? userCalls || [] : calls}
          callAggregatedScorecardList={
            isUsersBreakdown
              ? userCallAggregatedScorecardList || {}
              : callAggregatedScorecardList
          }
          agent={leaderboard?.agent}
          scorecardConfig={leaderboard?.scorecardConfig}
          isLoading={
            isUsersBreakdown
              ? isCallLoading ||
                isAgentLoading ||
                isCallAggregatedScorecardLoading
              : false
          }
          triesLeft={
            isUsersBreakdown
              ? agent?.competitionAgent?.user?.numTriesLeft || 0
              : 0
          }
          highestScoreCallId={
            selectedCompetitionLeaderboardItem?.highestScoreCallDetails?.id
          }
          firstName={selectedCompetitionLeaderboardItem?.firstName || ''}
          lastName={selectedCompetitionLeaderboardItem?.lastName || ''}
          isUsersBreakdown={isUsersBreakdown}
          hideUnattemptedColumns={!isUsersBreakdown}
        />
      </div>
    </div>
  );
}
