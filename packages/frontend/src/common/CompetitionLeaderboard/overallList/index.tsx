import { AggregateScoreBreakdownModal } from '@/common/AggregateScoreBreakdownModal';
import { FilterType } from '@/common/Calls/AIRoleplay/List/common';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import _ from 'lodash';
import useCallAggregatedScorecards from '@/hooks/useCallAggregatedScorecards';
import { useCompetitionOrgAgent } from '@/hooks/useCompetitionOrgAgent';
import useOrgAgents from '@/hooks/useOrgAgents';
import useOrgCalls from '@/hooks/useOrgCalls';
import useUserSession from '@/hooks/useUserSession';
import { AgentDto, AgentStatus } from '@/lib/Agent/types';
import { CallAggregatedScorecardListDto } from '@/lib/Call/types';
import {
  CompetitionDetailsDto,
  CompetitionLeaderboardDto,
  CompetitionLeaderboardItem,
  CompetitionOverallLeaderboardDto,
  CompetitionOverallLeaderboardItem,
} from '@/lib/Competition/Leaderboard/types';
import LinksManager from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import { LinkedInLogoIcon, StarFilledIcon } from '@radix-ui/react-icons';
import {
  ArrowDownFromLine,
  ExternalLink,
  Lock,
  Phone,
  Search,
  SearchIcon,
  StarIcon,
  TrophyIcon,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useMemo, useRef, useState } from 'react';

const competitionInfoMap: {
  [competitionTag: string]: {
    finalAgentVapiId: string;
    finalAgentMinAvgScore: number;
    startDate: Date;
  };
} = {
  losl: {
    finalAgentVapiId: 'e70e4777-7106-4c37-be6e-a40dd9faeacb',
    finalAgentMinAvgScore: 60,
    startDate: new Date('2025-01-13T08:00:00Z'),
  },
};

interface IProps {
  competitionTag: string;
  isUserInCompetitionOrg: boolean;
  leaderboard?: CompetitionOverallLeaderboardDto;
  orgAgents?: AgentDto[];
  goToAgentId: (agentId: number) => void;
}

export const cleanName = (name: string) =>
  name
    .toLowerCase()
    .replaceAll(/\s+/g, ' ')
    .replaceAll(/[^a-z0-9]/g, '');

export default function OverallList({
  competitionTag,
  isUserInCompetitionOrg,
  leaderboard,
  orgAgents,
  goToAgentId,
}: IProps) {
  const competitionInfo = competitionInfoMap[competitionTag];
  const { dbUser } = useUserSession();
  const [modalOpen, setModalOpen] = useState(false);

  const [nameFilter, setNameFilter] = useState('');
  const [expertsFilter, setExpertsFilter] = useState(false);

  const nameCleanedLeaderboardItems = useMemo(() => {
    return [...(leaderboard?.items || [])].map((item) => ({
      ...item,
      nameClean: cleanName(`${item?.firstName || ''} ${item?.lastName || ''}`),
    }));
  }, [leaderboard?.items]);

  const filteredLeaderboardItems = useMemo(() => {
    let mappedItems = nameCleanedLeaderboardItems.map((item, idx) => ({
      ...item,
      ogIdx: idx,
    }));
    if (expertsFilter) {
      mappedItems = mappedItems.filter((i) => i.isInfluencer);
    }
    if (nameFilter) {
      const nameFilterClean = cleanName(nameFilter);
      mappedItems = mappedItems.filter((i) =>
        i.nameClean.includes(nameFilterClean),
      );
    }
    return mappedItems;
  }, [nameCleanedLeaderboardItems, expertsFilter, nameFilter]);

  const { data: userCalls, isLoading: isCallLoading } = useOrgCalls(
    0,
    1000,
    [],
    {
      [FilterType.REPS]: [dbUser?.id],
    } as any,
  );
  const {
    data: userCallAggregatedScorecardList,
    isLoading: isCallAggregatedScorecardLoading,
  } = useCallAggregatedScorecards(
    userCalls?.map((c) => c.vapiId) || [],
    !!userCalls?.length,
  );

  const [
    selectedCompetitionLeaderboardItem,
    setSelectedCompetitionLeaderboardItem,
  ] = useState<{
    firstName: string;
    lastName: string;
    highestScoreCallId: number;
    agent: {
      id: number;
      firstName: string;
      lastName: string;
      vapiId: string;
      iconUrl?: string;
    };
    agentDto?: AgentDto;
    scorecardConfig: any;
  } | null>(null);
  const [isUsersBreakdown, setIsUsersBreakdown] = useState(false);
  const [calls, setCalls] = useState<
    {
      id: number;
      createdAt: Date;
      vapiId: string;
    }[]
  >([]);
  const [callAggregatedScorecardList, setCallAggregatedScorecardList] =
    useState<CallAggregatedScorecardListDto>({});

  const userRank = useMemo(() => {
    if (!leaderboard || !dbUser) {
      return '-';
    }
    return (
      leaderboard.items.find((i) => i.userId === dbUser.id)?.rank?.toString() ||
      '-'
    );
  }, [leaderboard]);

  const onClickRow = (
    item: CompetitionOverallLeaderboardItem,
    agentId: number,
  ) => {
    if (!leaderboard) {
      return;
    }
    const agentInfo = leaderboard.agents.find((a) => a.id === agentId);
    if (!agentInfo) {
      return;
    }
    const highestScoreCall = item.highestScoreCallDetailsMap[agentId];
    setSelectedCompetitionLeaderboardItem({
      firstName: item.firstName,
      lastName: item.lastName,
      highestScoreCallId: highestScoreCall.id,
      scorecardConfig: agentInfo?.scorecardConfig,
      agent: agentInfo,
      agentDto: orgAgents?.find((oa) => oa.id === agentInfo.id),
    });
    const isUser = item.userId === dbUser?.id;
    setIsUsersBreakdown(isUser);
    if (!isUser) {
      const callId = highestScoreCall.id || 0;
      const callVapiId = highestScoreCall.vapiId || '';
      setCalls([
        {
          id: callId,
          vapiId: callVapiId,
          createdAt: new Date(),
        },
      ]);
      setCallAggregatedScorecardList({
        [callVapiId]: highestScoreCall.aggregatedScorecardDto,
      });
    }
    setModalOpen(true);
  };
  const userRowRef = useRef<HTMLTableRowElement>(null);

  const renderScore = (score: number, useIcon = false) => {
    const metric = score >= 80 ? 'high' : score >= 60 ? 'medium' : 'low';
    return (
      <div className="flex flex-row justify-center items-center">
        {useIcon && (
          <Image
            alt="stars"
            src={`/images/competition/stars-${metric === 'high' ? 'blue' : metric === 'medium' ? 'gold' : 'red'}.svg`}
            className="w-4 h-4 mr-2"
            width={16}
            height={16}
          />
        )}
        <p
          className={`${
            metric === 'high'
              ? 'text-[#45C6D9]'
              : metric === 'medium'
                ? 'text-[#A7820F]'
                : 'text-[#9D1C1D]'
          } text-center text-lg font-semibold`}
        >
          {score.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 1,
          })}
        </p>
      </div>
    );
  };

  const isFinalAgentLocked = useMemo(() => {
    if (!dbUser || !leaderboard || !competitionInfo) {
      return false;
    }
    const userItem = leaderboard.items.find((i) => i.userId === dbUser.id);
    if (!userItem) {
      return false;
    }
    if (competitionTag === 'losl') {
      const totalNumberOfAgents = leaderboard.agents.length;
      const avgScore =
        (Object.values(userItem.highestScoreCallDetailsMap)
          .filter((v) => v.vapiId !== competitionInfo.finalAgentVapiId)
          .map((v) => v.aggregatedScorecardDto?.aggregateScore || 0)
          .reduce((prev, curr) => prev + curr, 0) *
          100) /
        (totalNumberOfAgents - 1);

      console.log('AVERAGE SCORE', avgScore);
      // if avg score is < minAvgScore for everything until final agent, final agent is locked
      return avgScore < competitionInfo.finalAgentMinAvgScore;
    }
    return false;
  }, [leaderboard, dbUser, competitionTag, competitionInfo]);

  const totalNumberOfAgents = leaderboard?.agents.length;
  const userItem = (leaderboard?.items || []).find(
    (i) => i.userId === dbUser?.id,
  );
  const avgScore = Number(
    totalNumberOfAgents && userItem
      ? (Object.values(userItem.highestScoreCallDetailsMap)
          .filter((v) => v.vapiId !== competitionInfo.finalAgentVapiId)
          .map((v) => v.aggregatedScorecardDto?.aggregateScore || 0)
          .reduce((prev, curr) => prev + curr, 0) *
          100) /
          (totalNumberOfAgents - 1)
      : 0,
  ).toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 1,
  });

  const hasCompetitionStarted = useMemo(() => {
    if (!competitionInfo) {
      return false;
    }
    const now = new Date();
    return now.getTime() >= competitionInfo.startDate.getTime();
  }, [competitionTag, competitionInfo]);

  if (!leaderboard) {
    return <div />;
  }

  return (
    <div className="flex flex-col pt-4">
      <div className="flex flex-row items-center">
        {!!dbUser && (
          <div className="flex flex-row items-center">
            <p className="font-bold text-muted-foreground text-lg">My rank:</p>
            <div className="relative mt-[4px]">
              <Image
                alt="my rank badge"
                src="/images/competition/my-rank-badge.svg"
                className="w-16"
                style={{
                  aspectRatio: 56 / 60,
                }}
                width={64}
                height={64}
              />
              <div className="absolute left-0 right-0 top-0 bottom-0 flex flex-row justify-center items-center">
                <span className="text-xl text-muted-foreground font-bold mb-[4px] mr-[2px]">
                  {userRank}
                </span>
              </div>
            </div>
            {userRank !== '-' && (
              <p
                className="underline font-medium text-muted-foreground underline-offset-4 cursor-pointer"
                onClick={() => {
                  userRowRef?.current?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                  });
                }}
              >
                Show in the leaderboard
              </p>
            )}
          </div>
        )}
        <div className="flex-1" />
        <Tabs
          value={expertsFilter ? 'experts' : 'all'}
          onValueChange={(v) => setExpertsFilter(v === 'experts')}
          className="mr-4"
        >
          <TabsList className="bg-white">
            <TabsTrigger value="all" className="data-[state=active]:border">
              All Participants ({leaderboard.items.length})
            </TabsTrigger>
            <TabsTrigger value="experts" className="data-[state=active]:border">
              <Image
                alt="check icon"
                src="/images/competition/check-black.svg"
                className="mr-2"
                style={{
                  filter: expertsFilter ? '' : 'invert(.5)',
                }}
                width={16}
                height={16}
              />
              <p>
                Experts (
                {_.countBy(leaderboard.items, 'isInfluencer').true || 0})
              </p>
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="relative">
          <div className="absolute left-0 top-0 bottom-0 flex flex-col pl-2.5 justify-center">
            <SearchIcon className="text-muted-foreground w-4 h-4" />
          </div>
          <Input
            className="text-black w-[200px] lg:w-[300px] bg-white/70 pl-8"
            placeholder="Search"
            value={nameFilter}
            onChange={(e) => {
              setNameFilter(e.target.value);
            }}
          />
        </div>
      </div>

      <div className="border rounded-xl">
        <Table className="bg-white/70 rounded-xl">
          <TableHeader>
            <TableRow className="bg-muted/40">
              <TableHead className="sticky left-0 bg-[#FBFBFB] z-50">
                <p className="w-[50px]">Rank</p>
              </TableHead>
              <TableHead className="sticky left-[66px] bg-[#FBFBFB] z-40 drop-shadow-md">
                <p className="w-[250px] lg:w-[350px]">Player</p>
              </TableHead>
              <TableHead className="text-center">Average High Score</TableHead>
              {leaderboard.agents.map((agent, index) => {
                return (
                  <TableHead
                    key={index}
                    className="text-center cursor-pointer hover:bg-[#efefef] rounded-lg"
                    onClick={() => {
                      goToAgentId(agent.id);
                    }}
                  >
                    <div className="flex flex-col items-center space-y-1.5 py-2">
                      {agent.iconUrl && (
                        <Image
                          alt="agent icon"
                          src={`/images/competition/icons/${agent.iconUrl}`}
                          className="mr-2 w-4 h-4"
                          style={{
                            filter: 'invert(.5)',
                          }}
                          width={16}
                          height={16}
                        />
                      )}
                      <p>
                        {agent.firstName} {agent.lastName}
                      </p>
                    </div>
                  </TableHead>
                );
              })}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLeaderboardItems.map((item) => {
              const isUser = item?.userId === dbUser?.id;
              const avgAggScorePerc = (item?.averageAggregateScore || 0) * 100;
              return (
                <TableRow
                  key={item.ogIdx}
                  className={cn('group hover:bg-[#F5FCFC]', {
                    'bg-teal-100 hover:bg-teal-200': isUser,
                  })}
                  {...(isUser ? { ref: userRowRef } : {})}
                >
                  <TableCell
                    className={cn(
                      'sticky left-0 px-[8px] z-50 bg-white group-hover:bg-[#F5FCFC]',
                      {
                        'bg-teal-100 group-hover:bg-teal-200': isUser,
                      },
                    )}
                  >
                    <div className=" text-primary text-base flex flex-row justify-center">
                      {item.ogIdx < 3 ? (
                        <div>
                          <Image
                            alt="medal icon"
                            src={`/images/competition/medals/medal-${item.ogIdx + 1}.svg`}
                            width={24}
                            height={24}
                          />
                        </div>
                      ) : (
                        <p>{item.ogIdx + 1}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell
                    className={cn(
                      'sticky left-[66px] z-40 bg-white group-hover:bg-[#F5FCFC] drop-shadow-md',
                      {
                        'bg-teal-100 group-hover:bg-teal-200': isUser,
                      },
                    )}
                  >
                    <div className="w-[250px] lg:w-[420px] flex flex-col lg:flex-row space-y-2 lg:space-y-0 lg:space-x-2 items-start justify-center lg:justify-start lg:items-center">
                      <Link
                        href={item.linkedInUrl || '#'}
                        {...(item.linkedInUrl
                          ? {
                              target: '_blank',
                              rel: 'noopener noreferrer',
                            }
                          : {})}
                      >
                        <Button
                          variant={'outline'}
                          className="space-x-2 pl-1 pr-3 py-0 h-[29px] text-base rounded-full bg-white text-primary hover:bg-muted/80 hover:transition-all duration-300"
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <Avatar className="w-5 h-5">
                            <AvatarImage src={item.avatar} />
                            <AvatarFallback className="text-xs">
                              {item?.firstName?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <div className="capitalize">
                            {item?.firstName || ''} {item?.lastName || ''}
                          </div>
                          {!!item.linkedInUrl && <LinkedInLogoIcon />}
                        </Button>
                      </Link>
                      {item.isInfluencer && (
                        <div className="flex items-center space-x-1 px-1.5 rounded-full py-1 bg-[#3C82F626] text-sm font-medium">
                          <Image
                            alt="check icon"
                            width={16}
                            height={16}
                            src="/images/competition/check-blue.svg"
                          />
                          <span className="text-[#105AD4]">{'Expert'}</span>
                        </div>
                      )}
                      {isUser && (
                        <div className="flex items-center space-x-1 px-3 rounded-full py-1 bg-green-300 text-sm font-medium">
                          <span className="text-black">{'You'}</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className={cn('text-center')}>
                    {renderScore(avgAggScorePerc, true)}
                  </TableCell>
                  {leaderboard.agents.map((agent, index) => {
                    const aggScore =
                      item.highestScoreCallDetailsMap[agent.id]
                        ?.aggregatedScorecardDto?.aggregateScore || 0;
                    const isFinalAgent =
                      agent.vapiId === competitionInfo?.finalAgentVapiId;
                    const agentLockedReason =
                      isFinalAgent && isFinalAgentLocked
                        ? `You need to get average of ${competitionInfo?.finalAgentMinAvgScore || 0} in your other bots to unlock this bot. Your current average on the other bots is: ${avgScore}`
                        : !hasCompetitionStarted
                          ? 'Competition has not started yet'
                          : '';
                    const isAgentLocked = !!agentLockedReason;
                    const showCallButton =
                      isUser && isUserInCompetitionOrg && !aggScore;
                    let callButton = showCallButton ? (
                      <a
                        href={isAgentLocked ? '#' : `/buyers/${agent.vapiId}`}
                        className={cn(
                          'px-3 py-2 flex flex-row items-center space-x-2 rounded-lg bg-[#ECF3FA] border border-gray-300',
                          {
                            'cursor-not-allowed': isAgentLocked,
                            'hover:scale-95 transition-transform duration-200':
                              !isAgentLocked,
                          },
                        )}
                      >
                        {isAgentLocked && (
                          <Lock className="w-4 h-4" color="#1A829D" />
                        )}
                        {!isAgentLocked && (
                          <Phone className="w-4 h-4" color="#1A829D" />
                        )}
                        <p className="text-[#1A829D] text font-medium">
                          {isAgentLocked ? 'Locked' : 'Call now'}
                        </p>
                      </a>
                    ) : (
                      <div />
                    );
                    if (showCallButton && isAgentLocked) {
                      callButton = (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>{callButton}</TooltipTrigger>
                            <TooltipContent>{agentLockedReason}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      );
                    }
                    return (
                      <TableCell key={index} className={cn('')}>
                        <div className="flex flex-row justify-center">
                          {showCallButton ? (
                            callButton
                          ) : (
                            <div
                              className="cursor-pointer"
                              onClick={() => onClickRow(item, agent.id)}
                            >
                              {renderScore(aggScore * 100, false)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    );
                  })}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        <AggregateScoreBreakdownModal
          modalOpen={modalOpen}
          setModalOpen={setModalOpen}
          calls={isUsersBreakdown ? userCalls || [] : calls}
          callAggregatedScorecardList={
            isUsersBreakdown
              ? userCallAggregatedScorecardList || {}
              : callAggregatedScorecardList
          }
          agent={selectedCompetitionLeaderboardItem?.agent}
          scorecardConfig={selectedCompetitionLeaderboardItem?.scorecardConfig}
          isLoading={
            isUsersBreakdown
              ? isCallLoading || isCallAggregatedScorecardLoading
              : false
          }
          triesLeft={
            isUsersBreakdown
              ? selectedCompetitionLeaderboardItem?.agentDto?.competitionAgent
                  ?.user?.numTriesLeft || 0
              : 0
          }
          highestScoreCallId={
            selectedCompetitionLeaderboardItem?.highestScoreCallId
          }
          firstName={selectedCompetitionLeaderboardItem?.firstName || ''}
          lastName={selectedCompetitionLeaderboardItem?.lastName || ''}
          isUsersBreakdown={isUsersBreakdown}
          hideUnattemptedColumns={!isUsersBreakdown}
        />
      </div>
    </div>
  );
}
