import { Skeleton } from '@/components/ui/skeleton';
import useCompetitionLeaderboards from '@/hooks/useCompetitionLeaderboard';
import { AnimatePresence, motion } from 'framer-motion';
import List from './list';
import Podium from './podium';
import { useEffect, useMemo, useState } from 'react';
import {
  CompetitionLeaderboardDto,
  CompetitionLeaderboardItem,
  CompetitionOverallLeaderboardDto,
  CompetitionOverallLeaderboardItem,
} from '@/lib/Competition/Leaderboard/types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import OverallList from './overallList';
import { AgentStatus } from '@/lib/Agent/types';
import useOrgAgents from '@/hooks/useOrgAgents';
import useUserSession from '@/hooks/useUserSession';
import useCountdown from '../Home/Competitions/CountdownTimer/useCountdown';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import useCompetitionDetails from '@/hooks/useCompetitionDetails';
import { useRouter, useSearchParams } from 'next/navigation';

interface IProps {
  competitionTag: string;
  className?: string;
}

const winnersIdx = Object.fromEntries([].map((userId, idx) => [userId, idx]));

export default function CompetitionLeaderboard({
  competitionTag,
  className,
}: IProps) {
  const { dbOrg } = useUserSession();
  const { data: details, isLoading: isLoadingDetails } =
    useCompetitionDetails(competitionTag);
  const { data: leaderboards, isLoading: isLoadingLeaderboards } =
    useCompetitionLeaderboards(competitionTag);
  const { data: orgAgents, isLoading: isOrgAgentsLoading } = useOrgAgents(
    undefined,
    true,
    undefined,
    100,
    undefined,
    AgentStatus.ACTIVE,
  );

  const idToAgent = useMemo(() => {
    return Object.fromEntries(orgAgents?.map((a) => [a.id, a]) || []);
  }, [orgAgents]);

  const sortedLeaderboards = useMemo(() => {
    return (
      leaderboards?.map?.(
        (leaderboard) =>
          ({
            ...leaderboard,
            items: [...leaderboard.items]
              .map((leaderboardItem, idx) => ({
                ...leaderboardItem,
                ogIdx: idx,
              }))
              .sort((i1, i2) => {
                /* eslint-disable no-prototype-builtins */
                if (
                  winnersIdx.hasOwnProperty(i1.userId) &&
                  winnersIdx.hasOwnProperty(i2.userId)
                ) {
                  return winnersIdx[i1.userId] - winnersIdx[i2.userId];
                } else if (winnersIdx.hasOwnProperty(i1.userId)) {
                  return -1;
                } else if (winnersIdx.hasOwnProperty(i2.userId)) {
                  return 1;
                }
                /* eslint-enable no-prototype-builtins */
                return i1.ogIdx - i2.ogIdx;
              }),
          }) as CompetitionLeaderboardDto,
      ) || []
    );
  }, [leaderboards]);

  const overallLeaderboard: CompetitionOverallLeaderboardDto = useMemo(() => {
    const userIdToLeaderboardItems: {
      [userId: number]: (CompetitionLeaderboardItem & { agentId: number })[];
    } = {};
    sortedLeaderboards.forEach((l) => {
      l.items.forEach((i) => {
        if (!userIdToLeaderboardItems[i.userId]) {
          userIdToLeaderboardItems[i.userId] = [];
        }
        userIdToLeaderboardItems[i.userId].push({ ...i, agentId: l.agent.id });
      });
    });
    const items: CompetitionOverallLeaderboardItem[] = Object.values(
      userIdToLeaderboardItems,
    )
      .map((individualLeaderboardItems) => {
        return {
          ...individualLeaderboardItems[0],
          averageAggregateScore:
            individualLeaderboardItems
              .map(
                (i) =>
                  i.highestScoreCallDetails.aggregatedScorecardDto
                    .aggregateScore || 0,
              )
              .reduce((prev, curr) => prev + curr, 0) /
            individualLeaderboardItems.length,
          highestScoreCallDetailsMap: Object.fromEntries(
            individualLeaderboardItems.map((i) => [
              i.agentId,
              i.highestScoreCallDetails,
            ]),
          ),
        };
      })
      .sort((i1, i2) => {
        if (i1.averageAggregateScore !== i2.averageAggregateScore) {
          return i2.averageAggregateScore - i1.averageAggregateScore;
        }
        return i1.userId - i2.userId;
      });
    items.forEach((item, idx) => {
      item.rank = idx + 1;
    });
    return {
      agents: sortedLeaderboards.map((l) => ({
        ...l.agent,
        scorecardConfig: l.scorecardConfig,
      })),
      items,
    };
  }, [sortedLeaderboards]);

  const competitionCountdown = useCountdown(details?.endsAt || new Date());

  const countdownDisplayInfo = useMemo(() => {
    const v = competitionCountdown.map((u) => u.toString().padStart(2, '0'));
    return [
      {
        label: 'days',
        value: v[0],
      },
      {
        label: 'hours',
        value: v[1],
      },
      {
        label: 'minutes',
        value: v[2],
      },
      {
        label: 'seconds',
        value: v[3],
      },
    ];
  }, [competitionCountdown]);

  const searchParams = useSearchParams();
  const router = useRouter();
  const [selectedAgentId, setSelectedAgentId] = useState(
    searchParams.get('tab') || 'overall',
  );

  useEffect(() => {
    const url = new URL(window.location.href);
    url.searchParams.set('tab', selectedAgentId);
    router.replace(url.toString());
  }, [selectedAgentId]);

  if (isLoadingLeaderboards || isLoadingDetails || !details) {
    return (
      <div className={`p-4 ${className}`}>
        <Skeleton className="w-full h-[200px] mb-3" />
        <Skeleton className="w-full h-[600px] mb-3" />
      </div>
    );
  }

  if (sortedLeaderboards.length === 0) {
    return (
      <div className={className}>
        <div className="w-full flex items-center justify-center">
          <div>The leaderboard will be available soon.</div>
        </div>
      </div>
    );
  }

  const isUserInCompetitionOrg = dbOrg?.id === details.id;

  return (
    <AnimatePresence>
      <div className={`flex flex-col p-4 relative ${className}`}>
        <div className="absolute left-0 right-0 top-0 h-screen">
          <img
            src="/images/competition/comp-bg.png"
            className="h-full w-full"
          />
        </div>
        {['losl', 'dev-losl'].includes(competitionTag) && (
          <div className=" flex flex-wrap items-center text-black z-10 mb-3">
            <Link href="https://hyperbound.ai" target="_blank">
              <Image
                src="/images/black-logo-with-text.svg"
                width={102}
                height={16}
                alt="Hyperbound Logo"
              />
            </Link>
            <span className="mx-4">&amp;</span>
            <Link href="https://pclub.io" target="_blank">
              <Image
                src="/images/competition/league-of-sales-legends/pclub.svg"
                width={80}
                height={21}
                alt="PClub Logo"
              />
            </Link>
            <div className="flex-1" />
            <p className="text-muted-foreground">
              Days to play - January 14-16
            </p>
          </div>
        )}
        <div className="flex flex-row justify-between items-end mb-4 z-10">
          <div className="flex flex-col h-full">
            <p className="text-2xl font-bold">{details.name}</p>
            <p className="text-muted-foreground">
              Put your AI skills to the test in our lightning-fast bot-calling
              competition!
              <br />
              Speed, creativity, and precision will crown the ultimate &quot;AI
              Bot Champion&quot;.
            </p>
          </div>
          <div className="flex flex-row items-center">
            <Link href={'/'}>
              {isUserInCompetitionOrg && (
                <Button
                  variant={'default'}
                  className="text-white bg-gradient-to-b mr-2 from-[#3DC3E6] via-[#49C8CF] via-33% to-[#36C4BF] to-99% hover:scale-[0.98] hover:opacity-90 transition-all duration-200"
                >
                  Continue competition
                </Button>
              )}
            </Link>
            {countdownDisplayInfo.map((c) => {
              return (
                <div key={c.label} className="flex flex-col pl-6 items-center">
                  <p className="text-lg font-bold">{c.value}</p>
                  <p className="text-muted-foreground">{c.label}</p>
                </div>
              );
            })}
          </div>
        </div>
        <Tabs
          value={selectedAgentId}
          onValueChange={(v) => setSelectedAgentId(v)}
          className="w-full z-10"
        >
          <TabsList className="w-full bg-[#EEF7F5]">
            <TabsTrigger value="overall" className="flex-1">
              Overall
            </TabsTrigger>
            {sortedLeaderboards.map((l, idx) => {
              const icon = l.agent.iconUrl;
              const isSelected = l.agent.id.toString() === selectedAgentId;
              return (
                <TabsTrigger
                  key={idx}
                  value={l.agent.id.toString()}
                  className="flex-1"
                >
                  <div className="flex flex-row">
                    {icon && (
                      <img
                        src={`/images/competition/icons/${icon}`}
                        className="mr-2"
                        style={{
                          filter: isSelected ? '' : 'invert(.5)',
                        }}
                      />
                    )}
                    <p>
                      {l.agent.firstName} {l.agent.lastName}
                    </p>
                  </div>
                </TabsTrigger>
              );
            })}
          </TabsList>
          <TabsContent value="overall">
            <div className="mt-8">
              <Podium leaderboard={overallLeaderboard} />
            </div>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.4,
                delay: 1.2,
              }}
              className="mt-4 w-full relative"
            >
              <div className="absolute left-0 right-0 bottom-0 top-0">
                <OverallList
                  isUserInCompetitionOrg={isUserInCompetitionOrg}
                  competitionTag={competitionTag}
                  leaderboard={overallLeaderboard}
                  orgAgents={orgAgents}
                  goToAgentId={(agentId: number) =>
                    setSelectedAgentId(agentId.toString())
                  }
                />
              </div>
            </motion.div>
          </TabsContent>
          {sortedLeaderboards.map((l, idx) => (
            <TabsContent key={idx} value={l.agent.id.toString()}>
              <div className="mt-16">
                <Podium leaderboard={l} />
              </div>
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.4,
                  delay: 1.2,
                }}
                className="mt-4 w-full relative"
              >
                <div className="absolute left-0 right-0 bottom-0 top-0">
                  <List
                    leaderboard={l}
                    isAgentLoading={isOrgAgentsLoading}
                    agent={idToAgent[l.agent.id]}
                  />
                </div>
              </motion.div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </AnimatePresence>
  );
}
