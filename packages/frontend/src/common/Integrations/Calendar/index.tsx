import DashboardNavbar from '@/common/DashboardNavbar';
import { Button } from '@/components/ui/button';
import { useIntegrations } from '@/hooks/useIntegrations';
import { IntegrationServiceType } from '@/lib/Integrations/types';
import { IntegrationsCalendarRules } from './Rules';
import { useEffect, useState } from 'react';
import { BotSchedulingRule } from './types';

const IntegrationsCalendar = () => {
  const [rules, setRules] = useState<BotSchedulingRule[]>([]);
  const { data: integrations } = useIntegrations();
  const calendarIntegration = integrations?.find?.(
    (i) => i.type === IntegrationServiceType.CALENDAR,
  );
  useEffect(() => {
    const existingRules =
      calendarIntegration?.additionalInfos?.optOutBotSchedulingRules;
    if (existingRules) {
      setRules(existingRules);
    }
  }, [calendarIntegration]);
  if (!calendarIntegration) {
    return <div />;
  }
  return (
    <div className="h-full w-full flex flex-col">
      <DashboardNavbar
        breadcrumbs={[
          { title: 'Integrations', href: '/integrations' },
          { title: 'Calendar' },
        ]}
      />
      <div className="flex flex-row w-full flex-1">
        <div className="flex-1 px-4 py-4 items-stretch flex flex-col">
          <p className="text-lg">Rules for Hyperbound Meeting Recorder</p>
          <IntegrationsCalendarRules
            rules={rules}
            setRules={setRules}
            calendarIntegration={calendarIntegration}
            className="flex-1 w-full"
          />
        </div>
      </div>
    </div>
  );
};

export default IntegrationsCalendar;
