import { Integration } from '@/lib/Integrations/types';
import {
  BotSchedulingRule,
  BotSchedulingSubruleOperation,
  BotSchedulingSubruleTarget,
  operationToDisplayName,
  targetToDisplayName,
} from '../../types';
import { Button } from '@/components/ui/button';
import { Trash2Icon } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import MultiStringsInput from '@/components/ui/Hyperbound/multiStringsInput';
import { Fragment } from 'react';

export const IntegrationsCalendarModalRules = (props: {
  calendarIntegration: Integration;
  rules: BotSchedulingRule[];
  setRules: (rules: BotSchedulingRule[]) => unknown;
  className?: string;
}) => {
  return (
    <div className={`flex flex-col items-start ${props.className}`}>
      <p className="mt-4 text-sm font-medium ">
        Rules for Hyperbound Meeting Recorder
      </p>
      <p className="text-sm my-2">
        By default, the Hyperbound Meeting Recorder joins all of the calls
        scheduled in your Calendar. To opt out of some of these calls based on a
        set of rules, you can use the below input.
      </p>
      {props.rules.map((rule, idx) => {
        const isFinalRule = idx === props.rules.length - 1;
        return (
          <Fragment key={rule.id}>
            <div
              key={rule.id}
              className="w-full py-3 flex flex-col items-start border rounded-lg bg-white"
            >
              <div className="flex flex-row justify-between items-center w-full px-2">
                <p className="text-m">Opt Out Rule {idx + 1}</p>
                <div
                  className="cursor-pointer"
                  onClick={() => {
                    props.setRules(props.rules.filter((r) => r.id !== rule.id));
                  }}
                >
                  <Trash2Icon className="w-4 h-4 ml-3 text-red-600" />
                </div>
              </div>
              {rule.subrules.map((subrule, subruleIdx) => {
                const isFinalSubrule = subruleIdx === rule.subrules.length - 1;
                const isMeetingPlatformSubrule =
                  subrule.target ===
                  BotSchedulingSubruleTarget.MEETING_PLATFORM;
                return (
                  <div
                    className="flex flex-col w-full mt-4 px-2"
                    key={subrule.id}
                  >
                    <div className="flex flex-row space-x-2 w-full px-2">
                      <div className="flex-1">
                        <Select
                          value={subrule.target}
                          onValueChange={(v) => {
                            subrule.target = v as BotSchedulingSubruleTarget;
                            props.setRules([...props.rules]);
                          }}
                        >
                          <SelectTrigger className="flex-1 text-wrap">
                            {targetToDisplayName[subrule.target]}
                          </SelectTrigger>
                          <SelectContent>
                            {Object.values(BotSchedulingSubruleTarget).map(
                              (t, idx) => {
                                return (
                                  <SelectItem key={idx} value={t}>
                                    {targetToDisplayName[t]}
                                  </SelectItem>
                                );
                              },
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Select
                          value={subrule.operation}
                          onValueChange={(v) => {
                            subrule.operation =
                              v as BotSchedulingSubruleOperation;
                            props.setRules([...props.rules]);
                          }}
                        >
                          <SelectTrigger className="flex- text-wrap">
                            {operationToDisplayName[subrule.operation]}
                          </SelectTrigger>
                          <SelectContent>
                            {Object.values(BotSchedulingSubruleOperation).map(
                              (o, idx) => {
                                return (
                                  <SelectItem key={idx} value={o}>
                                    {operationToDisplayName[o]}
                                  </SelectItem>
                                );
                              },
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                      <div
                        className="cursor-pointer ml-2 self-center"
                        onClick={() => {
                          rule.subrules = rule.subrules.filter(
                            (s) => s.id !== subrule.id,
                          );
                          props.setRules([...props.rules]);
                        }}
                      >
                        <Trash2Icon className="w-4 h-4" />
                      </div>
                    </div>
                    <div className="mt-2 px-2">
                      <div className="">
                        <MultiStringsInput
                          placeholder="List of values (press enter to add)"
                          current={subrule.values}
                          onChange={(newValues) => {
                            subrule.values = newValues;
                            props.setRules([...props.rules]);
                          }}
                        />
                      </div>
                    </div>
                    {isMeetingPlatformSubrule && (
                      <div className="mt-2 px-2">
                        <p>
                          Possible values of meeting platform are:{' '}
                          <span className="font-medium">
                            zoom, google_meet, microsoft_teams,
                            microsoft_teams_live (personal teams)
                          </span>
                        </p>
                      </div>
                    )}
                    {!isFinalSubrule && (
                      <div className="w-full flex flex-row items-center space-x-2 pt-4 px-2">
                        <p className="font-bold">AND</p>
                        <hr className="flex-1" />
                      </div>
                    )}
                  </div>
                );
              })}
              <Button
                variant="secondary"
                className="mt-4 ml-4"
                onClick={() => {
                  rule.subrules.push({
                    id: Math.random().toString(),
                    target: BotSchedulingSubruleTarget.MEETING_NAME,
                    operation: BotSchedulingSubruleOperation.EQUALS,
                    values: [],
                  });
                  props.setRules([...props.rules]);
                }}
              >
                Add subrule
              </Button>
            </div>
            {!isFinalRule && (
              <div className="w-full flex flex-row items-center space-x-2 py-2">
                <p className="font-bold">OR</p>
                <hr className="flex-1" />
              </div>
            )}
          </Fragment>
        );
      })}
      <div className="flex flex-row space-x-2 mt-2">
        <Button
          disabled={!props.calendarIntegration}
          variant="outline"
          className="mt-2"
          onClick={() => {
            props.setRules([
              ...props.rules,
              {
                id: Math.random().toString(),
                subrules: [
                  {
                    id: Math.random().toString(),
                    target: BotSchedulingSubruleTarget.MEETING_NAME,
                    operation: BotSchedulingSubruleOperation.EQUALS,
                    values: [],
                  },
                ],
              },
            ]);
          }}
        >
          Add rule
        </Button>
      </div>
    </div>
  );
};
