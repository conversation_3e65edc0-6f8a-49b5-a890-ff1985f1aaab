import {
  meetingRecorderPlatformToDetails,
  SchedulingPreviewCalendarEvent,
} from '../../types';
import { useMemo } from 'react';
import dayjs from 'dayjs';
import { Switch } from '@/components/ui/switch';
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>Provider,
  <PERSON><PERSON>ip<PERSON>ontent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { shortenEmail } from '@/lib/utils';

export const IntegrationsCalendarModalCalendarPreview = ({
  calendarEvents,
  isLoading,
}: {
  calendarEvents: SchedulingPreviewCalendarEvent[];
  isLoading: boolean;
}) => {
  const emailInfoOfCorrespondingCalEvent = useMemo(() => {
    return calendarEvents.map((ce) => {
      const participantEmailsStr = [...ce.participantEmails]
        .map((e) => {
          const [username, domain] = e.split('@');
          return {
            username,
            domain,
          };
        })
        .sort((e1, e2) => {
          if (e1.domain !== e2.domain) {
            return e1.domain.length - e2.domain.length;
          }
          return e1.username.localeCompare(e2.username);
        })
        .map((e) => `${e.username}@${e.domain}`)
        .map(shortenEmail)
        .join('\n');
      return {
        participantEmailsStr,
        organiserEmail: shortenEmail(ce.organiserEmail),
      };
    });
  }, [calendarEvents]);

  const dateFormat = 'YYYY-MM-DD';
  const today = useMemo(() => {
    return dayjs().format(dateFormat);
  }, []);

  const formatTimeRange = (date: string) => {
    return dayjs(date).format('h:mm A');
  };

  const dateToCalendarEvents = useMemo(() => {
    const res: {
      [date: string]: {
        displayDate: string;
        events: (SchedulingPreviewCalendarEvent & {
          timeRange: string;
          ogIdx: number;
        })[];
      };
    } = {};
    calendarEvents.forEach((c, idx) => {
      const date = dayjs(c.startTime).format(dateFormat);
      if (!res[date]) {
        const displayDate =
          date === today ? 'Today' : dayjs(c.startTime).format('dddd, MMMM DD');
        res[date] = {
          displayDate,
          events: [],
        };
      }
      const timeRange = `${formatTimeRange(c.startTime)} - ${formatTimeRange(c.endTime)}`;
      res[date].events.push({ ...c, timeRange, ogIdx: idx });
    });
    return res;
  }, [today, calendarEvents]);

  const dateList = useMemo(() => {
    return Object.keys(dateToCalendarEvents).sort();
  }, [dateToCalendarEvents]);

  return (
    <div>
      {isLoading && 'Loading...'}
      {!isLoading && (
        <div>
          {dateList.map((date) => {
            const { displayDate, events } = dateToCalendarEvents[date];
            return (
              <div key={date} className="flex flex-col">
                <p className="font-medium mt-4">{displayDate}</p>
                <div className="mt-0">
                  {events.map((e) => {
                    const platformDetails =
                      meetingRecorderPlatformToDetails[e.platform];
                    return (
                      <div
                        className="rounded-lg border bg-white flex flex-col px-2 py-3 mt-3"
                        key={e.ogIdx}
                      >
                        <p className="font-medium">{e.title}</p>
                        <div className="flex flex-row space-x-2 mt-2">
                          <span>
                            {
                              emailInfoOfCorrespondingCalEvent[e.ogIdx]
                                .organiserEmail
                            }
                          </span>
                          <div className="w-0.5 my-1.5 bg-[#E4E4E7]" />
                          <span>{e.timeRange}</span>
                        </div>
                        <div className="flex flex-row mt-2 space-x-2 items-center">
                          {platformDetails.logo && (
                            <img
                              src={platformDetails.logo}
                              className="w-4 h-4"
                            />
                          )}
                          <p className="">{platformDetails?.title}</p>
                        </div>
                        <div className="flex flex-row space-x-2 items-center mt-4">
                          <TooltipProvider delayDuration={200}>
                            <Tooltip>
                              <TooltipTrigger>
                                <Switch
                                  className=" cursor-default data-[state=checked]:bg-[#3C82F6] data-[state=unchecked]:bg-input"
                                  checked={e.willBotAttendAccordingToRules}
                                  color="#3C82F6"
                                />
                              </TooltipTrigger>
                              <TooltipContent side="bottom">
                                Ability to configure bot join settings for
                                individual calls coming soon! Until then, please
                                use the rules to configure it.
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <p>
                            {e.willBotAttendAccordingToRules
                              ? 'Hyperbound Bot will join this meeting'
                              : 'Hyperbound Bot will not join this meeting'}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
