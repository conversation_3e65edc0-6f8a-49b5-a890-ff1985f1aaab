import {
  meetingRecorderPlatformToDetails,
  SchedulingPreviewCalendarEvent,
} from '../../types';
import { useMemo, useState } from 'react';
import dayjs from 'dayjs';
import {
  Tooltip,
  TooltipProvider,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { shortenEmail } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import RecallAiService from '@/lib/Integrations/RecallAi';

export const IntegrationsCalendarModalCalendarPreview = ({
  calendarEvents,
  refetchCalendarEvents,
  isLoading,
}: {
  calendarEvents: SchedulingPreviewCalendarEvent[];
  refetchCalendarEvents: () => Promise<void>;
  isLoading: boolean;
}) => {
  const [shouldRecordOverrideCache, setShouldRecordOverrideCache] = useState<{
    [key: number]: boolean | null;
  }>({});
  const emailInfoOfCorrespondingCalEvent = useMemo(() => {
    return calendarEvents.map((ce) => {
      const participantEmailsStr = [...ce.participantEmails]
        .map((e) => {
          const [username, domain] = e.split('@');
          return {
            username,
            domain,
          };
        })
        .sort((e1, e2) => {
          if (e1.domain !== e2.domain) {
            return e1.domain.length - e2.domain.length;
          }
          return e1.username.localeCompare(e2.username);
        })
        .map((e) => `${e.username}@${e.domain}`)
        .map(shortenEmail)
        .join('\n');
      return {
        participantEmailsStr,
        organiserEmail: shortenEmail(ce.organiserEmail),
      };
    });
  }, [calendarEvents]);

  const dateFormat = 'YYYY-MM-DD';
  const today = useMemo(() => {
    return dayjs().format(dateFormat);
  }, []);

  const formatTimeRange = (date: string) => {
    return dayjs(date).format('h:mm A');
  };

  const dateToCalendarEvents = useMemo(() => {
    const res: {
      [date: string]: {
        displayDate: string;
        events: (SchedulingPreviewCalendarEvent & {
          timeRange: string;
          ogIdx: number;
        })[];
      };
    } = {};
    calendarEvents.forEach((c, idx) => {
      const date = dayjs(c.startTime).format(dateFormat);
      if (!res[date]) {
        const displayDate =
          date === today ? 'Today' : dayjs(c.startTime).format('dddd, MMMM DD');
        res[date] = {
          displayDate,
          events: [],
        };
      }
      const timeRange = `${formatTimeRange(c.startTime)} - ${formatTimeRange(c.endTime)}`;
      res[date].events.push({ ...c, timeRange, ogIdx: idx });
    });
    return res;
  }, [today, calendarEvents]);

  const dateList = useMemo(() => {
    return Object.keys(dateToCalendarEvents).sort();
  }, [dateToCalendarEvents]);

  const handleShouldRecordOverrideChange = async (
    calendarEventId: number,
    shouldRecordOverride: boolean | null,
  ) => {
    setShouldRecordOverrideCache({
      ...shouldRecordOverrideCache,
      [calendarEventId]: shouldRecordOverride,
    });
    try {
      await RecallAiService.updateShouldRecordOverride(
        calendarEventId,
        shouldRecordOverride,
      );
      await refetchCalendarEvents();
      const newShouldRecordOverrideCache = { ...shouldRecordOverrideCache };
      delete newShouldRecordOverrideCache[calendarEventId];
      setShouldRecordOverrideCache(newShouldRecordOverrideCache);
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <div>
      {isLoading && 'Loading...'}
      {!isLoading && (
        <div>
          {dateList.map((date) => {
            const { displayDate, events } = dateToCalendarEvents[date];
            return (
              <div key={date} className="flex flex-col">
                <p className="font-medium mt-4">{displayDate}</p>
                <div className="mt-0">
                  {events.map((e) => {
                    const isInDb = !!e.calendarEventId;
                    const isInCache =
                      shouldRecordOverrideCache[e.calendarEventId || -1] !==
                      undefined;
                    const shouldRecordOverride =
                      shouldRecordOverrideCache[e.calendarEventId || -1] ??
                      e.shouldRecordOverride;
                    const willBotAttend =
                      typeof e.shouldRecordOverride === 'boolean'
                        ? e.shouldRecordOverride
                        : e.willBotAttendAccordingToRules;
                    const platformDetails =
                      meetingRecorderPlatformToDetails[e.platform];

                    const selectComponent = (
                      <Select
                        disabled={!isInDb || isInCache}
                        onValueChange={(v) => {
                          handleShouldRecordOverrideChange(
                            e.calendarEventId || -1,
                            v === 'null'
                              ? null
                              : v === 'recording'
                                ? true
                                : false,
                          );
                        }}
                        value={
                          shouldRecordOverride === null
                            ? 'null'
                            : shouldRecordOverride
                              ? 'recording'
                              : 'no-recording'
                        }
                      >
                        <SelectTrigger>
                          {shouldRecordOverride == null
                            ? 'Not overriden'
                            : shouldRecordOverride
                              ? 'Must be recorded'
                              : 'Must not be recorded'}
                          {isInCache && (
                            <span className="text-muted-foreground">
                              {' '}
                              (Saving)
                            </span>
                          )}
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="null">Not overriden</SelectItem>
                          <SelectItem value="recording">
                            Must be recorded
                          </SelectItem>
                          <SelectItem value="no-recording">
                            Must not be recorded
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    );

                    return (
                      <div
                        className="rounded-lg border bg-white flex flex-col px-2 py-3 mt-3 overflow-hidden relative"
                        key={e.ogIdx}
                      >
                        <div
                          className={`absolute top-0 left-0 right-0 h-[8px] ${
                            willBotAttend ? 'bg-[#3C82F6]' : 'bg-red-600'
                          }`}
                        ></div>
                        <p className="font-medium">{e.title}</p>
                        <div className="flex flex-row space-x-2 mt-2">
                          <span>
                            {
                              emailInfoOfCorrespondingCalEvent[e.ogIdx]
                                .organiserEmail
                            }
                          </span>
                          <div className="w-0.5 my-1.5 bg-[#E4E4E7]" />
                          <span>{e.timeRange}</span>
                        </div>
                        <div className="flex flex-row mt-2 space-x-2 items-center">
                          {platformDetails.logo && (
                            <img
                              src={platformDetails.logo}
                              className="w-4 h-4"
                            />
                          )}
                          <p className="">{platformDetails?.title}</p>
                        </div>
                        <div className="flex flex-col space-y-2 mt-4">
                          <p className="font-medium">
                            Override bot join settings for this meeting
                          </p>
                          {isInDb ? (
                            selectComponent
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger>
                                  {selectComponent}
                                </TooltipTrigger>
                                <TooltipContent side="bottom">
                                  <p>
                                    Cannot override bot join settings for this
                                    meeting.
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                          <p className="mt-2">
                            {willBotAttend
                              ? 'Hyperbound Bot will join this meeting'
                              : 'Hyperbound Bot will not join this meeting'}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
