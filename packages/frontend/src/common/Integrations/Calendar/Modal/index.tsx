import { useEffect, useMemo, useState } from 'react';
import { SchedulingPreviewCalendarEvent } from '../types';
import RecallAiService from '@/lib/Integrations/RecallAi';
import { useIntegrations } from '@/hooks/useIntegrations';
import {
  IntegrationAdditionalInfos,
  IntegrationServiceType,
} from '@/lib/Integrations/types';
import {
  Sheet,
  SheetContentLight,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TabsContent } from '@radix-ui/react-tabs';
import { IntegrationsCalendarModalRules } from './Rules';
import { IntegrationsCalendarModalCalendarPreview } from './CalendarPreviewModal';
import { Button } from '@/components/ui/button';

const IntegrationCalendarModal = ({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (o: boolean) => unknown;
}) => {
  const [calendarEvents, setCalendarEvents] = useState<
    SchedulingPreviewCalendarEvent[]
  >([]);
  const [isCalendarEventsLoading, setIsCalendarEventsLoading] = useState(false);

  const [additionalInfos, setAdditionalInfos] =
    useState<IntegrationAdditionalInfos>({});
  const { data: integrations } = useIntegrations();
  const calendarIntegration = integrations?.find?.(
    (i) => i.type === IntegrationServiceType.CALENDAR,
  );
  useEffect(() => {
    const existingAdditionalInfos = calendarIntegration?.additionalInfos;
    if (existingAdditionalInfos) {
      setAdditionalInfos(existingAdditionalInfos);
    }
  }, [calendarIntegration]);

  const refreshCalendarEvents = async () => {
    if (!calendarIntegration) {
      return;
    }
    setIsCalendarEventsLoading(true);
    const res = await RecallAiService.getCalendarSchedulingPreview(
      calendarIntegration.id,
      additionalInfos.optOutBotSchedulingRules,
    );
    if (res) {
      setCalendarEvents(
        res.sort((e1, e2) => {
          const startTimeDiff =
            new Date(e1.startTime).getTime() - new Date(e2.startTime).getTime();
          if (startTimeDiff !== 0) {
            return startTimeDiff;
          }
          return (
            new Date(e1.endTime).getTime() - new Date(e2.endTime).getTime()
          );
        }),
      );
    }
    setIsCalendarEventsLoading(false);
  };

  useEffect(() => {
    if (calendarIntegration) {
      refreshCalendarEvents();
    } else {
      setCalendarEvents([]);
    }
  }, [calendarIntegration, additionalInfos]);

  const [isSaving, setIsSaving] = useState(false);
  const saveErrMessage = useMemo(() => {
    if (
      additionalInfos.optOutBotSchedulingRules?.some((r) => !r.subrules.length)
    ) {
      return 'All your rules need to have at least 1 subrule';
    }
  }, [additionalInfos]);

  const [tab, setTab] = useState<'rules' | 'calendar'>('rules');

  if (!calendarIntegration) {
    return <div />;
  }

  const onSave = async () => {
    setIsSaving(true);
    await RecallAiService.setAdditionalInfos(
      calendarIntegration.id,
      additionalInfos,
    );
    setIsSaving(false);
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContentLight
        side={'rightFull'}
        className="p-0 w-full max-w-[800px] flex flex-col"
      >
        <SheetHeader className="py-2 px-3 border-b">
          <SheetTitle>Integration Settings</SheetTitle>
        </SheetHeader>
        <div className="flex-1 text-sm py-2 px-3 bg-[#FBFBFB]">
          <Tabs
            value={tab}
            onValueChange={(v) => setTab(v as 'rules' | 'calendar')}
            className="h-full flex flex-col"
          >
            <TabsList className="w-full mb-3">
              <TabsTrigger className="flex-1" value="rules">
                Rules
              </TabsTrigger>
              <TabsTrigger className="flex-1" value="calendar">
                Calendar
              </TabsTrigger>
            </TabsList>
            <TabsContent value="rules" className="flex-1">
              <div className="flex-1 relative h-full w-full">
                <div className="absolute overflow-y-auto left-0 right-0 top-0 bottom-0">
                  <IntegrationsCalendarModalRules
                    calendarIntegration={calendarIntegration}
                    additionalInfos={additionalInfos}
                    setAdditionalInfos={setAdditionalInfos}
                  />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="calendar" className="flex-1">
              <div className="flex-1 relative h-full w-full">
                <div className="absolute overflow-y-auto left-0 right-0 top-0 bottom-0">
                  <IntegrationsCalendarModalCalendarPreview
                    calendarEvents={calendarEvents}
                    refetchCalendarEvents={refreshCalendarEvents}
                    isLoading={isCalendarEventsLoading}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        <SheetFooter className="bg-white border-t flex flex-row !justify-start px-2 py-3">
          {tab === 'rules' && (
            <>
              <Button
                disabled={isSaving || !!saveErrMessage}
                className=""
                onClick={() => {
                  onSave();
                }}
              >
                Save
              </Button>
              <Button
                disabled={isSaving}
                className=""
                variant="outline"
                onClick={() => {
                  setOpen(false);
                }}
              >
                Cancel
              </Button>
            </>
          )}
        </SheetFooter>
      </SheetContentLight>
    </Sheet>
  );
};

export default IntegrationCalendarModal;
