import { Dialog } from '@/components/ui/dialog';
import { useEffect, useMemo, useState } from 'react';
import { BotSchedulingRule, SchedulingPreviewCalendarEvent } from '../types';
import RecallAiService from '@/lib/Integrations/RecallAi';
import { useIntegrations } from '@/hooks/useIntegrations';
import { IntegrationServiceType } from '@/lib/Integrations/types';
import {
  Sheet,
  SheetContentLight,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TabsContent } from '@radix-ui/react-tabs';
import { IntegrationsCalendarModalRules } from './Rules';
import { IntegrationsCalendarModalCalendarPreview } from './CalendarPreviewModal';
import { Button } from '@/components/ui/button';

const IntegrationCalendarModal = ({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: (o: boolean) => unknown;
}) => {
  const [calendarEvents, setCalendarEvents] = useState<
    SchedulingPreviewCalendarEvent[]
  >([]);
  const [isCalendarEventsLoading, setIsCalendarEventsLoading] = useState(false);

  const [rules, setRules] = useState<BotSchedulingRule[]>([]);
  const { data: integrations } = useIntegrations();
  const calendarIntegration = integrations?.find?.(
    (i) => i.type === IntegrationServiceType.CALENDAR,
  );
  useEffect(() => {
    const existingRules =
      calendarIntegration?.additionalInfos?.optOutBotSchedulingRules;
    if (existingRules) {
      setRules(existingRules);
    }
  }, [calendarIntegration]);

  const refreshCalendarEvents = async () => {
    if (!calendarIntegration) {
      return;
    }
    setIsCalendarEventsLoading(true);
    const res = await RecallAiService.getCalendarSchedulingPreview(
      calendarIntegration.id,
      rules,
    );
    if (res) {
      setCalendarEvents(
        res.sort((e1, e2) => {
          const startTimeDiff =
            new Date(e1.startTime).getTime() - new Date(e2.startTime).getTime();
          if (startTimeDiff !== 0) {
            return startTimeDiff;
          }
          return (
            new Date(e1.endTime).getTime() - new Date(e2.endTime).getTime()
          );
        }),
      );
    }
    setIsCalendarEventsLoading(false);
  };

  useEffect(() => {
    if (calendarIntegration) {
      refreshCalendarEvents();
    } else {
      setCalendarEvents([]);
    }
  }, [calendarIntegration, rules]);

  const [isSaving, setIsSaving] = useState(false);
  const saveErrMessage = useMemo(() => {
    if (rules.some((r) => !r.subrules.length)) {
      return 'All your rules need to have at least 1 subrule';
    }
  }, [rules]);

  if (!calendarIntegration) {
    return <div />;
  }

  const onSave = async () => {
    setIsSaving(true);
    await RecallAiService.setBotSchedulingRules(calendarIntegration.id, rules);
    setIsSaving(false);
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContentLight
        side={'rightFull'}
        className="p-0 w-full max-w-[800px] flex flex-col"
      >
        <SheetHeader className="py-2 px-3 border-b">
          <SheetTitle>Integration Settings</SheetTitle>
        </SheetHeader>
        <div className="flex-1 text-sm py-2 px-3 bg-[#FBFBFB]">
          <Tabs defaultValue="rules" className="h-full flex flex-col">
            <TabsList className="w-full mb-3">
              <TabsTrigger className="flex-1" value="rules">
                Rules
              </TabsTrigger>
              <TabsTrigger className="flex-1" value="calendar">
                Calendar
              </TabsTrigger>
            </TabsList>
            <TabsContent value="rules" className="flex-1">
              <div className="flex-1 relative h-full w-full">
                <div className="absolute overflow-y-auto left-0 right-0 top-0 bottom-0">
                  <IntegrationsCalendarModalRules
                    calendarIntegration={calendarIntegration}
                    rules={rules}
                    setRules={setRules}
                  />
                </div>
              </div>
            </TabsContent>
            <TabsContent value="calendar" className="flex-1">
              <div className="flex-1 relative h-full w-full">
                <div className="absolute overflow-y-auto left-0 right-0 top-0 bottom-0">
                  <IntegrationsCalendarModalCalendarPreview
                    calendarEvents={calendarEvents}
                    isLoading={isCalendarEventsLoading}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        <SheetFooter className="bg-white border-t flex flex-row !justify-start px-2 py-3">
          <Button
            disabled={isSaving || !!saveErrMessage}
            className=""
            onClick={() => {
              onSave();
            }}
          >
            Save
          </Button>
          <Button
            disabled={isSaving}
            className=""
            variant="outline"
            onClick={() => {
              setOpen(false);
            }}
          >
            Cancel
          </Button>
        </SheetFooter>
      </SheetContentLight>
    </Sheet>
  );
};

export default IntegrationCalendarModal;
