import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { BotSchedulingRule, SchedulingPreviewCalendarEvent } from '../../types';
import RecallAiService from '@/lib/Integrations/RecallAi';
import { Integration } from '@/lib/Integrations/types';
import { useEffect, useMemo, useState } from 'react';
import { Table, TableBody, TableCell, TableHead } from '@/components/ui/table';
import { TableRow } from '@/components/ui/Hyperbound/table';
import { Check, X } from 'lucide-react';
import dayjs from 'dayjs';
import { shortenEmail } from '@/lib/utils';

export const CalendarPreviewModal = ({
  modalOpen,
  setModalOpen,
  calendarIntegration,
  rules,
}: {
  modalOpen: boolean;
  setModalOpen: (v: boolean) => unknown;
  calendarIntegration: Integration;
  rules: BotSchedulingRule[];
}) => {
  const [calendarEvents, setCalendarEvents] = useState<
    SchedulingPreviewCalendarEvent[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);

  const refreshCalendarEvents = async () => {
    setIsLoading(true);
    const res = await RecallAiService.getCalendarSchedulingPreview(
      calendarIntegration.id,
      rules,
    );
    if (res) {
      setCalendarEvents(
        res.sort((e1, e2) => {
          const startTimeDiff =
            new Date(e1.startTime).getTime() - new Date(e2.startTime).getTime();
          if (startTimeDiff !== 0) {
            return startTimeDiff;
          }
          return (
            new Date(e1.endTime).getTime() - new Date(e2.endTime).getTime()
          );
        }),
      );
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (modalOpen && calendarIntegration) {
      refreshCalendarEvents();
    } else {
      setCalendarEvents([]);
    }
  }, [modalOpen, calendarIntegration, rules]);

  const emailInfoOfCorrespondingCalEvent = useMemo(() => {
    return calendarEvents.map((ce) => {
      const participantEmailsStr = [...ce.participantEmails]
        .map((e) => {
          const [username, domain] = e.split('@');
          return {
            username,
            domain,
          };
        })
        .sort((e1, e2) => {
          if (e1.domain !== e2.domain) {
            return e1.domain.length - e2.domain.length;
          }
          return e1.username.localeCompare(e2.username);
        })
        .map((e) => `${e.username}@${e.domain}`)
        .map(shortenEmail)
        .join('\n');
      return {
        participantEmailsStr,
        organiserEmail: shortenEmail(ce.organiserEmail),
      };
    });
  }, [calendarEvents]);

  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn max-w-[1000px] max-h-[80vh] mt-16 flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Calendar Events Preview
          </DialogTitle>
        </DialogHeader>
        {isLoading && 'Loading...'}
        {!isLoading && (
          <Table className="flex-1">
            <TableBody className="h-full">
              <TableRow className="bg-white">
                <TableHead className="sticky top-0 bg-white">
                  Will Bot Attend the meet?
                </TableHead>
                <TableHead className="sticky top-0 bg-white">Title</TableHead>
                <TableHead className="sticky top-0 bg-white">
                  Start Time ({dayjs().format('Z')})
                </TableHead>
                <TableHead className="sticky top-0 bg-white">
                  End Time ({dayjs().format('Z')})
                </TableHead>
                <TableHead className="sticky top-0 bg-white">
                  Event Type
                </TableHead>
                <TableHead className="sticky top-0 bg-white">
                  Participant Emails
                </TableHead>
                <TableHead className="sticky top-0 bg-white">
                  Organiser Email
                </TableHead>
              </TableRow>
              {calendarEvents.map((ce, idx) => {
                return (
                  <TableRow key={idx}>
                    <TableCell>
                      <div className="flex flex-row justify-center w-full h-full items-center">
                        {ce.willBotAttendAccordingToRules && (
                          <div className="mr-1 bg-green-600 self-start h-[16px] w-[16px] rounded-[2px]">
                            <Check className="text-white" size={16} />
                          </div>
                        )}
                        {!ce.willBotAttendAccordingToRules && (
                          <div className="mr-1 bg-red-600 self-start h-[12px] w-[12px] rounded-[2px]">
                            <X className="text-white" size={12} />
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{ce.title}</TableCell>
                    <TableCell>
                      {dayjs(ce.startTime).format('MMM D, h:mm A')}
                    </TableCell>
                    <TableCell>
                      {dayjs(ce.endTime).format('MMM D, h:mm A')}
                    </TableCell>
                    <TableCell>{ce.eventType}</TableCell>
                    <TableCell className="whitespace-pre-line">
                      {
                        emailInfoOfCorrespondingCalEvent[idx]
                          .participantEmailsStr
                      }
                    </TableCell>
                    <TableCell>
                      {emailInfoOfCorrespondingCalEvent[idx].organiserEmail}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        )}
      </DialogContent>
    </Dialog>
  );
};
