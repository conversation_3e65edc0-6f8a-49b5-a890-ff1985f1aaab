import { Integration } from '@/lib/Integrations/types';
import {
  BotSchedulingRule,
  BotSchedulingSubruleOperation,
  BotSchedulingSubruleTarget,
  operationToDisplayName,
  targetToDisplayName,
} from '../types';
import { useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { CalendarIcon, Trash2Icon } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import MultiStringsInput from '@/components/ui/Hyperbound/multiStringsInput';
import { CalendarPreviewModal } from './CalendarPreviewModal';
import RecallAiService from '@/lib/Integrations/RecallAi';

export const IntegrationsCalendarRules = (props: {
  calendarIntegration: Integration;
  rules: BotSchedulingRule[];
  setRules: (rules: BotSchedulingRule[]) => unknown;
  className?: string;
}) => {
  const [calendarPreviewModalOpen, setCalendarPreviewModalOpen] =
    useState(false);

  const [isSaving, setIsSaving] = useState(false);
  const saveErrMessage = useMemo(() => {
    if (props.rules.some((r) => !r.subrules.length)) {
      return 'All your rules need to have at least 1 subrule';
    }
  }, [props.rules]);

  const onSave = async () => {
    setIsSaving(true);
    await RecallAiService.setBotSchedulingRules(
      props.calendarIntegration.id,
      props.rules,
    );
    setIsSaving(false);
  };

  return (
    <div className={`flex flex-col items-start ${props.className}`}>
      <p className="text-sm mt-2">
        By default, the Hyperbound Meeting Recorder joins all of the calls
        scheduled in your Calendar. To opt out of some of these calls based on a
        set of rules, you can use the below input.
      </p>
      {props.rules.map((rule, idx) => {
        const isFinalRule = idx === props.rules.length - 1;
        return (
          <div key={rule.id} className="w-full pt-3 flex flex-col items-start">
            <div className="flex flex-row justify-start items-center w-full">
              <p className="text-base">Opt Out Rule {idx + 1}</p>
              <div
                className="cursor-pointer"
                onClick={() => {
                  props.setRules(props.rules.filter((r) => r.id !== rule.id));
                }}
              >
                <Trash2Icon className="w-4 h-4 ml-3" />
              </div>
            </div>
            {rule.subrules.map((subrule, subruleIdx) => {
              const isFinalSubrule = subruleIdx === rule.subrules.length - 1;
              return (
                <div
                  className="flex flex-col w-full mt-2 pl-4"
                  key={subrule.id}
                >
                  <div className="flex flex-row space-x-2 w-full">
                    <div>
                      <Select
                        value={subrule.target}
                        onValueChange={(v) => {
                          subrule.target = v as BotSchedulingSubruleTarget;
                          props.setRules([...props.rules]);
                        }}
                      >
                        <SelectTrigger className="flex- text-wrap">
                          {targetToDisplayName[subrule.target]}
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(BotSchedulingSubruleTarget).map(
                            (t, idx) => {
                              return (
                                <SelectItem key={idx} value={t}>
                                  {targetToDisplayName[t]}
                                </SelectItem>
                              );
                            },
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Select
                        value={subrule.operation}
                        onValueChange={(v) => {
                          subrule.operation =
                            v as BotSchedulingSubruleOperation;
                          props.setRules([...props.rules]);
                        }}
                      >
                        <SelectTrigger className="flex- text-wrap">
                          {operationToDisplayName[subrule.operation]}
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(BotSchedulingSubruleOperation).map(
                            (o, idx) => {
                              return (
                                <SelectItem key={idx} value={o}>
                                  {operationToDisplayName[o]}
                                </SelectItem>
                              );
                            },
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex-1">
                      <MultiStringsInput
                        placeholder="List of values (press enter to add)"
                        current={subrule.values}
                        onChange={(newValues) => {
                          subrule.values = newValues;
                          props.setRules([...props.rules]);
                        }}
                      />
                    </div>
                    <div
                      className="cursor-pointer ml-2 self-center"
                      onClick={() => {
                        rule.subrules = rule.subrules.filter(
                          (s) => s.id !== subrule.id,
                        );
                        props.setRules([...props.rules]);
                      }}
                    >
                      <Trash2Icon className="w-4 h-4" />
                    </div>
                  </div>
                  {!isFinalSubrule && (
                    <div className="w-full flex flex-row items-center space-x-2 pt-3">
                      <p className="font-bold">AND</p>
                      <hr className="flex-1" />
                    </div>
                  )}
                </div>
              );
            })}
            <Button
              variant="secondary"
              className="mt-4 ml-4"
              onClick={() => {
                rule.subrules.push({
                  id: Math.random().toString(),
                  target: BotSchedulingSubruleTarget.MEETING_NAME,
                  operation: BotSchedulingSubruleOperation.EQUALS,
                  values: [],
                });
                props.setRules([...props.rules]);
              }}
            >
              Add subrule
            </Button>
            {!isFinalRule && (
              <div className="w-full flex flex-row items-center space-x-2 pt-3">
                <p className="font-bold">OR</p>
                <hr className="flex-1" />
              </div>
            )}
          </div>
        );
      })}
      <div className="flex flex-row space-x-2 mt-2">
        <Button
          disabled={!props.calendarIntegration}
          variant="outline"
          className="mt-2"
          onClick={() => {
            props.setRules([
              ...props.rules,
              {
                id: Math.random().toString(),
                subrules: [
                  {
                    id: Math.random().toString(),
                    target: BotSchedulingSubruleTarget.MEETING_NAME,
                    operation: BotSchedulingSubruleOperation.EQUALS,
                    values: [],
                  },
                ],
              },
            ]);
          }}
        >
          Add rule
        </Button>
        <Button
          disabled={!props.calendarIntegration}
          variant="outline"
          className="mt-2"
          onClick={() => {
            setCalendarPreviewModalOpen(true);
          }}
        >
          View Calendar Preview&nbsp;
          <CalendarIcon className="w-4 h-4" />
        </Button>
      </div>
      <Button
        disabled={isSaving || !!saveErrMessage}
        className="mt-2"
        onClick={() => {
          onSave();
        }}
      >
        Save
      </Button>
      {!!saveErrMessage && <p className="text-red-600">{saveErrMessage}</p>}
      <CalendarPreviewModal
        calendarIntegration={props.calendarIntegration}
        rules={props.rules}
        modalOpen={calendarPreviewModalOpen}
        setModalOpen={setCalendarPreviewModalOpen}
      />
    </div>
  );
};
