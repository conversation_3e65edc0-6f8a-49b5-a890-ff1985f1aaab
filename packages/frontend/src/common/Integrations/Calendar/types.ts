export type BotSchedulingRule = {
  id?: string;
  subrules: BotSchedulingSubrule[];
};

export enum BotSchedulingSubruleTarget {
  MEETING_NAME = 'MEETING_NAME',
  MEETING_PLATFORM = 'MEETING_PLATFORM',
  START_DAY_OF_WEEK = 'START_DAY_OF_WEEK',
  START_DAY_OF_MONTH = 'START_DAY_OF_MONTH',
  END_DAY_OF_WEEK = 'END_DAY_OF_WEEK',
  END_DAY_OF_MONTH = 'END_DAY_OF_MONTH',
  PARTICIPANTS_EMAIL = 'PARTICIPANTS_EMAIL',
  PARTICIPANTS_EMAIL_DOMAIN = 'PARTICIPANTS_EMAIL_DOMAIN',
  ALL_PARTICIPANTS_EMAIL_DOMAIN = 'ALL_PARTICIPANTS_EMAIL_DOMAIN',
  ORGANISER_EMAIL = 'ORGANISER_EMAIL',
  ORGANISER_EMAIL_DOMAIN = 'ORGANISER_EMAIL_DOMAIN',
  EVENT_TYPE = 'EVENT_TYPE',
}

export const targetToDisplayName = {
  [BotSchedulingSubruleTarget.MEETING_NAME]: 'Meeting Name',
  [BotSchedulingSubruleTarget.MEETING_PLATFORM]: 'Meeting Platform',
  [BotSchedulingSubruleTarget.START_DAY_OF_WEEK]: "Start Time's Day of Week",
  [BotSchedulingSubruleTarget.START_DAY_OF_MONTH]: "Start Time's Day of Month",
  [BotSchedulingSubruleTarget.END_DAY_OF_WEEK]: "End Time's Day of Week",
  [BotSchedulingSubruleTarget.END_DAY_OF_MONTH]: "End Time's Day of Month",
  [BotSchedulingSubruleTarget.PARTICIPANTS_EMAIL]: 'Participants Email',
  [BotSchedulingSubruleTarget.PARTICIPANTS_EMAIL_DOMAIN]:
    'Participants Email Domain',
  [BotSchedulingSubruleTarget.ALL_PARTICIPANTS_EMAIL_DOMAIN]:
    'All Participants Email Domain',
  [BotSchedulingSubruleTarget.ORGANISER_EMAIL]: 'Organiser Email',
  [BotSchedulingSubruleTarget.ORGANISER_EMAIL_DOMAIN]: 'Organiser Email Domain',
  [BotSchedulingSubruleTarget.EVENT_TYPE]: 'Event Type',
};

export enum BotSchedulingSubruleOperation {
  EQUALS = 'EQUALS',
  EQUALS_I = 'EQUALS_I',
  NOT_EQUALS = 'NOT_EQUALS',
  NOT_EQUALS_I = 'NOT_EQUALS_I',
  CONTAINS = 'CONTAINS',
  CONTAINS_I = 'CONTAINS_I',
  NOT_CONTAINS = 'NOT_CONTAINS',
  NOT_CONTAINS_I = 'NOT_CONTAINS_I',
}

export const operationToDisplayName = {
  [BotSchedulingSubruleOperation.EQUALS]: 'Equals',
  [BotSchedulingSubruleOperation.EQUALS_I]: 'Equals (case-insensitive)',
  [BotSchedulingSubruleOperation.NOT_EQUALS]: 'Not Equals',
  [BotSchedulingSubruleOperation.NOT_EQUALS_I]: 'Not Equals (case-insensitive)',
  [BotSchedulingSubruleOperation.CONTAINS]: 'Contains',
  [BotSchedulingSubruleOperation.CONTAINS_I]: 'Contains (case-insensitive)',
  [BotSchedulingSubruleOperation.NOT_CONTAINS]: 'Not Contains',
  [BotSchedulingSubruleOperation.NOT_CONTAINS_I]:
    'Not Contains (case-insensitive)',
};

export type BotSchedulingSubrule = {
  id?: string;
  target: BotSchedulingSubruleTarget;
  operation: BotSchedulingSubruleOperation;
  values: string[];
};

export enum MeetingRecorderPlatform {
  Zoom = 'Zoom',
  Google = 'Google',
  Microsoft = 'Microsoft',
  Unknown = 'Unknown',
}

export const meetingRecorderPlatformToDetails = {
  [MeetingRecorderPlatform.Zoom]: {
    title: 'Zoom',
    logo: '/images/integrations/zoom-logo.svg',
  },
  [MeetingRecorderPlatform.Google]: {
    title: 'Google Meet',
    logo: '/images/integrations/google-meet-logo.svg',
  },
  [MeetingRecorderPlatform.Microsoft]: {
    title: 'Microsoft Teams',
    logo: '/images/integrations/microsoft-teams-meet-logo.svg',
  },
  [MeetingRecorderPlatform.Unknown]: { title: 'Unknown Platform', logo: '' },
};

export type SchedulingPreviewCalendarEvent = {
  title: string;
  platform: MeetingRecorderPlatform;
  startTime: string;
  endTime: string;
  participantEmails: string[];
  organiserEmail: string;
  isOrganiserSelf: boolean;
  eventType: string;
  willBotAttendAccordingToRules: boolean;
  calendarEventId: number | null;
  shouldRecordOverride: boolean | null;
};
