import DashboardNavbar from '@/common/DashboardNavbar';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useIntegrations } from '@/hooks/useIntegrations';
import useOrg from '@/hooks/useOrg';
import useUserSession from '@/hooks/useUserSession';
import IntegrationService, { RealCallsService } from '@/lib/Integrations';
import { RealCallAutoEmailTarget } from '@/lib/Integrations/RealCalls/types';
import {
  Integration,
  IntegrationAuthType,
  IntegrationServiceType,
  IntegrationStatus,
} from '@/lib/Integrations/types';
import { useAuthInfo } from '@propelauth/react';
import dayjs from 'dayjs';
import {
  MailIcon,
  Pause,
  Pencil,
  Play,
  PlusCircle,
  Share2Icon,
  XIcon,
} from 'lucide-react';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { RealCallAutoEmailTargetSelector } from '../RealCallAutoEmailTargetSelector';
import IntegrationCalendarModal from './Calendar/Modal';
import NewIntegrationModal from './newIntegrationModal';
import RealCallSettings from './RealCallSettings';
import { AppPermissions } from '@/lib/permissions';
import IntegrationModalV2 from './IntegrationModalV2';
import IntegrationSharingPanel from './IntegrationSharingPanel';

/* @deprecated */
export const zoomLiveRecorderProviderName = 'Zoom (Live Recorder)';

export const INTEGRATIONS_WITH_SHARING = ['zoom', 'gong'];

export const hyperboundMeetingRecorderProviderName =
  'Hyperbound Meeting Recorder';
export default function Integrations() {
  const { canAccessIntegrations, useIntegrationsV2, canAccess, isAdmin } =
    useUserSession();
  const [openNewModal, setOpenNewModal] = useState<boolean>(false);
  const { data: org } = useOrg();
  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());
  const authInfo = useAuthInfo();
  const [editingIntegration, setEditingIntegration] = useState<
    Integration | undefined
  >();
  const [openSharingPanel, setOpenSharingPanel] = useState<boolean>(false);
  /*****************************************/
  /************* INIT **********************/
  /*****************************************/

  const {
    data: dbIntegrations,
    isLoading: isLoadingIntegrations,
    refetch: refetchIntegrations,
  } = useIntegrations(canAccessIntegrations);
  const [allIntegrations, setAllIntegrations] = useState<Integration[]>([]);
  const hyperboundMeetingRecorderConfig = useMemo(() => {
    const calendarIntegration = allIntegrations.find(
      (i) => i.type === IntegrationServiceType.CALENDAR,
    );
    const platformIntegrations = allIntegrations.filter(
      (i) => i.type === IntegrationServiceType.REAL_CALL_SCORING,
    );

    if (calendarIntegration) {
      return {
        isEnabled: true,
        calendarIntegration,
        platformIntegrations,
      } as const;
    }
    return {
      isEnabled: false,
    } as const;
  }, [allIntegrations]);
  const adjustedIntegrations = useMemo(() => {
    const filteredIntegrations = allIntegrations.filter(
      (i) =>
        i.status !== IntegrationStatus.INACTIVE &&
        i.type !== IntegrationServiceType.CALENDAR,
    );
    const isCalendarActive =
      hyperboundMeetingRecorderConfig.calendarIntegration?.status ===
      IntegrationStatus.ACTIVE;
    const isAnyPlatformIntegrationActive =
      hyperboundMeetingRecorderConfig.platformIntegrations?.some?.(
        (i) => i.status === IntegrationStatus.ACTIVE,
      );
    if (hyperboundMeetingRecorderConfig.isEnabled) {
      filteredIntegrations.push({
        id: -1,
        name: `Meeting Recorder`,
        authType: IntegrationAuthType.NONE,
        status:
          isCalendarActive && isAnyPlatformIntegrationActive
            ? IntegrationStatus.ACTIVE
            : IntegrationStatus.PAUSED,
        provider: {
          id: -1,
          logoUrl: '/images/square-logo-transparent.svg',
          needsUserTeamSettings: false,
          services: [IntegrationServiceType.REAL_CALL_SCORING],
          companyName: hyperboundMeetingRecorderProviderName,
        },
      });
    }
    return filteredIntegrations;
  }, [allIntegrations, hyperboundMeetingRecorderConfig]);

  useEffect(() => {
    if (!isLoadingIntegrations && dbIntegrations) {
      setAllIntegrations(dbIntegrations);
    }
  }, [dbIntegrations, isLoadingIntegrations]);

  /*****************************************/
  /**************** ACTIONS ****************/
  /*****************************************/

  const closeNewIntPnl = () => {
    setEditingIntegration(undefined);
    setOpenNewModal(false);
  };

  const startEditing = (i: Integration) => {
    setEditingIntegration(i);
    setOpenNewModal(true);
  };

  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams.get('state') && searchParams.get('code')) {
      setOpenNewModal(true);
    }
  }, [searchParams.toString()]);

  const toggleStatus = async (i: Integration, newStatus: IntegrationStatus) => {
    try {
      await IntegrationService.updateIntegrationStatus(i.id, newStatus);
    } catch (e) {
      console.log(e);
    }

    refetchIntegrations();
  };

  const [zoomLiveRecorderModalOpen, setZoomLiveRecorderModalOpen] =
    useState(false);

  const onChangeAutoEmailTarget = async (
    target: RealCallAutoEmailTarget | '-',
  ) => {
    if (target === '-') {
      await RealCallsService.deleteRealCallOrgConfig();
      return;
    }
    await RealCallsService.upsertRealCallOrgConfig(target);
  };

  const [editRealCallSettings, setEditRealCallSettings] = useState(false);

  /*****************************************/
  /**************** RENDER *****************/
  /*****************************************/

  return (
    <div>
      <DashboardNavbar
        breadcrumbs={[{ title: 'Integrations' }]}
        rightContent={
          <>
            {isAdmin && editRealCallSettings && (
              <>
                <Button
                  onClick={() => {
                    setEditRealCallSettings(false);
                  }}
                  disabled={isPilotEnded}
                  className="mr-2"
                  variant={'outline'}
                >
                  Close
                </Button>
              </>
            )}
            {!editRealCallSettings && (
              <>
                {isAdmin && (
                  <Button
                    onClick={() => {
                      setEditRealCallSettings(true);
                    }}
                    disabled={isPilotEnded}
                    className="mr-2"
                    variant={'outline'}
                  >
                    <Pencil className="w-4 h-4 mr-2" />
                    Edit Settings
                  </Button>
                )}
                <Button
                  onClick={() => {
                    setOpenNewModal(true);
                  }}
                  disabled={isPilotEnded}
                >
                  <PlusCircle className="w-4 h-4 mr-2" />
                  New Integration
                </Button>
              </>
            )}
          </>
        }
      />

      <div className="mx-2">
        <div className="px-3 mt-4">
          {canAccess(AppPermissions.INTEGRATIONS) &&
            isAdmin &&
            !editRealCallSettings && (
              <RealCallAutoEmailTargetSelector
                defaultValue={org?.realCallConfig?.autoEmailTarget || '-'}
                onSelect={onChangeAutoEmailTarget}
                isLoading={!org}
              />
            )}
        </div>

        {isAdmin && adjustedIntegrations.length > 0 && (
          <div className="px-3 w-full mt-8">
            <RealCallSettings
              edit={editRealCallSettings}
              startEditing={() => setEditRealCallSettings(true)}
            />
          </div>
        )}

        {/*********************************/}
        {/******** INTEGRATIONS ***********/}
        {/*********************************/}

        {adjustedIntegrations.length > 0 && !editRealCallSettings && (
          <div className="px-2 w-full mt-16">
            <div className="font-semibold ml-1 mb-2 text-base">
              All integrations
            </div>
            <Table className="w-full">
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Service Provider</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>&nbsp;</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {adjustedIntegrations.map((i: Integration) => {
                  const provider = i.provider;
                  if (provider) {
                    let status = 'Paused';
                    if (i.status == IntegrationStatus.ACTIVE) {
                      status = 'Active';
                    }
                    return (
                      <TableRow
                        key={'int-' + i.id}
                        className="cursor-pointer"
                        tabIndex={0}
                        role="button"
                        onClick={() => {
                          if (
                            i.provider?.companyName ===
                            hyperboundMeetingRecorderProviderName
                          ) {
                            setZoomLiveRecorderModalOpen(true);
                          } else {
                            startEditing(i);
                          }
                        }}
                      >
                        <TableCell>{i.name}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Image
                              src={IntegrationService.getProviderLogoUrl(
                                provider.logoUrl,
                              )}
                              alt={`${provider.companyName} Logo`}
                              width={20}
                              height={20}
                              className="mr-1"
                            />
                            <div>{provider.companyName}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {i.type == 'REAL_CALL_SCORING' &&
                            'Real calls scoring'}
                          {i.type === 'CALENDAR' && 'Calendar'}
                        </TableCell>
                        <TableCell>{status}</TableCell>
                        <TableHead>
                          {isAdmin && i.id > 0 && (
                            <div className="flex flex-row items-center justify-end">
                              <TooltipProvider delayDuration={50}>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      size={'sm'}
                                      variant={'ghost'}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        toggleStatus(
                                          i,
                                          i.status === IntegrationStatus.ACTIVE
                                            ? IntegrationStatus.PAUSED
                                            : IntegrationStatus.ACTIVE,
                                        );
                                      }}
                                    >
                                      {i.status ===
                                        IntegrationStatus.ACTIVE && (
                                        <Pause size={16} />
                                      )}
                                      {i.status ===
                                        IntegrationStatus.PAUSED && (
                                        <Play size={16} />
                                      )}
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent side="bottom">
                                    {i.status === IntegrationStatus.ACTIVE &&
                                      'Pause integration'}
                                    {i.status === IntegrationStatus.PAUSED &&
                                      'Resume integration'}
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>

                              {i.status !== IntegrationStatus.INACTIVE && (
                                <TooltipProvider delayDuration={50}>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        size={'sm'}
                                        variant={'ghost'}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          toggleStatus(
                                            i,
                                            IntegrationStatus.INACTIVE,
                                          );
                                        }}
                                      >
                                        <XIcon size={16} />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent side="bottom">
                                      Remove integration
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                              {!i.originalIntegrationId &&
                                INTEGRATIONS_WITH_SHARING.includes(
                                  provider?.companyName?.toLowerCase(),
                                ) && (
                                  <TooltipProvider delayDuration={50}>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Button
                                          size={'sm'}
                                          variant={'ghost'}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            setEditingIntegration(i);
                                            setOpenSharingPanel(true);
                                          }}
                                        >
                                          <Share2Icon size={16} />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent side="bottom">
                                        Share integration
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                )}
                            </div>
                          )}
                        </TableHead>
                      </TableRow>
                    );
                  }
                })}
              </TableBody>
            </Table>
          </div>
        )}

        {/*********************************/}
        {/******** EMPTY PAGE ***********/}
        {/*********************************/}
        <div className="flex flex-col w-full mt-[6%] h-full items-center">
          <div className="w-full flex items-center justify-center">
            <div>
              {adjustedIntegrations.length == 0 && (
                <Image
                  alt="Integrations Overview"
                  src="/images/integrations.jpg"
                  width={600}
                  height={450}
                />
              )}

              {!canAccessIntegrations && (
                <Button
                  size={'lg'}
                  variant={'default'}
                  className={
                    'mt-10 w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200'
                  }
                  style={{
                    backgroundImage:
                      'linear-gradient(to right, #000000, #5189CE, #A168A2)',
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    window.location.href = 'mailto:<EMAIL>';
                  }}
                >
                  <MailIcon className="mr-2 h-4 w-4" />
                  Contact the Hyperbound team to{' '}
                  {authInfo?.isLoggedIn ? 'get started' : 'learn more'}
                </Button>
              )}
            </div>
          </div>
        </div>
        {useIntegrationsV2 ? (
          <IntegrationModalV2
            key={'ni-' + (editingIntegration?.id || '0')}
            open={openNewModal}
            onClose={closeNewIntPnl}
            onSave={(shouldRefresh) => {
              refetchIntegrations();
              if (shouldRefresh) {
                window.location.replace('/integrations');
              }
            }}
            integration={editingIntegration}
          />
        ) : openNewModal ? (
          <NewIntegrationModal
            key={'ni-' + (editingIntegration?.id || '0')}
            open={openNewModal}
            onClose={closeNewIntPnl}
            onSave={(shouldRefresh) => {
              refetchIntegrations();
              if (shouldRefresh) {
                window.location.replace('/integrations');
              }
            }}
            integration={editingIntegration}
          />
        ) : null}

        <IntegrationCalendarModal
          open={zoomLiveRecorderModalOpen}
          setOpen={setZoomLiveRecorderModalOpen}
        />
        <IntegrationSharingPanel
          integration={editingIntegration || ({} as Integration)}
          open={openSharingPanel}
          onOpenChange={() => setOpenSharingPanel(!openSharingPanel)}
        />
      </div>
    </div>
  );
}
