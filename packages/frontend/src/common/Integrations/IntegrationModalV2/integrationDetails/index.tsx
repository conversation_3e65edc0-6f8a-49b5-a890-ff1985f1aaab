import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import useUserSession from '@/hooks/useUserSession';
import { ServiceProvider } from '@/lib/Integrations/types';
import Link from 'next/link';
import { useContext, useMemo, useState } from 'react';
import { IntegrationModalV2Context } from '../context';

interface IProps {
  onUpdate: (ak?: string, sk?: string) => void;
  provider?: ServiceProvider;
  isV2Integration?: boolean;
}

export default function IntegrationDetails({
  onUpdate,
  provider,
  isV2Integration,
}: IProps) {
  const {
    isSaving,
  } = useContext(IntegrationModalV2Context);
  const [authKey, setAuthKey] = useState<string>();
  const [secretKey, setSecretKey] = useState<string>();
  const { dbOrg } = useUserSession();
  const isOrum = provider?.companyName?.toLowerCase() === 'orum';

  const update = (s: string, v: string) => {
    if (s == 'authKey') {
      setAuthKey(v);
      onUpdate(v, secretKey);
    } else if (s == 'secretKey') {
      setSecretKey(v);
      onUpdate(authKey, v);
    }
  };

  const authType = useMemo(() => {
    if (isOrum) {
      return 'none';
    }

    return [
      'zoom',
      'google',
      'microsoft',
      'hubspot',
      'salesloft',
      'aircall',
      'chorus',
      'zoomphone',
    ].includes(provider?.companyName?.toLowerCase?.() || '')
      ? 'oauth'
      : 'basic';
  }, [provider, isOrum]);

  return (
    <div>
      {authType === 'basic' && (
        <>
          <div className="mt-2">
            <Label>Auth Key</Label>
            <div className="mt-2 flex items-center">
              <Input
                disabled={isSaving}
                value={authKey}
                onChange={(e: any) => {
                  update('authKey', e.target.value);
                }}
              />
            </div>
          </div>
          <div className="mt-4">
            <Label>Secret Key</Label>
            <div className="mt-2 flex items-center">
              <Input
                disabled={isSaving}
                value={secretKey}
                onChange={(e: any) => {
                  update('secretKey', e.target.value);
                }}
              />
            </div>
          </div>
        </>
      )}
      {isOrum && (
        <>
          <div className="mt-4">
            <Label>Paste this URL into your Orum Webhook&apos;s URL:</Label>
            <div className="mt-2 flex items-center font-bold">
              {`${process.env.NEXT_PUBLIC_BASE_API_URL}/v1/webhooks/orum/${dbOrg?.id}`}
            </div>
          </div>
          <div className="mt-4">
            <Label>And this is your webhook signing secret:</Label>
            <div className="mt-2 flex items-center flex-wrap break-all font-bold">
              {`${process.env.NEXT_PUBLIC_ORUM_WEBHOOK_SIGNING_SECRET}`}
            </div>
          </div>
        </>
      )}
      {!isV2Integration && (
        <div className="mt-4">
          {provider?.configurationNotes && (
            <div className="mt-4">
              <Label>Notes</Label>
              <div className="text-sm">{provider?.configurationNotes}</div>
            </div>
          )}
          {provider?.configurationLinks && (
            <div className="mt-4">
              <Label>Useful links</Label>
              {provider?.configurationLinks.map((link: any, i: number) => {
                return (
                  <div key={'l-' + String(i)}>
                    <Link
                      href={link.url}
                      target="_new"
                      className="text-blue-500 hover:text-blue-600 text-sm mt-2"
                    >
                      {link.title}
                    </Link>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
