import IntegrationService from '@/lib/Integrations';
import RecallAiService from '@/lib/Integrations/RecallAi';
import {
  Integration,
  IntegrationBasicAuthDetails,
  IntegrationBearerAuthDetails,
  IntegrationNangoAuthDetails,
  IntegrationOAuthDetails,
  IntegrationServiceType,
  Recipe,
  ServiceProvider,
} from '@/lib/Integrations/types';
import IntegrationV2Service from '@/lib/Integrations/v2';
import { nangoClient } from '@/lib/nango';
import { useSearchParams } from 'next/navigation';
import { createContext, useEffect, useMemo, useState } from 'react';

const INTEGRATION_V2_PROVIDERS = [
  'hubspot',
  'salesloft',
  'aircall',
  'glyphic',
  'chorus',
  'zoomphone',
];

const initialState = {
  open: false,
  setOpen: () => {},
  editingIntegration: null,
  setEditingIntegration: () => {},
  panelState: 'selectProvider',
  setPanelState: () => {},
  isProviderZoomLiveRecorder: false,
  isSaving: false,
  setIsSaving: () => {},
  onSave: () => {},
  onClose: () => {},
  saveIntegration: async () => {},
  authKey: undefined,
  authType: 'none',
  setAuthKey: () => {},
  secretKey: undefined,
  setSecretKey: () => {},
  canSave: false,
  setCanSave: () => {},
  verifyUserCanSave: () => {},
  updateAccessKeys: () => {},
  integration: undefined,
  next: () => {},
  back: () => {},
  saveDetails: () => {},
  isNew: false,
  isV2Provider: false,
  selectedRecipe: null,
  setSelectedRecipe: () => {},
  authDetails: {},
  setAuthDetails: () => {},
  save: async () => {},
};

export const IntegrationModalV2Context = createContext<{
  open: boolean;
  setOpen: (open: boolean) => void;
  integration: Integration | undefined;
  editingIntegration: Integration | null;
  setEditingIntegration: (integration: Integration) => void;
  panelState: string;
  setPanelState: (panelState: string) => void;
  isProviderZoomLiveRecorder: boolean;
  isSaving: boolean;
  setIsSaving: (isSaving: boolean) => void;
  onSave: ((shouldRefresh: boolean) => unknown) | undefined;
  onClose: () => void;
  saveIntegration: (
    overrideProvider?: ServiceProvider,
    overrideName?: string,
    overrideIntegrationType?: IntegrationServiceType,
    dontCloseAutomatically?: boolean,
    onSaveHook?: () => Promise<unknown>,
  ) => Promise<unknown>;
  authType: string;
  authKey: string | undefined;
  setAuthKey: (authKey: string) => void;
  secretKey: string | undefined;
  setSecretKey: (secretKey: string) => void;
  canSave: boolean;
  setCanSave: (canSave: boolean) => void;
  verifyUserCanSave: () => void;
  updateAccessKeys: (ak?: string, sk?: string) => void;
  next: () => void;
  back: () => void;
  saveDetails: () => void;
  isNew: boolean;
  isV2Provider: boolean;
  selectedRecipe: Recipe | null;
  setSelectedRecipe: (recipe: Recipe) => void;
  authDetails: Record<string, string>;
  setAuthDetails: (authDetails: Record<string, string>) => void;
  save: (
    overrideProvider?: ServiceProvider,
    overrideName?: string,
    overrideIntegrationType?: IntegrationServiceType,
    dontCloseAutomatically?: boolean,
    onSaveHook?: () => Promise<unknown>,
  ) => Promise<unknown>;
}>(initialState);

export const IntegrationModalV2Provider = ({
  children,
  integration,
  onSave,
  onClose,
}: {
  children: React.ReactNode;
  integration?: Integration;
  onSave: ((shouldRefresh: boolean) => unknown) | undefined;
  onClose: () => void;
}) => {
  let isNew = true;
  let _panelState = 'selectProvider';
  if (integration) {
    _panelState = 'configProvider';
    isNew = false;
  }

  const searchParams = useSearchParams();
  const [open, setOpen] = useState(false);
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null);
  const [canSave, setCanSave] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [authKey, setAuthKey] = useState<string>();
  const [secretKey, setSecretKey] = useState<string>();
  const [authDetails, setAuthDetails] = useState<Record<string, string>>({});
  const [editingIntegration, setEditingIntegration] =
    useState<Integration | null>(integration || ({} as Integration));
  const [panelState, setPanelState] = useState<string>(_panelState);
  const isProviderZoomLiveRecorder =
    editingIntegration?.provider?.companyName === 'Hyperbound Meeting Recorder';

  const isV2Provider = useMemo(() => {
    return INTEGRATION_V2_PROVIDERS.includes(
      editingIntegration?.provider?.companyName?.toLowerCase() || '',
    );
  }, [editingIntegration]);

  const isOrum = useMemo(() => {
    return editingIntegration?.provider?.companyName?.toLowerCase() === 'orum';
  }, [editingIntegration]);

  const resetState = () => {
    setOpen(initialState.open);
    setEditingIntegration(initialState.editingIntegration);
    setPanelState(initialState.panelState);
    setIsSaving(initialState.isSaving);
    setAuthKey(initialState.authKey);
    setSecretKey(initialState.secretKey);
    setCanSave(initialState.canSave);
    setSelectedRecipe(initialState.selectedRecipe);
    setAuthDetails(initialState.authDetails);
  };

  useEffect(() => {
    verifyUserCanSave();
  }, [authKey, secretKey]);

  useEffect(() => {
    return () => {
      resetState();
    };
  }, []);

  const updateAccessKeys = (ak?: string, sk?: string) => {
    if (ak) {
      setAuthKey(ak);
    }
    if (sk) {
      setSecretKey(sk);
    }
  };

  const authType = useMemo(() => {
    if (isOrum) {
      return 'none';
    }

    return [
      'zoom',
      'google',
      'hubspot',
      'salesloft',
      'aircall',
      'glyphic',
      'chorus',
      'zoomphone',
    ].includes(editingIntegration?.provider?.companyName?.toLowerCase?.() || '')
      ? 'oauth'
      : 'basic';
  }, [editingIntegration, isOrum]);

  const verifyUserCanSave = () => {
    let cs = false;
    if (editingIntegration?.name != '') {
      if (
        editingIntegration &&
        editingIntegration.provider &&
        editingIntegration.provider.companyName !=
          'Manual Call Transcript Upload'
      ) {
        switch (authType) {
          case 'oauth': {
            cs = true;
            break;
          }
          case 'basic': {
            cs = !!authKey && !!secretKey;
            break;
          }
          case 'none': {
            cs = true;
            break;
          }
        }
      } else {
        cs = true;
      }
    }
    setCanSave(cs);
  };

  useEffect(() => {
    const conf: {
      integrationProviderId: number;
      integrationProviderCompanyName: 'Google' | 'Microsoft';
      integrationName: string;
    } = JSON.parse(searchParams.get('state') || '{}');
    if (conf.integrationProviderId) {
      setPanelState('configProvider');
      setEditingIntegration({
        name: conf.integrationName || '',
        type: IntegrationServiceType.REAL_CALL_SCORING,
        provider: {
          id: conf.integrationProviderId,
          companyName: conf.integrationProviderCompanyName || 'Google',
        },
      } as Integration);
    }
  }, [searchParams]);

  useEffect(() => {
    // for google/microsoft redirect
    if (editingIntegration?.provider?.id && searchParams.get('code')) {
      save();
    }
  }, [editingIntegration]);

  const redirectUserToGoogle = (
    integrationProviderId: number | null,
    integrationName: string | null,
  ) => {
    const params = {
      client_id: process.env.NEXT_PUBLIC_GOOGLE_OAUTH_CLIENT_ID || '',
      redirect_uri: process.env.NEXT_PUBLIC_BASE_URL + `/integrations`,
      response_type: 'code',
      scope: [
        'https://www.googleapis.com/auth/calendar.events.readonly',
        'https://www.googleapis.com/auth/userinfo.email',
      ].join(' '),
      access_type: 'offline',
      prompt: 'consent',
      state: JSON.stringify({
        integrationProviderId,
        integrationName,
        integrationProviderCompanyName: 'Google',
      }),
    };

    const url = new URL('https://accounts.google.com/o/oauth2/v2/auth');
    url.search = new URLSearchParams(params).toString();
    window.location.assign(url.toString());
  };

  const redirectUserToMicrosoft = (
    integrationProviderId: number | null,
    integrationName: string | null,
  ) => {
    const params = {
      client_id:
        process.env.NEXT_PUBLIC_MICROSOFT_OUTLOOK_OAUTH_CLIENT_ID || '',
      redirect_uri: process.env.NEXT_PUBLIC_BASE_URL + `/integrations`,
      response_type: 'code',
      scope: [
        'offline_access',
        'openid',
        'email',
        'https://graph.microsoft.com/Calendars.Read',
      ].join(' '),
      state: JSON.stringify({
        integrationProviderId,
        integrationName,
        integrationProviderCompanyName: 'Microsoft',
      }),
    };

    const url = new URL(
      'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
    );
    url.search = new URLSearchParams(params).toString();
    window.location.assign(url.toString());
  };

  const saveIntegrationOldFlow = async (
    editingIntegrationCopy: Integration,
    overrideIntegrationType?: IntegrationServiceType,
    dontCloseAutomatically = false,
    onSaveHook?: () => Promise<unknown>,
  ): Promise<{ go: boolean; shouldRefresh: boolean }> => {
    setIsSaving(true);
    let go = false;
    let basicAuthDetails:
        | Omit<IntegrationBasicAuthDetails, 'integrationId'>
        | undefined,
      bearerAuthDetails:
        | Omit<IntegrationBearerAuthDetails, 'integrationId'>
        | undefined,
      oauthDetails: Omit<IntegrationOAuthDetails, 'integrationId'> | undefined,
      nangoAuthDetails:
        | Omit<IntegrationNangoAuthDetails, 'integrationId'>
        | undefined;
    let shouldRefresh = false;
    try {
      let integrationType = overrideIntegrationType
        ? overrideIntegrationType
        : IntegrationServiceType.REAL_CALL_SCORING;
      switch (editingIntegrationCopy.provider?.companyName?.toLowerCase()) {
        case 'gong': {
          const nangoRes = await nangoClient.auth('gong', {
            credentials: {
              username: authKey,
              password: secretKey,
            },
          });
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'zoom': {
          const nangoRes = await nangoClient.auth('zoom');
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'hubspot': {
          const nangoRes = await nangoClient.auth('hubspot');
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'google': {
          const code = searchParams.get('code');
          if (code) {
            // received code from google, proceed to creating integration
            const res = await RecallAiService.exchangeGoogleCode(
              code,
              editingIntegrationCopy.provider.id,
            );
            if (!res?.refreshToken) {
              throw new Error('Could not get refresh token');
            }
            oauthDetails = {
              refreshToken: res.refreshToken,
              // recall ai manages it for us
              isExternallyManaged: true,
            };
            shouldRefresh = true;
            integrationType = IntegrationServiceType.CALENDAR;
            // for google meet, no auth
          } else if (
            overrideIntegrationType !== IntegrationServiceType.REAL_CALL_SCORING
          ) {
            // did not receive code from google yet, need to redirect user
            redirectUserToGoogle(
              editingIntegrationCopy.provider.id,
              editingIntegrationCopy.name,
            );
            return { go: false, shouldRefresh: false };
          }
          break;
        }
        case 'microsoft': {
          if (
            overrideIntegrationType !== IntegrationServiceType.REAL_CALL_SCORING
          ) {
            const code = searchParams.get('code');
            if (code) {
              // received code from microsoft, proceed to creating integration
              const res = await RecallAiService.exchangeMicrosoftCode(
                code,
                editingIntegrationCopy.provider.id,
              );
              if (!res?.refreshToken) {
                throw new Error('Could not get refresh token');
              }
              oauthDetails = {
                refreshToken: res.refreshToken,
                // recall ai manages it for us
                isExternallyManaged: true,
              };
              shouldRefresh = true;
              integrationType = IntegrationServiceType.CALENDAR;
            } else {
              // did not receive code from microsoft yet, need to redirect user
              redirectUserToMicrosoft(
                editingIntegrationCopy.provider.id,
                editingIntegrationCopy.name,
              );
              return { go: false, shouldRefresh: false };
            }
          } else {
            // for microsoft teams, no auth
          }
          break;
        }
        case 'orum': {
          // for orum, no auth
          break;
        }
      }
      await IntegrationService.upsertIntegration(
        editingIntegrationCopy.name,
        editingIntegrationCopy.id,
        integrationType,
        editingIntegrationCopy.provider?.id,
        basicAuthDetails,
        bearerAuthDetails,
        oauthDetails,
        nangoAuthDetails,
      );
      go = true;
    } catch (e) {
      console.log(e);
    }

    if (onSaveHook) {
      await onSaveHook();
    }

    setIsSaving(false);
    if (go && !dontCloseAutomatically) {
      onSave?.(shouldRefresh);
      onClose();
    }

    return { go, shouldRefresh };
  };

  const saveIntegrationV2 = async (
    editingIntegrationCopy: Integration,
    overrideIntegrationType?: IntegrationServiceType,
    dontCloseAutomatically = false,
    onSaveHook?: () => Promise<unknown>,
  ) => {
    setIsSaving(true);
    let go = false;

    if (!editingIntegrationCopy.provider?.companyName || !selectedRecipe?.id) {
      return;
    }
    try {
      await IntegrationV2Service.createIntegration({
        name: editingIntegrationCopy.name,
        providerName: editingIntegrationCopy.provider?.companyName,
        recipeId: selectedRecipe.id,
        authDetails,
      });
      go = true;
    } catch (e) {
      console.log(e);
    }

    if (onSaveHook) {
      await onSaveHook();
    }

    setIsSaving(false);
    if (go && !dontCloseAutomatically) {
      onSave?.(false);
      onClose();
    }
  };

  const saveIntegration = async (
    overrideProvider?: ServiceProvider,
    overrideName?: string,
    overrideIntegrationType?: IntegrationServiceType,
    dontCloseAutomatically = false,
    onSaveHook?: () => Promise<unknown>,
  ) => {
    const editingIntegrationCopy = { ...editingIntegration } as Integration;
    if (overrideProvider) {
      editingIntegrationCopy.provider = overrideProvider;
    }
    if (overrideName) {
      editingIntegrationCopy.name = overrideName;
    }

    if (isV2Provider) {
      await saveIntegrationV2(
        editingIntegrationCopy,
        overrideIntegrationType,
        dontCloseAutomatically,
        onSaveHook,
      );
    } else {
      await saveIntegrationOldFlow(
        editingIntegrationCopy,
        overrideIntegrationType,
        dontCloseAutomatically,
        onSaveHook,
      );
    }
  };

  const saveDetails = async () => {
    setIsSaving(true);

    let go = false;

    if (editingIntegration?.name && editingIntegration?.id) {
      try {
        await IntegrationService.updateDetails(
          editingIntegration.id,
          editingIntegration.name,
        );
        go = true;
      } catch (e) {
        console.log(e);
      }
    }

    setIsSaving(false);
    if (go) {
      if (onSave) {
        onSave(false);
      }
      onClose();
    }
  };

  const save = async (
    overrideProvider?: ServiceProvider,
    overrideName?: string,
    overrideIntegrationType?: IntegrationServiceType,
    dontCloseAutomatically = false,
    onSaveHook?: () => Promise<unknown>,
  ) => {
    setIsSaving(true);
    const editingIntegrationCopy = { ...editingIntegration } as Integration;
    if (overrideProvider) {
      editingIntegrationCopy.provider = overrideProvider;
    }
    if (overrideName) {
      editingIntegrationCopy.name = overrideName;
    }

    let go = false;

    let basicAuthDetails:
        | Omit<IntegrationBasicAuthDetails, 'integrationId'>
        | undefined,
      bearerAuthDetails:
        | Omit<IntegrationBearerAuthDetails, 'integrationId'>
        | undefined,
      oauthDetails: Omit<IntegrationOAuthDetails, 'integrationId'> | undefined,
      nangoAuthDetails:
        | Omit<IntegrationNangoAuthDetails, 'integrationId'>
        | undefined;
    let shouldRefresh = false;
    try {
      let integrationType = overrideIntegrationType
        ? overrideIntegrationType
        : IntegrationServiceType.REAL_CALL_SCORING;
      switch (editingIntegrationCopy.provider?.companyName?.toLowerCase()) {
        case 'gong': {
          const nangoRes = await nangoClient.auth('gong', {
            credentials: {
              username: authKey,
              password: secretKey,
            },
          });
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'zoom': {
          const nangoRes = await nangoClient.auth('zoom');
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'hubspot': {
          const nangoRes = await nangoClient.auth('hubspot');
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'google': {
          const code = searchParams.get('code');
          if (code) {
            // received code from google, proceed to creating integration
            const res = await RecallAiService.exchangeGoogleCode(
              code,
              editingIntegrationCopy.provider.id,
            );
            if (!res?.refreshToken) {
              throw new Error('Could not get refresh token');
            }
            oauthDetails = {
              refreshToken: res.refreshToken,
              // recall ai manages it for us
              isExternallyManaged: true,
            };
            shouldRefresh = true;
            integrationType = IntegrationServiceType.CALENDAR;
            // for google meet, no auth
          } else if (
            overrideIntegrationType !== IntegrationServiceType.REAL_CALL_SCORING
          ) {
            // did not receive code from google yet, need to redirect user
            redirectUserToGoogle(
              editingIntegrationCopy.provider.id,
              editingIntegrationCopy.name,
            );
            return;
          }
          break;
        }
        case 'microsoft': {
          if (
            overrideIntegrationType !== IntegrationServiceType.REAL_CALL_SCORING
          ) {
            const code = searchParams.get('code');
            if (code) {
              // received code from microsoft, proceed to creating integration
              const res = await RecallAiService.exchangeMicrosoftCode(
                code,
                editingIntegrationCopy.provider.id,
              );
              if (!res?.refreshToken) {
                throw new Error('Could not get refresh token');
              }
              oauthDetails = {
                refreshToken: res.refreshToken,
                // recall ai manages it for us
                isExternallyManaged: true,
              };
              shouldRefresh = true;
              integrationType = IntegrationServiceType.CALENDAR;
            } else {
              // did not receive code from microsoft yet, need to redirect user
              redirectUserToMicrosoft(
                editingIntegrationCopy.provider.id,
                editingIntegrationCopy.name,
              );
              return;
            }
          } else {
            // for microsoft teams, no auth
          }
          break;
        }
      }
      await IntegrationService.upsertIntegration(
        editingIntegrationCopy.name,
        editingIntegrationCopy.id,
        integrationType,
        editingIntegrationCopy.provider?.id,
        basicAuthDetails,
        bearerAuthDetails,
        oauthDetails,
        nangoAuthDetails,
      );
      go = true;
    } catch (e) {
      console.log(e);
    }

    if (onSaveHook) {
      await onSaveHook();
    }

    setIsSaving(false);
    if (go && !dontCloseAutomatically) {
      onSave?.(shouldRefresh);
      onClose();
    }
  };

  const next = async () => {
    const go = true;
    if (editingIntegration) {
      if (go) {
        setPanelState((o: string) => {
          if (o == 'selectProvider') {
            return 'configProvider';
          }

          if (o == 'configProvider') {
            return 'recipeList';
          }

          if (o == 'recipeList') {
            return 'authDetails';
          }

          return '';
        });
      }
    }
  };

  const back = () => {
    setPanelState((o: string) => {
      if (o == 'configProvider') {
        return 'selectProvider';
      }

      return '';
    });
  };

  return (
    <IntegrationModalV2Context.Provider
      value={{
        open,
        setOpen,
        integration,
        editingIntegration,
        setEditingIntegration,
        panelState,
        setPanelState,
        isProviderZoomLiveRecorder,
        isSaving,
        setIsSaving,
        onSave,
        onClose,
        saveIntegration,
        authKey,
        setAuthKey,
        secretKey,
        setSecretKey,
        canSave,
        setCanSave,
        verifyUserCanSave,
        updateAccessKeys,
        next,
        back,
        saveDetails,
        authType,
        isNew,
        isV2Provider,
        selectedRecipe,
        setSelectedRecipe,
        authDetails,
        setAuthDetails,
        save,
      }}
    >
      {children}
    </IntegrationModalV2Context.Provider>
  );
};
