import { IntegrationModalV2Context } from '@/common/Integrations/IntegrationModalV2/context';
import { useContext } from 'react';
import Hubspot from './components/Hubspot';
import Salesloft from './components/Salesloft';
import Aircall from './components/Aircall';
import Chorus from './components/Chorus';
import ZoomPhone from './components/ZoomPhone';
import { Label } from '@/components/ui/label';
import Link from 'next/link';
import Glyphic from './components/Glyphic';

export default function AuthDetails() {
  const { editingIntegration } = useContext(IntegrationModalV2Context);

  const AuthDetailsComponentByProvider = {
    hubspot: Hubspot,
    salesloft: Salesloft,
    aircall: Aircall,
    chorus: Chorus,
    zoomphone: ZoomPhone,
    glyphic: Glyphic,
    default: () => null,
  };

  return (
    <div>
      {AuthDetailsComponentByProvider[
        (editingIntegration?.provider?.companyName?.toLowerCase() as keyof typeof AuthDetailsComponentByProvider) ||
          'default'
      ]()}
      <div className="mt-4">
        {editingIntegration?.provider?.configurationNotes && (
          <div className="mt-4">
            <Label>Notes</Label>
            <div className="text-sm">
              {editingIntegration?.provider?.configurationNotes}
            </div>
          </div>
        )}
        {editingIntegration?.provider?.configurationLinks && (
          <div className="mt-4">
            <Label>Useful links</Label>
            {editingIntegration?.provider?.configurationLinks.map(
              (link: { title: string; url: string }, i: number) => {
                return (
                  <div key={'l-' + String(i)}>
                    <Link
                      href={link.url}
                      target="_new"
                      className="text-blue-500 hover:text-blue-600 text-sm mt-2"
                    >
                      {link.title}
                    </Link>
                  </div>
                );
              },
            )}
          </div>
        )}
      </div>
    </div>
  );
}
