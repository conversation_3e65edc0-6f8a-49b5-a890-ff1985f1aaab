import { IntegrationModalV2Context } from '@/common/Integrations/IntegrationModalV2/context';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useContext } from 'react';

export default function Hubspot() {
  const { authDetails, setAuthDetails } = useContext(IntegrationModalV2Context);
  return (
    <div>
      <div className="mt-4">
        <Label>Username</Label>
        <div className="mt-2 flex items-center">
          <Input
            value={authDetails.username}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setAuthDetails({ ...authDetails, username: e.target.value });
            }}
          />
        </div>
      </div>
      <div className="mt-4">
        <Label>Password</Label>
        <div className="mt-2 flex items-center">
          <Input
            value={authDetails.password}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setAuthDetails({ ...authDetails, password: e.target.value });
            }}
          />
        </div>
      </div>
    </div>
  );
}
