import { IntegrationModalV2Context } from '@/common/Integrations/IntegrationModalV2/context';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useContext } from 'react';

export default function Hubspot() {
  const { authDetails, setAuthDetails } = useContext(IntegrationModalV2Context);
  return (
    <div>
      <div className="mt-4">
        <Label>API Token</Label>
        <div className="mt-2 flex items-center">
          <Input
            value={authDetails.api_key}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setAuthDetails({ ...authDetails, api_key: e.target.value });
            }}
          />
        </div>
      </div>
    </div>
  );
}
