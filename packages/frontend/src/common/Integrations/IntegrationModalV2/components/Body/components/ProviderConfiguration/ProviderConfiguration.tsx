import { IntegrationModalV2Context } from '@/common/Integrations/IntegrationModalV2/context';
import IntegrationDetails from '@/common/Integrations/IntegrationModalV2/integrationDetails';
import IntegrationDetailsForHyperboundMeetingRecorder from '@/common/Integrations/IntegrationModalV2/integrationDetailsForHyperboundMeetingRecorder';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Integration } from '@/lib/Integrations/types';
import { useContext } from 'react';

export default function ProviderConfiguration() {
  const {
    editingIntegration,
    setEditingIntegration,
    isProviderZoomLiveRecorder,
    isSaving,
    saveIntegration,
    verifyUserCanSave,
    updateAccessKeys,
    isV2Provider,
  } = useContext(IntegrationModalV2Context);

  if (isProviderZoomLiveRecorder) {
    return (
      <IntegrationDetailsForHyperboundMeetingRecorder
        isSaving={isSaving}
        saveIntegration={saveIntegration}
      />
    );
  }

  return (
    <div>
      <div>
        <Label>Names</Label>
        <p className="text-sm">
          This is just to identify this integration in other pages.
        </p>
        <div className="mt-2 flex items-center">
          <Input
            disabled={isSaving}
            value={editingIntegration?.name}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              setEditingIntegration({
                ...(editingIntegration || ({} as Integration)),
                name: e.target.value,
              });
              verifyUserCanSave();
            }}
          />
        </div>
      </div>
      {editingIntegration?.provider &&
        editingIntegration?.provider?.companyName !=
          'Manual Call Transcript Upload' && (
          <IntegrationDetails
            onUpdate={updateAccessKeys}
            provider={editingIntegration.provider}
            isV2Integration={isV2Provider}
          />
        )}
    </div>
  );
}
