import { DialogFooter } from '@/components/ui/dialog';
import useUserSession from '@/hooks/useUserSession';
import { ChevronRight, Loader2Icon } from 'lucide-react';
import { IntegrationModalV2Context } from '../../context';
import { useContext } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';

export default function Footer() {
  const { canAccessIntegrations } = useUserSession();
  const {
    panelState,
    editingIntegration,
    isProviderZoomLiveRecorder,
    isSaving,
    canSave,
    integration,
    back,
    next,
    saveDetails,
    saveIntegration,
    isNew,
    isV2Provider,
    authType,
    save,
  } = useContext(IntegrationModalV2Context);

  if (!canAccessIntegrations) {
    return null;
  }

  return (
    <DialogFooter>
      {panelState != 'selectProvider' && integration == undefined && (
        <Button
          variant={'outline'}
          onClick={back}
          disabled={editingIntegration?.provider == undefined || isSaving}
        >
          <ChevronLeft size={18} className="ml-1" />
          Back
        </Button>
      )}
      {panelState == 'selectProvider' && (
        <Button
          variant={'default'}
          onClick={next}
          disabled={editingIntegration?.provider == undefined || isSaving}
        >
          Next
          <ChevronRight size={18} className="ml-1" />
        </Button>
      )}
      {panelState == 'configProvider' &&
        !isProviderZoomLiveRecorder &&
        (isNew && isV2Provider ? (
          <Button
            variant={'default'}
            onClick={() => {
              if (isV2Provider) {
                next();
              } else {
                saveIntegration(undefined, undefined, undefined, false);
              }
            }}
            disabled={!canSave || isSaving}
          >
            {isSaving ? (
              <Loader2Icon className="animate-spin" size={18} />
            ) : isV2Provider ? (
              'Next'
            ) : (
              'Save'
            )}
          </Button>
        ) : isNew ? (
          <Button
            variant={'default'}
            onClick={() => save(undefined, undefined, undefined, false)}
            disabled={!canSave || isSaving}
          >
            {isSaving ? (
              <Loader2Icon className="animate-spin" size={18} />
            ) : authType === 'oauth' ? (
              'Open'
            ) : (
              'Save'
            )}
          </Button>
        ) : (
          <>
            {authType === 'oauth' && (
              <Button
                variant={'outline'}
                onClick={() => save(undefined, undefined, undefined, false)}
                disabled={!canSave || isSaving}
              >
                {isSaving ? (
                  <Loader2Icon className="animate-spin" size={18} />
                ) : (
                  'Edit configuration'
                )}
              </Button>
            )}

            <Button onClick={saveDetails} disabled={!canSave || isSaving}>
              {isSaving ? (
                <Loader2Icon className="animate-spin" size={18} />
              ) : (
                'Save'
              )}
            </Button>
          </>
        ))}
      {panelState === 'recipeList' && isV2Provider ? (
        <Button
          variant={'default'}
          onClick={next}
          disabled={!canSave || isSaving}
        >
          {isSaving ? (
            <Loader2Icon className="animate-spin" size={18} />
          ) : (
            'Next'
          )}
        </Button>
      ) : null}

      {panelState == 'authDetails' && (
        <Button
          variant={'default'}
          onClick={() => {
            saveIntegration(undefined, undefined, undefined, false);
          }}
          disabled={!canSave || isSaving}
        >
          {isSaving ? (
            <Loader2Icon className="animate-spin" size={18} />
          ) : (
            'Save'
          )}
        </Button>
      )}
    </DialogFooter>
  );
}
