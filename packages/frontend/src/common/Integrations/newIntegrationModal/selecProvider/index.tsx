import { useServiceProviders } from '@/hooks/useIntegrations';
import {
  IntegrationServiceType,
  ServiceProvider,
} from '@/lib/Integrations/types';
import { useEffect, useMemo, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2Icon } from 'lucide-react';
import Image from 'next/image';
import { hyperboundMeetingRecorderProviderName } from '../..';
import useUserSession from '@/hooks/useUserSession';
import IntegrationService from '@/lib/Integrations';

interface IProps {
  onProviderSelected: (p: ServiceProvider) => void;
}

export default function SelectProvider({ onProviderSelected }: IProps) {
  const { betaIntegrations, betaIntegrationsAllowed } = useUserSession();
  const { data: dbProviders, isLoading: isLoadingProviders } =
    useServiceProviders(IntegrationServiceType.REAL_CALL_SCORING);

  const [providers, setProviders] = useState<ServiceProvider[]>();
  const adjustedProviders = useMemo(() => {
    if (!providers) {
      return [];
    }
    const filteredProviders = providers.filter(
      (p) => p.companyName !== 'Google' && p.companyName != 'Microsoft',
    );
    const googleProvider = providers.find((p) => p.companyName === 'Google');
    const zoomProvider = providers.find((p) => p.companyName === 'Zoom');
    if (!!zoomProvider && !!googleProvider) {
      filteredProviders.push({
        id: -1,
        logoUrl: '/images/square-logo-transparent.svg',
        needsUserTeamSettings: false,
        services: [IntegrationServiceType.REAL_CALL_SCORING],
        companyName: hyperboundMeetingRecorderProviderName,
      });
    }
    return filteredProviders.filter(
      ({ companyName }) =>
        !betaIntegrations.has(companyName.toLowerCase()) ||
        betaIntegrationsAllowed.has(companyName.toLowerCase()),
    );
  }, [providers]);

  const [selectedProvider, setSelectedProvider] = useState<ServiceProvider>();

  useEffect(() => {
    if (!isLoadingProviders && dbProviders) {
      setProviders(dbProviders);
    }
  }, [dbProviders, isLoadingProviders]);

  const selectProvider = (id: string) => {
    adjustedProviders?.map((p) => {
      if (p.id == Number(id)) {
        setSelectedProvider(p);
        onProviderSelected(p);
        return;
      }
    });
  };

  return (
    <div>
      <div className="mt-4 mb-4 flex items-center">
        <div className="w-[25vw]">
          <Select
            onValueChange={(value: string) => {
              selectProvider(value);
            }}
            value={String(selectedProvider?.id || '')}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose a provider" />
            </SelectTrigger>
            <SelectContent>
              {adjustedProviders && (
                <>
                  {adjustedProviders.map((option: ServiceProvider) => {
                    return (
                      <SelectItem key={option.id} value={String(option.id)}>
                        <div className="flex items-center">
                          <Image
                            src={IntegrationService.getProviderLogoUrl(
                              option.logoUrl,
                            )}
                            alt={`${option.companyName} Logo`}
                            width={20}
                            height={20}
                            className="mr-1"
                          />
                          <div>{option.companyName}</div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </>
              )}
            </SelectContent>
          </Select>
        </div>
        <div className="ml-1">
          {isLoadingProviders && <Loader2Icon className="animate-spin" />}
        </div>
      </div>
    </div>
  );
}
