import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import useUserSession from '@/hooks/useUserSession';
import { useAuthInfo } from '@propelauth/react';
import {
  Blocks,
  ChevronLeft,
  ChevronRight,
  Loader2Icon,
  Mail,
} from 'lucide-react';
import SelectProvider from './selecProvider';
import { useEffect, useRef, useState } from 'react';
import {
  Integration,
  IntegrationBasicAuthDetails,
  IntegrationBearerAuthDetails,
  IntegrationNangoAuthDetails,
  IntegrationOAuthDetails,
  IntegrationServiceType,
  ServiceProvider,
} from '@/lib/Integrations/types';
import IntegrationDetails from './integrationDetails';
import IntegrationService from '@/lib/Integrations';
import { Id, toast } from 'react-toastify';
import { nangoClient } from '@/lib/nango';
import { useSearchParams } from 'next/navigation';
import RecallAiService from '@/lib/Integrations/RecallAi';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { hyperboundMeetingRecorderProviderName } from '..';
import IntegrationDetailsForHyperboundMeetingRecorder from './integrationDetailsForHyperboundMeetingRecorder';
import { cn } from '@/lib/utils';

interface IProps {
  open: boolean;
  onClose: () => void;
  onSave?: (shouldRefresh: boolean) => unknown;
  integration?: Integration;
}

export default function NewIntegrationModal({
  open,
  onClose,
  onSave,
  integration,
}: IProps) {
  const { canAccessIntegrations } = useUserSession();

  const authInfo = useAuthInfo();

  let isNew = true;
  let _panelState = 'selectProvider';
  if (integration) {
    _panelState = 'configProvider';
    isNew = false;
  }

  const [panelState, setPanelState] = useState<string>(_panelState);
  const [editingIntegration, setEditingIntegration] = useState<Integration>(
    // @ts-ignore
    integration || {},
  );
  const [canSave, setCanSave] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const errorToastId = useRef<Id | null>(null);

  /*******************************************/
  /************* ACCES TOKENS ****************/
  /*******************************************/

  const [authKey, setAuthKey] = useState<string>();
  const [secretKey, setSecretKey] = useState<string>();

  const searchParams = useSearchParams();

  useEffect(() => {
    const conf: { integrationProviderId: number; integrationName: string } =
      JSON.parse(searchParams.get('state') || '{}');
    if (conf.integrationProviderId) {
      setPanelState('configProvider');
      setEditingIntegration({
        name: conf.integrationName || '',
        type: IntegrationServiceType.REAL_CALL_SCORING,
        provider: {
          id: conf.integrationProviderId,
          companyName: 'Google',
        },
      } as Integration);
    }
  }, [searchParams]);

  useEffect(() => {
    // for google redirect
    if (editingIntegration?.provider?.id && searchParams.get('code')) {
      save();
    }
  }, [editingIntegration]);

  const updateAccessKeys = (ak?: string, sk?: string) => {
    if (ak) {
      setAuthKey(ak);
    }
    if (sk) {
      setSecretKey(sk);
    }
  };

  const authType = ['zoom', 'google', 'hubspot'].includes(
    editingIntegration?.provider?.companyName?.toLowerCase?.() || '',
  )
    ? 'oauth'
    : 'basic';

  useEffect(() => {
    verifyUserCanSave();
  }, [authType, authKey, secretKey]);

  const verifyUserCanSave = () => {
    let cs = false;
    if (editingIntegration.name != '') {
      if (
        editingIntegration &&
        editingIntegration.provider &&
        editingIntegration.provider.companyName !=
          'Manual Call Transcript Upload'
      ) {
        switch (authType) {
          case 'oauth': {
            cs = true;
            break;
          }
          case 'basic': {
            cs = !!authKey && !!secretKey;
            break;
          }
        }
      } else {
        cs = true;
      }
    }
    setCanSave(cs);
  };

  /*******************************************/
  /****************** SAVE *******************/
  /*******************************************/

  const redirectUserToGoogle = (
    integrationProviderId: number | null,
    integrationName: string | null,
  ) => {
    const params = {
      client_id: process.env.NEXT_PUBLIC_GOOGLE_OAUTH_CLIENT_ID || '',
      redirect_uri: process.env.NEXT_PUBLIC_BASE_URL + `/integrations`,
      response_type: 'code',
      scope: [
        'https://www.googleapis.com/auth/calendar.events.readonly',
        'https://www.googleapis.com/auth/userinfo.email',
      ].join(' '),
      access_type: 'offline',
      prompt: 'consent',
      state: JSON.stringify({ integrationProviderId, integrationName }),
    };

    const url = new URL('https://accounts.google.com/o/oauth2/v2/auth');
    url.search = new URLSearchParams(params).toString();
    window.location.assign(url.toString());
  };

  const saveDetails = async () => {
    setIsSaving(true);

    let go = false;

    if (editingIntegration.name) {
      try {
        await IntegrationService.updateDetails(
          editingIntegration.id,
          editingIntegration.name,
        );
        go = true;
      } catch (e) {
        console.log(e);
        errorToastId.current = toast.error(
          'There was an error. Please try again.',
        );
      }
    } else {
      errorToastId.current = toast.error('Name cannot be empty.');
    }

    setIsSaving(false);
    if (go) {
      if (onSave) {
        onSave(false);
      }
      onClose();
    }
  };

  const save = async (
    overrideProvider?: ServiceProvider,
    overrideName?: string,
    overrideIntegrationType?: IntegrationServiceType,
    dontCloseAutomatically = false,
    onSaveHook?: () => Promise<unknown>,
  ) => {
    setIsSaving(true);
    const editingIntegrationCopy = { ...editingIntegration } as Integration;
    if (overrideProvider) {
      editingIntegrationCopy.provider = overrideProvider;
    }
    if (overrideName) {
      editingIntegrationCopy.name = overrideName;
    }

    let go = false;

    let basicAuthDetails:
        | Omit<IntegrationBasicAuthDetails, 'integrationId'>
        | undefined,
      bearerAuthDetails:
        | Omit<IntegrationBearerAuthDetails, 'integrationId'>
        | undefined,
      oauthDetails: Omit<IntegrationOAuthDetails, 'integrationId'> | undefined,
      nangoAuthDetails:
        | Omit<IntegrationNangoAuthDetails, 'integrationId'>
        | undefined;
    let shouldRefresh = false;
    try {
      let integrationType = overrideIntegrationType
        ? overrideIntegrationType
        : IntegrationServiceType.REAL_CALL_SCORING;
      switch (editingIntegrationCopy.provider?.companyName?.toLowerCase()) {
        case 'gong': {
          const nangoRes = await nangoClient.auth('gong', {
            credentials: {
              username: authKey,
              password: secretKey,
            },
          });
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'zoom': {
          const nangoRes = await nangoClient.auth('zoom');
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'hubspot': {
          const nangoRes = await nangoClient.auth('hubspot');
          nangoAuthDetails = {
            nangoIntegrationId: nangoRes.providerConfigKey,
            nangoConnectionId: nangoRes.connectionId,
          };
          break;
        }
        case 'google': {
          const code = searchParams.get('code');
          if (code) {
            // received code from google, proceed to creating integration
            const res = await RecallAiService.exchangeGoogleCode(
              code,
              editingIntegrationCopy.provider.id,
            );
            if (!res?.refreshToken) {
              throw new Error('Could not get refresh token');
            }
            oauthDetails = {
              refreshToken: res.refreshToken,
              // recall ai manages it for us
              isExternallyManaged: true,
            };
            shouldRefresh = true;
            integrationType = IntegrationServiceType.CALENDAR;
            // for google meet, no auth
          } else if (
            overrideIntegrationType !== IntegrationServiceType.REAL_CALL_SCORING
          ) {
            // did not receive code from google yet, need to redirect user
            redirectUserToGoogle(
              editingIntegrationCopy.provider.id,
              editingIntegrationCopy.name,
            );
            return;
          }
          break;
        }
        case 'microsoft': {
          // for microsoft teams, no auth
          break;
        }
      }
      await IntegrationService.upsertIntegration(
        editingIntegrationCopy.name,
        editingIntegrationCopy.id,
        integrationType,
        editingIntegrationCopy.provider?.id,
        basicAuthDetails,
        bearerAuthDetails,
        oauthDetails,
        nangoAuthDetails,
      );
      go = true;
    } catch (e) {
      console.log(e);
      errorToastId.current = toast.error(
        'There was an error. Please try again.',
      );
    }

    if (onSaveHook) {
      await onSaveHook();
    }

    setIsSaving(false);
    if (go && !dontCloseAutomatically) {
      onSave?.(shouldRefresh);
      onClose();
    }
  };

  /*******************************************/
  /************* NAVIGATION ******************/
  /*******************************************/

  const next = async () => {
    const go = true;
    if (editingIntegration) {
      if (go) {
        setPanelState((o: string) => {
          if (o == 'selectProvider') {
            return 'configProvider';
          }

          return '';
        });
      }
    }
  };

  const back = () => {
    setPanelState((o: string) => {
      if (o == 'configProvider') {
        return 'selectProvider';
      }

      return '';
    });
  };

  const isProviderZoomLiveRecorder =
    editingIntegration.provider?.companyName ===
    hyperboundMeetingRecorderProviderName;

  /*******************************************/
  /************* RENDERING ******************/
  /*******************************************/

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className={cn('close-btn mt-16 flex flex-col', {
          'h-4/5':
            panelState === 'configProvider' && isProviderZoomLiveRecorder,
        })}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Blocks className="w-5 h-5 mr-2" />
            {editingIntegration?.name
              ? 'Editing ' + editingIntegration.name
              : 'New integration'}
          </DialogTitle>
        </DialogHeader>

        {canAccessIntegrations && (
          <div className="flex-1">
            {panelState == 'selectProvider' && (
              <SelectProvider
                onProviderSelected={(p: ServiceProvider) => {
                  setEditingIntegration({ ...editingIntegration, provider: p });
                }}
              />
            )}
            {panelState == 'configProvider' &&
              (!isProviderZoomLiveRecorder ? (
                <div>
                  <div>
                    <Label>Names</Label>
                    <p className="text-sm">
                      This is just to identify this integration in other pages.
                    </p>
                    <div className="mt-2 flex items-center">
                      <Input
                        value={editingIntegration.name}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          setEditingIntegration({
                            ...editingIntegration,
                            name: e.target.value,
                          });
                          verifyUserCanSave();
                        }}
                      />
                    </div>
                  </div>
                  {editingIntegration &&
                    editingIntegration.provider &&
                    editingIntegration.provider.companyName !=
                      'Manual Call Transcript Upload' && (
                      <IntegrationDetails
                        onUpdate={updateAccessKeys}
                        provider={editingIntegration.provider}
                      />
                    )}
                </div>
              ) : (
                <IntegrationDetailsForHyperboundMeetingRecorder
                  isSaving={isSaving}
                  saveIntegration={save}
                />
              ))}
          </div>
        )}

        {!canAccessIntegrations && (
          <div className="grid gap-4 py-4">
            <div>
              <Button
                size={'lg'}
                variant={'default'}
                className={
                  'w-full text-white border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-80 transition-opacity duration-200'
                }
                style={{
                  backgroundImage:
                    'linear-gradient(to right, #000000, #5189CE, #A168A2)',
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  window.location.href = 'mailto:<EMAIL>';
                }}
              >
                <Mail className="mr-2 h-4 w-4" />
                Contact the Hyperbound team to{' '}
                {authInfo?.isLoggedIn ? 'get started' : 'learn more'}
              </Button>
            </div>
          </div>
        )}
        {canAccessIntegrations && (
          <DialogFooter>
            {panelState != 'selectProvider' && integration == undefined && (
              <Button
                variant={'outline'}
                onClick={back}
                disabled={editingIntegration.provider == undefined}
              >
                <ChevronLeft size={18} className="ml-1" />
                Back
              </Button>
            )}

            {panelState == 'selectProvider' && (
              <Button
                variant={'default'}
                onClick={next}
                disabled={editingIntegration.provider == undefined}
              >
                Next
                <ChevronRight size={18} className="ml-1" />
              </Button>
            )}

            {panelState == 'configProvider' &&
              !isProviderZoomLiveRecorder &&
              (isNew ? (
                <Button
                  variant={'default'}
                  onClick={() => save(undefined, undefined, undefined, false)}
                  disabled={!canSave || isSaving}
                >
                  {isSaving ? (
                    <Loader2Icon className="animate-spin" size={18} />
                  ) : authType === 'oauth' ? (
                    'Open'
                  ) : (
                    'Save'
                  )}
                </Button>
              ) : (
                <>
                  {authType === 'oauth' && (
                    <Button
                      variant={'outline'}
                      onClick={() =>
                        save(undefined, undefined, undefined, false)
                      }
                      disabled={!canSave || isSaving}
                    >
                      {isSaving ? (
                        <Loader2Icon className="animate-spin" size={18} />
                      ) : (
                        'Edit configuration'
                      )}
                    </Button>
                  )}

                  <Button onClick={saveDetails} disabled={!canSave || isSaving}>
                    {isSaving ? (
                      <Loader2Icon className="animate-spin" size={18} />
                    ) : (
                      'Save'
                    )}
                  </Button>
                </>
              ))}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
