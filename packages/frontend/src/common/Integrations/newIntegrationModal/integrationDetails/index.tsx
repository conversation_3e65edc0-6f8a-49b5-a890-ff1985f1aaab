import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Integration, ServiceProvider } from '@/lib/Integrations/types';
import Link from 'next/link';
import { useState } from 'react';

interface IProps {
  onUpdate: (ak?: string, sk?: string) => void;
  provider?: ServiceProvider;
}

export default function IntegrationDetails({ onUpdate, provider }: IProps) {
  const [authKey, setAuthKey] = useState<string>();
  const [secretKey, setSecretKey] = useState<string>();

  const update = (s: string, v: string) => {
    if (s == 'authKey') {
      setAuthKey(v);
      onUpdate(v, secretKey);
    } else if (s == 'secretKey') {
      setSecretKey(v);
      onUpdate(authKey, v);
    }
  };

  const authType = ['zoom', 'google', 'hubspot'].includes(
    provider?.companyName?.toLowerCase?.() || '',
  )
    ? 'oauth'
    : 'basic';

  return (
    <div>
      {authType === 'basic' && (
        <>
          <div className="mt-2">
            <Label>Auth Key</Label>
            <div className="mt-2 flex items-center">
              <Input
                value={authKey}
                onChange={(e: any) => {
                  update('authKey', e.target.value);
                }}
              />
            </div>
          </div>
          <div className="mt-4">
            <Label>Secret Key</Label>
            <div className="mt-2 flex items-center">
              <Input
                value={secretKey}
                onChange={(e: any) => {
                  update('secretKey', e.target.value);
                }}
              />
            </div>
          </div>
        </>
      )}
      {provider?.configurationNotes && (
        <div className="mt-4">
          <Label>Notes</Label>
          <div className="text-sm">{provider?.configurationNotes}</div>
        </div>
      )}
      {provider?.configurationLinks && (
        <div className="mt-4">
          <Label>Useful links</Label>
          {provider?.configurationLinks.map((link: any, i: number) => {
            return (
              <div key={'l-' + String(i)}>
                <Link
                  href={link.url}
                  target="_new"
                  className="text-blue-500 hover:text-blue-600 text-sm mt-2"
                >
                  {link.title}
                </Link>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
