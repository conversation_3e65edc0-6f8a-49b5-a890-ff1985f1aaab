import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useIntegrations, useServiceProviders } from '@/hooks/useIntegrations';
import {
  Integration,
  IntegrationServiceType,
  ServiceProvider,
} from '@/lib/Integrations/types';
import { Check } from 'lucide-react';
import Link from 'next/link';
import { useMemo, useState } from 'react';
import { hyperboundMeetingRecorderProviderName } from '../..';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  isSaving: boolean;
  saveIntegration: (
    serviceProviderOverride?: ServiceProvider,
    nameOverride?: string,
    integrationTypeOverride?: IntegrationServiceType,
    dontCloseAutomatically?: boolean,
    onSaveHook?: () => Promise<unknown>,
  ) => Promise<unknown>;
}

export default function IntegrationDetailsForHyperboundMeetingRecorder({
  isSaving,
  saveIntegration,
}: IProps) {
  const { data: providers, isLoading: isProvidersLoading } =
    useServiceProviders(IntegrationServiceType.REAL_CALL_SCORING, true);

  const zoomProvider = useMemo(() => {
    return providers?.find?.((p) => p.companyName === 'Zoom');
  }, [providers]);
  const googleProvider = useMemo(() => {
    return providers?.find?.((p) => p.companyName === 'Google');
  }, [providers]);
  const microsoftProvider = useMemo(() => {
    return providers?.find?.((p) => p.companyName === 'Microsoft');
  }, [providers]);

  const { betaIntegrations, betaIntegrationsAllowed } = useUserSession();

  const {
    data: integrations,
    isLoading: isIntegrationsLoading,
    refetch: refetchIntegrations,
  } = useIntegrations(true);
  const zoomIntegration = useMemo(() => {
    return integrations?.find?.(
      (i) =>
        i.provider?.companyName === 'Zoom' &&
        i.type === IntegrationServiceType.REAL_CALL_SCORING,
    );
  }, [integrations]);
  const googleCalendarIntegration = useMemo(() => {
    return integrations?.find?.(
      (i) =>
        i.provider?.companyName === 'Google' &&
        i.type === IntegrationServiceType.CALENDAR,
    );
  }, [integrations]);
  const googleMeetIntegration = useMemo(() => {
    return integrations?.find?.(
      (i) =>
        i.provider?.companyName === 'Google' &&
        i.type === IntegrationServiceType.REAL_CALL_SCORING,
    );
  }, [integrations]);
  const microsoftTeamsIntegration = useMemo(() => {
    return integrations?.find?.(
      (i) =>
        i.provider?.companyName === 'Microsoft' &&
        i.type === IntegrationServiceType.REAL_CALL_SCORING,
    );
  }, [integrations]);

  const videoPlatformsEnabled = useMemo(() => {
    const res = {
      zoom: true,
      googleMeet: true,
      microsoftTeams: true,
    };
    if (
      betaIntegrations.has('google') &&
      !betaIntegrationsAllowed.has('google')
    ) {
      res.googleMeet = false;
    }
    if (
      betaIntegrations.has('microsoft') &&
      !betaIntegrationsAllowed.has('microsoft')
    ) {
      res.microsoftTeams = false;
    }
    return res;
  }, [betaIntegrations, betaIntegrationsAllowed]);

  const [zoomIntegrationName, setZoomIntegrationName] = useState('');
  const [googleCalendarIntegrationName, setGoogleCalendarIntegrationName] =
    useState('');
  const [googleMeetIntegrationName, setGoogleMeetIntegrationName] =
    useState('');
  const [microsoftTeamsIntegrationName, setMicrosoftTemasIntegrationName] =
    useState('');

  if (
    isProvidersLoading ||
    isIntegrationsLoading ||
    !zoomProvider ||
    !googleProvider
  ) {
    return <div />;
  }

  const onSaveInternal = async (
    serviceProviderOverride?: ServiceProvider,
    nameOverride?: string,
    integrationTypeOverride?: IntegrationServiceType,
    dontCloseAutomatically?: boolean,
  ) => {
    await saveIntegration(
      serviceProviderOverride,
      nameOverride,
      integrationTypeOverride,
      dontCloseAutomatically,
      async () => {
        await refetchIntegrations();
      },
    );
  };

  return (
    <div className="h-full relative">
      <div className="absolute left-0 right-0 top-0 bottom-0 overflow-y-auto">
        <div className="flex flex-col items-start">
          <p className="text-sm">
            You need to setup Google Calendar integration so that the{' '}
            {hyperboundMeetingRecorderProviderName} can work. You can then setup
            any video platforms listed below for the{' '}
            {hyperboundMeetingRecorderProviderName}
            to join.
          </p>
          <p className="mr-1 mt-6">Google Calendar</p>
          {!googleCalendarIntegration && (
            <div className="mt-2">
              <div className="mb-2">
                <Label>Name</Label>
                <p className="text-sm">
                  This is just to identify this integration in other pages.
                </p>
                <div className="mt-2 flex items-center">
                  <Input
                    value={googleCalendarIntegrationName}
                    onChange={(e: any) => {
                      setGoogleCalendarIntegrationName(e.target.value);
                    }}
                  />
                </div>
              </div>
              {googleProvider?.configurationNotes && (
                <>
                  <Label>Notes</Label>
                  <div className="text-sm">
                    {googleProvider?.configurationNotes}
                  </div>
                </>
              )}
            </div>
          )}
          {googleCalendarIntegration ? (
            <Check className="text-green-500 w-4 h-4" />
          ) : (
            <Button
              className="mt-2"
              disabled={isSaving}
              onClick={() =>
                onSaveInternal(
                  googleProvider,
                  googleCalendarIntegrationName,
                  IntegrationServiceType.CALENDAR,
                  true,
                )
              }
            >
              Open
            </Button>
          )}
          {videoPlatformsEnabled.zoom && (
            <>
              <p className="mr-1 mt-4">Zoom</p>
              {!zoomIntegration && (
                <div className="mt-2">
                  <div className="mb-2">
                    <Label>Name</Label>
                    <p className="text-sm">
                      This is just to identify this integration in other pages.
                    </p>
                    <div className="mt-2 flex items-center">
                      <Input
                        value={zoomIntegrationName}
                        onChange={(e: any) => {
                          setZoomIntegrationName(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                  {zoomProvider?.configurationNotes && (
                    <>
                      <Label>Notes</Label>
                      <div className="text-sm">
                        {zoomProvider?.configurationNotes}
                      </div>
                    </>
                  )}
                </div>
              )}
              {zoomIntegration ? (
                <Check className="text-green-500" />
              ) : (
                <Button
                  className="mt-2"
                  disabled={isSaving || !googleCalendarIntegration}
                  onClick={() =>
                    onSaveInternal(
                      zoomProvider,
                      zoomIntegrationName,
                      undefined,
                      true,
                    )
                  }
                >
                  Open
                </Button>
              )}
            </>
          )}
          {videoPlatformsEnabled.googleMeet && (
            <>
              <p className="mr-1 mt-4">Google Meet</p>
              {!googleMeetIntegration && (
                <div className="mt-2">
                  <div className="mb-2">
                    <Label>Name</Label>
                    <p className="text-sm">
                      This is just to identify this integration in other pages.
                    </p>
                    <div className="mt-2 flex items-center">
                      <Input
                        value={googleMeetIntegrationName}
                        onChange={(e: any) => {
                          setGoogleMeetIntegrationName(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}
              {googleMeetIntegration ? (
                <Check className="text-green-500" />
              ) : (
                <Button
                  className="mt-2"
                  disabled={isSaving || !googleCalendarIntegration}
                  onClick={() =>
                    onSaveInternal(
                      googleProvider,
                      googleMeetIntegrationName,
                      IntegrationServiceType.REAL_CALL_SCORING,
                      true,
                    )
                  }
                >
                  Enable
                </Button>
              )}
            </>
          )}
          {videoPlatformsEnabled.microsoftTeams && (
            <>
              <p className="mr-1 mt-4">Microsoft Teams</p>
              {!microsoftTeamsIntegration && (
                <div className="mt-2">
                  <div className="mb-2">
                    <Label>Name</Label>
                    <p className="text-sm">
                      This is just to identify this integration in other pages.
                    </p>
                    <div className="mt-2 flex items-center">
                      <Input
                        value={microsoftTeamsIntegrationName}
                        onChange={(e: any) => {
                          setMicrosoftTemasIntegrationName(e.target.value);
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}
              {microsoftTeamsIntegration ? (
                <Check className="text-green-500" />
              ) : (
                <Button
                  className="mt-2"
                  disabled={isSaving || !googleCalendarIntegration}
                  onClick={() =>
                    onSaveInternal(
                      microsoftProvider,
                      microsoftTeamsIntegrationName,
                      IntegrationServiceType.REAL_CALL_SCORING,
                      true,
                    )
                  }
                >
                  Enable
                </Button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
