import UploadTranscriptDnd from '@/common/Calls/Real/List/Import/UploadTranscriptModal/upload';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertCircleIcon,
  XIcon,
  Loader2Icon,
  CheckCircle2,
  XCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import RepsFilter from '@/common/Calls/AIRoleplay/List/filtersControls/RepsFilter';
import RealCallsService from '@/lib/Integrations/RealCalls';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { useEffect } from 'react';
import useOrgUsersByIds from '@/hooks/useOrgUsersByIds';
import { Input } from '@/components/ui/input';

interface IProps {
  open: boolean;
  onClose: () => void;
}

export default function TestRoutingModal({ open, onClose }: IProps) {
  const [title, setTitle] = useState<string>('');
  const [transcript, setTranscript] = useState<any>(null);
  const [participant, setParticipant] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<any>(null);

  const { data: participantDetails } = useOrgUsersByIds(participant);

  const participantName = participantDetails?.[0]
    ? `${participantDetails[0].firstName} ${participantDetails[0].lastName}`.trim()
    : '';

  async function test() {
    if (loading) return;
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const res = await RealCallsService.testRouting(
        title,
        transcript,
        participant[0],
      );
      setResult(res);
    } catch (error) {
      console.error(error);
      setError('Error running test. Please try again later.');
    }

    setLoading(false);
  }

  useEffect(() => {
    const storedTranscript = localStorage.getItem('transcript');
    if (storedTranscript) {
      try {
        setTranscript(JSON.parse(storedTranscript));
      } catch (err) {
        console.error(err);
        setTranscript(storedTranscript);
      }
    }
  }, []);

  useEffect(() => {
    if (transcript === null) {
      localStorage.removeItem('test-routing-modal-transcript');
    } else {
      const transcriptValue =
        typeof transcript === 'string'
          ? transcript
          : JSON.stringify(transcript);

      try {
        localStorage.setItem('test-routing-modal-transcript', transcriptValue);
      } catch (error) {
        console.warn('Failed to save transcript to localStorage:', error);
      }
    }
  }, [transcript]);

  function TestResult({
    loading,
    result,
    error,
  }: {
    loading: boolean;
    result: any;
    error: any;
  }) {
    const renderContent = () => {
      if (loading) {
        return (
          <div className="text-xs font-medium flex flex-row items-center justify-start text-muted-foreground">
            <Loader2Icon className="w-3 h-3 mr-2 inline-block animate-spin" />
            <span>Running test...</span>
          </div>
        );
      } else if (result && result.categoryId) {
        return (
          <div>
            <div className="flex flex-row items-center justify-between mb-4">
              <div className="flex flex-row items-center justify-start">
                <CheckCircle2 className="w-6 h-6 mr-2 text-[#28a0a0]" />
                <p className="font-medium">Matches an existing call type</p>
              </div>
            </div>

            <p className="text-sm mb-4">{result.explanation}</p>

            <div className="flex flex-row items-center justify-between mb-4">
              <>Call Type:</>

              <span className="font-medium">{result.categoryLabel}</span>
            </div>

            <div className="flex flex-row items-center justify-between mb-4">
              <>Variation of:</>

              <div className="flex flex-row items-center justify-start text-xs">
                <div className="flex flex-row items-center justify-center">
                  {CALL_TYPE_OPTIONS?.map((option) => {
                    if (option.value == result.callType) {
                      const Icon = option.Icon;
                      return (
                        <div
                          className="flex items-center bg-gray-400/80 border border-gray-400 px-2 py-1 rounded-full text-white"
                          key={option.value}
                        >
                          <Icon className="mr-1" size={14} />
                          {option.label}
                        </div>
                      );
                    }
                  })}
                </div>
              </div>
            </div>

            <div className="flex flex-row items-center justify-between mb-4">
              <>Behavior:</>

              <span className="font-medium">
                {result.behavior
                  .split('_')
                  .map(
                    (word: string) =>
                      word.charAt(0).toUpperCase() +
                      word.slice(1).toLowerCase(),
                  )
                  .join(' ')}
              </span>
            </div>

            <div className="flex flex-row items-center justify-between">
              <>Access Type:</>

              <span className="font-medium">
                {result.categoryType.charAt(0).toUpperCase() +
                  result.categoryType.slice(1).toLowerCase()}
              </span>
            </div>
          </div>
        );
      } else if (result && result.explanation) {
        return (
          <div>
            <div className="flex flex-row items-center justify-start mb-4">
              <XCircle className="w-6 h-6 mr-2 text-red-500" />
              <p className="font-medium">No match found</p>
            </div>

            <p className="text-sm">{result.explanation}</p>
          </div>
        );
      } else if (error) {
        return (
          <div className="text-xs font-medium flex flex-row items-center justify-start text-red-500">
            <AlertCircleIcon className="w-3 h-3 mr-2 inline-block" />
            <span>{error}</span>
          </div>
        );
      }
      return null;
    };

    return (
      <div
        className={cn(
          'p-4 border rounded-lg',
          result && result.categoryId
            ? 'bg-[#9ce0f2]/10 border-[#9ce0f2]/50'
            : 'bg-red-500/10 border-red-500/50',
        )}
      >
        {renderContent()}
      </div>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={cn('close-btn mt-16 flex flex-col')}>
        <DialogHeader>
          <DialogTitle className="flex items-center">Test Routing</DialogTitle>
        </DialogHeader>

        {!result && !error && (
          <div className="mb-4">
            <p className="text-sm text-muted-foreground mb-6">
              Select a caller and then upload a transcript to test the routing.
            </p>

            <Input
              className="mb-2"
              placeholder="Call title (optional)"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />

            <RepsFilter
              maxResults={1}
              label="Caller"
              current={participant}
              onRepsUpdated={(ns: string[]) => {
                setParticipant(ns.map(Number));
              }}
            />

            <div className="mt-2">
              {!transcript && (
                <UploadTranscriptDnd
                  onError={() => {}}
                  clearError={() => {}}
                  onUpload={(transcript: any, plainText: string) => {
                    setTranscript(transcript);
                  }}
                  repName={participantName}
                />
              )}

              {transcript && (
                <div className="text-xs font-medium flex flex-row items-center justify-start text-muted-foreground p-2 bg-gray-100 rounded-md">
                  <XIcon
                    className="w-4 h-4 mr-1 inline-block cursor-pointer"
                    onClick={() => {
                      setTranscript(null);
                    }}
                  />
                  <span>Transcript has been uploaded</span>
                </div>
              )}
            </div>
          </div>
        )}
        {(result || error) && (
          <TestResult loading={loading} result={result} error={error} />
        )}

        <DialogFooter>
          <div className="flex flex-row items-center justify-end w-full gap-2">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>

            {!result && (
              <Button
                onClick={test}
                disabled={loading || !transcript || participant.length === 0}
                className={cn(loading && 'animate-pulse')}
              >
                {loading ? (
                  <div className="flex flex-row items-center">
                    <Loader2Icon className="animate-spin mr-2" />
                    Running test...
                  </div>
                ) : (
                  'Run Test'
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
