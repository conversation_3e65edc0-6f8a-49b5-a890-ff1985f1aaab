import UploadTranscriptDnd from '@/common/Calls/Real/List/Import/UploadTranscriptModal/upload';
import { Button } from '@/components/ui/button';
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertCircleIcon,
  XIcon,
  Loader2Icon,
  SparklesIcon,
  CopyIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import RealCallsService from '@/lib/Integrations/RealCalls';
import { useEffect } from 'react';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import {
  Select,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import { AgentCallType } from '@/lib/Agent/types';

interface IProps {
  open: boolean;
  onClose: () => void;
}

export default function GenerateDescriptionsModal({ open, onClose }: IProps) {
  const [transcript, setTranscript] = useState<any>(null);
  const [callType, setCallType] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{ [key: string]: string } | null>(null);
  const [error, setError] = useState<any>(null);

  async function generateDescriptions() {
    if (loading) return;
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const res = await RealCallsService.generateDescriptions({
        [callType as string]: transcript,
      });
      setResult(res);
    } catch (error) {
      setError('Error running test. Please try again later.');
    }

    setLoading(false);
  }

  useEffect(() => {
    const storedTranscript = localStorage.getItem('transcript');
    if (storedTranscript) {
      try {
        setTranscript(JSON.parse(storedTranscript));
      } catch (err) {
        setTranscript(storedTranscript);
      }
    }
  }, []);

  useEffect(() => {
    if (transcript === null) {
      localStorage.removeItem('generate-descriptions-modal-transcript');
    } else {
      const transcriptValue =
        typeof transcript === 'string'
          ? transcript
          : JSON.stringify(transcript);

      try {
        localStorage.setItem(
          'generate-descriptions-modal-transcript',
          transcriptValue,
        );
      } catch (error) {
        console.warn('Failed to save transcript to localStorage:', error);
      }
    }
  }, [transcript]);

  function GenerationResult({
    loading,
    result,
    error,
  }: {
    loading: boolean;
    result: any;
    error: any;
  }) {
    const renderContent = () => {
      if (loading) {
        return (
          <div className="text-xs font-medium flex flex-row items-center justify-start text-muted-foreground">
            <Loader2Icon className="w-3 h-3 mr-2 inline-block animate-spin" />
            <span>Generating descriptions...</span>
          </div>
        );
      } else if (result) {
        return (
          <div>
            <div className="flex flex-row items-center justify-between mb-4">
              <div className="flex flex-row items-center justify-start">
                <SparklesIcon className="w-6 h-6 mr-2 text-[#28a0a0]" />
                <p className="font-medium">Description</p>
              </div>

              <CopyIcon
                className="w-4 h-4 mr-2 cursor-pointer"
                onClick={() => {
                  navigator.clipboard.writeText(result[callType as string]);
                }}
              />
            </div>

            {Object.entries(result).map(([key, value]) => (
              <div key={key}>
                <p className="text-sm mb-4">{value as string}</p>
              </div>
            ))}
          </div>
        );
      } else if (error) {
        return (
          <div className="text-xs font-medium flex flex-row items-center justify-start text-red-500">
            <AlertCircleIcon className="w-3 h-3 mr-2 inline-block" />
            <span>{error}</span>
          </div>
        );
      }
      return null;
    };
    return (
      <div className="border border-[#9ce0f2] rounded-lg p-4 bg-[#9ce0f2]/10">
        {renderContent()}
      </div>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={cn('close-btn mt-16 flex flex-col')}>
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Generate Descriptions
          </DialogTitle>
        </DialogHeader>

        {!result && !error && (
          <>
            <p className="text-sm text-muted-foreground mb-4">
              Select a call type and then upload a transcript to generate
              fitting descriptions for a custom call type.
            </p>
            <Select
              onValueChange={(value: string) => {
                setCallType(value as AgentCallType);
              }}
              value={callType}
            >
              <SelectTrigger className="bg-white">
                <SelectValue placeholder="Select call type..." />
              </SelectTrigger>
              <SelectContent>
                {CALL_TYPE_OPTIONS?.map((option) => {
                  const Icon = option.Icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center">
                        <Icon className="mr-2" size={16} />
                        {option.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>

            <div className="mb-4">
              {!transcript && (
                <UploadTranscriptDnd
                  onError={() => {}}
                  clearError={() => {}}
                  onUpload={(transcript: any, plainText: string) => {
                    setTranscript(transcript);
                  }}
                />
              )}

              {transcript && (
                <div className="text-xs font-medium flex flex-row items-center justify-start text-muted-foreground p-2 bg-gray-100 rounded-md">
                  <XIcon
                    className="w-4 h-4 mr-1 inline-block cursor-pointer"
                    onClick={() => {
                      setTranscript(null);
                    }}
                  />
                  <span>Transcript has been uploaded</span>
                </div>
              )}
            </div>
          </>
        )}

        {(result || error) && (
          <GenerationResult loading={loading} result={result} error={error} />
        )}

        <DialogFooter>
          <div className="flex flex-row items-center justify-end w-full gap-2">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>

            {!result && (
              <Button
                onClick={generateDescriptions}
                disabled={loading || !transcript || !callType}
                className={cn(loading && 'animate-pulse')}
              >
                {loading ? (
                  <div className="flex flex-row items-center justify-center">
                    <Loader2Icon className="animate-spin mr-2" />
                    Generating...
                  </div>
                ) : (
                  <div className="flex flex-row items-center justify-center">
                    Generate
                  </div>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
