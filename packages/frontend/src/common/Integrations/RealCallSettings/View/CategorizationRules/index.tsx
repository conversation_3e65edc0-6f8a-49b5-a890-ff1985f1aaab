import {
  AllCategorizationRules,
  RealCallCategorizationRuleBehavior,
  RealCallOrganizationCategorizationRule,
  RealCallTeamCategorizationRule,
  RealCallUserCategorizationRule,
} from '@/lib/Integrations/RealCalls/types';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { useCallback, useEffect, useState } from 'react';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { RuleCategory } from '../..';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface IProps {
  categorizationRules: AllCategorizationRules;
  show: RuleCategory;
}

export default function ViewCategorizationRules({
  categorizationRules,
  show,
}: IProps) {
  const [rules, setRules] = useState<any[]>([]);

  useEffect(() => {
    let _rules:
      | RealCallOrganizationCategorizationRule[]
      | RealCallTeamCategorizationRule[]
      | RealCallUserCategorizationRule[] = [];

    if (show === RuleCategory.ORGANIZATION) {
      _rules = categorizationRules.organization;
    } else if (show === RuleCategory.TEAMS) {
      _rules = categorizationRules.teams;
    } else if (show === RuleCategory.USERS) {
      _rules = categorizationRules.users;
    }

    setRules(_rules);
  }, [categorizationRules]);

  /**********************************/
  /*********** RENDERING ************/
  /**********************************/

  const renderRow = useCallback(
    (rule: any) => {
      let bg = '';
      if (rule.id == undefined && !rule.isSaving) {
        bg = 'bg-gray-100';
      }

      return (
        <TableRow className={bg}>
          {show === RuleCategory.TEAMS && (
            <TableCell className="w-[20%]">{rule.team.name}</TableCell>
          )}
          {show === RuleCategory.USERS && (
            <TableCell className="w-[20%]">
              <div className="flex space-x-2 items-center">
                <Avatar className="w-6 h-6">
                  {rule.user?.avatar && (
                    <AvatarImage src={rule.user.avatar} alt="Avatar" />
                  )}
                  <AvatarFallback className="text-sm">
                    {rule.user?.firstName?.charAt(0) || ''}
                    {rule.user?.lastName?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
                <div className="capitalize">
                  {rule.user?.firstName || ''} {rule.user?.lastName || ''}
                </div>
              </div>
            </TableCell>
          )}
          <TableCell>{rule.label}</TableCell>
          <TableCell className="text-wrap">{rule.explanation}</TableCell>
          <TableCell className="w-[30%]">
            <div className="inline-flex text-muted-foreground text-xs py-1 px-2 rounded-full bg-gray-400">
              {CALL_TYPE_OPTIONS?.map((option) => {
                if (option.value == rule.callType) {
                  const Icon = option.Icon;
                  return (
                    <div
                      className="flex items-center text-white"
                      key={option.value}
                    >
                      <Icon className="mr-1" size={14} />
                      {option.label}
                    </div>
                  );
                }
              })}
            </div>
            <div className="mt-2">
              {rule.behavior == RealCallCategorizationRuleBehavior.SAVE_ONLY
                ? 'Save only'
                : 'Save and score'}
            </div>
            {rule.behavior ==
              RealCallCategorizationRuleBehavior.SAVE_AND_SCORE && (
              <div>
                <div className="text-muted-foreground text-xs mt-2">
                  Scorecard
                </div>
                <div className="flex items-center">
                  <div>{rule.scorecardConfig.tag}</div>
                </div>
              </div>
            )}
          </TableCell>
          <TableCell className="w-[80px]">&nbsp;</TableCell>
        </TableRow>
      );
    },
    [rules],
  );

  return (
    <Table hideFooter={true} fitToContent={true}>
      <TableContent>
        <TableRow className="sticky top-0 z-50">
          {show === RuleCategory.TEAMS && (
            <TableCellHead className="w-[20%]">Team</TableCellHead>
          )}
          {show === RuleCategory.USERS && (
            <TableCellHead className="w-[20%]">User</TableCellHead>
          )}
          <TableCellHead>Custom Call Type</TableCellHead>
          <TableCellHead>Description</TableCellHead>
          <TableCellHead className="w-[30%]">Behavior</TableCellHead>
          <TableCellHead className="w-[80px]">&nbsp;</TableCellHead>
        </TableRow>
        {rules.map((rule: any) => {
          return renderRow(rule);
        })}
      </TableContent>
      <TableFooter className="hidden">&nbsp;</TableFooter>
    </Table>
  );
}
