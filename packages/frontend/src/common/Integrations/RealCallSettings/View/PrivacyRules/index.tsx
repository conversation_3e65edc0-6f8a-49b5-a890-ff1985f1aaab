import {
  AllPrivacyRules,
  PrivacyRuleSettingType,
  RealCallOrganizationPrivacyRule,
  RealCallTeamPrivacyRule,
  RealCallUserPrivacyRule,
} from '@/lib/Integrations/RealCalls/types';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { useEffect, useState } from 'react';
import useOrgUsers from '@/hooks/useOrgUsers';
import { UserDto } from '@/lib/User/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import { RuleCategory } from '../..';
import { Input } from '@/components/ui/input';

interface IProps {
  privacyRules: AllPrivacyRules;
  show: RuleCategory;
}

export default function ViewPrivacyRules({ privacyRules, show }: IProps) {
  const [rules, setRules] = useState<any[]>([]);

  useEffect(() => {
    let _rules:
      | RealCallOrganizationPrivacyRule[]
      | RealCallTeamPrivacyRule[]
      | RealCallUserPrivacyRule[] = [];

    if (show === RuleCategory.ORGANIZATION) {
      _rules = privacyRules.organization;
    } else if (show === RuleCategory.TEAMS) {
      _rules = privacyRules.teams;
    } else if (show === RuleCategory.USERS) {
      _rules = privacyRules.users;
    }

    setRules(_rules);
  }, [privacyRules]);

  const { data: allUsers, isLoading: isLoadingUsers } = useOrgUsers(
    true,
    0,
    1000,
    '',
  );

  /**********************************/
  /*********** RENDERING ************/
  /**********************************/

  const renderRow = (rule: any) => {
    let bg = '';
    if (rule.id == undefined && !rule.isSaving) {
      bg = 'bg-gray-100';
    }

    const settings = rule.settings;
    const callTitleSettings = settings[PrivacyRuleSettingType.CALL_TITLE] || '';
    const partecipantsSettings =
      settings[PrivacyRuleSettingType.PARTECIPANTS] || [];
    const minDurationSettings =
      settings[PrivacyRuleSettingType.MIN_DURATION] || undefined;
    const maxDurationSettings =
      settings[PrivacyRuleSettingType.MAX_DURATION] || undefined;
    const saveCallSettings =
      settings[PrivacyRuleSettingType.SAVE_CALL] || false;

    return (
      <TableRow className={bg}>
        {show === RuleCategory.TEAMS && (
          <TableCell className="w-[20%]">{rule.team.name}</TableCell>
        )}
        {show === RuleCategory.USERS && (
          <TableCell className="w-[20%]">
            <div className="flex space-x-2 items-center">
              <Avatar className="w-6 h-6">
                {rule.user?.avatar && (
                  <AvatarImage src={rule.user.avatar} alt="Avatar" />
                )}
                <AvatarFallback className="text-sm">
                  {rule.user?.firstName?.charAt(0) || ''}
                  {rule.user?.lastName?.charAt(0) || ''}
                </AvatarFallback>
              </Avatar>
              <div className="capitalize">
                {rule.user?.firstName || ''} {rule.user?.lastName || ''}
              </div>
            </div>
          </TableCell>
        )}
        <TableCell className="w-[40%]">{callTitleSettings}</TableCell>
        <TableCell className="w-[40%] flex items-center">
          {partecipantsSettings.map((partecipant: number) => {
            return allUsers?.data?.map((user: UserDto) => {
              if (user.id === partecipant) {
                return (
                  <div
                    className="flex space-x-2 items-center mr-2"
                    key={user.id}
                  >
                    <Avatar className="w-6 h-6">
                      {user.avatar && (
                        <AvatarImage src={user.avatar} alt="Avatar" />
                      )}
                      <AvatarFallback className="text-sm">
                        {user.firstName?.charAt(0) || ''}
                        {user.lastName?.charAt(0) || ''}
                      </AvatarFallback>
                    </Avatar>
                    <div className="capitalize">
                      {user.firstName || ''} {user.lastName || ''}
                    </div>
                  </div>
                );
              }
            });
          })}
        </TableCell>
        <TableCell>
            <p className="text-xs text-muted-foreground mb-[2px]">
              Min. duration
            </p>
            <p className="mb-2">{minDurationSettings ?? 'undefined'}</p>
            <p className="text-xs text-muted-foreground mb-[2px]">
              Max. duration
            </p>
            <p className="mb-2">{maxDurationSettings ?? 'undefined'}</p>
        </TableCell>
        <TableCell>
          <p className="mb-2">{saveCallSettings ? 'Yes' : 'No'}</p>
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Table hideFooter={true} fitToContent={true}>
      <TableContent>
        <TableRow className="sticky top-0 z-50">
          {show === RuleCategory.TEAMS && (
            <TableCellHead className="w-[20%]">Team</TableCellHead>
          )}
          {show === RuleCategory.USERS && (
            <TableCellHead className="w-[20%]">User</TableCellHead>
          )}
          <TableCellHead>Call Title</TableCellHead>
          <TableCellHead>Participants</TableCellHead>
          <TableCellHead>
            <TooltipProvider> 
              <Tooltip>
                <TooltipTrigger className="cursor-help underline decoration-dotted">
                  Call Duration
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Calls shorter than the minimum or longer than the maximum
                    duration will not be scored based on this rule.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TableCellHead>
          <TableCellHead>
            <TooltipProvider> 
              <Tooltip>
                <TooltipTrigger className="cursor-help underline decoration-dotted">
                  Save call
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    If enabled, the call will still be saved but not scored.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TableCellHead>
          <TableCellHead className="w-[80px]">&nbsp;</TableCellHead>
        </TableRow>
        {rules.map((rule: any) => {
          return renderRow(rule);
        })}
      </TableContent>
      <TableFooter className="hidden">&nbsp;</TableFooter>
    </Table>
  );
}
