import {
  AllCategorizationRules,
  AllPrivacyRules,
  RealCallOrganizationCategorizationRule,
} from '@/lib/Integrations/RealCalls/types';
import { useQueryClient } from '@tanstack/react-query';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { RuleCategory } from '..';
import { Building, UserRound, UsersRound } from 'lucide-react';
import ViewCategorizationRules from './CategorizationRules';
import ViewPrivacyRules from './PrivacyRules';

interface IProps {
  categorizationRules: AllCategorizationRules;
  privacyRules: AllPrivacyRules;
}

export default function ViewSettings({
  categorizationRules,
  privacyRules,
}: IProps) {
  const [show, setShow] = useState<string>('categorizationRules');
  const [defaultAccordingLevel, setDefaultAccordingLevel] =
    useState<RuleCategory>(RuleCategory.ORGANIZATION);

  return (
    <div>
      <div className="flex items-center">
        <div className="font-semibold mb-2 text-base flex-1">
          Real call settings
        </div>
        <div>
          <Tabs defaultValue="categorizationRules" className="">
            <TabsList className={''}>
              <TabsTrigger
                value="categorizationRules"
                onClick={() => {
                  setShow('categorizationRules');
                  setDefaultAccordingLevel(RuleCategory.ORGANIZATION);
                }}
              >
                Call types &amp; Scorecards
              </TabsTrigger>
              <TabsTrigger
                value="privacyRules"
                onClick={() => {
                  setShow('privacyRules');
                  setDefaultAccordingLevel(RuleCategory.ORGANIZATION);
                }}
              >
                Privacy Settings
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="mt-4">
        <Accordion
          type="single"
          defaultValue={defaultAccordingLevel}
          collapsible
          className="mt-4"
        >
          <AccordionItem value={RuleCategory.ORGANIZATION}>
            <AccordionTrigger>
              <div className="flex items-center">
                <Building className="w-4 h-4 mr-2 text-muted-foreground" />
              </div>
              <div className="flex justify-between w-full mr-10">
                <div className="flex items-center space-x-8">
                  <h3 className="font-semibold w-48 text-left">Organization</h3>
                  <p className="text-muted-foreground">
                    Rules that apply to the entire organization
                  </p>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="p-4">
              <div>
                {show === 'categorizationRules' && (
                  <ViewCategorizationRules
                    categorizationRules={categorizationRules}
                    show={RuleCategory.ORGANIZATION}
                  />
                )}
                {show === 'privacyRules' && (
                  <ViewPrivacyRules
                    privacyRules={privacyRules}
                    show={RuleCategory.ORGANIZATION}
                  />
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value={RuleCategory.TEAMS}>
            <AccordionTrigger>
              <div className="flex items-center">
                <UsersRound className="w-4 h-4 mr-2 text-muted-foreground" />
              </div>
              <div className="flex justify-between w-full mr-10">
                <div className="flex items-center space-x-8">
                  <h3 className="font-semibold w-48 text-left">Teams</h3>
                  <p className="text-muted-foreground">
                    Rules that apply to specific teams
                  </p>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className=" p-4">
              <div>
                {show === 'categorizationRules' && (
                  <ViewCategorizationRules
                    categorizationRules={categorizationRules}
                    show={RuleCategory.TEAMS}
                  />
                )}
                {show === 'privacyRules' && (
                  <ViewPrivacyRules
                    privacyRules={privacyRules}
                    show={RuleCategory.TEAMS}
                  />
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value={RuleCategory.USERS}>
            <AccordionTrigger>
              <div className="flex items-center">
                <UserRound className="w-4 h-4 mr-2 text-muted-foreground" />
              </div>
              <div className="flex justify-between w-full mr-10">
                <div className="flex items-center space-x-8">
                  <h3 className="font-semibold w-48 text-left">Users</h3>
                  <p className="text-muted-foreground">
                    Rules that apply to specific users
                  </p>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="p-4">
              <div>
                {show === 'categorizationRules' && (
                  <ViewCategorizationRules
                    categorizationRules={categorizationRules}
                    show={RuleCategory.USERS}
                  />
                )}
                {show === 'privacyRules' && (
                  <ViewPrivacyRules
                    privacyRules={privacyRules}
                    show={RuleCategory.USERS}
                  />
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
