import {
  AllBotSchedulingRules,
  AllCategorizationRules,
  AllPrivacyRules,
} from '@/lib/Integrations/RealCalls/types';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { RuleCategory } from '..';
import { Building, SparklesIcon, UserRound, UsersRound } from 'lucide-react';
import ViewCategorizationRules from './CategorizationRules';
import ViewPrivacyRules from './PrivacyRules';
import ViewBotSchedulingRules from './BotSchedulingRules';
import { Button } from '@/components/ui/button';
import TestRoutingModal from './CategorizationRules/TestRoutingModal';
import GenerateDescriptionsModal from './CategorizationRules/GenerateDescriptionsModal';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  categorizationRules: AllCategorizationRules;
  privacyRules: AllPrivacyRules;
  botSchedulingRules: AllBotSchedulingRules;
}

export default function ViewSettings({
  categorizationRules,
  privacyRules,
  botSchedulingRules,
}: IProps) {
  const { isHyperboundMeetingRecorderAllowed } = useUserSession();
  const [show, setShow] = useState<string>('categorizationRules');
  const [defaultAccordingLevel, setDefaultAccordingLevel] =
    useState<RuleCategory>(RuleCategory.ORGANIZATION);
  const [openTestRoutingModal, setOpenTestRoutingModal] =
    useState<boolean>(false);
  const [openGenerateDescriptionsModal, setOpenGenerateDescriptionsModal] =
    useState<boolean>(false);

  return (
    <div>
      <div className="flex items-center">
        <div className="font-semibold mb-2 text-base flex-1">
          Routing Settings
        </div>
        <div>
          <Tabs defaultValue="categorizationRules" className="">
            <TabsList className={''}>
              <TabsTrigger
                value="categorizationRules"
                onClick={() => {
                  setShow('categorizationRules');
                  setDefaultAccordingLevel(RuleCategory.ORGANIZATION);
                }}
              >
                Routing Settings
              </TabsTrigger>
              <TabsTrigger
                value="privacyRules"
                onClick={() => {
                  setShow('privacyRules');
                  setDefaultAccordingLevel(RuleCategory.ORGANIZATION);
                }}
              >
                Privacy Settings
              </TabsTrigger>
              {isHyperboundMeetingRecorderAllowed && (
                <TabsTrigger
                  value="botSchedulingRules"
                  onClick={() => {
                    setShow('botSchedulingRules');
                    setDefaultAccordingLevel(RuleCategory.ORGANIZATION);
                  }}
                >
                  Bot Scheduling
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="mt-4">
        {show === 'categorizationRules' && (
          <div className="flex flex-row items-start gap-2">
            <Button
              className="font-semibold hover:opacity-90 duration-100 transition-opacity"
              style={{
                background:
                  'linear-gradient(180deg, #3DC3E6 0%, #49C8CF 33.33%, #36C4BF 98.96%)',
              }}
              onClick={() => {
                setOpenTestRoutingModal(true);
              }}
              variant={'default'}
            >
              <div className="flex items-center">
                <SparklesIcon className="mr-2 h-4 w-4" />
                Test Routing
              </div>
            </Button>

            <Button
              onClick={() => {
                setOpenGenerateDescriptionsModal(true);
              }}
            >
              Generate Descriptions
            </Button>
          </div>
        )}

        {openGenerateDescriptionsModal && (
          <GenerateDescriptionsModal
            key={'generate-descriptions'}
            open={openGenerateDescriptionsModal}
            onClose={() => {
              setOpenGenerateDescriptionsModal(false);
            }}
          />
        )}
        {openTestRoutingModal && (
          <TestRoutingModal
            key={'test-routing'}
            open={openTestRoutingModal}
            onClose={() => {
              setOpenTestRoutingModal(false);
            }}
          />
        )}

        <Accordion
          type="single"
          defaultValue={defaultAccordingLevel}
          collapsible
          className="mt-4"
        >
          <AccordionItem value={RuleCategory.ORGANIZATION}>
            <AccordionTrigger>
              <div className="flex items-center">
                <Building className="w-4 h-4 mr-2 text-muted-foreground" />
              </div>
              <div className="flex justify-between w-full mr-10">
                <div className="flex items-center space-x-8">
                  <h3 className="font-semibold w-48 text-left">Organization</h3>
                  <p className="text-muted-foreground">
                    Call types that apply to the entire organization
                  </p>
                </div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="p-4">
              <div>
                {show === 'categorizationRules' && (
                  <ViewCategorizationRules
                    categorizationRules={categorizationRules}
                    show={RuleCategory.ORGANIZATION}
                  />
                )}
                {show === 'privacyRules' && (
                  <ViewPrivacyRules
                    privacyRules={privacyRules}
                    show={RuleCategory.ORGANIZATION}
                  />
                )}
                {isHyperboundMeetingRecorderAllowed &&
                  show === 'botSchedulingRules' && (
                    <ViewBotSchedulingRules
                      botSchedulingRules={botSchedulingRules}
                      show={RuleCategory.ORGANIZATION}
                    />
                  )}
              </div>
            </AccordionContent>
          </AccordionItem>
          {show !== 'botSchedulingRules' && (
            <AccordionItem value={RuleCategory.TEAMS}>
              <AccordionTrigger>
                <div className="flex items-center">
                  <UsersRound className="w-4 h-4 mr-2 text-muted-foreground" />
                </div>
                <div className="flex justify-between w-full mr-10">
                  <div className="flex items-center space-x-8">
                    <h3 className="font-semibold w-48 text-left">Teams</h3>
                    <p className="text-muted-foreground">
                      Call types that apply to specific teams
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className=" p-4">
                <div>
                  {show === 'categorizationRules' && (
                    <ViewCategorizationRules
                      categorizationRules={categorizationRules}
                      show={RuleCategory.TEAMS}
                    />
                  )}
                  {show === 'privacyRules' && (
                    <ViewPrivacyRules
                      privacyRules={privacyRules}
                      show={RuleCategory.TEAMS}
                    />
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          )}
          {show !== 'botSchedulingRules' && (
            <AccordionItem value={RuleCategory.USERS}>
              <AccordionTrigger>
                <div className="flex items-center">
                  <UserRound className="w-4 h-4 mr-2 text-muted-foreground" />
                </div>
                <div className="flex justify-between w-full mr-10">
                  <div className="flex items-center space-x-8">
                    <h3 className="font-semibold w-48 text-left">Users</h3>
                    <p className="text-muted-foreground">
                      Call types that apply to specific users
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                <div>
                  {show === 'categorizationRules' && (
                    <ViewCategorizationRules
                      categorizationRules={categorizationRules}
                      show={RuleCategory.USERS}
                    />
                  )}
                  {show === 'privacyRules' && (
                    <ViewPrivacyRules
                      privacyRules={privacyRules}
                      show={RuleCategory.USERS}
                    />
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          )}
        </Accordion>
      </div>
    </div>
  );
}
