import {
  AllBotSchedulingRules,
  AllCategorizationRules,
  AllPrivacyRules,
} from '@/lib/Integrations/RealCalls/types';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';
import CategorizationRules from './CategorizationRules';
import { RuleCategory } from '..';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Building, UserRound, UsersRound } from 'lucide-react';
import EditPrivacyRules from './PrivacyRules';
import EditBotSchedulingRules from './BotSchedulingRules';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  categorizationRules: AllCategorizationRules;
  privacyRules: AllPrivacyRules;
  botSchedulingRules: AllBotSchedulingRules;
}

export default function EditRealCallSettings({
  categorizationRules,
  privacyRules,
  botSchedulingRules,
}: IProps) {
  const { isHyperboundMeetingRecorderAllowed } = useUserSession();
  const [show, setShow] = useState<string>('categorizationRules');
  const [defaultAccordingLevel, setDefaultAccordingLevel] =
    useState<RuleCategory>(RuleCategory.ORGANIZATION);

  return (
    <div>
      <div className="flex items-center">
        <div className="font-semibold mb-2 text-base flex-1">
          Routing Settings
        </div>
        <div>
          <Tabs defaultValue="categorizationRules" className="">
            <TabsList className={''}>
              <TabsTrigger
                value="categorizationRules"
                onClick={() => {
                  setShow('categorizationRules');
                  setDefaultAccordingLevel(RuleCategory.ORGANIZATION);
                }}
              >
                Routing Settings
              </TabsTrigger>
              <TabsTrigger
                value="privacyRules"
                onClick={() => {
                  setShow('privacyRules');
                  setDefaultAccordingLevel(RuleCategory.ORGANIZATION);
                }}
              >
                Privacy Settings
              </TabsTrigger>
              {isHyperboundMeetingRecorderAllowed && (
                <TabsTrigger
                  value="botSchedulingRules"
                  onClick={() => {
                    setShow('botSchedulingRules');
                    setDefaultAccordingLevel(RuleCategory.ORGANIZATION);
                  }}
                >
                  Bot Scheduling
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="mt-4">
        {show === 'categorizationRules' && (
          <div className="bg-gray-100 rounded-lg p-4 ">
            <div className="font-semibold">Routing Settings</div>
            <div className="mt-4">
              Use the interface below to create custom call type labels and add
              a brief description defining the types of calls to include. Our AI
              will analyze call content and automatically assign the correct
              call type based on your description. Once set, you can select a
              scorecard to accurately evaluate and score the calls. If no
              scorecard is provided then calls will not be scored.
            </div>
            <div className="mt-4">
              Depending on your needs, call types can be set at an organization
              level and/or specific per team and/or user. For each call,
              Hyperbound will try to assign a call type starting at the user
              level (if present) moving up to the organization level.{' '}
            </div>
          </div>
        )}
        {show === 'privacyRules' && (
          <div className="bg-gray-100 rounded-lg p-4 ">
            <div className="font-semibold">Privacy Rules</div>
            <div className="mt-4">
              Use the interface below to prevent calls from being scored in
              Hyperbound. You can specify a word that needs to be present in the
              meeting&apos;s title and/or a list of participants to the
              meeting/call.
            </div>
            <div className="mt-4">
              Depending on your needs, rules can be set at an organization level
              and/or specific per team and/or user. For each call, Hyperbound
              will start parsing privacy rules at the user level (if present)
              moving up to the organization level. If any rule applies, the call
              will not be scored.
            </div>
          </div>
        )}

        <div className="mt-4">
          <Accordion
            type="single"
            defaultValue={defaultAccordingLevel}
            collapsible
            className="mt-4"
          >
            <AccordionItem value={RuleCategory.ORGANIZATION}>
              <AccordionTrigger>
                <div className="flex items-center">
                  <Building className="w-4 h-4 mr-2 text-muted-foreground" />
                </div>
                <div className="flex justify-between w-full mr-10">
                  <div className="flex items-center space-x-8">
                    <h3 className="font-semibold w-48 text-left">
                      Organization
                    </h3>
                    <p className="text-muted-foreground">
                      Call types that apply to the entire organization
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                <div>
                  {show === 'categorizationRules' && (
                    <CategorizationRules
                      categorizationRules={categorizationRules}
                      show={RuleCategory.ORGANIZATION}
                    />
                  )}
                  {show === 'privacyRules' && (
                    <EditPrivacyRules
                      privacyRules={privacyRules}
                      show={RuleCategory.ORGANIZATION}
                    />
                  )}
                  {isHyperboundMeetingRecorderAllowed &&
                    show === 'botSchedulingRules' && (
                      <EditBotSchedulingRules
                        botSchedulingRules={botSchedulingRules}
                        show={RuleCategory.ORGANIZATION}
                      />
                    )}
                </div>
              </AccordionContent>
            </AccordionItem>
            {show !== 'botSchedulingRules' && (
              <AccordionItem value={RuleCategory.TEAMS}>
                <AccordionTrigger>
                  <div className="flex items-center">
                    <UsersRound className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">Teams</h3>
                      <p className="text-muted-foreground">
                        Call types that apply to specific teams
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="p-4">
                  <div>
                    {show === 'categorizationRules' && (
                      <CategorizationRules
                        categorizationRules={categorizationRules}
                        show={RuleCategory.TEAMS}
                      />
                    )}
                    {show === 'privacyRules' && (
                      <EditPrivacyRules
                        privacyRules={privacyRules}
                        show={RuleCategory.TEAMS}
                      />
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
            {show !== 'botSchedulingRules' && (
              <AccordionItem value={RuleCategory.USERS}>
                <AccordionTrigger>
                  <div className="flex items-center">
                    <UserRound className="w-4 h-4 mr-2 text-muted-foreground" />
                  </div>
                  <div className="flex justify-between w-full mr-10">
                    <div className="flex items-center space-x-8">
                      <h3 className="font-semibold w-48 text-left">Users</h3>
                      <p className="text-muted-foreground">
                        Call types that apply to specific users
                      </p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="p-4">
                  <div>
                    {show === 'categorizationRules' && (
                      <CategorizationRules
                        categorizationRules={categorizationRules}
                        show={RuleCategory.USERS}
                      />
                    )}
                    {show === 'privacyRules' && (
                      <EditPrivacyRules
                        privacyRules={privacyRules}
                        show={RuleCategory.USERS}
                      />
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
          </Accordion>
        </div>
      </div>
    </div>
  );
}
