import {
  AllPrivacyRules,
  MatchedCallBehavior,
  PrivacyRuleSettingType,
  RealCallOrganizationPrivacyRule,
  RealCallTeamPrivacyRule,
  RealCallUserPrivacyRule,
} from '@/lib/Integrations/RealCalls/types';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2Icon, PlusCircle, Trash2 } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RuleCategory } from '../..';
import useTeams from '@/hooks/useTeams';
import useOrgUsers from '@/hooks/useOrgUsers';
import { UserDto } from '@/lib/User/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useQueryClient } from '@tanstack/react-query';
import { RealCallsService } from '@/lib/Integrations';
import RepsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/RepsFilter';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
interface IProps {
  privacyRules: AllPrivacyRules;
  show: RuleCategory;
}

export default function EditPrivacyRules({ privacyRules, show }: IProps) {
  const queryClient = useQueryClient();
  const [rules, setRules] = useState<any[]>([]);

  useEffect(() => {
    let _rules:
      | RealCallOrganizationPrivacyRule[]
      | RealCallTeamPrivacyRule[]
      | RealCallUserPrivacyRule[] = [];

    if (show === RuleCategory.ORGANIZATION) {
      _rules = privacyRules.organization;
    } else if (show === RuleCategory.TEAMS) {
      _rules = privacyRules.teams;
    } else if (show === RuleCategory.USERS) {
      _rules = privacyRules.users;
    }

    setRules(_rules);
  }, [privacyRules]);

  const [rulesQueuedForSave, setRulesQueuedForSave] = useState<{
    [id: number | string]: any;
  }>({});
  const [rulesThaCanBeSaved, setRulesThaCanBeSaved] = useState<string[]>([]);

  const { data: allTeams, isLoading: isLoadingTeams } = useTeams(
    0,
    1000,
    '',
    true,
  );
  const { data: allUsers, isLoading: isLoadingUsers } = useOrgUsers(
    true,
    0,
    1000,
    '',
  );

  const [newRule, setNewRule] = useState<any>({
    ruleType: 'excludeCall',
    settings: {},
    teamId: 0,
    userId: 0,
    team: null,
    user: null,
    isSaving: false,
  });

  /**********************************/
  /******* NEW RULE EDITING *********/
  /**********************************/

  const verifyRuleForSave = (rule: any) => {
    let canBeSave = false;
    if (show === RuleCategory.ORGANIZATION) {
      if (
        rule.settings[PrivacyRuleSettingType.CALL_TITLE] ||
        rule.settings[PrivacyRuleSettingType.PARTECIPANTS] ||
        rule.settings[PrivacyRuleSettingType.MIN_DURATION] ||
        rule.settings[PrivacyRuleSettingType.MAX_DURATION] ||
        rule.settings[PrivacyRuleSettingType.MATCHED_CALL_BEHAVIOR]
      ) {
        canBeSave = true;
      }
    } else if (show === RuleCategory.TEAMS) {
      if (
        rule.teamId &&
        (rule.settings[PrivacyRuleSettingType.CALL_TITLE] ||
          rule.settings[PrivacyRuleSettingType.PARTECIPANTS] ||
          rule.settings[PrivacyRuleSettingType.MIN_DURATION] ||
          rule.settings[PrivacyRuleSettingType.MAX_DURATION] ||
          rule.settings[PrivacyRuleSettingType.MATCHED_CALL_BEHAVIOR])
      ) {
        canBeSave = true;
      }
    } else if (show === RuleCategory.USERS) {
      if (
        rule.userId &&
        (rule.settings[PrivacyRuleSettingType.CALL_TITLE] ||
          rule.settings[PrivacyRuleSettingType.PARTECIPANTS] ||
          rule.settings[PrivacyRuleSettingType.MIN_DURATION] ||
          rule.settings[PrivacyRuleSettingType.MAX_DURATION] ||
          rule.settings[PrivacyRuleSettingType.MATCHED_CALL_BEHAVIOR])
      ) {
        canBeSave = true;
      }
    }

    if (canBeSave) {
      const idRule = rule.id ? String(rule.id) : 'new';
      setRulesThaCanBeSaved((rtbs: any) => {
        const tmp: any[] = [];
        let found = false;
        rtbs.map((rtb: any) => {
          if (rtb == idRule) {
            found = true;
          }
          tmp.push(rtb);
        });

        if (!found) {
          tmp.push(idRule);
        }

        return [...tmp];
      });
      setTimeout(() => {
        startAutosaveCounter();
      });
    } else {
      const idRule = rule.id ? String(rule.id) : 'new';
      setRulesThaCanBeSaved((rtbs: any) => {
        const tmp: any[] = [];
        rtbs.map((rtb: any) => {
          if (rtb !== idRule) {
            tmp.push(rtb);
          }
        });
        return [...tmp];
      });
    }
  };

  /********************************/
  /*********** AUTOSAVE ***********/
  /********************************/

  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  const saveFunction = useRef<Function | null>(null);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const startAutosaveCounter = useCallback(() => {
    cancelAutosaveCounter();
    const delay = 1600;

    timeoutRef.current = setTimeout(() => {
      if (saveFunction.current) {
        saveFunction.current();
      }
    }, delay);
  }, [rulesQueuedForSave, rulesThaCanBeSaved, rules]);

  const cancelAutosaveCounter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  /********************************/
  /*********** ACITONS ************/
  /********************************/

  const DeleteRule = (id: number) => {
    try {
      if (show === RuleCategory.ORGANIZATION) {
        RealCallsService.deletePrivacyRulePerOrganization(id);
      } else if (show === RuleCategory.TEAMS) {
        RealCallsService.deletePrivacyRulePerTeam(id);
      } else if (show === RuleCategory.USERS) {
        RealCallsService.deletePrivacyRulePerUser(id);
      }
    } catch (e) {
      console.log(e);
    }
    setTimeout(() => {
      queryClient.invalidateQueries({ queryKey: ['real-calls-privacy-rules'] });
    }, 1000);
  };

  const Save = useCallback(() => {
    const addedRules: number[] = [];
    const newRulesThatCanBeSaved: any[] = [];
    let manageNew = false;
    for (const key of Object.keys(rulesQueuedForSave)) {
      const idRule = key == 'new' ? 'new' : parseInt(key);
      const ruleToSave: any = rulesQueuedForSave[idRule];
      if (rulesThaCanBeSaved.indexOf(key) > -1) {
        if (key === 'new') {
          manageNew = true;
        } else {
          addedRules.push(ruleToSave.id);
        }

        try {
          if (show === RuleCategory.ORGANIZATION) {
            RealCallsService.upsertPrivacyRulePerOrganization(
              ruleToSave.id,
              ruleToSave.ruleType,
              ruleToSave.settings,
            );
          } else if (show === RuleCategory.TEAMS) {
            RealCallsService.upsertPrivacyRulePerTeam(
              ruleToSave.id,
              ruleToSave.teamId,
              ruleToSave.ruleType,
              ruleToSave.settings,
            );
          } else if (show === RuleCategory.USERS) {
            RealCallsService.upsertPrivacyRulePerUser(
              ruleToSave.id,
              ruleToSave.userId,
              ruleToSave.ruleType,
              ruleToSave.settings,
            );
          }
        } catch (e) {
          console.log(e);
        }
      } else {
        newRulesThatCanBeSaved.push(key);
      }
    }

    const tmp: any[] = [];
    for (const r of rules) {
      if (addedRules.indexOf(r.id) > -1) {
        tmp.push({ ...rulesQueuedForSave[r.id], isSaving: true });
      } else {
        tmp.push(r);
      }
    }
    if (manageNew) {
      tmp.push({ ...rulesQueuedForSave['new'], isSaving: true });
      setNewRule({
        ruleType: 'excludeCall',
        settings: [],
        teamId: 0,
        userId: 0,
        team: null,
        user: null,
        isSaving: false,
      });
    }
    setRules([...tmp]);
    setRulesQueuedForSave({});
    setRulesThaCanBeSaved(newRulesThatCanBeSaved);

    setTimeout(() => {
      queryClient.invalidateQueries({ queryKey: ['real-calls-privacy-rules'] });
    }, 1000);
  }, [rulesQueuedForSave, rulesThaCanBeSaved, rules]);

  saveFunction.current = Save;

  const updateRuleInfo = (rule: any) => {
    if (rule.id) {
      setRulesQueuedForSave((rqfs: any) => {
        return { ...rqfs, [rule.id]: rule };
      });
      setRules((olr: any) => {
        return olr.map((r: any) => {
          if (r.id == rule.id) {
            return rule;
          }
          return r;
        });
      });
      verifyRuleForSave({ ...rule });
    } else {
      setRulesQueuedForSave((rqfs: any) => {
        return { ...rqfs, ['new']: rule };
      });
      setNewRule({ ...rule });
      verifyRuleForSave({ ...rule });
    }
  };

  /**********************************/
  /*********** RENDERING ************/
  /**********************************/

  const renderRow = useCallback(
    (rule: any) => {
      if (rulesQueuedForSave[rule.id]) {
        rule = rulesQueuedForSave[rule.id];
      }
      let bg = '';
      if (rule.id == undefined && !rule.isSaving) {
        bg = 'bg-gray-100';
      }

      const settings = rule.settings;
      const callTitleSettings =
        settings[PrivacyRuleSettingType.CALL_TITLE] || '';
      const partecipantsSettings =
        settings[PrivacyRuleSettingType.PARTECIPANTS] || [];
      const minDurationSettings =
        settings[PrivacyRuleSettingType.MIN_DURATION] || '';
      const maxDurationSettings =
        settings[PrivacyRuleSettingType.MAX_DURATION] || '';
      const matchedCallBehaviorSettings =
        settings[PrivacyRuleSettingType.MATCHED_CALL_BEHAVIOR] ||
        MatchedCallBehavior.DISCARD_CALL;

      return (
        <TableRow className={bg}>
          {show === RuleCategory.TEAMS && (
            <TableCell className="w-[20%]">
              <Select
                onValueChange={(value: string) => {
                  allTeams?.map((v) => {
                    if (v.id === parseInt(value)) {
                      updateRuleInfo({
                        ...rule,
                        teamId: parseInt(value),
                        team: v,
                      });
                    }
                  });
                }}
                value={String(rule.teamId)}
              >
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Select team..." />
                </SelectTrigger>
                <SelectContent>
                  {allTeams?.map((v) => {
                    return (
                      <SelectItem key={v.id} value={String(v.id)}>
                        {v.name}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </TableCell>
          )}
          {show === RuleCategory.USERS && (
            <TableCell className="w-[20%]">
              <Select
                onValueChange={(value: string) => {
                  allUsers?.data?.map((v: UserDto) => {
                    if (v.id === parseInt(value)) {
                      updateRuleInfo({
                        ...rule,
                        userId: parseInt(value),
                        user: v,
                      });
                    }
                  });
                }}
                value={String(rule.userId)}
              >
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Select team..." />
                </SelectTrigger>
                <SelectContent>
                  {allUsers?.data?.map((user: UserDto) => {
                    return (
                      <SelectItem key={user.id} value={String(user.id)}>
                        <div className="flex space-x-2 items-center">
                          <Avatar className="w-6 h-6">
                            {user?.avatar && (
                              <AvatarImage src={user.avatar} alt="Avatar" />
                            )}
                            <AvatarFallback className="text-sm">
                              {user?.firstName?.charAt(0) || ''}
                              {user?.lastName?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <div className="capitalize">
                            {user?.firstName || ''} {user?.lastName || ''}
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </TableCell>
          )}
          <TableCell className="w-[40%]">
            <Input
              className="bg-white"
              placeholder="Call title includes..."
              value={callTitleSettings}
              onChange={(e) => {
                updateRuleInfo({
                  ...rule,
                  settings: {
                    ...settings,
                    [PrivacyRuleSettingType.CALL_TITLE]: e.target.value,
                  },
                });
              }}
            />
          </TableCell>
          <TableCell className="w-[40%]">
            <RepsFilter
              className="bg-white"
              current={partecipantsSettings}
              onRepsUpdated={(ns: string[]) => {
                updateRuleInfo({
                  ...rule,
                  settings: {
                    ...settings,
                    [PrivacyRuleSettingType.PARTECIPANTS]: ns.map(
                      (repId: string) => {
                        return parseInt(repId);
                      },
                    ),
                  },
                });
              }}
            />
          </TableCell>
          <TableCell>
            <span className="text-xs text-muted-foreground mb-[2px]">
              Min. duration in seconds
            </span>
            <Input
              className="bg-white mb-1"
              type="number"
              placeholder="seconds"
              value={minDurationSettings}
              onChange={(e) => {
                updateRuleInfo({
                  ...rule,
                  settings: {
                    ...settings,
                    [PrivacyRuleSettingType.MIN_DURATION]: parseInt(
                      e.target.value,
                    ),
                  },
                });
              }}
            />
            <span className="text-xs text-muted-foreground mb-[2px]">
              Max. duration in seconds
            </span>
            <Input
              className="bg-white"
              type="number"
              placeholder="seconds"
              value={maxDurationSettings}
              onChange={(e) => {
                updateRuleInfo({
                  ...rule,
                  settings: {
                    ...settings,
                    [PrivacyRuleSettingType.MAX_DURATION]: parseInt(
                      e.target.value,
                    ),
                  },
                });
              }}
            />
          </TableCell>
          <TableCell>
            {rule.isSaving && (
              <Loader2Icon
                size={18}
                className="text-muted-foreground animate-spin"
              />
            )}
            {!rule.isSaving && rule.id == undefined && (
              <Button
                variant={'outline'}
                disabled={rulesThaCanBeSaved.indexOf('new') < 0}
                onClick={() => {
                  Save();
                }}
              >
                <PlusCircle size={16} />
              </Button>
            )}
            {!rule.isSaving && rule.id != undefined && (
              <Button
                variant={'outline'}
                onClick={() => {
                  DeleteRule(rule.id);
                }}
              >
                <Trash2 size={16} />
              </Button>
            )}
          </TableCell>
        </TableRow>
      );
    },
    [rulesQueuedForSave, rules],
  );

  return (
    <Table hideFooter={true} fitToContent={true}>
      <TableContent>
        <TableRow className="sticky top-0 z-50">
          {show === RuleCategory.TEAMS && (
            <TableCellHead className="w-[20%]">Team</TableCellHead>
          )}
          {show === RuleCategory.USERS && (
            <TableCellHead className="w-[20%]">User</TableCellHead>
          )}
          <TableCellHead>Call Title</TableCellHead>
          <TableCellHead>Participants</TableCellHead>
          <TableCellHead>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger className="cursor-help underline decoration-dotted">
                  Call Duration
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    Calls longer than the minimum and shorter than the maximum
                    range will not be imported.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TableCellHead>
          <TableCellHead className="w-[80px]">&nbsp;</TableCellHead>
        </TableRow>
        {rules.map((rule: any) => {
          return renderRow(rule);
        })}
        {renderRow(newRule)}
      </TableContent>
      <TableFooter className="hidden">&nbsp;</TableFooter>
    </Table>
  );
}
