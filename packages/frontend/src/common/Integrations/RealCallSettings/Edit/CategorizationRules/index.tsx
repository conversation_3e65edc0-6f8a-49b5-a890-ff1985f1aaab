import {
  AllCategorizationRules,
  RealCallCategorizationRule,
  RealCallOrganizationCategorizationRule,
  RealCallTeamCategorizationRule,
  RealCallUserCategorizationRule,
} from '@/lib/Integrations/RealCalls/types';
import Table, {
  TableRow,
  TableCell,
  TableCellHead,
  TableFooter,
  TableContent,
} from '@/components/ui/Hyperbound/table';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Loader2Icon, PlusCircle, Trash2 } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { ScorecardConfigCallType } from '@/lib/ScorecardConfig/types';
import { RuleCategory } from '../..';
import useTeams from '@/hooks/useTeams';
import useOrgUsers from '@/hooks/useOrgUsers';
import { UserDto } from '@/lib/User/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useQueryClient } from '@tanstack/react-query';
import { RealCallsService } from '@/lib/Integrations';
import { AgentCallType } from '@/lib/Agent/types';

interface IProps {
  categorizationRules: AllCategorizationRules;
  show: RuleCategory;
}

export default function CategorizationRules({
  categorizationRules,
  show,
}: IProps) {
  const queryClient = useQueryClient();
  const [rules, setRules] = useState<
    | RealCallOrganizationCategorizationRule[]
    | RealCallTeamCategorizationRule[]
    | RealCallUserCategorizationRule[]
  >([]);

  useEffect(() => {
    let _rules:
      | RealCallOrganizationCategorizationRule[]
      | RealCallTeamCategorizationRule[]
      | RealCallUserCategorizationRule[] = [];

    if (show === RuleCategory.ORGANIZATION) {
      _rules = categorizationRules.organization;
    } else if (show === RuleCategory.TEAMS) {
      _rules = categorizationRules.teams;
    } else if (show === RuleCategory.USERS) {
      _rules = categorizationRules.users;
    }

    setRules(_rules);
  }, [categorizationRules]);

  const [rulesQueuedForSave, setRulesQueuedForSave] = useState<{
    [id: number | string]: RealCallCategorizationRule;
  }>({});
  const [rulesThaCanBeSaved, setRulesThaCanBeSaved] = useState<string[]>([]);

  const { data: scorecardConfigOptions } = useScorecardConfigsForOrg();
  const { data: allTeams } = useTeams(0, 1000, '', true);
  const { data: allUsers } = useOrgUsers(true, 0, 1000, '');

  const [newRule, setNewRule] = useState<
    RealCallCategorizationRule & { isSaving?: boolean }
  >({
    label: '',
    explanation: '',
    callType: '' as AgentCallType,
    scorecardConfigId: 0,
    teamId: 0,
    userId: 0,
    team: undefined,
    user: undefined,
    isSaving: false,
  });

  /**********************************/
  /******* NEW RULE EDITING *********/
  /**********************************/

  const verifyRuleForSave = (rule: RealCallCategorizationRule) => {
    let canBeSave = false;
    if (show === RuleCategory.ORGANIZATION) {
      if (
        rule.label &&
        rule.explanation &&
        rule.callType &&
        rule.scorecardConfigId > 0
      ) {
        canBeSave = true;
      }
    } else if (show === RuleCategory.TEAMS) {
      if (
        (rule as unknown as RealCallTeamCategorizationRule).teamId &&
        rule.label &&
        rule.explanation &&
        rule.callType &&
        rule.scorecardConfigId > 0
      ) {
        canBeSave = true;
      }
    } else if (show === RuleCategory.USERS) {
      if (
        (rule as unknown as RealCallUserCategorizationRule).userId &&
        rule.label &&
        rule.explanation &&
        rule.callType &&
        rule.scorecardConfigId > 0
      ) {
        canBeSave = true;
      }
    }

    if (canBeSave) {
      const idRule = rule.id ? String(rule.id) : 'new';
      setRulesThaCanBeSaved((rtbs) => {
        const tmp: string[] = [];
        let found = false;
        rtbs.map((rtb) => {
          if (rtb == idRule) {
            found = true;
          }
          tmp.push(rtb);
        });

        if (!found) {
          tmp.push(idRule);
        }

        return [...tmp];
      });
      setTimeout(() => {
        startAutosaveCounter();
      });
    } else {
      const idRule = rule.id ? String(rule.id) : 'new';
      setRulesThaCanBeSaved((rtbs) => {
        const tmp: string[] = [];
        rtbs.map((rtb) => {
          if (rtb !== idRule) {
            tmp.push(rtb);
          }
        });
        return [...tmp];
      });
    }
  };

  /********************************/
  /*********** AUTOSAVE ***********/
  /********************************/

  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  const saveFunction = useRef<Function>();
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();

  const startAutosaveCounter = useCallback(() => {
    cancelAutosaveCounter();
    const delay = 1600;

    timeoutRef.current = setTimeout(() => {
      if (saveFunction.current) {
        saveFunction.current();
      }
    }, delay);
  }, [rulesQueuedForSave, rulesThaCanBeSaved, rules]);

  const cancelAutosaveCounter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  /********************************/
  /*********** ACITONS ************/
  /********************************/

  const Save = useCallback(() => {
    const addedRules: number[] = [];
    const newRulesThatCanBeSaved: string[] = [];
    let manageNew = false;
    for (const key of Object.keys(rulesQueuedForSave)) {
      const idRule = key == 'new' ? 'new' : parseInt(key);
      const ruleToSave = rulesQueuedForSave[idRule];
      if (rulesThaCanBeSaved.indexOf(key) > -1) {
        if (key === 'new') {
          manageNew = true;
        } else {
          addedRules.push(ruleToSave.id || 0);
        }

        try {
          if (show === RuleCategory.ORGANIZATION) {
            RealCallsService.upsertCategorizationRulePerOrganization(
              ruleToSave.id,
              ruleToSave.label,
              ruleToSave.explanation,
              ruleToSave.callType,
              ruleToSave.scorecardConfigId,
            );
          } else if (show === RuleCategory.TEAMS) {
            RealCallsService.upsertCategorizationRulePerTeam(
              ruleToSave.id,
              (ruleToSave as unknown as RealCallTeamCategorizationRule).teamId,
              ruleToSave.label,
              ruleToSave.explanation,
              ruleToSave.callType,
              ruleToSave.scorecardConfigId,
            );
          } else if (show === RuleCategory.USERS) {
            RealCallsService.upsertCategorizationRulePerUser(
              ruleToSave.id,
              (ruleToSave as unknown as RealCallUserCategorizationRule).userId,
              ruleToSave.label,
              ruleToSave.explanation,
              ruleToSave.callType,
              ruleToSave.scorecardConfigId,
            );
          }
        } catch (e) {
          console.log(e);
        }
      } else {
        newRulesThatCanBeSaved.push(key);
      }
    }

    const tmp: (RealCallCategorizationRule & { isSaving?: boolean })[] = [];
    for (const r of rules) {
      if (addedRules.indexOf(r.id || 0) > -1) {
        tmp.push({ ...rulesQueuedForSave[r.id || 0], isSaving: true });
      } else {
        tmp.push(r);
      }
    }
    if (manageNew) {
      tmp.push({ ...rulesQueuedForSave['new'], isSaving: true });
      setNewRule({
        label: '',
        explanation: '',
        callType: '' as AgentCallType,
        scorecardConfigId: 0,
        teamId: 0,
        userId: 0,
        team: undefined,
        user: undefined,
        isSaving: false,
      });
    }
    setRules([...tmp]);
    setRulesQueuedForSave({});
    setRulesThaCanBeSaved(newRulesThatCanBeSaved);

    setTimeout(() => {
      queryClient.invalidateQueries({
        queryKey: ['real-calls-categorization-rules'],
      });
    }, 1000);
  }, [rulesQueuedForSave, rulesThaCanBeSaved, rules]);

  saveFunction.current = Save;

  const updateRuleInfo = (rule: RealCallCategorizationRule) => {
    if (rule.id) {
      setRulesQueuedForSave((rqfs) => {
        return { ...rqfs, [rule.id || 0]: rule };
      });
      setRules((olr) => {
        return olr.map((r) => {
          if (r.id == rule.id) {
            return rule;
          }
          return r;
        });
      });
      verifyRuleForSave({ ...rule });
    } else {
      setRulesQueuedForSave((rqfs) => {
        return { ...rqfs, ['new']: rule };
      });
      setNewRule({ ...rule });
      verifyRuleForSave({ ...rule });
    }
  };

  const [ruleBeingDeleted, setRuleBeingDeleted] = useState<number | null>(null);

  const DeleteRule = async (id: number) => {
    setTimeout(() => {
      queryClient.invalidateQueries({
        queryKey: ['real-calls-categorization-rules'],
      });
    }, 1000);
    try {
      setRuleBeingDeleted(id);
      if (show === RuleCategory.ORGANIZATION) {
        await RealCallsService.deleteCategorizationRulePerOrganization(id);
      } else if (show === RuleCategory.TEAMS) {
        await RealCallsService.deleteCategorizationRulePerTeam(id);
      } else if (show === RuleCategory.USERS) {
        await RealCallsService.deleteCategorizationRulePerUser(id);
      }
    } catch (e) {
      console.log(e);
    }
    setRuleBeingDeleted(null);
  };

  /**********************************/
  /*********** RENDERING ************/
  /**********************************/

  const renderRow = useCallback(
    (rule: RealCallCategorizationRule & { isSaving?: boolean }) => {
      const isSaving = rule.isSaving || ruleBeingDeleted === rule.id;
      const ruleId = rule.id || 0;
      if (rulesQueuedForSave[ruleId]) {
        rule = rulesQueuedForSave[ruleId];
      }
      let bg = '';
      if (rule.id == undefined && !isSaving) {
        bg = 'bg-gray-100';
      }

      return (
        <TableRow className={bg}>
          {show === RuleCategory.TEAMS && (
            <TableCell className="w-[20%]">
              <Select
                onValueChange={(value: string) => {
                  allTeams?.map((v) => {
                    if (v.id === parseInt(value)) {
                      updateRuleInfo({
                        ...rule,
                        teamId: parseInt(value),
                        team: v,
                      });
                    }
                  });
                }}
                value={String(
                  (rule as unknown as RealCallTeamCategorizationRule).teamId,
                )}
              >
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Select team..." />
                </SelectTrigger>
                <SelectContent>
                  {allTeams?.map((v) => {
                    return (
                      <SelectItem key={v.id} value={String(v.id)}>
                        {v.name}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </TableCell>
          )}
          {show === RuleCategory.USERS && (
            <TableCell className="w-[20%]">
              <Select
                onValueChange={(value: string) => {
                  allUsers?.data?.map((v: UserDto) => {
                    if (v.id === parseInt(value)) {
                      updateRuleInfo({
                        ...rule,
                        userId: parseInt(value),
                        user: v,
                      });
                    }
                  });
                }}
                value={String(
                  (rule as unknown as RealCallUserCategorizationRule).userId,
                )}
              >
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Select team..." />
                </SelectTrigger>
                <SelectContent>
                  {allUsers?.data?.map((user: UserDto) => {
                    return (
                      <SelectItem key={user.id} value={String(user.id)}>
                        <div className="flex space-x-2 items-center">
                          <Avatar className="w-6 h-6">
                            {user?.avatar && (
                              <AvatarImage src={user.avatar} alt="Avatar" />
                            )}
                            <AvatarFallback className="text-sm">
                              {user?.firstName?.charAt(0) || ''}
                              {user?.lastName?.charAt(0) || ''}
                            </AvatarFallback>
                          </Avatar>
                          <div className="capitalize">
                            {user?.firstName || ''} {user?.lastName || ''}
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </TableCell>
          )}
          <TableCell>
            <Input
              className="bg-white"
              placeholder="Custom Call Type"
              value={rule.label}
              onChange={(e) => {
                updateRuleInfo({ ...rule, label: e.target.value });
              }}
            />
          </TableCell>
          <TableCell>
            <Textarea
              className="bg-white"
              placeholder="Description"
              onChange={(e) => {
                updateRuleInfo({ ...rule, explanation: e.target.value });
              }}
              value={rule.explanation}
            />
          </TableCell>
          <TableCell className="w-[30%]">
            <div>
              <Select
                onValueChange={(value: string) => {
                  updateRuleInfo({ ...rule, callType: value as AgentCallType });
                }}
                value={rule.callType}
              >
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Select scorecard of call type..." />
                </SelectTrigger>
                <SelectContent>
                  {CALL_TYPE_OPTIONS?.map((option) => {
                    const Icon = option.Icon;
                    return (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center">
                          <Icon className="mr-2" size={16} />
                          {option.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="mt-2">
              <Select
                disabled={!rule.callType}
                onValueChange={(value: string) => {
                  scorecardConfigOptions?.map((option) => {
                    if (option.id === Number(value)) {
                      updateRuleInfo({
                        ...rule,
                        scorecardConfigId: Number(value),
                        scorecardConfig: option,
                      });
                    }
                  });
                }}
                value={String(rule.scorecardConfigId)}
              >
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Choose a scorecard" />
                </SelectTrigger>
                <SelectContent>
                  {scorecardConfigOptions?.map((option) => {
                    let skip = true;
                    option.callTypes?.forEach(
                      (callType: ScorecardConfigCallType) => {
                        if (callType.callType === rule.callType) {
                          skip = false;
                        }
                      },
                    );

                    if (!skip) {
                      return (
                        <SelectItem key={option.id} value={String(option.id)}>
                          {option.tag}
                        </SelectItem>
                      );
                    }
                  })}
                </SelectContent>
              </Select>
            </div>
          </TableCell>
          <TableCell className="w-[80px]">
            {isSaving && (
              <Loader2Icon
                size={18}
                className="text-muted-foreground animate-spin"
              />
            )}
            {!isSaving && !ruleId && (
              <Button
                variant={'outline'}
                disabled={rulesThaCanBeSaved.indexOf('new') < 0}
                onClick={() => {
                  Save();
                }}
              >
                <PlusCircle size={16} />
              </Button>
            )}
            {!isSaving && !!ruleId && (
              <Button
                variant={'outline'}
                onClick={() => {
                  DeleteRule(ruleId);
                }}
              >
                <Trash2 size={16} />
              </Button>
            )}
          </TableCell>
        </TableRow>
      );
    },
    [rulesQueuedForSave, rules, ruleBeingDeleted],
  );

  return (
    <Table hideFooter={true} fitToContent={true}>
      <TableContent>
        <TableRow className="sticky top-0 z-50">
          {show === RuleCategory.TEAMS && (
            <TableCellHead className="w-[20%]">Team</TableCellHead>
          )}
          {show === RuleCategory.USERS && (
            <TableCellHead className="w-[20%]">User</TableCellHead>
          )}
          <TableCellHead>Custom Call Type</TableCellHead>
          <TableCellHead>Description</TableCellHead>
          <TableCellHead className="w-[30%]">Scorecard</TableCellHead>
          <TableCellHead className="w-[80px]">&nbsp;</TableCellHead>
        </TableRow>
        {rules.map((rule) => {
          return renderRow(rule);
        })}
        {renderRow(newRule)}
      </TableContent>
      <TableFooter className="hidden">&nbsp;</TableFooter>
    </Table>
  );
}
