import { Button } from '@/components/ui/button';
import { useCategorizationRules, usePrivacyRules } from '@/hooks/useRealCalls';
import { Pencil } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import EditRealCallSettings from './Edit';
import ViewSettings from './View';
import useUserSession from '@/hooks/useUserSession';

interface IProps {
  edit: boolean;
  startEditing: () => void;
}

export enum RuleCategory {
  ORGANIZATION = 'organization',
  TEAMS = 'teams',
  USERS = 'users',
}

export default function RealCallSettings({ edit, startEditing }: IProps) {
  const { data: categorizationRules, isLoading: isLoadingCategorizationRules } =
    useCategorizationRules();
  const { data: privacyRules, isLoading: isLoadingPrivacyRules } =
    usePrivacyRules();
  const [isLoading, setIsLoading] = useState(false);
  const { dbOrg } = useUserSession();

  const botSchedulingRules = useMemo(() => {
    return dbOrg?.settings?.optOutBotSchedulingRules || [];
  }, [dbOrg]);

  useEffect(() => {
    if (!isLoadingCategorizationRules && !isLoadingPrivacyRules) {
      setIsLoading(false);
    }
  }, [isLoadingPrivacyRules, isLoadingCategorizationRules]);

  if (isLoading) {
    return <div></div>;
  }

  let isEmpty = false;

  if (!edit) {
    if (
      (!categorizationRules ||
        (categorizationRules.organization.length === 0 &&
          categorizationRules.teams.length === 0 &&
          categorizationRules.users.length === 0)) &&
      (!privacyRules ||
        (privacyRules.organization.length === 0 &&
          privacyRules.teams.length === 0 &&
          privacyRules.users.length === 0))
    ) {
      isEmpty = true;
    }
  }

  if (!edit && isEmpty) {
    return (
      <div>
        <div className="font-semibold mb-2 text-base flex-1">
          Real call settings
        </div>
        <Button onClick={startEditing} variant={'outline'}>
          <Pencil size={16} className="mr-1" />
          Edit real call settings
        </Button>
      </div>
    );
  }

  if (edit) {
    return (
      <EditRealCallSettings
        categorizationRules={
          categorizationRules || { organization: [], teams: [], users: [] }
        }
        privacyRules={
          privacyRules || { organization: [], teams: [], users: [] }
        }
        botSchedulingRules={{ organization: botSchedulingRules || [] }}
      />
    );
  } else {
    return (
      <ViewSettings
        categorizationRules={
          categorizationRules || { organization: [], teams: [], users: [] }
        }
        privacyRules={
          privacyRules || { organization: [], teams: [], users: [] }
        }
        botSchedulingRules={{ organization: botSchedulingRules || [] }}
      />
    );
  }
}
