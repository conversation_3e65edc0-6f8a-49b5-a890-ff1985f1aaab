import React, { useEffect, useMemo, useState } from 'react';
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  She<PERSON><PERSON>eader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { RoleEnum } from '@/lib/User/types';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { Loader2 } from 'lucide-react';
import dayjs from 'dayjs';
import useUserSession from '@/hooks/useUserSession';
import { useQueryClient } from '@tanstack/react-query';
import OrganizationCard from '@/components/OrganizationCard';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardTitle, CardHeader } from '@/components/ui/card';
import { OrganizationDto } from '@/lib/Organization/types';
import { Integration, IntegrationStatus } from '@/lib/Integrations/types';
import IntegrationService from '@/lib/Integrations';

interface IIntegrationSharingPanelProps {
  integration: Integration;
  open: boolean;
  onOpenChange: () => void;
}

function IntegrationSharingPanel({
  integration,
  open,
  onOpenChange,
}: IIntegrationSharingPanelProps) {
  const authInfo = useAuthInfo();
  const org = useActiveOrg();
  const queryClient = useQueryClient();

  const { dbOrg, subOrganizations, sortedOrgs } = useUserSession();

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  const [madeChanges, setMadeChanges] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');

  const filteredSubOrganizations = useMemo(() => {
    const filtered = subOrganizations?.filter((org) => org.uid !== dbOrg?.uid);
    if (search) {
      return filtered?.filter((org) =>
        org.name.toLowerCase().includes(search.toLowerCase()),
      );
    }
    return filtered;
  }, [subOrganizations, dbOrg, search]);

  /***********************************/
  /***** SHARE AGENT WITH ORG  *******/
  /***********************************/

  const [orgsPerIntegration, setOrgsPerIntegration] = useState<{
    [key: number]: { selected: boolean };
  }>({});

  useEffect(() => {
    const newOrgsPerIntegration: {
      [key: number]: { selected: boolean };
    } = {};
    integration.subIntegrations?.map((i) => {
      const isSharedFromThisIntegration =
        i.originalIntegrationId === integration.id;

      if (
        i.orgId &&
        i.status === IntegrationStatus.ACTIVE &&
        isSharedFromThisIntegration
      ) {
        newOrgsPerIntegration[i.orgId] = {
          selected: true,
        };
      }
    });
    setOrgsPerIntegration(newOrgsPerIntegration);
  }, [integration]);

  const orgsSharedWith = useMemo(() => {
    return filteredSubOrganizations?.filter(
      (org) => orgsPerIntegration[org.id]?.selected,
    );
  }, [filteredSubOrganizations, orgsPerIntegration]);

  const orgsNotSharedWith = useMemo(() => {
    return filteredSubOrganizations?.filter(
      (org) => !orgsPerIntegration[org.id]?.selected,
    );
  }, [filteredSubOrganizations, orgsPerIntegration]);

  const shareIntegration = async () => {
    try {
      setIsSaving(true);
      const selectedOrgIds = Object.keys(orgsPerIntegration).map((orgId) =>
        Number(orgId),
      );
      await IntegrationService.shareIntegrationWithSubOrgs(
        integration.id,
        selectedOrgIds,
      );
      setMadeChanges(false);
    } catch (e) {
      console.log(`Error sharing integration with orgs`, e);
    } finally {
      setIsSaving(false);
    }
    queryClient.invalidateQueries({
      predicate: (query) => query.queryKey[0] === 'integrations',
    });
  };

  const _onCardClick = (subOrg: OrganizationDto) => {
    setMadeChanges(true);
    if (orgsPerIntegration[subOrg.id]) {
      delete orgsPerIntegration[subOrg.id];
    } else {
      orgsPerIntegration[subOrg.id] = {
        selected: true,
      };
    }

    setOrgsPerIntegration({ ...orgsPerIntegration });
  };

  const _onOpenChange = () => {
    setOrgsPerIntegration({});
    setSearch('');
    setMadeChanges(false);
    onOpenChange();
  };

  if (!integration) {
    return null;
  }

  return (
    <Sheet open={open} onOpenChange={_onOpenChange}>
      <SheetContent className="flex flex-col max-w-[90vw] md:max-w-[90vw] sm:max-w-[90vw]">
        <SheetHeader>
          <SheetTitle>Integration: {integration.name}</SheetTitle>
        </SheetHeader>

        <p className="text-lg font-semibold text-foreground mt-4">
          Organizations
        </p>
        <Input
          placeholder="Search an organization"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pr-10 py-4"
        />
        <div className="flex flex-row justify-between gap-6 overflow-y-auto">
          <Card className={cn('mt-4 flex-1 px-4 pt-2 pb-4')}>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center mb-6">
                <p className="font-semibold">Not shared with</p>
                <Badge variant={'secondary'}>{orgsNotSharedWith?.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0 pr-4">
              {orgsNotSharedWith?.length ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {orgsNotSharedWith?.map((orgDetails) => (
                    <div key={orgDetails.id} className="relative">
                      <OrganizationCard
                        org={orgDetails}
                        orgMetadata={
                          sortedOrgs.find((org) => org.orgId === orgDetails.uid)
                            ?.orgMetadata || undefined
                        }
                        className={cn('transition-all duration-200')}
                        onClick={() => _onCardClick(orgDetails)}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-sm text-muted-foreground">
                  No organizations found
                </p>
              )}
            </CardContent>
          </Card>

          <Card className={cn('mt-4 flex-1 px-4 pt-2 pb-4')}>
            <CardHeader className="rounded-t-xl px-3 pt-3 pb-0">
              <CardTitle className="flex justify-between items-center mb-6">
                <p className="font-semibold">Shared with</p>
                <Badge variant={'secondary'}>{orgsSharedWith?.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0 pr-4">
              {orgsSharedWith?.length ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {orgsSharedWith?.map((orgDetails) => (
                    <div key={orgDetails.id} className="relative">
                      <OrganizationCard
                        org={orgDetails}
                        orgMetadata={
                          sortedOrgs.find((org) => org.orgId === orgDetails.uid)
                            ?.orgMetadata || undefined
                        }
                        className={cn('transition-all duration-200')}
                        onClick={() => _onCardClick(orgDetails)}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-sm text-muted-foreground">
                  No organizations found
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {authInfo?.accessHelper?.isAtLeastRole(
          org?.orgId as string,
          RoleEnum.ADMIN,
        ) ? (
          <Button
            onClick={() => shareIntegration()}
            disabled={isPilotEnded || !madeChanges || isSaving}
          >
            {isSaving ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : null}
            {isSaving ? 'Saving...' : 'Save changes'}
          </Button>
        ) : null}
      </SheetContent>
    </Sheet>
  );
}

export default React.memo(IntegrationSharingPanel);
