import { Eye, EyeOff } from 'lucide-react';

interface ISelectColumnsProps {
  columns: any[];
  updateColumnsSettings: (columns: any[]) => void;
}

export default function EditColumnsSelection({
  columns,
  updateColumnsSettings,
}: ISelectColumnsProps) {
  const toggleColum = (cid: string) => {
    const newColumns = columns.map((c) => {
      if (c.id === cid) {
        return {
          ...c,
          visible: !c.visible,
        };
      }
      return c;
    });

    updateColumnsSettings([...newColumns]);
  };

  return (
    <div>
      <div className="italic text-sm mb-3 text-muted-foreground">
        Shown Columns
      </div>
      <div>
        {columns.map((c) => {
          if (!c.diabled) {
            return (
              <div
                key={'ecs' + c.id}
                className="flex aligg-center hover:bg-muted/80 p-1 cursor-pointer"
                onClick={() => toggleColum(c.id)}
              >
                <div className="text-sm flex-1">{c.header}</div>
                {c.visible ? (
                  <div>
                    <Eye size={16} />{' '}
                  </div>
                ) : (
                  <div>
                    <EyeOff size={16} />
                  </div>
                )}
              </div>
            );
          }
        })}
      </div>
    </div>
  );
}
