import {
  AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE,
  AGENT_GENDER_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { TableCell, TableRow } from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  AgentCallType,
  AgentDto,
  AgentEmotionalState,
  AgentGender,
} from '@/lib/Agent/types';
import { memo, useCallback, useEffect, useState } from 'react';
import { AgentInfo } from '..';
import { VOICE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import { NotebookPen, NotebookText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import useUserSession from '@/hooks/useUserSession';
import { getResearchData } from '@/common/CreateBuyerForm/Main/utils';
import AgentAvatar from '@/components/Avatars/Agent';

interface ITableRowProps {
  isHeaderAgent?: boolean;
  info: AgentInfo;
  columns: any[];
  toggleAgent?: (id: string) => void;
  toggleDelete?: (id: string) => void;
  updateValue?: (
    id: string,
    propName: string,
    newValue: string | AgentCallType | AgentEmotionalState,
  ) => void;
  openResumeCallEdit: (id: string) => void;
}

function AgentTableRow({
  columns,
  isHeaderAgent,
  info,
  toggleAgent,
  toggleDelete,
  updateValue,
  openResumeCallEdit,
}: ITableRowProps) {
  const { CALL_SCENARIO_OPTIONS } = useUserSession();

  let { data: scorecardConfigOptions } = useScorecardConfigsForOrg();
  if (!scorecardConfigOptions) {
    scorecardConfigOptions = [];
  }

  const scorecardConfigOptionsObject: any = {};
  for (const o of scorecardConfigOptions) {
    if (o.id) {
      scorecardConfigOptionsObject[o.id] = o.tag;
    }
  }

  const agent = info.agent;

  const allCells: any[] = [];

  const isMarkedForDelete = info.delete;

  const _toggleAgent = (id: string) => {
    if (toggleAgent) {
      toggleAgent(id);
    }
  };

  const _updateValue = (id: string, propName: string, newValue: any) => {
    if (updateValue) {
      updateValue(id, propName, newValue);
    }
  };

  const _toggleDelete = (id: string) => {
    if (toggleDelete) {
      toggleDelete(id);
    }
  };

  /*****************************************************/
  /************** FULL NAME SUPPORT ********************/
  /*****************************************************/
  /* we need to support full name input field, which is 
  /* a combination of first and last name 
  /*****************************************************/

  const calculateFullName = useCallback(() => {
    //this strange lines are needed to support user's typing in the input field
    let fullName = '';
    if (agent.firstName) {
      fullName += agent.firstName;
    }
    if (agent.lastName) {
      if (fullName != '') {
        fullName += ' ';
      }
      fullName += agent.lastName;
    }
    return fullName;
  }, []);

  const [fullName, setFullName] = useState<string>(calculateFullName());

  useEffect(() => {
    setFullName(calculateFullName());
  }, [agent.firstName, agent.lastName, calculateFullName]);

  const updateName = (value: string) => {
    const newChar = value.replace(fullName, '');
    if (newChar != ' ') {
      _updateValue(info.id, 'name', value);
    }
    setFullName(value);
  };

  //---------------------------------------
  //----------- end full name support
  //---------------------------------------

  /*****************************************************/
  /************** UTILITY FUNCTIONS ********************/
  /*****************************************************/

  const getCallScenario = (agent: AgentDto) => {
    if (agent) {
      const existingAgentResearch = getResearchData(agent);

      let callScenario = AgentCallType.NONE;

      if (agent.callType == AgentCallType.COLD) {
        callScenario = existingAgentResearch.cold_call_scenario;
      } else if (agent.callType == AgentCallType.WARM) {
        callScenario = existingAgentResearch.warm_call_scenario;
      } else if (agent.callType == AgentCallType.DISCOVERY) {
        callScenario = existingAgentResearch.discovery_call_scenario;
      }

      return callScenario;
    }
    return '';
  };

  /*****************************************/
  /************* RENDERING *****************/
  /*****************************************/

  if (isHeaderAgent) {
    allCells.push(<TableCell className="w-6 h-6"></TableCell>);
  } else {
    if (isMarkedForDelete) {
      const checkboxCell = <TableCell></TableCell>;
      allCells.push(checkboxCell);
    } else {
      const checkboxCell = (
        <TableCell>
          <Checkbox
            className="w-6 h-6"
            onClick={() => {
              _toggleAgent(info.id);
            }}
            checked={info.selected}
          />
        </TableCell>
      );

      allCells.push(checkboxCell);
    }
  }

  columns.map((c) => {
    if (c.visible && !c.disabled) {
      if (c.id == 'avatar') {
        const cell = (
          <TableCell key={c.id + info.id}>
            <AgentAvatar className="w-8 h-8" agent={agent} />
          </TableCell>
        );
        allCells.push(cell);
      } else if (c.id == 'jobTitle') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const cell = (
            <TableCell key={c.id + info.id}>
              <Input
                value={agent.jobTitle}
                onChange={(e) => _updateValue(info.id, c.id, e.target.value)}
              />
            </TableCell>
          );
          allCells.push(cell);
        } else {
          const cell = (
            <TableCell key={c.id + info.id}>{agent.jobTitle}</TableCell>
          );
          allCells.push(cell);
        }
      } else if (c.id == 'name') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const cell = (
            <TableCell key={c.id + info.id + agent.firstName + agent.lastName}>
              <Input
                value={fullName}
                onChange={(e) => updateName(e.target.value)}
              />
            </TableCell>
          );
          allCells.push(cell);
        } else {
          const cell = (
            <TableCell key={c.id + info.id}>
              {agent.firstName + ' ' + agent.lastName}
            </TableCell>
          );
          allCells.push(cell);
        }
      } else if (c.id == 'gender') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const cell = (
            <TableCell key={c.id + info.id + agent.gender}>
              <Select
                onValueChange={(value: AgentGender) => {
                  _updateValue(info.id, c.id, value);
                }}
                defaultValue={agent.gender}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose an option" />
                </SelectTrigger>
                <SelectContent>
                  {AGENT_GENDER_OPTIONS.map((option) => {
                    return (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </TableCell>
          );
          allCells.push(cell);
        } else {
          const cell = (
            <TableCell key={c.id + info.id}>
              {agent.gender?.charAt(0).toUpperCase() +
                agent.gender?.toLowerCase().substring(1)}
            </TableCell>
          );
          allCells.push(cell);
        }
      } else if (c.id == 'voice') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const cell = (
            <TableCell key={c.id + info.id + agent.voice}>
              <Select
                onValueChange={(value: AgentGender) => {
                  _updateValue(info.id, c.id, value);
                }}
                defaultValue={agent.voice}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose an option" />
                </SelectTrigger>
                <SelectContent>
                  {VOICE_OPTIONS[agent.gender].map((option) => {
                    return (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </TableCell>
          );
          allCells.push(cell);
        } else {
          const voiceName = VOICE_OPTIONS[agent.gender]?.map((o) => {
            if (o.value == agent.voice) {
              return o.label;
            }
          });
          const cell = <TableCell key={c.id + info.id}>{voiceName}</TableCell>;
          allCells.push(cell);
        }
      } else if (c.id == 'resumeCall') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const cell = (
            <TableCell key={c.id + info.id + agent.scorecardConfigId}>
              <Button
                variant="ghost"
                onClick={() => {
                  openResumeCallEdit(info.id);
                }}
              >
                <NotebookPen size={20} />
              </Button>
            </TableCell>
          );
          allCells.push(cell);
        } else {
          const research = getResearchData(agent);
          if (research.messages) {
            const cell = (
              <TableCell key={c.id + info.id}>
                <NotebookText />
              </TableCell>
            );
            allCells.push(cell);
          } else {
            const cell = <TableCell key={c.id + info.id}>&nbsp;</TableCell>;
            allCells.push(cell);
          }
        }
      } else if (c.id == 'scorecard') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const cell = (
            <TableCell key={c.id + info.id + agent.scorecardConfigId}>
              <Select
                onValueChange={(value: string) => {
                  _updateValue(info.id, c.id, Number(value));
                }}
                defaultValue={String(agent.scorecardConfigId)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a scorecard" />
                </SelectTrigger>
                <SelectContent>
                  {scorecardConfigOptions && (
                    <>
                      {scorecardConfigOptions.map((option) => {
                        return (
                          <SelectItem key={option.id} value={String(option.id)}>
                            {option.tag}
                          </SelectItem>
                        );
                      })}
                    </>
                  )}
                </SelectContent>
              </Select>
            </TableCell>
          );
          allCells.push(cell);
        } else {
          let scName = '';
          if (agent.scorecardConfigId) {
            scName = scorecardConfigOptionsObject[agent.scorecardConfigId];
          }

          const cell = <TableCell key={c.id + info.id}>{scName}</TableCell>;
          allCells.push(cell);
        }
      } else if (c.id == 'companyName') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const cell = (
            <TableCell key={c.id + info.id}>
              <Input
                className="w-26"
                value={agent.companyName}
                onChange={(e) => _updateValue(info.id, c.id, e.target.value)}
              />
            </TableCell>
          );
          allCells.push(cell);
        } else {
          const cell = (
            <TableCell key={c.id + info.id}>{agent.companyName}</TableCell>
          );
          allCells.push(cell);
        }
      } else if (c.id == 'callType') {
        const callType = CALL_TYPE_OPTIONS.find(
          (ct) => ct.value == agent.callType,
        )?.value;

        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          //need to add callType to the key or react wont re-render this select correctly
          const cell = (
            <TableCell key={c.id + info.id + callType}>
              <Select
                onValueChange={(value: AgentCallType) => {
                  _updateValue(info.id, c.id, value);
                }}
                defaultValue={callType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose an option" />
                </SelectTrigger>
                <SelectContent>
                  {CALL_TYPE_OPTIONS.map((option) => {
                    if (option.value != AgentCallType.GATEKEEPER) {
                      return (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      );
                    }
                  })}
                </SelectContent>
              </Select>
            </TableCell>
          );

          allCells.push(cell);
        } else {
          const callType = CALL_TYPE_OPTIONS.find(
            (ct) => ct.value == agent.callType,
          )?.label;
          const cell = <TableCell key={c.id + agent.id}>{callType}</TableCell>;
          allCells.push(cell);
        }
      } else if (c.id == 'callScenario') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const callType: AgentCallType | undefined = CALL_TYPE_OPTIONS.find(
            (ct) => ct.value == agent.callType,
          )?.value;
          if (callType && callType != AgentCallType.FOCUS) {
            //need to add callType to the key or react wont re-render this select correctly
            const scenario = getCallScenario(agent);
            const options =
              CALL_SCENARIO_OPTIONS[
                callType as keyof typeof CALL_SCENARIO_OPTIONS
              ];
            const cell = (
              <TableCell key={c.id + info.id + scenario} className="max-w-32">
                <Select
                  onValueChange={(value: string) => {
                    _updateValue(info.id, c.id, value);
                  }}
                  defaultValue={scenario}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an option" />
                  </SelectTrigger>
                  <SelectContent>
                    {options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </TableCell>
            );
            allCells.push(cell);
          }
        } else {
          allCells.push(
            <TableCell key={c.id + info.id}>
              {getCallScenario(agent)}
            </TableCell>,
          );
        }
      } else if (c.id == 'emotionalState') {
        if (c.editable && !isMarkedForDelete && !isHeaderAgent) {
          const callType: AgentCallType | undefined = CALL_TYPE_OPTIONS.find(
            (ct) => ct.value == agent.callType,
          )?.value;
          if (callType && callType != AgentCallType.FOCUS) {
            const emotionalState = AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
              callType as keyof typeof AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE
            ].find((ct) => ct.value == agent.emotionalState)?.value;

            const options =
              AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
                callType as Exclude<
                  AgentCallType,
                  | AgentCallType.NONE
                  | AgentCallType.GATEKEEPER
                  | AgentCallType.FOCUS
                >
              ];

            const cell = (
              <TableCell
                key={c.id + info.id + emotionalState}
                className="max-w-16"
              >
                <Select
                  onValueChange={(value: AgentEmotionalState) => {
                    _updateValue(info.id, c.id, value);
                  }}
                  defaultValue={emotionalState}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an option" />
                  </SelectTrigger>
                  <SelectContent>
                    {options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </TableCell>
            );
            allCells.push(cell);
          }
        } else {
          const callType: AgentCallType | undefined = CALL_TYPE_OPTIONS.find(
            (ct) => ct.value == agent.callType,
          )?.value;
          if (callType && callType != AgentCallType.FOCUS) {
            const emotionalState = AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
              callType as Exclude<
                AgentCallType,
                | AgentCallType.NONE
                | AgentCallType.GATEKEEPER
                | AgentCallType.FOCUS
              >
            ].find((ct) => ct.value == agent.emotionalState)?.label;
            const cell = (
              <TableCell key={c.id + info.id}>{emotionalState}</TableCell>
            );
            allCells.push(cell);
          }
        }
      }
    }
  });

  if (isHeaderAgent) {
    allCells.push(<TableCell></TableCell>);
  } else {
    allCells.push(
      <TableCell>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Switch
                checked={!isMarkedForDelete}
                onCheckedChange={() => {
                  _toggleDelete(info.id);
                }}
              />
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{isMarkedForDelete ? 'Activate bot' : 'Deactivate bot'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </TableCell>,
    );
  }

  if (isHeaderAgent) {
    return <TableRow className="bg-slate-200">{allCells}</TableRow>;
  } else {
    return (
      <TableRow style={{ opacity: isMarkedForDelete ? 0.5 : 1 }}>
        {allCells}
      </TableRow>
    );
  }
}

export default memo(AgentTableRow);
