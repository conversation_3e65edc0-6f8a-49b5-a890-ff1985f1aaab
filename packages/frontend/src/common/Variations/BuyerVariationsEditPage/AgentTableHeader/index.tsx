import React, { useState } from 'react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { TableHead } from '@/components/ui/table';
import { Pencil } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AgentEmotionalState,
  AgentCallType,
  AgentGender,
  AgentVoice,
} from '@/lib/Agent/types';
import {
  CALL_TYPE_OPTIONS,
  AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE,
  AGENT_GENDER_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { VOICE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import useUserSession from '@/hooks/useUserSession';
import useCallTypeOptions from '@/hooks/useCallTypeOptions';

interface ITableHeadProps {
  columns: any[];
  isOneSelected: boolean;
  numberOfAgents: number;
  allAgentsSelected: boolean;
  allAgentsDeleted: boolean;
  toggleAllAgents: () => void;
  toggleDeleteAllAgents: () => void;
  updateSelected: (
    propName: string,
    newValue:
      | string
      | AgentCallType
      | AgentEmotionalState
      | { [key in AgentCallType]: string }
      | AgentVoice
      | number,
  ) => void;
  getSelectedCallTypes: () => AgentCallType[];
  openResumeCallEditAll: () => void;
}

function AgentTableHeader({
  columns,
  isOneSelected,
  numberOfAgents,
  allAgentsSelected,
  allAgentsDeleted,
  toggleDeleteAllAgents,
  toggleAllAgents,
  updateSelected,
  getSelectedCallTypes,
  openResumeCallEditAll,
}: ITableHeadProps) {
  const { CALL_SCENARIO_OPTIONS } = useUserSession();
  const { callTypeOptions } = useCallTypeOptions();

  let { data: scorecardConfigOptions } = useScorecardConfigsForOrg();
  if (!scorecardConfigOptions) {
    scorecardConfigOptions = [];
  }

  const [editSelectedJobTitle, setEditSelectedJobTitle] = useState<string>('');
  const [editSelectedCompanyName, setEditSelectedCompanyName] =
    useState<string>('');
  const [editCallTypeForSelected, setEditCallTypeForSelected] =
    useState<AgentCallType>(AgentCallType.COLD);
  const [name, setEditName] = useState<string>('');
  const [gender, setEditGender] = useState<string>('');
  const [voice, setEditVoice] = useState<AgentVoice>();
  const [scorecard, setEditScorecard] = useState<number>(0);

  const _openPops: any = {};

  columns.forEach((column) => {
    _openPops[column.id] = false;
  });
  const [openPops, setOpenPops] = useState(_openPops);

  const _updateSelected = (
    propName: string,
    newValue:
      | string
      | AgentCallType
      | AgentEmotionalState
      | AgentVoice
      | number
      | { [key in AgentCallType]: string },
  ) => {
    openPops[propName] = false;
    setOpenPops({ ...openPops });
    if (updateSelected) {
      updateSelected(propName, newValue);
    }
  };

  //********* CALL SCENARIO ****/

  const selectedCallTypes = getSelectedCallTypes();

  const defaultOptionsForCallScenarios: { [key in AgentCallType]: string } = {
    [AgentCallType.COLD]: '',
    [AgentCallType.DISCOVERY]: '',
    [AgentCallType.WARM]: '',
    [AgentCallType.GATEKEEPER]: '',
    [AgentCallType.FOCUS]: '',
    [AgentCallType.CHECKIN]: '',
    [AgentCallType.RENEWAL]: '',
    [AgentCallType.CUSTOM]: '',
    [AgentCallType.DEMO]: '',
    [AgentCallType.NONE]: '',
    [AgentCallType.MANAGER_ONE_ON_ONE]: '',
  };

  selectedCallTypes.forEach((ct) => {
    const options =
      CALL_SCENARIO_OPTIONS[ct as keyof typeof CALL_SCENARIO_OPTIONS];
    defaultOptionsForCallScenarios[ct] = options[0].value;
  });

  const [editCallScenarioForSelected, setEditCallScenarioForSelected] =
    useState<{ [key in AgentCallType]: string }>(
      defaultOptionsForCallScenarios,
    );

  //********* EMOTIONAL STATE ****/

  const defaultOptionsForEmotionalState: { [key in AgentCallType]: string } = {
    [AgentCallType.COLD]: '',
    [AgentCallType.DISCOVERY]: '',
    [AgentCallType.WARM]: '',
    [AgentCallType.GATEKEEPER]: '',
    [AgentCallType.FOCUS]: '',
    [AgentCallType.CHECKIN]: '',
    [AgentCallType.RENEWAL]: '',
    [AgentCallType.CUSTOM]: '',
    [AgentCallType.DEMO]: '',
    [AgentCallType.NONE]: '',
    [AgentCallType.MANAGER_ONE_ON_ONE]: '',
  };

  selectedCallTypes.forEach((ct) => {
    const options =
      AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
        ct as keyof typeof AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE
      ];
    defaultOptionsForEmotionalState[ct] = options[0].value;
  });

  const [editEmotionalStateForSelected, setEditEmotionalStateForSelected] =
    useState<{ [key in AgentCallType]: string }>(
      defaultOptionsForEmotionalState,
    );

  const allHeaders = columns.map((column) => {
    if (column.visible && !column.disabled) {
      if (isOneSelected) {
        return (
          <TableHead key={column.id}>
            {column.id == 'resumeCall' && (
              <div
                className="flex"
                style={{ cursor: 'pointer' }}
                onClick={openResumeCallEditAll}
              >
                <div>{column.header}</div>
                <div className="ml-1 w-6">
                  <Pencil className="w-4 h-4" />
                </div>
              </div>
            )}
            {column.id != 'resumeCall' && (
              <Popover
                open={openPops[column.id]}
                onOpenChange={() => {
                  openPops[column.id] = !openPops[column.id];
                  setOpenPops({ ...openPops });
                }}
              >
                <PopoverTrigger asChild>
                  <div className="flex" style={{ cursor: 'pointer' }}>
                    <div>{column.header}</div>
                    <div className="ml-1 w-6">
                      <Pencil className="w-4 h-4" />
                    </div>
                  </div>
                </PopoverTrigger>
                <PopoverContent align="start">
                  {column.id == 'avatar' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit avatar for selected buyers
                      </div>
                      <Button
                        className="mt-4"
                        onClick={(e) => {
                          _updateSelected(column.id, '');
                        }}
                      >
                        Generate Random Avatar
                      </Button>
                    </>
                  )}
                  {column.id == 'jobTitle' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit job title for selected buyers
                      </div>
                      <div className="mt-4">
                        <div className="text-sm font-medium mb-2">
                          Job Title
                        </div>
                        <Input
                          className="w-full"
                          value={editSelectedJobTitle}
                          onChange={(e) =>
                            setEditSelectedJobTitle(e.target.value)
                          }
                        />
                      </div>
                      <div className="flex mt-2">
                        <div className="flex-1"></div>
                        <Button
                          onClick={(e) => {
                            _updateSelected(column.id, editSelectedJobTitle);
                          }}
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                  {column.id == 'name' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit full name for selected buyers
                      </div>
                      <div className="mt-4">
                        <div className="text-sm font-medium mb-2">Name</div>
                        <Input
                          className="w-full"
                          value={name}
                          onChange={(e) => setEditName(e.target.value)}
                        />
                      </div>
                      <div className="flex mt-4 items-center">
                        <Button
                          onClick={(e) =>
                            _updateSelected(column.id, '-random-')
                          }
                        >
                          Random
                        </Button>
                        <div className="flex-1"></div>
                        <div className="text-sm mr-1">or</div>
                        <Button
                          onClick={(e) => {
                            _updateSelected(column.id, name);
                          }}
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                  {column.id == 'gender' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit gender for selected buyers
                      </div>
                      <div className="mt-4">
                        <div className="text-sm font-medium mb-2">Gender</div>
                        <Select
                          onValueChange={(value: AgentGender) => {
                            setEditGender(value);
                          }}
                          defaultValue=""
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an option" />
                          </SelectTrigger>
                          <SelectContent>
                            {AGENT_GENDER_OPTIONS.map((option) => {
                              return (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              );
                            })}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex mt-4 items-center">
                        <Button
                          onClick={(e) =>
                            _updateSelected(column.id, '-random-')
                          }
                        >
                          Random
                        </Button>
                        <div className="flex-1"></div>
                        <div className="text-sm mr-1">or</div>
                        <Button
                          onClick={(e) => {
                            _updateSelected(column.id, gender);
                          }}
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                  {column.id == 'voice' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit voice for selected buyers
                      </div>
                      <div className="mt-4">
                        <div className="text-sm font-medium mb-2">Voice</div>
                        <Select
                          onValueChange={(value: AgentVoice) => {
                            setEditVoice(value);
                          }}
                          defaultValue=""
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an option" />
                          </SelectTrigger>
                          <SelectContent>
                            {AGENT_GENDER_OPTIONS.map((gendOption, i) => {
                              let f = true;
                              return VOICE_OPTIONS[gendOption.value].map(
                                (option) => {
                                  if (f) {
                                    f = false;
                                    return (
                                      <>
                                        <div
                                          className={
                                            'text-sm font-semibold ml-2 ' +
                                            (i > 0 ? 'mt-2' : 'mt-1')
                                          }
                                        >
                                          {gendOption.label}
                                        </div>
                                        <SelectItem
                                          key={option.value}
                                          value={option.value}
                                        >
                                          {option.label}
                                        </SelectItem>
                                      </>
                                    );
                                  } else {
                                    return (
                                      <SelectItem
                                        key={option.value}
                                        value={option.value}
                                      >
                                        {option.label}
                                      </SelectItem>
                                    );
                                  }
                                },
                              );
                            })}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex mt-4 items-center">
                        <Button
                          onClick={(e) =>
                            _updateSelected(column.id, '-random-')
                          }
                        >
                          Random
                        </Button>
                        <div className="flex-1"></div>
                        <div className="text-sm mr-1">or</div>
                        <Button
                          disabled={!voice}
                          onClick={(e) => {
                            _updateSelected(column.id, voice || '');
                          }}
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                  {column.id == 'scorecard' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit scorecard for selected buyers
                      </div>
                      <div className="mt-4">
                        <div className="text-sm font-medium mb-2">
                          Scorecard
                        </div>
                        <Select
                          onValueChange={(value: string) => {
                            setEditScorecard(Number(value));
                          }}
                          defaultValue=""
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an option" />
                          </SelectTrigger>
                          <SelectContent>
                            {scorecardConfigOptions && (
                              <>
                                {scorecardConfigOptions.map((option) => {
                                  return (
                                    <SelectItem
                                      key={option.id}
                                      value={String(option.id)}
                                    >
                                      {option.tag}
                                    </SelectItem>
                                  );
                                })}
                              </>
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex mt-4 items-center">
                        <Button
                          onClick={(e) =>
                            _updateSelected(column.id, '-random-')
                          }
                        >
                          Random
                        </Button>
                        <div className="flex-1"></div>
                        <div className="text-sm mr-1">or</div>
                        <Button
                          onClick={(e) => {
                            _updateSelected(column.id, scorecard);
                          }}
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                  {column.id == 'companyName' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit company name for selected buyers
                      </div>
                      <div className="mt-4">
                        <div className="text-sm font-medium mb-2">
                          Company Name
                        </div>
                        <Input
                          className="w-full"
                          value={editSelectedCompanyName}
                          onChange={(e) =>
                            setEditSelectedCompanyName(e.target.value)
                          }
                        />
                      </div>
                      <div className="flex mt-4 items-center">
                        <div className="flex-1"></div>
                        <Button
                          onClick={(e) => {
                            _updateSelected(column.id, editSelectedCompanyName);
                          }}
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                  {column.id == 'callType' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit call type for selected buyers
                      </div>
                      <div className="mt-4">
                        <div className="text-sm font-medium mb-2">
                          Call Type
                        </div>
                        <Select
                          onValueChange={(value: AgentCallType) => {
                            setEditCallTypeForSelected(value);
                          }}
                          defaultValue=""
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an option" />
                          </SelectTrigger>
                          <SelectContent>
                            {callTypeOptions.map((option) => {
                              if (option.value != AgentCallType.GATEKEEPER) {
                                return (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                );
                              }
                            })}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex mt-4 items-center">
                        <Button
                          onClick={(e) =>
                            _updateSelected(column.id, '-random-')
                          }
                        >
                          Random
                        </Button>
                        <div className="flex-1"></div>
                        <div className="text-sm mr-1">or</div>
                        <Button
                          onClick={(e) =>
                            _updateSelected(column.id, editCallTypeForSelected)
                          }
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                  {column.id == 'callScenario' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit call scenario for selected buyers
                      </div>
                      <div className="mt-4">
                        {selectedCallTypes.map((ct) => {
                          return (
                            <div key={'header-' + ct} className="mb-4">
                              <div className="text-sm font-medium mb-2">
                                For{' '}
                                {
                                  CALL_TYPE_OPTIONS.find((c) => c.value == ct)
                                    ?.label
                                }{' '}
                                bot(s) set
                              </div>
                              <Select
                                onValueChange={(value: string) => {
                                  editCallScenarioForSelected[ct] = value;
                                  setEditCallScenarioForSelected({
                                    ...editCallScenarioForSelected,
                                  });
                                }}
                                defaultValue={editCallScenarioForSelected[ct]}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Choose an option" />
                                </SelectTrigger>
                                <SelectContent>
                                  {CALL_SCENARIO_OPTIONS[
                                    ct as keyof typeof CALL_SCENARIO_OPTIONS
                                  ].map((option) => (
                                    <SelectItem
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          );
                        })}
                      </div>
                      <div className="flex mt-4 items-center">
                        <Button
                          onClick={(e) =>
                            _updateSelected(column.id, '-random-')
                          }
                        >
                          Random
                        </Button>
                        <div className="flex-1"></div>
                        <div className="text-sm mr-1">or</div>
                        <Button
                          onClick={(e) =>
                            _updateSelected(
                              column.id,
                              editCallScenarioForSelected,
                            )
                          }
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                  {column.id == 'emotionalState' && (
                    <>
                      <div className="italic text-sm mb-2 text-muted-foreground">
                        Edit emotional state for selected buyers
                      </div>
                      <div className="mt-4">
                        {selectedCallTypes.map((ct) => {
                          return (
                            <div key={'header-' + ct} className="mb-4">
                              <div className="text-sm font-medium mb-2">
                                For{' '}
                                {
                                  CALL_TYPE_OPTIONS.find((c) => c.value == ct)
                                    ?.label
                                }{' '}
                                bot(s) set
                              </div>
                              <Select
                                onValueChange={(value: string) => {
                                  editEmotionalStateForSelected[ct] = value;
                                  setEditEmotionalStateForSelected({
                                    ...editEmotionalStateForSelected,
                                  });
                                }}
                                defaultValue={editEmotionalStateForSelected[ct]}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Choose an option" />
                                </SelectTrigger>
                                <SelectContent>
                                  {AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
                                    ct as keyof typeof CALL_SCENARIO_OPTIONS
                                  ].map((option) => (
                                    <SelectItem
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          );
                        })}
                      </div>
                      <div className="flex mt-4 items-center">
                        <Button
                          onClick={(e) =>
                            _updateSelected(column.id, '-random-')
                          }
                        >
                          Random
                        </Button>
                        <div className="flex-1"></div>
                        <div className="text-sm mr-1">or</div>
                        <Button
                          onClick={(e) =>
                            _updateSelected(
                              column.id,
                              editEmotionalStateForSelected,
                            )
                          }
                        >
                          Apply
                        </Button>
                      </div>
                    </>
                  )}
                </PopoverContent>
              </Popover>
            )}
          </TableHead>
        );
      } else {
        return (
          <TableHead key={column.id}>
            <div className="flex">
              <div>{column.header}</div>
              <div className="ml-1 w-6">&nbsp;</div>
            </div>
          </TableHead>
        );
      }
    }
  });

  if (numberOfAgents && numberOfAgents > 0) {
    allHeaders.unshift(
      <TableHead key="checkAllCell">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Checkbox
                className="w-6 h-6 mr-2"
                onClick={toggleAllAgents}
                checked={allAgentsSelected}
              />
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Select all</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </TableHead>,
    );

    allHeaders.push(
      <TableHead key="deleteAllCell">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Switch
                checked={!allAgentsDeleted}
                onCheckedChange={toggleDeleteAllAgents}
              />
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{allAgentsDeleted ? 'Activate all' : 'Deactivate all'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </TableHead>,
    );
  } else {
    allHeaders.unshift(<TableHead key="checkAllCell" />);
    allHeaders.push(<TableHead key="deleteAllCell" />);
  }

  return allHeaders;
}

export default React.memo(AgentTableHeader);
