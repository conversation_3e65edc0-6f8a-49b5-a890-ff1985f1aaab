'use client';

import {
  AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE,
  AGENT_GENDER_OPTIONS,
  AVATAR_OPTIONS,
  CALL_TYPE_OPTIONS,
} from '@/common/CreateBuyerForm/constants';
import DashboardNavbar from '@/common/DashboardNavbar';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ProgressCustom } from '@/components/ui/progress-custom';
import { Table, TableBody, TableHeader } from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useAgent } from '@/hooks/useAgent';
import useOrgAgentVariations from '@/hooks/useOrgAgentVariations';
import AgentService from '@/lib/Agent';
import {
  AgentCallType,
  AgentDto,
  AgentEmotionalState,
  AgentGender,
  AgentStatus,
  AgentVoice,
} from '@/lib/Agent/types';
import { cn } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import _ from 'lodash';
import {
  Columns3,
  Loader2Icon,
  PencilIcon,
  Square,
  SquareCheck,
  UploadCloudIcon,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
import AgentTableHeader from './AgentTableHeader';
import AgentTableRow from './AgentTableRow';
import EditColumnsSelection from './EditColumnsSelection';
import { VOICE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { Agent } from 'http';
import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import EditResumeCall from './EditResumeCall';
import { AnimatePresence, motion } from 'framer-motion';
import useUserSession from '@/hooks/useUserSession';
import { randomAvatar } from '@/common/CreateBuyerForm/Main/utils';
import useAvatars from '@/hooks/useAvatars';

const Columns = [
  {
    id: 'avatar',
    header: 'Avatar',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: true, //can a user decide if this prop can be randomly generated?
    useRandomValue: true, //user's decision
  },
  {
    id: 'name',
    header: 'Full Name',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: false, //name is alway random anyway
    useRandomValue: true,
  },
  {
    id: 'gender',
    header: 'Gender',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: true,
    useRandomValue: true,
  },
  {
    id: 'voice',
    header: 'Voice',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: true,
    useRandomValue: true,
  },
  {
    id: 'jobTitle',
    header: 'Job Title',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: false,
    useRandomValue: false,
  },
  {
    id: 'companyName',
    header: 'Company',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: false,
    useRandomValue: false,
  },
  {
    id: 'callType',
    header: 'Call Type',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: true,
    useRandomValue: false,
  },
  {
    id: 'scorecard',
    header: 'Scorecard',
    visible: false,
    editable: true,
    disabled: false,
    canGenerateRandom: false,
    useRandomValue: false,
  },
  {
    id: 'resumeCall',
    header: 'Resume Call',
    visible: false,
    editable: true,
    disabled: false,
    canGenerateRandom: false,
    useRandomValue: false,
  },
  {
    id: 'callScenario',
    header: 'Scenario',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: true,
    useRandomValue: true,
  },
  {
    id: 'emotionalState',
    header: 'Emotional State',
    visible: true,
    editable: true,
    disabled: false,
    canGenerateRandom: true,
    useRandomValue: true,
  },
];

export class AgentInfo {
  id: string;
  agent: AgentDto;
  selected: boolean;
  edited: boolean;
  delete: boolean;
  isSaving: boolean;

  constructor(agent: AgentDto) {
    this.agent = agent;
    if (this.agent.id && this.agent.id != 0) {
      this.id = String(this.agent.id);
    } else {
      this.id = _.uniqueId();
    }
    this.selected = false;
    this.edited = false;
    this.delete = false;
    this.isSaving = false;
  }
}

class PageState {
  parentAgent: AgentDto;
  agents: AgentInfo[];
  allSelected: boolean;
  isOneSelected: boolean;
  canSave: boolean;
  allMarkedDelete: boolean;

  constructor(parentAgent: AgentDto, agentVariations: AgentDto[]) {
    this.parentAgent = parentAgent;
    if (agentVariations && agentVariations.length > 0) {
      this.agents = agentVariations.map((a) => {
        return new AgentInfo(a);
      });
    } else {
      this.agents = [];
    }

    this.allSelected = false;
    this.isOneSelected = false;
    this.canSave = false;
    this.allMarkedDelete = false;
  }
}

interface IBuyerVariationsEditPageProps {
  vapiId: string;
}

const variationNameEmptyError = 'Please enter a name for this variation';

export default function BuyerVariationsEditPage({
  vapiId,
}: IBuyerVariationsEditPageProps) {
  const queryClient = useQueryClient();

  const { CALL_SCENARIO_OPTIONS } = useUserSession();

  const parentAgentId = vapiId;
  let fetchAgent = true;
  let pageTitle = 'Generate Variations for Buyer';
  if (!parentAgentId || parentAgentId == '') {
    fetchAgent = false;
  }

  const { data: tmpParentAgent } = useAgent(parentAgentId, fetchAgent);
  const { data: agentVariations } = useOrgAgentVariations(
    parentAgentId,
    fetchAgent,
  );

  let parentAgent = {} as AgentDto;
  if (tmpParentAgent) {
    parentAgent = tmpParentAgent;
  }

  let needsNewVariationsName = false;
  if (parentAgent.variationName && parentAgent.variationName != '') {
    pageTitle = 'Variations: ' + parentAgent.variationName;
  } else {
    needsNewVariationsName = true;
    pageTitle =
      'Generate Variations for ' +
      parentAgent.firstName +
      ' ' +
      parentAgent.lastName;
  }

  const [numberOfVariations, setNumberOfVariations] = useState<number | ''>(10);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [pageState, setPageState] = useState<PageState>(
    new PageState(parentAgent, agentVariations || []),
  );
  const [columnsSettings, setColumnsSettings] = useState<any[]>(Columns);

  const [isSaving, setIsSaving] = useState<boolean>(false);

  const errorToastId = useRef<Id | null>(null);

  let { data: scorecardConfigOptions } = useScorecardConfigsForOrg();
  if (!scorecardConfigOptions) {
    scorecardConfigOptions = [];
  }

  const { data: avatarOptions } = useAvatars();

  useEffect(() => {
    let tmpTagName = '';
    if (parentAgent) {
      if (parentAgent.variationName && parentAgent.variationName != '') {
        tmpTagName = parentAgent.variationName;
      } else {
        tmpTagName = parentAgent.firstName + ' ' + parentAgent.lastName;
      }
    }
    setTagName(tmpTagName);
  }, [parentAgent]);

  const updateNumberOfVariations = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = e.target.value;
    if (v != '') {
      if (/^\d+$/.test(v)) {
        const vn = parseInt(v);
        if (vn < 100) {
          setNumberOfVariations(vn);
        }
      }
    } else {
      setNumberOfVariations('');
    }
  };

  /**************************************************************/
  /***************** VARIATION TAG/NAME *************************/
  /**************************************************************/

  const [editingTag, setEditingTag] = useState<boolean>(needsNewVariationsName);
  const [tagName, setTagName] = useState<string>('');
  const [tagError, setTagError] = useState<boolean>(false);
  const updateTagName = (e: React.ChangeEvent<HTMLInputElement>) => {
    const t = e.target.value;
    if (t == '') {
      setTagError(true);
      if (!needsNewVariationsName) {
        //if needsNewVariationsName = new variations => wait for the user to generate variations
        setPageState({ ...pageState, canSave: false });
      }
    } else {
      setTagError(false);
      if (!needsNewVariationsName) {
        //if needsNewVariationsName = new variations => wait for the user to generate variations
        setPageState({ ...pageState, canSave: true });
      }
    }
    setTagName(t);
  };

  /**************************************************************/
  /***************** GENERATE VARIATIONS ************************/
  /**************************************************************/

  const [showRandomizePanel, setShowRandomizaPanel] = useState<boolean>(false);

  const openRandomizeSettings = () => {
    setShowRandomizaPanel(true);
  };

  const toggleRandomizeProp = (colId: string) => {
    const tmp: any[] = columnsSettings.map((c) => {
      if (c.id == colId) {
        c.useRandomValue = !c.useRandomValue;
        return c;
      }
      return c;
    });

    setColumnsSettings([...tmp]);
  };

  const generateRandom = () => {
    setShowRandomizaPanel(false);

    setShowRandomizaPanel(false);
    setIsGenerating(true);
    setTimeout(() => {
      generateMore('random');
    }, 500);
  };

  const generateSimilar = () => {
    setIsGenerating(true);
    setTimeout(() => {
      generateMore('similar');
    }, 500);
  };

  const generateMore = async (type: string) => {
    let numb = 1;
    if (numberOfVariations != '') {
      numb = numberOfVariations;
    }

    const newAgents: AgentInfo[] = [];

    const a = { ...parentAgent, gatekeepers: [], guardedAgents: [] };

    a.id = 0;

    const editableColumns = Columns.filter((c) => c.editable).map((c) => c.id);
    const canRandomlyGenerateProp = columnsSettings
      .filter((c) => c.useRandomValue)
      .map((c) => c.id);
    let numberOfMaleNames = 0;
    let numberOfFemaleNames = 0;

    for (let i = 0; i < numb; i++) {
      const ag = new AgentInfo({ ...a });
      if (type == 'random') {
        //gender first, it affects other props
        if (
          editableColumns.indexOf('gender') > -1 &&
          canRandomlyGenerateProp.indexOf('gender') > -1
        ) {
          ag.agent.gender = getRandomGender();
        }

        if (
          editableColumns.indexOf('avatar') > -1 &&
          canRandomlyGenerateProp.indexOf('avatar') > -1
        ) {
          const avatar = getRandomAvatar(
            ag.agent.avatar || '',
            ag.agent.gender,
          );
          ag.agent.avatar = avatar;
          ag.agent.avatarUrl = avatarOptions?.[avatar];
        }

        if (
          editableColumns.indexOf('voice') > -1 &&
          canRandomlyGenerateProp.indexOf('voice') > -1
        ) {
          ag.agent.voice = getRandomVoice(ag.agent.gender);
        }

        if (
          editableColumns.indexOf('scorecard') > -1 &&
          canRandomlyGenerateProp.indexOf('scorecard') > -1
        ) {
          ag.agent.scorecardConfigId = getRandomScorecard();
        }

        if (
          editableColumns.indexOf('callType') > -1 &&
          canRandomlyGenerateProp.indexOf('callType') > -1
        ) {
          ag.agent.callType = getRandomCallType();
        }

        if (
          editableColumns.indexOf('callScenario') > -1 &&
          canRandomlyGenerateProp.indexOf('callScenario') > -1
        ) {
          ag.agent.research = getRandomScenario(ag.agent.callType);
        }
        if (
          editableColumns.indexOf('emotionalState') > -1 &&
          canRandomlyGenerateProp.indexOf('emotionalState') > -1
        ) {
          ag.agent.emotionalState = getRandomEmotionalState(ag.agent.callType);
        }
      }

      ag.selected = true;
      ag.edited = true;

      if (ag.agent.gender == AgentGender.FEMALE) {
        numberOfFemaleNames++;
      } else {
        numberOfMaleNames++;
      }
      newAgents.push(ag);
    }

    //gender was generated, now we can generate the name
    if (editableColumns.indexOf('name') > -1) {
      const names = await AgentService.getRandomNames(
        numberOfMaleNames,
        numberOfFemaleNames,
      );
      if (names) {
        newAgents.forEach((a) => {
          const rm = names[a.agent.gender].pop();
          a.agent.firstName = rm.firstName;
          a.agent.lastName = rm.lastName;
        });
      }
    }

    if (pageState.agents.length == 0) {
      pageState.allSelected = true;
    }

    if (type == 'random') {
      //random should be able to save, but verify it can save any previous edit first
      let canSave = true;
      if (pageState.agents) {
        pageState.agents.map((a) => {
          if (!canAgentBeSaved(a.agent)) {
            canSave = false;
          }
        });
      }
      pageState.canSave = canSave;
    }

    pageState.isOneSelected = true;
    pageState.agents = [...pageState.agents, ...newAgents];

    setPageState({ ...pageState });

    setNumberOfVariations(numb);
    setIsGenerating(false);
  };

  /************** RANDOMIZE FUNCTIONS  **************/

  const getRandomName = async (
    gender: AgentGender,
  ): Promise<{ firstName: string; lastName: string }> => {
    let fullName;
    if (gender == AgentGender.FEMALE) {
      const names = await AgentService.getRandomNames(0, 1);
      fullName = names[gender].pop();
    } else {
      const names = await AgentService.getRandomNames(1, 0);
      fullName = names[gender].pop();
    }
    return fullName;
  };

  const getRandomGender = () => {
    const r = Math.floor(Math.random() * AGENT_GENDER_OPTIONS.length);
    return AGENT_GENDER_OPTIONS[r].value;
  };

  const getRandomAvatar = (currentAvatar: string, gender: AgentGender) => {
    const avatars = AVATAR_OPTIONS[gender];
    let randomAvatar: string;
    do {
      randomAvatar = avatars[Math.floor(Math.random() * avatars.length)];
    } while (randomAvatar === currentAvatar);
    return randomAvatar;
  };

  const getRandomCallType = () => {
    let randomCallType: AgentCallType;
    do {
      const r = Math.floor(Math.random() * CALL_TYPE_OPTIONS.length);
      randomCallType = CALL_TYPE_OPTIONS[r].value;
    } while (
      randomCallType === AgentCallType.FOCUS ||
      randomCallType === AgentCallType.GATEKEEPER ||
      randomCallType === AgentCallType.CHECKIN ||
      randomCallType === AgentCallType.RENEWAL
    );
    return randomCallType;
  };

  const getRandomVoice = (gender: AgentGender): AgentVoice => {
    const options = VOICE_OPTIONS[gender];
    const r = Math.floor(Math.random() * options.length);
    return options[r].value;
  };

  const getRandomScorecard = (attempt = 0): number | undefined => {
    const r = Math.floor(Math.random() * scorecardConfigOptions.length);
    if (scorecardConfigOptions[r].id) {
      return scorecardConfigOptions[r].id;
    } else {
      if (attempt > 5) {
        return undefined;
      } else {
        attempt++;
        return getRandomScorecard(attempt);
      }
    }
  };

  const getRandomScenario = (callType: AgentCallType) => {
    const options =
      CALL_SCENARIO_OPTIONS[callType as keyof typeof CALL_SCENARIO_OPTIONS];
    const r = Math.floor(Math.random() * options.length);
    const randomScenario = options[r].value;

    let callScenario_Cold = 'None - Default';
    let callScenario_Worm = 'None - Default';
    let callScenario_Discovery = 'None - Default';

    if (callType == AgentCallType.COLD) {
      callScenario_Cold = randomScenario;
    } else if (callType == AgentCallType.WARM) {
      callScenario_Worm = randomScenario;
    } else if (callType == AgentCallType.DISCOVERY) {
      callScenario_Discovery = randomScenario;
    }

    return JSON.stringify({
      public_presence: '',
      incumbent_solution_info: '',
      problem_aware_info: '',
      solution_aware_info: '',
      pre_existing_champion_info: '',
      cold_call_scenario: callScenario_Cold,
      warm_call_scenario: callScenario_Worm,
      discovery_call_scenario: callScenario_Discovery,
      discovery_call_context: '',
      warm_call_context: '',
    });
  };

  const getRandomEmotionalState = (callType: AgentCallType) => {
    const options =
      AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
        callType as keyof typeof AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE
      ];
    const r = Math.floor(Math.random() * options.length);
    return options[r].value;
  };

  /**************************************************************/
  /*************************** TOGGELS **************************/
  /**************************************************************/

  const toggleAgent = (id: string) => {
    let allSelected = true;
    let isOneSelected = false;
    const agents = pageState.agents.map((a) => {
      if (a.id == id) {
        a.selected = !a.selected;
      }
      if (!a.delete) {
        if (!a.selected) {
          allSelected = false;
        }
        if (a.selected) {
          isOneSelected = true;
        }
      }

      return a;
    });

    pageState.allSelected = allSelected;
    pageState.agents = [...agents];
    pageState.isOneSelected = isOneSelected;
    setPageState({ ...pageState });
  };

  const toggleAllAgents = () => {
    pageState.allSelected = !pageState.allSelected;
    pageState.isOneSelected = pageState.allSelected;
    const agents = pageState.agents.map((a) => {
      if (!a.delete) {
        a.selected = pageState.allSelected;
      }
      return a;
    });
    pageState.agents = [...agents];
    setPageState({ ...pageState });
  };

  const toggleDeleteAllAgents = () => {
    pageState.allMarkedDelete = !pageState.allMarkedDelete;
    const agents = pageState.agents.map((a) => {
      a.delete = pageState.allMarkedDelete;
      return a;
    });
    if (pageState.allMarkedDelete) {
      pageState.allSelected = false;
    }
    pageState.agents = [...agents];
    setPageState({ ...pageState });
  };

  const toggleDelete = (id: string) => {
    let allSelected = true;
    let allMarkedDelete = true;
    let isOneSelected = false;
    let canSave = true;
    const agents = pageState.agents.map((a) => {
      if (a.id == id) {
        a.delete = !a.delete;
      }
      if (!a.delete) {
        allMarkedDelete = false;

        if (!a.selected) {
          allSelected = false;
        }
        if (a.selected) {
          isOneSelected = true;
        }
      }

      if (!canAgentBeSaved(a.agent)) {
        canSave = false;
      }

      return a;
    });

    pageState.allSelected = allSelected;
    pageState.agents = [...agents];
    pageState.isOneSelected = isOneSelected;
    pageState.allMarkedDelete = allMarkedDelete;
    pageState.canSave = canSave;
    setPageState({ ...pageState });
  };

  /**************************************************************/
  /************************ UPDATE VAUES ************************/
  /**************************************************************/

  const updateSingleAgent = (
    id: string,
    propName: string,
    newValue: string | AgentCallType | AgentEmotionalState,
  ) => {
    updateValues(false, id, propName, newValue);
  };

  const updateSelectedAgents = (
    propName: string,
    newValue:
      | string
      | AgentCallType
      | AgentEmotionalState
      | AgentVoice
      | number
      | { [key in AgentCallType]: string },
  ) => {
    updateValues(true, '', propName, newValue);
  };

  const canAgentBeSaved = (a: AgentDto) => {
    //a.companyName == '' ||
    if (
      a.avatar == '' ||
      a.jobTitle == '' ||
      !a.callType ||
      !a.emotionalState ||
      !a.research
    ) {
      return false;
    }
    return true;
  };

  const [showGenderDialog, setShowGenderDialog] = useState<boolean>(false);
  const updateValuesProps = useRef<{
    selected: boolean;
    id: string;
    propName: string;
    newValue:
      | string
      | AgentCallType
      | AgentEmotionalState
      | AgentGender
      | AgentVoice
      | number
      | { [key in AgentCallType]: string };
  } | null>(null);

  const closeGenderDialog = (updateRelatedInfo: boolean) => {
    if (updateValuesProps.current) {
      updateValues(
        updateValuesProps.current?.selected,
        updateValuesProps.current.id,
        updateValuesProps.current.propName,
        updateValuesProps.current.newValue,
        false,
        updateRelatedInfo,
      );
    }
    setShowGenderDialog(false);
  };

  const updateValues = async (
    selected: boolean,
    id: string,
    propName: string,
    newValue:
      | string
      | AgentCallType
      | AgentEmotionalState
      | AgentGender
      | { [key in AgentCallType]: string }
      | AgentVoice
      | number,
    askGenderConfirmation = true,
    updateInfoAccordingToGender = false,
  ) => {
    if (askGenderConfirmation) {
      if (propName == 'gender') {
        //show dialog to user
        setShowGenderDialog(true);
        updateValuesProps.current = { selected, id, propName, newValue };
      }
    }
    let canSave = true;
    const agents = [];

    for (let i = 0; i < pageState.agents.length; i++) {
      const a = pageState.agents[i];

      let go = false;
      if (selected && a.selected) {
        go = true;
      } else if (!selected && a.id == id) {
        go = true;
      }

      if (go) {
        const avatar = getRandomAvatar(a.agent.avatar, a.agent.gender);
        switch (propName) {
          case 'avatar':
            a.agent.avatar = avatar || '';
            a.agent.avatarUrl = avatarOptions?.[avatar];
            break;
          case 'jobTitle':
            a.agent.jobTitle = newValue as string;
            break;
          case 'name':
            if (newValue === '-random-') {
              const fullName = await getRandomName(a.agent.gender);
              a.agent.firstName = fullName.firstName;
              a.agent.lastName = fullName.lastName;
            } else {
              const fullName = newValue as string;
              const names = fullName.split(' ');
              a.agent.firstName = names[0] || '';
              a.agent.lastName = names.slice(1).join(' ') || '';
            }
            break;
          case 'gender':
            if (newValue === '-random-') {
              a.agent.gender = getRandomGender();
            } else {
              a.agent.gender = newValue as AgentGender;
            }

            if (updateInfoAccordingToGender) {
              const avatar = getRandomAvatar(a.agent.avatar, a.agent.gender);
              a.agent.avatar = avatar;
              a.agent.avatarUrl = avatarOptions?.[avatar];
              a.agent.voice = getRandomVoice(a.agent.gender);
              const fullName = await getRandomName(a.agent.gender);
              a.agent.firstName = fullName.firstName;
              a.agent.lastName = fullName.lastName;
            }
            break;
          case 'voice':
            if (newValue === '-random-') {
              a.agent.voice = getRandomVoice(a.agent.gender);
            } else {
              const options = VOICE_OPTIONS[a.agent.gender];
              let ok = false;
              options.map((v) => {
                if (v.value == newValue) {
                  ok = true;
                }
              });
              if (ok) {
                a.agent.voice = newValue as AgentVoice;
              }
            }
            break;
          case 'scorecard':
            if (newValue === '-random-') {
              a.agent.scorecardConfigId = getRandomScorecard();
            } else {
              a.agent.scorecardConfigId = newValue as number;
            }
            break;
          case 'resumeCall': {
            const tmp = JSON.parse(a.agent.research || '{}');
            tmp.messages = newValue as string;
            a.agent.research = JSON.stringify(tmp);
            break;
          }
          case 'companyName':
            a.agent.companyName = newValue as string;
            break;
          case 'callType':
            if (newValue === '-random-') {
              a.agent.callType = getRandomCallType();
            } else {
              a.agent.callType = newValue as AgentCallType;
            }

            //clean up the scenario (the calltype changed)
            //a.agent.research = getDefafultsCallScenario();

            //switching from defaults to random:
            a.agent.research = getRandomScenario(a.agent.callType);
            break;
          case 'callScenario':
            if (newValue === '-random-') {
              a.agent.research = getRandomScenario(a.agent.callType);
            } else {
              let newScenario = newValue;
              if (selected) {
                newScenario = (newValue as { [key in AgentCallType]: string })[
                  a.agent.callType
                ];
              }

              const options =
                CALL_SCENARIO_OPTIONS[
                  a.agent.callType as keyof typeof CALL_SCENARIO_OPTIONS
                ];
              options.map((ct) => {
                if (ct.value == newScenario) {
                  let callScenario_Cold = 'None - Default';
                  let callScenario_Worm = 'None - Default';
                  let callScenario_Discovery = 'None - Default';

                  if (a.agent.callType == AgentCallType.COLD) {
                    callScenario_Cold = ct.value;
                  } else if (a.agent.callType == AgentCallType.WARM) {
                    callScenario_Worm = ct.value;
                  } else if (a.agent.callType == AgentCallType.DISCOVERY) {
                    callScenario_Discovery = ct.value;
                  }

                  const tmp = JSON.parse(a.agent.research || '{}');

                  tmp.cold_call_scenario = callScenario_Cold;
                  tmp.warm_call_scenario = callScenario_Worm;
                  tmp.discovery_call_scenario = callScenario_Discovery;

                  a.agent.research = JSON.stringify(tmp);
                }
              });
            }
            break;
          case 'emotionalState':
            if (newValue === '-random-') {
              a.agent.emotionalState = getRandomEmotionalState(
                a.agent.callType,
              );
            } else {
              let newEmotionalState = newValue;
              if (selected) {
                newEmotionalState = (
                  newValue as { [key in AgentCallType]: string }
                )[a.agent.callType];
              }
              a.agent.emotionalState = newEmotionalState as AgentEmotionalState;
            }
            break;
          default:
            console.log('Unknown property: ' + propName);
        }

        a.selected = true;
        a.edited = true;

        if (!canAgentBeSaved(a.agent)) {
          canSave = false;
        }
      }

      agents.push({ ...a });
    }

    pageState.isOneSelected = true;
    pageState.agents = agents;
    pageState.canSave = canSave;
    setPageState({ ...pageState });
  };

  const getDefafultsCallScenario = () => {
    const callScenario_Cold = 'None - Default';
    const callScenario_Worm = 'None - Default';
    const callScenario_Discovery = 'None - Default';

    return JSON.stringify({
      public_presence: '',
      incumbent_solution_info: '',
      problem_aware_info: '',
      solution_aware_info: '',
      pre_existing_champion_info: '',
      cold_call_scenario: callScenario_Cold,
      warm_call_scenario: callScenario_Worm,
      discovery_call_scenario: callScenario_Discovery,
      discovery_call_context: '',
      warm_call_context: '',
    });
  };
  /**************************************************************/
  /************************ RESUME CALL *************************/
  /**************************************************************/

  const [editResumeCallPnlStatus, setEditResumeCallPnlStatus] =
    useState<boolean>(false);
  const [editResumeCallAgent, setEditResumeCallAgent] = useState<AgentInfo>();
  const [edititingMultipleResumeCall, setEdititingMultipleResumeCall] =
    useState<boolean>(false);

  const openResumeCallEditAll = () => {
    setEdititingMultipleResumeCall(true);
    setEditResumeCallPnlStatus(true);
  };

  const openResumeCallEdit = (id: string) => {
    setEdititingMultipleResumeCall(false);
    const agent = pageState.agents.find((a) => a.id == id);
    if (agent) {
      setEditResumeCallAgent(agent);
      setEditResumeCallPnlStatus(true);
    }
  };

  const closeEditResumeCallPnl = () => {
    setEditResumeCallPnlStatus(false);
  };

  const updateResumeCallTranscript = (newValue: string, isMulti: boolean) => {
    closeEditResumeCallPnl();
    if (isMulti) {
      updateSelectedAgents('resumeCall', newValue);
    } else if (editResumeCallAgent) {
      updateSingleAgent(editResumeCallAgent.id, 'resumeCall', newValue);
    }
  };

  /**************************************************************/
  /************************ SAVE TO DB **************************/
  /**************************************************************/

  const updateAgentMutation = useMutation({
    mutationFn: AgentService.updateAgent,
  });

  const updateAgentVariationNameMutation = useMutation({
    mutationFn: AgentService.updateVariationName,
  });

  const createAgentMutation = useMutation({
    mutationFn: AgentService.createAgent,
  });

  const [savingProgress, setSavingProgress] = useState<number>(0);
  const agentsToBeSaved = useRef<number>(0);

  const saveNewVariationName = async (showconfirmationMessage = false) => {
    setTagError(false);
    if (tagName == '') {
      setTagError(true);
      errorToastId.current = toast.error(variationNameEmptyError);
      return false;
    }

    await updateAgentVariationNameMutation.mutateAsync({
      agentId: parentAgent.id,
      newVariationName: tagName,
    });

    if (showconfirmationMessage) {
      errorToastId.current = toast.success('Name successfuly updated');
    }

    return true;
  };

  const save = async () => {
    agentsToBeSaved.current = 0;
    let canSave = true;
    pageState.agents.map((a) => {
      if (a.selected || a.delete) {
        agentsToBeSaved.current++;
        if (!canAgentBeSaved(a.agent)) {
          canSave = false;
        }
      }
    });

    if (!canSave) {
      errorToastId.current = toast.success(
        'Please verify all fields are filled in correctly before saving.',
      );
      return;
    }

    canSave = await saveNewVariationName();
    if (!canSave) {
      return;
    }

    setSavingProgress(0);
    setIsSaving(true);

    //mark the parent agent:
    parentAgent.variationParentAgentId = parentAgent.id;
    parentAgent.variationName = tagName;
    let somethingWrong = false;
    try {
      await updateAgentMutation.mutateAsync({ ...parentAgent });
    } catch (e) {
      somethingWrong = true;
      console.log(e);
      if (!toast.isActive(errorToastId.current as Id)) {
        errorToastId.current = toast.error(
          'There was an error saving. Please try again.',
        );
      }
    }

    const progressStep = Math.floor(100 / agentsToBeSaved.current);

    if (agentsToBeSaved.current > 0 && !somethingWrong) {
      //save all agents:
      const tmp: AgentInfo[] = [];

      for (const a of pageState.agents) {
        if (a.selected || a.delete) {
          if (a.delete) {
            if (a.agent.id > 0) {
              //delete
              await updateAgentMutation.mutateAsync({
                id: a.agent.id,
                status: AgentStatus.INACTIVE,
              });
              setSavingProgress((o) => {
                return o + progressStep;
              });
            } else {
              //nothing to do here, just remove from the array => do not push into tmp and it will dissapear
            }
          } else {
            let saved = false;
            a.agent.variationParentAgentId = parentAgent.id;
            a.agent.variationName = tagName;

            if (a.agent.id > 0) {
              //update
              try {
                await updateAgentMutation.mutateAsync({ ...a.agent });
                saved = true;
              } catch (e) {
                if (!toast.isActive(errorToastId.current as Id)) {
                  errorToastId.current = toast.error(
                    'There was an error saving. Please try again.',
                  );
                }
              }
            } else {
              //create
              const agent: any = { ...a.agent };
              delete agent.id;
              delete agent.createdAt;
              delete agent.updatedAt;
              delete agent.vapiId;
              try {
                const newAgent = await createAgentMutation.mutateAsync({
                  ...agent,
                });
                if (newAgent && newAgent.id > 0) {
                  a.agent = newAgent;
                  saved = true;
                }
              } catch (e) {
                if (!toast.isActive(errorToastId.current as Id)) {
                  errorToastId.current = toast.error(
                    'There was an error saving. Please try again.',
                  );
                }
              }
            }

            if (saved) {
              a.edited = false;
              a.selected = false;
              tmp.push(a);
              setSavingProgress((o) => {
                return o + progressStep;
              });
            }
          }
        } else {
          tmp.push(a);
        }
      }

      queryClient.invalidateQueries({ queryKey: ['orgAgentVariations'] });

      pageState.isOneSelected = false;
      pageState.allSelected = false;
      pageState.agents = [...tmp];
      setPageState({ ...pageState });
    }

    setIsSaving(false);
    setEditingTag(false);
  };

  /**************************************************************/
  /************************ RENDER PAGE *************************/
  /**************************************************************/

  //utility function used by the header
  const getSelectedCallTypes = () => {
    const selectedCallTypes: AgentCallType[] = [];
    pageState.agents.map((a) => {
      if (a.selected) {
        if (selectedCallTypes.indexOf(a.agent.callType) == -1) {
          selectedCallTypes.push(a.agent.callType);
        }
      }
    });
    return selectedCallTypes;
  };

  return (
    <div className="overflow-hidden">
      {needsNewVariationsName ? (
        <DashboardNavbar
          breadcrumbs={[{ title: 'Buyer Bots' }, { title: pageTitle }]}
        />
      ) : (
        <DashboardNavbar
          breadcrumbs={[{ title: 'Buyer Bots' }, { title: pageTitle || '' }]}
          titleAsContent={
            <div className="flex items-center space-x-2">
              <h2 className="text-xl font-semibold">{pageTitle}</h2>
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <PencilIcon
                      className="cursor-pointer"
                      size={18}
                      onClick={() => {
                        setEditingTag((old) => !old);
                      }}
                    />
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p>Edit variations name</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          }
        />
      )}

      <div className="mx-4 my-6 flex items-center">
        <div className="mr-2">Generate</div>
        <Input
          className="w-14 mr-2"
          value={numberOfVariations}
          onChange={updateNumberOfVariations}
        />
        <div className="mr-2">
          variations of {parentAgent?.firstName + ' ' + parentAgent?.lastName}
        </div>
        <div className="mr-2">
          <Button
            variant="outline"
            disabled={isGenerating}
            onClick={generateSimilar}
          >
            Duplicate
          </Button>
        </div>
        <div className="mr-2">or</div>
        <div className="mr-2">
          <Button
            variant="outline"
            disabled={isGenerating}
            onClick={openRandomizeSettings}
          >
            Randomize
          </Button>
        </div>
        {isGenerating && <Loader2Icon className="animate-spin" />}
        <div className="flex-1"></div>
        {editingTag && (
          <>
            <div>Variations Name:</div>
            <Input
              className={cn('w-42 ml-2 mr-1', { 'border-red-500': tagError })}
              value={tagName}
              onChange={updateTagName}
            />
            <div className="w-[2px] bg-gray-100 ml-2 mr-2 h-[34px] rounded-full"></div>
          </>
        )}
        <div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant={'outline'} className="text-xs">
                <Columns3 className="mr-1" />
                Show Columns
              </Button>
            </PopoverTrigger>
            <PopoverContent align="end">
              <EditColumnsSelection
                columns={columnsSettings}
                updateColumnsSettings={setColumnsSettings}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <AnimatePresence>
        <motion.div
          animate={{
            height: showRandomizePanel ? 'auto' : 0,
            opacity: showRandomizePanel ? 1 : 0,
          }}
          initial={{ height: 0, opacity: 0 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{
            duration: 0.2,
            delay: 0,
          }}
        >
          <div className="mx-4 mb-6 border rounded-lg overflow-hidden">
            <div className="font-semibold p-4 text-base">
              Please choose which features you&apos;d like the system to
              randomly generate for you.
            </div>

            <div className="grid grid-cols-3 px-4">
              {columnsSettings.map((c) => {
                if (!c.diabled && c.canGenerateRandom) {
                  return (
                    <div
                      key={'ecs-' + c.id}
                      className="flex items-center hover:bg-muted/80 p-2 cursor-pointer rounded-md"
                      onClick={() => {
                        toggleRandomizeProp(c.id);
                      }}
                    >
                      <div className="mr-1">
                        {c.useRandomValue ? (
                          <SquareCheck size={18} />
                        ) : (
                          <Square size={18} />
                        )}
                      </div>
                      <div className="text-sm flex-1">{c.header}</div>
                    </div>
                  );
                }
              })}
            </div>

            <div className="flex items-center p-4">
              <div className="text-muted-foreground mr-4">
                Please note that values of unselected features will be simply
                copied from the original bot. Name is always randomly generated.
              </div>
              <div className="flex-1"></div>
              <Button
                variant="outline"
                disabled={isGenerating}
                onClick={() => {
                  setShowRandomizaPanel(false);
                }}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button disabled={isGenerating} onClick={generateRandom}>
                Randomize
              </Button>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
      <div className="mx-4">
        <Table>
          <TableHeader>
            <AgentTableHeader
              columns={columnsSettings}
              isOneSelected={pageState.isOneSelected}
              updateSelected={updateSelectedAgents}
              getSelectedCallTypes={getSelectedCallTypes}
              numberOfAgents={pageState.agents.length}
              allAgentsSelected={pageState.allSelected}
              toggleAllAgents={toggleAllAgents}
              allAgentsDeleted={pageState.allMarkedDelete}
              toggleDeleteAllAgents={toggleDeleteAllAgents}
              openResumeCallEditAll={openResumeCallEditAll}
            />
          </TableHeader>
          <TableBody>
            <AgentTableRow
              columns={columnsSettings}
              info={new AgentInfo(parentAgent)}
              isHeaderAgent={true}
              openResumeCallEdit={() => {}}
            />
            {pageState.agents.map((agent) => {
              return (
                <AgentTableRow
                  columns={columnsSettings}
                  key={'row-' + agent.id}
                  info={agent}
                  toggleAgent={toggleAgent}
                  toggleDelete={toggleDelete}
                  updateValue={updateSingleAgent}
                  openResumeCallEdit={openResumeCallEdit}
                />
              );
            })}
          </TableBody>
        </Table>
      </div>
      <div className="mx-4 my-6 flex items-center">
        {isSaving && (
          <div className="w-[60%]">
            <ProgressCustom value={savingProgress} className="w-[60%]" />
          </div>
        )}
        <div className="flex-1"></div>
        <Button
          disabled={!pageState.canSave || isSaving}
          className={cn('', {
            'pointer-events-none': !pageState.canSave,
          })}
          variant={'default'}
          onClick={save}
        >
          {isSaving ? (
            <>
              Publish <Loader2Icon className="animate-spin ml-2" />
            </>
          ) : (
            <>
              Publish <UploadCloudIcon className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>

      <EditResumeCall
        open={editResumeCallPnlStatus}
        close={closeEditResumeCallPnl}
        save={updateResumeCallTranscript}
        agentInfo={editResumeCallAgent}
        isMulti={edititingMultipleResumeCall}
      />

      {/* DIALOG FOR UPDATING NAME AND AVAT WITH GENDER */}
      <Dialog open={showGenderDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit gender</DialogTitle>
            <DialogDescription>
              Randomly update name, avatar, and voice according to the selected
              gender?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant={'secondary'}
              onClick={() => {
                closeGenderDialog(false);
              }}
            >
              No
            </Button>
            <Button
              onClick={() => {
                closeGenderDialog(true);
              }}
            >
              Yes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
