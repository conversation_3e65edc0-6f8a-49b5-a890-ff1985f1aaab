import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Trash2 } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { AgentInfo } from '..';

interface IProps {
  open: boolean;
  close: () => void;
  save: (newValue: string, isMulti: boolean) => void;
  agentInfo?: AgentInfo;
  isMulti: boolean;
}

export default function EditResumeCall({
  open,
  close,
  save,
  agentInfo,
  isMulti,
}: IProps) {
  const [resumeCalls, setResumeCalls] = useState<string[]>([]);

  useEffect(() => {
    let cm: string[] = [];
    if (agentInfo && agentInfo.agent.research && !isMulti) {
      const tmp = JSON.parse(agentInfo.agent.research);
      if (tmp.messages) {
        cm = tmp.messages.split('\n');
      } else {
        cm.push(''); //to support new
      }
    } else {
      cm.push(''); //to support new
    }
    setResumeCalls(cm);
  }, [agentInfo, isMulti]);

  const fakeFocus = useRef<HTMLTextAreaElement>(null);
  useEffect(() => {
    if (open) {
      setTimeout(() => {
        if (fakeFocus.current && open) {
          fakeFocus.current.focus();
        } else {
          console.log(1);
        }
      }, 200);
    }
  }, [open]);

  const [errorMessage, setErrorMessage] = useState('');

  const onSave = () => {
    setErrorMessage('');
    let go = true;
    let resume_calls_message = '';
    if (resumeCalls.length > 1) {
      if (resumeCalls.length % 2 === 0) {
        const tmp = [...resumeCalls];
        tmp.pop();
        for (const m of tmp) {
          if (m != '') {
            resume_calls_message += m + '\n';
          } else {
            go = false;
          }
        }
        if (!go) {
          setErrorMessage('Resume calls contains empty messages.');
        }
      } else {
        setErrorMessage(
          'Resume calls must end with a buyer message. Please add a buyer message to the end of the list.',
        );
        go = false;
      }
    }

    if (go) {
      save(resume_calls_message, isMulti);
    }
  };

  return (
    <Dialog open={open}>
      <DialogContent className="w-[50%]">
        <DialogHeader>
          <DialogTitle>Edit Resume Call</DialogTitle>
          <DialogDescription>
            {isMulti
              ? 'Edit transcript of a call up to a specific point for all selected buyers.'
              : 'Edit transcript of a call up to a specific point.'}
          </DialogDescription>
        </DialogHeader>
        <div className="max-h-[420px] overflow-auto">
          {resumeCalls.map((s, i) => {
            const isAssistant = i % 2 === 0;
            let label = 'Rep';
            let bg = 'bg-muted/40';
            if (isAssistant) {
              label = 'Buyer';
              bg = 'bg-muted';
            }

            let isLast = false;
            if (i == resumeCalls.length - 1) {
              isLast = true;
            }
            let opacity = '';
            if (isLast) {
              opacity = 'opacity-50';
            }

            return (
              <div
                key={i}
                className={
                  'flex items-center border rounded-lg p-2 mb-2 ' +
                  opacity +
                  ' ' +
                  bg
                }
                onClick={(e) => {
                  if (isLast) {
                    resumeCalls.push('');
                    setResumeCalls([...resumeCalls]);
                  }
                }}
              >
                <div className="w-[50px] text-xs text-muted-foreground ml-2">
                  {label}
                </div>
                <div className="flex-1 h-[80px]">
                  <textarea
                    id={'txt-' + i}
                    className="outline-none resize-none p-2 rounded-lg w-full h-[80px] box-border"
                    value={s}
                    onChange={(e) => {
                      resumeCalls[i] = e.target.value;
                      setResumeCalls([...resumeCalls]);
                    }}
                    onKeyDown={(evt: any) => {
                      const keyCode = evt.keyCode;
                      if (keyCode === 13) evt.preventDefault(); //enter
                      if (keyCode === 9) {
                        evt.preventDefault(); //tab
                        const next = i + 1;
                        if (next == resumeCalls.length - 1) {
                          resumeCalls.push('');
                          setResumeCalls([...resumeCalls]);
                        }
                        const tmp = document.getElementById('txt-' + next);
                        if (tmp) {
                          tmp.focus();
                        }
                      }
                    }}
                  />
                </div>
                <div>
                  <Button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (!isLast) {
                        resumeCalls.splice(i, 1);
                        setResumeCalls([...resumeCalls]);
                      }
                    }}
                    variant="outline"
                    className="ml-2 text-muted-foreground border-0"
                  >
                    <Trash2 size={18} />
                  </Button>
                </div>
              </div>
            );
          })}
          <textarea ref={fakeFocus} className="h-0 w-0" />
        </div>
        <DialogFooter>
          <div className="text-red-400 text-sm mt-2">{errorMessage}</div>
          <div className="flex-1"></div>
          <Button
            variant={'secondary'}
            onClick={() => {
              close();
            }}
          >
            Cancel
          </Button>
          <Button onClick={onSave}>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
