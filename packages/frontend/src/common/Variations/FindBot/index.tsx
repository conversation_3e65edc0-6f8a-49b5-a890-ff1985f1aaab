import { useState, useRef } from 'react';
import DashboardNavbar from '@/common/DashboardNavbar';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import useOrgAgents from '@/hooks/useOrgAgents';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  CALL_TYPE_OPTIONS,
  AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE,
} from '@/common/CreateBuyerForm/constants';
import { BrainIcon } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import AgentAvatar from '@/components/Avatars/Agent';

interface IFindBotProps {
  onBotSelected: (vapiId: string) => void;
}

export default function FindBot({ onBotSelected }: IFindBotProps) {
  const resultsMultiplier = 10;
  const [searchString, setSearchString] = useState<string>('');
  const [numberOfResults, setNumberOfResults] =
    useState<number>(resultsMultiplier);

  const { data: agents, isLoading: isLoading } = useOrgAgents(
    undefined,
    true,
    0,
    numberOfResults,
    searchString,
  );

  let noMoreAgents = false;

  if (agents) {
    if (agents.length > 0) {
      if (agents.length < numberOfResults) {
        noMoreAgents = true;
      }
    }
  }

  const loadMore = () => {
    setNumberOfResults(numberOfResults + resultsMultiplier);
  };

  const selectBot = (vapiId: string) => {
    if (onBotSelected) {
      onBotSelected(vapiId);
    }
  };

  const [searchLabel, setSearchLabel] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);

  const filterResults = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      setNumberOfResults(resultsMultiplier);
      setSearchString(s);
    }, 200);

    setSearchLabel(s);
  };

  return (
    <div>
      <DashboardNavbar
        breadcrumbs={[{ title: 'Buyer Bots' }, { title: 'Variations' }]}
      />
      <div className="pt-4 px-8">
        <Card className="w-[450px] mb-4">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle>Variations</CardTitle>
                <CardDescription className="mt-1 w-80">
                  With just one click, create numerous variations of any bot to
                  explore different scenarios.
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm font-medium leading-none mb-2">
              Select a bot you want to use as base:
            </p>
            <Input
              placeholder="Search by name, job title, or company..."
              value={searchLabel}
              onChange={(e) => filterResults(e.target.value)}
              className="max-w-md"
            />

            <div className="mt-2">
              {agents?.map((agent) => {
                //exclude variations:
                if (
                  !agent.variationParentAgentId ||
                  agent.variationParentAgentId === agent.id
                ) {
                  const callType = CALL_TYPE_OPTIONS.find(
                    (ct) => ct.value == agent.callType,
                  )?.value;
                  let emotionalState;
                  if (callType) {
                    emotionalState =
                      AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE[
                        callType as keyof typeof AGENT_EMOTIONAL_STATE_OPTIONS_PER_CALL_TYPE
                      ].find((ct) => ct.value == agent.emotionalState)?.label;
                  }

                  if (!emotionalState) {
                    emotionalState = '';
                  }

                  return (
                    <Card
                      key={agent.id}
                      className="shadow-sm transition-shadow duration-300 cursor-pointer hover:shadow-xl mb-2"
                      onClick={() => {
                        selectBot(agent.vapiId);
                      }}
                    >
                      <CardHeader>
                        <div className="flex space-x-2 items-center">
                          <AgentAvatar className="w-14 h-14" agent={agent} />
                          <div>
                            <CardTitle>
                              {agent.firstName} {agent.lastName}
                            </CardTitle>
                            <CardDescription className="text-[0.76rem]">
                              {agent.jobTitle} {'@  ' + agent.companyName}
                            </CardDescription>
                            <div className="flex mt-1 space-x-2 items-center">
                              <div>
                                {agent.callType && (
                                  <Badge variant="secondary">{callType}</Badge>
                                )}
                              </div>
                              <div>
                                <Badge variant="default">
                                  <BrainIcon className="w-3 h-3 mr-1" />{' '}
                                  {emotionalState}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  );
                }
              })}
            </div>
            {!noMoreAgents && (
              <Button className="w-full" variant="ghost" onClick={loadMore}>
                Load More...
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
