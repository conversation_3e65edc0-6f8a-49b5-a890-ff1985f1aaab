import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import LinksManager from '@/lib/linksManager';
import { cn } from '@/lib/utils';
import dayjs from 'dayjs';
import { SquareArrowOutUpRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface IProps {
  call: any;
  className?: string;
}

export default function CallBox({ call, className }: IProps) {
  const router = useRouter();

  const n = call.callerFullName?.split(' ') || [];
  const fn = n[0] || '';
  const ln = n[1] || '';
  let minutes = '00';
  let seconds = '00';
  const _minutes = Math.floor(call.durationSeconds / 60);
  const _seconds = Math.trunc(call.durationSeconds - _minutes * 60);
  if (_minutes < 10 && _minutes >= 0) {
    minutes = '0' + String(_minutes);
  } else {
    minutes = String(_minutes);
  }
  if (_seconds < 10 && _seconds >= 0) {
    seconds = '0' + String(_seconds);
  } else {
    seconds = String(_seconds);
  }

  return (
    <div
      className={cn(
        'flex items-center border rounded-lg p-3 cursor-pointer hover:bg-gray-50',
        className,
      )}
      onClick={() => {
        router.push(LinksManager.trainingCalls(call.vapiId));
      }}
    >
      <div className="mr-2">
        <Avatar className="w-6 h-6">
          <AvatarImage src={`${call.callerAvatar}`} />
          <AvatarFallback>
            {fn.charAt(0) || ''}
            {ln.charAt(0) || ''}
          </AvatarFallback>
        </Avatar>
      </div>
      <div className="font-medium mr-4">{call.callerFullName}</div>
      <div className="flex-1 text-muted-foreground">
        {minutes}:{seconds}
      </div>
      <div className="text-muted-foreground mr-3">
        {dayjs(call.createdAt).format('MMM D')}
      </div>
      <div className="text-muted-foreground">
        <SquareArrowOutUpRight size={16} />
      </div>
    </div>
  );
}
