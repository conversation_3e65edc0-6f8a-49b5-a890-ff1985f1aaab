import useUserImprovements from '@/hooks/useUserImprovements';
import { cn } from '@/lib/utils';
import { Loader2Icon, SparklesIcon } from 'lucide-react';
import { ProgressCircle } from '@tremor/react';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import CallBox from './callBox';
import { AnimatePresence, motion } from 'framer-motion';
interface IProps {
  className?: string;
}

export default function Improvements({ className }: IProps) {
  const { data: improvements, isLoading } = useUserImprovements();
  const [selectedImprovement, setSelectedImprovement] = useState<any>();

  useEffect(() => {
    if (!isLoading && improvements && improvements.length > 0) {
      setSelectedImprovement(improvements[0]);
    }
  }, [improvements, isLoading]);

  return (
    <div className={className}>
      <div className="font-semibold text-base flex items-center text-teal-600">
        <SparklesIcon className="w-4 h-4 mr-1 text-teal-600" />
        Improvements
        {isLoading && (
          <div className="ml-2 flex items-center justify-center">
            <Loader2Icon className="animate-spin" size={20} />
          </div>
        )}
      </div>
      <div className="mt-6">
        {!(improvements && improvements.length > 0) && (
          <div className="border rounded-lg p-10 flex flex-col items-center justify-center bg-white">
            <div className="bg-gray-100 rounded-full p-2 flex items-center justify-center">
              <SparklesIcon className="w-4 h-4 text-teal-600 " />
            </div>
            <div className="mt-6 font-medium text-center">
              Hyperbound AI Coach needs more data before providing suggestions
              for improvement.
            </div>
            <div className="mt-6 text-muted-foreground w-[600px] text-center">
              Once you complete more roleplays, Hyperbound AI will find areas of
              weakness, provide strategies (or suggested roleplays) to address
              them, and suggest successful calls made by other members in your
              organization.
            </div>
          </div>
        )}

        {improvements && improvements.length > 0 && (
          <AnimatePresence>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="mt-3 flex items-stretch"
            >
              <div className="w-[50%] mr-3 ">
                <div className="bg-white rounded-lg border h-full flex flex-col py-2 pl-2">
                  {improvements.map((imp: any, i: number) => {
                    let addMargin = true;
                    if (i == improvements.length - 1) {
                      addMargin = false;
                    }
                    let selected = false;
                    if (selectedImprovement) {
                      if (selectedImprovement.criterion == imp.criterion) {
                        selected = true;
                      }
                    }
                    return (
                      <div
                        key={'imp-' + i}
                        className={cn(
                          'bg-white group flex items-stretch flex-1 ',
                          {
                            'mb-3': addMargin,
                          },
                        )}
                        onClick={() => {
                          setSelectedImprovement(imp);
                        }}
                      >
                        <div
                          className={cn(
                            'flex items-start flex-1 mr-2 rounded-lg border cursor-pointer p-3 group-hover:bg-gray-50',
                            {
                              'bg-gray-50': selected,
                            },
                          )}
                        >
                          <div className="flex items-start relative">
                            <ProgressCircle
                              color={'teal'}
                              value={imp.score}
                              size="sm"
                            >
                              <span className="text-xs text-gray-700">
                                {imp.score}%
                              </span>
                            </ProgressCircle>
                          </div>
                          <div className="flex-1 ml-3">
                            <div className="flex-1">
                              Practice more{' '}
                              <span className="font-semibold">
                                &quot;{imp.criterion}&quot; - {imp.score}%
                              </span>
                            </div>
                            <div className="text-xs text-muted-foreground mt-3 mr-5">
                              {imp.message}{' '}
                            </div>
                          </div>
                        </div>
                        <div
                          className={cn('w-[2px] bg-black invisible', {
                            'group-hover:visible': !selected,
                            visible: selected,
                          })}
                        >
                          &nbsp;
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className="w-[50%] bg-white p-3 rounded-lg border overflow-auto">
                {selectedImprovement && (
                  <>
                    <div className="flex-1">
                      You need to improve{' '}
                      <span className="font-semibold">
                        &quot;{selectedImprovement.criterion}&quot;
                      </span>
                    </div>
                    <div className="my-2 text-muted-foreground">
                      These calls highlight areas where you didn&apos;t meet
                      your goals. Use them as opportunities to reflect, identify
                      what can be improved, and sharpen your skills. Each call
                      is a step toward becoming more confident and successful.
                    </div>

                    {!selectedImprovement.calls && (
                      <div className="text-muted-foreground mt-10 text-center">
                        No call found
                      </div>
                    )}

                    {selectedImprovement.calls && (
                      <>
                        <div className="mt-4 mb-2">
                          Calls where you under-performed:
                        </div>
                        <div>
                          {(!selectedImprovement.calls.underPerformedCalls ||
                            selectedImprovement.calls.underPerformedCalls
                              .length == 0) && (
                            <div className="text-muted-foreground mt-10 text-center">
                              No calls found.
                            </div>
                          )}
                          {selectedImprovement.calls.underPerformedCalls &&
                            selectedImprovement.calls.underPerformedCalls
                              .length > 0 &&
                            selectedImprovement.calls.underPerformedCalls.map(
                              (c: any, i: number) => {
                                let mb = 'mb-2';
                                if (
                                  i ==
                                  selectedImprovement.calls.underPerformedCalls
                                    .length -
                                    1
                                ) {
                                  mb = '';
                                }
                                return (
                                  <CallBox
                                    call={c}
                                    key={c.vapiId}
                                    className={mb}
                                  />
                                );
                              },
                            )}
                        </div>
                        <div className="mt-4 mb-2">
                          Similar calls to listen to:
                        </div>
                        <div>
                          {(!selectedImprovement.calls.callsToListenTo ||
                            selectedImprovement.calls.callsToListenTo.length ==
                              0) && (
                            <div className="text-muted-foreground mt-10 text-center">
                              No calls found.
                            </div>
                          )}
                          {selectedImprovement.calls.callsToListenTo &&
                            selectedImprovement.calls.callsToListenTo.length >
                              0 &&
                            selectedImprovement.calls.callsToListenTo.map(
                              (c: any, i: number) => {
                                let mb = 'mb-2';
                                if (
                                  i ==
                                  selectedImprovement.calls.callsToListenTo
                                    .length -
                                    1
                                ) {
                                  mb = '';
                                }
                                return (
                                  <CallBox
                                    call={c}
                                    key={c.vapiId}
                                    className={mb}
                                  />
                                );
                              },
                            )}
                        </div>
                      </>
                    )}
                  </>
                )}
              </div>
            </motion.div>
          </AnimatePresence>
        )}
      </div>
    </div>
  );
}
