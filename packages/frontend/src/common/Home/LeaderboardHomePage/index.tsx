import BuyersFilter from '@/common/AnalyticsOld/DashboardTab/Filters/BuyersFilter';
import { FilterState } from '@/common/AnalyticsOld/DashboardTab/Filters/Filter';
import Leaderboard from '@/common/Calls/AIRoleplay/Summary/tabs/leaderboard';
import useOrgAgents from '@/hooks/useOrgAgents';
import { AgentDto, AgentStatus } from '@/lib/Agent/types';
import { useEffect, useRef, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { CheckIcon } from 'lucide-react';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';
import AgentAvatar from '@/components/Avatars/Agent';

interface IProps {
  className?: string;
}
export default function LeaderboardHomePage({ className }: IProps) {
  const [filterState, setFilterState] = useState<FilterState>(
    new FilterState(true, 20),
  );
  const [allBuyers, setAllBuyers] = useState<AgentDto[]>([]);
  const [selected, setSelected] = useState<AgentDto>();

  const { data: allBuyersDB, isLoading: isLoadingBuyers } = useOrgAgents(
    undefined,
    true,
    filterState.from,
    filterState.numberOfResults,
    filterState.search,
    AgentStatus.ACTIVE,
  );

  useEffect(() => {
    let hasMore = true;
    if (!isLoadingBuyers && allBuyersDB) {
      if (allBuyersDB.length < filterState.numberOfResults) {
        hasMore = false;
      }
      if (filterState.isSearching) {
        setAllBuyers([...allBuyersDB]);
      } else {
        setAllBuyers([...allBuyers, ...allBuyersDB]);
      }
      if (allBuyersDB.length > 0 && !selected) {
        setSelected(allBuyersDB[0]);
      }
    }
    setFilterState({
      ...filterState,
      isLoadingItems: isLoadingBuyers,
      hasMore,
      isSearching: false,
    });
  }, [isLoadingBuyers, allBuyersDB]);

  /**************************************/
  /************** STATE MNG *************/
  /**************************************/

  const [searchString, setSearchString] = useState<string>('');
  const timeoutSearchRes = useRef<ReturnType<typeof setTimeout> | null>(null);

  const search = async (s: string) => {
    if (timeoutSearchRes.current) {
      clearTimeout(timeoutSearchRes.current);
    }

    timeoutSearchRes.current = setTimeout(async () => {
      setFilterState({ ...filterState, from: 0, isSearching: true, search: s });
    }, 200);

    setSearchString(s);
  };

  const loadMore = () => {
    setFilterState({
      ...filterState,
      from: filterState.from + filterState.numberOfResults,
    });
  };

  return (
    <div className={className}>
      <div className="mt-8 flex items-start">
        <div className="flex-1 min-w-[50%] overflow-auto">
          {!isLoadingBuyers && (
            <Leaderboard agentId={selected?.id} showLeaderboardDateFilter />
          )}
        </div>
        <ScrollablePage
          className="bg-white p-4 rounded-lg border mx-4 min-w-[30%]"
          marginBottom={20}
        >
          <div className="text-muted-foreground mb-4">
            Select an AI buyer bot
          </div>

          {allBuyers.map((v, i) => {
            return (
              <div
                key={'item-' + i}
                onClick={() => {
                  setSelected(v);
                }}
                className="flex items-center rounded-sm text-sm mx-1 px-2 py-1 cursor-pointer hover:bg-muted"
              >
                <div
                  className={cn(
                    'mr-2 flex h-4 w-4 items-center justify-center border border-primary rounded-full',
                    selected?.id === v.id
                      ? 'bg-primary text-primary-foreground'
                      : 'opacity-50 [&_svg]:invisible',
                  )}
                >
                  <CheckIcon className={cn('h-4 w-4')} />
                </div>
                <div className="flex space-x-2 items-center">
                  <AgentAvatar className="w-6 h-6" agent={v} />

                  <div className="capitalize">
                    {v?.firstName || ''} {v?.lastName || ''}
                  </div>
                </div>
              </div>
            );
          })}
          {allBuyers.length > 0 && (
            <>
              {filterState.hasMore && (
                <div
                  onClick={loadMore}
                  className="text-center text-muted-foreground text-sm m-1 p-1 cursor-pointer hover:bg-muted hover:text-black rounded-sm"
                >
                  More...
                </div>
              )}
            </>
          )}
        </ScrollablePage>
      </div>
    </div>
  );
}
