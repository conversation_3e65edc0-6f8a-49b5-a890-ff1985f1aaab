import useTeams, { useUserTeams } from '@/hooks/useTeams';
import useUserSession from '@/hooks/useUserSession';
import { TeamDto } from '@/lib/User/types';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select';
import { Loader2Icon } from 'lucide-react';
import { useDashboardForHome } from '@/hooks/useDashboard';
import {
  AnalyticsFilterDateRange,
  AnalyticsFilterState,
  AnalyticsFilterType,
  DashboardTabDto,
  DateFilterType,
} from '@/lib/AnalyticsOld/types';
import DashboardTab from '@/common/AnalyticsOld/DashboardTab';
import DatesFilterShort from '@/common/AnalyticsOld/DashboardTab/Filters/DateFilterShort';
import dayjs from 'dayjs';
import RepsFilter from '@/common/AnalyticsOld/DashboardTab/Filters/RepsFilter';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  className?: string;
}

export default function Analytics({ className }: IProps) {
  const { userId, canAccess } = useUserSession();

  const { data: allUserTeams, isLoading: isLoadingUserTeams } = useUserTeams(
    0,
    1000,
    '',
    !canAccess(AppPermissions.VIEW_ANALYTICS),
  );
  const { data: allAdminTeams, isLoading: isLoadingAdminTeams } = useTeams(
    0,
    1000,
    '',
    canAccess(AppPermissions.VIEW_ANALYTICS),
  );

  const teams = canAccess(AppPermissions.VIEW_ANALYTICS)
    ? allAdminTeams
    : allUserTeams;

  const f: AnalyticsFilterState = {} as AnalyticsFilterState;

  const today = dayjs();

  f[AnalyticsFilterType.DATE] = {
    range: AnalyticsFilterDateRange.CUSTOM,
    fromDate: today.subtract(7, 'days').startOf('day').toDate(),
    toDate: today.toDate(),
  };

  const [selectedTeamId, setSelectedTeamId] = useState<string>('all');
  const [selectedTeam, setSelectedTeam] = useState<TeamDto | undefined>();
  const [globalFilters, setGlobalFilters] = useState<AnalyticsFilterState>(f);

  useEffect(() => {
    if (canAccess(AppPermissions.VIEW_ANALYTICS) || !userId) {
      return;
    }
    setGlobalFilters((f) => {
      if (userId) {
        f[AnalyticsFilterType.REPS] = [userId];
      }
      return { ...f };
    });
  }, [userId, canAccess(AppPermissions.VIEW_ANALYTICS)]);

  useEffect(() => {
    if (canAccess(AppPermissions.VIEW_ANALYTICS) || !teams?.length) {
      return;
    }
    setGlobalFilters((f) => {
      const newTeam = teams[0];
      f[AnalyticsFilterType.TEAMS] = [newTeam.id];
      setSelectedTeamId(String(newTeam.id));
      setSelectedTeam(newTeam);
      return { ...f };
    });
  }, [teams, canAccess(AppPermissions.VIEW_ANALYTICS)]);

  const { data: allHomeDashboards, isLoading: isLoadingDashboards } =
    useDashboardForHome(); //useDashboard(); //
  const [dashboard, setDashboard] = useState<DashboardTabDto>();

  useEffect(() => {
    if (!isLoadingDashboards && allHomeDashboards) {
      if (allHomeDashboards.length > 0) {
        setDashboard(allHomeDashboards[0]);
      }
    }
  }, [allHomeDashboards, isLoadingDashboards]);

  const isLoading =
    isLoadingDashboards ||
    (canAccess(AppPermissions.VIEW_ANALYTICS)
      ? isLoadingAdminTeams
      : isLoadingUserTeams);

  return (
    <div className={className}>
      <div className="flex items-center">
        <div className="font-semibold text-base">
          {canAccess(AppPermissions.VIEW_ANALYTICS)
            ? 'Reps usage and performance'
            : 'My usage and performance'}
        </div>
        <div className="flex-1"></div>
        <div className="flex items-center">
          {canAccess(AppPermissions.VIEW_ANALYTICS) && (
            <div className=" mr-3">
              <RepsFilter
                current={globalFilters[AnalyticsFilterType.REPS]}
                onRepsUpdated={(reps: string[]) => {
                  globalFilters[AnalyticsFilterType.REPS] = reps.map(parseInt);
                  setGlobalFilters({ ...globalFilters });
                }}
                isRadioSelect={true}
                keepSelectionInPlace={true}
                displaySelectedName={true}
                className={'bg-white h-[36px] min-w-[280px]'}
                hideClearBtn={!canAccess(AppPermissions.VIEW_ANALYTICS)}
              />
            </div>
          )}
          <div className="w-[250px] mr-3 bg-white">
            <Select
              onValueChange={(v: string) => {
                if (v === 'all') {
                  setSelectedTeamId('all');
                  setSelectedTeam(undefined);
                  globalFilters[AnalyticsFilterType.TEAMS] = [];
                  setGlobalFilters({ ...globalFilters });
                } else {
                  setSelectedTeamId(v);
                  teams?.map((t) => {
                    if (t.id === parseInt(v)) {
                      setSelectedTeam(t);
                      globalFilters[AnalyticsFilterType.TEAMS] = [t.id];
                      setGlobalFilters({ ...globalFilters });
                    }
                  });
                }
              }}
              value={selectedTeamId}
            >
              <SelectTrigger>
                {selectedTeam ? selectedTeam.name : 'All teams'}
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={'all'}>All teams</SelectItem>
                {teams?.map((v) => {
                  return (
                    <SelectItem key={v.id} value={String(v.id)}>
                      {v.name}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
          <div>
            <DatesFilterShort
              current={globalFilters[AnalyticsFilterType.DATE]}
              onDatesUpdated={(n: DateFilterType) => {
                globalFilters[AnalyticsFilterType.DATE] = n;
                setGlobalFilters({ ...globalFilters });
              }}
            />
          </div>
          <div className="">
            {isLoading && <Loader2Icon className="animate-spin ml-2" />}
          </div>
        </div>
      </div>

      <div>
        {dashboard ? (
          <DashboardTab
            dashboard={dashboard}
            paddingX={1}
            doNotFitHeightToScreen={true}
            hideWidgetFilters={true}
            overwriteFilters={globalFilters}
          />
        ) : (
          <div className="mt-6 text-muted-foreground">No dashboard found</div>
        )}
      </div>
    </div>
  );
}
