import useCompetitionLeaderboards from '@/hooks/useCompetitionLeaderboard';
import useUserSession from '@/hooks/useUserSession';
import CompetitionService from '@/lib/Competition';
import {
  CompetitionLeaderboardDto,
  CompetitionLeaderboardItem,
  CompetitionOverallLeaderboardItem,
} from '@/lib/Competition/Leaderboard/types';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useMemo, useState } from 'react';
import CountdownTimer from './CountdownTimer';

const winnersIdx = Object.fromEntries([].map((userId, idx) => [userId, idx]));

export default function PClubCompetitionHome() {
  const [state, setState] = useState<any>({});
  const [leaderboard, setLeaderboard] = useState<string>('');

  const init = async () => {
    let data: any;
    try {
      data = await CompetitionService.getHomeData('losl');
      if (data) {
        data = data.data;
      }
    } catch (e) {
      console.log(e);
    }

    console.log(data);

    if (data) {
      setState({
        score: data.score,
        breakthrough_score: data.breakthrough_score,
        breakthrough_attempt: data.breakthrough_attempt,

        explorer_score: data.explorer_score,
        explorer_attempt: data.explorer_attempt,

        challenger_score: data.challenger_score,
        challenger_attempt: data.challenger_attempt,

        navigator_score: data.navigator_score,
        navigator_attempt: data.navigator_attempt,

        closer_score: data.closer_score,
        closer_attempt: data.closer_attempt,
      });
    }
  };

  useEffect(() => {
    init();
  }, []);
  const { dbUser, competitionEndDate } = useUserSession();

  // iFrame src:
  //      https://story.screenspace.io/hyperbound/{level}?{parameters}
  // High Level:
  //      level: Choose competition_locked or competition_unlocked. This unlocks the Closer level.
  //      name: Player's full name
  //      score: Player's total score
  // Levels:
  //      breakthrough_score: Total score for Breakthrough level
  //      breakthrough_attempt: Total attempts for Breakthrough level
  //      breakthrough_target: Relative target URL for Breakthrough level
  //      explorer_score: Total score for Explorer level
  //      explorer_attempt: Total attempts for Explorer level
  //      explorer_target: Relative URL for Explorer level
  //      challenger_score: Total score for Challenger level
  //      challenger_attempt: Total attempts for Challenger level
  //      challenger_target: Relative URL for Challenger level
  //      navigator_score: Total score for Navigator level
  //      navigator_attempt: Total attempts for Navigator level
  //      navigator_target: Relative URL for Navigator level
  //      closer_score: Total score for Closer level
  //      closer_attempt: Total attempts for Closer level
  //      closer_target: Relative URL for Closer level
  // Leaderboard:
  //      leaderboard1_name: Full name of 1st place
  //      leaderboard1_handle: Handle for 1st place LinkedIn profile
  //      leaderboard2_name: Full name of 2nd place
  //      leaderboard2_handle: Handle for 2nd place LinkedIn profile
  //      leaderboard3_name: Full name of 3rd place
  //      leaderboard3_handle: Handle for 3rd place LinkedIn profile

  let level = 'competition_locked'; //'competition_locked';
  //To "unlock" the boss level, change competition_locked to competition_unlocked in the URL

  const t = Number.parseFloat(
    Number(
      (state.breakthrough_score +
        state.explorer_score +
        state.challenger_score +
        state.navigator_score) /
        4,
    ).toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 1,
    }),
  );
  if (t >= 60) {
    level = 'competition_unlocked';
  }

  const showPopup = true;
  if (showPopup) {
    level = level + '/21662';
  }

  const userFullName = encodeURIComponent(
    dbUser?.firstName + ' ' + dbUser?.lastName,
  );

  console.log(state);

  const breakthrough_target = encodeURIComponent(
    'https://app.hyperbound.ai/buyers/dda2298a-0acd-4e3c-840b-6dc2c1ffe4e3',
  );
  const explorer_target = encodeURIComponent(
    'https://app.hyperbound.ai/buyers/1e8dc235-c009-410c-8a85-77ea1f77b8b4',
  );
  const challenger_target = encodeURIComponent(
    'https://app.hyperbound.ai/buyers/1fdb93f8-88d5-44a6-a858-7bd2dfc610f9',
  );
  const navigator_target = encodeURIComponent(
    'https://app.hyperbound.ai/buyers/24cab102-29a8-4b54-8d17-41dc5d6a76d1',
  );
  const closer_target = encodeURIComponent(
    'https://app.hyperbound.ai/buyers/e70e4777-7106-4c37-be6e-a40dd9faeacb',
  );

  const params = `?zoom=2&name=${userFullName}&score=${state.score}&breakthrough_score=${state.breakthrough_score}&breakthrough_attempt=${state.breakthrough_attempt}&breakthrough_target=${breakthrough_target}&explorer_score=${state.explorer_score}&explorer_attempt=${state.explorer_attempt}&explorer_target=${explorer_target}&challenger_score=${state.challenger_score}&challenger_attempt=${state.challenger_attempt}&challenger_target=${challenger_target}&navigator_score=${state.navigator_score}&navigator_attempt=${state.navigator_attempt}&navigator_target=${navigator_target}&closer_score=${state.closer_score}&closer_attempt=${state.closer_attempt}&closer_target=${closer_target}&subscore=${t}${leaderboard}`;

  const url = 'https://story.screenspace.io/hyperbound/' + level + params;

  /**********************************/
  /*********** LEADERBOARD **********/
  /**********************************/

  const { data: leaderboards } = useCompetitionLeaderboards('losl');

  const sortedLeaderboards = useMemo(() => {
    return (
      leaderboards?.map?.(
        (leaderboard) =>
          ({
            ...leaderboard,
            items: [...leaderboard.items]
              .map((leaderboardItem, idx) => ({
                ...leaderboardItem,
                ogIdx: idx,
              }))
              .sort((i1, i2) => {
                /* eslint-disable no-prototype-builtins */
                if (
                  winnersIdx.hasOwnProperty(i1.userId) &&
                  winnersIdx.hasOwnProperty(i2.userId)
                ) {
                  return winnersIdx[i1.userId] - winnersIdx[i2.userId];
                } else if (winnersIdx.hasOwnProperty(i1.userId)) {
                  return -1;
                } else if (winnersIdx.hasOwnProperty(i2.userId)) {
                  return 1;
                }
                /* eslint-enable no-prototype-builtins */
                return i1.ogIdx - i2.ogIdx;
              }),
          }) as CompetitionLeaderboardDto,
      ) || []
    );
  }, [leaderboards]);

  useEffect(() => {
    const userIdToLeaderboardItems: {
      [userId: number]: (CompetitionLeaderboardItem & { agentId: number })[];
    } = {};
    sortedLeaderboards.forEach((l) => {
      l.items.forEach((i) => {
        if (!userIdToLeaderboardItems[i.userId]) {
          userIdToLeaderboardItems[i.userId] = [];
        }
        userIdToLeaderboardItems[i.userId].push({ ...i, agentId: l.agent.id });
      });
    });
    const items: CompetitionOverallLeaderboardItem[] = Object.values(
      userIdToLeaderboardItems,
    )
      .map((individualLeaderboardItems) => {
        return {
          ...individualLeaderboardItems[0],
          averageAggregateScore:
            individualLeaderboardItems
              .map(
                (i) =>
                  i.highestScoreCallDetails.aggregatedScorecardDto
                    .aggregateScore || 0,
              )
              .reduce((prev, curr) => prev + curr, 0) /
            individualLeaderboardItems.length,
          highestScoreCallDetailsMap: Object.fromEntries(
            individualLeaderboardItems.map((i) => [
              i.agentId,
              i.highestScoreCallDetails,
            ]),
          ),
        };
      })
      .sort((i1, i2) => {
        if (i1.averageAggregateScore !== i2.averageAggregateScore) {
          return i2.averageAggregateScore - i1.averageAggregateScore;
        }
        return i1.userId - i2.userId;
      });

    // const r = {
    //   agents: sortedLeaderboards.map((l) => ({
    //     ...l.agent,
    //     scorecardConfig: l.scorecardConfig,
    //   })),
    //   items,
    // };
    let i = 0;

    let leaderboard1_name = encodeURIComponent('');
    let leaderboard1_handle = encodeURIComponent('');

    let leaderboard2_name = encodeURIComponent('');
    let leaderboard2_handle = encodeURIComponent('');

    let leaderboard3_name = encodeURIComponent('');
    let leaderboard3_handle = encodeURIComponent('');

    for (const item of items) {
      if (i == 0) {
        leaderboard1_name = encodeURIComponent(
          item.firstName + ' ' + item.lastName,
        );
        leaderboard1_handle = encodeURIComponent(
          item.linkedInUrl.replace('https://www.linkedin.com/in/', ''),
        );
      } else if (i == 1) {
        leaderboard2_name = encodeURIComponent(
          item.firstName + ' ' + item.lastName,
        );
        leaderboard2_handle = encodeURIComponent(
          item.linkedInUrl.replace('https://www.linkedin.com/in/', ''),
        );
      } else if (i == 2) {
        leaderboard3_name = encodeURIComponent(
          item.firstName + ' ' + item.lastName,
        );
        leaderboard3_handle = encodeURIComponent(
          item.linkedInUrl.replace('https://www.linkedin.com/in/', ''),
        );
      }
      i++;
    }

    setLeaderboard(
      `&leaderboard1_name=${leaderboard1_name}&leaderboard1_handle=${leaderboard1_handle}&leaderboard2_name=${leaderboard2_name}&leaderboard2_handle=${leaderboard2_handle}&leaderboard3_name=${leaderboard3_name}&leaderboard3_handle=${leaderboard3_handle}`,
    );
  }, [sortedLeaderboards]);

  /*****************************/
  /*********** RENDER **********/
  /*****************************/

  // <iframe src="https://story.screenspace.io/hyperbound/e_01ba7631" width="100%" height="100%" scrolling="no" allow="autoplay; fullscreen; clipboard-write" style="min-height:400px;border:none;background:#FBFBFB"></iframe>

  return (
    <div className="h-100">
      <div className="absolute top-0 left-0 w-full z-50 ">
        <div className="flex items-center w-full pt-6">
          <div className="flex-1" />
          <div className="rounded-2xl p-2 flex flex-wrap items-center space-x-4 justify-center bg-white text-black z-10">
            <Link href="https://hyperbound.ai" target="_blank">
              <Image
                src="/images/black-logo-with-text.svg"
                width={153}
                height={24}
                alt="Hyperbound Logo"
              />
            </Link>
            <span className="">&amp;</span>
            <Link href="https://pclub.io" target="_blank">
              <Image
                src="/images/competition/league-of-sales-legends/pclub.svg"
                width={120}
                height={32}
                alt="PClub Logo"
              />
            </Link>
          </div>
          <div className="flex-1 flex items-center">
            <div className="flex-1" />
            <div className="flex flex-col justify-center items-center text-black  pr-6">
              <CountdownTimer
                abbreviatedSuffix={false}
                bigNumbers={true}
                targetDate={
                  new Date(competitionEndDate ? competitionEndDate : '')
                }
              />
              <div className="text-right w-full mt-2 text-muted-foreground font-semibold">
                Until competition ends
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="h-[100vh] w-100">
        <iframe src={url} width="100%" height="100%" />
      </div>
    </div>
  );
}
