'use client';

import clsx from 'clsx';
import useCountdown from './useCountdown';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

interface ICountdownTimerProps {
  targetDate: string | number | Date;
  expiredMessage?: string;
  abbreviatedSuffix?: boolean;
  onExpire?: () => Promise<void>;
  bigNumbers?: boolean;
}
export default function CountdownTimer({
  targetDate,
  expiredMessage,
  abbreviatedSuffix = false,
  onExpire,
  bigNumbers,
}: ICountdownTimerProps) {
  const [days, hours, minutes, seconds] = useCountdown(targetDate, onExpire);

  if (days + hours + minutes + seconds <= 0) {
    return <>{expiredMessage || 'Expired'}</>;
  }

  if (bigNumbers) {
    return (
      <div className="flex space-x-8">
        <div className="text-center">
          <div className={clsx('text-[40px] font-bold', inter.className)}>
            {days < 10 ? 0 : ''}
            {days}
          </div>
          <div className="text-[16px] text-gray-500 mt-2">
            day{days !== 1 ? 's' : ''}
          </div>
        </div>
        <div className="text-center">
          <div className={clsx('text-[40px] font-bold', inter.className)}>
            {hours < 10 ? 0 : ''}
            {hours}
          </div>
          <div className="text-[16px] text-gray-500 mt-2">
            hour{hours !== 1 ? 's' : ''}
          </div>
        </div>
        <div className="text-center">
          <div className={clsx('text-[40px] font-bold', inter.className)}>
            {minutes < 10 ? 0 : ''}
            {minutes}
          </div>
          <div className="text-[16px] text-gray-500 mt-2">
            minute{minutes !== 1 ? 's' : ''}
          </div>
        </div>
        <div className="text-center">
          <div className={clsx('text-[40px] font-bold', inter.className)}>
            {seconds < 10 ? 0 : ''}
            {seconds}
          </div>
          <div className="text-[16px] text-gray-500 mt-2">
            second{seconds !== 1 ? 's' : ''}
          </div>
        </div>
        {/* {days > 0 ? <span className="text-4xl">{days}</span> : ""}
        {hours > 0 ? <span className="text-4xl">{hours}</span> : ""}
        {minutes > 0 ? <span className="text-4xl">{minutes}</span> : ""}
        {seconds > 0 ? <span className="text-4xl">{seconds}</span> : ""} */}
      </div>
    );
  }

  return (
    <>
      {days > 0 ? `${days}${abbreviatedSuffix ? 'd' : ' days'}, ` : ''}
      {hours > 0 ? `${hours}${abbreviatedSuffix ? 'h' : ' hours'}, ` : ''}
      {minutes > 0 ? `${minutes}${abbreviatedSuffix ? 'm' : ' minutes'}, ` : ''}
      {seconds > 0 ? `${seconds}${abbreviatedSuffix ? 's' : ' seconds'}` : ''}
    </>
  );
}
