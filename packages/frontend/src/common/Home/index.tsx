import PageHeader from '@/components/PageHeader';
import ScrollablePage from '@/components/ui/Hyperbound/scollable-page';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import useUserSession from '@/hooks/useUserSession';
import { Lock } from 'lucide-react';
import { useEffect, useState } from 'react';
import Analytics from './Analytics';
import Improvements from './Improvements';
import LeaderboardHomePage from './LeaderboardHomePage';

export default function HomePage() {
  const {
    blurSecondaryPages,
    showLeaderboardInHomePage,
    dbOrg,
    isOrgActive,
    isLoggedIn,
  } = useUserSession();

  const [currentTab, setCurrentTab] = useState<string>('my-usage');
  const [freezeTabs, setFreezeTabs] = useState<boolean>(false);

  const onTabChange = (t: string) => {
    setCurrentTab(t);
  };

  useEffect(() => {
    if (dbOrg) {
      if (dbOrg.id == 132) {
        setFreezeTabs(true);
        setCurrentTab('leaderboard');
      }
    }
  }, [dbOrg]);

  if (isLoggedIn && !isOrgActive) {
    return (
      <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6 overflow-auto flex flex-col">
        <PageHeader title="Home" />
        <div className="flex flex-1 w-full flex-col justify-center items-center text-center">
          <p>
            You no longer have access to Hyperbound.
            <br />
            Please contact support in case you think this is not intended.
          </p>
        </div>
      </ScrollablePage>
    );
  }

  return (
    <ScrollablePage className="bg-[#FBFBFB] h-[100vh] py-4 px-6 overflow-auto">
      <PageHeader title="Home" />

      {showLeaderboardInHomePage ? (
        <Tabs defaultValue="my-usage" className="mt-6" value={currentTab}>
          <TabsList>
            <TabsTrigger
              value="my-usage"
              onClick={() => {
                if (!freezeTabs) {
                  onTabChange('my-usage');
                }
              }}
            >
              My usage and performance
            </TabsTrigger>
            <TabsTrigger
              value="leaderboard"
              onClick={() => onTabChange('leaderboard')}
            >
              Leaderboard
            </TabsTrigger>
          </TabsList>
          <TabsContent value="my-usage">
            <>
              <Improvements className="mt-6" />

              <Analytics className="mt-6" />
            </>
          </TabsContent>
          <TabsContent value="leaderboard">
            <LeaderboardHomePage className="mt-6" />
          </TabsContent>
        </Tabs>
      ) : (
        <>
          {/* <div className="bg-white border rounded-lg mt-6 p-4 text-base">
              <p className="font-semibold mb-2">New Hyperbound interface!</p>
              <p className="text-sm text-muted-foreground">We are excited to unveil our new UI! In the following video, Mia is going to introduce you to the new interface and show you how to navigate through it.</p>
              <div className="mt-4 ">
                <iframe className="rounded-lg shadow-lg" width="462" height="260" src="https://www.youtube.com/embed/3ClJ_eS1r0o?si=_CxcMCx09wYmQq5W" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
              </div>
            </div> */}

          <Improvements className="mt-6" />

          <Analytics className="mt-6" />
        </>
      )}

      {blurSecondaryPages && (
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock custom rep home
            </div>
            <div className="text-base mt-2 text-muted-foreground">
              Complete your onboarding to unlock a custom home page for your
              reps
            </div>
          </div>
        </div>
      )}
    </ScrollablePage>
  );
}
