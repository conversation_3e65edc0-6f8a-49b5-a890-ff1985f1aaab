import React, { Dispatch, SetStateAction, useState } from 'react';
import Drag<PERSON>nd<PERSON>rop from './DragAndDrop'; // Generic component
import { Button } from '@/components/ui/button';
import { Folder } from 'lucide-react';

import { extractTextFromFile, MAX_FILE_SIZE } from './utils';

interface IUploadTabContentProps {
  inputState: [
    inputValue: string,
    setInputValue: Dispatch<SetStateAction<string>>,
  ];
  onUploadSuccess?: () => void;
}

const UploadTranscript = ({
  inputState,
  onUploadSuccess,
}: IUploadTabContentProps) => {
  const [, setTranscriptContent] = inputState;
  const [error, setError] = useState<string | undefined>(undefined);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);

  const handleUpload = async (file: File) => {
    if (file.size > MAX_FILE_SIZE) {
      setError('File size exceeds limit.');
      setUploadedFileName(null);
      return;
    }

    try {
      const result = await extractTextFromFile(file);

      if (result.success && result.text) {
        setTranscriptContent(result.text);
        setUploadedFileName(file.name);
        setError(undefined);
        onUploadSuccess?.();
      } else {
        console.error(result);
        setError(result.error || 'Error processing file.');
        setUploadedFileName(null);
      }
    } catch (e) {
      console.error('File processing error:', e);
      setError('Error processing file.');
      setUploadedFileName(null);
    }
  };

  return (
    <DragAndDrop
      onDrop={(files) => {
        if (files.length) {
          handleUpload(files[0]);
        }
      }}
      accept={{
        'text/plain': ['.txt'],
        'application/rtf': ['.rtf'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          ['.docx'],
        'application/pdf': ['.pdf'],
      }}
      maxSize={MAX_FILE_SIZE}
      renderContent={(isDragActive) => (
        <div
          className="bg-zinc-50 p-6 rounded-lg flex flex-col items-center justify-center gap-4 w-full"
          style={{ minHeight: '160px' }}
        >
          {isDragActive ? (
            <div className="text-sm text-zinc-500">Drop file here...</div>
          ) : (
            <>
              <div className="text-center">
                <p className="text-sm text-zinc-800 font-medium">
                  Drag and drop a file or click to browse
                </p>
              </div>
              {uploadedFileName ? (
                <div className="text-lg text-zinc-800 font-bold">
                  {uploadedFileName}
                </div>
              ) : (
                <Button
                  variant="ghost"
                  className="bg-white text-zinc-800 shadow rounded-lg flex items-center gap-2 px-4 py-2 hover:bg-white"
                >
                  <Folder className="w-4 h-4" />
                  Browse
                </Button>
              )}
            </>
          )}
        </div>
      )}
      errorMessage={error}
    />
  );
};

export default UploadTranscript;
