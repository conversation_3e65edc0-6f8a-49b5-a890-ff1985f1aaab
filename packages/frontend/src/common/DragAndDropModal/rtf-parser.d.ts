declare module 'rtf-parser' {
  interface RTFNode {
    value?: string;
    style?: {
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
      fontSize?: number;
      color?: string;
    };
    content?: RTFNode[] | RTFNode;
  }

  interface RTFDocument {
    content?: RTFNode[];
    metadata?: {
      title?: string;
      author?: string;
      operator?: string;
      created?: Date;
      modified?: Date;
    };
  }

  interface RTFParserStatic {
    (rtfString: string, options?: object): Promise<RTFDocument>;
    string(
      rtfString: string,
      callback: (err: Error | null, doc: RTFDocument) => void,
    ): void;
  }

  const RTFParser: RTFParserStatic;
  export = RTFParser;
}
