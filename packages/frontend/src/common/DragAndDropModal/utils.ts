import RTFParser from 'rtf-parser';
import mammoth from 'mammoth';
import * as pdfjsLib from 'pdfjs-dist';

export const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2 Mb
export const MAX_TRANSCRIPT_LENGTH = 500 * 1000; // 500K characters
export const MIN_TRANSCRIPT_LENGTH = 1; // 1 character

type ExtractResult = {
  success: boolean;
  text?: string;
  error?: string;
};

// Enhanced type definitions
interface ValidationResult {
  isValid: boolean;
  error?: string;
}

if (typeof window !== 'undefined') {
  pdfjsLib.GlobalWorkerOptions.workerSrc =
    'https://unpkg.com/pdfjs-dist@4.10.38/build/pdf.worker.min.mjs';
}

type FileType = 'txt' | 'rtf' | 'docx' | 'pdf' | 'unsupported';

// File validation functions
const validateFileSize = (file: File): ValidationResult => {
  if (file.size === 0) {
    return {
      isValid: false,
      error:
        'This file appears to be empty. Please upload a file with content.',
    };
  }
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File is too large. Please upload a file smaller than ${MAX_FILE_SIZE / 1000000}MB.`,
    };
  }
  return { isValid: true };
};

const validateFileContent = (text: string): ValidationResult => {
  if (text.length < MIN_TRANSCRIPT_LENGTH) {
    return {
      isValid: false,
      error: "The file doesn't contain any readable text content.",
    };
  }
  if (text.length > MAX_TRANSCRIPT_LENGTH) {
    return {
      isValid: false,
      error: `File content is too long. Maximum length is ${MAX_TRANSCRIPT_LENGTH.toLocaleString()} characters.`,
    };
  }
  return { isValid: true };
};

// Enhanced file type detection
const detectFileType = (file: File): FileType => {
  const fileName = file.name.toLowerCase();
  const mimeType = file.type.toLowerCase();

  // Check for mismatch between extension and mime type
  if (fileName.endsWith('.pdf') && !mimeType.includes('pdf')) {
    throw new Error(
      "This file has a PDF extension but doesn't appear to be a valid PDF.",
    );
  }
  if (fileName.endsWith('.docx') && !mimeType.includes('document')) {
    throw new Error(
      "This file has a DOCX extension but doesn't appear to be a valid Word document.",
    );
  }

  if (mimeType === 'text/plain' || fileName.endsWith('.txt')) return 'txt';
  if (fileName.endsWith('.rtf')) return 'rtf';
  if (fileName.endsWith('.docx')) return 'docx';
  if (fileName.endsWith('.pdf')) return 'pdf';
  return 'unsupported';
};

// Main extract function with enhanced error handling
export const extractTextFromFile = async (
  file: File,
): Promise<ExtractResult> => {
  // Initial file validation
  const sizeValidation = validateFileSize(file);
  if (!sizeValidation.isValid) {
    return {
      success: false,
      error: sizeValidation.error,
    };
  }

  try {
    const fileType = detectFileType(file);

    // Handle RTFD files
    if (file.name.toLowerCase().endsWith('.rtfd')) {
      return {
        success: false,
        error:
          'RTFD files (Mac Rich Text Format Directory) are not supported. Please save your file as a standard RTF or TXT format.',
      };
    }

    switch (fileType) {
      case 'txt':
        return await extractTextFromTXT(file);
      case 'rtf':
        return await extractTextFromRTF(file);
      case 'docx':
        return await extractTextFromDOCX(file);
      case 'pdf':
        return await extractTextFromPDF(file);
      default:
        return {
          success: false,
          error:
            'Please upload a TXT, RTF, DOCX, or PDF file. Other file formats are not supported.',
        };
    }
  } catch (e: any) {
    return {
      success: false,
      error:
        e.message || 'An unexpected error occurred while processing your file.',
    };
  }
};

// Extract text from TXT file
const extractTextFromTXT = async (file: File): Promise<ExtractResult> => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve({
        success: true,
        text: reader.result as string,
      });
    };
    reader.onerror = () => {
      resolve({
        success: false,
        error: 'Error reading TXT file',
      });
    };
    reader.readAsText(file);
  });
};

const extractTextFromRTF = async (file: File): Promise<ExtractResult> => {
  try {
    const reader = new FileReader();
    const rtfContent = await new Promise<string>((resolve, reject) => {
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(new Error('Error reading RTF file'));
      reader.readAsText(file);
    });

    return new Promise((resolve) => {
      RTFParser.string(rtfContent, (err: Error | null, doc: any) => {
        if (err) {
          console.error('Parser error:', err);
          resolve({
            success: false,
            error: 'Error parsing RTF content',
          });
          return;
        }

        try {
          const extractText = (node: any): string => {
            if (!node) return '';

            // If node is a string, return it
            if (typeof node === 'string') return node;

            // If node has text content
            if (node.value) return node.value;

            // If node has children, process them
            if (node.content) {
              if (Array.isArray(node.content)) {
                return node.content.map(extractText).join('');
              }
              return extractText(node.content);
            }

            return '';
          };

          const text = extractText(doc);

          // Clean up the text
          const cleanText = text
            .replace(/\0/g, '') // Remove null characters
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();

          if (!cleanText) {
            resolve({
              success: false,
              error: `Error: No content detected in RTF.`,
            });
          }

          resolve({
            success: true,
            text: cleanText,
          });
        } catch (e) {
          console.error('Text extraction error:', e);
          resolve({
            success: false,
            error: 'Error extracting text from RTF content',
          });
        }
      });
    });
  } catch (e: any) {
    console.error('RTF Processing Error:', e);
    return {
      success: false,
      error: `Error processing RTF file: ${e.message}`,
    };
  }
};

const extractTextFromDOCX = async (file: File): Promise<ExtractResult> => {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer });

    if (result.messages.some((msg) => msg.type === 'error')) {
      console.error('Mammoth messages:', result.messages);
      return {
        success: false,
        error: 'Error parsing DOCX content',
      };
    }

    // Clean up the text
    const cleanText = result.value
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    if (!cleanText) {
      return {
        success: false,
        error: `Error: No content detected in DOCX file.`,
      };
    }

    return {
      success: true,
      text: cleanText,
    };
  } catch (e: any) {
    console.error('DOCX Processing Error:', e);
    return {
      success: false,
      error: `Error processing DOCX file: ${e.message}`,
    };
  }
};

// Enhanced PDF extraction with better error handling
const extractTextFromPDF = async (file: File): Promise<ExtractResult> => {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjsLib.getDocument(new Uint8Array(arrayBuffer)).promise;

    if (pdf.numPages === 0) {
      return {
        success: false,
        error: 'This PDF appears to be empty or corrupted.',
      };
    }

    let fullText = '';
    let hasExtractedText = false;

    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();

      const pageText = textContent.items
        .map((item) => ('str' in item ? item.str : ''))
        .join(' ');

      if (pageText.trim()) {
        hasExtractedText = true;
      }
      fullText += pageText + '\n\n';
    }

    if (!hasExtractedText) {
      return {
        success: false,
        error:
          "This PDF appears to be scanned or doesn't contain extractable text. Please try OCR software first or manually transcribe the content.",
      };
    }

    const cleanText = fullText
      .replace(/\s+/g, ' ')
      .replace(/\n{3,}/g, '\n\n')
      .replace(/\0/g, '')
      .trim();

    const contentValidation = validateFileContent(cleanText);
    if (!contentValidation.isValid) {
      return {
        success: false,
        error: contentValidation.error,
      };
    }

    return {
      success: true,
      text: cleanText,
    };
  } catch (e: any) {
    const errorMessage = e.message || '';
    if (errorMessage.includes('Invalid PDF structure')) {
      return {
        success: false,
        error:
          'This PDF file appears to be corrupted. Please try saving it again or converting it to a different format.',
      };
    }
    if (errorMessage.includes('Password')) {
      return {
        success: false,
        error:
          'This PDF is password-protected. Please remove the password protection and try again.',
      };
    }
    return {
      success: false,
      error:
        "Unable to read this PDF. Please ensure it's not corrupted and contains actual text rather than images.",
    };
  }
};
