import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { CloudUpload } from 'lucide-react';
import useDragAndDrop from '@/hooks/ui/useDragAndDrop';
import { MAX_FILE_SIZE } from './utils';
import { JSX } from 'react';

const DefaultContent = ({
  accept,
}: {
  accept: { [key: string]: string[] }; // E.g., {'text/vtt': ['.vtt']}
}) => (
  <>
    <div className="mb-2">
      <CloudUpload className="w-5 h-5 text-muted-foreground" />
    </div>
    <div className="text-sm text-muted-foreground mb-2">
      Click to select or drag&apos;n&apos;drop a file here
    </div>
    <div className="text-xs text-muted-foreground">
      Accepted formats: {Object.values(accept).flat().join(', ')}
    </div>
  </>
);

export enum DropRejectedReason {
  FileSizeExceeded = 'FileSizeExceeded',
}

interface DragAndDropProps {
  onDrop: (files: File[]) => void;
  accept?: { [key: string]: string[] }; // E.g., {'text/vtt': ['.vtt']}
  maxSize?: number; // Max file size in bytes
  onDropRejected?: (reason: DropRejectedReason) => void;
  renderContent?: (isDragActive: boolean, files: File[] | null) => JSX.Element;
  errorMessage?: string;
}

const DragAndDrop: React.FC<DragAndDropProps> = ({
  onDrop,
  onDropRejected: _onDropRejected,
  accept = {},
  maxSize = MAX_FILE_SIZE, // Default: 10MB
  renderContent,
  errorMessage,
}) => {
  const [isDNDActive, setIsDNDActive] = useState(false);
  const [currentDraggedFile] = useState<File | null>(
    null,
  );

  const handleFileDrop = useCallback(
    (files: File[]) => {
      onDrop(files);
    },
    [onDrop],
  );

  const { dragPanelRef, onMouseEnter, onMouseLeave } =
    useDragAndDrop<File, string>(setIsDNDActive, (movingFile, target) => {
      console.log('Dropped file:', movingFile);
      console.log('Dropped on target:', target);
    });

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept,
    maxSize,
    onDropRejected: (rejectedFiles) => {
      if (rejectedFiles[0]?.file.size > maxSize) {
        if(_onDropRejected) {
          _onDropRejected(DropRejectedReason.FileSizeExceeded)
        } else {
          alert('File size exceeds the maximum limit.')
        }
      }
    },
    onDrop: handleFileDrop,
  });

  return (
    <div
      {...getRootProps()}
      onMouseEnter={() => onMouseEnter('dropzone')} // Example target object
      onMouseLeave={onMouseLeave}
      className={`relative ${isDNDActive ? 'select-none' : ''}`}
    >
      <input {...getInputProps()} />
      <div className="border border-dashed border-slate-300 flex flex-col items-center justify-center rounded-lg">
        {renderContent ? (
          renderContent(isDragActive, null)
        ) : (
          <>
            {isDragActive ? (
              <div className="text-sm text-muted-foreground mb-2">
                Drop file here...
              </div>
            ) : (
              <DefaultContent accept={accept} />
            )}
          </>
        )}
        {errorMessage && (
          <div className="text-red-500 text-sm">{errorMessage}</div>
        )}
      </div>
      <div
        ref={dragPanelRef}
        className="absolute pointer-events-none bg-gray-100 rounded p-2 shadow"
        style={{ display: 'none' }}
      >
        {currentDraggedFile?.name || 'Dragging...'}
      </div>
    </div>
  );
};

export default DragAndDrop;
