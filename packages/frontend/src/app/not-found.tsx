'use client';

import Image from 'next/image';
import { HeroHighlight } from '../components/ui/Hyperbound/hero-highlight';
import useUserSession from '@/hooks/useUserSession';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ChevronLeft, CircleHelpIcon } from 'lucide-react';

export default function NotFoundPage() {
  const { dbOrg } = useUserSession();

  return (
    <div className="w-[100vw] h-[100vh]">
      <div className="flex justify-center items-center relative z-50">
        <div className="flex justify-center w-full fixed left-0 top-0 z-50 backdrop-blur-3xl backdrop-brightness-150">
          <div
            className="flex justify-center md:justify-between items-center z-50 w-full py-3 px-8"
            style={
              {
                // background:
                //   "linear-gradient(to bottom, rgba(255, 255, 255, 0.9) 60%, rgba(255, 255, 255, 0))",
              }
            }
          >
            <div className="w-[346px]">
              <Image
                src={`https://www.hyperbound.ai/images/black-logo-with-text.svg`}
                alt="Hyperbound logo"
                width={153}
                height={36}
                priority
              />
            </div>
          </div>
        </div>
      </div>
      <HeroHighlight
        className="px-4 md:px-0 "
        containerClassName={' h-[100vh]'}
      >
        <div className="flex items-center">
          <div className="flex-1 overflow-hidden p-10 text-center">
            <img
              src={`/error.png`}
              alt="Hyperbound logo"
              className="h-[60vh] rounded-lg"
            />
          </div>
          <div className="flex-1 ml-20">
            <div className="text-6xl font-bold text-center mb-8">404</div>
            <div className="text-3xl font-bold">
              Ooops! Something went wrong!
            </div>
            <div className="flex items-center justify-center mt-20">
              <Link href={'/buyers'}>
                <Button
                  size={'lg'}
                  variant={'default'}
                  className={
                    'w-[160px] ml-4 p-6 text-base border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-90 transition-opacity duration-200'
                  }
                  style={{
                    backgroundImage:
                      'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
                  }}
                >
                  <ChevronLeft className="mr-2" size={20} />
                  Back
                </Button>
              </Link>
              {dbOrg?.pilotDetails?.expiryDate && (
                <Link
                  href={dbOrg?.pilotDetails?.faqLink as string}
                  target="_blank"
                >
                  <Button
                    className="w-[160px] ml-4 p-6 text-base border border-white/50 hover:text-white drop-shadow-2xl hover:opacity-90 transition-opacity duration-200"
                    size={'lg'}
                    variant={'default'}
                  >
                    <CircleHelpIcon className="mr-2" size={20} />
                    View FAQ
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </HeroHighlight>
    </div>
  );
}
