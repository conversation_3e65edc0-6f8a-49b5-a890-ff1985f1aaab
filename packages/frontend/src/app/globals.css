@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  :root {
    --chart-1: oklch(80.9% 0.105 251.813);
    --chart-2: oklch(62.3% 0.214 259.815);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
  }

  .dark {
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
  }
}

.highlight-row {
  background: 'linear-gradient(to right, #000000, #5189CE, #A168A2)' !important;
  background-image: '-webkit-linear-gradient(to right, #000000, #5189CE, #A168A2)' !important;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.plyr {
  border-radius: 12px !important;
  --plyr-color-main: #1a829d;
  .plyr__progress input[type='range']::-webkit-slider-thumb {
    background-color: #1a829d;
  }
  .plyr-controls {
    padding-bottom: 10px !important;
  }
  .plyr__control {
    border-radius: 100px !important;
  }
  .plyr__control:hover {
    color: #1f2937 !important;
    background-color: #f3f4f6 !important;
  }
  .plyr__control[aria-expanded='true'] {
    color: #1f2937 !important;
    background-color: #f3f4f6 !important;
  }

  .plyr__menu__container {
    position: absolute !important;
    bottom: -60px !important;
    max-height: 124px;
    overflow: auto;
    .plyr__control--back {
      display: none !important;
    }
  }
  .plyr__menu__container::after {
    content: none !important;
  }

  .plyr__control--overlaid svg,
  .plyr__control[data-plyr='play'] svg,
  .plyr__control[data-plyr='pause'] svg {
    color: #1a829d; /* Your custom color */
  }

  .plyr__control--overlaid:hover,
  .plyr__control[data-plyr='play']:hover,
  .plyr__control[data-plyr='pause']:hover {
    background-color: transparent !important;
    box-shadow: none !important;
  }

  .plyr__progress input[type='range']::-moz-range-thumb {
    background-color: #1a829d;
  }
}
/* Fit in-line download button */
.video-plyr-wrapper .plyr__controls {
  padding-right: 48px !important;
}
