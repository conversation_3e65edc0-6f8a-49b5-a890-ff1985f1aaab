'use client';

import { use, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import useUserSession from '@/hooks/useUserSession';
import { useAgent } from '@/hooks/useAgent';
import CallSimulationPanel from '@/components/CallSimulationPanel';
import useDemoAgentByVapiId from '@/hooks/useDemoAgentByVapiId';
import useLearningModules from '@/hooks/useLearningModules';
import {
  AiRoleplayTask,
  LearningModuleStatus,
  TaskAndAttempts,
  TaskAttempt,
} from '@/lib/LearningModule/types';
import { Loader2 } from 'lucide-react';

function FullscreenAgentSimulation(props: { params: Promise<{ id: string }> }) {
  const params = use(props.params);
  const searchParams = useSearchParams();

  const { source } = useMemo(() => {
    return {
      source: searchParams.get('source') || 'buyers',
    };
  }, [searchParams]);

  const { userId, isLoggedIn } = useUserSession();

  const { data: learningModulesData } = useLearningModules(
    userId ? [userId] : [],
    LearningModuleStatus.IN_PROGRESS,
    '',
    'asc',
    false,
    0,
    100,
    false,
    source === 'scorm' && userId ? true : false,
  );

  const {
    data: myAgent,
    isLoading: isLoadingMyAgent,
    isError: isErrorMyAgent,
  } = useAgent(params.id);
  const { data: demoAgent, isLoading: isLoadingDemoAgent } =
    useDemoAgentByVapiId(isLoggedIn ? isErrorMyAgent : true);
  const agent = demoAgent || myAgent;
  const isLoadingAgent = isLoadingDemoAgent || isLoadingMyAgent;

  const taskAndAttempts = useMemo(() => {
    if (
      source === 'scorm' &&
      userId &&
      learningModulesData?.learningModules?.length
    ) {
      let currentAgentTask: AiRoleplayTask | undefined = undefined;
      const currentAgentTaskAttempts: TaskAttempt[] = [];

      learningModulesData.learningModules?.forEach((lm) => {
        lm.subModules.forEach((sm) =>
          sm.tasks.forEach((t) => {
            if (
              t.info &&
              'agent' in t.info &&
              t.info.agent?.id === Number(agent?.id)
            ) {
              currentAgentTask = t.info;
            }

            sm.assigneesStats.forEach((as) => {
              as.tasks.forEach((ts) => {
                if (ts.taskId === t.id) {
                  currentAgentTaskAttempts.push(...(ts.attempts || []));
                }
              });
            });
          }),
        );
      });
      return {
        task: currentAgentTask,
        attempts: currentAgentTaskAttempts,
      };
    }
    return undefined as unknown;
  }, [source, userId, learningModulesData, agent]) as TaskAndAttempts;

  if (isLoadingAgent || !agent) {
    return (
      <div className="w-full h-full p-6 bg-gradient-to-t from-[#1B1D20] to-black/70 flex items-center justify-center">
        <Loader2 className="w-10 h-10 animate-spin text-white" />
      </div>
    );
  }

  return (
    <div className="w-full h-full p-6 bg-gradient-to-t from-[#1B1D20] to-black/70 flex items-center justify-center">
      <CallSimulationPanel
        agent={agent}
        navigateToCallSummary={source === 'scorm' && userId ? false : true}
        showLeaderboardDateFilter={true}
        taskAndAttempts={taskAndAttempts}
      />
    </div>
  );
}

export default FullscreenAgentSimulation;
