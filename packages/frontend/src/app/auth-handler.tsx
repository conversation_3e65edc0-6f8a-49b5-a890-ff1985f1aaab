'use client';

import React, { useState, useEffect } from 'react';
import {
  AuthProvider,
  AuthProviderForTesting,
  UserInformationForTesting,
} from '@propelauth/react';
import { useSearchParams } from 'next/navigation';
import UserService from '@/lib/User';
import { useIsInIframe } from '@/hooks/useIsInIframe';
import { refreshHeaders } from './auth-wrapper';
import OnLoadManager from './onLoadManager';

export default function AuthHandler({
  children,
  getActiveOrgFn,
}: {
  children: React.ReactNode;
  getActiveOrgFn?: () => string | null;
}) {
  const hypTokenKey = 'hyp_tkn',
    tokenToReturnWhenWindowIsUndefined = 'tokenToReturnWhenWindowIsUndefined';
  const getHypTokenFromLocal = () => {
    if (typeof window === 'undefined') {
      return tokenToReturnWhenWindowIsUndefined;
    }
    const token = window.localStorage.getItem(hypTokenKey);
    return token || '';
  };
  const setHypTokenToLocal = (token: string) => {
    window.localStorage.setItem(hypTokenKey, token);
  };
  const isInIframe = useIsInIframe();
  const searchParams = useSearchParams();
  const tokenFromLocal = getHypTokenFromLocal();
  const token = searchParams.get(hypTokenKey) || tokenFromLocal;
  const orgId = searchParams.get('orgId');
  const [embeddedUserInfo, setEmbeddedUserInfo] =
    useState<UserInformationForTesting | null>(null);
  const [loading, setLoading] = useState(true);
  const [tokenAuthFailed, setTokenAuthFailed] = useState(false);

  useEffect(() => {
    if (
      isInIframe &&
      token !== tokenFromLocal &&
      token !== tokenToReturnWhenWindowIsUndefined
    ) {
      setHypTokenToLocal(token);
    }
  }, [isInIframe, token, tokenFromLocal]);

  const fetchUserAndVerifyToken = async () => {
    try {
      const userData = await UserService.verifyToken(token || '', orgId || '');
      return userData;
    } catch (err) {
      console.log('err fetch user and verify token', err);
      return null;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (token === tokenToReturnWhenWindowIsUndefined) {
        return;
      }
      if (isInIframe && token) {
        const userData = await fetchUserAndVerifyToken();
        if (!userData) {
          setTokenAuthFailed(true);
          return;
        }
        const orgArray = Object.values(userData.userInfo.orgIdToOrgMemberInfo);
        setEmbeddedUserInfo({
          accessToken: userData.accessToken,
          user: userData.userInfo,
          orgMemberInfos: orgArray,
          getAccessTokenForOrg: async (orgId: string) => {
            return {
              accessToken: userData.accessTokensPerOrg[orgId],
              error: undefined,
            };
          },
        });
        await refreshHeaders(
          {
            isLoggedIn: true,
            accessToken: userData.accessToken,
            user: userData.userInfo,
          },
          {
            id: orgArray[0]?.orgId,
            name: orgArray[0]?.orgName,
            userId: userData.userInfo.userId,
          },
        );
        setLoading(false);
      } else {
        setLoading(false);
      }
    };
    fetchData();
  }, [isInIframe, token, orgId]);

  if (!tokenAuthFailed && isInIframe && (loading || !!embeddedUserInfo)) {
    if (loading) {
      return <div>Loading...</div>;
    } else if (embeddedUserInfo) {
      return (
        <AuthProviderForTesting
          userInformation={embeddedUserInfo}
          activeOrgFn={getActiveOrgFn}
        >
          {children}
        </AuthProviderForTesting>
      );
    }
  } else {
    return (
      <AuthProvider
        authUrl={process.env.NEXT_PUBLIC_AUTH_URL as string}
        getActiveOrgFn={getActiveOrgFn}
      >
        <OnLoadManager />
        {children}
      </AuthProvider>
    );
  }
}
