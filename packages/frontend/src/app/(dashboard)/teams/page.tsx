'use client';

import Teams from '@/common/Teams';
import TeamsOld from '@/common/TeamsOld';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';

export default function TeamsPage() {
  const { isLoggedIn, loading, isTemp, isManagerHierarchyEnabled } =
    useUserSession(true, true, false, RedirectTo.PAGE_NOT_FOUND);

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else if (isManagerHierarchyEnabled) {
    return <Teams />;
  } else {
    return <TeamsOld />;
  }
}
