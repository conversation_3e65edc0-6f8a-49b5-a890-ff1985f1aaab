'use client';

import Analytics from '@/common/Hyperbound/Analytics';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { useRouter } from 'next/navigation';

export default function HyperboundAnalyticsPage() {
  const router = useRouter();

  const { isHyperboundUser } = useUserSession();
  if (!isHyperboundUser) {
    router.push(RedirectTo.PAGE_NOT_FOUND);
  }

  return <Analytics />;
}
