'use client';

import HyperboundAgentsManagementPage from '@/common/Hyperbound/agents';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { useRouter } from 'next/navigation';

export default function HyperboundUsersPage() {
  const router = useRouter();

  const { isHyperboundUser } = useUserSession();
  if (!isHyperboundUser) {
    router.push(RedirectTo.PAGE_NOT_FOUND);
  }

  return <HyperboundAgentsManagementPage />;
}
