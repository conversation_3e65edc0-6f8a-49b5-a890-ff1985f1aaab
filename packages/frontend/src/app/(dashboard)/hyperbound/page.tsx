'use client';

import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { useRouter } from 'next/navigation';

// THESE PAGES ARE ONLY ACCESSIBLE TO HYPERBOUND USERS

export default function HyperboundPage() {
  const router = useRouter();

  const { isHyperboundUser } = useUserSession();
  if (!isHyperboundUser) {
    router.push(RedirectTo.PAGE_NOT_FOUND);
  }

  return <div></div>;
}
