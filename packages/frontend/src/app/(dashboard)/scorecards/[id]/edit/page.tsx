'use client';

import { EditScorecard } from '@/common/Scorecards/EditScorecard';
import LinksManager from '@/lib/linksManager';
import { useParams, useRouter } from 'next/navigation';

export default function EditScorecardPage() {
  const router = useRouter();
  const { id: scorecardId }: { id: string } = useParams();

  return (
    <EditScorecard
      scorecardId={parseInt(scorecardId)}
      onCancel={() => {
        router.back();
      }}
      onSaved={(scorecardId: number) => {
        router.push(LinksManager.scorecards(`?id=${scorecardId}`));
      }}
    />
  );
}
