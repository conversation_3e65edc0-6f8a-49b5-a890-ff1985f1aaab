'use client';

import { CreateScorecard } from '@/common/Scorecards/CreateScorecard';
import LinksManager from '@/lib/linksManager';
import { useRouter } from 'next/navigation';

export default function CreateScorecardsPage() {
  const router = useRouter();
  return (
    <CreateScorecard
      onCancel={() => {
        router.back();
      }}
      onSaved={(scorecardId: number) => {
        router.push(LinksManager.scorecards(`?id=${scorecardId}`));
      }}
    />
  );
}
