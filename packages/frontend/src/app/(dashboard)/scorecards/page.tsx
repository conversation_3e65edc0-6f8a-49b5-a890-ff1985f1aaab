'use client';

import Scorecards from '@/common/Scorecards';
import ScorecardsOld from '@/common/Scorecards/old';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { AgentCallType } from '@/lib/Agent/types';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function ScorecardsPage() {

  console.log('asdasdasd');
  
  const { isLoggedIn, loading, isTemp, useNewScorecardsUI } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const initialScorecardId = searchParams.get('id');
  const [scorecardId, setScorecardId] = useState<number | undefined>(
    initialScorecardId ? parseInt(initialScorecardId) : undefined,
  );

  const getCallTypesFromStr = (callTypesStr: string) => {
    return callTypesStr
      .split(',')
      .filter((t) => !!t)
      .map((t) => t as AgentCallType);
  };

  const [callTypes, setCallTypes] = useState<(AgentCallType | string)[]>(
    getCallTypesFromStr(searchParams.get('types') || 'all'),
  );

  const [filterType, setFilterType] = useState<string>(
    searchParams.get('filterType') || 'any',
  );

  useEffect(() => {
    const scorecardIdFromParams = searchParams.get('id') || '';
    if (!scorecardId) {
      return;
    } else if (scorecardIdFromParams !== scorecardId.toString()) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('id', scorecardId.toString());
      router.push(`${pathname}?${newSearchParams.toString()}`);
    }
  }, [pathname, scorecardId, searchParams.toString()]);

  useEffect(() => {
    const callTypesFromParams = getCallTypesFromStr(
      searchParams.get('types') || 'all',
    );
    const filterTypeFromParams = searchParams.get('filterType') || 'any';
    if (!callTypes.length) {
      return;
    } else if (
      JSON.stringify(callTypesFromParams) !== JSON.stringify(callTypes) ||
      filterType !== filterTypeFromParams
    ) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('types', callTypes.join(','));
      newSearchParams.set(
        'filterType',
        callTypes?.length === 1 ? filterType : 'any',
      );
      router.push(`${pathname}?${newSearchParams.toString()}`);
    }
  }, [pathname, callTypes, searchParams.toString(), filterType]);

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else if (useNewScorecardsUI) {
    return (
      <Scorecards
        selectedScorecardId={scorecardId}
        setSelectedScorecardId={setScorecardId}
        callTypes={callTypes}
        setCallTypes={setCallTypes}
        filterType={callTypes?.length === 1 ? filterType : 'any'}
        setFilterType={setFilterType}
      />
    );
  }

  return <ScorecardsOld />;
}
