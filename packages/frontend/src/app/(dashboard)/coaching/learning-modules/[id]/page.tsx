'use client';
import { use } from "react";

import ViewLearningModuleOrCompetition from '@/common/LearningModules/View';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';

export default function LearningModulePage(
  props: {
    params: Promise<{ id: string }>;
  }
) {
  const params = use(props.params);

  const {
    id
  } = params;

  const { loading, isTemp } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || isTemp) {
    return <div />;
  } else {
    return <ViewLearningModuleOrCompetition id={id} />;
  }
}
