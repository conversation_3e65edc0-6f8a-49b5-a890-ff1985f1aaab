'use client';

import ViewLearningModuleOrCompetition from '@/common/LearningModules/View';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';

export default function LearningModulePage({
  params: { id },
}: {
  params: { id: string };
}) {
  const { loading, isTemp } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || isTemp) {
    return <div />;
  } else {
    return <ViewLearningModuleOrCompetition id={id} />;
  }
}
