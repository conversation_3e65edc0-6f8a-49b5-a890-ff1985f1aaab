'use client';
import { use } from 'react';
import { useSearchParams } from 'next/navigation';

import ViewLearningModuleOrCompetition from '@/common/LearningModules/View';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import ViewLearningModuleOrCompetitionV2 from '@/common/LearningModulesV2/View';

export default function LearningModulePage(props: {
  params: Promise<{ id: string; assignmentId?: string }>;
}) {
  const params = use(props.params);
  const searchParams = useSearchParams();
  const assignmentId = searchParams.get('assignmentId');

  const { id } = params;

  const { loading, isTemp, isAdmin, useLearningModulesV2 } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || isTemp) {
    return <div />;
  } else if (!assignmentId && isAdmin && useLearningModulesV2) {
    return <ViewLearningModuleOrCompetitionV2 id={id} />;
  } else {
    return <ViewLearningModuleOrCompetition id={assignmentId || id} />;
  }
}
