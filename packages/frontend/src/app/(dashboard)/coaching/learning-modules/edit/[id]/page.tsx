'use client';
import { use } from 'react';

import EditLearningModule from '@/common/LearningModules/Edit';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { AppPermissions } from '@/lib/permissions';
import EditLearningModuleV2 from '@/common/LearningModulesV2/Edit';
import { useSearchParams } from 'next/navigation';

export default function EditLearningModulesPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = use(props.params);
  const searchParams = useSearchParams();
  const assignmentId = searchParams.get('assignmentId');

  const { id } = params;

  const { loading, isTemp, canAccess, useLearningModulesV2 } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || isTemp || !canAccess(AppPermissions.MANAGE_LEARNING_MODULE)) {
    return <div />;
  } else if (!assignmentId && useLearningModulesV2) {
    return <EditLearningModuleV2 id={id} />;
  } else {
    return <EditLearningModule id={id || undefined} />;
  }
}
