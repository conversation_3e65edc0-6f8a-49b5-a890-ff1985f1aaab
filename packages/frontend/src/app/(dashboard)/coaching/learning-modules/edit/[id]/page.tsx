'use client';
import { use } from "react";

import EditLearningModule from '@/common/LearningModules/Edit';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { AppPermissions } from '@/lib/permissions';

export default function EditLearningModulesPage(
  props: {
    params: Promise<{ id: string }>;
  }
) {
  const params = use(props.params);

  const {
    id
  } = params;

  const { loading, isTemp, canAccess } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || isTemp || !canAccess(AppPermissions.MANAGE_LEARNING_MODULE)) {
    return <div />;
  } else {
    return <EditLearningModule id={id} />;
  }
}
