'use client';

import EditLearningModule from '@/common/LearningModules/Edit';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { AppPermissions } from '@/lib/permissions';

export default function EditLearningModulesPage({
  params: { id },
}: {
  params: { id: string };
}) {
  const { loading, isTemp, canAccess } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || isTemp || !canAccess(AppPermissions.MANAGE_LEARNING_MODULE)) {
    return <div />;
  } else {
    return <EditLearningModule id={id} />;
  }
}
