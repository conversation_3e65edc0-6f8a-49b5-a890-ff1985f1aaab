'use client';

import EditLearningModule from '@/common/LearningModules/Edit';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { useSearchParams } from 'next/navigation';

export default function CreateLearningModulesPage() {
  const { loading, isTemp } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  const params = useSearchParams();
  const curSearchParams = new URLSearchParams(params);

  let isCompetition = false;
  if (curSearchParams.get('competition')) {
    isCompetition = true;
  }

  if (loading || isTemp) {
    return <div />;
  } else {
    return <EditLearningModule isCompetition={isCompetition} />;
  }
}
