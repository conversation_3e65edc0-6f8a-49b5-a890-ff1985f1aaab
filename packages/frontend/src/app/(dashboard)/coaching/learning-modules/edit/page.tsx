'use client';

import EditLearningModule from '@/common/LearningModules/Edit';
import EditLearningModuleV2 from '@/common/LearningModulesV2/Edit';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { useSearchParams } from 'next/navigation';

export default function CreateLearningModulesPage() {
  const { loading, isTemp, useLearningModulesV2 } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  const params = useSearchParams();
  const curSearchParams = new URLSearchParams(params);

  let isCompetition = false;
  if (curSearchParams.get('competition')) {
    isCompetition = true;
  }

  const assignmentId = curSearchParams.get('assignmentId');

  if (loading || isTemp) {
    return <div />;
  } else if (!assignmentId && useLearningModulesV2) {
    return <EditLearningModuleV2 isCompetition={isCompetition} />;
  } else {
    return (
      <EditLearningModule
        id={assignmentId || undefined}
        isCompetition={isCompetition}
      />
    );
  }
}
