'use client';

import LearningModules from '@/common/LearningModules';
import useRouting from '@/hooks/useRouting';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { LearningModuleStatus } from '@/lib/LearningModule/types';

export default function LearningModulesPage() {
  const { loading, isTemp } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  const { getUrlParameter } = useRouting();

  const s = getUrlParameter('status');
  const a = getUrlParameter('assignees')?.split(',').map(Number) || [];
  const o = getUrlParameter('order');
  const t = getUrlParameter('show') || '';

  if (loading || isTemp) {
    return <div />;
  } else {
    return (
      <LearningModules
        status={s as LearningModuleStatus}
        assignees={a}
        order={o || 'asc'}
        openTab={t}
      />
    );
  }
}
