'use client';

import LearningModules from '@/common/LearningModules';
import LearningModulesV2 from '@/common/LearningModulesV2';
import useRouting from '@/hooks/useRouting';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import {
  LearningModuleStatus,
  LearningModuleTemplateStatus,
} from '@/lib/LearningModule/types';

export default function LearningModulesPage() {
  const { loading, isTemp, isAdmin, useLearningModulesV2 } = useUserSession(
    false,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  const { getUrlParameter } = useRouting();

  const s = getUrlParameter('status');
  const a = getUrlParameter('assignees')?.split(',').map(Number) || [];
  const o = getUrlParameter('order');
  const t = getUrlParameter('show') || '';

  if (loading || isTemp) {
    return <div />;
  } else if (isAdmin && useLearningModulesV2) {
    return (
      <LearningModulesV2
        status={s as LearningModuleTemplateStatus}
        assignees={a}
        order={o || 'asc'}
        openTab={t}
      />
    );
  } else {
    return (
      <LearningModules
        status={s as LearningModuleStatus}
        assignees={a}
        order={o || 'asc'}
        openTab={t}
      />
    );
  }
}
