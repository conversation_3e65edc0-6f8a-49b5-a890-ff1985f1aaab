'use client';

import AdminAssignmentsCard from '@/common/AdminAssignmentsCard';
import RepAssignmentsCard from '@/common/RepAssignmentsCard';
import CreateAssignmentModal from '@/common/CreateAssignment/CreateAssignmentModal';
import DashboardNavbar from '@/common/DashboardNavbar';
import { Button } from '@/components/ui/button';
import { RoleEnum } from '@/lib/User/types';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { CirclePlusIcon } from 'lucide-react';
import { redirect } from 'next/navigation';
import { useState } from 'react';
import useUserSession from '@/hooks/useUserSession';
import dayjs from 'dayjs';
import useOrg from '@/hooks/useOrg';
import { AppPermissions } from '@/lib/permissions';

function AssignmentsPage() {
  const authInfo = useAuthInfo();
  const { data: dbOrg } = useOrg();
  const org = useActiveOrg();
  const [modalOpen, setModalOpen] = useState(false);
  const { canAccess, isTemp } = useUserSession();

  // Unlike the higher order functions, we need to check the loading case now
  if (authInfo?.loading) {
    return <div />;
  } else if (!authInfo.isLoggedIn || isTemp) {
    redirect('/404');
  }

  const startCreateNew = () => {
    console.log('startCreateNew');
    setModalOpen(true);
  };

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <div>
      <DashboardNavbar
        breadcrumbs={[{ title: 'Coaching' }, { title: 'Assignments' }]}
        subContent={<p className="text-sm">Active assignments by buyer bot</p>}
        rightContent={
          canAccess(AppPermissions.MANAGE_LEARNING_MODULE) && (
            <div onClick={() => setModalOpen(true)} className="flex">
              <Button variant={'default'} disabled={isPilotEnded}>
                <CirclePlusIcon className="w-4 h-4 mr-2" />
                New
              </Button>
            </div>
          )
        }
      />
      <div className="pt-4 px-8 mt-6">
        {authInfo?.accessHelper?.isAtLeastRole(
          org?.orgId as string,
          RoleEnum.ADMIN,
        ) ? (
          <AdminAssignmentsCard startCreateNew={startCreateNew} />
        ) : (
          <RepAssignmentsCard />
        )}
      </div>
      {canAccess(AppPermissions.MANAGE_LEARNING_MODULE) && (
        <CreateAssignmentModal
          modalOpen={modalOpen}
          setModalOpen={setModalOpen}
          onSubmit={() => {
            setModalOpen(false);
          }}
        />
      )}
    </div>
  );
}

export default AssignmentsPage;
