'use client';

import DashboardNavbar from '@/common/DashboardNavbar';
import useAdminAssignmentByAgentId from '@/hooks/useAdminAssignmentByAgentId';
import { useAuthInfo } from '@propelauth/react';
import {
  MoreVerticalIcon,
  Users2Icon,
  Trash2,
  CirclePlusIcon,
} from 'lucide-react';
import { redirect, useRouter } from 'next/navigation';
import React, { useMemo, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import useOrgUsers from '@/hooks/useOrgUsers';
import { Badge } from '@/components/ui/badge';
import _ from 'lodash';
import dayjs from 'dayjs';
import { Flex, ProgressCircle } from '@tremor/react';
import { Button } from '@/components/ui/button';
import CreateAssignmentModal from '@/common/CreateAssignment/CreateAssignmentModal';
import Link from 'next/link';
import { AssignmentDto, AssignmentStatus } from '@/lib/Assignment/types';
import DeleteConfirmationModal from '@/components/ConfirmationModal';
import AssignmentService from '@/lib/Assignment';
import { useQueryClient } from '@tanstack/react-query';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import LinksManager from '@/lib/linksManager';
import useOrg from '@/hooks/useOrg';

function AssignmentPage({ params: { id } }: { params: { id: string } }) {
  const queryClient = useQueryClient();
  const { data: org } = useOrg();
  const [requestDeleteConfirmation, setRequestDeleteConfirmation] =
    useState(false);
  const [requestDeleteAllConfirmation, setRequestDeleteallConfirmation] =
    useState(false);
  const [deleteAssignment, setDeleteAssignment] =
    useState<AssignmentDto | null>(null);

  const authInfo = useAuthInfo();
  const { data: assignment, isLoading } = useAdminAssignmentByAgentId(
    Number(id),
  );
  const [modalOpen, setModalOpen] = useState(false);
  const { data: result, isLoading: isLoadingOrgUsers } = useOrgUsers();
  const router = useRouter();

  const orgUsers = result?.data || [];

  // Unlike the higher order functions, we need to check the loading case now
  if (authInfo?.loading) {
    return <div />;
  } else if (!authInfo.isLoggedIn) {
    redirect('/404');
  }

  if (isLoading || isLoadingOrgUsers) {
    return <div />;
  }

  if (!assignment) {
    return (
      <div>
        <DashboardNavbar
          breadcrumbs={[
            { title: 'Coaching' },
            { title: 'Assignments', href: '/coaching/assignments' },
            { title: 'No assignments found' },
          ]}
        />
        <div className="flex flex-col pt-4 px-8">
          No assignments found for this buyer bot
        </div>
      </div>
    );
  }

  const agent = assignment?.agent;

  const agentName = `${assignment?.agent?.firstName || ''} ${
    assignment?.agent?.lastName || ''
  }`;

  const uaAssignments = _.groupBy(
    assignment.userAssignments,
    (ua) => ua.dueDate,
  );

  //************ DELETE ASSIGMENTS  ************/

  const startDeleteAssignment = async (assignment: AssignmentDto) => {
    setDeleteAssignment(assignment);
    setRequestDeleteConfirmation(true);
  };

  const handleDeleteAssignment = async () => {
    setRequestDeleteConfirmation(false);
    if (deleteAssignment) {
      await AssignmentService.deleteAssignment(deleteAssignment.id);
      queryClient.invalidateQueries({ queryKey: ['adminAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['adminAssignmentsByUser'] });
      queryClient.invalidateQueries({
        queryKey: ['adminAssignmentsByAgentId'],
      });
      queryClient.invalidateQueries({ queryKey: ['assignments'] });
    }
  };

  const handleCancelDeleteAssignment = async () => {
    setRequestDeleteConfirmation(false);
    setDeleteAssignment(null);
  };

  /***** DELETE ALL *****/

  const startDeleteAllAssignments = async () => {
    setRequestDeleteallConfirmation(true);
  };

  const handleDeleteAllAssignments = async () => {
    setRequestDeleteConfirmation(false);
    if (id) {
      await AssignmentService.deleteAssignmentForAgent(Number(id));
      queryClient.invalidateQueries({ queryKey: ['adminAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['adminAssignmentsByUser'] });
      queryClient.invalidateQueries({
        queryKey: ['adminAssignmentsByAgentId'],
      });
      queryClient.invalidateQueries({ queryKey: ['assignments'] });
      router.push('/coaching/assignments');
    }
  };

  const handleCancelDeleteAllAssignments = async () => {
    setRequestDeleteallConfirmation(false);
  };

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <div>
      <DashboardNavbar
        breadcrumbs={[
          { title: 'Coaching' },
          { title: 'Assignments', href: '/coaching/assignments' },
          {
            title: `Buyer Bot: ${agentName}`,
          },
        ]}
        agent={assignment?.agent}
        rightContent={
          <div onClick={() => setModalOpen(true)} className="flex">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size={'sm'}
                  className="w-8 h-8 rounded-full p-0 top-4 right-4"
                >
                  <MoreVerticalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center">
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    startDeleteAllAssignments();
                  }}
                >
                  <Trash2 className="w-4 h-4 mr-2 text-muted-foreground" />
                  <span>Delete All</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button
              variant={'default'}
              className="ml-4"
              disabled={isPilotEnded}
            >
              <CirclePlusIcon className="w-4 h-4 mr-2" />
              New
            </Button>
          </div>
        }
        subContent={
          <div className="text-muted-foreground mt-2">
            <div className="flex flex-col md:flex-row space-x-0 space-y-4 md:space-x-4 md:space-y-0">
              <div className="flex items-center space-x-2">
                <Users2Icon className="w-4 h-4" />
                <p className="text-sm">
                  {assignment.numTotalUsers} rep
                  {assignment?.numTotalUsers === 1 ? '' : 's'} assigned
                </p>
              </div>
            </div>
          </div>
        }
      />
      <div className="flex flex-col pt-4 px-8">
        <AnimatePresence>
          <div className="space-y-10">
            {Object.keys(uaAssignments).map((dueDate, i) => {
              const uaAssignment = uaAssignments[dueDate];

              return (
                <div key={dueDate} className="max-w-lg">
                  <div className="flex justify-between items-center space-x-2">
                    <h3 className="text-lg font-medium">
                      Due {dayjs(dueDate).format('dddd, MMMM D')}
                    </h3>
                    <Badge variant={'secondary'}>
                      {_.sumBy(uaAssignment, 'numCompletedCalls')} /{' '}
                      {_.sumBy(uaAssignment, 'numAssignedCalls')} calls
                    </Badge>
                  </div>
                  <div className="space-y-2 mt-4">
                    {uaAssignment.map((ua, i) => {
                      const user = orgUsers?.find((u) => u.id === ua.userId);
                      const completedRatio =
                        ua.numCompletedCalls / ua.numAssignedCalls;
                      const allRepsExceptCurrent = (orgUsers || [])
                        .filter((u) => u.id !== user?.id)
                        .map((u) => u.id);

                      return (
                        <div key={ua.id}>
                          <motion.div
                            initial={{ opacity: 0, y: -50 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ y: -50 }}
                            transition={{
                              duration: 0.4,
                              delay: i * 0.1,
                            }}
                            className="flex items-center"
                          >
                            <Link
                              href={LinksManager.trainingCalls(
                                `${
                                  allRepsExceptCurrent?.length > 0
                                    ? `?excludeReps=${allRepsExceptCurrent.join(
                                        ',',
                                      )}`
                                    : ''
                                }`,
                              )}
                              className="flex-1"
                            >
                              <Alert className="flex justify-between">
                                <AlertDescription className="flex items-center space-x-2">
                                  <Avatar className="w-8 h-8 mr-2">
                                    {user?.avatar && (
                                      <AvatarImage src={user?.avatar} />
                                    )}
                                    <AvatarFallback className="text-sm">
                                      {user?.firstName?.charAt(0) || ''}
                                      {user?.lastName?.charAt(0) || ''}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <p>
                                      {ua.numCompletedCalls} of{' '}
                                      {ua.numAssignedCalls} calls completed
                                    </p>
                                    <p className="text-muted-foreground text-xs">
                                      {user?.firstName} {user?.lastName}
                                    </p>
                                  </div>
                                  {dayjs(ua.dueDate).isBefore(dayjs()) &&
                                    ua.status !==
                                      AssignmentStatus.COMPLETED && (
                                      <Badge
                                        className="bg-red-400 text-white"
                                        variant={'destructive'}
                                      >
                                        Overdue
                                      </Badge>
                                    )}
                                </AlertDescription>
                                <ProgressCircle
                                  color={
                                    completedRatio >= 0.75
                                      ? 'emerald'
                                      : completedRatio >= 0.4
                                        ? 'yellow'
                                        : 'red'
                                  }
                                  value={completedRatio * 100}
                                  size="sm"
                                >
                                  <span className="text-xs text-gray-700 font-medium">
                                    {Number(completedRatio).toLocaleString(
                                      'en-US',
                                      {
                                        maximumFractionDigits: 0,
                                        minimumFractionDigits: 0,
                                        style: 'percent',
                                      },
                                    )}
                                  </span>
                                </ProgressCircle>
                              </Alert>
                            </Link>

                            <div className="ml-1">
                              <Button
                                variant={'ghost'}
                                size="icon"
                                onMouseDown={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                }}
                                className="rounded-full"
                                onClick={() => startDeleteAssignment(ua)}
                              >
                                <Trash2 className="text-red-400 w-5 h-5" />
                              </Button>
                            </div>
                          </motion.div>
                          {/* <Separator className="my-6" /> */}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </AnimatePresence>
      </div>
      <CreateAssignmentModal
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        onSubmit={() => {
          setModalOpen(false);
        }}
      />

      <DeleteConfirmationModal
        open={requestDeleteConfirmation}
        onCancel={handleCancelDeleteAssignment}
        onConfirm={handleDeleteAssignment}
        title={'Delete assignment'}
        description={'Are you sure you want to delete this assignment?'}
      />
      <DeleteConfirmationModal
        open={requestDeleteAllConfirmation}
        onCancel={handleCancelDeleteAllAssignments}
        onConfirm={handleDeleteAllAssignments}
        title={'Delete all assignments'}
        description={
          'Are you sure you want to delete all the assignments for this bot?'
        }
      />
    </div>
  );
}

export default AssignmentPage;
