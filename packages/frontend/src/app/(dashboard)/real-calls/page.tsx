'use client';

import RealCalls from '@/common/Calls/Real/List';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { Lock } from 'lucide-react';
import Image from 'next/image';

export default function RealCallsPage() {
  const { isLoggedIn, loading, isTemp } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else {
    //<RealCalls showFiltersPanel={false} onFiltersPanelClose={() => {}} />
    return (
      <div
        className={
          'bg-[#FBFBFB] relative h-[100vh] block overflow-hidden px-4 py-4'
        }
      >
        <div className="h-[100vh] w-full overflow-hidden flex flex-col">
          <div className="w-full relative flex-1">
            <Image
              src={'/images/real.png'}
              fill
              className="object-contain object-top"
              alt="real calls scoring"
            />
          </div>
        </div>
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock the power of Real Call Scoring
            </div>
            <div className="text-base mt-2 text-muted-foreground max-w-[50%] text-center">
              Complete your onboarding to gain insights into how your team
              communicates and performs on calls. Unlock actionable feedback and
              coaching opportunities with Hyperbound&apos;s real-time AI call
              scoring capabilities.
            </div>
          </div>
        </div>
      </div>
    );
  }
}
