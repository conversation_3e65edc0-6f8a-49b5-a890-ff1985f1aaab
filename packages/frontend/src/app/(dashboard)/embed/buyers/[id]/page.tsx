'use client';

import AgentSimulationPanel from '@/app/(dashboard)/buyers/[id]/oldPanel';
import { useQueryClient } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, use } from 'react';

function EmbedDemoBuyerSimulation(props: { params: Promise<{ id: string }> }) {
  const params = use(props.params);
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams?.get('invite')) {
      const inviteCode = searchParams?.get('invite');
      queryClient.setQueryData(['hbInvite'], inviteCode);
    }
  }, [searchParams?.get('invite')]);

  return <AgentSimulationPanel agentVapiId={params.id} />;
}

export default EmbedDemoBuyerSimulation;
