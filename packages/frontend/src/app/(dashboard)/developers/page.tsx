'use client';

import { CopyButton } from '@/components/CopyButton';
import PageHeader from '@/components/PageHeader';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import Table, {
  TableCell,
  TableCellHead,
  TableContent,
  TableFooter,
  TableRow,
} from '@/components/ui/Hyperbound/table';
import useUserSession from '@/hooks/useUserSession';
import { useMemo } from 'react';

export default function DevelopersPage() {
  const { dbOrg, subOrganizations, org } = useUserSession();
  const subOrganizationsClean = useMemo(() => {
    if (!subOrganizations) {
      return [];
    }
    return [...subOrganizations].sort((o1, o2) =>
      o1.name.localeCompare(o2.name),
    );
  }, [subOrganizations]);

  return (
    <div className="py-4 px-6">
      <PageHeader title="Developers" />
      <div className="mt-6">
        <p>
          Organization Name: <span className="font-medium">{dbOrg?.name}</span>
        </p>
        <p>
          Organization ID: <span className="font-medium">{dbOrg?.uid}</span>{' '}
          <CopyButton text={dbOrg?.uid || ''} />
        </p>
      </div>
      <div className="mt-4">
        <Table>
          <TableContent>
            <TableRow>
              <TableCellHead>
                <p>Sub Organization</p>
              </TableCellHead>
              <TableCellHead>
                <p>ID</p>
              </TableCellHead>
            </TableRow>
            {subOrganizationsClean.map((o) => {
              return (
                <TableRow key={o.id}>
                  <TableCell>
                    <div className="flex flex-row items-center">
                      <Avatar>
                        {o.logo && <AvatarImage src={o.logo} />}
                        <AvatarFallback className="text-sm">
                          {o.name.charAt(0).toUpperCase() || ''}
                        </AvatarFallback>
                      </Avatar>
                      <p className="ml-2">{o.name}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <p>
                      {o.uid}
                      <span className="ml-2">
                        <CopyButton text={o.uid} />
                      </span>
                    </p>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableContent>
          <TableFooter className="">
            <p className="text-muted-foreground">
              You can use the Organization/Suborganization IDs when calling our
              APIs with the API key.
            </p>
          </TableFooter>
        </Table>
      </div>
    </div>
  );
}
