'use client';

import { CreatePlaylistModal } from '@/common/CreatePlaylist/CreatePlaylistModal';
import DashboardNavbar from '@/common/DashboardNavbar';
import PlaylistList from '@/common/PlaylistList';
import { Button } from '@/components/ui/button';
import useOrg from '@/hooks/useOrg';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import dayjs from 'dayjs';
import { PlusIcon } from 'lucide-react';
import { useState } from 'react';

function PlaylistsPage() {
  const [modalOpen, setModalOpen] = useState(false);
  const { data: org } = useOrg();

  const { isLoggedIn, loading, isTemp } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  const isPilotEnded = dayjs(org?.pilotDetails?.expiryDate).isBefore(dayjs());

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else {
    return (
      <div>
        <DashboardNavbar
          breadcrumbs={[
            {
              title: 'Call History',
            },
            { title: 'Playlists' },
          ]}
          rightContent={
            <div onClick={() => setModalOpen(true)} className="flex">
              <Button variant={'default'} disabled={isPilotEnded}>
                <PlusIcon className="w-4 h-4 mr-2" />
                New
              </Button>
            </div>
          }
        />
        <div className="pt-4 px-8">
          <PlaylistList />
        </div>
        <CreatePlaylistModal
          modalOpen={modalOpen}
          setModalOpen={setModalOpen}
          onSubmit={() => {
            setModalOpen(false);
          }}
        />
      </div>
    );
  }
}

export default PlaylistsPage;
