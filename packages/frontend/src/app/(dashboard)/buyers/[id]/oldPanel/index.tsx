import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import LeaderboardCard from '@/common/LeaderboardCard';
import { CHALLENGE_BOT_VAPI_ID } from '@/common/Sidebar/OldSidebar';
import CallSimulation from '@/components/CallSimulation';
import ColdCallSimulationCard from '@/components/ColdCallSimulationCard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useAgent } from '@/hooks/useAgent';
import useDemoAgentByVapiId from '@/hooks/useDemoAgentByVapiId';
import useUserSession from '@/hooks/useUserSession';
import { AgentCallType, AgentDto } from '@/lib/Agent/types';
import { CallDto } from '@/lib/Call/types';
import { RoleEnum } from '@/lib/User/types';
import Analytics from '@/system/Analytics';
import { BuyerEvents } from '@/system/Analytics/events/BuyerEvents';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { AnimatePresence, motion } from 'framer-motion';
import {
  ChevronDownIcon,
  DoorOpenIcon,
  EditIcon,
  LayoutListIcon,
  LockIcon,
  TrophyIcon,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { redirect, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import LinksManager from '@/lib/linksManager';
import dayjs from 'dayjs';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import CallService from '@/lib/Call';
import { cn } from '@/lib/utils';
import AgentAvatar from '@/components/Avatars/Agent';
import { AppPermissions } from '@/lib/permissions';

interface IProps {
  agentVapiId: string;
}

function AgentSimulationPanel({ agentVapiId }: IProps) {
  const router = useRouter();
  const authInfo = useAuthInfo();
  const { isTemp, dbOrg, canAccess, isInIframe, blurLeaderboard } =
    useUserSession();

  const [latestCall, setLatestCall] = useState<CallDto | null>(null);
  const [callOngoing, setCallOngoing] = useState<boolean>(false);
  const org = useActiveOrg();
  const {
    data: myAgent,
    isFetched: isFetchedMyAgent,
    isFetching: isFetchingMyAgent,
    isLoading: isLoadingMyAgent,
    isError: isErrorMyAgent,
  } = useAgent(agentVapiId);
  const {
    data: demoAgent,
    isLoading: isLoadingDemoAgent,
    isFetched: isFetchedDemoAgent,
    isFetching: isFetchingDemoAgent,
  } = useDemoAgentByVapiId(authInfo?.isLoggedIn ? isErrorMyAgent : true);

  //params used to track gatekeeper calls
  const urlParams = useSearchParams();
  const curUrlParams = new URLSearchParams(urlParams);
  const onDoneCallVapiId = curUrlParams.get('callAfter'); //after gatekeeper call this vapiId
  const prevGatekeeperCallVapiId = curUrlParams.get('prevGatekeeperCall'); //link this gatekeeper call to next call
  let playOnLoad = false;
  if (curUrlParams.get('autodial')) {
    playOnLoad = true;
  }

  const agent = demoAgent || myAgent;
  const isFetching = isFetchingMyAgent || isFetchingDemoAgent;
  const isLoadingAgent = isLoadingDemoAgent || isLoadingMyAgent;

  useEffect(() => {
    if (
      agentVapiId &&
      isFetchedDemoAgent &&
      authInfo?.isLoggedIn &&
      isFetchedMyAgent &&
      !isFetching &&
      !agent
    ) {
      redirect('/404');
    }
  }, [isFetchedDemoAgent, isFetchedMyAgent, agentVapiId]);

  let resumeCallTranscript = [];
  if (agent && agent.research) {
    const existingAgentResearch = JSON.parse(agent.research || '{}');
    if (existingAgentResearch.messages) {
      resumeCallTranscript = existingAgentResearch.messages.split('\n');
      resumeCallTranscript.pop(); //last message is empty.....splitting on \n
    }
  }

  const renderBody = () => {
    if (agent?.vapiId !== CHALLENGE_BOT_VAPI_ID) {
      return (
        <div className="flex-1 flex justify-center min-h-screen">
          <div
            className={cn({
              'p-10': !isInIframe,
              'p-0': isInIframe,
            })}
          >
            <Tabs
              defaultValue="Buyer Bot"
              className="min-w-[300px] md:min-w-[400px] max-w-[600px]"
            >
              {!isInIframe && (
                <TabsList
                  className={
                    'grid w-full mb-8 ' +
                    (resumeCallTranscript.length > 0
                      ? 'grid-cols-3'
                      : 'grid-cols-2')
                  }
                >
                  <TabsTrigger value="Buyer Bot">Buyer Bot</TabsTrigger>
                  {demoAgent ? (
                    <TooltipProvider delayDuration={50}>
                      <Tooltip
                        onOpenChange={(o) => {
                          if (o) {
                            Analytics.track(
                              BuyerEvents.CUSTOMIZE_BUYER_BOT_HOVERED,
                              {
                                agentId: agent?.id,
                              },
                            );
                          }
                        }}
                      >
                        <TooltipTrigger asChild>
                          <span tabIndex={0}>
                            <TabsTrigger
                              value="Leaderboard"
                              className="ml-8"
                              disabled={!!demoAgent || blurLeaderboard}
                            >
                              Leaderboard{' '}
                              {(!!demoAgent || blurLeaderboard) && (
                                <LockIcon className="w-4 h-4 ml-2" />
                              )}
                            </TabsTrigger>
                          </span>
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          <p>
                            Book a demo to see a leaderboard for each buyer bot
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    <>
                      {resumeCallTranscript.length > 0 && (
                        <TabsTrigger value="Resume-Call-Transcript">
                          Previous Transcript
                        </TabsTrigger>
                      )}
                      <TabsTrigger value="Leaderboard">
                        Leaderboard{' '}
                        {blurLeaderboard && (
                          <LockIcon className="w-4 h-4 ml-2" />
                        )}
                      </TabsTrigger>
                    </>
                  )}
                </TabsList>
              )}
              <TabsContent value="Buyer Bot" className="flex justify-center">
                {agent && !authInfo?.isLoggedIn ? (
                  <ColdCallSimulationCard
                    agent={agent}
                    latestCall={latestCall}
                    callOngoing={callOngoing}
                    setCallOngoing={setCallOngoing}
                    setLatestCall={setLatestCall}
                    isLoadingAgent={isLoadingAgent}
                    isDemoAgent={!!demoAgent}
                    location="individual_buyer_page"
                  />
                ) : (
                  authInfo?.isLoggedIn && (
                    <CallSimulation
                      showAgentStatusBtn={canAccess(AppPermissions.MANAGE_BOTS)}
                      showTooltips={true}
                      showScoringMessageOnEnd={true}
                      agent={agent as AgentDto}
                      playOnLoad={playOnLoad}
                      onCallEnds={async (completedCall: CallDto) => {
                        if (
                          agent?.callType === AgentCallType.GATEKEEPER &&
                          onDoneCallVapiId &&
                          onDoneCallVapiId != ''
                        ) {
                          router.push(
                            `/buyers/${onDoneCallVapiId}?autodial=1&prevGatekeeperCall=${completedCall?.id}`,
                          );
                        } else if (
                          prevGatekeeperCallVapiId &&
                          prevGatekeeperCallVapiId != ''
                        ) {
                          //link gatekeeper call to completedCall
                          await CallService.linkGatekeeperCallToAgentCall(
                            Number(prevGatekeeperCallVapiId),
                            completedCall?.id,
                          );
                          setTimeout(() => {
                            router.push(
                              LinksManager.trainingCalls(completedCall?.vapiId),
                            );
                          }, 4000);
                        } else {
                          setTimeout(() => {
                            router.push(
                              LinksManager.trainingCalls(completedCall?.vapiId),
                            );
                          }, 4000);
                        }
                      }}
                    />
                  )
                )}
              </TabsContent>
              <TabsContent value="Leaderboard">
                <AnimatePresence>
                  <motion.div
                    initial={{ scale: 0.9 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0.9 }}
                    transition={{ duration: 0.3 }}
                  >
                    <LeaderboardCard agent={agent as AgentDto} />
                  </motion.div>
                </AnimatePresence>
              </TabsContent>
              <TabsContent value="Resume-Call-Transcript">
                <AnimatePresence>
                  <motion.div
                    initial={{ scale: 0.9 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0.9 }}
                    transition={{ duration: 0.3 }}
                  >
                    {(resumeCallTranscript || []).map(
                      (s: string, i: number) => {
                        const isAssistant = i % 2 === 0;
                        let label = 'Rep';
                        let bg = 'bg-muted/40';
                        let mg = 'ml-10';
                        let float = ' flex flex-col justify-end';
                        let txt = ' text-right';
                        if (isAssistant) {
                          label = 'Buyer';
                          bg = 'bg-muted';
                          float = '';
                          mg = 'mr-10';
                          txt = '';
                        }

                        return (
                          <div key={i} className={'mb-4 ' + mg + float}>
                            <div
                              className={
                                'text-xs text-muted-foreground mb-1 ' + txt
                              }
                            >
                              {label}
                            </div>
                            <div className={'border rounded-lg p-2 ' + bg}>
                              {s}
                            </div>
                          </div>
                        );
                      },
                    )}
                  </motion.div>
                </AnimatePresence>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      );
    }

    return (
      <div className="flex-1 md:flex md:justify-center py-4 px-4 space-x-0 space-y-4 md:space-y-0 md:space-x-4">
        <Tabs
          defaultValue="Product to sell"
          className="min-w-[400px] md:min-w-[600px] max-w-[600px]"
        >
          <TabsList
            className="grid w-full grid-cols-3"
            style={{
              background:
                'linear-gradient(to right, #000000, #5189CE, #A168A2)',
              backgroundImage:
                '-webkit-linear-gradient(to right, #000000, #5189CE, #A168A2)',
            }}
          >
            <TabsTrigger value="Product to sell" className="text-white/90">
              1) What you&apos;re selling
            </TabsTrigger>
            <TabsTrigger value="Competition rules" className="text-white/90">
              2) Competition rules
            </TabsTrigger>
            <TabsTrigger value="Scoring criteria" className="text-white/90">
              3) Scoring criteria
            </TabsTrigger>
            {/* <TabsTrigger value="Buyer details">Buyer details</TabsTrigger> */}
          </TabsList>
          <TabsContent value="Competition rules">
            <Card className="mt-8 md:mt-0">
              <CardHeader>
                <CardTitle>Competition rules</CardTitle>
                <CardDescription>How it works</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <p>
                  The Beat the Bot Challenge is a competition to test your cold
                  calling skills against our AI-powered buyer bot. All
                  participants will be featured on a public leaderboard.
                </p>
                <p className="font-bold">Round 1 (March 18-20):</p>
                <ul className="list-disc pl-4">
                  <li>
                    You have 5 tries to make a cold call to the buyer bot and
                    our AI will score your performance (outlined in the
                    &apos;Scoring criteria&apos; tab)
                  </li>
                  <li>
                    Your goal is to sell DataFind to Alex Dupont, a VP of Sales
                    Operations @ CustomerCenter (more details in the &quot;What
                    you&apos;re selling&quot; tab above)
                  </li>
                </ul>
                <p className="font-bold">Round 2 (March 21-22):</p>
                <ul className="list-disc pl-4">
                  <li>
                    The top 100 sellers on the leaderboard will be chosen to
                    participate in Round 2 and sell to a new buyer bot that will
                    be released on March 21
                  </li>
                  <li>
                    You have 5 tries to make a cold call to the buyer bot and
                    our AI will score your performance (outlined in the
                    &apos;Scoring criteria&apos; tab)
                  </li>
                  <li>
                    Your goal is to sell FlexibleSuccess to Richard Morrison, a
                    Director of Customer Support @ Bolthouse (more details in
                    the &quot;What you&apos;re selling&quot; tab above)
                  </li>
                </ul>
                <p className="font-bold">Winners announced (March 23):</p>
                <ul className="list-disc pl-4">
                  <li>Special prizes for the top 5 sellers</li>
                  <li>Social badges for all finalists</li>
                  <li>
                    Social badges for all participants who book a meeting with
                    the bot
                  </li>
                  <li>
                    Special social badges for all participants who beat their
                    favorite thought leaders and influencers
                  </li>
                </ul>
                <div className="pt-4 space-y-4">
                  <p className="text-sm">Brought to you by:</p>
                  <div className="flex flex-wrap justify-between">
                    <Link href="https://www.hyperbound.ai/" target="_blank">
                      <Image
                        src="/images/competition/hyperbound-gray-logo.svg"
                        width={120}
                        height={80}
                        alt="Hyperbound Logo"
                      />
                    </Link>
                    <Link href="https://sellbetter.xyz/" target="_blank">
                      <Image
                        src="/images/competition/sell-better-logo.svg"
                        width={80}
                        height={40}
                        alt="Sell Better Logo"
                      />
                    </Link>
                    <Link href="https://www.cognism.com/" target="_blank">
                      <Image
                        src="/images/competition/cognism-logo.svg"
                        width={100}
                        height={80}
                        alt="Cognism Logo"
                      />
                    </Link>
                    <Link href="https://reachdesk.com/" target="_blank">
                      <Image
                        src="/images/competition/reachdesk-logo.svg"
                        width={105}
                        height={80}
                        alt="Reachdesk Logo"
                      />
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="Scoring criteria">
            <Card className="mt-8 md:mt-0">
              <CardHeader>
                <CardTitle>Scoring criteria</CardTitle>
                <CardDescription>How you will be scored</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <p className="font-bold">
                  You will be graded on the following:
                </p>
                <ul className="list-disc pl-4">
                  <li>
                    Did you get the prospect to reveal their biggest priorities
                    around customer support? And leverage that in your pitch?
                  </li>
                  <li>
                    Did you get the prospect to stay on the phone with you long
                    enough to turn this into a quality conversation?
                  </li>
                  <li>
                    Did you get the prospect to agree on a time with you to
                    schedule a demo call?
                  </li>
                  <li>Did you speak at between 150 and 180 WPM?</li>
                  <li>Was your talk listen ratio between 50 and 65%? </li>
                  <li>How many filler words did you use?</li>
                  <li>
                    Was your longest monologue between the recommended range of
                    35 to 45 secs?
                  </li>
                </ul>
                <div className="pt-4 space-y-4">
                  <p className="text-sm">Brought to you by:</p>
                  <div className="flex flex-wrap justify-between">
                    <Link href="https://www.hyperbound.ai/" target="_blank">
                      <Image
                        src="/images/competition/hyperbound-gray-logo.svg"
                        width={120}
                        height={80}
                        alt="Hyperbound Logo"
                      />
                    </Link>
                    <Link href="https://sellbetter.xyz/" target="_blank">
                      <Image
                        src="/images/competition/sell-better-logo.svg"
                        width={80}
                        height={40}
                        alt="Sell Better Logo"
                      />
                    </Link>
                    <Link href="https://www.cognism.com/" target="_blank">
                      <Image
                        src="/images/competition/cognism-logo.svg"
                        width={100}
                        height={80}
                        alt="Cognism Logo"
                      />
                    </Link>
                    <Link href="https://reachdesk.com/" target="_blank">
                      <Image
                        src="/images/competition/reachdesk-logo.svg"
                        width={105}
                        height={80}
                        alt="Reachdesk Logo"
                      />
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="Product to sell">
            <Card className="mt-8 md:mt-0">
              <CardHeader>
                <CardTitle>What you&apos;re selling: FlexibleSuccess</CardTitle>
                <CardDescription>
                  FlexibleSuccess is an outsourced customer support company
                  looking to acquire clients in the automotive space.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="mb-4 grid grid-cols-[25px_1fr] items-start last:mb-0 last:pb-0 px-4">
                  <span className="flex h-2 w-2 translate-y-1 rounded-full bg-[#8E71AD]" />
                  <div className="space-y-1">
                    <p className="text-sm">
                      Used by 1000+ DTC brands including Allbirds, Warby Parker,
                      Casper Mattresses, and more. Have not sold to automotive
                      before.
                    </p>
                  </div>
                </div>
                <div className="mb-4 grid grid-cols-[25px_1fr] items-start last:mb-0 last:pb-0 px-4">
                  <span className="flex h-2 w-2 translate-y-1 rounded-full bg-[#8E71AD]" />
                  <div className="space-y-1">
                    <p className="text-sm">
                      Casper has seen a 20 percentage point increase in customer
                      satisfaction since using FlexibleSuccess.
                    </p>
                  </div>
                </div>
                <div className="mb-4 grid grid-cols-[25px_1fr] items-start last:mb-0 last:pb-0 px-4">
                  <span className="flex h-2 w-2 translate-y-1 rounded-full bg-[#8E71AD]" />
                  <div className="space-y-1">
                    <p className="text-sm">
                      All staffing, training, and retraining of agents is
                      managed at scale by FlexibleSuccess, enabling 24/7 support
                      at 20% lower costs than in-house support.
                    </p>
                  </div>
                </div>
                <div className="pt-4 space-y-4">
                  <p className="text-sm">Brought to you by:</p>
                  <div className="flex flex-wrap justify-between">
                    <Link href="https://www.hyperbound.ai/" target="_blank">
                      <Image
                        src="/images/competition/hyperbound-gray-logo.svg"
                        width={120}
                        height={80}
                        alt="Hyperbound Logo"
                      />
                    </Link>
                    <Link href="https://sellbetter.xyz/" target="_blank">
                      <Image
                        src="/images/competition/sell-better-logo.svg"
                        width={80}
                        height={40}
                        alt="Sell Better Logo"
                      />
                    </Link>
                    <Link href="https://www.cognism.com/" target="_blank">
                      <Image
                        src="/images/competition/cognism-logo.svg"
                        width={100}
                        height={80}
                        alt="Cognism Logo"
                      />
                    </Link>
                    <Link href="https://reachdesk.com/" target="_blank">
                      <Image
                        src="/images/competition/reachdesk-logo.svg"
                        width={105}
                        height={80}
                        alt="Reachdesk Logo"
                      />
                    </Link>
                  </div>
                </div>
                {/* <div>
                  {PRODUCT_KEY_FEATURES.map((feature) => (
                    <div
                      key={feature.name}
                      className="mb-4 grid grid-cols-[25px_1fr] items-start last:mb-0 last:pb-0 px-4"
                    >
                      <span className="flex h-2 w-2 translate-y-1 rounded-full bg-[#8E71AD]" />
                      <div className="space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {feature.name}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div> */}
              </CardContent>
            </Card>
          </TabsContent>
          {/* <TabsContent value="Buyer details">
            <Card className="mt-8 md:mt-0">
              <CardHeader>
                <CardTitle>Buyer details</CardTitle>
                <CardDescription>Details about the buyer</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Table>
                  <TableBody>
                    {PROSPECT_INFO_ROWS.map((row) => (
                      <TableRow key={row.key}>
                        <TableCell className="font-semibold">
                          {row.key}
                        </TableCell>
                        <TableCell>{row.value}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent> */}
        </Tabs>
        {agent ? (
          <ColdCallSimulationCard
            agent={agent}
            latestCall={latestCall}
            callOngoing={callOngoing}
            setCallOngoing={setCallOngoing}
            setLatestCall={setLatestCall}
            isLoadingAgent={isLoadingAgent}
            isDemoAgent={!!demoAgent}
            location="individual_buyer_page"
          />
        ) : null}
      </div>
    );
  };

  const firstBreadcrumb = useMemo(() => {
    const bc: BreadcrumbItem = {
      title: 'Buyer Bots',
      href: `/buyers?callType=${agent?.callType}`,
    };

    if (!isTemp) {
      bc['href'] = `/buyers?callType=${agent?.callType}`;
    }

    return bc;
  }, [isTemp]);

  const middleBreadcrumb = useMemo(() => {
    const bc: BreadcrumbItem = {
      title:
        agent?.vapiId === CHALLENGE_BOT_VAPI_ID
          ? 'Beat the Bot Challenge: Round 2 (COMPLETED)'
          : `${
              agent?.callType
                ? `${
                    CALL_TYPE_OPTIONS.find(
                      (item) => item.value === agent.callType,
                    )?.label || agent.callType
                  }s `
                : 'Calls'
            } `,
    };

    if (!isTemp) {
      bc['href'] = `/buyers?callType=${agent?.callType}`;
    }

    return bc;
  }, [isTemp]);

  const isPilotEnded = dayjs(dbOrg?.pilotDetails?.expiryDate).isBefore(dayjs());

  return (
    <div>
      <DashboardNavbar
        hideNav={isInIframe}
        breadcrumbs={
          isInIframe
            ? undefined
            : [
                firstBreadcrumb,
                middleBreadcrumb,
                {
                  title: `${agent?.firstName || ''} ${agent?.lastName || ''} `,
                },
              ]
        }
        subContent={
          agent?.vapiId === CHALLENGE_BOT_VAPI_ID && (
            <p className="text-muted-foreground mt-2 max-w-[580px]">
              Are you ready to prove your cold calling skills are top-notch?
              Shoot your shot & try to beat the bot, out-rank your peers and
              even take out some influencers (March 18 - 23)
            </p>
          )
        }
        titleRight={
          agent?.vapiId !== CHALLENGE_BOT_VAPI_ID && (
            <div className="flex items-center space-x-2">
              {demoAgent && <Badge>FREE DEMO</Badge>}
              {/* {(authInfo?.isLoggedIn || !_.isEmpty(hbDemoInboundForm)) && (
              <Button onClick={onCopyClick} variant={"ghost"}>
                {isCopied ? (
                  <CopyCheckIcon className="w-4 h-4 mr-2 text-green-600" />
                ) : (
                  <CopyIcon className="w-4 h-4 mr-2" />
                )}
                {isCopied ? "Copied! " : "Share URL"}
              </Button>
            )} */}
            </div>
          )
        }
        rightContent={
          <div className="flex space-x-2">
            <div className="flex space-x-4">
              {agent?.gatekeepers && agent?.gatekeepers?.length > 0 && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button disabled={false} variant={'outline'}>
                      <DoorOpenIcon className="w-4 h-4 mr-2" />
                      Choose Gatekeeper
                      <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent align="start" className="max-w-[200px] p-0">
                    {agent?.gatekeepers?.map((gk) => {
                      return (
                        <div
                          key={'gk-' + gk.id}
                          className="flex items-center space-x-2 pl-2 pr-3 py-1 hover:bg-muted/80 hover:transition-all duration-300 text-sm cursor-pointer"
                          onClick={() => {
                            router.push(
                              `/buyers/${gk.vapiId}?callAfter=${agent?.vapiId}`,
                            );
                          }}
                        >
                          <AgentAvatar className="w-6 h-6" agent={gk} />
                          <div className="capitalize">
                            {gk?.firstName || ''} {gk?.lastName || ''}
                          </div>
                        </div>
                      );
                    })}
                  </PopoverContent>
                </Popover>
              )}
              {/* If the user is logged in and seeing a demo agent, then don't show View Calls button */}
              {!(authInfo?.isLoggedIn && demoAgent) && (
                <>
                  {agent?.vapiId !== CHALLENGE_BOT_VAPI_ID && demoAgent && (
                    <TooltipProvider delayDuration={50}>
                      <Tooltip
                        onOpenChange={(o) => {
                          if (o) {
                            Analytics.track(
                              BuyerEvents.CUSTOMIZE_BUYER_BOT_HOVERED,
                              {
                                agentId: agent?.id,
                              },
                            );
                          }
                        }}
                      >
                        <TooltipTrigger asChild>
                          <span tabIndex={0}>
                            <Button disabled variant={'outline'}>
                              <EditIcon className="w-4 h-4 mr-2" />
                              Customize buyer bot
                              <LockIcon className="w-4 h-4 ml-2" />
                            </Button>
                          </span>
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          <p>Book a demo to customize your own buyer bot</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <Link
                    href={LinksManager.trainingCalls(`?buyer=${agent?.id}`)}
                    className="h-min"
                    onClick={() => {
                      Analytics.track(BuyerEvents.VIEW_CALLS_CLICKED, {
                        agentId: agent?.id,
                        from: 'individual_buyer_page',
                      });
                    }}
                  >
                    <Button variant={'default'}>
                      <LayoutListIcon className="w-4 h-4 mr-2" />
                      View calls
                    </Button>
                  </Link>
                </>
              )}
              {agent?.vapiId === CHALLENGE_BOT_VAPI_ID && (
                <Link
                  href={`/leaderboard`}
                  className="h-min"
                  onClick={() => {
                    Analytics.track(BuyerEvents.VIEW_LEADERBOARD_CLICKED, {
                      agentId: agent?.id,
                      from: 'individual_buyer_page',
                    });
                  }}
                >
                  <Button variant={'outline'}>
                    <TrophyIcon className="w-4 h-4 mr-2" />
                    View leaderboard
                  </Button>
                </Link>
              )}
              {authInfo?.isLoggedIn &&
                authInfo?.accessHelper?.isAtLeastRole(
                  org?.orgId as string,
                  RoleEnum.ADMIN,
                ) &&
                !demoAgent && (
                  <Link
                    href={
                      agent?.callType === AgentCallType.FOCUS
                        ? `/buyers/${agentVapiId}/edit/focus?callType=${
                            agent.callType || ''
                          } `
                        : `/buyers/${agentVapiId}/edit/main?callType=${
                            agent?.callType || ''
                          } `
                    }
                    className="h-min"
                    hidden={isPilotEnded}
                  >
                    <Button variant={'default'} disabled={isPilotEnded}>
                      <EditIcon className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                  </Link>
                )}
            </div>
          </div>
        }
      />
      {renderBody()}
    </div>
  );
}

export default AgentSimulationPanel;
