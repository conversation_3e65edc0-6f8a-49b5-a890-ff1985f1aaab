'use client';

import useUserSession from '@/hooks/useUserSession';
import AgentSimulationPanel from './oldPanel';
import { useAgent } from '@/hooks/useAgent';
import CallSimulationPanel from '@/components/CallSimulationPanel';
import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import { useMemo, useState, use } from 'react';
import { CHALLENGE_BOT_VAPI_ID } from '@/common/Sidebar/OldSidebar';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  EditIcon,
  Headphones,
  LayoutListIcon,
  LockIcon,
  MoreHorizontalIcon,
  Loader2,
  TrophyIcon,
} from 'lucide-react';
import { AgentCallType } from '@/lib/Agent/types';
import LinksManager from '@/lib/linksManager';
import useDemoAgentByVapiId from '@/hooks/useDemoAgentByVapiId';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { AppPermissions } from '@/lib/permissions';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Share2Icon } from '@radix-ui/react-icons';
import AgentService from '@/lib/Agent';
import { useSearchParams } from 'next/navigation';
import useLearningModules from '@/hooks/useLearningModules';
import {
  AiRoleplayTask,
  LearningModuleStatus,
  TaskAndAttempts,
  TaskAttempt,
} from '@/lib/LearningModule/types';

function AgentSimulation(props: { params: Promise<{ id: string }> }) {
  const params = use(props.params);
  const searchParams = useSearchParams();

  const { source } = useMemo(() => {
    return {
      source: searchParams.get('source') || 'buyers',
    };
  }, [searchParams]);

  const {
    userId,
    isLoggedIn,
    isTemp,
    canAccess,
    isPilotEnded,
    useOldSimulationPanel,
    isScormEnabled,
  } = useUserSession();

  const { data: learningModulesData } = useLearningModules(
    userId ? [userId] : [],
    LearningModuleStatus.IN_PROGRESS,
    '',
    'asc',
    false,
    0,
    100,
    false,
    source === 'scorm' && userId ? true : false,
  );

  const {
    data: myAgent,
    isLoading: isLoadingMyAgent,
    isError: isErrorMyAgent,
  } = useAgent(params.id);
  const { data: demoAgent, isLoading: isLoadingDemoAgent } =
    useDemoAgentByVapiId(isLoggedIn ? isErrorMyAgent : true);
  const agent = demoAgent || myAgent;
  const isLoadingAgent = isLoadingDemoAgent || isLoadingMyAgent;

  const taskAndAttempts = useMemo(() => {
    if (
      source === 'scorm' &&
      userId &&
      learningModulesData?.learningModules?.length
    ) {
      let currentAgentTask: AiRoleplayTask | undefined = undefined;
      const currentAgentTaskAttempts: TaskAttempt[] = [];

      learningModulesData.learningModules?.forEach((lm) => {
        lm.subModules.forEach((sm) =>
          sm.tasks.forEach((t) => {
            if (t.info?.agent?.id === Number(agent?.id)) {
              currentAgentTask = t.info;
            }

            sm.assigneesStats.forEach((as) => {
              as.tasks.forEach((ts) => {
                if (ts.taskId === t.id) {
                  currentAgentTaskAttempts.push(...(ts.attempts || []));
                }
              });
            });
          }),
        );
      });
      return {
        task: currentAgentTask,
        attempts: currentAgentTaskAttempts,
      };
    }
    return undefined as unknown;
  }, [source, userId, learningModulesData, agent]) as TaskAndAttempts;

  const firstBreadcrumb = useMemo(() => {
    const bc: BreadcrumbItem = {
      title: 'Buyer Bots',
      href: `/buyers?callType=${agent?.callType}`,
    };

    if (!isTemp) {
      bc['href'] = `/buyers?callType=${agent?.callType}`;
    }

    return bc;
  }, [isTemp, agent?.callType]);

  const middleBreadcrumb = useMemo(() => {
    const bc: BreadcrumbItem = {
      title:
        agent?.vapiId === CHALLENGE_BOT_VAPI_ID
          ? 'Beat the Bot Challenge: Round 2 (COMPLETED)'
          : `${
              agent?.callType
                ? `${
                    CALL_TYPE_OPTIONS.find(
                      (item) => item.value === agent.callType,
                    )?.label || agent.callType
                  }s `
                : 'Calls'
            } `,
    };

    if (!isTemp) {
      bc['href'] = `/buyers?callType=${agent?.callType}`;
    }

    return bc;
  }, [isTemp, agent?.callType]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const exportSCORM = async (e: React.MouseEvent<any>) => {
    e.stopPropagation();
    e.preventDefault();
    try {
      setIsExportingSCORM(true);
      await AgentService.exportSCORMPackage(agent?.id as number);
    } catch (error) {
      console.error('Failed to export SCORM package:', error);
    } finally {
      setIsExportingSCORM(false);
    }
  };

  const [isExportingSCORM, setIsExportingSCORM] = useState(false);

  if (isLoadingAgent) {
    return <div></div>;
  } else if (!isLoggedIn) {
    return (
      <div className="h-full flex flex-col">
        <DashboardNavbar
          breadcrumbs={[
            firstBreadcrumb,
            middleBreadcrumb,
            {
              title: `${agent?.firstName || ''} ${agent?.lastName || ''} `,
            },
          ]}
          subContent={
            agent?.vapiId === CHALLENGE_BOT_VAPI_ID && (
              <p className="text-muted-foreground mt-2 max-w-[580px]">
                Are you ready to prove your cold calling skills are top-notch?
                Shoot your shot & try to beat the bot, out-rank your peers and
                even take out some influencers (March 18 - 23)
              </p>
            )
          }
          rightContent={
            <div className="flex space-x-2">
              <div className="flex space-x-4">
                <Link
                  href={LinksManager.trainingCalls(`?buyers=${agent?.id}`)}
                  className="h-min"
                >
                  <Button variant={'default'}>
                    <LayoutListIcon className="w-4 h-4 mr-2" />
                    View calls
                  </Button>
                </Link>

                {canAccess(AppPermissions.MANAGE_BOTS) &&
                  agent?.canOrgEditAgent && (
                    <TooltipProvider delayDuration={50}>
                      <Tooltip>
                        <TooltipTrigger disabled>
                          <Button variant={'outline'} disabled>
                            <EditIcon className="w-4 h-4 mr-2" />
                            Edit
                            <LockIcon className="w-4 h-4 ml-2" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="left">
                          <p>Book a demo to customise your own buyer bot</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}

                {canAccess(AppPermissions.EXPORT_BOTS) && (
                  <TooltipProvider delayDuration={50}>
                    <Tooltip>
                      <TooltipTrigger disabled>
                        <Button variant={'outline'} disabled>
                          <EditIcon className="w-4 h-4 mr-2" />
                          Export as SCORM
                          <LockIcon className="w-4 h-4 ml-2" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="left">
                        <p>Book a demo to customise your own buyer bot</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
          }
        />
        <div className="flex-1 bg-gradient-to-t from-[#1B1D20] to-black/70 flex flex-col">
          <div className="flex items-center">
            <div className="flex-grow"></div>
            <div className="text-sm text-white flex items-center p-1">
              <Headphones size={18} className="mr-2" />
              Call with {agent?.firstName} {agent?.lastName}
            </div>
            <div className="flex-grow"></div>
          </div>
          {!!agent && (
            <CallSimulationPanel
              agent={agent}
              navigateToCallSummary={
                source === 'scorm' && userId ? false : true
              }
              showLeaderboardDateFilter={true}
              taskAndAttempts={taskAndAttempts}
            />
          )}
        </div>
      </div>
    );
  } else if (!useOldSimulationPanel && agent && isLoggedIn) {
    return (
      <div className="h-screen flex flex-col">
        <DashboardNavbar
          breadcrumbs={[
            firstBreadcrumb,
            middleBreadcrumb,
            {
              title: `${agent?.firstName || ''} ${agent?.lastName || ''} `,
            },
          ]}
          subContent={
            agent?.vapiId === CHALLENGE_BOT_VAPI_ID && (
              <p className="text-muted-foreground mt-2 max-w-[580px]">
                Are you ready to prove your cold calling skills are top-notch?
                Shoot your shot & try to beat the bot, out-rank your peers and
                even take out some influencers (March 18 - 23)
              </p>
            )
          }
          titleRight={
            agent?.vapiId !== CHALLENGE_BOT_VAPI_ID && (
              <div className="flex items-center space-x-2">
                {!!demoAgent && <Badge>FREE DEMO</Badge>}
              </div>
            )
          }
          rightContent={
            <div className="flex space-x-2">
              <div className="flex space-x-4">
                {agent.vapiId === CHALLENGE_BOT_VAPI_ID && (
                  <Link href={`/leaderboard`} className="h-min">
                    <Button variant={'outline'}>
                      <TrophyIcon className="w-4 h-4 mr-2" />
                      View leaderboard
                    </Button>
                  </Link>
                )}
                <Link
                  href={LinksManager.trainingCalls(`?buyers=${agent?.id}`)}
                  className="h-min"
                >
                  <Button variant={'default'}>
                    <LayoutListIcon className="w-4 h-4 mr-2" />
                    View calls
                  </Button>
                </Link>
                {canAccess(AppPermissions.MANAGE_BOTS) &&
                  agent?.canOrgEditAgent && (
                    <Link
                      href={
                        agent?.callType === AgentCallType.FOCUS
                          ? `/buyers/${agent.vapiId}/edit/focus?callType=${
                              agent.callType || ''
                            } `
                          : `/buyers/${agent.vapiId}/edit/main?callType=${
                              agent?.callType || ''
                            } `
                      }
                      className="h-min"
                      hidden={isPilotEnded}
                    >
                      <Button variant={'default'} disabled={isPilotEnded}>
                        <EditIcon className="w-4 h-4 mr-2" />
                        Edit
                      </Button>
                    </Link>
                  )}

                {canAccess(AppPermissions.EXPORT_BOTS) && isScormEnabled && (
                  <DropdownMenu>
                    <DropdownMenuTrigger>
                      <Button>
                        <MoreHorizontalIcon className="w-4 h-4 mr-2" />
                        More
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="cursor-pointer"
                        onClick={exportSCORM}
                        disabled={isExportingSCORM}
                      >
                        {isExportingSCORM ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Share2Icon className="w-4 h-4 mr-2" />
                        )}
                        <span>
                          {isExportingSCORM
                            ? 'Exporting SCORM package...'
                            : 'Export SCORM package'}
                        </span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          }
        />
        <div className="flex-1 bg-gradient-to-t from-[#1B1D20] to-black/70 flex flex-col">
          <div className="flex items-center">
            <div className="flex-grow"></div>
            <div className="text-sm text-white flex items-center p-1">
              <Headphones size={18} className="mr-2" />
              Call with {agent.firstName} {agent.lastName}
            </div>
            <div className="flex-grow"></div>
          </div>
          <CallSimulationPanel
            agent={agent}
            navigateToCallSummary={source === 'scorm' && userId ? false : true}
            showLeaderboardDateFilter={true}
            taskAndAttempts={taskAndAttempts}
          />
        </div>
      </div>
    );
  } else if (useOldSimulationPanel && agent && isLoggedIn) {
    return <AgentSimulationPanel agentVapiId={params.id} />;
  }
}

export default AgentSimulation;
