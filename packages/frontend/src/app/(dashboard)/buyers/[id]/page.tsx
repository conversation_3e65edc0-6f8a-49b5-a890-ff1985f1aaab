'use client';

import useUserSession from '@/hooks/useUserSession';
import AgentSimulationPanel from './oldPanel';
import { useAgent } from '@/hooks/useAgent';
import CallSimulationPanel from '@/components/CallSimulationPanel';
import DashboardNavbar, { BreadcrumbItem } from '@/common/DashboardNavbar';
import { useMemo } from 'react';
import { CHALLENGE_BOT_VAPI_ID } from '@/common/Sidebar/OldSidebar';
import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  EditIcon,
  Headphones,
  LayoutListIcon,
  LockIcon,
  TrophyIcon,
} from 'lucide-react';
import { AgentCallType } from '@/lib/Agent/types';
import LinksManager from '@/lib/linksManager';
import useDemoAgentByVapiId from '@/hooks/useDemoAgentByVapiId';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { AppPermissions } from '@/lib/permissions';

function AgentSimulation({ params }: { params: { id: string } }) {
  const { isLoggedIn, isTemp, canAccess, isPilotEnded, useOldSimulationPanel } =
    useUserSession();

  const {
    data: myAgent,
    isLoading: isLoadingMyAgent,
    isError: isErrorMyAgent,
  } = useAgent(params.id);
  const { data: demoAgent, isLoading: isLoadingDemoAgent } =
    useDemoAgentByVapiId(isLoggedIn ? isErrorMyAgent : true);

  const agent = demoAgent || myAgent;
  const isLoadingAgent = isLoadingDemoAgent || isLoadingMyAgent;

  const firstBreadcrumb = useMemo(() => {
    const bc: BreadcrumbItem = {
      title: 'Buyer Bots',
      href: `/buyers?callType=${agent?.callType}`,
    };

    if (!isTemp) {
      bc['href'] = `/buyers?callType=${agent?.callType}`;
    }

    return bc;
  }, [isTemp, agent?.callType]);

  const middleBreadcrumb = useMemo(() => {
    const bc: BreadcrumbItem = {
      title:
        agent?.vapiId === CHALLENGE_BOT_VAPI_ID
          ? 'Beat the Bot Challenge: Round 2 (COMPLETED)'
          : `${
              agent?.callType
                ? `${
                    CALL_TYPE_OPTIONS.find(
                      (item) => item.value === agent.callType,
                    )?.label || agent.callType
                  }s `
                : 'Calls'
            } `,
    };

    if (!isTemp) {
      bc['href'] = `/buyers?callType=${agent?.callType}`;
    }

    return bc;
  }, [isTemp, agent?.callType]);

  if (isLoadingAgent) {
    return <div></div>;
  } else if (!isLoggedIn) {
    return (
      <div className="h-full flex flex-col">
        <DashboardNavbar
          breadcrumbs={[
            firstBreadcrumb,
            middleBreadcrumb,
            {
              title: `${agent?.firstName || ''} ${agent?.lastName || ''} `,
            },
          ]}
          subContent={
            agent?.vapiId === CHALLENGE_BOT_VAPI_ID && (
              <p className="text-muted-foreground mt-2 max-w-[580px]">
                Are you ready to prove your cold calling skills are top-notch?
                Shoot your shot & try to beat the bot, out-rank your peers and
                even take out some influencers (March 18 - 23)
              </p>
            )
          }
          rightContent={
            <div className="flex space-x-2">
              <div className="flex space-x-4">
                <Link
                  href={LinksManager.trainingCalls(`?buyers=${agent?.id}`)}
                  className="h-min"
                >
                  <Button variant={'default'}>
                    <LayoutListIcon className="w-4 h-4 mr-2" />
                    View calls
                  </Button>
                </Link>

                <TooltipProvider delayDuration={50}>
                  <Tooltip>
                    <TooltipTrigger disabled>
                      <Button variant={'outline'} disabled>
                        <EditIcon className="w-4 h-4 mr-2" />
                        Edit
                        <LockIcon className="w-4 h-4 ml-2" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Book a demo to customise your own buyer bot</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          }
        />
        <div className="flex-1 bg-gradient-to-t from-[#1B1D20] to-black/70 flex flex-col">
          <div className="flex items-center">
            <div className="flex-grow"></div>
            <div className="text-sm text-white flex items-center p-1">
              <Headphones size={18} className="mr-2" />
              Call with {agent?.firstName} {agent?.lastName}
            </div>
            <div className="flex-grow"></div>
          </div>
          {!!agent && (
            <CallSimulationPanel
              agent={agent}
              navigateToCallSummary={true}
              showLeaderboardDateFilter={true}
            />
          )}
        </div>
      </div>
    );
  } else if (!useOldSimulationPanel && agent && isLoggedIn) {
    return (
      <div className="h-screen flex flex-col">
        <DashboardNavbar
          breadcrumbs={[
            firstBreadcrumb,
            middleBreadcrumb,
            {
              title: `${agent?.firstName || ''} ${agent?.lastName || ''} `,
            },
          ]}
          subContent={
            agent?.vapiId === CHALLENGE_BOT_VAPI_ID && (
              <p className="text-muted-foreground mt-2 max-w-[580px]">
                Are you ready to prove your cold calling skills are top-notch?
                Shoot your shot & try to beat the bot, out-rank your peers and
                even take out some influencers (March 18 - 23)
              </p>
            )
          }
          titleRight={
            agent?.vapiId !== CHALLENGE_BOT_VAPI_ID && (
              <div className="flex items-center space-x-2">
                {!!demoAgent && <Badge>FREE DEMO</Badge>}
              </div>
            )
          }
          rightContent={
            <div className="flex space-x-2">
              <div className="flex space-x-4">
                {agent.vapiId === CHALLENGE_BOT_VAPI_ID && (
                  <Link href={`/leaderboard`} className="h-min">
                    <Button variant={'outline'}>
                      <TrophyIcon className="w-4 h-4 mr-2" />
                      View leaderboard
                    </Button>
                  </Link>
                )}
                <Link
                  href={LinksManager.trainingCalls(`?buyers=${agent?.id}`)}
                  className="h-min"
                >
                  <Button variant={'default'}>
                    <LayoutListIcon className="w-4 h-4 mr-2" />
                    View calls
                  </Button>
                </Link>
                {canAccess(AppPermissions.MANAGE_BOTS) && (
                  <Link
                    href={
                      agent?.callType === AgentCallType.FOCUS
                        ? `/buyers/${agent.vapiId}/edit/focus?callType=${
                            agent.callType || ''
                          } `
                        : `/buyers/${agent.vapiId}/edit/main?callType=${
                            agent?.callType || ''
                          } `
                    }
                    className="h-min"
                    hidden={isPilotEnded}
                  >
                    <Button variant={'default'} disabled={isPilotEnded}>
                      <EditIcon className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          }
        />
        <div className="flex-1 bg-gradient-to-t from-[#1B1D20] to-black/70 flex flex-col">
          <div className="flex items-center">
            <div className="flex-grow"></div>
            <div className="text-sm text-white flex items-center p-1">
              <Headphones size={18} className="mr-2" />
              Call with {agent.firstName} {agent.lastName}
            </div>
            <div className="flex-grow"></div>
          </div>
          <CallSimulationPanel
            agent={agent}
            navigateToCallSummary={true}
            showLeaderboardDateFilter={true}
          />
        </div>
      </div>
    );
  } else if (useOldSimulationPanel && agent && isLoggedIn) {
    return <AgentSimulationPanel agentVapiId={params.id} />;
  }
}

export default AgentSimulation;
