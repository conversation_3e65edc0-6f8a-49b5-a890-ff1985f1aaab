'use client';
import { use } from "react";

import Leaderboard from '@/common/Calls/AIRoleplay/Summary/tabs/leaderboard';
import AgentAvatar from '@/components/Avatars/Agent';
import PageHeader from '@/components/PageHeader';
import { useAgent } from '@/hooks/useAgent';
import { BriefcaseIcon } from 'lucide-react';

export default function BuyerLeaderboard(
  props: {
    params: Promise<{ id: string }>;
  }
) {
  const params = use(props.params);
  const { data: agentDto } = useAgent(params.id, true);
  return (
    <div className="px-6 py-4 flex flex-col h-full">
      <div className="flex flex-row space-x-4 items-center">
        <AgentAvatar className="w-16 h-16" agent={agentDto} />

        <div className="flex flex-col">
          <PageHeader
            title={`${agentDto?.firstName} ${agentDto?.lastName} - Leaderboard`}
          />

          <div className="flex items-center space-x-2">
            <BriefcaseIcon className="w-4 h-4" />
            <p className="text-sm">
              {agentDto?.jobTitle || ''} @ {agentDto?.companyName || ''}
            </p>
          </div>
        </div>
      </div>
      <Leaderboard agentId={agentDto?.id} className=" mt-5 flex-1 pb-16" />
    </div>
  );
}
