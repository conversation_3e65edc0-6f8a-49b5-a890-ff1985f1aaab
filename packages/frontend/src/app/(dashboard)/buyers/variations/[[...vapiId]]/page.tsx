'use client';

import BuyerVariationsEditPage from '../../../../../common/Variations/BuyerVariationsEditPage';
import FindBot from '../../../../../common/Variations/FindBot';
import { usePathname, useRouter } from 'next/navigation';

export default function VariationsPage({
  params,
}: {
  params: { vapiId: string };
}) {
  const router = useRouter();
  const pathname = usePathname();

  const parentAgentId = params.vapiId ? params.vapiId[0] : '';

  const selectBot = (vapiId: string) => {
    router.push(`${pathname}/${vapiId}`);
  };

  if (parentAgentId != '') {
    return <BuyerVariationsEditPage vapiId={parentAgentId} />;
  } else {
    return <FindBot onBotSelected={selectBot} />;
  }
}
