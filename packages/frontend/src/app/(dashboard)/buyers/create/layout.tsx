'use client';

import CreateBuyerForm from '@/common/CreateBuyerForm';
import { useAuthInfo } from '@propelauth/react';
import { redirect } from 'next/navigation';
import React from 'react';

interface ICreateBuyerBotLayoutProps {
  children: React.ReactNode;
}

function CreateBuyerBotLayout({ children }: ICreateBuyerBotLayoutProps) {
  const authInfo = useAuthInfo();

  if (authInfo?.loading) {
    return <div></div>;
  }

  if (!authInfo.isLoggedIn) {
    redirect('/404');
  }

  return <CreateBuyerForm title="Create new">{children}</CreateBuyerForm>;
}

export default CreateBuyerBotLayout;
