'use client';

import Sidebar from '@/common/Sidebar';
import MaintenanceBanner from '@/components/MaintenanceBanner';
import TopBanner from '@/components/TopBanner';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { DashboardContextProvider } from '@/contexts/DashboardContext';
import useMaintenance from '@/hooks/useMaintenance';
import useUserSession from '@/hooks/useUserSession';
import { cn } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { SidebarEvents } from '@/system/Analytics/events/SidebarEvents';
import { useAuthInfo, useLogoutFunction } from '@propelauth/react';
import { TooltipArrow } from '@radix-ui/react-tooltip';
import { useQueryClient } from '@tanstack/react-query';
import { CheckIcon, CopyIcon } from 'lucide-react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import posthog from 'posthog-js';
import { useEffect, useRef, useState } from 'react';
import { Id, toast } from 'react-toastify';
// import { useAuthInfo, useRedirectFunctions } from "@propelauth/react";
import OldSidebar from '@/common/Sidebar/OldSidebar';
import CompetitionWelcomeModal from '@/components/Competitions/CompetitionWelcomeModal';

export const PARTNER_ORG_IDS = [
  '9f266bf9-2f37-4d01-b0fb-c9fe69933a88',
  '56dfe6cf-2cbc-49a2-aec6-b307d0213168',
];

function DashboardLayout({
  children, // will be a page or nested layout
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const authInfo = useAuthInfo();
  const [navbarTitle, setNavbarTitle] = useState<string>('Buyers');
  const { data: maintenance } = useMaintenance(!authInfo.isLoggedIn);
  const { isCompetitionOrg, dbUser, isInIframe, hideHyperboundLogoInIframe } =
    useUserSession();
  const [showCompetitionWelcomeModal, setShowCompetitionWelcomeModal] =
    useState(false);

  const logout = useLogoutFunction();
  const [isCopied, setCopied] = useState(false);
  const toastId = useRef<Id | null>(null);

  useEffect(() => {
    if (isCompetitionOrg && !dbUser?.metadata?.linkedInUrl) {
      setShowCompetitionWelcomeModal(true);
    }
  }, [isCompetitionOrg, dbUser]);

  const onLogout = async () => {
    Analytics.track(SidebarEvents.LOGOUT_CLICKED);
    if (authInfo?.isLoggedIn) {
      posthog.reset();
      logout(true);
      localStorage?.removeItem(
        process.env.NEXT_PUBLIC_ACTIVE_ORG_DATA_LOCAL_STORAGE_KEY as string,
      );
    } else {
      localStorage.removeItem(
        process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY as string,
      );
    }
    await queryClient.invalidateQueries();
  };

  const onCopy = async () => {
    await navigator.clipboard.writeText('https://www.hyperbound.ai');
    posthog.capture('copied_hyperbound_url_to_clipboard');
    setCopied(true);

    setTimeout(() => {
      setCopied(false);
    }, 2000);
    if (!toast.isActive(toastId.current as Id)) {
      toastId.current = toast.success('Copied hyperbound.ai to clipboard!');
    }
  };

  if (authInfo.loading) {
    return (
      <div className="w-screen h-screen flex items-center justify-center">
        {/* <span className="relative flex h-10 w-10">
          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-gray-100 opacity-75"></span>
          <span className="relative inline-flex rounded-full h-10 w-10 bg-gray-200"></span>
        </span> */}
      </div>
    );
  } else if (!authInfo.isLoggedIn) {
    if (isInIframe) {
      window.parent?.postMessage(
        {
          type: 'REDIRECT_TO_LOGIN',
          payload: {
            postLoginRedirectUrl: window.location.href,
          },
        },
        '*',
      );
    }
  }

  const SidebarComponent = !authInfo.isLoggedIn ? OldSidebar : Sidebar;

  return (
    <DashboardContextProvider value={{ navbarTitle, setNavbarTitle }}>
      <section
        className={cn('flex w-full relative h-screen', {
          'pt-16': !authInfo.isLoggedIn && !maintenance && !isInIframe,
          'pt-[108px]': !authInfo.isLoggedIn && maintenance && !isInIframe,
          'pt-[52px]': authInfo.isLoggedIn && maintenance,
        })}
      >
        {maintenance?.bannerMessage && <MaintenanceBanner />}
        {!authInfo.isLoggedIn && !isInIframe && <TopBanner />}
        {!isInIframe && <SidebarComponent />}
        <div
          className={cn('flex-1 z-10 ml-[0px] min-w-0', {
            // "md:ml-[354px]": !authInfo.isLoggedIn,
            // "md:ml-[254px]": authInfo.isLoggedIn,
            // "md:ml-[254px]": !isInIframe,
            // only need margins in iframes if pathname != calls or if we want to show hyp logo
            'ml-[68px]':
              (!isInIframe ||
                (pathname !== '/calls' && !hideHyperboundLogoInIframe)) &&
              authInfo.isLoggedIn,
            'md:ml-[254px]': !isInIframe && !authInfo.isLoggedIn,
          })}
        >
          {/* <DashboardNavbar /> */}
          <div className="flex justify-center items-center w-full h-full">
            <div className="w-full h-full">{children}</div>
            {isInIframe && pathname === '/home' && (
              <Button
                variant="outline"
                className="absolute bottom-4 left-4"
                onClick={onLogout}
              >
                Log out
              </Button>
            )}
            {isInIframe && !hideHyperboundLogoInIframe && (
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild className="fixed top-4 left-4 ">
                    <span tabIndex={0}>
                      <div onClick={onCopy} role="button" className="space-y-1">
                        <Image
                          src="/images/square-logo-transparent.svg"
                          alt="Hyperbound square logo"
                          width={20}
                          height={20}
                          className="opacity-50"
                        />
                      </div>
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <TooltipArrow />
                    <p>Copy hyperbound.ai</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {isInIframe && !hideHyperboundLogoInIframe && (
              <TooltipProvider delayDuration={50}>
                <Tooltip>
                  <TooltipTrigger asChild className="fixed bottom-4 left-4">
                    <span tabIndex={0}>
                      <div
                        onClick={onCopy}
                        role="button"
                        className={cn('space-y-1', {
                          'pointer-events-none': isCopied,
                        })}
                      >
                        <div className="flex items-center space-x-2 text-muted-foreground">
                          <p className="text-xs">Powered by</p>
                          {isCopied ? (
                            <CheckIcon className="w-4 h-4 text-green-500" />
                          ) : (
                            <CopyIcon className="w-4 h-4" />
                          )}
                        </div>
                        <Image
                          src="/images/black-logo-with-text.svg"
                          alt="Hyperbound full logo"
                          width={120}
                          height={20}
                        />
                      </div>
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <TooltipArrow />
                    <p>Copy hyperbound.ai</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
        <CompetitionWelcomeModal
          modalOpen={showCompetitionWelcomeModal}
          setModalOpen={setShowCompetitionWelcomeModal}
          isClosable={false}
          onSubmit={() => {
            setShowCompetitionWelcomeModal(false);
          }}
        />
      </section>
    </DashboardContextProvider>
  );
}

export default DashboardLayout;
