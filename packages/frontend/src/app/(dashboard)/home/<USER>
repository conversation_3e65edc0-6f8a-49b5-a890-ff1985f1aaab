import DashboardNavbar from '@/common/DashboardNavbar';
import CallSimulationPanel from '@/components/CallSimulationPanel';
import PageHeader from '@/components/PageHeader';
import { useCompetitionOrgAgent } from '@/hooks/useCompetitionOrgAgent';
import useUserSession from '@/hooks/useUserSession';
import { Headphones } from 'lucide-react';

function CompetitionHomePage() {
  const { data: agent } = useCompetitionOrgAgent();
  const { dbOrg } = useUserSession();

  return (
    <div className={'relative h-[100vh] block overflow-hidden'}>
      <div className="h-[100vh] overflow-auto flex flex-col">
        <div className="py-4 px-6">
          <PageHeader title={`${dbOrg?.name}`} />
        </div>
        {!!agent && (
          <div className="flex-1 bg-gradient-to-t from-[#1B1D20] to-black/70 flex flex-col">
            <div className="flex items-center">
              <div className="flex-grow"></div>
              <div className="text-sm text-white flex items-center p-1">
                <Headphones size={18} className="mr-2" />
                Call with {agent.firstName} {agent.lastName}
              </div>
              <div className="flex-grow"></div>
            </div>
            <CallSimulationPanel
              agent={agent}
              navigateToCallSummary={true}
              showLeaderboardDateFilter={true}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default CompetitionHomePage;
