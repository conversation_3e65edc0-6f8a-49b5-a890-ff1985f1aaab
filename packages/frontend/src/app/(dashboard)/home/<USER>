'use client';

import { useAuthInfo } from '@propelauth/react';
import { redirect } from 'next/navigation';
import useUserSession from '@/hooks/useUserSession';
import HomePage from '@/common/Home';
import PClubCompetitionHome from '@/common/Home/Competitions/pclub';

function HomePageRouter() {
  const authInfo = useAuthInfo();

  const { isCompetitionOrg, hideHomePage } = useUserSession();

  // Unlike the higher order functions, we need to check the loading case now
  if (authInfo?.loading) {
    return <div />;
  } else if (!authInfo.isLoggedIn) {
    redirect('https://hyperbound.ai');
  }

  if (isCompetitionOrg) {
    return <PClubCompetitionHome />;
  } else if (hideHomePage) {
    redirect('/ai-roleplay');
  } else {
    return <HomePage />;
  }
}

export default HomePageRouter;
