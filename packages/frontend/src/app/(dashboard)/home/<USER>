'use client';

import AdminAssignmentsCard from '@/common/AdminAssignmentsCard';
import LeaderboardCard from '@/common/LeaderboardCard';
import RepAssignmentsCard from '@/common/RepAssignmentsCard';
import DashboardNavbar from '@/common/DashboardNavbar';
import { Button } from '@/components/ui/button';
import { RoleEnum } from '@/lib/User/types';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import html2canvas from 'html2canvas';
import {
  ArrowRightIcon,
  BarChart2,
  DownloadIcon,
  Lock,
  MoveRightIcon,
  NotebookTabsIcon,
  TerminalIcon,
} from 'lucide-react';
import { redirect } from 'next/navigation';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { LightningBoltIcon } from '@radix-ui/react-icons';
import Link from 'next/link';
import useOrgAgents from '@/hooks/useOrgAgents';
import useOrg from '@/hooks/useOrg';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import useUserSession from '@/hooks/useUserSession';
import { useEffect, useRef } from 'react';
import CompetitionHomePage from './CompetitionHomePage';

function HomePageOld() {
  const authInfo = useAuthInfo();
  const org = useActiveOrg();
  const { data: agents } = useOrgAgents();
  const { data: dbOrg } = useOrg();

  const { blurSecondaryPages, isCompetitionOrg } = useUserSession();

  // Unlike the higher order functions, we need to check the loading case now
  if (authInfo?.loading) {
    return <div />;
  } else if (!authInfo.isLoggedIn) {
    redirect('https://hyperbound.ai');
  }

  if (isCompetitionOrg) {
    return <CompetitionHomePage />;
  }

  return (
    <div className={'relative h-[100vh] block overflow-hidden'}>
      <div className="h-[100vh] overflow-auto">
        <DashboardNavbar breadcrumbs={[{ title: 'Home' }]} />
        <div className="px-8 mt-6">
          <div className="mt-6">
            <p className="text-base font-semibold mb-4">New features</p>
            <div>
              <div className="flex space-x-4 mr-2 mt-2">
                <Alert className="pb-4 pt-3 rounded-xl">
                  <LightningBoltIcon className="h-5 w-5" />
                  <AlertTitle className="font-semibold text-base">
                    New Hyperbound interface!
                  </AlertTitle>
                  <AlertDescription>
                    We are excited to unveil our new UI! In the following video,
                    Mia is going to introduce you to the new interface and show
                    you how to navigate through it.
                  </AlertDescription>
                  <div className="mt-4 ">
                    <iframe
                      className="rounded-lg shadow-lg"
                      width="560"
                      height="315"
                      src="https://www.youtube.com/embed/3ClJ_eS1r0o?si=_CxcMCx09wYmQq5W"
                      title="YouTube video player"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      referrerPolicy="strict-origin-when-cross-origin"
                      allowFullScreen
                    ></iframe>
                  </div>
                </Alert>
              </div>
            </div>
            {/*
            <div className="grid grid-cols-3 flex-wrap w-full ">
               <div className="flex space-x-4 mr-2 mt-2">
                <Alert className="pb-4 pt-3 rounded-xl">
                  <LightningBoltIcon className="h-5 w-5" />
                  <AlertTitle className="font-semibold text-base">
                    Introducing Call Blitzes
                  </AlertTitle>
                  <AlertDescription className="h-[80px]">
                    Simulate an autodialer experience by choosing a group of bots
                    and starting your first call blitz session!
                  </AlertDescription>
                  <div className="mt-4">
                    <Link href="/call-blitz">
                      <Button
                        variant="default"
                        className="bg-teal-600 hover:bg-teal-600/90"
                      >
                        Start call blitz
                        <MoveRightIcon className="ml-2 w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                </Alert>
              </div>
              <div className="flex space-x-4 mr-2 mt-2">
                <Alert className="pb-4 pt-3 rounded-xl">
                  <BarChart2 className="h-5 w-5" />
                  <AlertTitle className="font-semibold text-base">
                    New Analytics
                  </AlertTitle>
                  <AlertDescription className="h-[80px]">
                    Check out our analytics page, now you can view and personalize
                    your data any way you like.
                  </AlertDescription>
                  <div className="mt-4">
                    <Link href="/analytics">
                      <Button
                        variant="default"
                        className="bg-teal-600 hover:bg-teal-600/90"
                      >
                        Go to analytics
                        <MoveRightIcon className="ml-2 w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                </Alert>
              </div>
              <div className="flex space-x-4 mr-2 mt-2">
                <Alert className="pb-4 pt-3 rounded-xl">
                  <NotebookTabsIcon className="h-5 w-5" />
                  <AlertTitle className="font-semibold text-base">
                    Knowledge Gap Analysis
                  </AlertTitle>
                  <AlertDescription className="h-[80px]">
                    Request your first personalized knowledge gap analysis of your entire team
                  </AlertDescription>
                  <div className="mt-4">
                    <Link href="/coaching/knowledge-gap">
                      <Button
                        variant="default"
                        className="bg-teal-600 hover:bg-teal-600/90"
                      >
                        Request knowledge gap
                        <MoveRightIcon className="ml-2 w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                </Alert>
              </div>
            </div>
            */}
            <div className="mt-8">
              <p className="text-base font-semibold">Activity</p>
              <div className="flex w-full flex-wrap mt-4 mb-6 items-stretch ">
                {dbOrg?.pilotDetails ? (
                  <div className="flex flex-wrap">
                    {(agents || [])
                      ?.sort((a, b) =>
                        `${a.firstName} ${a.lastName}`.localeCompare(
                          `${b.firstName} ${b.lastName}`,
                        ),
                      )
                      .map((agent) => (
                        <div key={agent.id} className="mr-2 mt-2">
                          <LeaderboardCard agent={agent} />
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="mr-2 mt-2">
                    <LeaderboardCard />
                  </div>
                )}
                <div className="mt-2">
                  {authInfo?.accessHelper?.isAtLeastRole(
                    org?.orgId as string,
                    RoleEnum.ADMIN,
                  ) ? (
                    <AdminAssignmentsCard showHeader={true} />
                  ) : (
                    <RepAssignmentsCard />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {(blurSecondaryPages || isCompetitionOrg) && (
        <div
          className="absolute top-0 left-0 right-0 bottom-0"
          style={{
            background:
              ' linear-gradient(to bottom, rgba(255, 255,255, 0) 0%, rgba(255, 255,255, 0.8) 30%, rgba(255, 255,255, 0.98) 100%)',
          }}
        >
          <div className="w-full flex items-center justify-center flex-col h-full pb-[14%]">
            <div className="flex-1"></div>
            <div className="text-muted-foreground flex flex-col justify-center items-center">
              <div>
                <Lock size={16} />
              </div>
              <div className="text-xs mt-2">Locked</div>
            </div>
            <div className="font-semibold text-lg mt-4">
              Unlock custom rep home
            </div>
            <div className="text-base mt-2 text-muted-foreground">
              Complete your onboarding to unlock a custom home page for your
              reps
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default HomePageOld;
