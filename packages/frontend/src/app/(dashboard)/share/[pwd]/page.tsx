'use client';

import RealCallNewSummary from '@/common/Calls/Real/NewSummary';
import { RealCallsService } from '@/lib/Integrations';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function RealCallSummaryPublicPageMoreDirect() {
  const params = useParams();
  const pwd = String(params.pwd);
  const [callId, setCallId] = useState(0);

  useEffect(() => {
    (async () => {
      const res = await RealCallsService.getCallId(pwd);
      if (res) {
        setCallId(res.id);
      }
    })();
  }, [pwd]);

  if (!callId) {
    return <div />;
  }

  return (
    <RealCallNewSummary
      callId={callId}
      pwd={String(params.pwd)}
      isPublicVersion={true}
    />
  );
}
