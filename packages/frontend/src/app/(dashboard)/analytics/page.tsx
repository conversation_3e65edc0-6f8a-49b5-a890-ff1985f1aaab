'use client';

import Analytics from '@/common/Analytics';
import AnalyticsOld from '@/common/AnalyticsOld';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';

export default function AnalyticsPage() {
  const { isLoggedIn, loading, isTemp, dbOrg } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else if (dbOrg?.frontEndConf?.isLightdashEnabled ?? false) {
    return <Analytics />;
  } else {
    return <AnalyticsOld />;
  }
}
