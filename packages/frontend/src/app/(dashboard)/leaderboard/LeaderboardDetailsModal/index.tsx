import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { LeaderboardDto } from '@/lib/Leaderboard/types';

interface ILeaderboardDetailsModalProps {
  modalOpen: boolean;
  setModalOpen: (modalOpen: boolean) => void;
  leaderboardItems: LeaderboardDto[];
  activeLeaderboardItem?: LeaderboardDto;
  isLoading: boolean;
  round: number;
}

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import { useRouter } from 'next/navigation';
import { CHALLENGE_BOT_VAPI_ID } from '@/common/Sidebar/OldSidebar';
import { InfoIcon, LayoutListIcon, Loader2Icon, PhoneIcon } from 'lucide-react';
import useDemoAgents from '@/hooks/useDemoAgents';
import { useAuthInfo } from '@propelauth/react';
import Link from 'next/link';
import { useMemo } from 'react';
import Analytics from '@/system/Analytics';
import { BuyerEvents } from '@/system/Analytics/events/BuyerEvents';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { CallEvents } from '@/system/Analytics/events/CallEvents';
import LinksManager from '@/lib/linksManager';

const stats = [
  {
    name: 'Talk Speed',
    key: 'talkSpeed',
    suffix: 'wpm',
    weight: 0.075,
    info: 'How fast did you speak? Recommended range is between 150 WPM and 180 WPM.',
  },
  {
    name: 'Talk/Listen Ratio',
    key: 'talkListenRatio',
    weight: 0.075,
    info: 'How much you talked vs. how much you listened. Recommended range is between 0.5 and 0.65',
  },
  {
    name: 'Filler Words',
    key: 'fillerWords',
    suffix: 'wpm',
    weight: 0.075,
    info: "How many filler words like 'um' and 'uh' did you use per min?",
  },
  {
    name: 'Longest Monologue',
    key: 'longestMonologue',
    suffix: 'secs',
    weight: 0.075,
    info: 'How many secs was your longest monologue? Recommended range is between 35 - 45 secs',
  },
];

const getSectionWeight = (sectionTitle: string) => {
  if (sectionTitle === 'Opener') return 0.1;
  if (sectionTitle === 'Discovery' || sectionTitle === 'Gating') return 0.1;
  if (sectionTitle === 'Closing') return 0.5;
};

function LeaderboardDetailsModal({
  modalOpen,
  setModalOpen,
  leaderboardItems = [],
  activeLeaderboardItem,
  isLoading,
  round,
}: ILeaderboardDetailsModalProps) {
  const authInfo = useAuthInfo();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const { data: demoAgents, isLoading: isLoadingDemoAgents } = useDemoAgents(
    !authInfo?.isLoggedIn,
  );

  const sections = [
    {
      name: 'Opener',
      info: 'Did you get the prospect to stay on the phone with you long enough to turn this into a quality conversation?',
    },
    {
      name: 'Discovery',
      info:
        round === 1
          ? 'Did you get the prospect to reveal their current biggest priority around staff management?'
          : 'Did you get the prospect to reveal their biggest priorities around customer support? And leverage that in your pitch?',
    },
    {
      name: 'Closing',
      info: 'Did you get the prospect to agree on a time with you to schedule a demo call?',
    },
  ];

  const allBuyersExceptCurrent = useMemo(() => {
    if (!demoAgents?.length) return [];

    return demoAgents
      .filter((a) => a.vapiId !== CHALLENGE_BOT_VAPI_ID)
      .map((a) => a.id);
  }, [demoAgents?.length]);

  let hideOtherColumns = false;

  const firstItem = leaderboardItems?.[0];

  if (
    firstItem?.demoInboundFormResponse?.id !== Number(hbDemoInboundForm?.id)
  ) {
    hideOtherColumns = true;
    leaderboardItems = leaderboardItems.filter(
      (item) => item.id === activeLeaderboardItem?.id,
    );
  }

  const rows: any[] = [];
  (firstItem?.latestScorecard?.criteria || []).forEach((criterion: any) => {
    const row: any = {
      category: `${criterion.sectionTitle} (${Number(
        getSectionWeight(criterion.sectionTitle),
      ).toLocaleString('en-US', {
        style: 'percent',
      })})`,
    };
    leaderboardItems.forEach((item, i) => {
      const itemCriterion = item.latestScorecard?.criteria.find(
        (c: any) => c.sectionTitle === criterion.sectionTitle,
      );
      row[`Try${i + 1}`] = `${itemCriterion?.passedCriteriaCount || 0} / ${
        itemCriterion?.totalCriteriaCount || 0
      }`;
    });
    if (!hideOtherColumns) {
      new Array(firstItem?.demoInboundFormResponse?.challengeNumTriesLeft || 0)
        .fill(0)
        .forEach((_, i) => {
          row[`Try${leaderboardItems?.length + i + 1}`] = `-`;
        });
    }
    rows.push(row);
  });

  stats.forEach((stat) => {
    const row: any = {
      category: `${stat.name} (${Number(stat.weight).toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 1,
        style: 'percent',
      })})`,
    };
    leaderboardItems.forEach((item, i) => {
      row[`Try${i + 1}`] = `${item.latestScorecard?.[stat.key] || 0} ${
        stat.suffix || ''
      }`;
    });
    if (!hideOtherColumns) {
      new Array(firstItem?.demoInboundFormResponse?.challengeNumTriesLeft || 0)
        .fill(0)
        .forEach((_, i) => {
          row[`Try${leaderboardItems?.length + i + 1}`] = `-`;
        });
    }
    rows.push(row);
  });

  const renderInfoButton = (category: string) => {
    const categoryName = category.split('(')[0]?.trim();
    const stat = stats.find((stat) => stat.name === categoryName);

    let info = stat?.info;

    if (!stat) {
      const section = sections.find((section) => section.name === categoryName);

      if (!section) {
        return null;
      }

      info = section.info;
    }

    return (
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger>
            <InfoIcon className="w-4 h-4 text-muted-foreground" />
          </TooltipTrigger>
          <TooltipContent side="right">{info || ''}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  return (
    <Dialog open={modalOpen} onOpenChange={setModalOpen}>
      <DialogContent className="close-btn max-w-[1000px] overflow-hidden overflow-x-scroll">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Scoring Breakdown
          </DialogTitle>
          <DialogDescription>
            for {firstItem?.demoInboundFormResponse?.name}
            {firstItem?.demoInboundFormResponse?.id ===
            Number(hbDemoInboundForm?.id)
              ? ' (You)'
              : ''}
          </DialogDescription>
          {isLoading ? (
            <Loader2Icon />
          ) : (
            <Table>
              <TableCaption>Final scores are out of 100</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[180px]">Category</TableHead>
                  {leaderboardItems.map((item, i) => (
                    <TableHead key={i}>
                      <p>
                        {firstItem?.demoInboundFormResponse?.id !==
                        Number(hbDemoInboundForm?.id)
                          ? 'Best Try'
                          : `Try ${i + 1}`}
                      </p>
                    </TableHead>
                  ))}
                  {!hideOtherColumns &&
                    new Array(
                      firstItem?.demoInboundFormResponse
                        ?.challengeNumTriesLeft || 0,
                    )
                      .fill(0)
                      .map((_, i) => {
                        return (
                          <TableHead key={leaderboardItems?.length + i + 1}>
                            Try {leaderboardItems?.length + i + 1}
                          </TableHead>
                        );
                      })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {rows.map((row) => (
                  <TableRow key={row.category}>
                    {Object.keys(row).map((key, i) => {
                      return (
                        <TableCell
                          className={cn({
                            'bg-green-100':
                              i >= 1 &&
                              leaderboardItems[i - 1]?.id ===
                                activeLeaderboardItem?.id,
                            'flex space-x-2 items-center': key === 'category',
                          })}
                          key={key}
                        >
                          <p>{row[key]}</p>
                          {renderInfoButton(row[key])}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
              <TableFooter>
                <TableRow>
                  <TableCell>Score</TableCell>
                  {leaderboardItems?.map((item, i) => (
                    <TableCell
                      className={cn('font-bold', {
                        'bg-green-100': item.id === activeLeaderboardItem?.id,
                      })}
                      key={item.id}
                    >
                      {Number(
                        item.latestScorecard?.aggregateScore * 100,
                      ).toLocaleString('en-US', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 1,
                      })}
                    </TableCell>
                  ))}
                  {!hideOtherColumns &&
                    new Array(
                      firstItem?.demoInboundFormResponse
                        ?.challengeNumTriesLeft || 0,
                    )
                      .fill(0)
                      .map((_, i) => {
                        return (
                          <TableCell key={leaderboardItems?.length + i + 1}>
                            -
                          </TableCell>
                        );
                      })}
                </TableRow>
                {!hideOtherColumns && !authInfo?.isLoggedIn && (
                  <TableRow>
                    <TableCell>Call Transcript</TableCell>
                    {leaderboardItems.map((item, i) => {
                      return (
                        <TableCell
                          className={cn({
                            'bg-green-100':
                              item.id === activeLeaderboardItem?.id,
                          })}
                          key={item.id}
                        >
                          <Link
                            target="_blank"
                            className="text-[#8E71AD] underline underline-offset-2"
                            href={LinksManager.trainingCalls(
                              item.latestCall?.vapiId,
                            )}
                          >
                            View
                          </Link>
                        </TableCell>
                      );
                    })}
                  </TableRow>
                )}
              </TableFooter>
            </Table>
          )}
        </DialogHeader>
        <DialogFooter>
          {Number(hbDemoInboundForm?.id) ===
            activeLeaderboardItem?.demoInboundFormResponse?.id && (
            <>
              <Link
                href={LinksManager.trainingCalls(
                  `?buyers=${activeLeaderboardItem?.agent?.id}`,
                )}
                className="h-min"
                onClick={() => {
                  Analytics.track(BuyerEvents.VIEW_CALLS_CLICKED, {
                    agentId: 497,
                    from: 'leaderboard',
                  });
                }}
              >
                <Button variant="outline">
                  <LayoutListIcon className="w-4 h-4 mr-2" />
                  View my calls/transcripts
                </Button>
              </Link>
              <Link
                href={`/buyers/${CHALLENGE_BOT_VAPI_ID}`}
                onClick={() => {
                  Analytics.track(CallEvents.START_NEW_CALL_CLICKED, {
                    agentId: CHALLENGE_BOT_VAPI_ID,
                    from: 'beat_the_bot_leaderboard',
                  });
                }}
              >
                <Button variant={'default'}>
                  <PhoneIcon className="w-4 h-4 mr-2" /> Start new call (
                  {hbDemoInboundForm?.challengeNumTriesLeft}
                  {hbDemoInboundForm?.challengeNumTriesLeft === 1
                    ? ' try'
                    : ' tries'}{' '}
                  left)
                </Button>
              </Link>
            </>
          )}
          {/* <Button onClick={() => setModalOpen(false)} variant={"secondary"}>
            Close
          </Button> */}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default LeaderboardDetailsModal;
