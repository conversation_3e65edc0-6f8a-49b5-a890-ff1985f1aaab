'use client';

import { CaretSortIcon, DotsHorizontalIcon } from '@radix-ui/react-icons';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import * as React from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import { DemoInboundFormResponseDto } from '@/lib/Demo/types';
import { LeaderboardDto } from '@/lib/Leaderboard/types';
import { cn } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { CallEvents } from '@/system/Analytics/events/CallEvents';
import { CopyIcon, ListTodoIcon, Loader2Icon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export interface FilterOption {
  label: string;
  value: string;
}

interface ICallTableProps {
  leaderboard: LeaderboardDto[];
  setActiveLeaderboardDemoInboundFormResponseId: (
    demoInboundFormResponseId: number,
  ) => void;
  activeLeaderboardItems: LeaderboardDto[];
  isLoading: boolean;
  round: number;
}

export default function LeaderboardTable({
  leaderboard = [],
  isLoading,
  setActiveLeaderboardDemoInboundFormResponseId,
  activeLeaderboardItems,
  round,
}: ICallTableProps) {
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const targetRowRef = React.useRef<HTMLTableRowElement>(null);

  // Add a useEffect hook to scroll to the target row on component mount
  React.useEffect(() => {
    // Check if the target row ref exists and is attached to a DOM element
    if (targetRowRef.current) {
      // Scroll to the target row
      targetRowRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }, []);

  // @ts-ignore
  const columns: ColumnDef<LeaderboardDto>[] = React.useMemo(
    () => [
      {
        id: 'rank',
        accessorKey: 'rank',
        header: ({ column }) => {
          return (
            <Button
              variant="ghost"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === 'asc')
              }
            >
              Rank
              <CaretSortIcon className="ml-2 h-4 w-4" />
            </Button>
          );
        },
        cell: ({ row }) => {
          const rank = row.getValue('rank') as number;

          return (
            <Avatar className="w-12 h-12">
              <AvatarFallback
                className={cn('text-2xl font-bold text-primary bg-white', {
                  'text-green-600': rank === 1,
                  'text-yellow-600': rank === 2,
                  'text-red-600': rank === 3,
                  'text-4xl': rank <= 10,
                })}
              >
                {round === 2 && rank <= 10 ? '🏅' : rank}
              </AvatarFallback>
            </Avatar>
          );
        },
      },
      {
        id: 'caller',
        accessorKey: 'demoInboundFormResponse',
        header: 'Caller',
        filterFn: (row, columnId: string, filterValue: string) => {
          const name = row.original.demoInboundFormResponse?.name;
          return (
            !!filterValue &&
            name?.toLowerCase().includes(filterValue.toLowerCase())
          );
        },
        cell: ({ row }) => {
          const demoInboundFormResponse = row.getValue(
            'caller',
          ) as DemoInboundFormResponseDto;

          const callerName = demoInboundFormResponse?.name || '';

          const firstName = callerName?.substring(
            0,
            callerName?.indexOf(' ') || 0,
          ); // "72"
          const lastName = callerName.substring(
            (callerName?.indexOf(' ') || 0) + 1,
          ); // "tocirah sneab"

          return (
            <Link
              onClick={(e) => {
                e.stopPropagation();
              }}
              href={demoInboundFormResponse?.linkedinUrl || '#'}
              target="_blank"
            >
              <Button
                variant={'outline'}
                className={cn(
                  'space-x-2 pl-2 pr-4 py-0 h-12 shadow-md rounded-full bg-white hover:bg-muted/80 hover:transition-all duration-300',
                  {
                    'border-[0.5px] border-yellow-500':
                      demoInboundFormResponse?.isInfluencer,
                  },
                )}
              >
                <Avatar className="w-9 h-9">
                  {demoInboundFormResponse?.avatar && (
                    <AvatarImage src={demoInboundFormResponse?.avatar} />
                  )}
                  <AvatarFallback className="text-sm capitalize">
                    {firstName?.charAt(0)}
                    {lastName?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex space-x-2 items-center capitalize text-[0.95rem]">
                  <p>
                    {callerName}
                    {demoInboundFormResponse?.id ===
                      Number(hbDemoInboundForm?.id) && ' (You)'}
                  </p>
                  <Image
                    width={14}
                    height={14}
                    className="-mt-0.5"
                    src={'/images/linkedin-icon-white.svg'}
                    alt={demoInboundFormResponse?.linkedinUrl as string}
                  />
                </div>
              </Button>
            </Link>
          );
        },
      },
      {
        id: 'isInfluencer',
        accessorKey: 'demoInboundFormResponse',
        sortingFn: 'sortByInfluencer',
        header: ({ column }) => {
          return (
            <Button
              variant="ghost"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === 'asc')
              }
            >
              Expert
              <CaretSortIcon className="ml-2 h-4 w-4" />
            </Button>
          );
        },
        cell: ({ row }) => {
          const demoInboundFormResponse = row.getValue(
            'caller',
          ) as DemoInboundFormResponseDto;

          return demoInboundFormResponse?.isInfluencer ? (
            <Badge
              variant={'outline'}
              className="text-base rounded-full border-yellow-500 text-yellow-700 bg-white"
            >
              Expert
            </Badge>
          ) : null;
        },
      },
      {
        id: 'latestScorecard',
        accessorKey: 'latestScorecard',
        header: 'High Score',
        cell: ({ row }) => {
          const scorecard = row.getValue('latestScorecard') as any;

          return (
            <Badge
              variant="secondary"
              className={cn('text-2xl rounded-full bg-transparent', {
                'text-white':
                  row.original.demoInboundFormResponse?.id ===
                  hbDemoInboundForm?.id,
              })}
            >
              {Number((scorecard?.aggregateScore || 0) * 100).toLocaleString(
                'en-US',
                {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 1,
                },
              )}
            </Badge>
          );
        },
      },
      {
        id: 'actions',
        enableHiding: false,
        cell: ({ row }) => {
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={cn('h-8 w-8 p-0', {
                    'text-white hover:bg-white/20 hover:text-white':
                      row.original.demoInboundFormResponse?.id ===
                      hbDemoInboundForm?.id,
                  })}
                >
                  <span className="sr-only">Open menu</span>
                  <DotsHorizontalIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    const url = `${window.location.host}/leaderboard?id=${row.original.demoInboundFormResponse?.id}`;
                    navigator.clipboard.writeText(url);
                  }}
                >
                  <CopyIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  <span>Copy URL</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    setActiveLeaderboardDemoInboundFormResponseId(
                      row.original?.demoInboundFormResponse?.id as number,
                    );
                  }}
                >
                  <ListTodoIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                  <span>View scoring breakdown</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [round],
  );

  const table = useReactTable({
    data: leaderboard,
    columns,
    sortingFns: {
      sortByInfluencer: (rowA: any, rowB: any, columnId: any): number => {
        alert('hi');
        const isInfluencerA = rowA.original.isInfluencer ? 'Expert' : '';
        const isInfluencerB = rowB.original.isInfluencer ? 'Expert' : '';
        return isInfluencerA > isInfluencerB ? 1 : -1;
        // rowA.getValue(columnId).value < rowB.getValue(columnId).value ? 1 : -1,
      },
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  if (!table.getRowModel()) {
    return null;
  }

  return (
    <div className="w-full">
      {/* <Separator /> */}
      <div className="px-4 pt-2 flex justify-between">
        <Input
          placeholder="Search by caller name"
          value={(table.getColumn('caller')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('caller')?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        {!!hbDemoInboundForm?.id &&
          (round === 1 || (round === 2 && hbDemoInboundForm?.isFinalist)) && (
            <p className="text-2xl rounded-full bg-transparent font-semibold">
              <span className="text-xl">My High Score:</span>
              {'  '}
              {Number(
                leaderboard.find(
                  (l) =>
                    l.demoInboundFormResponse?.id === hbDemoInboundForm?.id,
                )?.latestScorecard?.aggregateScore * 100,
              ).toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 1,
              })}{' '}
              <span className="text-sm">/ 100</span>
            </p>
          )}
      </div>
      <div className="flex items-center justify-between px-6 mt-4">
        <div className="flex-1 mr-6 text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} of {leaderboard?.length}{' '}
          participant{leaderboard?.length === 1 ? '' : 's'}
        </div>
      </div>
      <div>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  ref={
                    row.original.demoInboundFormResponse?.id ===
                    hbDemoInboundForm?.id
                      ? targetRowRef
                      : null
                  }
                  className={cn('cursor-pointer h-10', {
                    'bg-gradient-to-r from-[#000000] via-[#5189CE] to-[#A168A2] hover:opacity-90 hover:transition-all hover:duration-100':
                      row.original.demoInboundFormResponse?.id ===
                      hbDemoInboundForm?.id,
                    'border-l-8 border-l-[#2FB6E1]':
                      row.original.demoInboundFormResponse?.isFinalist,
                    'border-l-yellow-500':
                      round === 2 && (row.original.rank || 0) <= 10,
                  })}
                  tabIndex={0}
                  role="button"
                  onClick={() => {
                    setActiveLeaderboardDemoInboundFormResponseId(
                      row.original?.demoInboundFormResponse?.id as number,
                    );
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="[&:nth-child(1)]:pl-6">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="w-full">
                <TableCell
                  colSpan={columns.length}
                  className="h-24 w-full flex items-center justify-center text-center"
                >
                  {isLoading ? (
                    <Loader2Icon className="animate-spin" />
                  ) : (
                    'No participants yet'
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
