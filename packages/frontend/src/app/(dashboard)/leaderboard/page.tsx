'use client';

import DashboardNavbar from '@/common/DashboardNavbar';
import { CHALLENGE_BOT_VAPI_ID } from '@/common/Sidebar/OldSidebar';
import DemoWelcomeModal from '@/components/DemoWelcomeModal';
import { Button } from '@/components/ui/button';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import { ScrollArea } from '@/components/ui/scroll-area';
import useDemoAgents from '@/hooks/useDemoAgents';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import useLeaderboard from '@/hooks/useLeaderboard';
import useLeaderboardByDemoInboundFormResponseId from '@/hooks/useLeaderboardByDemoInboundFormResponseId';
import { cn } from '@/lib/utils';
import Analytics from '@/system/Analytics';
import { BuyerEvents } from '@/system/Analytics/events/BuyerEvents';
import { CallEvents } from '@/system/Analytics/events/CallEvents';
import { useAuthInfo } from '@propelauth/react';
import { useQueryClient } from '@tanstack/react-query';
import JSZip from 'jszip';
import {
  ChevronUpIcon,
  DownloadIcon,
  LayoutListIcon,
  PhoneIcon,
  TrophyIcon,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useRef, useState } from 'react';
import LeaderboardDetailsModal from './LeaderboardDetailsModal';
import LeaderboardTable from './LeaderboardTable';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert } from '@/components/ui/alert';
import LinksManager from '@/lib/linksManager';

function Leaderboard() {
  const authInfo = useAuthInfo();
  const queryClient = useQueryClient();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { data: demoAgents, isLoading: isLoadingDemoAgents } = useDemoAgents(
    !authInfo?.isLoggedIn,
  );
  const curSearchParams = new URLSearchParams(searchParams);
  const [open, setOpen] = useState(false);
  const [openRegistration, setOpenRegistration] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [round, setRound] = useState(
    curSearchParams.get('round') === '1' ? 1 : 2,
  );

  const isInitialMount = useRef<number>(0);

  useEffect(() => {
    setRound(Number(searchParams.get('round') || 2));
  }, [searchParams.get('round')]);

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(document.documentElement.scrollTop);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    if (scrollPosition > 200) {
      setShowScrollButton(true);
    } else {
      setShowScrollButton(false);
    }
  }, [scrollPosition]);

  useEffect(() => {
    if (isInitialMount.current >= 2) {
      // Save current scroll position when modal opens
      if (open) {
        setScrollPosition(document.documentElement.scrollTop);
        // Disable scrolling on the body
        document.body.style.overflow = 'hidden';
      } else {
        // Restore scroll position when modal closes
        setTimeout(() => {
          document.documentElement.scrollTop = scrollPosition;
        }, 100);
        // Enable scrolling on the body
        document.body.style.overflow = 'auto';
      }
    } else {
      isInitialMount.current += 1;
    }
  }, [open]);

  const { data: leaderboard, isLoading: isLoadingLeaderboard } = useLeaderboard(
    round === 1 ? 497 : 565,
  );
  const {
    data: activeLeaderboardItems,
    isLoading: isLoadingActiveLeaderboard,
    isFetched,
    isSuccess,
  } = useLeaderboardByDemoInboundFormResponseId(
    Number(curSearchParams.get('id') || 0),
    round === 1 ? 497 : 565,
  );

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const allBuyersExceptCurrent = useMemo(() => {
    if (!demoAgents?.length) return [];

    return demoAgents
      .filter((a) => a.vapiId !== CHALLENGE_BOT_VAPI_ID)
      .map((a) => a.id);
  }, [demoAgents?.length]);

  const handleDownloadAll = () => {
    const images = hbDemoInboundForm?.challengeSocialCards || [];
    const zip = new JSZip();

    // Promises to load and add each image to the zip file
    const promises = images.map((src, index) => {
      return fetch(src)
        .then((response) => response.blob())
        .then((blob) => {
          zip.file(`image_${index + 1}.jpg`, blob); // Assuming images are JPEG format
        });
    });

    // Once all promises are resolved, generate the zip file and initiate the download
    Promise.all(promises).then(() => {
      zip.generateAsync({ type: 'blob' }).then((content) => {
        // Create a temporary link element to initiate the download
        const link = document.createElement('a');
        link.href = URL.createObjectURL(content);
        link.download = 'images.zip'; // Set the filename for the zip file
        document.body.appendChild(link);
        link.click();
        // Remove the link element after the download
        document.body.removeChild(link);
      });
    });
  };

  const handleDownloadImage = (src: string, index: number) => {
    fetch(src)
      .then((response) => response.blob())
      .then((blob) => {
        // Create a temporary link element
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `image_${index + 1}.jpg`; // Set the filename for the image
        document.body.appendChild(link);
        link.click();
        // Remove the link element after the download
        document.body.removeChild(link);
      })
      .catch((error) => {
        console.error('Error downloading image:', error);
      });
  };

  useEffect(() => {
    if (
      !curSearchParams.has('round') ||
      curSearchParams.get('round') === 'undefined'
    ) {
      curSearchParams.set('round', '2');
      router.replace(`${pathname}?${curSearchParams.toString()}`);
    }
  }, []);

  useEffect(() => {
    curSearchParams.set('round', String(round));
    router.push(`${pathname}?${curSearchParams.toString()}`);
  }, [round]);

  useEffect(() => {
    if (isFetched && isSuccess) {
      setTimeout(() => {
        setOpen(true);
      }, 500);
    }
  }, [isFetched, isSuccess]);

  const myLeaderboardItem = leaderboard?.find(
    (l) => l.demoInboundFormResponse?.id === hbDemoInboundForm?.id,
  );

  return (
    <div>
      <DashboardNavbar
        breadcrumbs={[
          { title: 'Beat the Bot Challenge (Mar 18 - 23)' },
          { title: 'Leaderboard' },
        ]}
        titleRight={<TrophyIcon className="w-5 h-5 text-muted-foreground" />}
        subContent={
          <p className="text-muted-foreground mt-2 max-w-[580px]">
            Click on a row to view scoring breakdown. Learn more at{' '}
            <a
              href="https://hyperbound.ai"
              className="underline"
              target="_blank"
              rel="noreferrer"
            >
              hyperbound.ai
            </a>
            <div className=""></div>
          </p>
        }
        rightContent={
          <div className="flex space-x-4">
            {hbDemoInboundForm?.presClubChallengeRegistered ? (
              <>
                {!authInfo?.isLoggedIn && (
                  <Link
                    href={LinksManager.trainingCalls()}
                    className="h-min"
                    onClick={() => {
                      Analytics.track(BuyerEvents.VIEW_CALLS_CLICKED, {
                        agentId: round === 1 ? 497 : 565,
                        from: 'beat_the_bot_leaderboard',
                      });
                    }}
                  >
                    <Button variant="outline">
                      <LayoutListIcon className="w-4 h-4 mr-2" />
                      View my calls/transcripts
                    </Button>
                  </Link>
                )}
                <Link
                  href={`/buyers/${CHALLENGE_BOT_VAPI_ID}`}
                  onClick={() => {
                    Analytics.track(CallEvents.START_NEW_CALL_CLICKED, {
                      agentId: CHALLENGE_BOT_VAPI_ID,
                      from: 'beat_the_bot_leaderboard',
                    });
                  }}
                >
                  <Button variant={'default'}>
                    <PhoneIcon className="w-4 h-4 mr-2" /> Start new call (
                    {hbDemoInboundForm?.challengeNumTriesLeft}
                    {hbDemoInboundForm?.challengeNumTriesLeft === 1
                      ? ' try'
                      : ' tries'}{' '}
                    left)
                  </Button>
                </Link>
              </>
            ) : (
              <Button
                variant={'default'}
                size={'lg'}
                className="text-lg"
                onClick={() => {
                  router.push(`/buyers/${CHALLENGE_BOT_VAPI_ID}`);
                }}
              >
                Continue the competition
              </Button>
            )}
          </div>
        }
      />
      <div className="min-h-screen pt-2">
        <div className="px-4 mb-4">
          <Alert
            className="mb-4 text-white text-base"
            style={{
              backgroundImage:
                'linear-gradient(to right, #2FB6E1 0%, #32C490 100%)',
            }}
          >
            {!hbDemoInboundForm?.isFinalist
              ? 'Congratulations on completing Round 1! Round 2 is also completed now. Stay tuned for the final results!'
              : round === 2 && (myLeaderboardItem?.rank || 0) <= 10
                ? 'Congratulations, you have qualified in the top 10 for the Hyperbound "Beat the Bot" Challenge! Watch out for an email for next steps and your prize 😉'
                : `Congratulations, you have been chosen as a finalist from Round 1, and will be advancing to Round 2! Hit "Start new call" on the top right to begin!`}
          </Alert>
          <Tabs
            defaultValue={round === 1 ? 'Round 1' : 'Round 2'}
            value={round === 1 ? 'Round 1' : 'Round 2'}
            className="max-w-[400px]"
          >
            <TabsList
              className="grid w-full grid-cols-2"
              style={{
                background:
                  'linear-gradient(to right, #000000, #5189CE, #A168A2)',
                backgroundImage:
                  '-webkit-linear-gradient(to right, #000000, #5189CE, #A168A2)',
              }}
            >
              <TabsTrigger
                value="Round 1"
                className="text-white/90"
                onClick={() => setRound(1)}
              >
                Round 1
              </TabsTrigger>
              <TabsTrigger
                value="Round 2"
                className="text-white/90"
                onClick={() => setRound(2)}
              >
                Round 2
              </TabsTrigger>
              {/* <TabsTrigger value="Buyer details">Buyer details</TabsTrigger> */}
            </TabsList>
          </Tabs>
        </div>
        <div
          className={cn('flex md:hidden', {
            'md:flex': !hbDemoInboundForm?.id,
          })}
        >
          <LeaderboardTable
            round={round}
            isLoading={false}
            leaderboard={leaderboard || []}
            activeLeaderboardItems={activeLeaderboardItems!}
            setActiveLeaderboardDemoInboundFormResponseId={(id: number) => {
              curSearchParams.set('id', String(id));
              router.replace(`${pathname}?${curSearchParams.toString()}`);
              queryClient.refetchQueries({
                queryKey: ['leaderboardItems', id],
              });
              setOpen(true);
            }}
          />
        </div>
        <div
          className={cn('w-full hidden md:flex', {
            'md:hidden': !hbDemoInboundForm?.id,
          })}
        >
          <ResizablePanelGroup direction="horizontal" className="">
            <ResizablePanel defaultSize={80}>
              <LeaderboardTable
                round={round}
                isLoading={false}
                leaderboard={leaderboard || []}
                activeLeaderboardItems={activeLeaderboardItems!}
                setActiveLeaderboardDemoInboundFormResponseId={(id: number) => {
                  curSearchParams.set('id', String(id));
                  router.replace(`${pathname}?${curSearchParams.toString()}`);
                  queryClient.refetchQueries({
                    queryKey: ['leaderboardItems', id],
                  });
                  setOpen(true);
                }}
              />
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={20} className="overflow-y-scroll">
              <div className="px-4 pt-3">
                <p className="text-xl font-semibold">
                  Social &quot;Brag&quot; Cards
                </p>
                <p className="text-sm">
                  Download and share on LinkedIn, IG, TikTok, etc., show off
                  your skills, and challenge your friends! Not seeing your
                  cards? Refresh the page.
                </p>
                {!!hbDemoInboundForm?.challengeSocialCards?.length && (
                  <Button
                    className="my-2"
                    variant={'default'}
                    onClick={() => handleDownloadAll()}
                  >
                    <DownloadIcon className="w-4 h-4 mr-2" />
                    Download all
                  </Button>
                )}
                <ScrollArea className="max-h-screen overflow-y-auto rounded-lg">
                  {(hbDemoInboundForm?.challengeSocialCards || []).map(
                    (src, i) => (
                      <div className="relative" key={src}>
                        <Image
                          src={src}
                          alt="social card"
                          className="w-full mt-2 rounded-lg"
                          width={200}
                          height={200}
                        />
                        <div className="absolute inset-0 bg-black opacity-0 hover:opacity-50 transition-opacity duration-300 rounded-lg">
                          <button
                            className="absolute inset-0 flex items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity duration-300"
                            onClick={() => handleDownloadImage(src, i)}
                          >
                            <DownloadIcon className="w-7 h-7" />
                          </button>
                        </div>
                      </div>
                    ),
                  )}
                </ScrollArea>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>

        <LeaderboardDetailsModal
          round={round}
          modalOpen={open}
          setModalOpen={(open) => {
            if (!open) {
              curSearchParams.delete('id');
              router.replace(
                `${pathname}${
                  curSearchParams.toString()
                    ? `?${curSearchParams.toString()}`
                    : ''
                }`,
              );
            }
            if (
              !open &&
              !!hbDemoInboundForm?.id &&
              !hbDemoInboundForm?.challengeSocialCards?.length
            ) {
              queryClient.invalidateQueries({
                queryKey: [process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY],
              });
            }
            setOpen(open);
          }}
          leaderboardItems={activeLeaderboardItems!}
          activeLeaderboardItem={leaderboard?.find(
            (l) =>
              l.demoInboundFormResponse?.id ===
              Number(curSearchParams.get('id')),
          )}
          isLoading={isLoadingLeaderboard || isLoadingActiveLeaderboard}
        />
        <DemoWelcomeModal
          modalOpen={openRegistration}
          setModalOpen={setOpenRegistration}
          onSubmit={() => {
            setOpenRegistration(false);
            router.push(`/buyers/${CHALLENGE_BOT_VAPI_ID}`);
          }}
          isClosable
          submitText="Register for challenge"
          registerForChallenge
        />

        {showScrollButton && (
          <button
            onClick={handleScrollToTop}
            className="fixed bottom-4 right-4 bg-gradient-to-br from-[#000000] to-[#A168A2] text-white p-4 rounded-full shadow-md hover:opacity-90 focus:outline-none"
          >
            <ChevronUpIcon />
          </button>
        )}
      </div>
    </div>
  );
}

export default Leaderboard;
