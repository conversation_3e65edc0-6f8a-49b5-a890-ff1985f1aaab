'use client';

import CallBlitz from '@/common/CallBlitz';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';

function CallBlitzPage() {
  const { isLoggedIn, loading, isTemp } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else {
    return <CallBlitz />;
  }
}

export default CallBlitzPage;
