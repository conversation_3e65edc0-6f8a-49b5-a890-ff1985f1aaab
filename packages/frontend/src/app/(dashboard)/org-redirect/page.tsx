'use client';

import { refreshHeaders } from '@/app/auth-wrapper';
import { Skeleton } from '@/components/ui/skeleton';
import {
  OrgMemberInfo,
  useAuthInfo,
  useRedirectFunctions,
} from '@propelauth/react';
import { useSearchParams } from 'next/navigation';
import { use, useEffect } from 'react';

export default function OrgRedirectPage() {
  const { redirectToLoginPage } = useRedirectFunctions();
  const searchParams = useSearchParams();
  const authInfo = useAuthInfo();

  const handleRedirect = async () => {
    const orgId = searchParams.get('redirectOrgUid');
    if (!orgId) {
      console.error('redirectOrgUid not set');
      return;
    }
    if (searchParams.get('c') === 'true') {
      let org: OrgMemberInfo | undefined;
      while (!org) {
        org = authInfo.orgHelper?.getOrg?.(orgId);
        if (!org) {
          console.error('org not found');
        }
        await new Promise((r) => setTimeout(r, 500));
      }
      let userId: string | undefined;
      while (!userId) {
        userId = authInfo.user?.userId;
        if (!userId) {
          console.error('userId not found');
        }
        await new Promise((r) => setTimeout(r, 500));
      }
      // invitation screen complete, go to org id now
      await refreshHeaders(authInfo, {
        id: org.orgId,
        name: org.orgName,
        userId: userId,
      });
      window.location.assign('/');
    } else {
      const newSearchParams = new URLSearchParams();
      newSearchParams.set('redirectOrgUid', orgId);
      newSearchParams.set('c', 'true');
      redirectToLoginPage({
        postLoginRedirectUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/org-redirect?${newSearchParams.toString()}`,
      });
    }
  };

  useEffect(() => {
    handleRedirect();
  }, [searchParams]);
  return (
    <div className="flex flex-col space-x-0 space-y-4 m-4">
      <Skeleton className="w-2/3 h-6" />
      <Skeleton className="w-1/2 h-6" />
      <Skeleton className="w-2/3 h-6" />
    </div>
  );
}
