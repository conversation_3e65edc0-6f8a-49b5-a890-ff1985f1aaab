'use client';

import DashboardNavbar from '@/common/DashboardNavbar';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import usePartnerByPartnerId from '@/hooks/usePartnerByPartnerId';
import PartnerService from '@/lib/Partner';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { Loader2Icon, SendIcon } from 'lucide-react';
import { redirect } from 'next/navigation';
import { useRef, use } from 'react';
import { useForm } from 'react-hook-form';
import { Id, toast } from 'react-toastify';
import { z } from 'zod';

export interface FormData {
  email: string;
}

const formSchema = z.object({
  email: z.string().email('Invalid email').min(1, 'This field is required'),
});

interface IPartnerSignupPageProps {
  params: Promise<{
    partnerId: string;
  }>;
}

function PartnerSignupPage(props: IPartnerSignupPageProps) {
  const params = use(props.params);

  const {
    partnerId
  } = params;

  const { data: partner, isLoading } = usePartnerByPartnerId();
  const toastId = useRef<Id | null>(null);

  // 1. Define your form.
  const form = useForm<FormData>({
    mode: 'onTouched',
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  const createPartnerSignupMutation = useMutation({
    mutationFn: PartnerService.createPartnerSignup,
    onSuccess: (invited, params) => {
      toastId.current = toast.success(
        `An invite has been sent to your email to join ${partner?.name}'s org on Hyperbound. Check your email for the invite link and sign in to the org. Don't forget to check your spam inbox if it don't see it.`,
      );
    },
    onError: (err) => {
      if (!toast.isActive(toastId.current as Id)) {
        toastId.current = toast.error(
          'There was an error inviting you to the partner organization',
        );
      }
    },
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!partner) {
    redirect('/404');
  }

  const onSubmit = (values: FormData) => {
    createPartnerSignupMutation.mutate({
      partnerId,
      email: values.email,
    });
  };

  return (
    <div>
      <div className="flex items-center justify-center mt-24 w-full">
        <Card className="min-w-[450px] max-h-[450px] py-16 px-2 rounded-3xl">
          <CardContent>
            <div className="flex flex-col items-center text-center mb-8">
              <div className="flex items-center space-x-4">
                <Avatar className="w-16 h-16">
                  <AvatarImage src={'/images/square-black-logo.svg'} />
                  <AvatarFallback className="text-lg text-muted-foreground">
                    HB
                  </AvatarFallback>
                </Avatar>
                <p className="text-4xl">🤝</p>
                <Avatar className="w-16 h-16 border border-muted">
                  {partner?.logo && <AvatarImage src={partner?.logo} />}
                  <AvatarFallback className="text-lg text-muted-foreground">
                    {partner?.name?.charAt(0) || ''}
                  </AvatarFallback>
                </Avatar>
              </div>

              <h3 className="text-2xl mt-5">Hyperbound // {partner?.name}</h3>
              <p className="mt-2">
                Sign up to receive an invite to join the {partner?.name} org on
                Hyperbound
              </p>
            </div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} noValidate>
                <div className="flex flex-col justify-center">
                  <div className="flex space-x-4 w-full">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>Email *</FormLabel>
                          <FormControl>
                            <Input
                              className="h-10"
                              required
                              type="email"
                              placeholder=""
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <Button
                    className="mt-4"
                    disabled={
                      !form.formState.isValid ||
                      createPartnerSignupMutation.isPending
                    }
                    variant="default"
                    size="lg"
                  >
                    {createPartnerSignupMutation.isPending ? (
                      <Loader2Icon className="animate-spin" />
                    ) : (
                      <>
                        Send me an invite
                        <SendIcon className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default PartnerSignupPage;
