'use client';

import Members from '@/common/Members';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { useRouter } from 'next/navigation';

export default function MembersPage() {
  const { isLoggedIn, loading, isTemp, isCompetitionOrg } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  const router = useRouter();

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else if (!isCompetitionOrg) {
    return <Members />;
  } else {
    router.push(RedirectTo.PAGE_NOT_FOUND);
  }
}
