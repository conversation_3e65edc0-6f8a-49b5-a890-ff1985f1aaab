'use client';
import { use } from 'react';

import UserProfile from '@/common/Members/UserProfile';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';

export default function MemberPage(props: { params: Promise<{ id: string }> }) {
  const params = use(props.params);

  const { id } = params;

  const { isLoggedIn, loading, isTemp } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else {
    return <UserProfile userId={Number(id)} />;
  }
}
