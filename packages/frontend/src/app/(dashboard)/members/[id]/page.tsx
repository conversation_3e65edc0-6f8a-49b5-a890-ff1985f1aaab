'use client';

import UserProfile from '@/common/Members/UserProfile';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';

export default function MemberPage({
  params: { id },
}: {
  params: { id: string };
}) {
  const { isLoggedIn, loading, isTemp } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else {
    return <UserProfile userId={Number(id)} />;
  }
}
