'use client';

import { CreatePlaylistModal } from '@/common/CreatePlaylist/CreatePlaylistModal';
import DashboardNavbar from '@/common/DashboardNavbar';
import PlanAssessment from '@/common/PlanAssessment';
import PlaylistList from '@/common/PlaylistList';
import { Button } from '@/components/ui/button';
import useOrg from '@/hooks/useOrg';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import dayjs from 'dayjs';
import { PlusIcon } from 'lucide-react';
import { useState } from 'react';

function PlanAssessmentPage() {
  return <PlanAssessment />;
}

export default PlanAssessmentPage;
