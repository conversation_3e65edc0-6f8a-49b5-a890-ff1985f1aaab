'use client';

import CallSummary from '@/common/Calls/AIRoleplay/Summary';

import { useParams, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

function CallSummaryPage() {
  const params = useParams();
  const searchParams = useSearchParams();

  const [openTab, setOpenTab] = useState<string | undefined>(
    searchParams.get('tab') || undefined,
  );

  useEffect(() => {
    setOpenTab(searchParams.get('tab') || undefined);
  }, [searchParams.get('tab')]);

  const [onlyScorecard] = useState(
    searchParams.get('onlyScorecard') === 'true',
  );

  return (
    <CallSummary
      vapiId={params?.id as string}
      openTab={openTab}
      onlyScorecard={onlyScorecard}
    />
  );
}

export default CallSummaryPage;
