'use client';

import NewCallSummary from '@/common/Calls/AIRoleplay/NewSummary';
import CallSummary from '@/common/Calls/AIRoleplay/Summary';
import { useIsInIframe } from '@/hooks/useIsInIframe';
import useMobile from '@/hooks/useMobile';
import useUserSession from '@/hooks/useUserSession';

import { useParams, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

function CallSummaryPage() {
  const isMobile = useMobile();
  const isInIframe = useIsInIframe();
  const { useNewPostCall } = useUserSession();
  const params = useParams();
  const searchParams = useSearchParams();

  const [openTab, setOpenTab] = useState<string | undefined>(
    searchParams.get('tab') || undefined,
  );

  useEffect(() => {
    if (openTab !== searchParams.get('tab')) {
      setOpenTab(searchParams.get('tab') || undefined);
    }
  }, [searchParams]);

  const [onlyScorecard] = useState(
    searchParams.get('onlyScorecard') === 'true',
  );

  return (
    <>
      {useNewPostCall && !isInIframe && !isMobile ? (
        <NewCallSummary
          callId={params?.id as string}
          openTab={openTab}
          onlyScorecard={onlyScorecard}
        />
      ) : (
        <CallSummary
          vapiId={params?.id as string}
          openTab={openTab}
          onlyScorecard={onlyScorecard}
        />
      )}
    </>
  );
}

export default CallSummaryPage;
