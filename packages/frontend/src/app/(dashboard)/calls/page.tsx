'use client';

import DemoWelcomeModal from '@/components/DemoWelcomeModal';
import useHbDemoInboundForm from '@/hooks/useHbDemoInboundForm';
import Analytics from '@/system/Analytics';
import { DemoInboundFormEvents } from '@/system/Analytics/events/DemoInboundFormEvents';
import { useAuthInfo } from '@propelauth/react';
import { useQueryClient } from '@tanstack/react-query';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import CallsTable from '../../../common/Calls';
import { useParams, useSearchParams } from 'next/navigation';
import useUserSession from '@/hooks/useUserSession';

export default function CallsPage() {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const authInfo = useAuthInfo();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const queryClient = useQueryClient();
  const [initialTimeoutPassed, setInitialTimeoutPassed] = useState(false);
  useEffect(() => {
    setTimeout(() => {
      setInitialTimeoutPassed(true);
    }, 1500);
  }, []);

  useEffect(() => {
    if (
      initialTimeoutPassed &&
      !authInfo.isLoggedIn &&
      _.isEmpty(hbDemoInboundForm) &&
      !modalOpen
    ) {
      Analytics.track(DemoInboundFormEvents.OPENED, {
        invite: queryClient.getQueryData(['hbInvite']) || null,
        from: 'calls_page',
      });
      setModalOpen(true);
    }
  }, [hbDemoInboundForm, initialTimeoutPassed, authInfo.isLoggedIn]);

  const params = useParams();
  const searchParams = useSearchParams();

  const [openTab, setOpenTab] = useState<string | undefined>(
    searchParams.get('openTab') || undefined,
  );

  useEffect(() => {
    setOpenTab(searchParams.get('openTab') || undefined);
  }, [searchParams.get('openTab')]);

  const { isInIframe } = useUserSession();

  return (
    <>
      {isInIframe && <div className="h-4" />}
      <CallsTable openTab={openTab} />
      {isInIframe && <div className="h-12" />}
      {!authInfo.isLoggedIn && (
        <DemoWelcomeModal
          modalOpen={modalOpen}
          setModalOpen={setModalOpen}
          onSubmit={() => {
            setModalOpen(false);
          }}
          isClosable
          submitText="Let's go, see calls"
        />
      )}
    </>
  );
}
