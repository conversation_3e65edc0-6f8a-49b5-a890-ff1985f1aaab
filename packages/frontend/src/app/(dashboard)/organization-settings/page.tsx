'use client';

import OrganizationSettings from '@/common/OrganizationSettings';
import useUserSession, { RedirectTo } from '@/hooks/useUserSession';
import { useRouter } from 'next/navigation';

export default function MembersPage() {
  const { isLoggedIn, loading, isTemp, isAdmin } = useUserSession(
    true,
    true,
    false,
    RedirectTo.PAGE_NOT_FOUND,
  );

  const router = useRouter();

  if (loading || !isLoggedIn || isTemp) {
    return <div />;
  } else if (isAdmin) {
    return <OrganizationSettings />;
  } else {
    router.push(RedirectTo.PAGE_NOT_FOUND);
  }
}
