'use client';

import { HeroHighlight } from '@/components/ui/Hyperbound/hero-highlight';
import Image from 'next/image';

export default function RestrictedPage() {
  return (
    <div className="w-[100vw] h-[100vh]">
      <div className="flex justify-center items-center relative z-50">
        <div className="flex justify-center w-full fixed left-0 top-0 z-50 backdrop-blur-3xl backdrop-brightness-150">
          <div
            className="flex justify-center md:justify-between items-center z-50 w-full py-3 px-8"
            style={
              {
                // background:
                //   "linear-gradient(to bottom, rgba(255, 255, 255, 0.9) 60%, rgba(255, 255, 255, 0))",
              }
            }
          >
            <div className="w-[346px]">
              <Image
                src={`https://www.hyperbound.ai/images/black-logo-with-text.svg`}
                alt="Hyperbound logo"
                width={153}
                height={36}
                priority
              />
            </div>
          </div>
        </div>
      </div>
      <HeroHighlight
        className="px-4 md:px-0 "
        containerClassName={' h-[100vh]'}
      >
        <div className="flex items-center">
          <div className="flex-1 overflow-hidden p-10 text-center">
            <img
              src={`/error.png`}
              alt="Hyperbound logo"
              className="h-[60vh] rounded-lg"
            />
          </div>
          <div className="flex-1 ml-20 text-center">
            <div className="text-6xl font-bold text-center mb-8">
              Access Restricted
            </div>
            <div className="text-2xl font-bold">
              Contact your admin for more details
            </div>
          </div>
        </div>
      </HeroHighlight>
    </div>
  );
}
