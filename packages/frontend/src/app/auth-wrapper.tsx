/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import useUserSession from '@/hooks/useUserSession';
import { API, API_BLOB } from '@/lib/Client';
import { useAuthInfo } from '@propelauth/react';
import { UseAuthInfoProps } from '@propelauth/react/dist/types/hooks/useAuthInfo';
import * as Sentry from '@sentry/nextjs';
import { jwtDecode } from 'jwt-decode';
import { usePathname, useSearchParams } from 'next/navigation';
import Script from 'next/script';
import { useEffect, useMemo, useRef, useState } from 'react';
import AuthHandler from './auth-handler';
import { useIsFocused } from '@/hooks/useIsFocused';
import { useOrgSwitch } from '@/hooks/useOrgSwitch';
import { SwitchOrgEvent } from '@/lib/Organization/types';

interface Props {
  children: React.ReactNode;
}

export interface OrgData {
  id: string;
  name: string;
  userId: string;
  token?: string;
}

const getOrgDataAndExpFromLocal = () => {
  const orgData = JSON.parse(
    localStorage.getItem(
      process.env.NEXT_PUBLIC_ACTIVE_ORG_DATA_LOCAL_STORAGE_KEY as string,
    ) || '{}',
  ) as OrgData | undefined;
  if (!orgData?.token) {
    return { orgData, exp: 0 };
  }
  let exp = 0;
  try {
    const decoded = jwtDecode(orgData.token);
    exp = decoded.exp || 0;
  } catch (e: any) {
    console.log('error decoding org data', e);
    orgData.token = undefined;
  }
  return { orgData, exp };
};

export const getOrgDataFromLocal = () => {
  return getOrgDataAndExpFromLocal().orgData;
};

const setOrgDataToLocal = (data: OrgData) => {
  return localStorage.setItem(
    process.env.NEXT_PUBLIC_ACTIVE_ORG_DATA_LOCAL_STORAGE_KEY as string,
    JSON.stringify(data),
  );
};

const getUserAccessToken = async (
  authInfo: Partial<UseAuthInfoProps>,
  orgId: string,
) => {
  if (!authInfo.isLoggedIn) {
    return '';
  }

  let accessToken = authInfo.accessToken;
  if (orgId) {
    const accessTokenForOrgRes =
      await authInfo?.tokens?.getAccessTokenForOrg(orgId);
    // defaults to authInfo level access token if error
    if (!accessTokenForOrgRes || accessTokenForOrgRes.error) {
      Sentry.captureException(
        new Error(`Error: ${accessTokenForOrgRes?.error || 'Fn undefined'}`),
      );
    } else if (accessTokenForOrgRes.accessToken) {
      accessToken = accessTokenForOrgRes.accessToken;
    }
  }

  return accessToken || '';
};

export const blacklistedOrgIds = [
  '5724ac48-772a-41e7-b2a4-7be6e068419a',
  'a457a4e8-fa3d-486c-86b2-7b2dcc71aee0',
];

const getOrgData = (
  authInfo: Partial<UseAuthInfoProps>,
  orgId?: string,
): OrgData | undefined => {
  const user = authInfo.user;
  const orgs = authInfo.orgHelper?.getOrgs?.() || [];
  const org = orgId
    ? orgs.find((o) => o.orgId === orgId)
    : orgs.filter((o) => !blacklistedOrgIds.includes(o.orgId))[0];
  if (org && user) {
    return {
      id: org.orgId,
      name: org.orgName,
      userId: user.userId,
    };
  }
};

const syncOrgIdAndName = (
  authInfo: Partial<UseAuthInfoProps>,
  overrideOrg?: OrgData,
) => {
  if (!authInfo.isLoggedIn) {
    return { id: '', name: '' };
  }

  let finalOrgId: string | null = null,
    finalOrgName: string | null = null,
    finalUserId: string | null = null;

  const orgDataFromLocalStorage = getOrgDataFromLocal() as OrgData | undefined;
  // if there is an override org and if the org from localstorage is different from the one in override
  if (
    !!overrideOrg &&
    (orgDataFromLocalStorage?.id !== overrideOrg.id ||
      authInfo.user?.userId !== orgDataFromLocalStorage?.userId)
  ) {
    finalOrgName = overrideOrg.name;
    finalOrgId = overrideOrg.id;
    finalUserId = authInfo.user?.userId || null;
  }
  // if local storage does not contain the org id or contains an invalid org id
  else if (
    !orgDataFromLocalStorage?.id ||
    !authInfo?.orgHelper?.getOrgIds().includes(orgDataFromLocalStorage?.id)
  ) {
    const newOrgData = getOrgData(authInfo);
    if (newOrgData) {
      ({
        id: finalOrgId,
        name: finalOrgName,
        userId: finalUserId,
      } = newOrgData);
    }
  }

  if (finalOrgId !== null && finalOrgName !== null && finalUserId !== null) {
    API.defaults.params = {
      orgId: finalOrgId,
    };
    API_BLOB.defaults.params = {
      orgId: finalOrgId,
    };
    const res: OrgData = {
      id: finalOrgId,
      name: finalOrgName,
      userId: finalUserId,
    };
    setOrgDataToLocal(res);
    return res;
  }

  // orgDataFromLocalStorage at this point is assured to be defined
  // check above lines for the "why"
  API.defaults.params = {
    orgId: (orgDataFromLocalStorage as OrgData).id,
  };
  API_BLOB.defaults.params = {
    orgId: (orgDataFromLocalStorage as OrgData).id,
  };

  return orgDataFromLocalStorage as OrgData;
};

const setAuthHeaders = (token: string) => {
  API.defaults.headers.common['Authorization'] = token ? `Bearer ${token}` : ``;

  API_BLOB.defaults.headers.common['Authorization'] = token
    ? `Bearer ${token}`
    : ``;
};

export const useSSOUrl = () => {
  const { dbOrg } = useUserSession();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const ssoUrl = useMemo(() => {
    if (!dbOrg) {
      return null;
    }

    // parent org if exists because they usually have SSO setup
    // and not the sub orgs
    const ssoOrgUid = dbOrg.parentOrganization?.uid || dbOrg.uid;

    const currentOrgUid = dbOrg.uid;
    const redirectSearchParams = new URLSearchParams(searchParams);
    redirectSearchParams.set('switchOrgUid', currentOrgUid);

    const redirectUrl = new URL(
      'https://app.hyperbound.ai' +
        pathname +
        '?' +
        redirectSearchParams.toString(),
    );
    const ssoUrlObj = new URL(
      'https://auth.hyperbound.ai/api/fe/v3/login/saml',
    );
    ssoUrlObj.searchParams.set('rt', btoa(redirectUrl.toString()));
    ssoUrlObj.searchParams.set('org_id', ssoOrgUid);
    return ssoUrlObj.toString();
  }, [searchParams, pathname, dbOrg]);
  return ssoUrl;
};

export const refreshHeaders = async (
  authInfo: Partial<UseAuthInfoProps>,
  overrideOrg?: OrgData,
) => {
  if (!authInfo.isLoggedIn) {
    return;
  }

  syncOrgIdAndName(authInfo, overrideOrg);
  // get the newly refreshed org from local
  const { orgData: localOrgData, exp: localOrgJWTExp } =
    getOrgDataAndExpFromLocal();
  const unixAMinuteAfter = new Date().getTime() / 1000 + 60;

  /**
   * Use the same token IFF:
   * - token is truthy
   * - token's expiry is at least 60 seconds in the future
   *
   * Else, reset the access token by contacting propel auth
   */
  if (!localOrgData) {
    Sentry.captureException(
      new Error(
        `Logged in user has no local org data: ${authInfo?.user?.userId}`,
      ),
    );
    return;
  }
  if (!!localOrgData.token && localOrgJWTExp > unixAMinuteAfter) {
    setAuthHeaders(localOrgData?.token || '');
  } else {
    setAuthHeaders(authInfo.accessToken || '');
    const userAccessToken = await getUserAccessToken(authInfo, localOrgData.id);
    setAuthHeaders(userAccessToken);
    localOrgData.token = userAccessToken;
    setOrgDataToLocal(localOrgData);
  }

  API.defaults.headers.common['Time-Zone'] =
    Intl.DateTimeFormat().resolvedOptions().timeZone;
  API_BLOB.defaults.headers.common['Time-Zone'] =
    Intl.DateTimeFormat().resolvedOptions().timeZone;
};

const refreshHeadersLoop = async (
  authInfo: UseAuthInfoProps,
  abortController?: AbortController,
) => {
  while (!abortController?.signal.aborted) {
    await refreshHeaders(authInfo);
    const { exp } = getOrgDataAndExpFromLocal();
    const currentTimeSecs = Math.round(new Date().getTime() / 1000);

    // update it at least every 5 minutes
    // update it at most every 5 seconds
    const secondsToNextUpdate = Math.min(
      Math.max(exp - currentTimeSecs, 5),
      60 * 5,
    );

    await new Promise((r) => setTimeout(r, secondsToNextUpdate * 1000));
  }
};

function useAxiosWithPropelAuth() {
  const authInfo = useAuthInfo();
  const searchParams = useSearchParams();
  const isFocused = useIsFocused();
  useOrgSwitch();

  useEffect(() => {
    if (!isFocused) {
      return;
    }
    const abortController = new AbortController();
    refreshHeadersLoop(authInfo, abortController);
    return () => {
      abortController.abort();
    };
  }, [authInfo?.accessToken, authInfo?.isLoggedIn, isFocused]);

  useEffect(() => {
    const orgIdFromSearchParams = searchParams.get('orgId');
    const localOrg = getOrgDataFromLocal();
    if (!!orgIdFromSearchParams && orgIdFromSearchParams !== localOrg?.id) {
      const orgData = getOrgData(authInfo, orgIdFromSearchParams);
      if (orgData) {
        setOrgDataToLocal(orgData);
      }
    }
  }, [authInfo, searchParams.get('orgId')]);
}

export function WithAxiosWithPropelAuth({
  children,
}: {
  children?: React.ReactNode;
}) {
  useAxiosWithPropelAuth();
  const [initialDelayPassed, setIntialDelayPassed] = useState(false);
  const {
    isLoggedIn,
    isInIframe,
    hideChatButtonInIframe,
    dbUser,
    isCompetitionOrg,
    dbOrg,
    isMember,
    sortedOrgs,
  } = useUserSession();

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isChatOpenRef = useRef(false);

  const chatButtonStyle = useMemo(() => {
    let styles = '';
    styles += `
      #pylon-chat .PylonChat-bubbleFrameContainer {
        position: fixed !important;
      }
      .chat-drag-handle {
        position: fixed;
        width: 60px;
        height: 60px;
        cursor: pointer;
        z-index: 2147480003;
        background: transparent;
      }
    `;
    if ((hideChatButtonInIframe || !dbOrg) && isInIframe) {
      styles += `#pylon-chat { display: none; }`;
      styles += `#warmly-widget { display: none; }`;
    }
    if (isCompetitionOrg) {
      if (pathname === '/home') {
        styles += `#pylon-chat .PylonChat-bubbleFrameContainer { right: 180px; }`;
      }
    }
    return styles;
  }, [isInIframe, hideChatButtonInIframe, isCompetitionOrg, pathname, dbOrg]);

  const hostname =
    typeof window === 'undefined' ||
    !window ||
    typeof document === 'undefined' ||
    !document?.referrer
      ? ''
      : window.location != window.parent.location
        ? new URL(document.referrer).hostname
        : document.location.hostname;

  const isHyperboundHostname = useMemo(
    () => hostname?.endsWith('hyperbound.ai'),
    [hostname],
  );

  useEffect(() => {
    if (isInIframe) {
      const styleTag = document.createElement('style');
      styleTag.type = 'text/css';

      document.head.appendChild(styleTag);

      styleTag.innerHTML = `.t-consentPrompt { width: 50%; height: 50%; }`;

      return () => {
        document.head.removeChild(styleTag);
      };
    }
  }, [isInIframe]);

  useEffect(() => {
    setTimeout(() => {
      setIntialDelayPassed(true);
    }, 2500);
  }, []);

  const handleDrag = () => {
    let isDragging = false;
    let currentX: number;
    let currentY: number;
    let initialX: number;
    let initialY: number;
    let startTime: number;
    let movedDistance = 0;

    // Create drag handle div
    const dragHandle = document.createElement('div');
    dragHandle.className = 'chat-drag-handle';
    document.body.appendChild(dragHandle);

    const updateDragHandlePosition = () => {
      const chatBubble = document.querySelector(
        '#pylon-chat .PylonChat-bubbleFrameContainer',
      ) as HTMLElement;
      if (!chatBubble) return;

      const rect = chatBubble.getBoundingClientRect();
      dragHandle.style.right = `${window.innerWidth - rect.right}px`;
      dragHandle.style.bottom = `${window.innerHeight - rect.bottom}px`;
    };

    const dragStart = (e: MouseEvent) => {
      const chatBubble = document.querySelector(
        '#pylon-chat .PylonChat-bubbleFrameContainer',
      ) as HTMLElement;
      if (!chatBubble) return;

      startTime = Date.now();
      movedDistance = 0;
      const rect = chatBubble.getBoundingClientRect();
      currentX = rect.left;
      currentY = rect.top;
      initialX = e.clientX - currentX;
      initialY = e.clientY - currentY;
      isDragging = true;
    };

    const dragEnd = () => {
      if (isDragging) {
        const endTime = Date.now();
        const dragDuration = endTime - startTime;

        // If drag duration is less than 200ms and moved distance is less than 5px, consider it a click
        if (dragDuration < 200 && movedDistance < 5) {
          // @ts-ignore
          window.Pylon?.(isChatOpenRef.current ? 'hide' : 'show');
        }
      }
      isDragging = false;
    };

    const drag = (e: MouseEvent) => {
      if (!isDragging) return;

      e.preventDefault();
      const chatBubble = document.querySelector(
        '#pylon-chat .PylonChat-bubbleFrameContainer',
      ) as HTMLElement;
      if (!chatBubble) return;

      const newX = e.clientX - initialX;
      const newY = e.clientY - initialY;

      const dx = newX - currentX;
      const dy = newY - currentY;
      movedDistance += Math.sqrt(dx * dx + dy * dy);

      currentX = newX;
      currentY = newY;

      const maxX = window.innerWidth - chatBubble.offsetWidth;
      const maxY = window.innerHeight - chatBubble.offsetHeight;
      currentX = Math.min(Math.max(0, currentX), maxX);
      currentY = Math.min(Math.max(0, currentY), maxY);

      chatBubble.style.left = `${currentX}px`;
      chatBubble.style.top = `${currentY}px`;
      updateDragHandlePosition();
    };

    dragHandle.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', dragEnd);

    // Initial position setup
    const positionInterval = setInterval(updateDragHandlePosition, 1000);

    return () => {
      dragHandle.removeEventListener('mousedown', dragStart);
      document.removeEventListener('mousemove', drag);
      document.removeEventListener('mouseup', dragEnd);
      clearInterval(positionInterval);
      document.body.removeChild(dragHandle);
    };
  };

  useEffect(() => {
    if (isLoggedIn && !!dbUser) {
      (window as any).pylon = {
        chat_settings: {
          app_id: '18a3cf20-ad1b-4f30-aebd-aaedd4247e7a',
          email: dbUser.email,
          name: `${dbUser.firstName} ${dbUser.lastName}`,
          avatar_url: '',
        },
      };
      if (searchParams.get('switchOrgUid')) {
        const org = sortedOrgs.find(
          (o) => o.orgId === searchParams.get('switchOrgUid'),
        );
        if (org) {
          console.log('Switching org to: ', org.orgId, org.orgName);
          const newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.delete('switchOrgUid');
          const switchOrgEvent = new CustomEvent<SwitchOrgEvent>('switchOrg', {
            detail: {
              targetUuid: org.orgId,
              targetName: org.orgName,
              callerRoute:
                window.location.pathname + '?' + newSearchParams.toString(),
            },
          });
          window.dispatchEvent(switchOrgEvent);
          return;
        } else {
          console.error('Org not found: ', searchParams.get('switchOrgUid'));
        }
      }
      // TODO: remove linkedin specific hack
      if (dbUser.orgId === 414 && isMember && !!sortedOrgs?.length) {
        console.log('Cannot access parent org. Switching to first sub org.');
        const firstSubOrg = sortedOrgs
          .sort((o1, o2) => {
            // prefer switching to orgs that contain linkedin in them
            if (o1.orgName.toLowerCase().includes('linkedin')) {
              return -1;
            }
            if (o2.orgName.toLowerCase().includes('linkedin')) {
              return 1;
            }
            return 0;
          })
          .find((o) => o.orgName !== 'LinkedIn');
        if (firstSubOrg) {
          console.log(
            'Switching org to: ',
            firstSubOrg.orgId,
            firstSubOrg.orgName,
          );
          const switchOrgEvent = new CustomEvent<SwitchOrgEvent>('switchOrg', {
            detail: {
              targetUuid: firstSubOrg.orgId,
              targetName: firstSubOrg.orgName,
              callerRoute: window.location.pathname + window.location.search,
            },
          });
          window.dispatchEvent(switchOrgEvent);
          return;
        } else {
          console.error('No sub orgs found to switch to');
        }
      }
    }

    // Wait for Pylon chat to load
    const interval = setInterval(() => {
      if (
        document.querySelector('#pylon-chat .PylonChat-bubbleFrameContainer')
      ) {
        clearInterval(interval);
        // @ts-ignore
        window.Pylon?.('onShow', () => {
          isChatOpenRef.current = true;
        });
        // @ts-ignore
        window.Pylon?.('onHide', () => {
          isChatOpenRef.current = false;
        });
        handleDrag();
      }
    }, 1000);
    const sentryUserPropsInterval = setInterval(() => {
      if (dbUser && Sentry.isInitialized()) {
        clearInterval(sentryUserPropsInterval);
        Sentry.setUser({
          email: dbUser.email,
        });
      }
    }, 1000);

    return () => {
      clearInterval(interval);
      clearInterval(sentryUserPropsInterval);
    };
  }, [isLoggedIn, dbUser, isMember, sortedOrgs, searchParams]);

  return (
    <>
      {!!chatButtonStyle && <style>{chatButtonStyle}</style>}
      {children}
      {isLoggedIn && (
        <Script id="pylon-script">
          {`(function(){var e=window;var t=document;var n=function(){n.e(arguments)};n.q=[];n.e=function(e){n.q.push(e)};e.Pylon=n;var r=function(){var e=t.createElement("script");e.setAttribute("type","text/javascript");e.setAttribute("async","true");e.setAttribute("src","https://widget.usepylon.com/widget/18a3cf20-ad1b-4f30-aebd-aaedd4247e7a");var n=t.getElementsByTagName("script")[0];n.parentNode.insertBefore(e,n)};if(t.readyState==="complete"){r()}else if(e.addEventListener){e.addEventListener("load",r,false)}})();`}
        </Script>
      )}
      {initialDelayPassed &&
        !isLoggedIn &&
        !(isInIframe && isHyperboundHostname) && (
          <Script
            id="termly"
            type="text/javascript"
            src="https://app.termly.io/resource-blocker/2bbd0187-9a75-47a3-b993-644b016f88c0?autoBlock=off"
          ></Script>
        )}
    </>
  );
}

export function useSentry() {
  const isSentryEnabled = process.env.NEXT_PUBLIC_SENTRY_ENABLED === 'true';
  const sentryDsn = process.env.NEXT_PUBLIC_SENTRY_DSN;

  if (isSentryEnabled && !Sentry.isInitialized()) {
    Sentry.init({
      dsn: sentryDsn,
      tracesSampleRate: 1.0,
    });
  }
}

const AuthWrapper = ({ children }: Props) => {
  useSentry();

  return (
    <AuthHandler getActiveOrgFn={() => getOrgDataFromLocal()?.id || ''}>
      <WithAxiosWithPropelAuth />
      {children}
    </AuthHandler>
  );
};

export default AuthWrapper;
