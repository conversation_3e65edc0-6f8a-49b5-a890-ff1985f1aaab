'use client'; // Error boundaries must be Client Components

import { Button } from '@/components/ui/button';
import { timeout } from '@/lib/utils';
import * as Sentry from '@sentry/nextjs';
import { Loader2Icon } from 'lucide-react';
import NextError from 'next/error';
import { useEffect, useMemo, useState } from 'react';

interface IProps {
  error: Error & { digest?: string };
}

export default function GlobalError({ error }: IProps) {
  const isChunkLoadError = useMemo(() => {
    return (
      /loading chunk.+failed/.test(error?.message?.toLowerCase?.() || '') ||
      error?.name?.toLowerCase?.() === 'chunkloaderror'
    );
  }, [error]);
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);
  const [areCachesCleared, setAreCachesCleared] = useState(false);

  const clearCaches = async () => {
    const cacheKeys = await caches.keys();
    await timeout(
      Promise.allSettled(cacheKeys.map((key) => caches.delete(key))),
      2000,
    );
    setAreCachesCleared(true);
  };

  useEffect(() => {
    if (isChunkLoadError) {
      clearCaches();
    }
  }, [isChunkLoadError]);

  return (
    <html>
      <body>
        {isChunkLoadError ? (
          <div className="flex flex-col items-center justify-center h-screen w-screen">
            <p className="">
              Amazing news - a new version of this app is available!
            </p>
            <Button
              disabled={!areCachesCleared}
              variant={'default'}
              className="mt-3"
              size="default"
              onClick={() => {
                window.location.reload();
              }}
            >
              {!areCachesCleared ? (
                <Loader2Icon className="animate-spin mr-2" />
              ) : (
                <></>
              )}
              Reload
            </Button>
            <p className="mt-4 text-muted-foreground text-sm">
              Facing issues? Contact us at{' '}
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </div>
        ) : (
          <NextError statusCode={0} />
        )}
      </body>
    </html>
  );
}
