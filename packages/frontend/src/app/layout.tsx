import snippet from '@segment/snippet';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import relativeTime from 'dayjs/plugin/relativeTime';
import updateLocale from 'dayjs/plugin/updateLocale';
import utc from 'dayjs/plugin/utc';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import Script from 'next/script';
import 'react-toastify/dist/ReactToastify.css';
import AuthWrapper from './auth-wrapper';
import './globals.css';
import { CSPostHogProvider } from './providers';
import QueryWrapper from './query-wrapper';
import { FeatureFlagsProvider } from '@/contexts/FeatureFlagsContext';
import { Suspense } from 'react';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Hyperbound | AI Sales Role-Play & Upskilling Platform',
  description:
    'Scale sales training by simulating AI role-plays for cold calls, warm calls, discovery calls, & more. Customize AI buyers for your ICP. Close your skill gaps.',
  robots: {
    index: true,
    follow: true,
  },
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  category: 'technology',
  authors: [
    {
      name: 'IntelligentSystems Corp.',
    },
  ],
  creator: 'IntelligentSystems Corp.',
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_BASE_URL || 'https://app.hyperbound.ai',
  ),
  openGraph: {
    title: 'Hyperbound | AI Sales Role-Play & Upskilling Platform',
    description:
      'Scale sales training by simulating AI role-plays for cold calls, warm calls, discovery calls, & more. Customize AI buyers for your ICP. Close your skill gaps.',
    images: ['/images/hyperbound-banner.jpg'],
  },
  twitter: {
    title: 'Hyperbound | AI Sales Role-Play & Upskilling Platform',
    description:
      'Scale sales training by simulating AI role-plays for cold calls, warm calls, discovery calls, & more. Customize AI buyers for your ICP. Close your skill gaps.',
    images: ['/images/hyperbound-banner.jpg'],
  },
};

dayjs.extend(utc);
dayjs.extend(relativeTime);
dayjs.extend(updateLocale);
dayjs.extend(isBetween);

dayjs.updateLocale('en', {
  relativeTime: {
    future: 'in %s',
    past: '%s ago',
    s: 'a few seconds',
    m: '1m',
    mm: '%dm',
    h: '1h',
    hh: '%dh',
    d: '1d',
    dd: '%dd',
    M: '1mo',
    MM: '%dmo',
    y: '1y',
    yy: '%dy',
  },
});

function renderSnippet() {
  const opts = {
    apiKey: process.env.NEXT_PUBLIC_ANALYTICS_WRITE_KEY,
    // note: the page option only covers SSR tracking.
    // Page.js is used to track other events using `window.analytics.page()`
    page: true,
  };

  if (process.env.NODE_ENV === 'development') {
    return snippet.max(opts);
  }

  return snippet.min(opts);
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <FeatureFlagsProvider>
        <CSPostHogProvider>
          <head>
            <Script>{`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5MZ37TXD')`}</Script>
            <Script
              id="segment-script"
              dangerouslySetInnerHTML={{ __html: renderSnippet() }}
            />

            {/* <Script async src="https://cdn.withglimmer.com/t.js" /> */}
          </head>
          <body className={inter.className}>
            <noscript>
              <iframe
                src="https://www.googletagmanager.com/ns.html?id=GTM-5MZ37TXD"
                height="0"
                width="0"
                style={{ display: 'none', visibility: 'hidden' }}
              ></iframe>
            </noscript>
            <Suspense>
              <QueryWrapper>
                <AuthWrapper>
                  <main>
                    <div className="z-10 w-full items-center justify-center text-sm lg:flex">
                      {children}
                    </div>
                  </main>
                  <ReactQueryDevtools initialIsOpen={false} />
                </AuthWrapper>
              </QueryWrapper>
            </Suspense>
          </body>
        </CSPostHogProvider>
      </FeatureFlagsProvider>
    </html>
  );
}
