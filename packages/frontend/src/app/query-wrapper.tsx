'use client';

import { isBrowser } from '@/lib/utils';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { QueryClient } from '@tanstack/react-query';
import {
  PersistQueryClientProvider,
  Persister,
} from '@tanstack/react-query-persist-client';

interface Props {
  children: React.ReactNode;
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 60 * 24, // 24 hours,
      gcTime: 1000 * 60 * 60, // 1 hour
    },
  },
});

let persister: Persister;

if (isBrowser()) {
  persister = createSyncStoragePersister({ storage: window.localStorage });
}

const QueryWrapper = ({ children }: Props) => (
  <PersistQueryClientProvider
    client={queryClient}
    persistOptions={{ persister }}
  >
    {children}
  </PersistQueryClientProvider>
);

export default QueryWrapper;
