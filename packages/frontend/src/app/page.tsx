'use client';

import useUserSession from '@/hooks/useUserSession';
import { RoleEnum } from '@/lib/User/types';
import { useActiveOrg, useAuthInfo } from '@propelauth/react';
import { redirect } from 'next/navigation';

export default function Home() {
  const authInfo = useAuthInfo();
  const org = useActiveOrg();

  const { hideHomePage } = useUserSession();

  const isTemp = authInfo.accessHelper?.isRole(
    org?.orgId as string,
    RoleEnum.TEMP,
  );

  // Unlike the higher order functions, we need to check the loading case now
  if (authInfo?.loading) {
    return <div />;
  } else {
    if (authInfo?.isLoggedIn && !isTemp) {
      if (hideHomePage) {
        redirect('/ai-roleplay');
      } else {
        redirect('/home');
      }
    } else {
      redirect('/buyers');
    }
  }
}
