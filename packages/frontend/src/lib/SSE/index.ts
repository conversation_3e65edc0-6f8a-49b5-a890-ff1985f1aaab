import { API } from '../Client';
import { getOrgDataFromLocal } from '@/app/auth-wrapper';
import { SSEResponseChunk } from './types';

namespace SSEService {
    export async function* openStream(
      endpoint: string,
      body: object
    ): AsyncGenerator<SSEResponseChunk, void, unknown> {
      const orgId = getOrgDataFromLocal()?.id;
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_API_URL}${endpoint}?orgId=${orgId}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'text/event-stream',
            Authorization:
              (API.defaults.headers.common['Authorization'] || "") as string,
          },
          body: JSON.stringify(body),
        }
      );
  
      // Throw early if response is invalidl.
      if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
      if (!response.body) throw new Error("ReadableStream not yet supported in this browser.");
  
      // Establish instance.
      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let expectedChunkId = 0;
      const outOfOrderChunkBuffer = new Map<number, SSEResponseChunk>();
  
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
  
        // Decode chunk and add to buffer.
        buffer += decoder.decode(value, { stream: true });
        const parts = buffer.split("\n\n");
        buffer = parts.pop() || "";
  
        for (const part of parts) {
          const lines = part
            .split("\n")
            .map((line) => line.trim())
            .filter((line) => line.length > 0);
          const dataLines = lines
            .filter((line) => line.startsWith("data:"))
            .map((line) => line.substring("data:".length).trim());
  
          if (dataLines.length === 0) continue;
  
          // In case of multiple data lines, join with newline.
          const dataStr = dataLines.join("\n");
  
          try {
            const eventData = JSON.parse(dataStr) as | (SSEResponseChunk & { id?: number }) |  null;
            if (!eventData) continue;
  
            // Verify chunk order.
            if (typeof eventData.id === "number") {
                const id = eventData.id;
    
                if (id === expectedChunkId) {
                  // Chunk matches the expected order, yield immediately.
                  yield eventData;
                  expectedChunkId++;
    
                  // Check if the next expected chunks are available in the buffer.
                  while (outOfOrderChunkBuffer.has(expectedChunkId)) {
                    const bufferedChunk = outOfOrderChunkBuffer.get(expectedChunkId)!;
                    outOfOrderChunkBuffer.delete(expectedChunkId);
                    yield bufferedChunk;
                    expectedChunkId++;
                  }
                } else if (id > expectedChunkId) {
                  // Out-of-order chunk: store it in the buffer.
                  outOfOrderChunkBuffer.set(id, eventData);
    
                  // Fallback: if buffer reaches 3 and the expected chunk is still missing, yield the buffered chunks in sorted order and update expectedChunkId.
                  if (
                    outOfOrderChunkBuffer.size >= 3 &&
                    !outOfOrderChunkBuffer.has(expectedChunkId)
                  ) {
                    const sortedKeys = Array.from(outOfOrderChunkBuffer.keys()).sort(
                      (a, b) => a - b
                    );
                    
                    for (const key of sortedKeys) {
                      const bufferedChunk = outOfOrderChunkBuffer.get(key)!;
                      yield bufferedChunk;
                    }

                    // Update expectedChunkId to the highest yielded id + 1.
                    expectedChunkId = sortedKeys[sortedKeys.length - 1] + 1;
                    outOfOrderChunkBuffer.clear();
                  }
                } else {
                  console.warn(`Received duplicate or outdated chunk with id ${id}, expected ${expectedChunkId}. Ignoring.`);
                }
              } else {
                // Chunk has no id: yield it immediately.
                yield eventData;
              }
          } catch (e) {
            // Parsing failed, yield as raw str.
            yield { type: "raw", content: dataStr };
          }
        }
      }
    }
  }
export default SSEService;