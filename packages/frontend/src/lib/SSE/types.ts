export type SSEResponseChunk = {
    type: 'content',
    content: string,
    finishReason: string,
} | {
    type: 'done',
    finishReason: string,
} | {
    type: 'error',
    error: string,
} | {
    type: 'raw',
    content: string,
} | {
    type: 'tool_call',
    tool_calls: [
        {
            id: string,
            type: string,
            function: {
                name: string,
                arguments: string,
            },
        }
    ],
    finishReason: string,
}