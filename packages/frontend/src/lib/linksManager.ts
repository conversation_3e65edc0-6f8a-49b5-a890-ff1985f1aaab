// ----------- WARNING: PLEASE ADD A PAGE ONLY AFTER A SEARCH AND REPLACE IN THE WHOLE CODE -----------
// ----------- WARNING: PLEASE ADD A PAGE ONLY AFTER A SEARCH AND REPLACE IN THE WHOLE CODE -----------
// ----------- WARNING: PLEASE ADD A PAGE ONLY AFTER A SEARCH AND REPLACE IN THE WHOLE CODE -----------
enum Pages {
  HOME = '/home',
  TRAINING_CALLS = '/calls',
  REAL_CALLS = '/real-calls', //check below, this changes
  MEMBERS = '/members',
  TEAMS = '/teams',
  ORGANIZATION_SETTINGS = '/organization-settings',
  SCORECARDS = '/scorecards',
  INTEGRATIONS = '/integrations',
  DEVELOPERS = '/developers',
  LEARNING_MODULES = '/coaching/learning-modules',
  LEARNING_MODULES_EDIT = '/coaching/learning-modules/edit',
  AI_ROLEPLAY = '/ai-roleplay',
  ANALYTICS = '/analytics',
}
// ----------- WARNING: PLEASE ADD A PAGE ONLY AFTER A SEARCH AND REPLACE IN THE WHOLE CODE -----------
// ----------- WARNING: PLEASE ADD A PAGE ONLY AFTER A SEARCH AND REPLACE IN THE WHOLE CODE -----------
// ----------- WARNING: PLEASE ADD A PAGE ONLY AFTER A SEARCH AND REPLACE IN THE WHOLE CODE -----------

namespace LinksManager {
  function formatUrl(
    base: string,
    p: string,
    searchParams?: Record<string, string | number | boolean>,
  ): string {
    let url = base;
    if (p != '') {
      if (p[0] == '?') {
        url += `${p}`;
      } else {
        url += `/${p}`;
      }
    }

    if (searchParams) {
      Object.entries(searchParams).forEach(([key, value]) => {
        url += `?${key}=${value}`;
      });
    }
    return url.replace(/([^:]\/)\/+/g, '$1');
  }

  export function trainingCalls(p: string = ''): string {
    return formatUrl(Pages.TRAINING_CALLS, p);
  }

  export function realCalls(callId?: number): string {
    if (callId) {
      return Pages.REAL_CALLS + `/${callId}`;
    } else {
      return formatUrl('/calls?openTab=real-calls', '');
    }
  }

  export function members(p: string = ''): string {
    return formatUrl(Pages.MEMBERS, p);
  }

  export function teams(p: string = ''): string {
    return formatUrl(Pages.TEAMS, p);
  }

  export function organizationSettings(p: string = ''): string {
    return formatUrl(Pages.ORGANIZATION_SETTINGS, p);
  }

  export function scorecards(p: string = ''): string {
    return formatUrl(Pages.SCORECARDS, p);
  }

  export function integrations(p: string = ''): string {
    return formatUrl(Pages.INTEGRATIONS, p);
  }

  export function developers(p: string = ''): string {
    return formatUrl(Pages.DEVELOPERS, p);
  }

  export function learningModules(
    templateId: string = '',
    searchParams?: Record<string, string | number | boolean>,
  ): string {
    return formatUrl(Pages.LEARNING_MODULES, templateId, searchParams);
  }

  export function learningModulesEdit(
    id: string = '',
    searchParams?: Record<string, string | number | boolean>,
  ): string {
    return formatUrl(Pages.LEARNING_MODULES_EDIT, id, searchParams);
  }
}

export { Pages };
export default LinksManager;
