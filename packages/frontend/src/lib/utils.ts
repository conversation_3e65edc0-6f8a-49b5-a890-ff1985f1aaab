import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { VapiCallMessage } from './Call/types';

export function random() {
  return crypto.getRandomValues(new Uint32Array(1))[0] / 2 ** 32;
}

const sanitizeTranscript = (transcript: string): string => {
  let sanitized = transcript.trim();

  // Remove control characters except line breaks & tabs
  sanitized = sanitized.replace(/[^\x20-\x7E\t\n\r]/g, '');

  // Normalize line breaks (replace Windows \r\n with \n)
  sanitized = sanitized.replace(/\r\n/g, '\n');

  // Remove excessive blank lines (more than 2 in a row)
  sanitized = sanitized.replace(/\n{3,}/g, '\n\n');

  return sanitized;
};

export const isValidUUID = (uuid: string) => {
  const uuidV4Regex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidV4Regex.test(uuid);
};

export const validateAndSanitizeAnyTranscript = (
  transcript: string,
): string => {
  // Detect binary data by checking for non-printable ASCII characters
  for (let i = 0; i < transcript.length; i++) {
    const charCode = transcript.charCodeAt(i);
    if (charCode < 32 && !['\t', '\n', '\r'].includes(transcript[i])) {
      throw new Error('Transcript contains binary data.');
    }
  }

  // Check for common non-text file signatures
  const nonTextPatterns = [
    /%PDF-\d\.\d/, // PDF file header
    /%¡PS-Adobe/, // PostScript file header
    /\x89PNG/, // PNG image file signature
    /\xFF\xD8\xFF/, // JPEG image file signature
  ];

  if (nonTextPatterns.some((pattern) => pattern.test(transcript))) {
    throw new Error('Non-text or unsupported file format detected.');
  }

  const sanitized = sanitizeTranscript(transcript);

  if (!sanitized) {
    throw new Error('Transcript is empty or invalid.');
  }

  return sanitized;
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function isBrowser() {
  return typeof window !== 'undefined';
}

// copy pasta from https://stackoverflow.com/a/6969486
export function escapeStrForRegex(str: string) {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export function formatDuration(milliseconds: number) {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes)}:${String(remainingSeconds).padStart(2, '0')}`;
}

// Format time as MM:SS
export function formatTime(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes)}:${String(remainingSeconds).padStart(2, '0')}`;
}

export const formatTranscript = (
  transcript: VapiCallMessage[],
  botName: string,
  callId: string,
): string => {
  if (!transcript?.length) {
    return '';
  }

  const result = `Total Duration: ${formatTime(
    transcript[transcript.length - 1].time - transcript[0].time,
  )}\n\nCall ID: ${callId}\n\nTranscript:\n\n`;
  const conversation = transcript
    .map(
      (item, i) =>
        `[${formatTime(transcript[i].time - transcript[0].time)}] ${
          item?.role === 'user' ? 'Rep' : botName
        }: ${item.message}`,
    )
    .join('\n\n');

  return result + conversation;
};

export const formatRealCallTranscript = (
  transcript: {
    message: string;
    userName: string;
    secondsFromStart: number;
  }[],
  callId: number,
  platform: string,
): string => {
  if (!transcript?.length) {
    return '';
  }

  const result = `Total Duration: ${formatTime(
    (transcript[transcript.length - 1].secondsFromStart -
      transcript[0].secondsFromStart) *
      1000,
  )}\n\nReal Call ID: ${callId}\n\nReal Call Platform: ${platform}\n\nTranscript:\n\n`;
  const conversation = transcript
    .map(
      (item, i) =>
        `[${formatTime((transcript[i].secondsFromStart - transcript[0].secondsFromStart) * 1000)}] ${item.userName}: ${item.message}`,
    )
    .join('\n\n');

  return result + conversation;
};

export const toBase64 = (arr: ArrayLike<number> | ArrayBuffer) => {
  let binary = '';
  const bytes = new Uint8Array(arr);
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

export const sha256 = async (text: string, encoding: 'base64') => {
  const buffer = await crypto.subtle.digest(
    'SHA-256',
    new TextEncoder().encode(text),
  );
  if (encoding === 'base64') {
    return toBase64(buffer);
  }
  return '';
};

export const generateShortId = (): string => {
  return random().toString(36).slice(2);
};

export const checkObjHasSomeValues = (obj: unknown): boolean => {
  switch (typeof obj) {
    case 'bigint':
    case 'number':
    case 'undefined':
    case 'string': {
      return !!obj;
    }
    case 'boolean':
    case 'function':
    case 'symbol': {
      return true;
    }
    case 'object': {
      return (
        !!obj &&
        Object.values(obj).some((child) => checkObjHasSomeValues(child))
      );
    }
  }
};

export const isEmailAddress = (email: string) => {
  return email.match(
    // eslint-disable-next-line no-useless-escape
    /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  );
};

export function timeAgo(date: Date | string | number): string {
  const now = new Date();
  const past = new Date(date);
  const diff = now.getTime() - past.getTime();

  const isFuture = diff < 0;
  const absDiff = Math.abs(diff);

  // Time calculations
  const seconds = Math.floor(absDiff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  // Format based on your specifications
  let result: string;
  if (seconds < 45) {
    result = 'a few seconds';
  } else if (seconds < 60) {
    result = '1m';
  } else if (minutes < 60) {
    result = `${minutes}m`;
  } else if (hours < 24) {
    result = hours === 1 ? '1h' : `${hours}h`;
  } else if (days < 30) {
    result = days === 1 ? '1d' : `${days}d`;
  } else if (months < 12) {
    result = months === 1 ? '1mo' : `${months}mo`;
  } else {
    result = years === 1 ? '1y' : `${years}y`;
  }

  return isFuture ? `in ${result}` : `${result} ago`;
}

export const sleep = async (ms: number) => {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
};

export const timeout = async (promise: Promise<unknown>, ms: number) => {
  await Promise.race([promise, sleep(ms)]);
};

export function shortenEmail(email: string): string {
  let [username, domain] = email.split('@');
  if (username.length > 12) {
    username = shortenStr(username, 4);
  }
  if (domain.length > 20) {
    domain = shortenStr(domain, 4);
  }
  return `${username}@${domain}`;
}

export function shortenStr(str: string, partLength: number): string {
  return `${str.substring(0, partLength)}…${str.substring(str.length - partLength)}`;
}
