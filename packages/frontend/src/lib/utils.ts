import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { VapiCallMessage } from './Call/types';

export const BLOCKED_FILE_TYPES = [
  'image/png',
  'image/jpeg',
  'image/gif',
  'image/webp',
  'audio/mpeg',
  'audio/wav',
  'audio/ogg',
  'video/mp4',
  'video/webm',
  'video/quicktime',
];

export enum TranscriptFileExtensions {
  VTT = 'vtt', // WebVTT (Zoom, Gong, others)
  SRT = 'srt', // SubRip Subtitle (common for transcripts)
  TXT = 'txt', // Plain text (Kaia, Zoom exports)
  JSON = 'json', // JSON format (Gong, Otter.ai, others)
}

export function random() {
  return crypto.getRandomValues(new Uint32Array(1))[0] / 2 ** 32;
}

const sanitizeTranscript = (transcript: string): string => {
  let sanitized = transcript.trim();

  // Remove control characters except line breaks & tabs
  sanitized = sanitized.replace(/[^\x20-\x7E\t\n\r]/g, '');

  // Normalize line breaks (replace Windows \r\n with \n)
  sanitized = sanitized.replace(/\r\n/g, '\n');

  // Remove excessive blank lines (more than 2 in a row)
  sanitized = sanitized.replace(/\n{3,}/g, '\n\n');

  return sanitized;
};

export const validateAndSanitizeAnyTranscript = (
  transcript: string,
): string => {
  // Detect binary data by checking for non-printable ASCII characters
  for (let i = 0; i < transcript.length; i++) {
    const charCode = transcript.charCodeAt(i);
    if (charCode < 32 && !['\t', '\n', '\r'].includes(transcript[i])) {
      throw new Error('Transcript contains binary data.');
    }
  }

  // Check for common non-text file signatures
  const nonTextPatterns = [
    /%PDF-\d\.\d/, // PDF file header
    /%¡PS-Adobe/, // PostScript file header
    /\x89PNG/, // PNG image file signature
    /\xFF\xD8\xFF/, // JPEG image file signature
  ];

  if (nonTextPatterns.some((pattern) => pattern.test(transcript))) {
    throw new Error('Non-text or unsupported file format detected.');
  }

  const sanitized = sanitizeTranscript(transcript);

  if (!sanitized) {
    throw new Error('Transcript is empty or invalid.');
  }

  return sanitized;
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function isBrowser() {
  return typeof window !== 'undefined';
}

// copy pasta from https://stackoverflow.com/a/6969486
export function escapeStrForRegex(str: string) {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export function formatDuration(milliseconds: number) {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes)}:${String(remainingSeconds).padStart(2, '0')}`;
}

// Format time as MM:SS
export function formatTime(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes)}:${String(remainingSeconds).padStart(2, '0')}`;
}

export const formatTranscript = (
  transcript: VapiCallMessage[],
  botName: string,
  callId: string,
): string => {
  if (!transcript?.length) {
    return '';
  }

  const result = `Total Duration: ${formatTime(
    transcript[transcript.length - 1].time - transcript[0].time,
  )}\n\nCall ID: ${callId}\n\nTranscript:\n\n`;
  const conversation = transcript
    .map(
      (item, i) =>
        `[${formatTime(transcript[i].time - transcript[0].time)}] ${
          item?.role === 'user' ? 'Rep' : botName
        }: ${item.message}`,
    )
    .join('\n\n');

  return result + conversation;
};

export const formatRealCallTranscript = (
  transcript: {
    message: string;
    userName: string;
    secondsFromStart: number;
  }[],
  callId: number,
  platform: string,
): string => {
  if (!transcript?.length) {
    return '';
  }

  const result = `Total Duration: ${formatTime(
    (transcript[transcript.length - 1].secondsFromStart -
      transcript[0].secondsFromStart) *
      1000,
  )}\n\nReal Call ID: ${callId}\n\nReal Call Platform: ${platform}\n\nTranscript:\n\n`;
  const conversation = transcript
    .map(
      (item, i) =>
        `[${formatTime((transcript[i].secondsFromStart - transcript[0].secondsFromStart) * 1000)}] ${item.userName}: ${item.message}`,
    )
    .join('\n\n');

  return result + conversation;
};

export const toBase64 = (arr: ArrayLike<number> | ArrayBuffer) => {
  let binary = '';
  const bytes = new Uint8Array(arr);
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

export const sha256 = async (text: string, encoding: 'base64') => {
  const buffer = await crypto.subtle.digest(
    'SHA-256',
    new TextEncoder().encode(text),
  );
  if (encoding === 'base64') {
    return toBase64(buffer);
  }
  return '';
};

export const generateShortId = (): string => {
  return random().toString(36).slice(2);
};

export const checkObjHasSomeValues = (obj: unknown): boolean => {
  switch (typeof obj) {
    case 'bigint':
    case 'number':
    case 'undefined':
    case 'string': {
      return !!obj;
    }
    case 'boolean':
    case 'function':
    case 'symbol': {
      return true;
    }
    case 'object': {
      return (
        !!obj &&
        Object.values(obj).some((child) => checkObjHasSomeValues(child))
      );
    }
  }
};

export const isEmailAddress = (email: string) => {
  return email.match(
    // eslint-disable-next-line no-useless-escape
    /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  );
};

export function timeAgo(date: Date | string | number): string {
  const now = new Date();
  const past = new Date(date);
  const diff = now.getTime() - past.getTime();

  const isFuture = diff < 0;
  const absDiff = Math.abs(diff);

  // Time calculations
  const seconds = Math.floor(absDiff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);

  // Format based on your specifications
  let result: string;
  if (seconds < 45) {
    result = 'a few seconds';
  } else if (seconds < 60) {
    result = '1m';
  } else if (minutes < 60) {
    result = `${minutes}m`;
  } else if (hours < 24) {
    result = hours === 1 ? '1h' : `${hours}h`;
  } else if (days < 30) {
    result = days === 1 ? '1d' : `${days}d`;
  } else if (months < 12) {
    result = months === 1 ? '1mo' : `${months}mo`;
  } else {
    result = years === 1 ? '1y' : `${years}y`;
  }

  return isFuture ? `in ${result}` : `${result} ago`;
}

export const parseTime = (time: string): number => {
  let t = 0;

  const all = time.split('-->')[0].split(':');
  const hours =
    60 * 60 * 1000 * parseInt(all[0].replaceAll('00', '0').replaceAll(' ', ''));
  const minutes =
    60 * 1000 * parseInt(all[1].replaceAll('00', '0').replaceAll(' ', ''));

  let ms = 0;
  if (all[2]) {
    ms = parseInt(all[2].replace('.', ''));
    if (ms < 1000) {
      ms = 1000 + ms;
    }
  }

  t = ms + hours + minutes;
  //t = parseInt(all[2].split('.')[0]) + hours + minutes;

  return t;
};

const timeToSeconds = (time: string): number => {
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds;
};

export const kaiaToJson = (content: string): RealCallTranscriptMessage[] => {
  // https://support.outreach.io/hc/en-us/articles/28005562346395-Exporting-Data-in-Kaia-using-Daily-Export
  const transcript: RealCallTranscriptMessage[] = [];
  const lines = content.split('\n').map((line) => line.trim());

  for (const line of lines) {
    if (!line) continue; // Skip empty lines

    // Match format: [HH:MM:SS] - "Speaker": "Message"
    let match = line.match(/^\[(\d{2}:\d{2}:\d{2})\] - "(.*?)": "(.*)"$/);

    // Match alternative format: HH:MM:SS - (ID)Speaker: Message
    if (!match) {
      match = line.match(/^(\d{2}:\d{2}:\d{2}) - \((\d+)\)(.*?):\s*(.*)$/);
    }

    if (match) {
      let timestamp, userName, message;

      if (match.length === 4) {
        // First format: [HH:MM:SS] - "Speaker": "Message"
        timestamp = match[1];
        userName = match[2];
        message = match[3];
      } else {
        // Second format: HH:MM:SS - (ID)Speaker: Message
        timestamp = match[1];
        userName = match[3].trim();
        message = match[4].trim();
      }

      transcript.push({
        secondsFromStart: timeToSeconds(timestamp),
        userName,
        message,
        role: '' as unknown as 'bot' | 'user',
      });
    }
  }

  return transcript;
};

export const vttToJson = (content: string): RealCallTranscriptMessage[] => {
  const transcript: RealCallTranscriptMessage[] = [];
  const lines = content.split('\n').map((l: string) => l.trim());

  let currentMessage: Partial<RealCallTranscriptMessage> = {};
  let start = false;
  let noActorIdentified = true;
  for (const l of lines) {
    if (l == '') {
      start = false;
      if (currentMessage.message) {
        transcript.push(currentMessage as RealCallTranscriptMessage);
        currentMessage = {};
      }
    } else if (l.toLowerCase().includes('-->')) {
      start = true;
      currentMessage.secondsFromStart = parseTime(l);
    } else if (start) {
      let actor = '';
      let msg = '';
      let added = false;
      const re: RegExp = /<v(.*?)>/g;
      const matches = l.match(re);
      if (matches) {
        actor = matches[0].replace('<v', '').replace('>', '').trim();
        msg = l.replace(matches[0], '').trim();
        added = true;
      } else {
        const i = l.indexOf(':');
        if (i > -1) {
          const all = [l.slice(0, i), l.slice(i + 1)];
          actor = all[0].trim();
          msg = all[1].trim();
          added = true;
        }
      }

      if (!added) {
        if (!currentMessage.message) {
          currentMessage.message = '';
        }
        currentMessage.message += l;
      } else {
        currentMessage.userName = actor;
        currentMessage.message = msg;
        currentMessage.role = '' as unknown as 'bot' | 'user';
        noActorIdentified = false;
      }
    }
  }

  if (noActorIdentified) {
    throw new Error(
      'No actor identified in the transcript: the tool generating the vtt file is not including information about the speaker.',
    );
  }

  return transcript;
};

export const srtToJson = (content: string) => {
  const timeToSeconds = (time: string): number => {
    const [hours, minutes, seconds] = time
      .split(':')
      .map((part) => parseFloat(part.replace(',', '.')));
    return hours * 3600 + minutes * 60 + seconds;
  };

  const transcript: RealCallTranscriptMessage[] = [];
  const lines = content.split('\n').map((line) => line.trim());

  let currentMessage: Partial<RealCallTranscriptMessage> = {};
  let start = false;

  for (const line of lines) {
    if (!line) {
      // Empty line means the end of a block, push the message if it exists
      if (currentMessage.message) {
        transcript.push(currentMessage as RealCallTranscriptMessage);
        currentMessage = {};
      }
      start = false;
    } else if (line.includes('-->')) {
      // Extract timestamp
      start = true;
      const [startTime] = line.split(' --> ');
      currentMessage.secondsFromStart = timeToSeconds(startTime.trim());
    } else if (start) {
      // Extract speaker and message
      const separatorIndex = line.indexOf(':');
      if (separatorIndex > -1) {
        currentMessage.userName = line.slice(0, separatorIndex).trim();
        currentMessage.message = line.slice(separatorIndex + 1).trim();
      } else {
        // Continue message from previous line
        currentMessage.message = (currentMessage.message || '') + ' ' + line;
      }
    }
  }

  return transcript;
};

// Helper function to check if the text matches the kaia format
const isKaiaFormat = (text: string): boolean => {
  const kaiaFormatRegex = /^\d{2}:\d{2}:\d{2} - \(\d\).+/m; // Regex to match the kaia format
  return kaiaFormatRegex.test(text);
};

export const handleFileToJson = (text: string, fileExtension: string) => {
  /**
   * Converts the content of a transcript file to JSON format based on the file extension.
   *
   * This function supports various transcript file formats, including:
   * - VTT (WebVTT)
   * - TXT (specific kaia format)
   * - SRT (SubRip Subtitle)
   * - JSON (already in JSON format)
   *
   * For TXT files, the function checks if the content matches a specific format:
   *
   * Example of the expected format:
   * ```
   * 00:00:06 - (2)Mike Goodwin:
   * Hello. How's everyone doing?
   * ```
   * If the content does not match this format, an error will be thrown.
   *
   * @param {string} text - The content of the transcript file as a string.
   * @param {string} fileExtension - The file extension of the transcript file, used to determine the appropriate parsing method.
   * @returns {object} - The parsed JSON object representing the transcript.
   * @throws {Error} - Throws an error if the file format is unsupported or if the TXT file does not match the expected kaia format.
   *
   * @example
   * const vttText = "WEBVTT\n\n00:00:00.000 --> 00:00:01.000\nHello!";
   * const jsonVtt = handleFileToJson(vttText, TranscriptFileExtensions.VTT);
   *
   * const kaiaText = "00:00:06 - (2)Mike Goodwin:\nHello. How's everyone doing?";
   * const jsonKaia = handleFileToJson(kaiaText, TranscriptFileExtensions.TXT);
   *
   * const jsonText = '{"key": "value"}';
   * const jsonParsed = handleFileToJson(jsonText, TranscriptFileExtensions.JSON);
   */

  let json;

  switch (fileExtension) {
    case TranscriptFileExtensions.VTT:
      // Zoom files
      json = vttToJson(text);
      break;
    case TranscriptFileExtensions.TXT:
      // Check if the text matches the specific format for kaiaToJson
      if (isKaiaFormat(text)) {
        json = kaiaToJson(text);
      } else {
        throw new Error('Unrecognized .txt file format.');
      }
      break;
    case TranscriptFileExtensions.SRT:
      json = srtToJson(text); // If needed
      break;
    case TranscriptFileExtensions.JSON:
      json = JSON.parse(text); // Parse the JSON transcript
      // Validate the shape of the parsed JSON
      if (
        !Array.isArray(json) ||
        !json.every(
          (item) =>
            item.message &&
            item.userName &&
            typeof item.secondsFromStart === 'number',
        )
      ) {
        throw new Error(
          'The JSON you tried to upload was in an invalid format.',
        );
      }
      break;
    default:
      throw new Error(
        'Unsupported file format. Please upload a valid transcript file.',
      );
  }
  return json;
};

export const sleep = async (ms: number) => {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
};

export const timeout = async (promise: Promise<unknown>, ms: number) => {
  await Promise.race([promise, sleep(ms)]);
};

export function shortenEmail(email: string): string {
  let [username, domain] = email.split('@');
  if (username.length > 12) {
    username = shortenStr(username, 4);
  }
  if (domain.length > 20) {
    domain = shortenStr(domain, 4);
  }
  return `${username}@${domain}`;
}

export function shortenStr(str: string, partLength: number): string {
  return `${str.substring(0, partLength)}…${str.substring(str.length - partLength)}`;
}
