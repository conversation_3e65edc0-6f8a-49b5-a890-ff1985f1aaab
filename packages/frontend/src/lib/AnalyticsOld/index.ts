/* eslint-disable @typescript-eslint/no-explicit-any */
import { AnalyticsFilters } from '@/contexts/AnalyticsContext';
import { API } from '../Client';
import {
  DashboardWidgetTemplateDto,
  DashboardTabDto,
  DashboardWidgetDto,
  DateFilterType,
  AnalyticsFilterDateRange,
  AnalyticsFilterType,
  AnalyticsFilterState,
  ANALYTICS_DATE_RANGES,
} from './types';
import dayjs from 'dayjs';

namespace AnalyticsService {
  /*********************************/
  /********** DASHBOARD ************/
  /*********************************/

  function base64ToBlob(base64: string) {
    const binaryString = window.atob(base64);
    const binaryLen = binaryString.length;

    const ab = new ArrayBuffer(binaryLen);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < binaryLen; i++) {
      ia[i] = binaryString.charCodeAt(i);
    }

    return new Blob([ab]);
  }

  export async function exportToCsv(
    includeGraphs: string | string[],
    filters: AnalyticsFilterState,
  ): Promise<Blob | string> {
    let res;

    try {
      res = await API.post('/analytics/export-to-csv', {
        includeGraphs,
        filters,
      });
    } catch (err) {
      console.log(err);
    }

    if (res?.data) {
      return base64ToBlob(res?.data);
    } else {
      return '';
    }
  }

  export function getExportToCsvUrl(): string {
    return process.env.NEXT_PUBLIC_BASE_API_URL + '/analytics/export-to-csv';
  }

  export async function getWidgetTemplates(): Promise<
    DashboardWidgetTemplateDto[]
  > {
    let res;

    try {
      res = await API.get('/analytics/dashboard/templates');
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getDashboard(): Promise<DashboardTabDto[]> {
    let res;

    try {
      res = await API.get('/analytics/dashboard');
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getDashboardForHome(): Promise<DashboardTabDto[]> {
    let res;

    try {
      res = await API.get('/analytics/dashboard-for-home');
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getDashboardForReps(): Promise<DashboardTabDto[]> {
    let res;

    try {
      res = await API.get('/analytics/dashboard-for-reps');
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function addDashboardTab(
    title: string,
    isPersonal: boolean,
    isForAdminOnly: boolean,
    showInHome = false,
  ): Promise<DashboardTabDto> {
    let res;

    try {
      res = await API.post('/analytics/dashboard', {
        title,
        isPersonal,
        isForAdminOnly,
        showInHome,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateDashboardTab(
    dashboardId: number,
    title: string,
    filters: string,
  ): Promise<DashboardTabDto> {
    let res;

    try {
      res = await API.patch(`/analytics/dashboard/${dashboardId}`, {
        title,
        filters,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteDashboardTab(
    dashboardId: number,
  ): Promise<boolean> {
    const res = await API.delete(`/analytics/dashboard/${dashboardId}`);

    return res?.data;
  }

  export async function cloneDashboardTab(
    dashboardId: number,
  ): Promise<DashboardTabDto> {
    let res;

    try {
      res = await API.post(`/analytics/dashboard/clone/${dashboardId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function addWidgetToDashboard(
    dashboardId: number,
    widgetTemplateId: number,
    name: string,
    description: string,
    props: any,
    appliedFilters: any,
  ): Promise<DashboardWidgetDto> {
    let res;

    try {
      res = await API.post(`/analytics/dashboard/widget/${dashboardId}`, {
        widgetTemplateId,
        name,
        description,
        props,
        appliedFilters,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateWidget(
    widgetId: number,
    name: string,
    description: string,
    props: any,
    appliedFilters: any,
  ): Promise<DashboardWidgetDto> {
    let res;

    try {
      res = await API.patch(`/analytics/dashboard/widget/${widgetId}`, {
        name,
        description,
        props,
        appliedFilters,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteWidget(
    widgetId: number,
  ): Promise<DashboardWidgetDto> {
    let res;

    try {
      res = await API.delete(`/analytics/dashboard/widget/${widgetId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getChartData(
    chartType: string,
    filters: AnalyticsFilterState,
  ): Promise<any> {
    let res;

    try {
      res = await API.post(
        `/analytics/dashboard/chart-data/${chartType}`,
        filters,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export function calculateDescription(
    widget: DashboardWidgetDto,
    data?: any,
  ): string {
    widget = JSON.parse(JSON.stringify(widget));

    const regex = /\{\{(.*?)\}\}/g;

    const str = widget.description;
    const matches = [];
    let match;

    while ((match = regex.exec(str)) !== null) {
      matches.push(match[1]); // match[1] contains the text inside {{ }}
    }

    if (matches.length > 0) {
      const segments = str.split(regex);
      const f = widget.appliedFilters;
      const dr = AnalyticsService.getDatesRange(f[AnalyticsFilterType.DATE]);
      const strs: any = {};
      for (const m of matches) {
        if (m.toLowerCase() == 'dates.short') {
          if (!f[AnalyticsFilterType.DATE]) {
            f[AnalyticsFilterType.DATE] = {};
          }

          if (f[AnalyticsFilterType.DATE].range === undefined) {
            f[AnalyticsFilterType.DATE].range =
              AnalyticsFilterDateRange.THIS_MONTH;
          }

          if (
            f[AnalyticsFilterType.DATE].range == AnalyticsFilterDateRange.ALL
          ) {
            strs[m] = 'all-time';
          } else if (
            f[AnalyticsFilterType.DATE].range != AnalyticsFilterDateRange.CUSTOM
          ) {
            strs[m] = ANALYTICS_DATE_RANGES.find(
              (r) => r.value == f[AnalyticsFilterType.DATE].range,
            )?.label.toLowerCase();
          } else {
            const hours = dayjs(dr.to).diff(dayjs(dr.from), 'hours');
            const days = Math.floor(hours / 24);
            strs[m] = `past ${days} days`;
          }
        } else if (m.toLowerCase() == 'dates.fromdate') {
          strs[m] = dayjs(dr.from).format('MMM D, YYYY');
        } else if (m.toLowerCase() == 'dates.todate') {
          strs[m] = dayjs(dr.to).format('MMM D, YYYY');
        } else if (m.toLowerCase() == 'buyers.count') {
          strs[m] = f[AnalyticsFilterType.BUYERS].length;
        } else if (m.toLowerCase() == 'reps.count') {
          strs[m] = f[AnalyticsFilterType.REPS].length;
        } else if (m.toLowerCase() == 'teams.count') {
          strs[m] = f[AnalyticsFilterType.TEAMS].length;
        } else if (
          m.toLowerCase() == 'tags.count' &&
          f[AnalyticsFilterType.TAGS]
        ) {
          strs[m] = f[AnalyticsFilterType.TAGS].length;
        } else if (
          m.toLowerCase() == 'calltype.count' &&
          f[AnalyticsFilterType.CALL_TYPES]
        ) {
          strs[m] = f[AnalyticsFilterType.CALL_TYPES].length;
        } else if (
          m.toLowerCase() == 'scorecards.count' &&
          f[AnalyticsFilterType.SCORECARDS]
        ) {
          strs[m] = f[AnalyticsFilterType.SCORECARDS].length;
        } else if (
          m.toLowerCase() == 'criterions.count' &&
          f[AnalyticsFilterType.SCORECARDS_CRITERIONS]
        ) {
          strs[m] = f[AnalyticsFilterType.SCORECARDS_CRITERIONS].length;
        } else if (
          m.toLowerCase() == 'criterions.list' &&
          f[AnalyticsFilterType.SCORECARDS_CRITERIONS]
        ) {
          strs[m] = f[AnalyticsFilterType.SCORECARDS_CRITERIONS].join(', ');
        } else if (m.toLowerCase().includes('data')) {
          if (data) {
            const dataPath = m.split('.').slice(1);
            if (dataPath[0]) {
              if (dataPath[0] == 'list') {
                //just concatentate all the values in data
                strs[m] = data.join(', ');
              } else {
                //array of objects or object
                const n = dataPath[0];
                if (Array.isArray(data)) {
                  let arr = data.map((d: any) => {
                    return d[n];
                  });
                  //remove duplicates:
                  arr = arr.filter((v, i, a) => a.indexOf(v) === i);
                  strs[m] = arr.join(', ');
                } else {
                  //object
                  strs[m] = data[n];
                }
              }
            }
          } else {
            strs[m] = '';
          }
        }
      }

      let res = '';

      for (const s of segments) {
        if (strs[s]) {
          res += strs[s];
        } else {
          res += s;
        }
      }
      return res;
    } else {
      return widget.description;
    }
  }

  export function getDatesRange(f: DateFilterType) {
    const res = { from: new Date(), to: new Date() };

    const today = dayjs();
    res.to = today.endOf('day').toDate();
    if (!f || !f.range) {
      //last 30 days default
      res.from = today.subtract(1, 'month').startOf('day').toDate();
    } else if (f.range == AnalyticsFilterDateRange.CUSTOM) {
      res.from = f.fromDate;
      res.to = f.toDate;
    } else if (f.range == AnalyticsFilterDateRange.ALL) {
      res.from = today.subtract(10, 'year').startOf('day').toDate();
    } else if (f.range == AnalyticsFilterDateRange.TODAY) {
      res.from = today.startOf('day').toDate();
    } else if (f.range == AnalyticsFilterDateRange.YESTERDAY) {
      res.to = today.subtract(1, 'day').endOf('day').toDate();
      res.from = today.subtract(1, 'day').startOf('day').toDate();
    } else if (f.range == AnalyticsFilterDateRange.THIS_WEEK) {
      res.from = today.startOf('week').startOf('day').toDate();
    } else if (f.range == AnalyticsFilterDateRange.LAST_WEEK) {
      res.to = today.startOf('week').startOf('day').toDate();
      res.from = today
        .startOf('week')
        .subtract(1, 'week')
        .startOf('day')
        .toDate();
    } else if (f.range == AnalyticsFilterDateRange.LAST_TWO_WEEKS) {
      res.from = today.subtract(2, 'week').startOf('day').toDate();
    } else if (f.range == AnalyticsFilterDateRange.LAST_NINETY_DAYS) {
      res.from = today.subtract(90, 'day').startOf('day').toDate();
    } else if (f.range == AnalyticsFilterDateRange.THIS_MONTH) {
      res.from = today.startOf('month').toDate();
    } else if (f.range == AnalyticsFilterDateRange.LAST_MONTH) {
      res.to = today.startOf('month').subtract(1, 'day').endOf('day').toDate();
      res.from = today
        .startOf('month')
        .subtract(1, 'month')
        .startOf('day')
        .toDate();
    } else if (f.range == AnalyticsFilterDateRange.LAST_SIX_MONTHS) {
      res.from = today.subtract(6, 'month').startOf('day').toDate();
    } else if (f.range == AnalyticsFilterDateRange.LAST_YEAR) {
      res.from = today.subtract(1, 'year').startOf('day').toDate();
    }
    return res;
  }

  /*********************************/
  /************** OLD **************/
  /*********************************/

  export async function getCallVolumeHistoryByRep(filters: AnalyticsFilters) {
    let res;

    try {
      res = await API.get('/analytics/call-volume-history-by-rep', {
        params: {
          fromDate: filters?.dateRange?.from,
          toDate: filters?.dateRange?.to,
          buyerIdsBlacklist: filters?.buyerIdsBlacklist,
          userIdsBlacklist: filters?.userIdsBlacklist,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCallDurationHistoryByRep(filters: AnalyticsFilters) {
    let res;

    try {
      res = await API.get('/analytics/call-duration-history-by-rep', {
        params: {
          fromDate: filters?.dateRange?.from,
          toDate: filters?.dateRange?.to,
          buyerIdsBlacklist: filters?.buyerIdsBlacklist,
          userIdsBlacklist: filters?.userIdsBlacklist,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCallDurationByRepAndBuyer(
    filters: AnalyticsFilters,
  ) {
    let res;

    try {
      res = await API.get('/analytics/call-duration-by-rep-and-buyer', {
        params: {
          fromDate: filters?.dateRange?.from,
          toDate: filters?.dateRange?.to,
          buyerIdsBlacklist: filters?.buyerIdsBlacklist,
          userIdsBlacklist: filters?.userIdsBlacklist,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  // SCORECARD METRICS
  export async function getCallQualityConversationsHistoryByRep(
    filters: AnalyticsFilters,
  ) {
    let res;

    try {
      res = await API.get(
        '/analytics/call-quality-conversations-history-by-rep',
        {
          params: {
            fromDate: filters?.dateRange?.from,
            toDate: filters?.dateRange?.to,
            buyerIdsBlacklist: filters?.buyerIdsBlacklist,
            userIdsBlacklist: filters?.userIdsBlacklist,
          },
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCallBookedMeetingsHistoryByRep(
    filters: AnalyticsFilters,
  ) {
    let res;

    try {
      res = await API.get('/analytics/call-booked-meetings-history-by-rep', {
        params: {
          fromDate: filters?.dateRange?.from,
          toDate: filters?.dateRange?.to,
          buyerIdsBlacklist: filters?.buyerIdsBlacklist,
          userIdsBlacklist: filters?.userIdsBlacklist,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCallBookedMeetingsAvgDurationHistoryByRep(
    filters: AnalyticsFilters,
  ) {
    let res;

    try {
      res = await API.get(
        '/analytics/call-booked-meetings-avg-duration-history-by-rep',
        {
          params: {
            fromDate: filters?.dateRange?.from,
            toDate: filters?.dateRange?.to,
            buyerIdsBlacklist: filters?.buyerIdsBlacklist,
            userIdsBlacklist: filters?.userIdsBlacklist,
          },
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getAvgScorecardSectionScoresByRep(
    sectionTitle: 'Opener' | 'Gating' | 'Closing',
    filters: AnalyticsFilters,
  ) {
    try {
      const res = await API.get(
        '/analytics/avg-scorecard-section-scores-by-rep',
        {
          params: {
            fromDate: filters?.dateRange?.from,
            toDate: filters?.dateRange?.to,
            buyerIdsBlacklist: filters?.buyerIdsBlacklist,
            userIdsBlacklist: filters?.userIdsBlacklist,
          },
        },
      );

      if (res?.data) {
        return res?.data?.[sectionTitle];
      }
    } catch (err) {
      console.log(err);
    }

    return undefined;
  }

  export async function getAvgScorecardStatsByRep(
    statTitle:
      | 'FillerWords'
      | 'LongestMonologue'
      | 'TalkListenRatio'
      | 'TalkSpeed',
    filters: AnalyticsFilters,
  ) {
    let res;

    try {
      res = await API.get('/analytics/avg-scorecard-stats-by-rep', {
        params: {
          fromDate: filters?.dateRange?.from,
          toDate: filters?.dateRange?.to,
          buyerIdsBlacklist: filters?.buyerIdsBlacklist,
          userIdsBlacklist: filters?.userIdsBlacklist,
        },
      });

      if (res?.data) {
        return res?.data?.[statTitle];
      }
    } catch (err) {
      console.log(err);
    }

    return undefined;
  }

  export async function getDashboardStats(filters: AnalyticsFilters) {
    let res;

    try {
      res = await API.get('/analytics/dashboard-stats', {
        params: {
          fromDate: filters?.dateRange?.from,
          toDate: filters?.dateRange?.to,
          buyerIdsBlacklist: filters?.buyerIdsBlacklist,
          userIdsBlacklist: filters?.userIdsBlacklist,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default AnalyticsService;
