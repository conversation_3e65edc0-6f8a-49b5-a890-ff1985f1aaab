import { API } from '../../Client';
import DashboardTemplate, {
  DashboardTemplateUserType,
  DashboardTemplateWidget,
} from './types';

namespace DashboardTemplatesService {
  export async function createDashboardTabForOrg(
    orgId: number,
    templateId: number,
    title: string,
    isForAdminOnly: boolean,
    showInHome: boolean,
    defaultForReps: boolean,
    applyToAllOrgs: boolean = false,
  ) {
    let res;

    try {
      res = await API.post(
        '/analytics/dashboard-templates/add-to-organization',
        {
          orgId,
          templateId,
          title,
          isForAdminOnly,
          showInHome,
          applyToAllOrgs,
          defaultForReps,
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getSharingStats(templateId: number): Promise<any> {
    let res;

    try {
      res = await API.get(
        `/analytics/dashboard-templates/sharing-stats/${templateId}`,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getTemplates(): Promise<DashboardTemplate[]> {
    let res;

    try {
      res = await API.get('/analytics/dashboard-templates');
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createNew(
    title: string,
    filters: string,
    isPersonal: boolean,
    isForAdminOnly: boolean,
    forUserType: DashboardTemplateUserType,
    addToNewOrg: boolean,
    showInHome: boolean,
    defaultForReps: boolean,
  ): Promise<DashboardTemplate> {
    let res;

    try {
      res = await API.post('/analytics/dashboard-templates', {
        title,
        filters,
        isPersonal,
        isForAdminOnly,
        forUserType,
        addToNewOrg,
        showInHome,
        defaultForReps,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function update(
    id: number,
    title: string,
    filters: string,
    isPersonal: boolean,
    isForAdminOnly: boolean,
    forUserType: DashboardTemplateUserType,
    addToNewOrg: boolean,
    showInHome: boolean,
    defaultForReps: boolean,
  ): Promise<DashboardTemplate> {
    let res;

    try {
      res = await API.patch('/analytics/dashboard-templates/' + id, {
        title,
        filters,
        isPersonal,
        isForAdminOnly,
        forUserType,
        addToNewOrg,
        showInHome,
        defaultForReps,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteTemplate(id: number): Promise<boolean> {
    let res;

    try {
      res = await API.delete('/analytics/dashboard-templates/' + id);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function addWidget(
    templateId: number,
    widgetTemplateId: number,
    name: string,
    description: string,
    props: any,
    appliedFilters: any,
  ): Promise<DashboardTemplateWidget> {
    let res;

    try {
      res = await API.post(
        `/analytics/dashboard-templates/widget/${templateId}`,
        { widgetTemplateId, name, description, props, appliedFilters },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateWidget(
    widgetId: number,
    name: string,
    description: string,
    props: any,
    appliedFilters: any,
  ): Promise<DashboardTemplateWidget> {
    let res;

    try {
      res = await API.patch(
        `/analytics/dashboard-templates/widget/${widgetId}`,
        { name, description, props, appliedFilters },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteWidget(widgetId: number): Promise<boolean> {
    let res;

    try {
      res = await API.delete(
        `/analytics/dashboard-templates/widget/${widgetId}`,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default DashboardTemplatesService;
