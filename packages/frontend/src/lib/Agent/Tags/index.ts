import { API } from '../../Client';
import {
  TagDto,
  CreateTagDto,
  EditTagsAgentsLinksDto,
  UpdateTagDto,
} from '../types';

namespace TagsService {
  export async function getAll(
    from: number = 0,
    numberOfResults: number = 10,
    search: string = '',
    orderBy: string = 'name',
    order: string = 'asc',
  ): Promise<TagDto[]> {
    let res;

    try {
      res = await API.get(`/agents/tags/`, {
        params: {
          from,
          numberOfResults,
          search,
          orderBy,
          order,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getByIds(ids: number[]): Promise<TagDto[]> {
    if (ids.length === 0) {
      return [];
    }

    let res;

    try {
      res = await API.get(`/agents/tags-by-ids/`, {
        params: {
          ids,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createTag(body: CreateTagDto): Promise<TagDto> {
    let res;

    try {
      res = await API.post(`/agents/tags/`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function editTagsAgentsLinks(
    body: EditTagsAgentsLinksDto,
  ): Promise<TagDto> {
    return (await API.post(`/agents/edit-tags-agents-links/`, body))?.data;
  }

  export async function updateTag(body: UpdateTagDto): Promise<TagDto> {
    let res;

    try {
      res = await API.patch(`/agents/tags/`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteTag(id: number): Promise<TagDto> {
    let res;

    try {
      res = await API.delete(`/agents/tags/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default TagsService;
