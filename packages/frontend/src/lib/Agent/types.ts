/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-duplicate-enum-values */
import { FieldValues, UseFormReturn } from 'react-hook-form';
import { UserDto } from '../User/types';
import { AIBotCreatorData } from '@/common/CreateBuyerForm/Main/AIBotCreatorForm';
import ScorecardConfigDto from '../ScorecardConfig/types';
import { Dispatch, SetStateAction } from 'react';
import { z } from 'zod';
import { ParsedTranscriptMessage } from '../Ai/types';

export enum AgentGender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
}

export enum AgentEmotionalState {
  NONE = 'none',
  RUDE = 'rude',
  NICE = 'nice',
  NICE_LESS_INQUISITIVE = 'nice_less_inquisitive',
  NICE_NO_HANGUP = 'nice_no_hangup',
  RUDE_FLIP = 'rude_flip',
  LESS_RUDE = 'less_rude',
  LESS_RUDE_LESS_INQUISITIVE = 'less_rude_less_inquisitive',
  DISCOVERY = 'discovery',
  SASSY = 'sassy',
  RUDE_NOT_INQUISITIVE = 'rude_not_inquisitive',
}

export enum AgentCallType {
  COLD = 'cold',
  DISCOVERY = 'discovery',
  WARM = 'warm',
  GATEKEEPER = 'gatekeeper',
  FOCUS = 'focus',
  CHECKIN = 'checkin',
  RENEWAL = 'renewal',
  DEMO = 'demo',
  CUSTOM = 'custom',
  NONE = 'None - Default',
  MANAGER_ONE_ON_ONE = 'managerOneOnOne',
}

export enum AgentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DRAFT = 'DRAFT',
}

export enum AutogeneratedMethod {
  LINKEDIN_PROFILE = 'LINKEDIN_PROFILE',
}

export enum AgentVoice {
  JACK_PLAYHT = 'jack-playht',
  WILL_PLAYHT = 'will-playht',
  CHRIS_PLAYHT = 'chris-playht',
  MATT_PLAYHT = 'matt-playht',
  DAVIS_PLAYHT = 'davis-playht',
  MICHAEL_PLAYHT = 'michael-playht',
  JENNIFER_PLAYHT = 'jennifer-playht',
  RUBY_PLAYHT = 'ruby-playht',
  DONNA_PLAYHT = 'donna-playht',
  MELISSA_PLAYHT = 'melissa-playht',
  HUDSON = 'Hudson (american)',
  SAMUEL = 'Samuel (american)',
  DYLAN = 'Dylan (british)',
  FURIO = 'Furio (italian)',
  ADOLFO = 'Adolfo (american)',
  ATLAS = 'Atlas (american)',
  JULIAN = 'Julian (british)',
  HUNTER = 'Hunter (british)',
  THEODORE = 'Theodore (american)',
  BRYAN = 'Bryan (american)',
  BILLY = 'Billy (american)',
  NOLAN = 'Nolan (british)',
  DARNELL = 'Darnell (american)',
  DAVIS = 'Davis (american)',
  JACK = 'Jack (american)',
  MASON = 'Mason (american)',
  RUSSELL = 'Russell (australian)',
  CHRIS = 'Chris (american)',
  FLYNN = 'Flynn (british)',
  CHUCK = 'Chuck (british)',
  GEORGE = 'George (british)',
  WILBERT = 'Wilbert (british)',
  DICK = 'Dick (american)',
  DONOVAN = 'Donovan (american)',
  HARRIS = 'Harris (british)',
  DANIEL = 'Daniel (canadian)',
  DUDLEY = 'Dudley (american)',
  RICHIE = 'Richie (american)',
  HOOK = 'Hook (american)',
  LEROY = 'Leroy (american)',
  ALFONSO = 'Alfonso (american)',
  ARCHIE = 'Archie (australian)',
  WAYLON = 'Waylon (american)',
  CARTER = 'Carter (american)',
  MICHAEL = 'Michael (american)',
  MATT = 'Matt (american)',
  ADRIAN = 'Adrian (american)',
  FREDERICK = 'Frederick (british)',
  CLARK = 'Clark (british)',
  SARGE = 'Sarge (american)',
  BENTON = 'Benton (american)',
  ARTHUR = 'Arthur (british)',
  CLIFTON = 'Clifton (american)',
  OWEN = 'Owen (american)',
  ERASMO = 'Erasmo (american)',
  FRANKIE = 'Frankie (british)',
  ADA = 'Ada (south)',
  DARRELL = 'Darrell (british)',
  NIGEL = 'Nigel (australian)',
  RANGER = 'Ranger (american)',
  AXEL = 'Axel (american)',
  EARLE = 'Earle (british)',
  LOGAN = 'Logan (british)',
  ALESSANDRO = 'Alessandro (italian)',
  JORDAN = 'Jordan (american)',
  ALEX = 'Alex (british)',
  BAPTISTE = 'Baptiste (french)',
  WILBUR = 'Wilbur (american)',
  MITCH = 'Mitch (australian)',
  TEDDY = 'Teddy (australian)',
  MARK = 'Mark (british)',
  CHARLES = 'Charles (american)',
  LACHLAN = 'Lachlan (australian)',
  JOSEPH = 'Joseph (american)',
  LANCE = 'Lance (british)',
  FINLEY = 'Finley (british)',
  WILL = 'Will (american)',
  OLIVER = 'Oliver (british)',
  CALVIN = 'Calvin (american)',
  ANTHONY = 'Anthony (american)',
  WILLIAM = 'William (american)',
  LARRY = 'Larry (american)',
  PHOEBE = 'Phoebe (british)',
  AMELIA = 'Amelia (british)',
  ELEANOR = 'Eleanor (british)',
  NAVYA = 'Navya (indian)',
  AUTUMN = 'Autumn (american)',
  SAMARA = 'Samara (american)',
  SIOBHÁN = 'Siobhán (irish)',
  SARAH = 'Sarah (british)',
  APRIL = 'April (british)',
  ABIGAIL = 'Abigail (american)',
  MICAH = 'Micah (british)',
  MELISSA = 'Melissa (american)',
  SCARLETT = 'Scarlett (british)',
  SUMITA = 'Sumita (indian)',
  INDIGO = 'Indigo (british)',
  MADISON = 'Madison (irish)',
  NOVA = 'Nova (american)',
  NICOLE = 'Nicole (american)',
  ISABELLA = 'Isabella (british)',
  ARIANA = 'Ariana (american)',
  SOPHIA = 'Sophia (american)',
  SUSAN = 'Susan (american)',
  PIA = 'Pia (australian)',
  EILEEN = 'Eileen (american)',
  AALIYAH = 'Aaliyah (american)',
  LUMI = 'Lumi (finnish)',
  SAMANTHA = 'Samantha (american)',
  AUDREY = 'Audrey (american)',
  ADELAIDE = 'Adelaide (australian)',
  CARMEN = 'Carmen (mexican)',
  NIAMH = 'Niamh (irish)',
  DELILAH = 'Delilah (american)',
  MADELYN = 'Madelyn (british)',
  LUNA = 'Luna (american)',
  EVELYN = 'Evelyn (american)',
  CHARLOTTE = 'Charlotte (canadian)',
  JENNIFER = 'Jennifer (american)',
  RUBY = 'Ruby (australian)',
  AURORA = 'Aurora (british)',
  JESSIE = 'Jessie',
  ARNOLD_11LABS = 'Arnold',
  GRACE_11LABS = 'Grace',
  BROOKE = 'Brooke',
  JESSIE_11LABS = 'Jessie',
  CHARLIE_11LABS = 'Charlie',
  LIAM_11LABS = 'Liam',
  GIOVANNI_11LABS = 'Giovanni',
  THOMAS_11LABS = 'Thomas',
  DOROTHY_11LABS = 'Dorothy',
  FREYA_11LABS = 'Freya',
  JESSICA_11LABS = 'Jessica',
  MIMI_11LABS = 'Mimi',
  RACHEL_11LABS = 'Rachel',
  MANDY_11LABS = 'Mandy',
  JASON_CLONED = 'Jason',
  BEN_11LABS_FRENCH_MALE = 'ben_french_male',
  NIKLAS_11LABS_CENTRAL_EUROPEAN_MALE = 'niklas_central_european_male',
  BOGDAN_11LABS_GERMAN_MALE = 'bogdan_german_male',
  SIMON_11LABS_POLISH_MALE = 'simon_polish_male',
  MIKE_11LABS_DUTCH_MALE = 'mike_dutch_male',
  LEO_11LABS_SPANISH_MALE = 'leo_spanish_male',
  KEN_11LABS_JAPANESE_MALE = 'ken_japanese_male',
  YU_11LABS_TAIWANESE_MALE = 'yu_taiwanese_male',
  SAKURA_11LABS_JAPANESE_FEMALE = 'sakura_japanese_female',
  CLAIRE_11LABS_FRENCH_FEMALE = 'claire_french_female',
  SANTIAGO_11LABS_SPANISH_MALE = 'santiago_spanish_male',
  RONNIE_11LABS_ASIAN_MALE = 'ronnie_asian_male',
  JAKE_11LABS_SINGAPOREAN_MALE = 'jake_singaporean_male',
  TATSUYA_11LABS_JAPANESE_MALE = 'tatsuya_japanese_male',
  VALERIA_11LABS_SPANISH_FEMALE = 'valeria_spanish_female',
  LEONIE_11LABS_GERMAN_FEMALE = 'leonie_german_female',
  NAINA_11LABS_INDIAN_FEMALE = 'naina_indian_female',
  MONIKA_11LABS_INDIAN_FEMALE = 'monika_indian_female',
  RAJU_11LABS_INDIAN_MALE = 'raju_indian_male',
  SID_11LABS_INDIAN_MALE = 'sid_indian_male',
  AMRUT_11LABS_INDIAN_MALE = 'amrut_indian_male',
  FINN_11LABS = 'finn_elevenlabs',
  WILL_11LABS = 'will(burt)_elevenlabs',
  KEVIN_11LABS = 'kevin_elevenlabs',
  JOE_11LABS = 'joe_elevenlabs',
  ANGELA_11LABS = 'angela_elevenlabs',
  CLARA_11LABS = 'clara_elevenlabs',
  JENNIFER_11LABS = 'jennifer_elevenlabs',
  CHELSEA_11LABS = 'chelsea_elevenlabs',

  // XIAOYI_AZURE = "Xiaoyi", // azure does not work in voice map
  // ANANYA_AZURE = "Ananya", // azure does not work in voice map
}

export interface AgentResearch {
  public_presence?: string;
  incumbent_solution_info?: string;
  problem_aware_info?: string;
  solution_aware_info?: string;
  pre_existing_champion_info?: string;
  cold_call_scenario?: string;
  warm_call_scenario?: string;
  warm_call_context?: string;
  discovery_call_context?: string;
  discovery_call_scenario?: string;
  urgency?: string;
  qualified_prospect?: string;
  gatekeeper_name?: string;
}

export interface CreateAgentDto {
  avatar?: string;
  avatarBase64?: string;
  firstName: string;
  lastName: string;
  gender: AgentGender;
  voice: AgentVoice;
  jobTitle: string;
  acronyms?: Record<string, string>;
  personalDetails: string[];
  openerLine: string;
  research?: string;
  callType: AgentCallType;
  emotionalState: AgentEmotionalState;
  status: AgentStatus;
  companyName: string;
  companyDetails: string[];
  companyOrgStructure: string[];
  goals?: string[];
  opinions: string[];
  objections: string[];
  description?: string;
  ownerDemoInboundFormResponseId?: number;
  orgLogo?: string;
  orgName?: string;
  orgAdmin?: string;
  scorecardConfigId?: number;
}

export class CreateFocusAgentDto {
  firstName: string = '';
  lastName: string = '';
  gender: AgentGender = AgentGender.FEMALE;
  avatar: string = '';
  avatarUrl?: string = '';
  avatarBase64?: string = '';
  jobTitle: string = '';
  companyName: string = '';
  voice: AgentVoice = AgentVoice.JENNIFER_PLAYHT;
  description: string = '';
  openerLine: string = '';
  scorecardConfigId: number = 0;
  language?: AgentLanguage = AgentLanguage.EN_US;
  tags?: TagDto[] = [];
  tagsIds?: number[] = [];
}

export interface CreatePublicAgentDto extends CreateAgentDto {
  affiliateName?: string;
  affiliateInviteCode?: string;
  affiliateEmail?: string;
}

export enum TagCondition {
  AND = 'and',
  OR = 'or',
}

export interface TagDto {
  id: number;
  createdAt: Date | string;
  updatedAt: Date | string;
  createdBy: number;
  updatedBy: number;
  name: string;
  description?: string;
  orgId: number;
  numberOfAgents?: number;
}

export enum AgentLanguage {
  EN_US = 'en-US',
  CS = 'cs',
  FR = 'fr',
  FR_CA = 'fr-CA',
  SV = 'sv',
  DA = 'da',
  ES = 'es',
  ES_LATAM = 'es-419',
  JA = 'ja',
  DE = 'de',
  KO = 'ko',
  GU = 'gu',
  HI = 'hi',
  ID = 'id',
  PT = 'pt',
  IT = 'it',
  HE = 'he-IL',
  TA = 'ta',
  MR = 'mr',
  NL = 'nl',
  NO = 'no',
  PL = 'pl',
  RO = 'ro',
  VI = 'vi',
  TH_TH = 'th-TH',
  ZH = 'zh',
  ZH_HK = 'zh-HK',
}

export enum AGENT_PROVIDER {
  ELEVEN_LABS = 'ELEVEN_LABS',
  VAPI = 'VAPI',
}

export const AgentLanguagesLabels: { [key: string]: string } = {};
AgentLanguagesLabels[`${AgentLanguage.EN_US}`] = 'English (US)';
AgentLanguagesLabels[`${AgentLanguage.ZH_HK}`] = 'Cantonese (HK)';
AgentLanguagesLabels[`${AgentLanguage.ZH}`] = 'Chinese (Mandarin)';
AgentLanguagesLabels[`${AgentLanguage.CS}`] = 'Czech';
AgentLanguagesLabels[`${AgentLanguage.DA}`] = 'Danish';
AgentLanguagesLabels[`${AgentLanguage.NL}`] = 'Dutch (NL)';
AgentLanguagesLabels[`${AgentLanguage.FR}`] = 'French';
AgentLanguagesLabels[`${AgentLanguage.FR_CA}`] = 'French (Canada)';
AgentLanguagesLabels[`${AgentLanguage.DE}`] = 'German';
AgentLanguagesLabels[`${AgentLanguage.GU}`] = 'Gujarati';
AgentLanguagesLabels[`${AgentLanguage.HE}`] = 'Hebrew';
AgentLanguagesLabels[`${AgentLanguage.HI}`] = 'Hindi';
AgentLanguagesLabels[`${AgentLanguage.ID}`] = 'Indonesian';
AgentLanguagesLabels[`${AgentLanguage.IT}`] = 'Italian';
AgentLanguagesLabels[`${AgentLanguage.KO}`] = 'Korean';
AgentLanguagesLabels[`${AgentLanguage.JA}`] = 'Japanese';
AgentLanguagesLabels[`${AgentLanguage.MR}`] = 'Marathi';
AgentLanguagesLabels[`${AgentLanguage.NO}`] = 'Norwegian Bokmal (Norway)';
AgentLanguagesLabels[`${AgentLanguage.PL}`] = 'Polish';
AgentLanguagesLabels[`${AgentLanguage.PT}`] = 'Portuguese';
AgentLanguagesLabels[`${AgentLanguage.RO}`] = 'Romanian';
AgentLanguagesLabels[`${AgentLanguage.ES}`] = 'Spanish (Spain)';
AgentLanguagesLabels[`${AgentLanguage.ES_LATAM}`] = 'Spanish (LATAM)';
AgentLanguagesLabels[`${AgentLanguage.SV}`] = 'Swedish';
AgentLanguagesLabels[`${AgentLanguage.TA}`] = 'Tamil';
AgentLanguagesLabels[`${AgentLanguage.TH_TH}`] = 'Thai';
AgentLanguagesLabels[`${AgentLanguage.VI}`] = 'Vietnamese';

export interface CompetitionAgentUserDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  numTriesLeft: number;
  isInfluencer: boolean;
  socialCardImageUrls?: string[];
  highestScoreCallId: number;
}

export interface CompetitionAgentDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    publicInfo: {
      rules: string;
      whatAreYouSelling: string;
      scoring: string;
    };
    iconUrl?: string;
  };
  user: CompetitionAgentUserDto;
}

export interface AgentDto {
  id: number;
  vapiId: string;
  avatar: string;
  avatarUrl?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  createdBy: number;
  updatedBy: number;
  ownerId: number;
  owner?: UserDto;
  orgId: number;
  firstName: string;
  lastName: string;
  gender: AgentGender;
  voice: AgentVoice;
  jobTitle: string;
  acronyms: Record<string, string>;
  personalDetails: string[];
  openerLine: string;
  research?: string;
  callType: AgentCallType;
  emotionalState: AgentEmotionalState;
  status: AgentStatus;
  companyName: string;
  companyDetails: string[];
  companyOrgStructure: string[];
  goals: string[];
  opinions: string[];
  objections: string[];
  description: string;
  callScenario?: string;
  ownerDemoInboundFormResponseId?: number;
  orgName?: string;
  orgLogo?: string;
  orgAdmin?: string;
  bookRate?: number;
  updateLeaderboard?: boolean;
  scorecardConfigId?: number;
  variationParentAgentId?: number;
  variationName?: string;
  autogenerated: boolean;
  autogeneratedMethod?: AutogeneratedMethod;
  autogeneratedInfo?: any;
  tags: TagDto[];
  language: AgentLanguage;
  originalAgentId: number;
  canOrgEditAgent: boolean;
  subAgents: AgentDto[];
  guardedAgents: AgentDto[];
  gatekeepers: AgentDto[];
  competitionAgent?: CompetitionAgentDto;
  isPublic: boolean;
  folders: AgentFolderDto[];
  supportingAgentInfo?: AgentDto[];
  aiGenerator?: AIBotCreatorData;
  providerAgentId?: string;
  scenarioName?: string;
  providerName?: string;
  teams: number[];
}

export interface GatekeeperTemplateDto {
  id: number;
  firstName: string;
  lastName: string;
  callScenario: string;
  context: string;
  scorecardConfigId?: number;
  orgId: number;
  shareWithAllOrgs: boolean;
}

export interface BulkShareAgentOrgDto {
  orgUid: string;
  canEditAgent: boolean;
}

export interface BulkToggleShareWithOrgDto {
  agentId: number;
  orgs: BulkShareAgentOrgDto[];
  updateIfAlreadyShared: boolean;
}

export interface BulkToggleShareAgentsWithOrgDto {
  agentIds: number[];
  orgs: BulkShareAgentOrgDto[];
  updateIfAlreadyShared: boolean;
}

export interface VariationDto {
  id: number;
  name: string;
  vapiId: string;
}

export interface PublicAgentDto {
  id: number;
  vapiId: string;
  avatar: string;
  avatarUrl?: string;
  firstName: string;
  lastName: string;
  jobTitle: string;
  companyName: string;
  description: string;
  orgLogo: string;
  orgName: string;
  orgAdmin: string;
  research?: string;
  callType: AgentCallType;
  openerLine: string;
  emotionalState: AgentEmotionalState;
  gender: AgentGender;
  status: AgentStatus;
  bookRate?: number;
  updateLeaderboard?: boolean;
  affiliateInviteCode?: string;
  language: AgentLanguage;
  gatekeepers: AgentDto[];
  canOrgEditAgent?: boolean;
  providerAgentId?: string;
  supportingAgentInfo?: AgentDto[];
  providerName?: string;
}

export type AnyAgentDto = AgentDto | PublicAgentDto;

export const checkIsPublicAgent = (dto: AnyAgentDto): dto is PublicAgentDto => {
  return !(dto as AgentDto)?.orgId;
};

export class UpdateAgentDto {
  id?: number;
  avatar?: string;
  avatarUrl?: string;
  avatarBase64?: string;
  firstName?: string;
  lastName?: string;
  gender?: AgentGender;
  voice?: string;
  jobTitle?: string;
  acronyms?: Record<string, string>;
  personalDetails?: string[];
  openerLine?: string;
  research?: string;
  callType?: AgentCallType;
  emotionalState?: AgentEmotionalState;
  status?: AgentStatus;
  companyName?: string;
  companyDetails?: string[];
  companyOrgStructure?: string[];
  goals?: string[];
  opinions?: string[];
  objections?: string[];
  description?: string;
  orgLogo?: string;
  orgName?: string;
  orgAdmin?: string;
  scorecardConfigId?: number;
  language?: AgentLanguage;
  tagsIds?: number[];
}

export interface CreateAIAgentDraftDto {
  jobTitle: string;
  buyerContext: string;
  sellerContext: string;
  callScenario: string;
}

export interface UpdateVariationNameDto {
  agentId: number;
  newVariationName: string;
}

export interface CreateTagDto {
  name: string;
  description?: string;
}

export interface UpdateTagDto {
  id: number;
  name: string;
  description?: string;
}

export interface EditTagsAgentsLinksDto {
  addTags: number[];
  removeTags: number[];
  forAgents: number[];
}

export type AgentFolderID = number;
export interface AgentFolderDto {
  id: AgentFolderID;
  userId: number;
  orgId: number;
  name: string;
  description: string;
  parentFolderId?: number;
  parentFolder?: AgentFolderDto;
  isPersonal: boolean;
  isForAdminOnly: boolean;
  agent?: AgentDto;
  children?: AgentFolderDto[];
  sort: number;
  teamId?: number;
}

export interface ConfigDetails {
  scenarioName?: string;
  folders?: AgentFolderDto[];
  tags?: TagDto[];
  gatekeepers?: GatekeeperTemplateDto | AgentDto;
}

export interface BasicDetails {
  description: string;
  warm_call_context?: string;
  discovery_call_context?: string;
  demo_call_context?: string;
  checkin_call_context?: string;
  manager_one_on_one_call_context?: string;
}

export interface PersonalDetails {
  firstName: string;
  lastName: string;
  gender: AgentGender;
  avatar: string;
  avatarUrl?: string;
  avatarBase64?: string;
  jobTitle: string;
  personalDetails: string[];
  voice: AgentVoice;
  language: AgentLanguage;
  companyName: string;
  emotionalState: AgentEmotionalState;
  openerLine?: string;
}

export interface CompanyDetails {
  companyDetails: string[];
  companyOrgStructure: string[];
}

export interface PrioritiesAndObjections {
  goals: string[];
  objections: string[];
}

export interface Opinions {
  callScenario: string;
  opinions: string[];
  incumbent_solution_info?: string;
  problem_aware_info?: string;
  solution_aware_info?: string;
  pre_existing_champion_info?: string;
}

export interface Scorecard {
  scorecardConfigId: string;
}

export interface AdvancedSettings {
  public_presence?: string;
  genBotDraftId?: number;
  keywords?: string[];
}

export interface StepOneData
  extends ConfigDetails,
    BasicDetails,
    PersonalDetails,
    CompanyDetails,
    PrioritiesAndObjections,
    Opinions,
    Scorecard,
    AdvancedSettings {}

export interface IcreateBuyerBotEditFormSubmitButtonState {
  disabled: boolean;
  loading: boolean;
}

export type BuyerBotAiGeneratorFormType = UseFormReturn<
  AIBotCreatorData,
  any,
  FieldValues
>;
export type BuyerBotFormType = UseFormReturn<StepOneData, any, FieldValues>;

export interface CreateBuyerFormState {
  aiGenerator?: BuyerBotAiGeneratorFormType;
  main?: BuyerBotFormType;
}

export interface CreateBuyerDefaultFormValues {
  aiGenerator?: AIBotCreatorData;
  main?: StepOneData;
}

export interface CreateBuyerBotEditFormValues {
  form: BuyerBotFormType;
  isEditMode: boolean | undefined;
  isConfigDetailsValid: boolean;
  isBasicDetailsValid: boolean;
  isPersonalDetailsValid: boolean;
  isCompanyDetailsValid: boolean;
  isPrioritiesAndObjectionsValid: boolean;
  isOpinionsValid: boolean;
  isScorecardValid: boolean;
  isScenarioNameValid: boolean;
  isCreatingScorecard: boolean;
  setIsCreatingScorecard: Dispatch<SetStateAction<boolean>>;
  isEditingScorecard: boolean;
  setIsEditingScorecard: Dispatch<SetStateAction<boolean>>;
  isAdvancedSettingsValid: boolean;
  isSubFieldValid: (schema: z.ZodSchema) => boolean;
  callType: AgentCallType;
  cloneBuyerId: string | null;
  baseRoute: string;
  queryString: string;
  filteredScorecardConfigOptions?: ScorecardConfigDto[];
  existingAgent?: AgentDto;
  resumeCalls: string[];
  currentMessages: ParsedTranscriptMessage[];
  setCurrentMessages: Dispatch<SetStateAction<ParsedTranscriptMessage[]>>;
  setResumeCalls: Dispatch<SetStateAction<string[]>>;
  createBuyerBotEditFormSubmitButtonState: IcreateBuyerBotEditFormSubmitButtonState;
  setCreateBuyerBotEditFormSubmitButtonState: Dispatch<
    SetStateAction<IcreateBuyerBotEditFormSubmitButtonState>
  >;
  currentTab: 'individual' | 'multi-party';
  setCurrentTab: Dispatch<SetStateAction<'individual' | 'multi-party'>>;
  supportingAgentInfo?: AgentDto[];
  setSupportingAgentInfo?: Dispatch<SetStateAction<AgentDto[]>>;
  folderForContent: AgentFolderDto[];
  setFolderForContent: Dispatch<SetStateAction<AgentFolderDto[]>>;
}

export type ResearchData = {
  public_presence: string;
  incumbent_solution_info: string;
  problem_aware_info: string;
  solution_aware_info: string;
  pre_existing_champion_info: string;
  cold_call_scenario: AgentCallType;
  warm_call_scenario: AgentCallType;
  warm_call_context: string;
  discovery_call_scenario: AgentCallType;
  discovery_call_context: string;
  demo_call_scenario: AgentCallType;
  demo_call_context: string;
  manager_one_on_one_call_context: string;
  manager_one_on_one_call_scenario: string;
  checkin_call_context: string;
  renewal_call_scenario: string;
  renewal_call_context: string;
  language: AgentLanguage;
  messages: string;
  keywords: string[];
  openerLine: string;
  urgency?: string;
  qualified_prospect?: string;
  gatekeeper_name?: string;
};
