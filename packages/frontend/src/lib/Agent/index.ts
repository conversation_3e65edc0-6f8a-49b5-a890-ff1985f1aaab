import _ from 'lodash';
import { API } from '../Client';
import {
  AgentCallType,
  AgentDto,
  AgentEmotionalState,
  AgentStatus,
  AnyAgentDto,
  CreateAIAgentDraftDto,
  CreateAgentDto,
  CreateFocusAgentDto,
  CreatePublicAgentDto,
  GatekeeperTemplateDto,
  PublicAgentDto,
  TagCondition,
  UpdateAgentDto,
  UpdateVariationNameDto,
  VariationDto,
} from './types';

import TagsService from './Tags';
import FoldersService from './Folders';
import AvatarService from './Avatars';

namespace AgentService {
  export async function toggleShareAgentWithOrg(
    agentId: number,
    orgUid: string,
    canEditAgent: boolean,
  ): Promise<boolean> {
    let res;

    try {
      res = await API.post(`/agents/toggle-share-with-org`, {
        agentId,
        orgUid,
        canEditAgent,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createAgent(body: CreateAgentDto): Promise<AgentDto> {
    let res;

    try {
      res = await API.post(`/agents`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createFocusAgent(
    body: CreateFocusAgentDto,
  ): Promise<AgentDto> {
    let res;

    try {
      res = await API.post(`/agents/focus`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createDemoAgent(
    body: CreatePublicAgentDto,
  ): Promise<PublicAgentDto> {
    let res;

    try {
      res = await API.post(`/agents/demo`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createDemoAIAgentDraft(
    body: CreateAIAgentDraftDto,
  ): Promise<AgentDto> {
    let res;

    try {
      res = await API.post(`/agents/demo/ai-creator`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateVariationName(
    body: UpdateVariationNameDto,
  ): Promise<boolean> {
    let res;

    try {
      res = await API.post(`/agents/updateVariationName`, {
        agentId: body.agentId,
        newVariationName: body.newVariationName,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateAgent(body: UpdateAgentDto): Promise<AgentDto> {
    let res;

    try {
      res = await API.patch(
        `/agents/${body.id}`,
        _.omit(body, 'id', 'ownerId', 'orgId', 'callType'),
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createAIAgentDraft(
    body: CreateAIAgentDraftDto,
  ): Promise<AgentDto> {
    let res;

    try {
      res = await API.post(`/agents/ai-creator`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createAgentFromBotPlannerJson(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    body: any,
  ): Promise<AgentDto> {
    let res;

    try {
      res = await API.post(`/agents/ai-creator-for-bot-planner`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
  

  export async function getDemoAgents(
    ownerDemoInboundResponseFormId?: number,
  ): Promise<AnyAgentDto[]> {
    let res;

    try {
      res = await API.get(`/agents/demo`, {
        params: {
          ownerDemoInboundResponseFormId,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getRemainingAgents(): Promise<AgentDto[]> {
    let res;

    try {
      res = await API.get(`/agents/remaining`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getDemoAgentByVapiId(
    vapiId: string,
    ownerDemoInboundResponseFormId?: number,
  ): Promise<AnyAgentDto> {
    let res;

    try {
      res = await API.get(`/agents/demo/vapiId/${vapiId}`, {
        params: {
          ownerDemoInboundResponseFormId,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getOrgAgents(
    callType?: AgentCallType,
    from = 0,
    numberOfResults = 0,
    search = '',
    agentStatus?: AgentStatus | undefined,
    includeVariationsBots = false,
  ): Promise<AgentDto[]> {
    let res;

    try {
      res = await API.get(`/agents`, {
        params: {
          callType,
          from,
          numberOfResults,
          search,
          agentStatus,
          includeVariationsBots,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getOrgAgentsByIds(ids: number[]): Promise<AgentDto[]> {
    let res;

    if (!ids) {
      ids = [];
    }
    try {
      res = await API.get(`/agents/agentsByIds?ids=${ids.join(',')}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getOrgAgentVariations(
    vapiId: string,
  ): Promise<AgentDto[]> {
    if (!vapiId) {
      return [];
    }
    let res;
    try {
      res = await API.get(`/agents/variations/${vapiId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getOrgVariations(
    from: number = 0,
    numberOfResults: number = 10,
    search: string = '',
  ): Promise<VariationDto[]> {
    let res;
    try {
      res = await API.get(`/agents/variations/`, {
        params: {
          from,
          numberOfResults,
          search,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getAgentByVapiId(
    vapiId: string,
    withFolders: boolean = false,
  ): Promise<AgentDto> {
    let res;

    try {
      res = await API.get(
        `/agents/vapiId/${vapiId}/?withFolders=${withFolders}`,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getAgentstatsByCallType(): Promise<any> {
    let res;

    try {
      res = await API.get(`/agents/stats-by-calltype`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function addGatekeeperToAgent(
    agentId: number,
    gatekeeperTemplateId: number,
    scorecardConfigId?: number,
  ): Promise<AgentDto> {
    let res;

    try {
      res = await API.post(`/agents/add-gatekeeper-to-agent/${agentId}`, {
        templateId: gatekeeperTemplateId,
        scorecardConfigId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteGatekeeperFromAgent(
    agentId: number,
  ): Promise<AgentDto> {
    let res;

    try {
      res = await API.post(`/agents/delete-gatekeeper-for-agent/${agentId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getRandomNames(
    nMaleNames: number,
    nFemaleNames: number,
  ): Promise<any> {
    let res;

    try {
      res = await API.get(`/agents/random-names/`, {
        params: {
          nMaleNames,
          nFemaleNames,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getVapiAuthKey(
    isForDemo = false,
    agentVapiId?: string,
  ): Promise<string> {
    let res;

    if (isForDemo) {
      try {
        res = await API.get(`/agents/demo/vapi-auth-token/`);
      } catch (err) {
        console.log(err);
      }
    } else {
      res = await API.get(`/agents/vapi-auth-token/${agentVapiId}`);
    }

    return res?.data;
  }

  export async function getOrgAgentsFiltered(
    search = '',
    variationsIds: number[] = [],
    tagsIds: number[] = [],
    tagCondition: TagCondition = TagCondition.OR,
    callType?: AgentCallType,
    emotionalState?: AgentEmotionalState,
  ): Promise<AgentDto[]> {
    let res;

    try {
      res = await API.get(`/agents/filtered`, {
        params: {
          search,
          variationsIds,
          tagsIds,
          tagCondition,
          ...(callType ? { callType } : {}),
          ...(emotionalState ? { emotionalState } : {}),
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getGatekeepersTemplates(): Promise<
    GatekeeperTemplateDto[]
  > {
    let res;

    try {
      res = await API.get(`/agents/gatekeepers/templates`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function toggleAgentPublic({
    id,
  }: {
    id: number;
  }): Promise<AgentDto> {
    let res;

    try {
      res = await API.patch(`/agents/toggle-public/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export { TagsService, FoldersService, AvatarService };
export default AgentService;
