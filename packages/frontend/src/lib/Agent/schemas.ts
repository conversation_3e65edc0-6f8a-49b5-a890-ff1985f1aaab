// Generated by ts-to-zod
import { z } from 'zod';
import {
  type <PERSON>Dto,
  type AgentFolderDto,
  type PublicAgentDto,
  type AnyAgentDto,
  AgentGender,
  AgentEmotionalState,
  AgentCallType,
  AgentStatus,
  AutogeneratedMethod,
  AgentVoice,
  TagCondition,
  AgentLanguage,
} from './types';

export const agentGenderSchema = z.nativeEnum(AgentGender);

export const agentEmotionalStateSchema = z.nativeEnum(AgentEmotionalState);

export const agentCallTypeSchema = z.nativeEnum(AgentCallType);

export const agentStatusSchema = z.nativeEnum(AgentStatus);

export const autogeneratedMethodSchema = z.nativeEnum(AutogeneratedMethod);

export const agentVoiceSchema = z.nativeEnum(AgentVoice);

export const agentResearchSchema = z.object({
  public_presence: z.string().optional(),
  incumbent_solution_info: z.string().optional(),
  problem_aware_info: z.string().optional(),
  solution_aware_info: z.string().optional(),
  pre_existing_champion_info: z.string().optional(),
  cold_call_scenario: z.string().optional(),
  warm_call_scenario: z.string().optional(),
  warm_call_context: z.string().optional(),
  discovery_call_context: z.string().optional(),
  discovery_call_scenario: z.string().optional(),
  urgency: z.string().optional(),
  qualified_prospect: z.string().optional(),
  gatekeeper_name: z.string().optional(),
});

export const createAgentDtoSchema = z.object({
  avatar: z.string().optional(),
  firstName: z.string(),
  lastName: z.string(),
  gender: agentGenderSchema,
  voice: agentVoiceSchema,
  jobTitle: z.string(),
  acronyms: z.record(z.string()).optional(),
  personalDetails: z.array(z.string()),
  openerLine: z.string(),
  research: z.string().optional(),
  callType: agentCallTypeSchema,
  emotionalState: agentEmotionalStateSchema,
  status: agentStatusSchema,
  companyName: z.string(),
  companyDetails: z.array(z.string()),
  companyOrgStructure: z.array(z.string()),
  goals: z.array(z.string()).optional(),
  opinions: z.array(z.string()),
  objections: z.array(z.string()),
  description: z.string().optional(),
  ownerDemoInboundFormResponseId: z.number().optional(),
  orgLogo: z.string().optional(),
  orgName: z.string().optional(),
  orgAdmin: z.string().optional(),
  scorecardConfigId: z.number().optional(),
});

export const createPublicAgentDtoSchema = createAgentDtoSchema.extend({
  affiliateName: z.string().optional(),
  affiliateInviteCode: z.string().optional(),
  affiliateEmail: z.string().optional(),
});

export const tagConditionSchema = z.nativeEnum(TagCondition);

export const tagDtoSchema = z.object({
  id: z.number(),
  createdAt: z.union([z.date(), z.string().transform((str) => new Date(str))]),
  updatedAt: z.union([z.date(), z.string().transform((str) => new Date(str))]),
  createdBy: z.number(),
  updatedBy: z.number(),
  name: z.string(),
  description: z.string().optional(),
  orgId: z.number(),
  numberOfAgents: z.number().optional(),
});

export const agentLanguageSchema = z.nativeEnum(AgentLanguage);

export const competitionAgentUserDtoSchema = z.object({
  id: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
  numTriesLeft: z.number(),
  isInfluencer: z.boolean(),
  socialCardImageUrls: z.array(z.string()).optional(),
  highestScoreCallId: z.number(),
});

export const competitionAgentDtoSchema = z.object({
  id: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
  metadata: z.object({
    publicInfo: z.object({
      rules: z.string(),
      whatAreYouSelling: z.string(),
      scoring: z.string(),
    }),
  }),
  user: competitionAgentUserDtoSchema,
});

export const gatekeeperTemplateDtoSchema = z.object({
  id: z.number(),
  firstName: z.string(),
  lastName: z.string(),
  callScenario: z.string(),
  context: z.string(),
  scorecardConfigId: z.number().optional(),
  orgId: z.number(),
  shareWithAllOrgs: z.boolean(),
});

export const variationDtoSchema = z.object({
  id: z.number(),
  name: z.string(),
  vapiId: z.string(),
});

export const createAIAgentDraftDtoSchema = z.object({
  jobTitle: z.string(),
  buyerContext: z.string(),
  sellerContext: z.string(),
  callScenario: z.string(),
});

export const updateVariationNameDtoSchema = z.object({
  agentId: z.number(),
  newVariationName: z.string(),
});

export const createTagDtoSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
});

export const updateTagDtoSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().optional(),
});

export const editTagsAgentsLinksDtoSchema = z.object({
  addTags: z.array(z.number()),
  removeTags: z.array(z.number()),
  forAgents: z.array(z.number()),
});

export const agentFolderIDSchema = z.number();

const userDtoSchema = z.any();

export const agentDtoSchema: z.ZodSchema<AgentDto> = z.lazy(() =>
  z.object({
    id: z.number(),
    vapiId: z.string(),
    avatar: z.string(),
    createdAt: z.date().or(z.string().transform((str) => new Date(str))),
    updatedAt: z.date().or(z.string().transform((str) => new Date(str))),
    createdBy: z.number(),
    updatedBy: z.number(),
    ownerId: z.number(),
    owner: userDtoSchema.optional(),
    orgId: z.number(),
    firstName: z.string(),
    lastName: z.string(),
    gender: agentGenderSchema,
    voice: agentVoiceSchema,
    jobTitle: z.string(),
    acronyms: z.record(z.string()),
    personalDetails: z.array(z.string()),
    openerLine: z.string(),
    research: z.string().optional(),
    callType: agentCallTypeSchema,
    emotionalState: agentEmotionalStateSchema,
    status: agentStatusSchema,
    companyName: z.string(),
    companyDetails: z.array(z.string()),
    companyOrgStructure: z.array(z.string()),
    goals: z.array(z.string()),
    opinions: z.array(z.string()),
    objections: z.array(z.string()),
    description: z.string(),
    ownerDemoInboundFormResponseId: z.number().optional(),
    orgName: z.string().optional(),
    orgLogo: z.string().optional(),
    orgAdmin: z.string().optional(),
    bookRate: z.number().optional(),
    updateLeaderboard: z.boolean().optional(),
    scorecardConfigId: z.number().optional(),
    variationParentAgentId: z.number().optional(),
    variationName: z.string().optional(),
    autogenerated: z.boolean(),
    autogeneratedMethod: autogeneratedMethodSchema.optional(),
    autogeneratedInfo: z.any().optional(),
    tags: z.array(tagDtoSchema),
    language: agentLanguageSchema,
    originalAgentId: z.number(),
    canOrgEditAgent: z.boolean(),
    subAgents: z.array(agentDtoSchema),
    guardedAgents: z.array(agentDtoSchema),
    gatekeepers: z.array(agentDtoSchema),
    competitionAgent: competitionAgentDtoSchema.optional(),
    isPublic: z.boolean(),
    folders: z.array(agentFolderDtoSchema),
  }),
);

export const agentFolderDtoSchema: z.ZodType<AgentFolderDto> = z.lazy(() =>
  z.object({
    id: agentFolderIDSchema,
    userId: z.number(),
    orgId: z.number(),
    name: z.string(),
    description: z.string(), // Allow null
    parentFolderId: z.number().optional(), // Allow null or undefined
    isPersonal: z.boolean(),
    isForAdminOnly: z.boolean(),
    agent: agentDtoSchema.optional(), // Use your existing AgentDto schema
    children: z.array(agentFolderDtoSchema).optional(), // Recursive optional children
    sort: z.number(),
    teamId: z.number().optional(), // Allow null or undefined
  }),
);

export const publicAgentDtoSchema: z.ZodSchema<PublicAgentDto> = z.lazy(() =>
  z.object({
    id: z.number(),
    vapiId: z.string(),
    avatar: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    jobTitle: z.string(),
    companyName: z.string(),
    description: z.string(),
    orgLogo: z.string(),
    orgName: z.string(),
    orgAdmin: z.string(),
    research: z.string().optional(),
    callType: agentCallTypeSchema,
    openerLine: z.string(),
    emotionalState: agentEmotionalStateSchema,
    gender: agentGenderSchema,
    status: agentStatusSchema,
    bookRate: z.number().optional(),
    updateLeaderboard: z.boolean().optional(),
    affiliateInviteCode: z.string().optional(),
    language: agentLanguageSchema,
    gatekeepers: z.array(agentDtoSchema),
  }),
);

export const anyAgentDtoSchema: z.ZodSchema<AnyAgentDto> = z.lazy(() =>
  z.union([agentDtoSchema, publicAgentDtoSchema]),
);
