import { API } from '../../Client';
import { AgentGender } from '../types';

namespace AvatarService {
  export async function getAvatars(
    gender?: AgentGender,
  ): Promise<Record<string, string>> {
    let res;

    try {
      res = await API.get(`/agents/resources/avatars`, {
        params: {
          gender,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}
export default AvatarService;
