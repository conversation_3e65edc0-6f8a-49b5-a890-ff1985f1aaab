import { API } from '../../Client';
import { AgentDto, AgentFolderDto } from '../types';

namespace FoldersService {
  export async function getAgentsWithNoFolder(
    start: number,
  ): Promise<AgentDto[]> {
    let res;

    try {
      res = await API.get(`/agents/agents-with-no-folder/`, {
        params: {
          start,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getAll(teamId?: number): Promise<AgentFolderDto[]> {
    let res;

    try {
      res = await API.get(`/agents/agents-folders/${teamId ? teamId : ''}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getFavourites(
    teamId?: number,
  ): Promise<AgentFolderDto[]> {
    let res;

    try {
      res = await API.get(
        `/agents/agents-folders-favourites/${teamId ? teamId : ''}`,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createFolder(
    name: string,
    description: string,
    parentFolderId: number | undefined,
    position: number,
    isPersonal: boolean,
    isForAdminOnly: boolean,
    teamId?: number,
  ): Promise<AgentFolderDto> {
    let res;

    try {
      res = await API.post(`/agents/create-agent-folder/`, {
        name,
        description,
        parentFolderId,
        position,
        isPersonal,
        isForAdminOnly,
        teamId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateFolder(
    folderId: number,
    name: string,
    description: string,
    parentFolderId: number | undefined,
    position: number,
    isPersonal: boolean,
    isForAdminOnly: boolean,
  ): Promise<AgentFolderDto> {
    let res;

    try {
      res = await API.patch(`/agents/update-agent-folder/`, {
        folderId,
        name,
        description,
        parentFolderId,
        position,
        isPersonal,
        isForAdminOnly,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteFolder(
    folderId: number,
  ): Promise<AgentFolderDto> {
    let res;

    try {
      res = await API.delete(`/agents/agent-folder/${folderId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function addFolderToFav(
    folderId: number,
  ): Promise<AgentFolderDto> {
    let res;

    try {
      res = await API.post(`/agents/add-agent-folder-to-fav/`, { folderId });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function removeFolderFromFav(
    folderId: number,
  ): Promise<AgentFolderDto> {
    let res;

    try {
      res = await API.post(`/agents/remove-agent-folder-from-fav/`, {
        folderId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function linkAgentToFolder(
    agentId: number,
    parentFolderId: number,
    sort: number,
    teamId?: number,
  ): Promise<AgentFolderDto> {
    let res;

    try {
      res = await API.post(`/agents/link-agent-to-folder/`, {
        agentId,
        parentFolderId,
        sort,
        teamId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getFoldersByAgent(agentId: number): Promise<number[]> {
    let res;

    try {
      res = await API.get(`/agents/folders-by-agent/`, {
        params: {
          agentId,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default FoldersService;
