import { AgentCallType, AgentDto } from '../Agent/types';

export type Criterion = {
  type: string;
  group: string;
  query: string;
  corpus: string;
  criterion: string;
  explanation: string;
};

export type ScorecardConfigInnerConfig = {
  [sectionTitle: string]: Criterion[];
};

export type ScorecardConfigCallType = {
  callType: AgentCallType;
  isDefaultForCallType: boolean;
};

export enum ScorecardConfigStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
}

export type ScorecardConfigAgentsQuery = {
  page?: number;
  pageSize?: number;
  search?: string;
};

export type ScorecardConfigAgentsDto = {
  agents: AgentDto[];
  page: number;
  pageSize: number;
  totalCount: number;
};

export default interface ScorecardConfigDto {
  id?: number;
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: number;
  updatedBy?: number;
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  config: any;
  sectionsOrder?: string;
  competitionConfig?: {
    aggregateScoreWeights: {
      statWeights: {
        talkSpeed: number;
        fillerWords: number;
        talkListenRatio: number;
        longestMonologue: number;
      };
      sectionCriteriaWeights: {
        [sectionTitle: string]: number;
      };
    };
  };
  orgId: number;
  tag: string;
  defaultType: string;
  editable: boolean;
  callTypes?: ScorecardConfigCallType[];
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  stats: any;
  status: ScorecardConfigStatus;
  publishedId?: number;
  drafts?: ScorecardConfigDto[];
  archivable?: boolean;
  agents?: AgentDto[];
}

export interface ShareHistoryRecord {
  id: number;
  createdAt: Date;
  createdBy: number;
  orgId: number;
  sharedWithOrgId: number;
  scorecardConfigId: number;
}

export enum ScorecardConfigMaterialType {
  LINK = 'LINK',
  FILE = 'FILE',
}

export enum ScorecardConfigMaterialScope {
  SCORECARD = 'SCORECARD',
  CRITERION = 'CRITERION'
}

export interface LearningMaterialDto {
  id?: number;
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: number;
  updatedBy?: number;
  orgId?: number;
  scorecardConfigId: number;
  title: string;
  description?: string;
  scope: ScorecardConfigMaterialScope;
  type: ScorecardConfigMaterialType;
  link: string;
  metadata?: LearningMaterialMetadata;
  objectKey?: string;
}

export interface LearningMaterialMetadata {
  fileType: string;
  fileName: string;
  fileSize: number;
}
