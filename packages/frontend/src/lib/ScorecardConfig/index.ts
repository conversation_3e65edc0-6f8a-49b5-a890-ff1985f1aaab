import { API } from '../Client';
import ScorecardConfigDto, {
  LearningMaterialDto,
  ScorecardConfigAgentsDto,
  ScorecardConfigAgentsQuery,
  ScorecardConfigCallType,
  ScorecardConfigMaterialScope,
  ShareHistoryRecord,
} from './types';

namespace ScorecardConfigService {
  export function getSortedKeys(sc: Partial<ScorecardConfigDto>) {
    if (!sc || !sc.config) {
      return [];
    }
    const scKeys = Object.keys(sc.config);
    const sectionsOrder = sc.sectionsOrder ? JSON.parse(sc.sectionsOrder) : [];
    return scKeys
      .map((k, idx) => ({ k, ogIdx: idx }))
      .sort((a, b) => {
        if (
          sectionsOrder.indexOf(a.k) !== -1 &&
          sectionsOrder.indexOf(b.k) !== -1
        ) {
          return sectionsOrder.indexOf(a.k) - sectionsOrder.indexOf(b.k);
        }
        if (sectionsOrder.indexOf(a.k) !== -1) {
          return -1;
        }
        if (sectionsOrder.indexOf(b.k) !== -1) {
          return 1;
        }
        return a.ogIdx - b.ogIdx;
      })
      .map((k) => k.k);
  }

  export async function getScorecardConfigById(
    scorecardConfigId: number,
  ): Promise<ScorecardConfigDto | undefined> {
    let res;
    try {
      res = await API.get(`/scorecard-configs/${scorecardConfigId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getAllScorecardConfigsNameHash(): Promise<string> {
    try {
      const res = await API.get<string>(`/scorecard-configs/name-hash`);
      return res.data;
    } catch (err) {
      console.log(err);
    }

    return '';
  }

  export async function getAllScorecardConfigsForOrg(
    includeDrafts = false,
    includeAgents = false,
    teams?: number[],
  ): Promise<ScorecardConfigDto[]> {
    let res;
    try {
      res = await API.get(`/scorecard-configs/all`, {
        params: {
          includeDrafts,
          includeAgents,
          ...(teams && teams.length > 0 ? { teams } : {}),
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getScorecardConfigAgents(
    scorecardConfigId: number,
    query?: ScorecardConfigAgentsQuery,
  ): Promise<ScorecardConfigAgentsDto> {
    let res;
    try {
      res = await API.get(`/scorecard-configs/${scorecardConfigId}/agents`, {
        params: query,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getSections(
    scorecards?: number[],
    from = 0,
    numberOfResults = 10,
    search = '',
  ): Promise<string[]> {
    let res;
    try {
      res = await API.post(`/scorecard-configs/sections`, {
        params: {
          scorecards,
          from,
          numberOfResults,
          search,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCriterions(
    sections?: string[],
    from = 0,
    numberOfResults = 10,
    search = '',
  ): Promise<string[]> {
    let res;
    try {
      res = await API.post(`/scorecard-configs/criterions`, {
        params: {
          sections,
          from,
          numberOfResults,
          search,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function CreateNew(
    config: unknown,
    tag: string,
    editable: boolean,
    stats: unknown,
    callTypes?: ScorecardConfigCallType[],
  ): Promise<ScorecardConfigDto> {
    const res = await API.post(`/scorecard-configs`, {
      config,
      tag,
      editable,
      stats,
      callTypes,
    });

    return res?.data;
  }

  export async function CloneScorecardConfig(
    scorecardId: number,
  ): Promise<ScorecardConfigDto> {
    const res = await API.post(`/scorecard-configs/clone/${scorecardId}`);
    return res?.data;
  }

  export async function ArchiveScorecardConfigs(
    scorecardIds: number[],
  ): Promise<ScorecardConfigDto> {
    const res = await API.delete(`/scorecard-configs`, {
      data: { ids: scorecardIds },
    });
    return res?.data;
  }

  export async function Update(
    id: number,
    config: unknown,
    tag: string,
    stats: unknown,
    callTypes?: ScorecardConfigCallType[],
    publish = false,
  ): Promise<ScorecardConfigDto> {
    const res = await API.patch(`/scorecard-configs/${id}`, {
      config,
      tag,
      stats,
      callTypes,
      publish,
    });

    return res?.data;
  }

  export async function GenerateConfig(details: string): Promise<unknown> {
    const res = await API.post(`/scorecard-configs/generate-config`, {
      details,
    });

    return res?.data;
  }

  export async function GetShareHistory(scorecardId: number | undefined) {
    if (scorecardId) {
      const res = await API.get<ShareHistoryRecord[]>(
        `/scorecard-configs/share-history/${scorecardId}`,
      );
      return res?.data;
    } else {
      return [];
    }
  }

  export async function ShareWithOrg(scorecardId: number, orgId: string) {
    await API.post(`/scorecard-configs/share/${orgId}/${scorecardId}`);
  }

  export async function getLearningMaterials(
    scorecardConfigId: number,
  ): Promise<LearningMaterialDto[]> {
    if (!scorecardConfigId) {
      return [];
    }
    const res = await API.get(
      `/scorecard-configs/learning-materials/${scorecardConfigId}`,
    );
    return res?.data;
  }

  export async function upsertLearningMaterial(
    scorecardConfigId: number,
    title: string,
    description: string,
    type: string,
    link: string,
    metadata: string,
    scope?: ScorecardConfigMaterialScope,
    id?: number,
  ): Promise<LearningMaterialDto> {
    const res = await API.post(`/scorecard-configs/learning-materials`, {
      scorecardConfigId,
      title,
      description,
      type,
      link,
      metadata,
      scope,
      id,
    });
    return res?.data;
  }

  export async function uploadLearningMaterialFile(
    learningMaterialId: number,
    file: File,
  ): Promise<LearningMaterialDto> {
    const formData = new FormData();
    formData.append('file', file as Blob);

    const res = await API.post(
      `/scorecard-configs/upload-learning-material-file/${learningMaterialId}`,
      formData,
    );
    return res?.data;
  }

  export async function deleteLearningMaterial(
    learningMaterialId: number,
  ): Promise<LearningMaterialDto> {
    const res = await API.delete(
      `/scorecard-configs/learning-material/${learningMaterialId}`,
    );
    return res?.data;
  }
}

export default ScorecardConfigService;
