import { API } from '../Client';
import { DemoInboundFormResponseDto } from '../Demo/types';
import Improvement, {
  GenBotDraftDto,
  UserDto,
  UserOrgDto,
  UsersResultDto,
  UserVerifyTokenRes,
} from './types';

namespace UserService {
  export async function logAction(action: string) {
    try {
      await API.post('/users/log-action', {
        action,
      });
    } catch (err) {
      console.log(err);
    }
  }

  export async function verifyToken(token: string, orgUid: string) {
    const response = await API.get<UserVerifyTokenRes>('/users/verify-token', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        orgId: orgUid,
      },
    });
    return response.data;
  }

  export async function getUserOrgs(): Promise<UserOrgDto[]> {
    let res;
    try {
      res = await API.get('/users/me/orgs');
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }

  export async function getOrgUsers(
    from: number = 0,
    numberOfResults: number = 0,
    search: string = '',
    includePending = false,
    orderBy?: Record<string, 'asc' | 'desc'>,
    teams?: number[],
    roles?: string[],
    status?: string[],
  ): Promise<UsersResultDto> {
    let res;

    try {
      res = await API.get(`/users`, {
        params: {
          from,
          numberOfResults,
          search,
          includePending,
          orderBy,
          teams,
          roles,
          status,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getOrgUsersByIds(ids: number[]): Promise<UserDto[]> {
    let res;

    if (!ids || ids.length === 0) {
      ids = [];
    }

    try {
      res = await API.get(`/users/usersByIds?ids=${ids.join(',')}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getOrgUserById(id: number): Promise<UserDto> {
    let res;

    try {
      res = await API.get(`/users/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getOrgCurrentUser(): Promise<UserDto> {
    let res;

    try {
      res = await API.get(`/users/me`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getDemoUsers(
    demoInboundFormResponseId: number,
  ): Promise<DemoInboundFormResponseDto[]> {
    let res;

    try {
      res = await API.get('/users/demo', {
        params: {
          demoInboundFormResponseId,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getGenerateAgentDrafts(
    from: number,
    numberOfResults: number,
  ): Promise<GenBotDraftDto[]> {
    let res;

    try {
      res = await API.get('/users/gen-bot-drafts', {
        params: {
          from,
          numberOfResults,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getGenBotDraftByProviderId(
    providerId: string,
  ): Promise<GenBotDraftDto> {
    let res;

    try {
      res = await API.get(`/users/gen-bot-draft/${providerId}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function upsertGenerateAgentDrafts(
    body: GenBotDraftDto,
  ): Promise<GenBotDraftDto> {
    let res;

    try {
      res = await API.patch('/users/gen-bot-draft', body);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function markGenerateAgentDraftsAsUsed(
    draftId: number,
    agentId: number,
  ): Promise<void> {
    let res;

    try {
      res = await API.patch('/users/gen-bot-draft', {
        id: draftId,
        generatedAgentId: agentId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getUserImprovements(): Promise<Improvement[]> {
    let res;

    try {
      res = await API.get('/users/stats/improvements');
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }
}

export default UserService;
