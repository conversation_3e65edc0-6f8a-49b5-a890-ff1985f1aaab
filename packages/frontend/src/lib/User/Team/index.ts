import { API } from '../../Client';
import {
  CreateTeamDto,
  EditTeamsUsersLinksDto,
  TeamDto,
  UpdateTeamDto,
} from '../types';

namespace TeamsService {
  export async function get(teamId: number): Promise<TeamDto> {
    if (!teamId) return {} as TeamDto;
    let res;

    try {
      res = await API.get(`/users/teams/${teamId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getAll(
    from: number = 0,
    numberOfResults: number = 10,
    search: string = '',
    loadMembers = false,
  ): Promise<TeamDto[]> {
    let res;

    try {
      res = await API.get(`/users/teams/`, {
        params: {
          from,
          numberOfResults,
          search,
          loadMembers,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getAllForUser(
    from: number = 0,
    numberOfResults: number = 10,
    search: string = '',
  ): Promise<TeamDto[]> {
    let res;

    try {
      res = await API.get(`/users/teams/mine`, {
        params: {
          from,
          numberOfResults,
          search,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getByIds(ids: number[]): Promise<TeamDto[]> {
    if (ids.length === 0) {
      return [];
    }

    let res;

    try {
      res = await API.get(`/users/teams/by-ids/`, {
        params: {
          ids,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function createTeam(body: CreateTeamDto): Promise<TeamDto> {
    let res;

    try {
      res = await API.post(`/users/teams/`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateTeam(body: UpdateTeamDto): Promise<TeamDto> {
    let res;

    try {
      res = await API.patch(`/users/teams/`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteTeam(id: number): Promise<TeamDto> {
    let res;

    try {
      res = await API.delete(`/users/teams/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function addUserToTeam(
    body: EditTeamsUsersLinksDto,
  ): Promise<TeamDto> {
    let res;

    try {
      res = await API.post(`/users/teams/add-user-to-team`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function removeUserFromTeam(
    body: EditTeamsUsersLinksDto,
  ): Promise<TeamDto> {
    let res;

    try {
      res = await API.post(`/users/teams/remove-user-from-team`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function changeUserRoleForTeam(
    body: EditTeamsUsersLinksDto,
  ): Promise<TeamDto> {
    let res;

    try {
      res = await API.post(`/users/teams/changet-user-role`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default TeamsService;
