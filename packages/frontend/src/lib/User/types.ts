import { OrgMemberInfo, User } from '@propelauth/react';
import { AgentDto } from '../Agent/types';
import {
  IntegrationTeamSettings,
  IntegrationUserSettings,
} from '../Integrations/types';
import {
  RealCallTeamConfig,
  RealCallUserConfig,
} from '../Integrations/RealCalls/types';

export enum RoleEnum {
  OWNER = 'Owner',
  ADMIN = 'Admin',
  OBSERVER = 'Observer',
  TEMP = 'Temp',
  CUSTOM = 'Custom',
  MEMBER = 'Member',
  MEMBER_PLUS = 'MemberPlus', //a member that can edit bots
}

export enum UserStatus {
  INVITED = 'INVITED',
  ACTIVE = 'ACTIVE',
  DEPROVISIONED = 'DEPROVISIONED',
}
export interface UserDto {
  avatar: string;
  uid: string;
  createdBy: number;
  updatedBy: number;
  orgId: number;
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: Date;
  updatedAt: Date;
  role: RoleEnum;
  teams: TeamDto[];
  addAsSubOrgAdmin: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  metadata: any;
  status: UserStatus;
  integrationsSettings?: IntegrationUserSettings[];
  realCallConfig?: RealCallUserConfig;  
  lastActive: Date;
}

export interface UsersResultDto {
  data: UserDto[];
  totalCount: number;
  numberOfResults: number;
}

export interface UserTeamDto {
  avatar: string;
  uid: string;
  createdBy: number;
  updatedBy: number;
  orgId: number;
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: Date;
  updatedAt: Date;
  role: RoleEnum;
  teams: TeamDto[];
  teamRole: TeamRoleEnum;
  addAsSubOrgAdmin: boolean;
  metadata: any;
}

export interface TeamDto {
  id: number;
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: number;
  updatedBy?: number;
  name: string;
  description?: string;
  orgId?: number;
  org?: any;
  numberOfUsers?: number;
  users?: UserTeamDto[];
  role?: TeamRoleEnum;
  integrationsSettings?: IntegrationTeamSettings[];
  realCallConfig?: RealCallTeamConfig;
}

export enum TeamRoleEnum {
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
}

export interface CreateTeamDto {
  name: string;
  description?: string;
  users?: UserTeamDto[];
}

export interface UpdateTeamDto {
  id: number;
  name: string;
  description?: string;
  users?: UserTeamDto[];
}

export interface EditTeamsUsersLinksDto {
  teamId: number;
  userId: number;
  role?: string;
}

export enum GenBotDraftDtoStatusEnum {
  LATEST = 'LATEST',
  USED = 'USED',
}

export interface GenBotDraftDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  draft: any;
  status: GenBotDraftDtoStatusEnum;
  agent?: AgentDto;
}

export interface ImprovementCall {
  vapiId: string;
  createdAt: Date;
  durationSeconds: number;
  callerAvatar: string;
  callerFullName: string;
}

export interface ImprovementCalls {
  underPerformedCalls: ImprovementCall[];
  callsToListenTo: ImprovementCall[];
}

export default interface Improvement {
  criterion: string;
  message: string;
  score: number;
  calls: ImprovementCalls;
}

export type UserVerifyTokenRes = {
  accessToken: string;
  accessTokensPerOrg: { [orgId: string]: string };
  userInfo: User & {
    orgIdToOrgMemberInfo: {
      [orgId: string]: OrgMemberInfo;
    };
  };
};

export interface UserOrgDto {
  orgId: number;
  orgUid: string;
  orgName: string;
  status: UserStatus;
}
