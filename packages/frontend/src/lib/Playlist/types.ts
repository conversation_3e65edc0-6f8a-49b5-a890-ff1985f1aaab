import { UserDto } from '../User/types';

export interface PlaylistCallDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  playlistId: number;
  callId: number;
}

export interface PlaylistDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  name: string;
  shared: boolean;
  ownerId?: number;
  orgId: number;
  calls: PlaylistCallDto[];
  owner: UserDto;
}

export interface UpdatePlaylistDto {
  name?: string;
  description?: string;
  shared?: boolean;
}
