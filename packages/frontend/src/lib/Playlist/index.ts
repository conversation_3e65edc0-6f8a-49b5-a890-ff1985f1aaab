import { API } from '../Client';
import { PlaylistDto } from './types';

namespace PlaylistService {
  export async function createPlaylist({
    name,
    description,
    shared = false,
  }: {
    name: string;
    description?: string;
    shared?: boolean;
  }): Promise<PlaylistDto> {
    let res;
    try {
      res = await API.post(`/playlists`, {
        name,
        shared,
        description,
      });
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function getPlaylists(): Promise<PlaylistDto[]> {
    let res;
    try {
      res = await API.get(`/playlists`);
    } catch (err) {
      console.log(err);
    }
    return res?.data || [];
  }

  export async function getPlaylistById(id: number): Promise<PlaylistDto> {
    let res;
    try {
      res = await API.get(`/playlists/${id}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function updatePlaylistById({
    id,
    name,
    description,
    shared,
  }: {
    id: number;
    name?: string;
    description?: string;
    shared?: boolean;
  }): Promise<PlaylistDto> {
    let res;
    try {
      res = await API.patch(`/playlists/${id}`, {
        name,
        shared,
        description,
      });
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function deletePlaylistById({
    id,
  }: {
    id: number;
  }): Promise<PlaylistDto> {
    let res;
    try {
      res = await API.delete(`/playlists/${id}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function addCallToPlaylist({
    id,
    callId,
  }: {
    id: number;
    callId: number;
  }): Promise<PlaylistDto> {
    let res;
    try {
      res = await API.post(`/playlists/${id}/calls/${callId}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function removeCallFromPlaylist({
    id,
    callId,
  }: {
    id: number;
    callId: number;
  }): Promise<PlaylistDto> {
    let res;
    try {
      res = await API.delete(`/playlists/${id}/calls/${callId}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }
}

export default PlaylistService;
