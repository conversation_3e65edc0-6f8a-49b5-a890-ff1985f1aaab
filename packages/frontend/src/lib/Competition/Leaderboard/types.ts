import { CallAggregatedScorecardDto } from '@/lib/Call/types';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';

export interface CompetitionDetailsDto {
  id: number;
  name: string;
  endsAt: Date;
}

export interface CompetitionLeaderboardItem {
  userId: number;
  firstName: string;
  lastName: string;
  linkedInUrl: string;
  isInfluencer: boolean;
  rank: number;
  avatar: string;
  highestScoreCallDetails: {
    id: number;
    vapiId: string;
    aggregatedScorecardDto: CallAggregatedScorecardDto;
  };
}

export interface CompetitionLeaderboardDto {
  agent: {
    id: number;
    firstName: string;
    lastName: string;
    vapiId: string;
    iconUrl?: string;
  };
  scorecardConfig: ScorecardConfigDto;
  items: CompetitionLeaderboardItem[];
}

export interface CompetitionOverallLeaderboardItem {
  userId: number;
  firstName: string;
  lastName: string;
  linkedInUrl: string;
  isInfluencer: boolean;
  rank: number;
  avatar: string;
  averageAggregateScore: number;
  highestScoreCallDetailsMap: {
    [agentId: number]: {
      id: number;
      vapiId: string;
      aggregatedScorecardDto: CallAggregatedScorecardDto;
    };
  };
}

export interface CompetitionOverallLeaderboardDto {
  agents: {
    id: number;
    firstName: string;
    lastName: string;
    vapiId: string;
    iconUrl?: string;
    scorecardConfig: ScorecardConfigDto;
  }[];

  items: CompetitionOverallLeaderboardItem[];
}
