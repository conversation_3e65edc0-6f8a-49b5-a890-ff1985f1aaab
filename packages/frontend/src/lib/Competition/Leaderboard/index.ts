import { API } from '@/lib/Client';
import { CompetitionDetailsDto, CompetitionLeaderboardDto } from './types';

namespace CompetitionLeaderboardService {
  export async function getCompetitionDetails(competitionTag: string) {
    try {
      const res = await API.get<CompetitionDetailsDto>(
        `/competitions/${competitionTag}/details`,
      );
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function getCompetitionLeaderboards(competitionTag: string) {
    try {
      const res = await API.get<CompetitionLeaderboardDto[]>(
        `/competitions/${competitionTag}/leaderboard`,
      );
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function updateCompetitionUserMetadata({
    sizeOfSalesTeam,
    attribution,
    linkedinUrl,
  }: {
    sizeOfSalesTeam?: string;
    attribution?: string;
    linkedinUrl: string;
  }) {
    try {
      const res = await API.patch(`/competitions/update-user-metadata`, {
        sizeOfSalesTeam,
        attribution,
        linkedinUrl,
      });
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }
}

export default CompetitionLeaderboardService;
