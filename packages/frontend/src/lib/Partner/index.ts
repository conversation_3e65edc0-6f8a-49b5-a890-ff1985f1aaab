import { API } from '../Client';
import { CreatePartnerSignupDto, PartnerDto } from './types';

namespace PartnerService {
  export async function getPartnerByPartnerId(
    partnerId: string,
  ): Promise<PartnerDto> {
    let res;
    try {
      res = await API.get(`/partners/${partnerId}`);
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }

  export async function createPartnerSignup({
    partnerId,
    email,
  }: CreatePartnerSignupDto): Promise<boolean> {
    let res;
    try {
      res = await API.post('/partners/signup', {
        partnerId,
        email,
      });
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }
}

export default PartnerService;
