import { TagDto, AgentDto } from '../Agent/types';
import { UserDto } from '../User/types';

export class CallBlitzDto {
  id?: number;
  name?: string;
  tags?: TagDto[];
  createdAt?: Date;
  assignee?: UserDto;
  assignToReps?: number[];
  folderId?: number; // TODO
  allNestedAgents?: boolean; // TODO
}

export class CallBlitzBatchDto {
  agents: AgentDto[] = [];
  numberOfAgentsInSession: number = 0;
  calledAgents: AgentDto[] = [];
}
