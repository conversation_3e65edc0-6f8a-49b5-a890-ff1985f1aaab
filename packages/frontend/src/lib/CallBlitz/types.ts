import { TagDto, AgentDto } from '../Agent/types';
import { UserDto } from '../User/types';

export class CallBlitzDto {
  id?: number;
  name?: string;
  tags?: TagDto[] | number[];
  createdAt?: Date;
  assignee?: UserDto;
  assignToReps?: number[];
  folderId?: number[]; // TODO
  allNestedAgents?: boolean; // TODO
  learningModuleTaskId?: number;
  maxNumberOfCalls?: number;
}

export class CallBlitzBatchDto {
  agents: AgentDto[] = [];
  numberOfAgentsInSession: number = 0;
  calledAgents: AgentDto[] = [];
}
