import { API } from '../Client';
import { CallBlitzDto, CallBlitzBatchDto } from './types';

namespace CallBlitzService {
  export async function deleteSession(id: number): Promise<boolean> {
    return await API.delete(`/call-blitz/${id}`);
  }

  export async function get(id: number): Promise<CallBlitzDto> {
    let res;

    try {
      res = await API.get(`/call-blitz/${id}`);
    } catch (err) {
      console.log('ERROR', err);
      throw err;
    }

    return res?.data || [];
  }

  export async function load(
    from: number = 0,
    numberOfResults: number = 10,
    search: string = '',
    repsFilter: number[] = [],
  ): Promise<CallBlitzDto[]> {
    let res;

    try {
      res = await API.get(`/call-blitz`, {
        params: {
          from,
          numberOfResults,
          search,
          reps: repsFilter,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function loadByIds(
    callBlitz: number[],
  ): Promise<CallBlitzDto[]> {
    if (!callBlitz || callBlitz.length === 0) {
      return [];
    }

    let res;

    try {
      res = await API.get(`/call-blitz/load-by-ids`, {
        params: {
          ids: callBlitz,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function clone(sessionId: number): Promise<CallBlitzDto> {
    let res;

    try {
      res = await API.get(`/call-blitz/clone/${sessionId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function upsertSession(
    dto: CallBlitzDto,
  ): Promise<CallBlitzDto> {
    let res;

    const body: any = {};

    if (dto.id) {
      body.id = dto.id;
    }

    if (dto.name) {
      body.name = dto.name;
    }

    if (dto.tags) {
      body.tags = dto.tags;
    }

    if (dto.folderId) {
      body.folderId = dto.folderId;
    }

    if (dto.allNestedAgents) {
      body.allNestedAgents = dto.allNestedAgents;
    }

    try {
      res = await API.post(`/call-blitz`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function assignNewSession(
    dto: CallBlitzDto,
  ): Promise<CallBlitzDto[]> {
    let res;

    const body: any = {};

    if (dto.id) {
      body.id = dto.id;
    }

    if (dto.name) {
      body.name = dto.name;
    }

    if (dto.tags) {
      body.tags = dto.tags;
    }

    if (dto.assignToReps) {
      body.assignToReps = dto.assignToReps;
    }

    try {
      res = await API.post(`/call-blitz/assign`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getRandomAgents(
    sessionId: number,
    numberOfAgents: number,
    skipAgents: number[],
  ): Promise<CallBlitzBatchDto> {
    let res;

    try {
      res = await API.get(`/call-blitz/random-agents`, {
        params: {
          sessionId,
          numberOfAgents,
          skipAgents,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }
}

export default CallBlitzService;
