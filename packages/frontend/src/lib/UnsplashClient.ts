import axios, { AxiosInstance, ResponseType } from 'axios';

class UnsplashClient {
  private _client: AxiosInstance;

  get client() {
    return this._client;
  }

  constructor(responseType = 'json') {
    const baseURL = 'https://api.unsplash.com/';

    let rt: ResponseType = 'json';
    if (responseType === 'blob') {
      rt = 'blob';
    }

    if (!baseURL) {
      throw Error('Environment keys for API client are not defined');
    }

    this._client = axios.create({
      baseURL,
      responseType: rt,
      timeout: 120000,
    });
    this._client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error?.response?.status === 401) {
          // If JWT is expired or invalid for some reason, just remove it and send them back to login page
          console.error('User logged out: 401 Unauthorized');
        }
        return Promise.reject(error);
      },
    );
  }
}

const API = new UnsplashClient().client;

export default API;
