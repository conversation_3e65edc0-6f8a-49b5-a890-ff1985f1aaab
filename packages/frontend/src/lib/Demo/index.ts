import { API } from '../Client';
import { DemoInboundFormResponseDto } from './types';

namespace DemoService {
  export async function createInboundFormResponse({
    name,
    email,
    sizeOfSalesTeam,
    vapiId,
    attribution,
    inviteCode,
    linkedinUrl,
  }: {
    name: string;
    email: string;
    sizeOfSalesTeam: string;
    vapiId?: string;
    attribution?: string;
    inviteCode?: string;
    linkedinUrl?: string;
  }) {
    const res = await API.post(`/demo/inbound-form-response`, {
      name,
      email,
      sizeOfSalesTeam,
      vapiId,
      attribution,
      inviteCode,
      linkedinUrl,
    });
    return res?.data;
  }

  export async function updateInboundFormResponseById({
    id,
    name,
    email,
    sizeOfSalesTeam,
    attribution,
    linkedinUrl,
  }: {
    id: number;
    name: string;
    email: string;
    sizeOfSalesTeam: string;
    attribution?: string;
    linkedinUrl?: string;
  }) {
    const res = await API.patch(`/demo/inbound-form-response/${id}`, {
      name,
      email,
      sizeOfSalesTeam,
      attribution,
      linkedinUrl,
    });
    return res?.data;
  }

  export async function verifyEmailVerificationCode({
    demoInboundFormResponseId,
    email,
    code,
  }: {
    demoInboundFormResponseId: number;
    email: string;
    code: string;
  }) {
    const res = await API.post(
      `/demo/inbound-form-response/${demoInboundFormResponseId}/verify-email`,
      {
        email,
        code,
      },
    );
    return res?.data;
  }

  export async function getDemoInboundFormResponseById(
    id: number,
  ): Promise<DemoInboundFormResponseDto> {
    let res;
    try {
      res = await API.get(`/demo/inbound-form-response/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data as DemoInboundFormResponseDto;
  }
}

export default DemoService;
