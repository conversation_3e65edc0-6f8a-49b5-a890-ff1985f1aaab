export interface DemoInboundFormResponseDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  name: string;
  email: string;
  sizeOfSalesTeam: string;
  attribution?: string;
  agentId: string;
  blacklisted: boolean;
  fromAffiliateId?: number;
  linkedinUrl?: string;
  avatar?: string;
  presClubChallengeRegistered: boolean;
  isAdmin: boolean;

  //you need to calcualte these from .name, they are not passed from the DB....thanks Sai!!! :):
  firstName: string;
  lastName: string;
  challengeNumTriesLeft: number;
  isInfluencer: boolean;
  challengeSocialCards: string[];
  isFinalist: boolean;
}
