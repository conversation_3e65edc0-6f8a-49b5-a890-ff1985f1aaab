import blockFormats from './formats/block';
import inlineFormat from './formats/inline';
import jsonFormat from './formats/json';
import kaiaFormat from './formats/kaia';
import srtFormat from './formats/srt';
import vttFormat from './formats/vtt';
import { TranscriptFileExtensions, TranscriptFormat } from './utils';

const transcriptFormats: TranscriptFormat[] = [
  vttFormat,
  srtFormat,
  kaiaFormat,
  ...blockFormats,
  inlineFormat,
  jsonFormat,
];

export const parseFileToTranscriptJson = (
  text: string,
  fileExtension: TranscriptFileExtensions,
  repName?: string,
) => {
  for (const format of transcriptFormats) {
    try {
      if (format.checker(text, fileExtension)) {
        console.log(`Parser ${format.name} matched`);
        const res = format.parser(text, repName);
        if (res !== null) {
          return res;
        }
      }
    } catch (e) {
      console.error(`Parser ${format.name} failed`, e);
    }
  }
};
