export const BLOCKED_FILE_TYPES = [
  'image/png',
  'image/jpeg',
  'image/gif',
  'image/webp',
  'audio/mpeg',
  'audio/wav',
  'audio/ogg',
  'video/mp4',
  'video/webm',
  'video/quicktime',
];

export enum TranscriptFileExtensions {
  VTT = 'vtt', // WebVTT (Zoom, Gong, others)
  SRT = 'srt', // SubRip Subtitle (common for transcripts)
  TXT = 'txt', // Plain text (Kaia, Zoom exports)
  JSON = 'json', // JSON format (Gong, Otter.ai, others)
}

export type Message = {
  message: string;
  secondsFromStart: number;
  userName: string;
  role?: 'user' | 'bot';
};

export function parseTimestamp(timestamp: string): number {
  const parts = timestamp.split(':').map((p) => parseInt(p, 10));
  for (const part of parts) {
    if (isNaN(part)) {
      throw new Error(`Invalid timestamp (part is not a number): ${timestamp}`);
    }
  }

  if (parts.length === 3) {
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  } else if (parts.length === 2) {
    return parts[0] * 60 + parts[1];
  }

  throw new Error(`Invalid timestamp (wrong number of parts): ${timestamp}`);
}

export const getRole = (userName: string, repName?: string): 'user' | 'bot' => {
  const repFirstName = repName?.split(' ')[0]?.toLowerCase();
  const userNameParts = userName.split(' ').map((part) => part.toLowerCase());
  return userNameParts.some((part) => part === repFirstName) ? 'user' : 'bot';
};

export type TranscriptFormat = {
  name: string;
  checker: (text: string, fileExtension: TranscriptFileExtensions) => boolean;
  parser: (text: string, repName?: string) => Message[] | null;
};
