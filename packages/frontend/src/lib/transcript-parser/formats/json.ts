import { TranscriptFormat, TranscriptFileExtensions } from "../utils";

const jsonFormat: TranscriptFormat = {
    name: 'json',
    checker: (text: string, fileExtension: TranscriptFileExtensions) => {
        if(fileExtension !== TranscriptFileExtensions.JSON) {
            return false;
        }

      const json = JSON.parse(text); // Parse the JSON transcript
      // Validate the shape of the parsed JSON
      
      return  Array.isArray(json) &&
        json.every(
          (item) =>
            item.message &&
            item.userName &&
            typeof item.secondsFromStart === 'number',
        );
    },
    parser: (text: string) => {
        return JSON.parse(text);
    }
}

export default jsonFormat;
