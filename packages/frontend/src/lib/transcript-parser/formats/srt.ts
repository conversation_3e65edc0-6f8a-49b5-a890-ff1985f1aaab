import {
  TranscriptFormat,
  Message,
  TranscriptFileExtensions,
  getRole,
} from '../utils';

const srtFormat: TranscriptFormat = {
  name: 'srt',
  checker: (text: string, fileExtension: TranscriptFileExtensions) => {
    return fileExtension === TranscriptFileExtensions.SRT;
  },
  parser: (text: string, repName?: string) => {
    const timeToSeconds = (time: string): number => {
      const [hours, minutes, seconds] = time
        .split(':')
        .map((part) => parseFloat(part.replace(',', '.')));
      return hours * 3600 + minutes * 60 + seconds;
    };

    const transcript: Message[] = [];
    const lines = text.split('\n').map((line) => line.trim());

    let currentMessage: Partial<Message> = {};
    let start = false;

    for (const line of lines) {
      if (!line) {
        // Empty line means the end of a block, push the message if it exists
        if (currentMessage.message) {
          transcript.push(currentMessage as Message);
          currentMessage = {};
        }
        start = false;
      } else if (line.includes('-->')) {
        // Extract timestamp
        start = true;
        const [startTime] = line.split(' --> ');
        currentMessage.secondsFromStart = timeToSeconds(startTime.trim());
      } else if (start) {
        // Extract speaker and message
        const separatorIndex = line.indexOf(':');
        if (separatorIndex > -1) {
          currentMessage.userName = line.slice(0, separatorIndex).trim();
          currentMessage.message = line.slice(separatorIndex + 1).trim();
          if (repName) {
            currentMessage.role = getRole(currentMessage.userName, repName);
          }
        } else {
          // Continue message from previous line
          currentMessage.message = (currentMessage.message || '') + ' ' + line;
        }
      }
    }

    return transcript;
  },
};

export default srtFormat;
