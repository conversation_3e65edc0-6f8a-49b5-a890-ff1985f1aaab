import {
  TranscriptFormat,
  TranscriptFileExtensions,
  parseTimestamp,
  Message,
  getRole,
} from '../utils';

export const kaiaFormat: TranscriptFormat = {
  name: 'kaia',
  checker: (text: string, fileExtension: TranscriptFileExtensions) => {
    const kaiaFormatRegex = /^\d{2}:\d{2}:\d{2} - \(\d\).+/m;
    return (
      kaiaFormatRegex.test(text) &&
      fileExtension === TranscriptFileExtensions.TXT
    );
  },
  parser: (text: string, repName?: string) => {
    // https://support.outreach.io/hc/en-us/articles/28005562346395-Exporting-Data-in-Kaia-using-Daily-Export
    const transcript: Message[] = [];
    const lines = text.split('\n').map((line) => line.trim());

    for (const line of lines) {
      if (!line) continue; // Skip empty lines

      // Match format: [HH:MM:SS] - "Speaker": "Message"
      let match = line.match(/^\[(\d{2}:\d{2}:\d{2})\] - "(.*?)": "(.*)"$/);

      // Match alternative format: HH:MM:SS - (ID)Speaker: Message
      if (!match) {
        match = line.match(/^(\d{2}:\d{2}:\d{2}) - \((\d+)\)(.*?):\s*(.*)$/);
      }

      if (match) {
        let timestamp, userName, message;

        if (match.length === 4) {
          // First format: [HH:MM:SS] - "Speaker": "Message"
          timestamp = match[1];
          userName = match[2];
          message = match[3];
        } else {
          // Second format: HH:MM:SS - (ID)Speaker: Message
          timestamp = match[1];
          userName = match[3].trim();
          message = match[4].trim();
        }

        transcript.push({
          secondsFromStart: parseTimestamp(timestamp),
          userName,
          message,
          ...(repName
            ? {
                role: getRole(userName, repName),
              }
            : {}),
        });
      }
    }

    return transcript;
  },
};

export default kaiaFormat;
