import { TranscriptFormat, Message, parseTimestamp, getRole } from '../utils';

const inlineFormat: TranscriptFormat = {
  name: 'inline',
  checker: (text: string) => {
    const regex = /\d{1,2}:\d{1,2}(?::\d{1,2})? [^:]+: .+/;
    return regex.test(text);
  },
  parser: (text: string, repName?: string) => {
    const regex =
      /(\d{1,2}:\d{1,2}(?::\d{1,2})?) ([^:]+): ([\s\S]+?)(?=\d{1,2}:\d{1,2}(?::\d{1,2})? [^:]+:|$)/g;

    const result: Array<Message> = [];

    const matches = Array.from(text.matchAll(regex));

    for (const match of matches) {
      const timeStr = match[1].trim();
      const speaker = match[2].trim();
      const message = match[3].trim();

      const secondsFromStart = parseTimestamp(timeStr);

      result.push({
        message,
        secondsFromStart,
        userName: speaker,
        ...(repName
          ? {
              role: getRole(speaker, repName),
            }
          : {}),
      });
    }

    return result;
  },
};

export default inlineFormat;
