import { TranscriptFormat, Message, parseTimestamp, getRole } from '../utils';

const blockFormats: TranscriptFormat[] = [
  {
    name: 'pipe-block',
    checker: (text: string) => {
      const regex = /^\d{1,2}:\d{1,2}(?::\d{1,2})? \| .+/m;
      return regex.test(text);
    },
    parser: (text: string, repName?: string) => {
      const lines = text.split('\n');
      const result: Array<Message> = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // Match timestamps in the format of MM:SS or HH:MM:SS
        const match = line.match(
          /^(\d{1,2}):(\d{1,2})(?::(\d{1,2}))? \| (.+)$/,
        );
        if (match) {
          const [, part1, part2, part3, speaker] = match;

          let secondsFromStart = 0;
          if (part3 !== undefined) {
            // HH:MM:SS
            const hours = parseInt(part1, 10);
            const minutes = parseInt(part2, 10);
            const seconds = parseInt(part3, 10);
            secondsFromStart = hours * 3600 + minutes * 60 + seconds;
          } else {
            // MM:SS
            const minutes = parseInt(part1, 10);
            const seconds = parseInt(part2, 10);
            secondsFromStart = minutes * 60 + seconds;
          }

          const message = lines[i + 1]?.trim();
          if (message) {
            result.push({
              message,
              secondsFromStart,
              userName: speaker.trim(),
              ...(repName
                ? {
                    role: getRole(speaker, repName),
                  }
                : {}),
            });
            i++; // Skip the message line
          }
        }
      }

      return result;
    },
  },
  {
    name: 'colon-block',
    checker: (text: string) => {
      const regex = /\d{1,2}:\d{2}(?::\d{2})?\s*\n\s*[^:\n]+:\s*.+/;
      return regex.test(text);
    },
    parser: (text: string, repName?: string) => {
      const lines = text.split('\n');
      const result: Array<Message> = [];

      const repFirstName = repName?.split(' ')[0]?.toLowerCase();

      for (let i = 0; i < lines.length - 1; i++) {
        const timestampLine = lines[i].trim();
        const speakerLine = lines[i + 1].trim();

        if (
          /^\d{1,2}:\d{2}(?::\d{2})?$/.test(timestampLine) &&
          /^[^:]+: .+/.test(speakerLine)
        ) {
          const secondsFromStart = parseTimestamp(timestampLine);
          const [userName, ...messageParts] = speakerLine.split(':');
          const message = messageParts.join(':').trim();

          const userNameParts = userName
            .split(' ')
            .map((part) => part.toLowerCase());

          result.push({
            message,
            userName: userName.trim(),
            secondsFromStart,
            role: userNameParts.some((part) => part === repFirstName)
              ? 'user'
              : 'bot',
          });

          i++; // skip the speaker/message line on next loop
        }
      }

      return result;
    },
  },
];

export default blockFormats;
