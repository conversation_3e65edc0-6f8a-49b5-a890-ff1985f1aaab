import {
  TranscriptFormat,
  TranscriptFileExtensions,
  Message,
  getRole,
} from '../utils';

const vttFormat: TranscriptFormat = {
  name: 'vtt',
  checker: (_: string, fileExtension: TranscriptFileExtensions) => {
    return fileExtension === TranscriptFileExtensions.VTT;
  },
  parser: (text: string, repName?: string) => {
    const transcript: Message[] = [];
    const lines = text.split('\n').map((l: string) => l.trim());

    let currentMessage: Partial<Message> = {};
    let start = false;
    let noActorIdentified = true;
    for (const l of lines) {
      if (l == '') {
        start = false;
        if (currentMessage.message) {
          transcript.push(currentMessage as Message);
          currentMessage = {};
        }
      } else if (l.toLowerCase().includes('-->')) {
        start = true;
        const all = l.split('-->')[0].split(':');
        const hours =
          60 *
          60 *
          1000 *
          parseInt(all[0].replaceAll('00', '0').replaceAll(' ', ''));
        const minutes =
          60 *
          1000 *
          parseInt(all[1].replaceAll('00', '0').replaceAll(' ', ''));

        let ms = 0;
        if (all[2]) {
          ms = parseInt(all[2].replace('.', ''));
          if (ms < 1000) {
            ms = 1000 + ms;
          }
        }
        currentMessage.secondsFromStart = ms + hours + minutes;
      } else if (start) {
        let actor = '';
        let msg = '';
        let added = false;
        const re: RegExp = /<v(.*?)>/g;
        const matches = l.match(re);
        if (matches) {
          actor = matches[0].replace('<v', '').replace('>', '').trim();
          msg = l.replace(matches[0], '').trim();
          added = true;
        } else {
          const i = l.indexOf(':');
          if (i > -1) {
            const all = [l.slice(0, i), l.slice(i + 1)];
            actor = all[0].trim();
            msg = all[1].trim();
            added = true;
          }
        }

        if (!added) {
          if (!currentMessage.message) {
            currentMessage.message = '';
          }
          currentMessage.message += l;
        } else {
          currentMessage.userName = actor;
          currentMessage.message = msg;
          if (repName) {
            currentMessage.role = getRole(actor, repName);
          }
          noActorIdentified = false;
        }
      }
    }

    if (noActorIdentified) {
      return null;
    }

    return transcript;
  },
};

export default vttFormat;
