/* eslint-disable @typescript-eslint/no-explicit-any */
import { FilterType } from '@/common/Calls/AIRoleplay/List/common';
import { API } from '../Client';
import { TeamDto, UserDto } from '../User/types';
import LearningModule, {
  LearningModuleAssignmentRule,
  LearningModuleAssignmentsDto,
  LearningModuleStatus,
  LearningModuleTemplate,
  PaginatedResults,
  ReportPerUser,
} from './types';

namespace LearningModuleService {
  export async function get(
    id: string,
    isAdmin: boolean,
  ): Promise<LearningModule> {
    let res;
    try {
      res = await API.get(`/learning-module/${id}`, {
        params: {
          isAdmin,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getV2(id: string) {
    let res;
    try {
      res = await API.get<LearningModuleTemplate>(`/v2/learning-module/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCompetitionStats(id: string) {
    let res;
    try {
      res = await API.get(`/learning-module/competition-stats/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCompetitionInfoForAgentPanel(agentId: number) {
    return (
      await API.get(`/learning-module/competition-info-for-agent/${agentId}`)
    )?.data;
  }

  export async function getCompetitionsInfoForCall(vapiCallId: string) {
    console.log(vapiCallId);

    return (
      await API.get(`/learning-module/competition-info-for-call/${vapiCallId}`)
    )?.data;
  }

  export async function getStats(id: string, filters: Record<FilterType, any>) {
    return (
      await API.get<ReportPerUser[]>(`/learning-module/stats/${id}`, {
        params: {
          filters,
        },
      })
    )?.data;
  }

  export async function getAll(
    isAdmin: boolean,
    assignedTo: number[] = [],
    status?: LearningModuleStatus,
    search: string = '',
    orderByDate: string = 'asc',
    archived = false,
    start = 0,
    numberOfResults = 100,
    isCompetition = false,
  ): Promise<PaginatedResults<LearningModule>> {
    let res;
    if (isAdmin) {
      try {
        res = await API.get(`/learning-module/admin`, {
          params: {
            assignedTo,
            status,
            search,
            orderByDate,
            archived,
            start,
            numberOfResults,
            isCompetition,
          },
        });
      } catch (err) {
        console.log(err);
      }
    } else {
      try {
        res = await API.get(`/learning-module/me`, {
          params: {
            isCompetition,
          },
        });
      } catch (err) {
        console.log(err);
      }
    }

    return res?.data;
  }

  export async function getAllV2(): Promise<
    PaginatedResults<LearningModuleTemplate>
  > {
    const res =
      await API.get<PaginatedResults<LearningModuleTemplate>>(
        `/v2/learning-module`,
      );

    return res?.data;
  }

  export async function getAllV2WithFilters(
    status?: string,
    archived?: boolean,
    search?: string,
    start?: number,
    numberOfResults?: number,
  ): Promise<PaginatedResults<LearningModuleTemplate>> {
    const params: any = {};
    if (status) params.status = status;
    if (archived !== undefined) params.archived = archived;
    if (search) params.search = search;
    if (start !== undefined) params.start = start;
    if (numberOfResults !== undefined) params.numberOfResults = numberOfResults;

    const res = await API.get<PaginatedResults<LearningModuleTemplate>>(
      `/v2/learning-module`,
      { params },
    );

    return res?.data;
  }

  export async function createTemplate(
    template: Omit<
      LearningModuleTemplate,
      'id' | 'orgId' | 'createdAt' | 'createdBy'
    >,
  ): Promise<LearningModuleTemplate> {
    const res = await API.post<LearningModuleTemplate>(
      `/v2/learning-module`,
      template,
    );
    return res?.data;
  }

  export async function updateTemplate(
    id: string,
    template: Partial<LearningModuleTemplate>,
  ): Promise<LearningModuleTemplate> {
    const res = await API.patch<LearningModuleTemplate>(
      `/v2/learning-module/${id}`,
      template,
    );
    return res?.data;
  }

  export async function deleteTemplate(id: string): Promise<void> {
    await API.delete(`/v2/learning-module/${id}`);
  }

  export async function archiveTemplate(
    id: string,
  ): Promise<LearningModuleTemplate> {
    const res = await API.patch<LearningModuleTemplate>(
      `/v2/learning-module/${id}/archive`,
    );
    return res?.data;
  }

  export async function unarchiveTemplate(
    id: string,
  ): Promise<LearningModuleTemplate> {
    const res = await API.patch<LearningModuleTemplate>(
      `/v2/learning-module/${id}/unarchive`,
    );
    return res?.data;
  }

  export async function cloneTemplate(
    id: string,
  ): Promise<LearningModuleTemplate> {
    const res = await API.post<LearningModuleTemplate>(
      `/v2/learning-module/${id}/clone`,
    );
    return res?.data;
  }

  export async function assignTemplate(
    id: string,
    assignees: number[],
    dueDate: Date,
    startDate?: Date,
    assigneesDueDate?: Array<{ userId: number; dueDate: Date }>,
    notifyUsersOnCompletion?: number[],
  ): Promise<LearningModule> {
    const res = await API.post<LearningModule>(
      `/v2/learning-module/${id}/assign`,
      {
        assignees,
        dueDate,
        startDate,
        assigneesDueDate,
        notifyUsersOnCompletion,
      },
    );
    return res?.data;
  }

  export async function getTemplateAssignments(
    id: string,
  ): Promise<LearningModuleAssignmentsDto[]> {
    const res = await API.get<LearningModuleAssignmentsDto[]>(
      `/v2/learning-module/${id}/assignments`,
    );
    return res?.data;
  }

  export async function exportSCORMPackage(learningModuleId: string) {
    try {
      const response = await API.get(
        `/learning-module/${learningModuleId}/scorm-package`,
        {
          responseType: 'blob',
        },
      );

      const blob = response.data;
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'scorm_package.zip';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.log(`Error exporting SCORM package`, err);
    }
  }

  export async function getFilters(learningModuleId: string) {
    return (
      await API.get<{ teams: TeamDto[] }>(
        `/learning-module/${learningModuleId}/filters`,
      )
    )?.data;
  }

  export async function clone(lmID: string): Promise<LearningModule> {
    let res;

    try {
      res = await API.post(`/learning-module/clone/${lmID}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function upsert(lm: LearningModule): Promise<LearningModule> {
    let res;

    if (lm.id) {
      try {
        res = await API.patch(`/learning-module/${lm.id}`, {
          ...lm,
          assignees: lm.assignees.map((u: UserDto) => u.id),
        });
      } catch (err) {
        console.log(err);
      }
    } else {
      try {
        res = await API.post(`/learning-module/`, {
          ...lm,
          assignees: lm.assignees.map((u: UserDto) => u.id),
        });
      } catch (err) {
        console.log(err);
      }
    }

    return res?.data;
  }

  export async function upsertV2(
    lm: LearningModuleTemplate,
  ): Promise<LearningModuleTemplate> {
    let res;

    if (lm.id) {
      try {
        res = await API.patch(`/v2/learning-module/${lm.id}`, {
          ...lm,
        });
      } catch (err) {
        console.log(err);
      }
    } else {
      try {
        res = await API.post(`/v2/learning-module/`, {
          ...lm,
        });
      } catch (err) {
        console.log(err);
      }
    }

    return res?.data;
  }

  export async function uploadSplashImage(image: File): Promise<string> {
    const formData = new FormData();
    formData.append('image', image as Blob);

    let res;
    try {
      res = await API.patch(`/learning-module/upload/splash-image`, formData);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteAttempt(
    learningModuleId: string,
    taskId: string,
    attemptId: number,
  ) {
    let res;
    try {
      res = await API.delete(
        `/learning-module/${learningModuleId}/task/${taskId}/attempt/${attemptId}`,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getTemplateAssignmentRules(
    id: string,
  ): Promise<LearningModuleAssignmentRule[]> {
    const res = await API.get<LearningModuleAssignmentRule[]>(
      `/v2/learning-module/${id}/rules`,
    );
    return res?.data;
  }

  export async function createRule(
    templateId: string,
    rule: LearningModuleAssignmentRule,
  ): Promise<LearningModuleAssignmentRule> {
    const res = await API.post<LearningModuleAssignmentRule>(
      `/v2/learning-module/${templateId}/rules`,
      rule,
    );
    return res?.data;
  }

  export async function updateRule(
    templateId: string,
    ruleId: number,
    rule: LearningModuleAssignmentRule,
  ): Promise<LearningModuleAssignmentRule> {
    const res = await API.patch<LearningModuleAssignmentRule>(
      `/v2/learning-module/${templateId}/rules/${ruleId}`,
      rule,
    );
    return res?.data;
  }

  export async function startCallBlitzForTask(
    learningModuleId: string,
    taskId: string,
  ): Promise<{ sessionId: number }> {
    const res = await API.post<{ sessionId: number }>(
      `/learning-module/${learningModuleId}/tasks/${taskId}/start-call-blitz`,
    );
    return res?.data;
  }
}

export default LearningModuleService;
