/* eslint-disable @typescript-eslint/no-explicit-any */
import { FilterType } from '@/common/Calls/AIRoleplay/List/common';
import { API } from '../Client';
import { TeamDto, UserDto } from '../User/types';
import LearningModule, {
  LearningModuleStatus,
  PaginatedResults,
  ReportPerUser,
} from './types';

namespace LearningModuleService {
  export async function get(
    id: string,
    isAdmin: boolean,
  ): Promise<LearningModule> {
    let res;
    try {
      res = await API.get(`/learning-module/${id}`, {
        params: {
          isAdmin,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCompetitionStats(id: string) {
    let res;
    try {
      res = await API.get(`/learning-module/competition-stats/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCompetitionInfoForAgentPanel(agentId: number) {
    return (
      await API.get(`/learning-module/competition-info-for-agent/${agentId}`)
    )?.data;
  }

  export async function getCompetitionsInfoForCall(vapiCallId: string) {
    console.log(vapiCallId);

    return (
      await API.get(`/learning-module/competition-info-for-call/${vapiCallId}`)
    )?.data;
  }

  export async function getStats(id: string, filters: Record<FilterType, any>) {
    return (
      await API.get<ReportPerUser[]>(`/learning-module/stats/${id}`, {
        params: {
          filters,
        },
      })
    )?.data;
  }

  export async function getAll(
    isAdmin: boolean,
    assignedTo: number[] = [],
    status?: LearningModuleStatus,
    search: string = '',
    orderByDate: string = 'asc',
    archived = false,
    start = 0,
    numberOfResults = 100,
    isCompetition = false,
  ): Promise<PaginatedResults> {
    let res;
    if (isAdmin) {
      try {
        res = await API.get(`/learning-module/admin`, {
          params: {
            assignedTo,
            status,
            search,
            orderByDate,
            archived,
            start,
            numberOfResults,
            isCompetition,
          },
        });
      } catch (err) {
        console.log(err);
      }
    } else {
      try {
        res = await API.get(`/learning-module/me`, {
          params: {
            isCompetition,
          },
        });
      } catch (err) {
        console.log(err);
      }
    }

    return res?.data;
  }

  export async function exportSCORMPackage(learningModuleId: string) {
    try {
      const response = await API.get(
        `/learning-module/${learningModuleId}/scorm-package`,
        {
          responseType: 'blob',
        },
      );

      const blob = response.data;
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'scorm_package.zip';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.log(`Error exporting SCORM package`, err);
    }
  }

  export async function getFilters(learningModuleId: string) {
    return (
      await API.get<{ teams: TeamDto[] }>(
        `/learning-module/${learningModuleId}/filters`,
      )
    )?.data;
  }

  export async function clone(lmID: string): Promise<LearningModule> {
    let res;

    try {
      res = await API.post(`/learning-module/clone/${lmID}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function upsert(lm: LearningModule): Promise<LearningModule> {
    let res;

    if (lm.id) {
      try {
        res = await API.patch(`/learning-module/${lm.id}`, {
          ...lm,
          assignees: lm.assignees.map((u: UserDto) => u.id),
        });
      } catch (err) {
        console.log(err);
      }
    } else {
      try {
        res = await API.post(`/learning-module/`, {
          ...lm,
          assignees: lm.assignees.map((u: UserDto) => u.id),
        });
      } catch (err) {
        console.log(err);
      }
    }

    return res?.data;
  }

  export async function uploadSplashImage(image: File): Promise<string> {
    const formData = new FormData();
    formData.append('image', image as Blob);

    let res;
    try {
      res = await API.patch(`/learning-module/upload/splash-image`, formData);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteAttempt(
    learningModuleId: string,
    taskId: string,
    attemptId: number,
  ) {
    let res;
    try {
      res = await API.delete(
        `/learning-module/${learningModuleId}/task/${taskId}/attempt/${attemptId}`,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default LearningModuleService;
