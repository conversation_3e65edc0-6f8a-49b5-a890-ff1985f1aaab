import { API } from '../Client';
import { UserDto } from '../User/types';
import LearningModule, {
  LearningModuleStatus,
  PaginatedResults,
  ReportPerUser,
} from './types';

namespace LearningModuleService {
  export async function get(
    id: string,
    isAdmin: boolean,
  ): Promise<LearningModule> {
    let res;
    try {
      res = await API.get(`/learning-module/${id}`, {
        params: {
          isAdmin,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCompetitionStats(id: string) {
    let res;
    try {
      res = await API.get(`/learning-module/competition-stats/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCompetitionInfoForAgentPanel(agentId: number) {
    return (
      await API.get(`/learning-module/competition-info-for-agent/${agentId}`)
    )?.data;
  }

  export async function getCompetitionsInfoForCall(vapiCallId: string) {
    console.log(vapiCallId);

    return (
      await API.get(`/learning-module/competition-info-for-call/${vapiCallId}`)
    )?.data;
  }

  export async function getStats(id: string) {
    return (await API.get<ReportPerUser[]>(`/learning-module/stats/${id}`))
      ?.data;
  }

  export async function getAll(
    isAdmin: boolean,
    assignedTo: number[] = [],
    status?: LearningModuleStatus,
    search: string = '',
    orderByDate: string = 'asc',
    archived = false,
    start = 0,
    numberOfResults = 100,
    isCompetition = false,
  ): Promise<PaginatedResults> {
    let res;

    if (isAdmin) {
      try {
        res = await API.get(`/learning-module/admin`, {
          params: {
            assignedTo,
            status,
            search,
            orderByDate,
            archived,
            start,
            numberOfResults,
            isCompetition,
          },
        });
      } catch (err) {
        console.log(err);
      }
    } else {
      try {
        res = await API.get(`/learning-module/me`, {
          params: {
            isCompetition,
          },
        });
      } catch (err) {
        console.log(err);
      }
    }

    return res?.data;
  }

  export async function clone(lmID: string): Promise<LearningModule> {
    let res;

    try {
      res = await API.post(`/learning-module/clone/${lmID}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function upsert(lm: LearningModule): Promise<LearningModule> {
    let res;

    if (lm.id) {
      try {
        res = await API.patch(`/learning-module/${lm.id}`, {
          ...lm,
          assignees: lm.assignees.map((u: UserDto) => u.id),
        });
      } catch (err) {
        console.log(err);
      }
    } else {
      try {
        res = await API.post(`/learning-module/`, {
          ...lm,
          assignees: lm.assignees.map((u: UserDto) => u.id),
        });
      } catch (err) {
        console.log(err);
      }
    }

    return res?.data;
  }
}

export default LearningModuleService;
