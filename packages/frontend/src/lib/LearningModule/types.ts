import { AgentDto } from '../Agent/types';
import { UserDto } from '../User/types';

export enum LearningModuleStatus {
  IN_PREPARATION = 'IN_PREPARATION',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

export enum LearningModuleTaskType {
  AI_BUYER_ROLEPLAY = 'AI_BUYER_ROLEPLAY',
  QUIZ = 'QUIZ',
}

export const LEARNING_MODULES_TASK_TYPES_LABELS = [
  {
    id: LearningModuleTaskType.AI_BUYER_ROLEPLAY,
    label: 'AI Buyer Bot',
    locked: false,
  },
  {
    id: LearningModuleTaskType.QUIZ,
    label: 'Quizzes',
    locked: true,
  },
];

export interface PaginatedResults {
  totNumbOfDocs: number;
  learningModules: LearningModule[];
}

export default interface LearningModule {
  id: string; //mongodb id
  orgId: number;
  createdAt: Date;
  createdBy: number;
  name: string;
  description: string;
  dueDate: Date;
  status: LearningModuleStatus;
  isCertification: boolean;
  isCompetition: boolean;
  archived: boolean;
  subModules: SubModule[];
  assignees: UserDto[];
  assigneesDueDate?: AssigneeDueDate[];
  splashImageInfo: string;
  lockProgress: boolean;
  progress: number; //percentage - based on if its an admin or a rep loading the module
  startDate?: Date;
  notifyUsersOnCompletion?: number[];
}

export interface AssigneeDueDate {
  userId: number;
  dueDate: Date;
}

export interface SubModule {
  id: string;
  name: string;
  description: string;
  progress: number; //percentage
  tasks: Task[];
  assigneesStats: AssigneeStats[];
}

export interface ReportPerUser {
  user: {
    firstName: string;
    lastName: string;
    avatar: string;
    id: number;
  };
  sumOfScores: number; //used only here to speedup calculations
  avgScore: number;
  passedTasks: number;
  numberOfAttempts: number;
  submodules?: {
    id: string;
    sumOfScores: number;
    avgScore: number;
    passedTasks: number;
    numberOfAttempts: number;
    name: string;
    tasks: {
      id: string;
      sumOfScores: number;
      avgScore: number;
      numberOfAttempts: number;
      passed: boolean;
      agent: {
        firstName: string;
        lastName: string;
        avatar: string;
        id: number;
      };
    }[];
  }[];
  firstAttemptDate?: Date;
}

/***********************************/
/********* ATTEMTPTS ***************/
/***********************************/
export interface AssigneeStats {
  userId: number;
  numberOfTasksPassed: number;
  tasks: TaskStats[];
}

export interface TaskStats {
  taskId: string;
  passed: boolean;
  attempts: TaskAttempt[];
}

export interface TaskAttempt {
  id: number;
  passed: boolean;
  date: Date;
  results: string;
  callId: number; //for ai-roleplay tasks
  callVapiId: string; //for ai-roleplay tasks
}

/*******************************/
/********* TASKS ***************/
/*******************************/
export type Task =
  | {
      id: string;
      type: LearningModuleTaskType.AI_BUYER_ROLEPLAY;
      info: AiRoleplayTask;
      numberOfAssigneesThatPassed?: number;
      hasInfoUpdated?: boolean;
    }
  | {
      id: string;
      type: LearningModuleTaskType.QUIZ;
      info: undefined;
      numberOfAssigneesThatPassed?: number;
      hasInfoUpdated?: boolean;
    };

export interface AiRoleplayTask {
  agent?: AgentDto;
  minNumberOfAttempts?: number; //0 = infinite
  maxNumberOfAttempts?: number;
  minScorecardScore?: number;
  stats: EvalStats[];
  criterions: EvalCriterion[];
  incorporateFillerWordsScore?: boolean; //used only for competitions
}

export interface EvalStats {
  name: string;
  min: number;
  max: number;
}

export interface EvalCriterion {
  section: string;
  criterion: string;
}

export interface CompetitionInfoForAgentPanel {
  learningModuleId: string;
  name: string;
  description: string;
  numberOfAttempts: number;
  maxNumberOfAttempts: number;
  incorporateFillerWordsScore: boolean;
}

export interface TaskAndAttempts {
  task: AiRoleplayTask;
  attempts: TaskAttempt[];
}
