import { AgentDto } from '../Agent/types';
import { CallDto } from '../Call/types';
import { UserDto } from '../User/types';

export enum LearningModuleStatus {
  IN_PREPARATION = 'IN_PREPARATION',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

export enum LearningModuleTaskType {
  AI_BUYER_ROLEPLAY = 'AI_BUYER_ROLEPLAY',
  QUIZ = 'QUIZ',
  CALL_BLITZ = 'CALL_BLITZ',
}

export const LEARNING_MODULES_TASK_TYPES_LABELS = [
  {
    id: LearningModuleTaskType.AI_BUYER_ROLEPLAY,
    label: 'AI Buyer Bot',
    locked: false,
  },
  // {
  //   id: LearningModuleTaskType.QUIZ,
  //   label: 'Quizzes',
  //   locked: true,
  // },
  {
    id: LearningModuleTaskType.CALL_BLITZ,
    label: 'Call Blitz',
    locked: false,
  },
];

export interface PaginatedResults<T> {
  totNumbOfDocs: number;
  learningModules: T[];
}

export enum LearningModuleTemplateStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
}

export default interface LearningModule {
  id: string; //mongodb id
  orgId: number;
  createdAt: Date;
  createdBy: number;
  name: string;
  description: string;
  dueDate: Date;
  status: LearningModuleStatus;
  isCertification: boolean;
  isCompetition: boolean;
  archived: boolean;
  subModules: SubModule[];
  assignees: UserDto[];
  assigneesDueDate?: AssigneeDueDate[];
  splashImageInfo: string;
  lockProgress: boolean;
  progress: number; //percentage - based on if its an admin or a rep loading the module
  startDate?: Date;
  notifyUsersOnCompletion?: number[];
}

export interface AssigneeDueDate {
  userId: number;
  dueDate: Date;
}

export interface SubModule {
  id: string;
  name: string;
  description: string;
  progress: number; //percentage
  tasks: Task[];
  assigneesStats: AssigneeStats[];
}

export interface ReportPerUser {
  user: {
    firstName: string;
    lastName: string;
    avatar: string;
    id: number;
  };
  sumOfScores: number; //used only here to speedup calculations
  avgScore: number;
  passedTasks: number;
  numberOfAttempts: number;
  submodules?: {
    id: string;
    sumOfScores: number;
    avgScore: number;
    passedTasks: number;
    numberOfAttempts: number;
    name: string;
    tasks: {
      id: string;
      sumOfScores: number;
      avgScore: number;
      numberOfAttempts: number;
      passed: boolean;
      agent: {
        firstName: string;
        lastName: string;
        avatar: string;
        id: number;
      };
      attemptDate?: Date;
    }[];
  }[];
  firstAttemptDate?: Date;
}

/***********************************/
/********* ATTEMTPTS ***************/
/***********************************/
export interface AssigneeStats {
  userId: number;
  numberOfTasksPassed: number;
  tasks: TaskStats[];
}

export interface TaskStats {
  taskId: string;
  passed: boolean;
  attempts: TaskAttempt[];
}

export interface TaskAttempt {
  id: number;
  passed: boolean;
  date: Date;
  results: string;
  callId?: number; //for ai-roleplay tasks
  callVapiId?: string; //for ai-roleplay tasks
  calls?: CallDto[]; //for call blitz tasks
}

/*******************************/
/********* TASKS ***************/
/*******************************/
export type Task =
  | {
      id: string;
      type: LearningModuleTaskType.AI_BUYER_ROLEPLAY;
      info: AiRoleplayTask;
      numberOfAssigneesThatPassed?: number;
      hasInfoUpdated?: boolean;
    }
  | {
      id: string;
      type: LearningModuleTaskType.QUIZ;
      info: undefined;
      numberOfAssigneesThatPassed?: number;
      hasInfoUpdated?: boolean;
    }
  | {
      id: string;
      type: LearningModuleTaskType.CALL_BLITZ;
      info: CallBlitzTask;
      numberOfAssigneesThatPassed?: number;
      hasInfoUpdated?: boolean;
    };

export interface AiRoleplayTask {
  agent?: AgentDto;
  minNumberOfAttempts?: number;
  maxNumberOfAttempts?: number;
  minScorecardScore?: number;
  stats: EvalStats[];
  criterions: EvalCriterion[];
  incorporateFillerWordsScore?: boolean;
}

export interface CallBlitzTask {
  sessionName?: string;
  tags?: number[];
  folderId?: number[];
  allNestedAgents?: boolean;
  minNumberOfCalls?: number;
  maxNumberOfCalls?: number;
  minScorecardScore?: number;
  incorporateFillerWordsScore?: boolean;
}

export interface EvalStats {
  name: string;
  min: number;
  max: number;
}

export interface EvalCriterion {
  section: string;
  criterion: string;
}

export interface CompetitionInfoForAgentPanel {
  learningModuleId: string;
  name: string;
  description: string;
  numberOfAttempts: number;
  maxNumberOfAttempts: number;
  incorporateFillerWordsScore: boolean;
}

export interface TaskAndAttempts {
  task: AiRoleplayTask;
  attempts: TaskAttempt[];
}

export interface LearningModuleTemplate {
  id: string;
  orgId: number;
  createdAt: Date;
  createdBy: number;
  name: string;
  description: string;
  status: LearningModuleTemplateStatus;
  isCertification: boolean;
  isCompetition: boolean;
  archived: boolean;
  splashImageInfo: string;
  lockProgress: boolean;
  subModules: LearningModuleTemplateSubModule[];
  assignmentRules?: LearningModuleAssignmentRule[];
}

export interface LearningModuleAssignmentRule {
  id: number;
  learningModuleTemplateId: string;
  status: LearningModuleAssignmentRuleStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  criterias: LearningModuleAssignmentRuleCriteria[];
  executions: LearningModuleAssignmentRuleExecution[];
  dueDate?: Date;
  settings?: Record<string, string | number | boolean>;
}

export enum LearningModuleAssignmentRuleStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export interface LearningModuleAssignmentRuleCriteria {
  id?: number;
  status: LearningModuleAssignmentRuleCriteriaStatus;
  type: LearningModuleAssignmentRuleType;
  value?:
    | LearningModuleAssignmentRuleTeamCriteriaValue
    | LearningModuleAssignmentRuleSignUpDateCriteriaValue
    | LearningModuleAssignmentRuleCompanyStartDateCriteriaValue
    | LearningModuleAssignmentRuleRegionCriteriaValue
    | LearningModuleAssignmentRuleRoleCriteriaValue;
}

export enum LearningModuleAssignmentRuleCriteriaStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum LearningModuleAssignmentRuleType {
  PROFILE = 'PROFILE',
  ROLE = 'ROLE',
  TEAM = 'TEAM',
  SIGN_UP_DATE = 'SIGN_UP_DATE',
  COMPANY_START_DATE = 'COMPANY_START_DATE',
  REGION = 'REGION',
}

export interface LearningModuleAssignmentRuleExecution {
  id: number;
  status: LearningModuleAssignmentRuleStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface LearningModuleTemplateSubModule {
  id: string;
  name: string;
  description: string;
  tasks: Task[];
}

export interface LearningModuleAssignmentsDto {
  id: string;
  orgId: number;
  createdAt: Date;
  createdBy: number;
  name: string;
  description: string;
  dueDate: Date;
  status: LearningModuleStatus;
  isCertification: boolean;
  isCompetition: boolean;
  archived: boolean;
  subModules: SubModule[];
  assignees: UserDto[];
  assigneesDueDate?: AssigneeDueDate[];
  splashImageInfo: string;
  lockProgress: boolean;
  progress: number;
  startDate?: Date;
  notifyUsersOnCompletion?: number[];
  rule: LearningModuleAssignmentRuleExecution;
}

export interface LearningModuleAssignmentRuleTeamCriteriaValue {
  operator: 'in' | 'not_in';
  teams: number[];
}

export interface LearningModuleAssignmentRuleSignUpDateCriteriaValue {
  operator: 'is' | 'between';
  from?: Date;
  to?: Date;
  date?: Date;
}

export interface LearningModuleAssignmentRuleCompanyStartDateCriteriaValue {
  operator: 'is' | 'between';
  from?: Date;
  to?: Date;
  date?: Date;
}

export interface LearningModuleAssignmentRuleRegionCriteriaValue {
  region: string;
}

export interface LearningModuleAssignmentRuleRoleCriteriaValue {
  operator: 'in' | 'not_in';
  roles: string[];
}
