import axios, { AxiosInstance, ResponseType } from 'axios';
import { SwitchOrgEvent } from './Organization/types';

class Client {
  private _client: AxiosInstance;

  get client() {
    return this._client;
  }

  constructor(responseType = 'json') {
    const baseURL = process.env.NEXT_PUBLIC_BASE_API_URL;
    let rt: ResponseType = 'json';
    if (responseType === 'blob') {
      rt = 'blob';
    }

    if (!baseURL) {
      throw Error('Environment keys for API client are not defined');
    }

    this._client = axios.create({
      baseURL,
      responseType: rt,
      timeout: 1000 * 60 * 2, // 2 minutes default timeout
    });

    this._client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (
          error?.response?.status === 403 &&
          error?.response?.data?.message.startsWith('switchOrg::')
        ) {
          console.error(
            'User switching org: 403 Forbidden',
            error.response.data,
          );

          const splitMessage =
            error?.response?.data?.message?.split('::') || [];
          const switchOrgEvent = new CustomEvent<SwitchOrgEvent>('switchOrg', {
            detail: {
              targetUuid: splitMessage[1],
              targetName: splitMessage[2],
              callerRoute: window.location.pathname + window.location.search,
            },
          });

          window.dispatchEvent(switchOrgEvent);
        }

        if (error?.response?.status === 401) {
          console.error(
            'User logged out: 401 Unauthorized',
            error.response.data,
          );

          if (error.response.data.message === 'User is deprovisioned') {
            window.dispatchEvent(new Event('logout/deprovisioned'));
          }
        }
        return Promise.reject(error);
      },
    );
  }
}

// Create the base clients
const baseClient = new Client();
export const API = baseClient.client;
export const API_BLOB = new Client('blob').client;

// Create long-running client by modifying the timeout of the base client
const longRunningClient = baseClient.client;
longRunningClient.defaults.timeout = 1000 * 60 * 11; // 11 minutes
export const API_LONG_RUNNING = longRunningClient;
