import { API } from '../Client';
import { AffiliateDto, CreateAffiliateDto } from './types';

namespace AffiliateService {
  export async function createAffiliate(
    body: CreateAffiliateDto,
  ): Promise<AffiliateDto> {
    let res;

    try {
      res = await API.post(`/affiliates`, body);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getAffiliateByInviteCode(
    inviteCode: string,
  ): Promise<AffiliateDto> {
    let res;

    try {
      res = await API.get(`/affiliates/${inviteCode}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default AffiliateService;
