import { BotSchedulingRule } from '@/common/Integrations/Calendar/types';
import { AgentCallType } from '../Agent/types';
import ScorecardConfigDto from '../ScorecardConfig/types';
import { TeamDto, UserDto } from '../User/types';

export enum IntegrationServiceType {
  REAL_CALL_SCORING = 'REAL_CALL_SCORING',
  CALENDAR = 'CALENDAR',
}

export enum IntegrationStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  INACTIVE = 'INACTIVE',
}

export enum IntegrationAuthType {
  AUTHORIZATION_BASIC = 'AUTHORIZATION_BASIC',
  AUTHORIZATION_BEARER = 'AUTHORIZATION_BEARER',
  NANGO = 'NANGO',
  NONE = 'NONE',
}

export interface ServiceProvider {
  id: number;
  companyName: string;
  logoUrl: string;
  services: IntegrationServiceType[];
  configurationNotes?: string;
  configurationLinks?: any;
  needsUserTeamSettings: boolean;
}

export interface IntegrationBasicAuthDetails {
  integrationId: number;
  username: string;
  password: string;
}

export interface IntegrationBearerAuthDetails {
  integrationId: number;
  token: string;
}

export interface IntegrationNangoAuthDetails {
  integrationId: number;
  nangoIntegrationId: string;
  nangoConnectionId: string;
}

export interface IntegrationOAuthDetails {
  integrationId: number;
  refreshToken: string;
  accessToken?: string;
  isExternallyManaged: boolean;
}

export type IntegrationAdditionalInfos = {
  optOutBotSchedulingRules?: BotSchedulingRule[];
};

export interface Integration {
  id: number;
  name: string;
  orgId?: number;
  provider?: ServiceProvider;
  authType: IntegrationAuthType;
  additionalInfos?: IntegrationAdditionalInfos;
  status?: IntegrationStatus;
  type?: IntegrationServiceType;

  basicAuthDetails?: IntegrationBasicAuthDetails;
  bearerAuthDetails?: IntegrationBearerAuthDetails;
  nangoAuthDetails?: IntegrationNangoAuthDetails;
  originalIntegrationId?: number;
  originalIntegration?: Integration;
  subIntegrations?: Integration[];
}

export interface ZoomTranscriptPart {
  start: number;
  end: number;
  speaker: string;
  sentence: string;
}

export interface ZoomParticipant {
  id: string;
  name: string;
  user_id: string;
  registrant_id: string;
  user_email: string;
  join_time: string;
  leave_time: string;
  duration: number;
  status: string;
}

export interface ZoomMeetingDto {
  id: string;
  topic: string;
  start_time: string;
  timezone: string;
  duration: number;
  transcript: ZoomTranscriptPart[];
  participants: ZoomParticipant[];
}

export interface ZoomTranscriptPartInternal extends ZoomTranscriptPart {
  role: 'bot' | 'user';
}

export interface ZoomParticipantInternal extends ZoomParticipant {
  isInternal: boolean;
  avatarUrl?: string;
}

export interface ZoomMeetingInternalDto extends ZoomMeetingDto {
  transcript: ZoomTranscriptPartInternal[];
  participants: ZoomParticipantInternal[];
}

export interface IntegrationDto {
  id: number;
  name: string;
  orgId: number;
  providerId: number;
  provider?: ServiceProvider;
  authType: IntegrationAuthType;
  additionalInfos?: any;
  status: IntegrationStatus;
  basicAuthDetails?: IntegrationBasicAuthDetails;
  bearerAuthDetails?: IntegrationBearerAuthDetails;
  nangoAuthDetails?: IntegrationNangoAuthDetails;

  usersSettings?: IntegrationUserSettings[];
  teamsSettings?: IntegrationTeamSettings[];
}

export interface IntegrationUserSettings {
  id?: number;
  integrationId: number;
  userId: number;
  scorecardConfigId: number;
  callType: AgentCallType;
  externalUserId: string;

  integration?: IntegrationDto;
  user?: UserDto;
  scorecardConfig?: ScorecardConfigDto;
}

export interface IntegrationTeamSettings {
  integrationId: number;
  teamId: number;
  scorecardConfigId: number;
  callType: AgentCallType;

  integration?: IntegrationDto;
  team?: TeamDto;
  scorecardConfig?: ScorecardConfigDto;
}

export interface ExternalUserDto {
  userId: string;
  name?: string;
  email?: string;
}

export enum RecipeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export interface Recipe {
  id: string;
  name: string;
  provider: string;
  status: RecipeStatus;
  createdAt: Date;
}
