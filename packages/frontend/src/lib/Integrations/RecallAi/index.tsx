import {
  BotSchedulingRule,
  SchedulingPreviewCalendarEvent,
} from '@/common/Integrations/Calendar/types';
import { API } from '@/lib/Client';
import { IntegrationAdditionalInfos } from '../types';

namespace RecallAiService {
  export async function addBotToMeeting(meetingUrl: string) {
    try {
      const res = await API.post<{ success: boolean }>(
        `/integrations/recall-ai/add-bot-to-meeting`,
        {
          meetingUrl,
        },
      );
      return !!res.data?.success;
    } catch (err) {
      console.log(err);
    }
  }

  export async function updateShouldRecordOverride(
    calendarEventId: number,
    shouldRecordOverride: boolean | null,
  ) {
    try {
      const res = await API.patch(
        `/integrations/recall-ai/should-record-override`,
        {
          calendarEventId,
          shouldRecordOverride,
        },
      );
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function getCalendarSchedulingPreview(
    integrationId: number,
    optOutBotSchedulingRules?: BotSchedulingRule[],
  ) {
    try {
      const res = await API.post<SchedulingPreviewCalendarEvent[]>(
        `/integrations/recall-ai/get-calendar-scheduling-preview`,
        {
          integrationId,
          optOutBotSchedulingRules,
        },
      );
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function getCalendarCount() {
    try {
      const res = await API.get<{
        total: number;
      }>(`/integrations/recall-ai/calendar-count`);
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function setBotSchedulingRulesForOrg(
    optOutBotSchedulingRules: BotSchedulingRule[],
  ) {
    try {
      const res = await API.post(
        `/integrations/recall-ai/bot-scheduling-rules/org`,
        {
          optOutBotSchedulingRules,
        },
      );
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function setAdditionalInfos(
    integrationId: number,
    additionalInfos: IntegrationAdditionalInfos,
  ) {
    try {
      const res = await API.post(`/integrations/recall-ai/additional-infos`, {
        integrationId,
        additionalInfos,
      });
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function exchangeGoogleCode(
    code: string,
    integrationProviderId: number,
  ) {
    let res;
    try {
      res = await API.post(`/integrations/recall-ai/exchange-google-code`, {
        code,
        integrationProviderId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data as {
      refreshToken: string;
    };
  }

  export async function exchangeMicrosoftCode(
    code: string,
    integrationProviderId: number,
  ) {
    let res;
    try {
      res = await API.post(`/integrations/recall-ai/exchange-microsoft-code`, {
        code,
        integrationProviderId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data as {
      refreshToken: string;
    };
  }
}

export default RecallAiService;
