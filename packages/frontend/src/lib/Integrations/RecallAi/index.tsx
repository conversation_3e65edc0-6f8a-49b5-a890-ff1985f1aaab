import {
  BotSchedulingRule,
  SchedulingPreviewCalendarEvent,
} from '@/common/Integrations/Calendar/types';
import { API } from '@/lib/Client';

namespace RecallAiService {
  export async function addBotToMeeting(meetingUrl: string) {
    try {
      const res = await API.post<{ success: boolean }>(
        `/integrations/recall-ai/add-bot-to-meeting`,
        {
          meetingUrl,
        },
      );
      return !!res.data?.success;
    } catch (err) {
      console.log(err);
    }
  }

  export async function getCalendarSchedulingPreview(
    integrationId: number,
    optOutBotSchedulingRules?: BotSchedulingRule[],
  ) {
    try {
      const res = await API.post<SchedulingPreviewCalendarEvent[]>(
        `/integrations/recall-ai/get-calendar-scheduling-preview`,
        {
          integrationId,
          optOutBotSchedulingRules,
        },
      );
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function setBotSchedulingRules(
    integrationId: number,
    optOutBotSchedulingRules: BotSchedulingRule[],
  ) {
    try {
      const res = await API.post(
        `/integrations/recall-ai/bot-scheduling-rules`,
        {
          integrationId,
          optOutBotSchedulingRules,
        },
      );
      return res.data;
    } catch (err) {
      console.log(err);
    }
  }

  export async function exchangeGoogleCode(
    code: string,
    integrationProviderId: number,
  ) {
    let res;
    try {
      res = await API.post(`/integrations/recall-ai/exchange-google-code`, {
        code,
        integrationProviderId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data as {
      refreshToken: string;
    };
  }

  export async function exchangeMicrosoftCode(
    code: string,
    integrationProviderId: number,
  ) {
    let res;
    try {
      res = await API.post(`/integrations/recall-ai/exchange-microsoft-code`, {
        code,
        integrationProviderId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data as {
      refreshToken: string;
    };
  }
}

export default RecallAiService;
