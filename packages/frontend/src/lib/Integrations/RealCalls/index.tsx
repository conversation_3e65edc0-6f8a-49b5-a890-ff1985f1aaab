import { API } from '../../Client';
import { FilterState } from '@/common/Calls/Real/List';
import RealCall, {
  AllCategorizationRules,
  AllPrivacyRules,
  CallsFromProviderResults,
  RealCallOrganizationCategorizationRule,
  RealCallOrganizationPrivacyRule,
  RealCallAutoEmailTarget,
  RealCallPublicUrlDetails,
  RealCallsImportTask,
  RealCallsPublicDetailsDto,
  RealCallTeamCategorizationRule,
  RealCallTeamPrivacyRule,
  RealCallUserCategorizationRule,
  RealCallUserPrivacyRule,
  UpsertActionItemDto,
  FollowUpEmailResponse,
  RealCallCategorizationRuleBehavior,
} from './types';
import { AgentCallType } from '@/lib/Agent/types';
import { CallsSortingParam } from '@/common/Calls/AIRoleplay/List/common';
import { RealCallTranscriptMessage } from '@/lib/Ai/types';

namespace RealCallsService {
  export async function generateDescriptions(callTypesData: {
    [key: string]: string[];
  }) {
    const res = await API.post(
      `/integrations/real-calls/generate-descriptions`,
      {
        callTypesData,
      },
    );
    return res.data;
  }

  export async function testRouting(
    title: string,
    transcript: RealCallTranscriptMessage[],
    callerId: number,
  ) {
    const res = await API.post(`/integrations/real-calls/test-routing`, {
      title,
      transcript,
      callerId,
    });
    return res.data;
  }

  export async function generateFollowUpEmail(
    realCallId: number,
    fromUserName: string,
    refinement?: string,
  ) {
    const response = await API.post<FollowUpEmailResponse>(
      `/integrations/real-calls/${realCallId}/generate-follow-up-email`,
      {
        fromUserName,
        refinement,
      },
    );
    return response.data;
  }

  export async function getCallId(pwd: string) {
    try {
      const res = await API.get<{ id: number }>(
        `/integrations/real-calls/call-id`,
        {
          params: {
            pwd: pwd || '',
          },
        },
      );
      return res.data;
    } catch (e) {
      console.log(e);
    }
  }

  export async function upsertRealCallUserConfig(
    userId: number,
    autoEmailTarget: RealCallAutoEmailTarget,
  ) {
    const res = await API.patch(`/integrations/real-calls/config/user`, {
      userId,
      autoEmailTarget,
    });
    return res.data;
  }

  export async function deleteRealCallUserConfig(userId: number) {
    const res = await API.delete(`/integrations/real-calls/config/user`, {
      data: {
        userId,
      },
    });
    return res.data;
  }

  export async function upsertRealCallTeamConfig(
    teamId: number,
    autoEmailTarget: RealCallAutoEmailTarget,
  ) {
    const res = await API.patch(`/integrations/real-calls/config/team`, {
      teamId,
      autoEmailTarget,
    });
    return res.data;
  }

  export async function deleteRealCallTeamConfig(teamId: number) {
    const res = await API.delete(`/integrations/real-calls/config/team`, {
      data: {
        teamId,
      },
    });
    return res.data;
  }

  export async function upsertRealCallOrgConfig(
    autoEmailTarget: RealCallAutoEmailTarget,
  ) {
    const res = await API.patch(`/integrations/real-calls/config/org`, {
      autoEmailTarget,
    });
    return res.data;
  }

  export async function deleteRealCallOrgConfig() {
    const res = await API.delete(`/integrations/real-calls/config/org`);
    return res.data;
  }

  export async function getPublicDetails(callId: number, pwd?: string) {
    try {
      const res = await API.get<RealCallsPublicDetailsDto>(
        `/integrations/real-calls/public-details/${callId}`,
        {
          params: {
            pwd: pwd || '',
          },
        },
      );
      return res.data;
    } catch (e) {
      console.log(e);
    }
  }

  export async function getScorecardNew(callId: number) {
    try {
      const res = await API.get(`/integrations/real-calls/scorecard/${callId}`);
      return res.data;
    } catch (e) {
      console.log(e);
    }
  }

  export async function getPublicUrl(callId: number) {
    try {
      const res = await API.get<RealCallPublicUrlDetails>(
        `/integrations/real-calls/public-url/${callId}`,
      );
      return res.data;
    } catch (e) {
      console.log(e);
    }
  }

  export async function togglePublicUrl(
    callId: number,
    isPublicEnabled: boolean,
  ) {
    try {
      const res = await API.post<RealCallPublicUrlDetails>(
        `/integrations/real-calls/public-url/${callId}/toggle`,
        {
          isPublicEnabled,
        },
      );
      return res.data;
    } catch (e) {
      console.log(e);
    }
  }

  export async function regeneratePublicUrl(callId: number) {
    try {
      const res = await API.post<RealCallPublicUrlDetails>(
        `/integrations/real-calls/public-url/${callId}/regenerate`,
      );
      return res.data;
    } catch (e) {
      console.log(e);
    }
  }

  export async function sendSummaryEmail(
    callId: number,
    email?: string,
    realCallPartyId?: number,
  ) {
    try {
      await API.post(`/integrations/real-calls/send-summary-email/${callId}`, {
        email,
        realCallPartyId,
      });
    } catch (e) {
      console.log(e);
    }
  }

  export async function deleteActionItem(actionItemId: number) {
    try {
      await API.delete(`/integrations/real-calls/action-items/${actionItemId}`);
    } catch (e) {
      console.log(e);
    }
  }

  export async function updateActionItem(
    actionItemId: number,
    dto: UpsertActionItemDto,
  ) {
    try {
      const res = await API.patch(
        `/integrations/real-calls/action-items/${actionItemId}`,
        dto,
      );
      return res.data;
    } catch (e) {
      console.log(e);
    }
  }

  export async function updateCallInfo(
    callId: number,
    callType: string,
    callerId?: number,
    scorecardConfigId?: number,
    rescore = false,
  ) {
    let res;

    try {
      res = await API.post(
        `/integrations/real-calls/update-call-info/${callId}`,
        {
          callerId,
          callType,
          scorecardConfigId,
          rescore,
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateCallPrivacy(
    callId: number,
    isPrivate: boolean,
    sharedWith: number[],
  ) {
    const res = await API.post(
      `/integrations/real-calls/update-call-privacy/${callId}`,
      {
        isPrivate,
        sharedWith,
      },
    );
    return res?.data;
  }

  export async function deleteCall(callId: number) {
    let res;

    try {
      res = await API.delete(`/integrations/real-calls/${callId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getImportTasks(): Promise<RealCallsImportTask[]> {
    let res;

    try {
      res = await API.get(`/integrations/real-calls/import-tasks/`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function ImportCalls(
    integrationId: number,
    importScope: string,
    scorecardConfigId: number | undefined,
    callType: string | undefined,
    callProviderIds: string[],
  ) {
    let res;

    try {
      res = await API.post(`/integrations/real-calls/import-calls/`, {
        importScope,
        integrationId,
        scorecardConfigId,
        callType,
        callProviderIds,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function listCalls(
    filters: FilterState,
    sortBy: CallsSortingParam[],
  ) {
    let res;

    const data = {
      from: filters.from,
      numberOfResults: filters.numberOfResults,
      date: filters.dateFilter,
      minDuration: filters.minDuration,
      maxDuration: filters.maxDuration,
      scorecards: filters.scorecards,
      scorecardSections: filters.scorecardSections,
      scorecardCriterions: filters.scorecardCriterions,
      scorecardCriterionsStatus: filters.scorecardCriterionsStatus,
      reps: filters.reps,
      callTypes: filters.callTypes,
      organizationCustomCallTypesIds: filters.organizationCustomCallTypesIds,
      teamsCustomCallTypesIds: filters.teamsCustomCallTypesIds,
      usersCustomCallTypesIds: filters.usersCustomCallTypesIds,
      integrationId: filters.integrationId,
      search: filters.search,
      isPrivate: filters.isPrivate,
      sortBy: sortBy,
    };

    try {
      res = await API.post(`/integrations/real-calls/listCalls`, data);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getTotalIntegrationsCount() {
    try {
      const res = await API.get<{ count: number }>(
        `/integrations/real-calls/total-integrations-count`,
      );
      return res.data.count;
    } catch (err) {
      console.log(err);
    }
  }

  export async function listCallsFromProvider(
    integrationId: number,
    filters: FilterState,
    cursor: string,
    userId?: string,
  ): Promise<CallsFromProviderResults> {
    let res;

    try {
      res = await API.post(
        `/integrations/real-calls/list-calls-from-provider/${integrationId}`,
        {
          from: filters.from,
          numberOfResults: filters.numberOfResults,
          date: filters.dateFilter,
          cursor,
          userId,
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function listUsers(integrationId: number) {
    let res;
    try {
      res = await API.get(
        `/integrations/real-calls/listUsers/${integrationId}`,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCall(callId: number): Promise<RealCall> {
    let res;
    try {
      res = await API.get(`/integrations/real-calls/${callId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCoachingInfo(callId: number) {
    let res;
    try {
      res = await API.get(`/integrations/real-calls/coaching/${callId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function rescoreCall(
    callId: number,
    scorecardConfigId?: number,
  ) {
    const res = await API.post(`/integrations/real-calls/rescore/${callId}`, {
      scorecardConfigId,
    });

    return res?.data;
  }

  export async function getScorecard(
    integrationId: number,
    providerCallId: string,
  ) {
    let res;
    try {
      res = await API.get(
        `/integrations/real-calls/scorecard/${integrationId}/${providerCallId}`,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function addNewRealCall(
    callerId: number,
    scorecardConfigId: number,
    callType: string,
    integrationId: number,
    transcript: RealCallTranscriptMessage[],
    plainTextTranscript: string,
    title: string,
    actors: unknown,
    runScore = false,
  ) {
    const res = await API.post(`/integrations/real-calls/`, {
      callerId,
      integrationId,
      scorecardConfigId,
      transcript,
      plainTextTranscript,
      title,
      actors,
      runScore,
      callType,
    });

    return res?.data;
  }

  //-------------------------------- CATEGORIZATION RULES --------------------------------

  export async function getAlCategorizationRules(): Promise<AllCategorizationRules> {
    const res = await API.get(`/integrations/real-calls/categorization-rules`);
    return res?.data;
  }

  export async function upsertCategorizationRulePerOrganization(
    ruleId: number | undefined,
    label: string,
    explanation: string,
    callType: AgentCallType,
    behavior: RealCallCategorizationRuleBehavior,
    scorecardConfigId: number | undefined,
  ): Promise<RealCallOrganizationCategorizationRule> {
    const res = await API.post(
      `/integrations/real-calls/categorization-rules/organization${ruleId ? `/${ruleId}` : ``}`,
      {
        label,
        explanation,
        callType,
        behavior,
        scorecardConfigId,
      },
    );

    return res?.data;
  }

  export async function deleteCategorizationRulePerOrganization(
    ruleId: number | undefined,
  ): Promise<void> {
    const res = await API.delete(
      `/integrations/real-calls/categorization-rules/organization/${ruleId}`,
    );
    return res?.data;
  }

  export async function upsertCategorizationRulePerTeam(
    ruleId: number | undefined,
    teamId: number,
    label: string,
    explanation: string,
    callType: AgentCallType,
    behavior: RealCallCategorizationRuleBehavior,
    scorecardConfigId: number | undefined,
  ): Promise<RealCallTeamCategorizationRule> {
    const res = await API.post(
      `/integrations/real-calls/categorization-rules/team${ruleId ? `/${ruleId}` : ``}`,
      {
        teamId,
        label,
        explanation,
        callType,
        behavior,
        scorecardConfigId,
      },
    );

    return res?.data;
  }

  export async function deleteCategorizationRulePerTeam(
    ruleId: number | undefined,
  ): Promise<void> {
    const res = await API.delete(
      `/integrations/real-calls/categorization-rules/team/${ruleId}`,
    );
    return res?.data;
  }

  export async function upsertCategorizationRulePerUser(
    ruleId: number | undefined,
    userId: number,
    label: string,
    explanation: string,
    callType: AgentCallType,
    behavior: RealCallCategorizationRuleBehavior,
    scorecardConfigId: number | undefined,
  ): Promise<RealCallUserCategorizationRule> {
    const res = await API.post(
      `/integrations/real-calls/categorization-rules/user${ruleId ? `/${ruleId}` : ``}`,
      {
        userId,
        label,
        explanation,
        callType,
        behavior,
        scorecardConfigId,
      },
    );

    return res?.data;
  }

  export async function deleteCategorizationRulePerUser(
    ruleId: number | undefined,
  ): Promise<void> {
    const res = await API.delete(
      `/integrations/real-calls/categorization-rules/user/${ruleId}`,
    );
    return res?.data;
  }

  //-------------------------------- PRIVACY RULES --------------------------------

  export async function getAlPrivacyRules(): Promise<AllPrivacyRules> {
    const res = await API.get(`/integrations/real-calls/privacy-rules`);
    return res?.data;
  }

  export async function upsertPrivacyRulePerOrganization(
    ruleId: number | undefined,
    ruleType: string,
    settings: unknown,
  ): Promise<RealCallOrganizationPrivacyRule> {
    const res = await API.post(
      `/integrations/real-calls/privacy-rules/organization${ruleId ? `/${ruleId}` : ``}`,
      {
        ruleType,
        settings,
      },
    );

    return res?.data;
  }

  export async function deletePrivacyRulePerOrganization(
    ruleId: number | undefined,
  ): Promise<void> {
    const res = await API.delete(
      `/integrations/real-calls/privacy-rules/organization/${ruleId}`,
    );
    return res?.data;
  }

  export async function upsertPrivacyRulePerTeam(
    ruleId: number | undefined,
    teamId: number,
    ruleType: string,
    settings: unknown,
  ): Promise<RealCallTeamPrivacyRule> {
    const res = await API.post(
      `/integrations/real-calls/privacy-rules/team${ruleId ? `/${ruleId}` : ``}`,
      {
        teamId,
        ruleType,
        settings,
      },
    );

    return res?.data;
  }

  export async function deletePrivacyRulePerTeam(
    ruleId: number | undefined,
  ): Promise<void> {
    const res = await API.delete(
      `/integrations/real-calls/privacy-rules/team/${ruleId}`,
    );
    return res?.data;
  }

  export async function upsertPrivacyRulePerUser(
    ruleId: number | undefined,
    userId: number,
    ruleType: string,
    settings: unknown,
  ): Promise<RealCallUserPrivacyRule> {
    const res = await API.post(
      `/integrations/real-calls/privacy-rules/user${ruleId ? `/${ruleId}` : ``}`,
      {
        userId,
        ruleType,
        settings,
      },
    );

    return res?.data;
  }

  export async function deletePrivacyRulePerUser(ruleId: number | undefined) {
    const res = await API.delete(
      `/integrations/real-calls/privacy-rules/user/${ruleId}`,
    );
    return res?.data;
  }

  export async function getTranscription(callId: number) {
    const res = await API.get(
      `/integrations/real-calls/transcription/${callId}`,
    );
    return res?.data || '';
  }
}

export default RealCallsService;
