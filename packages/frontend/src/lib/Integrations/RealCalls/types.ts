import { OrganizationDto } from '@/lib/Organization/types';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { TeamDto, UserDto } from '@/lib/User/types';
import { IntegrationDto } from '../types';
import { AgentCallType } from '@/lib/Agent/types';

export type FollowUpEmailResponse = {
  title: string;
  body: string;
  toUserName: string;
};

export enum RealCallAutoEmailTarget {
  AllParticipants = 'AllParticipants',
  InternalParticipants = 'InternalParticipants',
  None = 'None',
}

export type RealCallOrganizationConfig = {
  id: number;
  orgId: number;
  autoEmailTarget: RealCallAutoEmailTarget;
};

export type RealCallTeamConfig = {
  id: number;
  orgId: number;
  teamId: number;
  autoEmailTarget: RealCallAutoEmailTarget;
};

export type RealCallUserConfig = {
  id: number;
  orgId: number;
  userId: number;
  autoEmailTarget: RealCallAutoEmailTarget;
};

export enum RealCallPartyAffiliation {
  INTERNAL = 'INTERNAL',
  EXTERNAL = 'EXTERNAL',
}

export type RealCallPublicUrlDetails = {
  url: string;
  isPublicEnabled: boolean;
};

export enum RealCallStatus {
  SCORING = 'SCORING',
  SCORED = 'SCORED',
  DELETED = 'DELETED',
}

export enum RealCallScoringStatus {
  TO_BE_SCORED = 'TO_BE_SCORED',
  SCORING = 'SCORING',
  SCORED_SUCCESS = 'SCORED_SUCCESS',
  SCORED_ERROR = 'SCORED_ERROR',
  SCORING_SKIPPED_DUE_TO_PRIVACY_RULE = 'SCORING_SKIPPED_DUE_TO_PRIVACY_RULE',
}

export enum RealCallsImportTaskScope {
  SELECTED_CALL_IDS = 'SELECTED_CALL_IDS',
  ALL = 'ALL',
  ALL_LAST_7_DAYS = 'ALL_LAST_7_DAYS',
  ALL_LAST_15_DAYS = 'ALL_LAST_15_DAYS',
  ALL_LAST_30_DAYS = 'ALL_LAST_30_DAYS',
  ALL_LAST_60_DAYS = 'ALL_LAST_60_DAYS',
}

export const RealCallsImportTaskScopeList = [
  {
    value: RealCallsImportTaskScope.SELECTED_CALL_IDS,
    label: 'Manually select calls',
  },
  { value: RealCallsImportTaskScope.ALL, label: 'All Calls' },
  {
    value: RealCallsImportTaskScope.ALL_LAST_7_DAYS,
    label: 'All Calls - Last 7 Days',
  },
  {
    value: RealCallsImportTaskScope.ALL_LAST_15_DAYS,
    label: 'All Calls - Last 15 Days',
  },
  {
    value: RealCallsImportTaskScope.ALL_LAST_30_DAYS,
    label: 'All Calls - Last 30 Days',
  },
  {
    value: RealCallsImportTaskScope.ALL_LAST_60_DAYS,
    label: 'All Calls - Last 60 Days',
  },
];

export default interface RealCall {
  id: number;
  orgId: number;
  callerId?: number;
  caller?: UserDto;
  createdAt: Date;
  callDate: Date;
  org: OrganizationDto;
  title: string;
  duration: number;
  platform?: string; //TBD???
  prospectName?: string; //TBD???
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transcript: any;
  plainTextTranscript: string;
  parties?: RealCallParty[];
  integrationId: number;
  providerCallId: string;
  callType: string;
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  scorecard?: any;
  status: RealCallStatus;
  integration: IntegrationDto;
  mediaUrls?: RealCallMediaUrls;
  scoringStatus: RealCallScoringStatus;
  isPrivate: boolean;
  sharedWith?: UserDto[];
  orgCategory: any;
  teamCategory: any;
  userCategory: any;
  categoryExplanation: string;
  privacyRuleExplanation?: string;
}

export type ExternalFile = {
  url: string;
};

export type RealCallMediaUrls = {
  audio?: ExternalFile;
  video?: ExternalFile;
};

export interface RealCallParty {
  id: number;
  realCallId: number;
  name: string;
  email: string;
  userId?: number;
  title: string;
  affiliation: RealCallPartyAffiliation;
  user?: UserDto;
  realCall: RealCall;
}

export interface RealCallsImportTask {
  id: number;
  createdAt: Date;
  orgId: number;
  userId: number;
  integrationId: number;
  scorecardConfigId?: number;
  importScope: RealCallsImportTaskScope;
  status: RealCallsImportTaskStatus;
  errorMsgForUser?: string;
  errorInfo?: string;
  runOnSingleTaskWorker?: boolean;
  taskWorkerId?: string;

  numberOfCalls: number;
  importedCalls: number;

  integration?: IntegrationDto;
  org: OrganizationDto;
  user: UserDto;

  calls?: RealCallsImportTaskCalls[];
}

export enum RealCallsImportTaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR',
}

export enum RealCallsImportTaskCallStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  DONE = 'DONE',
  ERROR = 'ERROR',
}

export interface RealCallsImportTaskCalls {
  id: number;
  taskId: number;
  providerCallId: string;
  status: RealCallsImportTaskCallStatus;
}

export interface CallsFromProviderResults {
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  calls: any[];
  records: {
    totalRecords: number;
    currentPageSize: number;
    currentPageNumber: number;
    cursor: string;
  };
}

export interface RealCallsDecisionMakerDto {
  decisionMaker: string;
  numTimesMentioned: number;
}

export interface RealCallsPublicPartiesDto {
  id: number;
  affiliation: string;
  name: string;
  email: string;
  user: {
    id: number;
    avatar: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface RealCallsPublicScorecardDto {
  notes: string;
  summary: string;
  objections: {
    objection: string;
    response: string;
    secondsFromStart: number;
  }[];
  questions: {
    question: string;
    secondsFromStart: number;
  }[];
}

export interface RealCallsActionItemsDto {
  id: number;
  realCallPartyId: number;
  action: string;
  isCompleted: boolean;
}

export interface RealCallsPublicDetailsDto {
  createdAt: Date;
  callDate: Date;
  callType: string;
  title: string;
  duration: number;
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transcript: any;
  status: RealCallStatus;
  scoringStatus: RealCallScoringStatus;
  org: {
    name: string;
  };
  integration: {
    provider: {
      companyName: string;
      logoUrl: string;
    };
  };
  caller: {
    id: number;
    avatar: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  decisionMakerMentions: RealCallsDecisionMakerDto[];
  parties: RealCallsPublicPartiesDto[];
  scorecard: RealCallsPublicScorecardDto;
  actionItems: RealCallsActionItemsDto[];
  mediaUrls?: RealCallMediaUrls;
}

export interface UpsertActionItemDto {
  realCallPartyId?: number;
  action?: string;
  isCompleted?: boolean;
}

export interface AllCategorizationRules {
  organization: RealCallOrganizationCategorizationRule[];
  teams: RealCallTeamCategorizationRule[];
  users: RealCallUserCategorizationRule[];
}

export type CustomCallType =
  | RealCallOrganizationCategorizationRule
  | RealCallTeamCategorizationRule
  | RealCallUserCategorizationRule;

export interface RealCallOrganizationCategorizationRule {
  id?: number;
  orgId?: number;
  label: string;
  explanation: string;
  callType: AgentCallType;
  scorecardConfigId: number;
  scorecardConfig?: ScorecardConfigDto;
}

export interface RealCallTeamCategorizationRule {
  id?: number;
  orgId?: number;
  teamId: number;
  label: string;
  explanation: string;
  callType: AgentCallType;
  scorecardConfigId: number;
  scorecardConfig?: ScorecardConfigDto;
  team?: TeamDto;
}

export interface RealCallUserCategorizationRule {
  id?: number;
  orgId?: number;
  userId: number;
  label: string;
  explanation: string;
  callType: AgentCallType;
  scorecardConfigId: number;
  scorecardConfig?: ScorecardConfigDto;
  user?: UserDto;
}

export type RealCallCategorizationRule =
  | RealCallOrganizationCategorizationRule
  | RealCallTeamCategorizationRule
  | RealCallUserCategorizationRule;

export interface AllPrivacyRules {
  organization: RealCallOrganizationPrivacyRule[];
  teams: RealCallTeamPrivacyRule[];
  users: RealCallUserPrivacyRule[];
}

export interface RealCallOrganizationPrivacyRule {
  id?: number;
  orgId?: number;
  ruleType: string;
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  settings: any;
}

export interface RealCallTeamPrivacyRule {
  id?: number;
  orgId?: number;
  teamId: number;
  ruleType: string;
  settings: PrivacyRuleSetting;
  team?: TeamDto;
}

export interface RealCallUserPrivacyRule {
  id?: number;
  orgId?: number;
  userId: number;
  ruleType: string;
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  settings: any;
  user?: UserDto;
}

export enum PrivacyRuleSettingType {
  CALL_TITLE = 'CALL_TITLE',
  PARTECIPANTS = 'PARTECIPANTS',
  MIN_DURATION = 'MIN_DURATION',
  MAX_DURATION = 'MAX_DURATION',
  SAVE_CALL = 'SAVE_CALL',
}
export interface PrivacyRuleSetting {
  // This should be removed in the future, right now the issue is that
  // we use it everywhere. Typing this would be a separate task in itself.
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}
