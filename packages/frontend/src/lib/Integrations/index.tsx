import { API } from '../Client';
import {
  Integration,
  IntegrationBasicAuthDetails,
  IntegrationBearerAuthDetails,
  IntegrationNangoAuthDetails,
  IntegrationOAuthDetails,
  IntegrationServiceType,
  IntegrationStatus,
  IntegrationTeamSettings,
  IntegrationUserSettings,
  ServiceProvider,
} from './types';
import RealCallsService from './RealCalls';

namespace IntegrationService {
  export function getProviderLogoUrl(logoUrl: string) {
    return logoUrl.startsWith('/')
      ? logoUrl
      : `/images/integrations/${logoUrl}`;
  }

  export async function updateDetails(
    integrationId: number,
    name: string,
    autoSyncEnabled?: boolean,
  ): Promise<void> {
    let res;

    const data: { name: string; autoSyncEnabled?: boolean } = {
      name,
    };
    if (autoSyncEnabled === false || autoSyncEnabled === true) {
      data.autoSyncEnabled = autoSyncEnabled;
    }

    try {
      res = await API.post(
        `/integrations/update-details/${integrationId}`,
        data,
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateIntegrationStatus(
    integrationId: number,
    status: IntegrationStatus,
  ): Promise<void> {
    let res;
    try {
      res = await API.patch(`/integrations/update-status/${integrationId}`, {
        status,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
  export async function upsertUserSettings(
    settings: IntegrationUserSettings,
  ): Promise<IntegrationUserSettings> {
    let res;
    try {
      res = await API.post(`/integrations/user-settings`, {
        id: settings.id,
        userId: settings.userId,
        integrationId: settings.integrationId,
        externalUserId: settings.externalUserId,
        callType: settings.callType,
        scorecardConfigId: settings.scorecardConfigId || undefined,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function upsertTeamSettings(
    settings: IntegrationTeamSettings,
  ): Promise<IntegrationTeamSettings> {
    let res;
    try {
      res = await API.post(`/integrations/team-settings`, {
        teamId: settings.teamId,
        integrationId: settings.integrationId,
        callType: settings.callType,
        scorecardConfigId: settings.scorecardConfigId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getUsersForService(
    serviceId: number,
  ): Promise<ServiceProvider[]> {
    let res;
    try {
      res = await API.get(`/integrations/service-providers/users/${serviceId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getServiceProviders(
    it: IntegrationServiceType,
  ): Promise<ServiceProvider[]> {
    let res;
    try {
      res = await API.get(`/integrations/service-providers/${it}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getIntegrations(): Promise<Integration[]> {
    let res;
    try {
      res = await API.get(`/integrations/`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getIntegration(id: number): Promise<Integration> {
    let res;
    try {
      res = await API.get(`/integrations/${id}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function upsertIntegration(
    name: string,
    id?: number,
    type?: IntegrationServiceType,
    providerId?: number,
    basicAuthDetails?: Omit<IntegrationBasicAuthDetails, 'integrationId'>,
    bearerAuthDetails?: Omit<IntegrationBearerAuthDetails, 'integrationId'>,
    oauthDetails?: Omit<IntegrationOAuthDetails, 'integrationId'>,
    nangoAuthDetails?: Omit<IntegrationNangoAuthDetails, 'integrationId'>,
  ): Promise<Integration> {
    let res;
    try {
      res = await API.post(`/integrations/`, {
        id,
        name,
        type,
        providerId,
        basicAuthDetails,
        bearerAuthDetails,
        oauthDetails,
        nangoAuthDetails,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export { RealCallsService };
export default IntegrationService;
