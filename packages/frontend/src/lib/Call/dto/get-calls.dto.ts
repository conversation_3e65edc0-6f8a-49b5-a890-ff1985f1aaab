import { FilterType } from '../../../common/Calls/AIRoleplay/List/common';
import { AgentCallType } from '../../Agent/types';

export type FilterBy = {
  [FilterType.BUYERS]?: number[];
  [FilterType.REPS]?: number[];
  [FilterType.PLAYLISTS]?: number[];
  [FilterType.DATE]?: {
    fromDate: Date;
    toDate: Date;
  };
  [FilterType.CALL_TYPES]?: AgentCallType[];
  [FilterType.TAGS]?: number[];
  [FilterType.CALL_BLITZ]?: number[];
  [FilterType.TEAMS]?: number[];
  [FilterType.LANGUAGES]?: string[];
  [FilterType.SCORECARDS]?: number[];
  [FilterType.SCORECARDS_SECTIONS]?: string[];
  [FilterType.SCORECARDS_CRITERIONS]?: string[];
  [FilterType.SCORECARDS_CRITERIONS_STATUS]?: string[];
  [FilterType.SEARCH]?: string;
};
