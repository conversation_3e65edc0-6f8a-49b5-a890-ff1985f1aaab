import { LearningMaterialDto } from "@/lib/ScorecardConfig/types";

export interface SimilarCallDto {
  callVapiId: string;
  callId: number;
  firstName: string;
  lastName: string;
  callStartedAt: string;
  callEndedAt: string;
  callerId: number;
  avatar?: string;
}

export interface CriterionCoachingInfoDto {
  criteria: string;
  passed: boolean;
  sectionTitle: string;
  similarCalls: SimilarCallDto[];
  coaching?: string;
  improvement?: string;
}

export interface CoachingResourceDto {
  similarCalls: SimilarCallDto[];
  learningMaterials: LearningMaterialDto[];
}

export interface PastPerformanceDto {
  id: number;
  vapiId: string;
  createdAt: Date;
  finalCallScore: number;
}