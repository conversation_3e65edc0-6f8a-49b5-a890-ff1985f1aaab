import { ISection } from '@/common/Calls/AIRoleplay/NewSummary/tabs/feedback/scorecardSection';
import { AgentDto } from '../Agent/types';
import { DemoInboundFormResponseDto } from '../Demo/types';
import { ExternalFile } from '../Integrations/RealCalls/types';
import { PlaylistDto } from '../Playlist/types';
import ScorecardConfigDto from '../ScorecardConfig/types';
import { UserDto } from '../User/types';

export interface VapiCallCustomer {
  number: string;
}

export interface VapiCallMessage {
  role: string;
  time: number;
  message: string;
  secondsFromStart: number;
}

export interface VapiCallMetadata {
  id: string;
  assistantId: string;
  phoneNumberId?: string;
  type: string;
  startedAt: string;
  endedAt: string;
  transcript: string;
  recordingUrl: string;
  summary: string;
  createdAt: string;
  updatedAt: string;
  orgId: string;
  cost: number;
  twilioCallSid?: string;
  customerId?: string;
  customer?: VapiCallCustomer;
  status: string;
  endedReason: string;
  messages: VapiCallMessage[];
  webCallUrl?: string;
  artifact?: unknown;
}

export enum CallScoringStatus {
  PENDING = 'PENDING',
  SCORING = 'SCORING',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR',
}

export enum ExcludeFromAnalyticsReasonType {
  USER_INPUT = 'USER_INPUT',
  ANALYTICS_MINIMUM_REQUIREMENTS = 'ANALYTICS_MINIMUM_REQUIREMENTS',
}

export interface CallDto {
  id: number;
  vapiId: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  startedAt: string;
  endedAt: string;
  orgId: number;
  callerId: number;
  caller?: UserDto;
  agentId: number;
  demoInboundFormResponseId: number;
  demoInboundFormResponse?: DemoInboundFormResponseDto;
  status: string;
  type: string;
  agent?: AgentDto;
  vapiMetadata?: VapiCallMetadata;
  scorecard: {
    totalScore: number;
    passedScore: number;
    aggregateScore: number;
    finalCallScore: number;
    criteria: ISection[];
    scorecardConfigId: number;
  };
  archived: boolean;
  buggedOut: boolean;
  bugDescription?: string;
  excludeFromAnalytics: boolean;
  excludeFromAnalyticsReason?: string;
  excludeFromAnalyticsReasonType?: ExcludeFromAnalyticsReasonType;
  playlists: PlaylistDto[];
  relatedGatekeeperCallId?: number;
  relatedGatekeeperCall?: CallDto;
  relatedAgentCall?: CallDto;
  vapiCallEndedReason?: string;
  scoringStatus: CallScoringStatus;
  scoringInfo?: string;
  mediaUrls?: CallMediaUrls;
  scorecardConfigName?: string;
  comments?: CallCommentDto[];
}

export interface CallCommentDto {
  id: number;
  callId: number;
  content: string;
  userId: number;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
  };
}

export type CallMediaUrls = {
  audio?: ExternalFile;
  video?: ExternalFile;
};

export interface CallStatsDto {
  talkListenRatio: {
    value: number;
    recommendedRange: number[];
  };
  fillerWords: {
    value: number;
    recommendedRange: number[];
  };
  talkSpeed: {
    value: number;
    recommendedRange: number[];
  };
  longestMonologue: {
    value: number;
    recommendedRange: number[];
  };
}

export interface CallObjectionDto {
  objection: string;
  response: string;
  secondsFromStart: number;
}

export interface CallQuestionDto {
  question: string;
  secondsFromStart: number;
}

export type CallScorecardInnerScorecard = {
  criteria: {
    passed: boolean;
    coaching?: string;
    criterion: string;
    explanation: string;
  }[];
  description: string;
  sectionTitle: string;
  totalCriteriaCount: number;
  passedCriteriaCount: number;
};

export interface CallScorecardDto {
  scorecards: ScorecardConfigDto[];
  totalScore: number;
  passedScore: number;
  aggregateScore: number;
  scorecardConfigId?: number;
}

export interface CallAggregatedScorecardDto
  extends CallScorecardDto,
    CallStatsDto {}
export type CallAggregatedScorecardListDto = {
  [callVapiId: string]: CallAggregatedScorecardDto;
};
