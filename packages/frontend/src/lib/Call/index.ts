import { CallsSortingParam } from '../../common/Calls/AIRoleplay/List/common';
import { API, API_BLOB } from '../Client';
import {
  CallAggregatedScorecardDto,
  CallAggregatedScorecardListDto,
  CallDto,
  CallObjectionDto,
  CallQuestionDto,
  CallScorecardDto,
  CallStatsDto,
  VapiCallMetadata,
} from './types';
import { FilterBy } from './dto/get-calls.dto';
import { CriterionCoachingInfoDto } from './dto/coaching-info.dto';

const VAPI_ENDED_REASONS_OK_STATES = [
  'customer-ended-call',
  'customer-ended',
  'silence-timeout',
  'silence-timed-out',
  'assistant-ended-call',
  'manually-canceled',
  'assistant-said-end-call-phrase',
  'assistant-forwarded-call',
];

const VAPI_ENDED_REASONS_NOT_OK_STATES: { [state: string]: string } = {
  'max-duration-timeout': 'Max allowed call duration exceeded',
  'exceeded-max-duration': 'Max allowed call duration exceeded',
  'assistant-join-timeout':
    'We have encountered an error, please try a new call',
  'server-shutdow': 'We have encountered an error, please try a new call',
  'unknown-error': 'We have encountered an error, please try a new call',
  'customer-did-not-give-microphone-permission':
    'Customer did not give microphone permission',
  'pipeline-error-first-message-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-playht-voice-failed':
    'We have encountered a problem width the voice associated with this bot. Our team has been notified. Meanwhile, you can try calling again or changing the bot voice.',
  'pipeline-error-eleven-labs-voice-failed':
    'We have encountered a problem width the voice associated with this bot. Our team has been notified. Meanwhile, you can try calling again or changing the bot voice.',
  'pipeline-error-cartesia-voice-failed':
    'We have encountered a problem width the voice associated with this bot. Our team has been notified. Meanwhile, you can try calling again or changing the bot voice.',
  'pipeline-error-eleven-labs-voice-disabled-by-owner':
    'We have encountered a problem width the voice associated with this bot. Our team has been notified. Meanwhile, you can try calling again or changing the bot voice.',
  'pipeline-error-playht-request-timed-out':
    'We have encountered an error, please try a new call',
  'pipeline-error-openai-llm-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-azure-openai-llm-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-anthropic-llm-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-deepgram-transcriber-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-eleven-labs-quota-exceeded':
    'We have encountered an error, please try a new call',
  'pipeline-error-eleven-labs-blocked-content-against-their-policy':
    'It seems like the content of this conversation is against our policy. Please try a new call.',
  'pipeline-error-deepgram-returning-502-network-error':
    'We have encountered an error, please try a new call',
  'pipeline-error-eleven-labs-system-busy-and-requested-upgrade':
    'We have encountered an error, please try a new call',
  'assistant-join-timed-out':
    'We have encountered an error, please try a new call',
  'phone-call-provider-closed-websocket':
    'We have encountered an error, please try a new call',
  'worker-shutdown':
    'We have encountered an error, Our team has been notified. Please try a new call',
  'pipeline-error-eleven-labs-500-server-error':
    'We have encountered an error, please try a new call',
};

namespace CallService {
  // DEMO ROUTES

  export async function createDemoCall({
    vapiId,
    assistantId,
    createdAt,
    updatedAt,
    type,
    status,
    demoInboundFormResponseId,
    vapiMetadata,
  }: {
    vapiId: string;
    assistantId: string;
    createdAt: string;
    updatedAt: string;
    type: string;
    status: string;
    demoInboundFormResponseId?: number;
    vapiMetadata: VapiCallMetadata;
  }): Promise<CallDto> {
    let res;
    try {
      res = await API.post('/calls/demo', {
        vapiId,
        assistantId,
        createdAt,
        updatedAt,
        type,
        status,
        demoInboundFormResponseId,
        vapiMetadata,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getDemoCalls(
    demoInboundFormResponseId: number,
    from: number = 0,
    numberOfResults: number = 20,
    sortBy: CallsSortingParam[],
    filterBy: FilterBy,
  ): Promise<CallDto[]> {
    let res;
    let sb = '';
    if (sortBy) {
      sb = JSON.stringify(sortBy);
    }
    try {
      res = await API.get('/calls/demo', {
        params: {
          demoInboundFormResponseId,
          from,
          numberOfResults,
          sortBy: sb,
          filterBy,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getCountDemoCalls(
    demoInboundFormResponseId: number,
    filterBy: FilterBy,
  ): Promise<number> {
    let res;

    try {
      res = await API.get('/calls/demo/count-calls', {
        params: { demoInboundFormResponseId, filterBy },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getDemoCall(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallDto> {
    console.log('Fetching call for call id:', callVapiId);
    const res = await API.get(`/calls/demo/vapiId/${callVapiId}`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function getDemoCallStats(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallStatsDto> {
    console.log('Fetching call stats for call id:', callVapiId);
    const res = await API.get(`/calls/demo/vapiId/${callVapiId}/stats`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function getDemoCallScorecard(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallScorecardDto> {
    console.log('Fetching call scorecard for call id:', callVapiId);
    const res = await API.get(`/calls/demo/vapiId/${callVapiId}/scorecard`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function getDemoCallQuestions(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallQuestionDto[]> {
    console.log('Fetching call questions for call id:', callVapiId);
    const res = await API.get(`/calls/demo/vapiId/${callVapiId}/questions`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    const questions = res?.data || [];
    return questions.filter(
      (question: CallQuestionDto) =>
        question?.question?.toLowerCase() !== 'none',
    );
  }

  export async function getDemoCallObjections(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallObjectionDto[]> {
    console.log('Fetching call objections for call id:', callVapiId);
    const res = await API.get(`/calls/demo/vapiId/${callVapiId}/objections`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    const objections = res?.data || [];
    return objections.filter(
      (objection: CallObjectionDto) =>
        objection?.objection?.toLowerCase() !== 'none',
    );
  }

  export async function deleteDemoCall({
    callVapiId,
    demoInboundFormResponseId,
  }: {
    callVapiId: string;
    demoInboundFormResponseId: number;
  }): Promise<void> {
    console.log('Deleting call for call id:', callVapiId);
    await API.delete(`/calls/demo/vapiId/${callVapiId}`, {
      params: {
        demoInboundFormResponseId,
      },
    });
  }

  export async function deleteDemoCalls({
    callIds,
    demoInboundFormResponseId,
  }: {
    callIds: number[];
    demoInboundFormResponseId: number;
  }) {
    console.log('Deleting calls for call ids:', callIds.join(','));
    await API.delete(`/calls/demo`, {
      data: {
        demoInboundFormResponseId,
        callIds,
      },
    });
  }

  export async function toggleReportBugForDemoCall({
    vapiId,
    buggedOut,
    bugDescription,
    demoInboundFormResponseId,
  }: {
    vapiId: string;
    buggedOut: boolean;
    bugDescription?: string;
    demoInboundFormResponseId: number;
  }): Promise<boolean> {
    let res;
    try {
      res = await API.patch(`/calls/demo/vapiId/${vapiId}/report-call-or-bug`, {
        buggedOut,
        bugDescription,
        demoInboundFormResponseId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  // AUTHENTICATED ROUTES

  export async function deleteCall({
    callVapiId,
  }: {
    callVapiId: string;
  }): Promise<void> {
    console.log('Deleting call for call id:', callVapiId);
    await API.delete(`/calls/vapiId/${callVapiId}`);
  }

  export async function deleteCalls({
    callIds,
  }: {
    callIds: number[];
  }): Promise<void> {
    console.log('Deleting calls for call ids:', callIds.join(','));
    await API.delete(`/calls`, {
      data: {
        callIds,
      },
    });
  }

  export async function createCall({
    vapiId,
    assistantId,
    createdAt,
    updatedAt,
    type,
    status,
    vapiMetadata,
    callBlitzSessionId,
  }: {
    vapiId: string;
    assistantId: string;
    createdAt: string;
    updatedAt: string;
    type: string;
    status: string;
    vapiMetadata: VapiCallMetadata;
    callBlitzSessionId?: number;
  }): Promise<CallDto> {
    let res;
    try {
      res = await API.post('/calls', {
        vapiId,
        assistantId,
        createdAt,
        updatedAt,
        type,
        status,
        vapiMetadata,
        callBlitzSessionId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCall(callVapiId: string): Promise<CallDto> {
    console.log('Fetching call stats for call id:', callVapiId);
    const res = await API.get(`/calls/vapiId/${callVapiId}`);
    return res?.data;
  }

  export async function getCallStats(
    callVapiId: string,
  ): Promise<CallStatsDto> {
    console.log('Fetching call stats for call id:', callVapiId);
    const res = await API.get(`/calls/vapiId/${callVapiId}/stats`);
    return res?.data;
  }

  export async function getCallScorecard(
    callVapiId: string,
  ): Promise<CallScorecardDto> {
    console.log('Fetching call scorecard for call id:', callVapiId);
    const res = await API.get(`/calls/vapiId/${callVapiId}/scorecard`);
    return res?.data;
  }

  export async function getCallAggregatedScorecard(
    callVapiId: string,
  ): Promise<CallAggregatedScorecardDto> {
    const res = await API.get<CallAggregatedScorecardDto>(
      `/calls/vapiId/${callVapiId}/aggregated-scorecard`,
    );
    return res?.data;
  }

  export async function getCallAggregatedScorecards(
    callVapiIds: string[],
  ): Promise<CallAggregatedScorecardListDto> {
    if (!callVapiIds.length) {
      return {};
    }
    const res = await API.get<CallAggregatedScorecardListDto>(
      `/calls/aggregated-scorecards`,
      {
        params: {
          callVapiIds,
        },
      },
    );
    return res?.data;
  }

  export async function getCallQuestions(
    callVapiId: string,
  ): Promise<CallQuestionDto[]> {
    console.log('Fetching call questions for call id:', callVapiId);
    const res = await API.get(`/calls/vapiId/${callVapiId}/questions`);
    const questions = res?.data || [];
    return questions.filter(
      (question: CallQuestionDto) =>
        question?.question?.toLowerCase() !== 'none',
    );
  }

  export async function getCallObjections(
    callVapiId: string,
  ): Promise<CallObjectionDto[]> {
    console.log('Fetching call objections for call id:', callVapiId);
    const res = await API.get(`/calls/vapiId/${callVapiId}/objections`);
    const objections = res?.data || [];
    return objections.filter(
      (objection: CallObjectionDto) =>
        objection?.objection?.toLowerCase() !== 'none',
    );
  }

  export async function downloadOrgCalls(filterBy: FilterBy): Promise<string> {
    let res;

    try {
      res = await API.get('/calls/download-calls', { params: { filterBy } });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getOrgCalls(
    from: number = 0,
    numberOfResults: number = 20,
    sortBy: CallsSortingParam[],
    filterBy?: FilterBy,
  ): Promise<CallDto[]> {
    let res;

    try {
      res = await API.post('/calls/get-calls', {
        from,
        numberOfResults,
        sortBy,
        filterBy,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getCountCalls(filterBy: FilterBy): Promise<number> {
    let res;

    try {
      res = await API.post('/calls/count-calls', { filterBy });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function toggleScorecardCriteriaForCall({
    vapiId,
    sectionTitle,
    criterion,
  }: {
    vapiId: string;
    sectionTitle: string;
    criterion: string;
  }): Promise<CallScorecardDto> {
    let res;
    try {
      res = await API.patch(
        `/calls/vapiId/${vapiId}/toggle-scorecard-criteria`,
        {
          sectionTitle,
          criterion,
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function toggleDisputedScorecardCriteriaForCall({
    vapiId,
    sectionTitle,
    criterion,
  }: {
    vapiId: string;
    sectionTitle: string;
    criterion: string;
  }): Promise<CallScorecardDto> {
    let res;
    try {
      res = await API.patch(
        `/calls/vapiId/${vapiId}/toggle-disputed-scorecard-criteria`,
        {
          sectionTitle,
          criterion,
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function reportBugOrExcludeFromAnalyticsForCall({
    vapiId,
    buggedOut,
    bugDescription,
    excludeFromAnalytics,
    excludeFromAnalyticsReason,
  }: {
    vapiId: string;
    buggedOut: boolean;
    bugDescription?: string;
    excludeFromAnalytics: boolean;
    excludeFromAnalyticsReason: string;
  }): Promise<boolean> {
    let res;
    try {
      res = await API.patch(`/calls/vapiId/${vapiId}/report-call-or-bug`, {
        buggedOut,
        bugDescription,
        excludeFromAnalytics,
        excludeFromAnalyticsReason,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getDemoAudioCall(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ) {
    let res;
    try {
      res = await API_BLOB.get(`/calls/demo/audio/${callVapiId}`, {
        params: {
          demoInboundFormResponseId,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getAudioCall(callVapiId: string) {
    let res;
    try {
      res = await API_BLOB.get(`/calls/audio/${callVapiId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function rescoreCall(callVapiId: string): Promise<boolean> {
    let res;
    try {
      res = await API.post(`/calls/rescore/${callVapiId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getCallCoaching(
    callVapiId: string,
  ): Promise<CriterionCoachingInfoDto[]> {
    const res = await API.get(`/calls/coaching/${callVapiId}`);
    return res?.data;
  }

  export async function getDemoCallCoaching(
    callVapiId: string,
    demoInboundFormResponseId?: number,
  ): Promise<CriterionCoachingInfoDto[]> {
    const res = await API.get(`/calls/demo/coaching/${callVapiId}`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function linkGatekeeperCallToAgentCall(
    gatekeeperCallId: number,
    agentCallId: number,
  ): Promise<CallDto> {
    let res;

    try {
      res = await API.post(`/calls/link-gatekeepercall-to-agent-call`, {
        gatekeeperCallId,
        agentCallId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export function getVapiEndedReasonInfo(vapiCallEndedReason: string): {
    isError: boolean;
    message: string;
  } {
    if (VAPI_ENDED_REASONS_OK_STATES.indexOf(vapiCallEndedReason) > -1) {
      return { isError: false, message: '' };
    }

    if (VAPI_ENDED_REASONS_NOT_OK_STATES[vapiCallEndedReason]) {
      return {
        isError: true,
        message: VAPI_ENDED_REASONS_NOT_OK_STATES[vapiCallEndedReason],
      };
    }

    return { isError: false, message: '' };
  }
}

export default CallService;
