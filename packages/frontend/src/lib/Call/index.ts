import { CallsSortingParam } from '../../common/Calls/AIRoleplay/List/common';
import { API, API_BLOB } from '../Client';
import {
  CallAggregatedScorecardDto,
  CallAggregatedScorecardListDto,
  CallCommentDto,
  CallDto,
  CallMediaUrls,
  CallObjectionDto,
  CallQuestionDto,
  CallScorecardDto,
  CallStatsDto,
  CallMetadata,
} from './types';
import { FilterBy } from './dto/get-calls.dto';
import {
  CoachingResourceDto,
  CriterionCoachingInfoDto,
  PastPerformanceDto,
} from './dto/coaching-info.dto';
import { FilterState } from '@/common/Calls/AIRoleplay/List';

const VAPI_ENDED_REASONS_OK_STATES = [
  'customer-ended-call',
  'customer-ended',
  'silence-timeout',
  'silence-timed-out',
  'assistant-ended-call',
  'manually-canceled',
  'assistant-said-end-call-phrase',
  'assistant-forwarded-call',
];

const VAPI_ENDED_REASONS_NOT_OK_STATES: { [state: string]: string } = {
  'max-duration-timeout': 'Max allowed call duration exceeded',
  'exceeded-max-duration': 'Max allowed call duration exceeded',
  'assistant-join-timeout':
    'We have encountered an error, please try a new call',
  'server-shutdow': 'We have encountered an error, please try a new call',
  'unknown-error': 'We have encountered an error, please try a new call',
  'customer-did-not-give-microphone-permission':
    'Customer did not give microphone permission',
  'pipeline-error-first-message-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-playht-voice-failed':
    'We have encountered a problem width the voice associated with this bot. Our team has been notified. Meanwhile, you can try calling again or changing the bot voice.',
  'pipeline-error-eleven-labs-voice-failed':
    'We have encountered a problem width the voice associated with this bot. Our team has been notified. Meanwhile, you can try calling again or changing the bot voice.',
  'pipeline-error-cartesia-voice-failed':
    'We have encountered a problem width the voice associated with this bot. Our team has been notified. Meanwhile, you can try calling again or changing the bot voice.',
  'pipeline-error-eleven-labs-voice-disabled-by-owner':
    'We have encountered a problem width the voice associated with this bot. Our team has been notified. Meanwhile, you can try calling again or changing the bot voice.',
  'pipeline-error-playht-request-timed-out':
    'We have encountered an error, please try a new call',
  'pipeline-error-openai-llm-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-azure-openai-llm-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-anthropic-llm-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-deepgram-transcriber-failed':
    'We have encountered an error, please try a new call',
  'pipeline-error-eleven-labs-quota-exceeded':
    'We have encountered an error, please try a new call',
  'pipeline-error-eleven-labs-blocked-content-against-their-policy':
    'It seems like the content of this conversation is against our policy. Please try a new call.',
  'pipeline-error-deepgram-returning-502-network-error':
    'We have encountered an error, please try a new call',
  'pipeline-error-eleven-labs-system-busy-and-requested-upgrade':
    'We have encountered an error, please try a new call',
  'assistant-join-timed-out':
    'We have encountered an error, please try a new call',
  'phone-call-provider-closed-websocket':
    'We have encountered an error, please try a new call',
  'worker-shutdown':
    'We have encountered an error, Our team has been notified. Please try a new call',
  'pipeline-error-eleven-labs-500-server-error':
    'We have encountered an error, please try a new call',
};

namespace CallService {
  // DEMO ROUTES

  export async function createDemoCall({
    vapiId,
    assistantId,
    createdAt,
    updatedAt,
    type,
    status,
    demoInboundFormResponseId,
    vapiMetadata,
  }: {
    vapiId?: string;
    assistantId: string;
    createdAt: string;
    updatedAt: string;
    type: string;
    status: string;
    demoInboundFormResponseId?: number;
    vapiMetadata?: CallMetadata;
  }): Promise<CallDto> {
    let res;
    try {
      res = await API.post('/calls/demo', {
        vapiId,
        assistantId,
        createdAt,
        updatedAt,
        type,
        status,
        demoInboundFormResponseId,
        vapiMetadata,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getDemoCalls(
    demoInboundFormResponseId: number,
    from: number = 0,
    numberOfResults: number = 20,
    sortBy: CallsSortingParam[],
    filterBy: FilterBy,
  ): Promise<CallDto[]> {
    let res;
    let sb = '';
    if (sortBy) {
      sb = JSON.stringify(sortBy);
    }
    try {
      res = await API.get('/calls/demo', {
        params: {
          demoInboundFormResponseId,
          from,
          numberOfResults,
          sortBy: sb,
          filterBy,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getCountDemoCalls(
    demoInboundFormResponseId: number,
    filterBy: FilterBy,
  ): Promise<number> {
    let res;

    try {
      res = await API.get('/calls/demo/count-calls', {
        params: { demoInboundFormResponseId, filterBy },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getDemoCall(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallDto> {
    console.log('Fetching call for call id:', callVapiId);
    const res = await API.get(`/calls/demo/vapiId/${callVapiId}`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function getDemoCallStats(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallStatsDto> {
    console.log('Fetching call stats for call id:', callVapiId);
    const res = await API.get(`/calls/demo/vapiId/${callVapiId}/stats`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function getDemoCallScorecard(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallScorecardDto> {
    console.log('Fetching call scorecard for call id:', callVapiId);
    const res = await API.get(`/calls/demo/vapiId/${callVapiId}/scorecard`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function getDemoCallQuestions(
    callId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallQuestionDto[]> {
    console.log('Fetching call questions for call id:', callId);
    const res = await API.get(`/calls/demo/vapiId/${callId}/questions`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    const questions = res?.data || [];
    return questions.filter(
      (question: CallQuestionDto) =>
        question?.question?.toLowerCase() !== 'none',
    );
  }

  export async function getDemoCallObjections(
    callId: string,
    demoInboundFormResponseId: number,
  ): Promise<CallObjectionDto[]> {
    console.log('Fetching call objections for call id:', callId);
    const res = await API.get(`/calls/demo/vapiId/${callId}/objections`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    const objections = res?.data || [];
    return objections.filter(
      (objection: CallObjectionDto) =>
        objection?.objection?.toLowerCase() !== 'none',
    );
  }

  export async function deleteDemoCall({
    callVapiId,
    demoInboundFormResponseId,
  }: {
    callVapiId: string;
    demoInboundFormResponseId: number;
  }): Promise<void> {
    console.log('Deleting call for call id:', callVapiId);
    await API.delete(`/calls/demo/vapiId/${callVapiId}`, {
      params: {
        demoInboundFormResponseId,
      },
    });
  }

  export async function deleteDemoCalls({
    callIds,
    demoInboundFormResponseId,
  }: {
    callIds: number[];
    demoInboundFormResponseId: number;
  }) {
    console.log('Deleting calls for call ids:', callIds.join(','));
    await API.delete(`/calls/demo`, {
      data: {
        demoInboundFormResponseId,
        callIds,
      },
    });
  }

  export async function toggleReportBugForDemoCall({
    vapiId,
    buggedOut,
    bugDescription,
    demoInboundFormResponseId,
  }: {
    vapiId: string;
    buggedOut: boolean;
    bugDescription?: string;
    demoInboundFormResponseId: number;
  }): Promise<boolean> {
    let res;
    try {
      res = await API.patch(`/calls/demo/vapiId/${vapiId}/report-call-or-bug`, {
        buggedOut,
        bugDescription,
        demoInboundFormResponseId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  // AUTHENTICATED ROUTES

  export async function deleteCall({
    callId,
  }: {
    callId: string;
  }): Promise<void> {
    console.log('Deleting call for call id:', callId);
    await API.delete(`/calls/callId/${callId}`);
  }

  export async function deleteCalls({
    callIds,
  }: {
    callIds: number[];
  }): Promise<void> {
    console.log('Deleting calls for call ids:', callIds.join(','));
    await API.delete(`/calls`, {
      data: {
        callIds,
      },
    });
  }

  export async function createCall({
    vapiId,
    assistantId,
    createdAt,
    updatedAt,
    type,
    status,
    vapiMetadata,
    callBlitzSessionId,
    providerCallId,
  }: {
    vapiId?: string | undefined;
    assistantId: string;
    createdAt: string;
    updatedAt: string;
    type: string;
    status: string;
    vapiMetadata?: CallMetadata | undefined;
    callBlitzSessionId?: number;
    providerCallId?: string;
  }): Promise<CallDto> {
    let res;
    try {
      res = await API.post('/calls', {
        vapiId,
        assistantId,
        createdAt,
        updatedAt,
        type,
        status,
        vapiMetadata,
        callBlitzSessionId,
        providerCallId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getCall(callId: string): Promise<CallDto> {
    console.log('Fetching call for call id:', callId);
    const res = await API.get(`/calls/callId/${callId}`);
    return res?.data;
  }

  export async function getCallStats(callId: string): Promise<CallStatsDto> {
    console.log('Fetching call stats for call id:', callId);
    const res = await API.get(`/calls/callId/${callId}/stats`);
    return res?.data;
  }

  export async function getCallScorecard(
    callId: string,
  ): Promise<CallScorecardDto> {
    console.log('Fetching call scorecard for call id:', callId);
    const res = await API.get(`/calls/callId/${callId}/scorecard`);
    return res?.data;
  }

  export async function getCallAggregatedScorecard(
    callVapiId: string,
  ): Promise<CallAggregatedScorecardDto> {
    const res = await API.get<CallAggregatedScorecardDto>(
      `/calls/vapiId/${callVapiId}/aggregated-scorecard`,
    );
    return res?.data;
  }

  export async function getCallAggregatedScorecards(
    callVapiIds: string[],
  ): Promise<CallAggregatedScorecardListDto> {
    if (!callVapiIds.length) {
      return {};
    }
    const res = await API.get<CallAggregatedScorecardListDto>(
      `/calls/aggregated-scorecards`,
      {
        params: {
          callVapiIds,
        },
      },
    );
    return res?.data;
  }

  export async function getCallQuestions(
    callId: string,
  ): Promise<CallQuestionDto[]> {
    console.log('Fetching call questions for call id:', callId);
    const res = await API.get(`/calls/callId/${callId}/questions`);
    const questions = res?.data || [];
    return questions.filter(
      (question: CallQuestionDto) =>
        question?.question?.toLowerCase() !== 'none',
    );
  }

  export async function getCallObjections(
    callId: string,
  ): Promise<CallObjectionDto[]> {
    console.log('Fetching call objections for call id:', callId);
    const res = await API.get(`/calls/callId/${callId}/objections`);
    const objections = res?.data || [];
    return objections.filter(
      (objection: CallObjectionDto) =>
        objection?.objection?.toLowerCase() !== 'none',
    );
  }

  export async function downloadOrgCalls(
    filterBy: FilterState,
  ): Promise<string> {
    let res;

    try {
      res = await API.get('/calls/download-calls', { params: { filterBy } });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getOrgCalls(
    from: number = 0,
    numberOfResults: number = 20,
    sortBy: CallsSortingParam[],
    filterBy?: FilterBy,
  ): Promise<CallDto[]> {
    let res;

    try {
      res = await API.post('/calls/get-calls', {
        from,
        numberOfResults,
        sortBy,
        filterBy,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getCountCalls(filterBy: FilterBy): Promise<number> {
    let res;

    try {
      res = await API.post('/calls/count-calls', { filterBy });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function toggleScorecardCriteriaForCall({
    callId,
    sectionTitle,
    criterion,
  }: {
    callId: string;
    sectionTitle: string;
    criterion: string;
  }): Promise<CallScorecardDto> {
    let res;
    try {
      res = await API.patch(
        `/calls/callId/${callId}/toggle-scorecard-criteria`,
        {
          sectionTitle,
          criterion,
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function toggleDisputedScorecardCriteriaForCall({
    callId,
    sectionTitle,
    criterion,
  }: {
    callId: string;
    sectionTitle: string;
    criterion: string;
  }): Promise<CallScorecardDto> {
    let res;
    try {
      res = await API.patch(
        `/calls/callId/${callId}/toggle-disputed-scorecard-criteria`,
        {
          sectionTitle,
          criterion,
        },
      );
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function reportBugOrExcludeFromAnalyticsForCall({
    callId,
    buggedOut,
    bugDescription,
    excludeFromAnalytics,
    excludeFromAnalyticsReason,
  }: {
    callId: string;
    buggedOut: boolean;
    bugDescription?: string;
    excludeFromAnalytics: boolean;
    excludeFromAnalyticsReason: string;
  }): Promise<boolean> {
    let res;
    try {
      res = await API.patch(`/calls/callId/${callId}/report-call-or-bug`, {
        buggedOut,
        bugDescription,
        excludeFromAnalytics,
        excludeFromAnalyticsReason,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function getDemoAudioCall(
    callVapiId: string,
    demoInboundFormResponseId: number,
  ) {
    let res;
    try {
      res = await API_BLOB.get(`/calls/demo/audio/${callVapiId}`, {
        params: {
          demoInboundFormResponseId,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getAudioCall(callId: string) {
    let res;
    try {
      res = await API_BLOB.get(`/calls/audio/${callId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function rescoreCall(callId: string): Promise<boolean> {
    let res;
    try {
      res = await API.post(`/calls/rescore/${callId}`);
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export async function getCallCoaching(
    callVapiId: string,
  ): Promise<CriterionCoachingInfoDto[]> {
    const res = await API.get(`/calls/coaching/${callVapiId}`);
    return res?.data;
  }

  export async function getCallCoachingResources(
    callId: string,
    agentId: number,
    scorecardConfigId: number,
    criteria: string,
    finalCallScore: number,
  ): Promise<CoachingResourceDto> {
    const res = await API.get(`/calls/coaching/${callId}/resources`, {
      params: {
        agentId,
        scorecardConfigId,
        criteria,
        finalCallScore,
      },
    });
    return res?.data;
  }

  export async function getAgentPastPerformanceForCaller(
    agentId: number,
    callerId: number,
  ): Promise<PastPerformanceDto[]> {
    const res = await API.get(`/calls/agent/caller-performance`, {
      params: {
        agentId,
        callerId,
      },
    });
    return res?.data;
  }

  export async function getDemoAgentPastPerformanceForCaller(
    agentId: number,
    demoInboundFormResponseId: number,
  ): Promise<PastPerformanceDto[]> {
    const res = await API.get(`/calls/demo/agent/caller-performance`, {
      params: {
        agentId,
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function getDemoCallCoaching(
    callVapiId: string,
    demoInboundFormResponseId?: number,
  ): Promise<CriterionCoachingInfoDto[]> {
    const res = await API.get(`/calls/demo/coaching/${callVapiId}`, {
      params: {
        demoInboundFormResponseId,
      },
    });
    return res?.data;
  }

  export async function linkGatekeeperCallToAgentCall(
    gatekeeperCallId: number,
    agentCallId: number,
  ): Promise<CallDto> {
    let res;

    try {
      res = await API.post(`/calls/link-gatekeepercall-to-agent-call`, {
        gatekeeperCallId,
        agentCallId,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data || [];
  }

  export function getVapiEndedReasonInfo(vapiCallEndedReason: string): {
    isError: boolean;
    message: string;
  } {
    if (VAPI_ENDED_REASONS_OK_STATES.indexOf(vapiCallEndedReason) > -1) {
      return { isError: false, message: '' };
    }

    if (VAPI_ENDED_REASONS_NOT_OK_STATES[vapiCallEndedReason]) {
      return {
        isError: true,
        message: VAPI_ENDED_REASONS_NOT_OK_STATES[vapiCallEndedReason],
      };
    }

    return { isError: false, message: '' };
  }

  export async function getMissingRecording(
    vapiId: string,
  ): Promise<CallMediaUrls | null> {
    let res;

    try {
      res = await API.get(`/calls/vapiId/${vapiId}/missing-recording`);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteCallComment(
    providerId: string,
    commentId: number,
  ): Promise<void> {
    await API.delete(`/calls/provider/${providerId}/comments/${commentId}`);
  }

  export async function createCallComment(
    providerId: string,
    comment: string,
  ): Promise<CallCommentDto> {
    const res = await API.post(`/calls/provider/${providerId}/comments`, {
      content: comment,
    });
    return res?.data;
  }

  export async function updateCallComment(
    providerId: string,
    commentId: number,
    comment: string,
  ): Promise<CallCommentDto> {
    const res = await API.patch(
      `/calls/provider/${providerId}/comments/${commentId}`,
      {
        content: comment,
      },
    );
    return res?.data;
  }
}

export default CallService;
