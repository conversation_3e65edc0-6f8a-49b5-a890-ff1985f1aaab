import Vapi from '@vapi-ai/web';
import AgentService from '@/lib/Agent';

namespace VapiManager {
  export async function getClient(
    isDemoUser = false,
    vapiAgentId: string,
  ): Promise<Vapi> {
    return new Vapi(await AgentService.getVapiAuthKey(isDemoUser, vapiAgentId));
  }
}

export enum VAPI_EVENTS {
  SPEECH_START = 'speech-start',
  SPEECH_END = 'speech-end',
  MESSAGE = 'message',
  CALL_START = 'call-start',
  CALL_END = 'call-end',
  VOLUME_LEVEL = 'volume-level',
  ERROR = 'error',
}

export { VapiManager };
