import { API } from '../Client';
import { RoleEnum } from '../User/types';
import { OrganizationDto, UserInvitation } from './types';

namespace OrganizationService {
  export async function deactivateUser(userId: number) {
    return await API.post('/organizations/deactivate-user', {
      userId,
    });
  }

  export async function reactivateUser(userId: number) {
    return await API.post('/organizations/reactivate-user', {
      userId,
    });
  }

  export async function sendLoginInfoToSlackers(
    numberOfDaysWithNoCalls: number,
  ): Promise<OrganizationDto[]> {
    const res = await API.post('/organizations/send-login-info-to-slackers', {
      numberOfDaysWithNoCalls,
    });
    return res?.data;
  }

  export async function sendLoginInfoToSelectedUsers(
    users: number[],
  ): Promise<OrganizationDto[]> {
    const res = await API.post('/organizations/send-login-info-to-selected-users', {
      users,
    });
    return res?.data;
  }

  export async function getAllOrganizations(): Promise<OrganizationDto[]> {
    const res = await API.get('/organizations/all-organizations');
    return res?.data;
  }

  export async function inviteMembers(
    orgId: number,
    emails: string[],
    role: RoleEnum,
  ): Promise<any> {
    const res = await API.post('/organizations/invite-members-to-org', {
      orgId,
      emails,
      role,
    });
    return res?.data;
  }

  export async function sendBulkInvitations(
    list: UserInvitation[],
  ): Promise<any> {
    let res;
    try {
      res = await API.post('/organizations/send-bulk-invitations', {
        invitations: list,
      });
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }

  export async function bulkUpdateUserRoles(
    userIds: number[],
    role: RoleEnum,
  ): Promise<any> {
    let res;
    try {
      res = await API.post('/organizations/bulk-update-user-roles', {
        userIds,
        role,
      });
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function getOrganization(): Promise<OrganizationDto> {
    const res = await API.get('/organizations/me');
    return res?.data;
  }

  export async function getAccountsUsedPerRole(): Promise<{
    [role: string]: { used: number; cap: number };
  }> {
    const res = await API.get('/organizations/accounts-used-per-role');
    return res?.data;
  }

  export async function getSubOrganizations(): Promise<OrganizationDto[]> {
    let res;
    try {
      res = await API.get('/organizations/sub-organizations');
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }

  export async function createSubOrganization(
    name: string,
    domain: string,
    enableAutoJoiningByDomain: boolean,
    membersMustHaveMatchingDomain: boolean,
    logo?: File,
  ): Promise<OrganizationDto> {
    const formData = new FormData();
    formData.append('logo', logo as Blob);
    formData.append('name', name);
    formData.append('domain', domain);
    formData.append(
      'enableAutoJoiningByDomain',
      enableAutoJoiningByDomain.toString(),
    );
    formData.append(
      'membersMustHaveMatchingDomain',
      membersMustHaveMatchingDomain.toString(),
    );

    let res;
    try {
      res = await API.post('/organizations/sub-organization', formData);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateOrganizationInfo(
    name: string,
    onlyAdminCanViewAllCalls: boolean,
    onlyAdminCanDeleteCalls: boolean,
    repsCanEditScoreResults: 'YES' | 'NO' | 'DISPUTE_ONLY',
  ): Promise<OrganizationDto> {
    let res;
    try {
      res = await API.patch('/organizations/', {
        name,
        onlyAdminCanViewAllCalls: onlyAdminCanViewAllCalls,
        onlyAdminCanDeleteCalls: onlyAdminCanDeleteCalls,
        repsCanEditScoreResults,
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function updateLogo(
      logo: File,
  ): Promise<OrganizationDto> {
    const formData = new FormData();
    formData.append('logo', logo as Blob);

    let res;
    try {
      res = await API.patch('/organizations/logo', formData);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default OrganizationService;
