import { RealCallOrganizationConfig } from '../Integrations/RealCalls/types';

export enum RepsCanEditScoreResults {
  YES = 'YES',
  NO = 'NO',
  DISPUTE_ONLY = 'DISPUTE_ONLY',
}

export type OrganizationSettings = {
  keywords?: string[];
  repAutoRemoval?: { unit: string; value: number; protectedUsers?: string[] };
  onlyAdminsCanExportCalls?: boolean;
  onlyAdminsCanDeleteUserCalls?: boolean;
};

export type OrganizationMetadata = {
  logo?: string;
};

export type OrganizationPilotDetails = {
  expiryDate?: string;
  faqLink?: string;
  surveyLink?: string;
  perLeaderboardGiftAmount?: number;
};

export type BrandingLogo = {
  url: string;
  altText?: string;
  width: string;
  height: string;
};

export type PoweredByBranding = {
  text?: string;
  logo?: BrandingLogo;
  iconLogo?: BrandingLogo;
};

export type OrganizationFrontendConf = {
  useOldSimulationPanel?: boolean;
  blurLeaderboard?: boolean;
  allowAgentPublicToggle?: boolean;
  canAccessIntegrations?: boolean;
  canAccessRealCallsScoring?: boolean;
  betaIntegrationsAllowed?: string[];
  videoCallEnabled?: boolean;
  useNewSidebar?: boolean;
  blurSecondaryPages?: boolean;
  competitionEndDate?: string;
  hideHomePage?: boolean;
  useOldScorecardsUI?: boolean;
  showLeaderboardInHomePage?: boolean;
  aiCoachTextOverride?: string;
  hideHyperboundLogoInIframe?: boolean;
  poweredByBranding?: PoweredByBranding;
  hideChatButtonInIframe?: boolean;
  showTeamsInLeaderboard?: boolean;
  showPlanAssessment?: boolean;
  allBotsPage_showTagsForBots?: boolean;
  onlyAdminsCanDeleteUserCalls?: boolean;
  onlyAdminsCanRescoreCalls?: boolean;
  onlyAccessFromIframes?: boolean;
  scoreMessages?: Record<string, string>;
  useBotBuilderV2?: boolean;
  useNewPostCall?: boolean;
  hideCoaching?: boolean;
  hideLearningMaterials?: boolean;
  hideSimilarCalls?: boolean;
  canAccessInternalCompetitions?: boolean;
  hideCallContext?: boolean;
  isCallHistoryLimitedUi?: boolean;
  onlyAdminsCanViewMedia?: boolean;
  onlyAdminsCanSeeMembersPage?: boolean;
  onlyAdminsCanFilterCalls?: boolean;
  onlyAdminsCanCreatePlaylists?: boolean;
  defaultLeaderboardDateRange?: string;
  hiddenRolesAllowed?: string[];
  useIntegrationsV2?: boolean;
  canAccessScorecardsMaterials?: boolean;
  showCallScoreTitle?: boolean;
  hideAiRoleplayFromMembers?: boolean;
  hideAllBotsFromMembers?: boolean;
  isLightdashEnabled?: boolean;
  isScormEnabled?: boolean;
  isRepInstructionsMandatory?: boolean;
  canGenerateRoutingDescriptions?: boolean;
  onlyAdminCanViewAllRealBuyerCalls?: boolean;
  showManagerCallType?: boolean;
  isHyperboundAppRestricted?: boolean;
};

export enum OrgStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  INACTIVE = 'INACTIVE',
}

export interface OrganizationDto {
  pilotDetails?: OrganizationPilotDetails;
  onlyAdminCanViewAllCalls: boolean;
  logo: string;
  id: number;
  uid: string;
  name: string;
  status: OrgStatus;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  canCreateSubOrgs: boolean;
  isCompetitionOrg: boolean;
  competitionTag?: string;
  parentOrgId: number;
  parentOrganization?: OrganizationDto;
  subOrganizations?: OrganizationDto[];
  repsCanEditScoreResults?: RepsCanEditScoreResults;
  frontEndConf?: OrganizationFrontendConf;
  realCallConfig?: RealCallOrganizationConfig;
  settings?: OrganizationSettings;
  metadata?: OrganizationMetadata;
}

export interface UserInvitation {
  email: string;
  team?: string;
  role?: string;
}

export interface UserRoleUpdate {
  userId: number;
  role: string;
}

export interface SwitchOrgEvent {
  targetUuid: string;
  targetName: string;
  callerRoute: string;
}
