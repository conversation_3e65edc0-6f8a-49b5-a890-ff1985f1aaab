import { InsightsCallType } from '../Analytics/types';

export type ChatMessageDto = {
  role: 'user' | 'assistant';
  createdAt?: string;
  content: string;
  error?: {
    message: string;
    code?: string;
  };
  toolCalls?: ToolCallDto[];
};

export type ToolCallDto = {
  name: string;
  arguments?: string[];
};

export type MonthlyPerformanceRequestDto = {
  month: number;
  year: number;
  type: InsightsCallType;
};

export type MonthlyPerformanceDto = {
  summary: string;
  significantObjectionsSummary: string;
  significantCompetitorsSummary: string;
  topBlockersSummary: string;
  worstPerformingCriterionsSummary: string;
  worstPerformingObjectionsSummary: string;
};
