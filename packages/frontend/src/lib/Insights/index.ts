import { AnalyticsFilterState, InsightsCallType } from '../Analytics/types';
import { SSEResponseChunk } from '../SSE/types';
import SSEService from '../SSE';
import { ChatMessageDto, MonthlyPerformanceDto } from './types';
import { API } from '../Client';

namespace InsightsService {
  export async function* sendChatMessage(
    message: string,
    filters: AnalyticsFilterState,
    type: InsightsCallType,
    messageHistory: ChatMessageDto[],
  ): AsyncGenerator<SSEResponseChunk, void, unknown> {
    yield* SSEService.openStream('/insights/chat', { message, filters, type, messageHistory });
  }

  export async function getMonthlyPerformance(
    month: number,
    year: number,
    type: InsightsCallType,
  ): Promise<MonthlyPerformanceDto> { 
    let res;

    try {
      res = await API.get(`/insights/widget/monthly-performance?month=${month}&year=${year}&type=${type}`);
    } catch (err) {
      console.log(err);
    }

    if (!res) {
      throw new Error('Failed to get monthly performance');
    }

    return res.data;
  }
}

export default InsightsService;