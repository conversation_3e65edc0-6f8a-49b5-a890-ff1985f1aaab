import { AgentDto } from '../Agent/types';

export enum AssignmentStatus {
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

export interface AssignmentDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  agentId: number;
  agent: AgentDto;
  userId: number;
  orgId: number;
  numAssignedCalls: number;
  numCompletedCalls: number;
  status: AssignmentStatus;
  dueDate: Date;
}

export interface AdminAssignmentDto {
  agentId: number;
  agent: AgentDto;
  numCompletedUsers: number;
  numTotalUsers: number;
  numInProgressUsers: number;
  numAssignedCalls: number;
  numCompletedCalls: number;
  userAssignments: AssignmentDto[];
  dueDate: Date;
}

export interface AssignmentManagerViewPerUserDto {
  id: number;
  firstName: string;
  lastName: string;
  avatar: string;
  assignments: AdminAssignmentDto[];
}
