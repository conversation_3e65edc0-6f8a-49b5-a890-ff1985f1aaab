import { API } from '../Client';
import {
  AdminAssignmentDto,
  AssignmentDto,
  AssignmentManagerViewPerUserDto,
} from './types';

namespace AssignmentService {
  export async function createAssignment({
    agentId,
    dueDate,
    userIds,
    numAssignedCalls,
  }: {
    agentId: number;
    dueDate: Date;
    userIds: number[];
    numAssignedCalls: number;
  }): Promise<AdminAssignmentDto> {
    const res = await API.post('/assignments', {
      agentId,
      dueDate,
      userIds,
      numAssignedCalls,
    });
    return res?.data;
  }

  export async function getAdminAssignments(): Promise<AdminAssignmentDto[]> {
    let res;
    try {
      res = await API.get('/assignments/admin');
    } catch (err) {
      console.log(err);
    }
    return res?.data || [];
  }

  export async function getAdminAssignmentsByUser(
    teamId?: number,
  ): Promise<AssignmentManagerViewPerUserDto[]> {
    let res;
    let filterByTeam = '';
    if (teamId) {
      filterByTeam = String(teamId);
    }
    try {
      res = await API.get('/assignments/admin/byuser/' + filterByTeam);
    } catch (err) {
      console.log(err);
    }
    return res?.data || [];
  }

  export async function getAdminAssignmentByAgentId(
    agentId: number,
  ): Promise<AdminAssignmentDto> {
    let res;
    try {
      res = await API.get(`/assignments/admin/agents/${agentId}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function getAssignments(): Promise<AssignmentDto[]> {
    let res;
    try {
      res = await API.get(`/assignments`);
    } catch (err) {
      console.log(err);
    }
    return res?.data || [];
  }

  export async function getAssignmentsByAgentId(
    agentId: number,
  ): Promise<AssignmentDto> {
    let res;
    try {
      res = await API.get(`/assignments/agents/${agentId}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function getAssignmentsByUserId(
    userId: number,
  ): Promise<AssignmentDto[]> {
    let res;
    try {
      res = await API.get(`/assignments/users/${userId}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data || [];
  }

  export async function getAssignmentByIdAndUserId(
    userId: number,
    id: number,
  ): Promise<AssignmentDto> {
    let res;
    try {
      res = await API.get(`/assignments/${id}/users/${userId}`);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function deleteAssignment(
    assignmetId: number,
  ): Promise<boolean> {
    let res;
    try {
      res = await API.delete('/assignments/' + assignmetId);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }

  export async function deleteAssignmentForAgent(
    agentId: number,
  ): Promise<boolean> {
    let res;
    try {
      res = await API.delete('/assignments/per-agent/' + agentId);
    } catch (err) {
      console.log(err);
    }
    return res?.data;
  }
}

export default AssignmentService;
