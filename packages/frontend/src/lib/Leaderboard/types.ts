import { PublicAgentDto } from '../Agent/types';
import { CallDto, CallScorecardDto } from '../Call/types';
import { DemoInboundFormResponseDto } from '../Demo/types';

export interface LeaderboardDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  demoInboundFormResponseId: number;
  demoInboundFormResponse?: DemoInboundFormResponseDto;
  latestCallId: number;
  latestCall?: Partial<CallDto>;
  latestScorecardId: number;
  latestScorecard?: any;
  agentId: number;
  agent?: Partial<PublicAgentDto>;
  rank?: number;
}
