import { API } from '../Client';
import { LeaderboardDto } from './types';

namespace LeaderboardService {
  export async function getLeaderboard(
    agentId: number,
  ): Promise<LeaderboardDto[]> {
    let res;
    try {
      res = await API.get('/leaderboard', {
        params: {
          agentId,
        },
      });
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }

  export async function getTopLeaderboard(
    agentId: number,
    limit: number,
  ): Promise<LeaderboardDto[]> {
    let res;
    try {
      res = await API.get('/leaderboard/top', {
        params: {
          agentId,
          limit,
        },
      });
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }

  export async function getLeaderboardItemsByDemoInboundFormResponseId(
    demoInboundFormResponseId: number,
    agentId: number,
  ): Promise<LeaderboardDto[]> {
    let res;
    try {
      res = await API.get(`/leaderboard/individual`, {
        params: {
          demoInboundFormResponseId,
          agentId,
        },
      });
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }
}

export default LeaderboardService;
