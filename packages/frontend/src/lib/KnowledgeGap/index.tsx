import { KnowledgeGapDto } from './types';
import { API } from '../Client';

namespace KnowledgeGapService {
  export async function get(): Promise<KnowledgeGapDto[]> {
    let res;

    try {
      res = await API.get('/knowledge-gap');
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function save(
    paragraph: KnowledgeGapDto,
  ): Promise<KnowledgeGapDto> {
    let res;

    try {
      res = await API.post('/knowledge-gap', paragraph);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }

  export async function deleteParagraph(id: number): Promise<boolean> {
    let res;

    try {
      res = await API.delete('/knowledge-gap/' + id);
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default KnowledgeGapService;
