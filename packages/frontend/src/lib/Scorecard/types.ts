import { TeamDto } from '../User/types';

export interface OrgLeaderboardItem {
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  score: number;
  rank: number;
  avatar: string;
  criteria: ScorecardCriteria[];
  callVapiId: string;
  teams: TeamDto[];
}

export interface OrgLeaderboardDto {
  leaderboard: OrgLeaderboardItem[];
}

export interface CallQuestionDto {
  question: string;
  secondsFromStart: number;
}

export interface CallObjectionDto {
  objection: string;
  response: string;
  secondsFromStart: number;
}

export type Criterum = {
  passed: boolean;
  coaching: string;
  criterion: string;
  explanation: string;
  improvement: string;
};

export type ScorecardCriteria = {
  criteria: Criterum[];
  description: string;
  sectionTitle: string;
  totalCriteriaCount: number;
  passedCriteriaCount: number;
};

export interface ScorecardDto {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: number;
  updatedBy: number;
  fillerWords: number;
  longestMonologue: number;
  talkListenRatio: number;
  talkSpeed: number;
  passedScore: number;
  totalScore: number;
  criteria: ScorecardCriteria[];
  objections: CallObjectionDto[];
  questions: CallQuestionDto[];
  callId: number;
  aggregateScore: number;
  finalCallScore: number;
  scorecardConfigId: number;
  scorecardConfigVersionId: number;
}
