import { API } from '../Client';
import { OrgLeaderboardDto } from './types';

namespace ScorecardService {
  export async function getOrgLeaderboard(
    fromDate: Date,
    toDate: Date,
    agentId?: number,
  ): Promise<OrgLeaderboardDto> {
    let res;
    try {
      res = await API.get(`/scorecards/leaderboard`, {
        params: {
          fromDate,
          toDate,
          agentId,
        },
      });
    } catch (err) {
      console.log(err);
    }

    return res?.data;
  }
}

export default ScorecardService;
