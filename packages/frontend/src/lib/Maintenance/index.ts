import { API } from '../Client';
import { MaintenanceDto } from './types';

namespace MaintenanceService {
  export async function getDemoLatestActiveMaintenance(): Promise<MaintenanceDto> {
    let res;
    try {
      res = await API.get('/maintenance/demo/latest');
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }

  export async function getLatestActiveMaintenance(): Promise<MaintenanceDto> {
    let res;
    try {
      res = await API.get('/maintenance/latest');
    } catch (err) {
      // console.log(err);
    }
    return res?.data;
  }
}

export default MaintenanceService;
