import { API_LONG_RUNNING as API } from '../Client';
import { ParsedTranscriptMessage, RealCallTranscriptMessage } from './types';

namespace AiService {
  export async function resumeCallsParse(
    messages: string,
  ): Promise<ParsedTranscriptMessage[]> {
    const res = await API.post(`agents/resume-calls/generate-from-transcript`, {
      params: {
        messages,
      },
    });

    return res?.data;
  }

  export async function realCallsParse(
    transcript: string,
  ): Promise<RealCallTranscriptMessage[]> {
    const res = await API.post(`/integrations/real-calls/parse-transcript`, {
      params: {
        transcript,
      },
    });

    return res?.data;
  }
}

export default AiService;
