import { RoleEnum } from './User/types';

export enum AppPermissions {
  /* Users */
  VIEW_USERS = 'USERS.VIEW',
  MANAGE_USERS = 'USERS.MANAGE',


  // VIEW_
  // MANAGE_
  // DELETE_
  // 

  /* Calls */
  VIEW_ALL_CALLS = 'VIEW_ALL_CALLS',
  DELETE_CALLS = 'DELETE_CALLS',
  MANAGE_REAL_CALLS = 'MANAGE_REAL_CALLS',
  EXPORT_CALL = 'EXPORT_CALL',
  RESCORE_CALLS = 'RESCORE_CALLS',

  /* Analytics */
  VIEW_ANALYTICS = 'VIEW_ANALYTICS',
  <PERSON><PERSON><PERSON>_ANALYTICS = '<PERSON>NA<PERSON>_ANALYTICS',
  CREATE_PUBLIC_DASHBOARDS = 'CREATE_PUBLIC_DASHBOARDS',

  /* Scorecard */
  TOGGLE_SCORECARD_CRITERIA = 'TOGGLE_SCORECARD_CRITERIA',
  TOGGLE_DISPUTED_SCORECARD_CRITERIA = 'TOGGLE_DISPUTED_SCORECARD_CRITERIA',

  /* Call Blitz */
  VIEW_ALL_CALL_BLITZ = 'VIEW_ALL_CALL_BLITZ',

  /* Scorecards */
  VIEW_ALL_SCORECARD_CONFIGS = 'VIEW_ALL_SCORECARD_CONFIGS',
  MANAGE_SCORECARDS = 'MANAGE_SCORECARDS',

  /* Playlists */
  DELETE_PLAYLISTS = 'DELETE_PLAYLISTS',
  MANAGE_PLAYLISTS = 'MANAGE_PLAYLISTS',

  /* Bots */
  MANAGE_BOTS = 'MANAGE_BOTS',
  CREATE_BOT = 'CREATE_BOT',
  VIEW_ALL_BOTS = 'VIEW_ALL_BOTS',
  CLONE_BOTS = 'CLONE_BOTS',
  SHARE_BOTS = 'SHARE_BOTS',

  /* Learning Modules */
  MANAGE_LEARNING_MODULE = 'MANAGE_LEARNING_MODULE',
  ADMIN_LEARNING_MODULES = 'ADMIN_LEARNING_MODULES',

  /* Teams */
  VIEW_TEAMS = 'VIEW_TEAMS',
  MANAGE_TEAMS = 'MANAGE_TEAMS',

  /* Tags */
  MANAGE_TAGS = 'MANAGE_TAGS',

  /* Leaderboard */
  VIEW_LEADERBOARD = 'VIEW_LEADERBOARD',

  /* Configs */
  DEVELOPER_SETTINGS = 'DEVELOPER_SETTINGS',
  INTEGRATIONS = 'INTEGRATIONS',
}

export const ROLES_BY_PERMISSION = {
  [AppPermissions.VIEW_ALL_CALLS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.OBSERVER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.VIEW_ANALYTICS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.OBSERVER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.TOGGLE_SCORECARD_CRITERIA]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.TOGGLE_DISPUTED_SCORECARD_CRITERIA]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.VIEW_ALL_CALL_BLITZ]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.OBSERVER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.VIEW_ALL_SCORECARD_CONFIGS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.OBSERVER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.DELETE_PLAYLISTS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.DELETE_CALLS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.MANAGE_BOTS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.CREATE_BOT]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.EXPORT_CALL]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.MANAGE_LEARNING_MODULE]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.ADMIN_LEARNING_MODULES]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.VIEW_TEAMS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
    RoleEnum.OBSERVER,
  ],
  [AppPermissions.RESCORE_CALLS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.MANAGE_ANALYTICS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.MANAGE_TAGS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.CLONE_BOTS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.SHARE_BOTS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.CREATE_PUBLIC_DASHBOARDS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.VIEW_ALL_BOTS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.MANAGE_PLAYLISTS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.VIEW_LEADERBOARD]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
    RoleEnum.OBSERVER,
  ],
  [AppPermissions.MANAGE_REAL_CALLS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.MANAGE_USERS]: [RoleEnum.ADMIN, RoleEnum.OWNER],
  [AppPermissions.MANAGE_TEAMS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.DEVELOPER_SETTINGS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.INTEGRATIONS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.MANAGE_SCORECARDS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
  ],
  [AppPermissions.VIEW_USERS]: [
    RoleEnum.ADMIN,
    RoleEnum.OWNER,
    RoleEnum.MEMBER_PLUS,
    RoleEnum.MEMBER,
    RoleEnum.OBSERVER,
  ],
};