import { AgentCallType } from '@/lib/Agent/types';
import { WidgetType } from './DashboardTemplates/types';

export enum DashboardWidgetTempalteStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE',
}

export enum DashboardWidgetTemplateWidgetType {
  CARD = 'CARD',
  CHART = 'CHART',
}

//filters that are applied to the whole dashboard
export enum GlobalFiltesType {
  RepsAnalytics = 'RepsAnalytics',
  ManagersAnalytics = 'ManagersAnalytics',
}

export interface DashboardWidgetTemplateDto {
  id: number;
  name: string;
  description: string;
  instructions: string;
  inCardInfos: string;
  width: number;
  height: number;
  type: WidgetType;
  customFilters: string;
  status: DashboardWidgetTempalteStatus;
  widgetType: DashboardWidgetTemplateWidgetType;
}

export interface DashboardTabDto {
  id: number;
  userId: number;
  title: string;
  index: number;
  isPersonal: boolean;
  isForAdminOnly: boolean;
  filters: string;
  showInHome: boolean;
  defaultForReps: boolean;
  dashboardTemplateId?: number;
  widgets: DashboardWidgetDto[];
  globalFiltersType?: GlobalFiltesType;
  hideWidgetsFilters: boolean;
}

export interface DashboardWidgetDto {
  id: number;
  name: string;
  description: string;
  userId: number;
  dashboardId: number;
  props: any;
  appliedFilters: any;
  widgetTemplateId: number;
  isNew?: boolean; //used only in the FE
  isDelete?: boolean; //used only in the FE
  type: WidgetType;
  customFilters: string;
  widgetDetails?: DashboardWidgetTemplateDto;
}

export enum AnalyticsFilterType {
  BUYERS = 'buyers',
  REPS = 'reps',
  PLAYLISTS = 'playlists',
  DATE = 'dates',
  CALL_TYPES = 'callTypes',
  TAGS = 'tags',
  CALL_BLITZ = 'callblitz',
  TEAMS = 'teams',
  SCORECARDS = 'scorecards',
  SCORECARDS_SECTIONS = 'scorecardSections',
  SCORECARDS_CRITERIONS = 'scorecardCriterions',
}

export enum AnalyticsFilterDateRange {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  THIS_WEEK = 'thisWeek',
  LAST_WEEK = 'lastWeek',
  LAST_TWO_WEEKS = 'lastTwoWeeks',
  THIS_MONTH = 'thisMonth',
  LAST_MONTH = 'lastMonth',
  LAST_SIX_MONTHS = 'lastSixMonths',
  LAST_YEAR = 'lastYear',
  ALL = 'all-time',
  CUSTOM = 'custom',
}

export const ANALYTICS_DATE_RANGES = [
  {
    label: 'Today',
    value: AnalyticsFilterDateRange.TODAY,
  },
  {
    label: 'Yesterday',
    value: AnalyticsFilterDateRange.YESTERDAY,
  },
  {
    label: 'This Week',
    value: AnalyticsFilterDateRange.THIS_WEEK,
  },
  {
    label: 'Last Week',
    value: AnalyticsFilterDateRange.LAST_WEEK,
  },
  {
    label: 'Last Two Weeks',
    value: AnalyticsFilterDateRange.LAST_TWO_WEEKS,
  },
  {
    label: 'This Month',
    value: AnalyticsFilterDateRange.THIS_MONTH,
  },
  {
    label: 'Last Month',
    value: AnalyticsFilterDateRange.LAST_MONTH,
  },
  {
    label: 'Last Six Months',
    value: AnalyticsFilterDateRange.LAST_SIX_MONTHS,
  },
  {
    label: 'Last Year',
    value: AnalyticsFilterDateRange.LAST_YEAR,
  },
  {
    label: 'All Time',
    value: AnalyticsFilterDateRange.ALL,
  },
  {
    label: 'Custom',
    value: AnalyticsFilterDateRange.CUSTOM,
  },
];

export type DateFilterType = {
  fromDate: Date;
  toDate: Date;
  range: AnalyticsFilterDateRange;
};

export class AnalyticsFilterState {
  [AnalyticsFilterType.BUYERS]: number[] = [];
  [AnalyticsFilterType.REPS]: number[] = [];
  [AnalyticsFilterType.PLAYLISTS]: number[] = [];
  [AnalyticsFilterType.DATE]: DateFilterType = {
    fromDate: new Date(),
    toDate: new Date(),
    range: AnalyticsFilterDateRange.THIS_MONTH,
  };
  [AnalyticsFilterType.CALL_TYPES]: AgentCallType[] = [];
  [AnalyticsFilterType.TAGS]: number[] = [];
  [AnalyticsFilterType.CALL_BLITZ]: number[] = [];
  [AnalyticsFilterType.TEAMS]: number[] = [];
  [AnalyticsFilterType.SCORECARDS]: number[] = [];
  [AnalyticsFilterType.SCORECARDS_SECTIONS]: string[] = [];
  [AnalyticsFilterType.SCORECARDS_CRITERIONS]: string[] = [];
}
