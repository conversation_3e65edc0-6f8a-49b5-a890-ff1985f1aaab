import { AgentCallType } from '@/lib/Agent/types';

export type DashboardDto = {
  name: string;
  uuid: string;
  providerProjectUuid: string;
};

export type EmbeddableDashboardDto = {
  name: string;
  uuid: string;
  providerProjectUuid: string;
  secret: string;
};

export enum DashboardWidgetTemplateWidgetType {
  CARD = 'CARD',
  CHART = 'CHART',
}

export enum AnalyticsFilterType {
  DATE = 'dates',
  CALL_TYPES = 'callTypes',
  CUSTOM_CALL_TYPES_ORG_IDS = 'customCallTypesOrgIds',
  CUSTOM_CALL_TYPES_TEAM_IDS = 'customCallTypesTeamIds',
  CUSTOM_CALL_TYPES_USER_IDS = 'customCallTypesUserIds',
  TEAMS = 'teams',
}

export enum AnalyticsFilterDateRange {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  THIS_WEEK = 'thisWeek',
  LAST_WEEK = 'lastWeek',
  LAST_TWO_WEEKS = 'lastTwoWeeks',
  THIS_MONTH = 'thisMonth',
  LAST_MONTH = 'lastMonth',
  LAST_SIX_MONTHS = 'lastSixMonths',
  LAST_YEAR = 'lastYear',
  ALL = 'all-time',
  CUSTOM = 'custom',
}

export const ANALYTICS_DATE_RANGES = [
  {
    label: 'Today',
    value: AnalyticsFilterDateRange.TODAY,
  },
  {
    label: 'Yesterday',
    value: AnalyticsFilterDateRange.YESTERDAY,
  },
  {
    label: 'This Week',
    value: AnalyticsFilterDateRange.THIS_WEEK,
  },
  {
    label: 'Last Week',
    value: AnalyticsFilterDateRange.LAST_WEEK,
  },
  {
    label: 'Last Two Weeks',
    value: AnalyticsFilterDateRange.LAST_TWO_WEEKS,
  },
  {
    label: 'This Month',
    value: AnalyticsFilterDateRange.THIS_MONTH,
  },
  {
    label: 'Last Month',
    value: AnalyticsFilterDateRange.LAST_MONTH,
  },
  {
    label: 'Last Six Months',
    value: AnalyticsFilterDateRange.LAST_SIX_MONTHS,
  },
  {
    label: 'Last Year',
    value: AnalyticsFilterDateRange.LAST_YEAR,
  },
  {
    label: 'All Time',
    value: AnalyticsFilterDateRange.ALL,
  },
  {
    label: 'Custom',
    value: AnalyticsFilterDateRange.CUSTOM,
  },
];

export type DateFilterType = {
  fromDate: Date;
  toDate: Date;
  range: AnalyticsFilterDateRange;
};

export class AnalyticsFilterState {
  [AnalyticsFilterType.DATE]: DateFilterType = {
    fromDate: new Date(),
    toDate: new Date(),
    range: AnalyticsFilterDateRange.THIS_MONTH,
  };
  [AnalyticsFilterType.CALL_TYPES]: AgentCallType[] = [];
  [AnalyticsFilterType.CUSTOM_CALL_TYPES_ORG_IDS]: number[] = [];
  [AnalyticsFilterType.CUSTOM_CALL_TYPES_TEAM_IDS]: number[] = [];
  [AnalyticsFilterType.CUSTOM_CALL_TYPES_USER_IDS]: number[] = [];
  [AnalyticsFilterType.TEAMS]: number[] = [];
}

export enum SectionType {
  SIM_CALL_OBJECTIONS_BY_REP = 'SIM-CALL-OBJECTIONS-BY-REP',
  SIM_CALL_CRITERION_BY_REP = 'SIM-CALL-CRITERION-BY-REP',
  SIM_CALL_REPS_OF_CONCERN = 'SIM-CALL-REPS-OF-CONCERN',
  REAL_CALL_OBJECTIONS_BY_REP = 'REAL-CALL-OBJECTIONS-BY-REP',
  REAL_CALL_CRITERION_BY_REP = 'REAL-CALL-CRITERION-BY-REP',
  REAL_CALL_REPS_OF_CONCERN = 'REAL-CALL-REPS-OF-CONCERN',
}

export enum InsightsCallType {
  SIMULATED_CALLS = 'simulated-calls',
  REAL_CALLS = 'real-calls',
}
