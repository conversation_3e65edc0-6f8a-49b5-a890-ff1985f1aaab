export enum DashboardTemplateUserType {
  Reps = 'Reps',
  Admins = 'Admins',
  CROs = 'CROs',
}

export enum WidgetType {
  CALL_VOLUME_HISTORY_BY_REP = 'CALL-VOLUME-HISTORY-BY-REP',
  CALL_VOLUME_HISTORY_BY_TEAM = 'CALL-VOLUME-HISTORY-BY-TEAM',
  AVG_BOTS_CALLED_PER_DAY_BY_REP = 'AVG-BOTS-CALLED-PER-DAY-BY-REP',
  AVG_BOTS_CALLED_PER_DAY_BY_TEAM = 'AVG-BOTS-CALLED-PER-DAY-BY-TEAM',
  TOTAL_CALLS = 'TOTAL-CALLS',
  TOTAL_CALLS_DURATION = 'TOTAL-CALLS-DURATION',
  ACTIVE_REPS = 'ACTIVE-REPS',
  SCORECARDS_STATS = 'SCORECARDS-STATS',
  SCORECARDS_TRENDS_BY_REP = 'SCORECARDS-TRENDS-BY-REP',
  SCORECARDS_TRENDS_BY_TEAM = 'SCORECARDS-TRENDS-BY-TEAM',
  SCORECARDS_SECTIONS_TRENDS = 'SCORECARDS-SECTIONS-TRENDS',
  SCORECARDS_CRITERIONS_TRENDS = 'SCORECARDS-CRITERIONS-TRENDS',
  FULL_REPORT = 'FULL-REPORT',
  AVG_IMPROVEMENT_VS_CALLS_PER_REP = 'AVG-IMPROVMENT-VS-CALLS-PER-REP',
  IMPROVEMENT_PER_CRITERION_BY_BOT = 'IMPROVMENT-PER-CRITERION-BY-BOT',
  TOP_USER = 'TOP-USER',
  SCORECARDS_VOLUME = 'SCORECARDS-VOLUME',
  PERFORMANCE_COMPARISON = 'PERFORMANCE-COMPARISON',
  PROFICIENCY_COMPARISON = 'PROFICIENCY-COMPARISON',
  // REP_OVERALL_PERFORMANCE = 'REP-OVERALL-PERFORMANCE',
  REAL_CALL_VOLUME_BY_REP = 'REAL-CALL-VOLUME-BY-REP',
  SIM_CALL_VOLUME_BY_REP = 'SIM-CALL-VOLUME-BY-REP',
  REAL_CALL_AVG_SCORE_BY_REP = 'REAL-CALL-AVG-SCORE-BY-REP',
  SIM_CALL_AVG_SCORE_BY_REP = 'SIM-CALL-AVG-SCORE-BY-REP',
  REAL_CALL_WEEKLY_CALL_VOLUME_VS_GOAL_PER_REP = 'REAL-CALL-WEEKLY-CALL-VOLUME-VS-GOAL-PER-REP',
  SIM_CALL_WEEKLY_CALL_VOLUME_VS_GOAL_PER_REP = 'SIM-CALL-WEEKLY-CALL-VOLUME-VS-GOAL-PER-REP',
  REAL_CALL_WEEKLY_CALL_SCORE_VS_GOAL_PER_REP = 'REAL-CALL-WEEKLY-CALL-SCORE-VS-GOAL-PER-REP',
  SIM_CALL_WEEKLY_CALL_SCORE_VS_GOAL_PER_REP = 'SIM-CALL-WEEKLY-CALL-SCORE-VS-GOAL-PER-REP',
  LEARNING_MODULES_PER_REP = 'LEARNING-MODULES-PER-REP',
  OBJECTION_FREQ_HAND_PER_REP = 'OBJECTION-FREQ-HAND-PER-REP',
  SIM_CALL_CRITERION_SUGGESTIONS_BY_REP = 'SIM-CALL-CRITERION-SUGGESTIONS-BY-REP',
  REAL_CALL_CRITERION_SUGGESTIONS_BY_REP = 'REAL-CALL-CRITERION-SUGGESTIONS-BY-REP',
  SIM_CALL_OBJECTION_IMPR_PER_REP = 'SIM-CALL-OBJECTION-IMPR-PER-REP',
  SIM_CALL_CALL_HISTORY = 'SIM-CALL-CALL-HISTORY',
  REAL_CALL_CALL_HISTORY = 'REAL-CALL-CALL-HISTORY',
  SIM_CALL_GROUPED_SUMMARY_BY_REP = 'SIM-CALL-GROUPED-SUMMARY-BY-REP',
  SIM_CALL_VOLUME_BY_REP_WITH_PASS_RATE = 'SIM-CALL-VOLUME-BY-REP-WITH-PASS-RATE',
  SIM_CALL_OBJECTIONS_BY_REP = 'SIM-CALL-OBJECTIONS-BY-REP',
  SIM_CALL_CRITERION_BY_REP = 'SIM-CALL-CRITERION-BY-REP',
  SIM_CALL_REPS_OF_CONCERN = 'SIM-CALL-REPS-OF-CONCERN',
  REAL_CALL_OBJECTIONS_BY_REP = 'REAL-CALL-OBJECTIONS-BY-REP',
  REAL_CALL_CRITERION_BY_REP = 'REAL-CALL-CRITERION-BY-REP',
  REAL_CALL_GROUPED_SUMMARY_BY_REP = 'REAL-CALL-GROUPED-SUMMARY-BY-REP',
  REAL_CALL_VOLUME_BY_REP_WITH_PASS_RATE = 'REAL-CALL-VOLUME-BY-REP-WITH-PASS-RATE',
  REAL_CALL_PROGRESS_OVER_TIME = 'REAL-CALL-PROGRESS-OVER-TIME',
  REAL_CALL_REPS_OF_CONCERN = 'REAL-CALL-REPS-OF-CONCERN',
}

export default interface DashboardTemplate {
  id: number;
  title: string;
  isPersonal: boolean;
  isForAdminOnly: boolean;
  filters: string;
  widgets: DashboardTemplateWidget[];
  forUserType: DashboardTemplateUserType;
  addToNewOrg: boolean;
  showInHome: boolean;
  defaultForReps: boolean;
}

export interface DashboardTemplateWidget {
  id: number;
  name: string;
  description: string;
  userId: number;
  dashboardTemplateId: number;
  props: any;
  appliedFilters: any;
  widgetTemplateId: number;
  type: WidgetType;
  customFilters: string;
  isNew?: boolean; //used only in the FE
  isDelete?: boolean; //used only in the FE
}
