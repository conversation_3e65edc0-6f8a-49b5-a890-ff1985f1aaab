import { API } from '../Client';
import { AnalyticsFilterState, DashboardDto, EmbeddableDashboardDto } from "./types";

namespace AnalyticsService {
    export async function getHomeDashboard(): Promise<EmbeddableDashboardDto> {
        let res;
    
        try {
          res = await API.get('/analytics/dashboards/home');
        } catch (err) {
          console.log(err);
        }

        if (!res) {
            throw new Error('Failed to get home dashboard');
        }
    
        return res?.data;
    }

    export async function getDashboard(
        dashboard: DashboardDto | undefined
    ): Promise<EmbeddableDashboardDto> {
        let res;

        if (!dashboard) {
            return getHomeDashboard();
        }
    
        try {
          res = await API.get(`/analytics/dashboards/secret/${dashboard.providerProjectUuid}/${dashboard.uuid}`);
        } catch (err) {
          console.log(err);
        }

        if (!res) {
            throw new Error('Failed to get dashboard secret');
        }
    
        return {
            name: dashboard.name,
            uuid: dashboard.uuid,
            providerProjectUuid: dashboard.providerProjectUuid,
            secret: res?.data,
        };
    }

    export async function getAvailableDashboards(): Promise<DashboardDto[]> {
        let res;
    
        try {
          res = await API.get('/analytics/dashboards');
        } catch (err) {
          console.log(err);
        }

        if (!res) {
            throw new Error('Failed to get available dashboards');
        }
    
        return res?.data;
    }

    export async function getInsightsData(
      sectionType: string,
      filters: AnalyticsFilterState,
    ): Promise<any> {
      let res;
  
      try {
        res = await API.post(
          `/analytics/dashboard/chart-data/${sectionType}`,
          filters,
        );
      } catch (err) {
        console.log(err);
      }
  
      return res?.data;
    }
}

export default AnalyticsService;