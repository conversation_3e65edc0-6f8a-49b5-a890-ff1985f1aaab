import PartnerService from '@/lib/Partner';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function usePartnerByPartnerId(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const params = useParams();

  const query = useQuery({
    queryKey: ['partners', params.partnerId, ...commonQueryKeys],
    queryFn: () =>
      PartnerService.getPartnerByPartnerId(params.partnerId as string),
    enabled: enabled && !!params.partnerId,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
