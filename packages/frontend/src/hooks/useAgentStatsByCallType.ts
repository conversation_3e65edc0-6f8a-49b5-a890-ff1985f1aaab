//

import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useAgentStatsByCallType(enabled: boolean = true) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['agent-stats-by-calltype', ...commonQueryKeys],
    queryFn: () => AgentService.getAgentstatsByCallType(),
    enabled: enabled && isLoggedIn,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
