/*
  For some panels with differente tabs/pages, we need to fix the width of the panel to avoid the content to jump when changing tabs.
*/

import { useEffect, useLayoutEffect, useRef } from 'react';

export default function useFixedWidthPanel() {
  const panelRef = useRef<HTMLDivElement>(null);
  const prevWindowWith = useRef<number>(window.innerWidth);

  useLayoutEffect(() => {
    if (panelRef.current) {
      const s = panelRef.current.getBoundingClientRect();
      panelRef.current.style.width = s.width + 'px';
      prevWindowWith.current = window.innerWidth;
    }
  }, []);

  useEffect(() => {
    function updateSize() {
      const delta = prevWindowWith.current - window.innerWidth;
      prevWindowWith.current = window.innerWidth;
      if (panelRef.current) {
        const s = panelRef.current.getBoundingClientRect();
        panelRef.current.style.width = s.width - delta + 'px';
      }
    }
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  return {
    panelRef,
  };
}
