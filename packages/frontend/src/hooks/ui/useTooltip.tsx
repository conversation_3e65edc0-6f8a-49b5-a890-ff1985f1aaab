import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { TooltipContentProps } from '@radix-ui/react-tooltip';

export const useTooltip = (
  children: React.ReactNode,
  {
    message,
    delayMs = 50,
    side = 'left',
    containerClassName = '',
  }: {
    message?: string;
    delayMs?: number;
    side?: TooltipContentProps['side'];
    containerClassName?: string;
  },
) => {
  if (!message) {
    return children;
  }
  return (
    <TooltipProvider delayDuration={delayMs}>
      <Tooltip>
        <TooltipTrigger className={containerClassName}>
          {children}
        </TooltipTrigger>
        <TooltipContent side={side}>{message}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
