import {
  useEffect,
  useRef,
  MouseEvent as Mouse<PERSON>ventReact,
  useCallback,
} from 'react';

/***********************************************
  
DOCUMENTATION:

  - Start adding to your code:

          const [isDNDActive, setIsDNDActive] = useState<boolean>(false);
          const onDrop = (movingObj: any, targetObj: any) => { }          
          const { dragPanelRef, onMouseDown, onMouseEnter, onMouseLeave } = useDragAndDrop(setIsDNDActive, onDrop);

    isDNDActive = used to manage states in your component when drag and drop is active
    dragPanelRef = add this as a ref to the div you want to show along the mouse cursor when dragging
                   note that a div paneld requires:
                      
                      className="absolute" 
                      style={{ display: "none" }}

    onDrop = function to be called when drop is done, it accepts two parameters: movingObj and targetObj


  - On the element that works as handle for drag and drop, add the following events:

          onMouseDown={(e) => { 
              //use referenceObject to fill dragPanelRef information (you can use a react state)
              onMouseDown(referenceObject, e) 
            }
          }
          //inc ase you need to stop clicks, you may also need:
          //onClick={(e) => { e.preventDefault(); e.stopPropagation(); }}
  
    referenceObject = any information you need to pass to the onDrop function as movingObj param (NB: referenceObject is of type any)

    - On droppable areas add the following events:

          onMouseEnter={(e) => { onMouseEnter(referenceObject) }}
          onMouseLeave={onMouseLeave}

      referenceObject = any information you need to pass to the onDrop as targetObj param 

    - You may need to stop text selection on drag, you can add the following css:

          'select-none': isDNDActive


FULL CODE EXAMPLE:

    const [isDNDActive, setIsDNDActive] = useState<boolean>(false);
    const [currentDraggedObject, setCurrentDraggedObject] = useState<any>();

    const onDrop = useCallback((movingObj: any, targetObj: any) => {
      console.log(movingObj, targetObj);
    }, []);

    const { dragPanelRef, onMouseDown, onMouseEnter, onMouseLeave } = useDragAndDrop(setIsDNDActive, onDrop);

    const startDragging = (f: any, e: React.MouseEvent) => {
      setCurrentDraggedFolder(f);
      onMouseDown(f, e);
    }

    ........................

    <div className={cn({'select-none': isDNDActive})}>
      {
        items.map(i => {
          return (
            <div 
              onMouseDown={(e) => { 
                setCurrentDraggedFolder(i)
                onMouseDown(i, e) 
              }}
              onMouseEnter={(e) => { onMouseEnter(i) }}
              onMouseLeave={onMouseLeave}
            >{i.name}</div>
          )
        })
      }
    </div>

    <div ref={dragPanelRef} className="absolute" style={{ display: "none" }}>{currentDraggedObject.name}</div>



***********************************************/

export default function useDragAndDrop<MovingObjectType, TargetObjectType>(
  setIsDNDActive: (isActive: boolean) => void,
  onDrop: (
    movingObj: MovingObjectType,
    targetObj: TargetObjectType | undefined,
  ) => void,
  onDragStart?: (movingObj: MovingObjectType) => void,
  onDraggingOverObj?: (hoverObj: TargetObjectType) => void,
  onDraggingLeaveObj?: () => void,
  debug?: string,
) {
  const dragPanel = useRef<HTMLDivElement | null>(null);

  const prevMousePosition = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const isDndRunning = useRef<boolean>(false);
  const mouseMoved = useRef<boolean>(false);
  const mouseDown = useRef<boolean>(false);
  const overState = useRef<TargetObjectType | null>(null);
  const movingObject = useRef<MovingObjectType | null>(null);

  const onMouseDown = (movingObj: MovingObjectType, e: MouseEventReact) => {
    if (debug) {
      console.log('down', debug);
    }

    if (e.button === 0) {
      mouseDown.current = true;
      prevMousePosition.current = { x: e.clientX, y: e.clientY };
      mouseMoved.current = false;
      movingObject.current = movingObj;
    }
  };

  const onMouseMove = useCallback(
    (e: MouseEvent) => {
      if (mouseDown.current) {
        if (!mouseMoved.current) {
          const dx = Math.abs(e.x - prevMousePosition.current.x);
          const dy = Math.abs(e.y - prevMousePosition.current.y);

          if (dx > 5 || dy > 5) {
            mouseMoved.current = true;
            isDndRunning.current = true;
            setIsDNDActive(true);
            window?.getSelection()?.removeAllRanges();
            if (onDragStart && movingObject.current) {
              onDragStart(movingObject.current);
            }
          }
        }

        if (isDndRunning.current) {
          // if (debug) {
          //   console.log("move", debug);
          // }
          if (dragPanel.current) {
            const pnl = dragPanel.current;
            pnl.style.display = 'block';
            pnl.style.left = e.pageX + 4 + 'px';
            pnl.style.top = e.pageY + 0 + 'px';
          }
        }
      }
    },
    [onDragStart],
  );

  const onMouseUp = useCallback(async () => {
    // if (debug) {
    //   console.log("up", debug);
    // }
    mouseDown.current = false;
    if (isDndRunning.current && movingObject.current) {
      setIsDNDActive(true);
      isDndRunning.current = false;

      setTimeout(() => {
        setIsDNDActive(false);
      }, 100);

      if (dragPanel.current) {
        const pnl = dragPanel.current;
        pnl.style.display = 'none';
      }
      onDrop(movingObject.current, overState.current || undefined);
    } else {
      setIsDNDActive(false);
      isDndRunning.current = false;
      if (dragPanel.current) {
        const pnl = dragPanel.current;
        pnl.style.display = 'none';
      }
    }
  }, [onDrop]);

  const onMouseEnter = useCallback(
    (hoverObj: TargetObjectType) => {
      // if (debug) {
      //   console.log("enter", debug);
      // }
      if (isDndRunning.current) {
        overState.current = hoverObj;
        if (onDraggingOverObj) {
          onDraggingOverObj(hoverObj);
        }
      }
    },
    [onDraggingOverObj],
  );

  const onMouseLeave = useCallback(() => {
    // if (debug) {
    //   console.log("leave", debug);
    // }
    overState.current = null;
    if (onDraggingLeaveObj && isDndRunning.current) {
      onDraggingLeaveObj();
    }
  }, [onDraggingLeaveObj]);

  const getDraggedObject = () => {
    return movingObject.current;
  };

  useEffect(() => {
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);

    return () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };
  }, [onMouseUp]);

  return {
    dragPanelRef: dragPanel,
    onMouseDown,
    onMouseEnter,
    onMouseLeave,
    getDraggedObject,
  };
}
