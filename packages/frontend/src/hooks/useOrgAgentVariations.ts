import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useOrgAgentVariations(
  vapiId: string,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['orgAgentVariations', vapiId, ...commonQueryKeys],
    queryFn: () => AgentService.getOrgAgentVariations(vapiId),
    enabled: isLoggedIn && enabled,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
