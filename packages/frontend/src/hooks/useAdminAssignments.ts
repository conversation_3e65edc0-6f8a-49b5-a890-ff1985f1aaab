import AssignmentService from '@/lib/Assignment';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useAdminAssignments(enabled = true) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['adminAssignments', ...commonQueryKeys],
    queryFn: () => AssignmentService.getAdminAssignments(),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}

export function useAdminAssignmentsByUser(teamId?: number, enabled = true) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['adminAssignmentsByUser', teamId, ...commonQueryKeys],
    queryFn: () => AssignmentService.getAdminAssignmentsByUser(teamId),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
