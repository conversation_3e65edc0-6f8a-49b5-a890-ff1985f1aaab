import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useOrgAgentsbyId(
  includeAgents: number[] = [],
  enabled = true,
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['orgAgents', includeAgents, ...commonQueryKeys],
    queryFn: () => AgentService.getOrgAgentsByIds(includeAgents),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
