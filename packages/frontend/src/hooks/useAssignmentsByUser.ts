import AssignmentService from '@/lib/Assignment';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useAssignmentsByUser(userId: number, enabled = true) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['assignments', userId, ...commonQueryKeys],
    queryFn: () => AssignmentService.getAssignmentsByUserId(userId),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
