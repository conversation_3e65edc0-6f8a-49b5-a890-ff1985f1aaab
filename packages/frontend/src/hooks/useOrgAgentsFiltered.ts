import { AgentCallType, AgentEmotionalState, AgentStatus, TagCondition } from './../lib/Agent/types';
import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useOrgAgentsFiltered(
  enabled = true,
  search = '',
  variationIds: number[] = [],
  tagsIds: number[] = [],
  tagCondition: TagCondition = TagCondition.OR,
  callType?: AgentCallType,
  emotionalState?: AgentEmotionalState,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'orgAgentsFiltered',
      search,
      variationIds,
      tagsIds,
      ...commonQueryKeys,
      tagCondition,
      ...(callType ? [callType] : []),
      ...(emotionalState ? [emotionalState] : []),
    ],
    queryFn: () =>
      AgentService.getOrgAgentsFiltered(
        search,
        variationIds,
        tagsIds,
        tagCondition,
        callType,
        emotionalState,
      ),
    enabled: enabled,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
