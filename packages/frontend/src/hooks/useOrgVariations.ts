import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useOrgVariations(
  enabled: boolean = true,
  from: number = 0,
  numberOfResults: number = 10,
  search: string = '',
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'orgVariations',
      from,
      numberOfResults,
      search,
      ...commonQueryKeys,
    ],
    queryFn: () => AgentService.getOrgVariations(from, numberOfResults, search),
    enabled: enabled,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
