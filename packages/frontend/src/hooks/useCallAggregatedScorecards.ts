import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallAggregatedScorecards(
  vapiIds: string[],
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['callAggregatedScorecards', ...vapiIds, ...commonQueryKeys],
    queryFn: () => CallService.getCallAggregatedScorecards(vapiIds),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 3000,
    retry: 5,
  });

  return query;
}
