import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export function useDemoCallCoaching(vapiId: string, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: [
      'call-coaching-demo',
      vapiId,
      hbDemoInboundForm?.id,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      CallService.getDemoCallCoaching(vapiId, hbDemoInboundForm?.id),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}

export default function useCallCoaching(vapiId: string, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['call-coaching', vapiId, ...commonQueryKeys],
    queryFn: () => CallService.getCallCoaching(vapiId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}
