import { useEffect } from 'react';
import { useAuthInfo } from '@propelauth/react';
import * as Sentry from '@sentry/nextjs';

import { refreshHeaders } from '@/app/auth-wrapper';
import { SwitchOrgEvent } from '@/lib/Organization/types';

export function useOrgSwitch() {
  const authInfo = useAuthInfo();

  const switchOrg = async (event: SwitchOrgEvent) => {
    try {
      await refreshHeaders(authInfo, {
        id: event.targetUuid,
        name: event.targetName,
        userId: authInfo.user?.userId || '',
      });

      // Forcing full refresh to avoid any stale data
      window.location.href = event.callerRoute;
    } catch (e) {
      Sentry.captureException(e, {
        extra: {
          message: 'Error in refreshing headers during org switch',
        },
      });
    }
  };

  useEffect(() => {
    const handleSwitchOrg = (event: CustomEvent<SwitchOrgEvent>) => {
      switchOrg(event.detail);
    };

    window.addEventListener('switchOrg', handleSwitchOrg as EventListener);

    return () => {
      window.removeEventListener('switchOrg', handleSwitchOrg as EventListener);
    };
  }, [authInfo]);
}
