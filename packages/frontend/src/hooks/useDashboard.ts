import AnalyticsService from '@/lib/Analytics';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useDashboard(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['dashboard', ...commonQueryKeys],
    queryFn: () => AnalyticsService.getDashboard(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useDashboardForHome(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['dashboard-for-home', ...commonQueryKeys],
    queryFn: () => AnalyticsService.getDashboardForHome(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useDashboardForReps(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['dashboard-for-reps', ...commonQueryKeys],
    queryFn: () => AnalyticsService.getDashboardForReps(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
