import PartnerService from '@/lib/Partner';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { use, useEffect, useState } from 'react';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function usePartnerByCachedPartnerId(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const queryClient = useQueryClient();
  const params = useParams();
  const [partnerId, setPartnerId] = useState('');

  useEffect(() => {
    const partnerId = queryClient.getQueryData(['partnerId']) as string;
    setPartnerId(partnerId);
  }, [queryClient.getQueryData(['partnerId'])]);

  const query = useQuery({
    queryKey: ['partners', partnerId, ...commonQueryKeys],
    queryFn: () => PartnerService.getPartnerByPartnerId(partnerId as string),
    enabled: enabled && !!partnerId,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
