import IntegrationService from '@/lib/Integrations';
import { IntegrationServiceType } from '@/lib/Integrations/types';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export function useIntegrations(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['integrations', ...commonQueryKeys],
    queryFn: () => IntegrationService.getIntegrations(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useServiceProviders(
  it: IntegrationServiceType,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['int-service-providers', it, ...commonQueryKeys],
    queryFn: () => IntegrationService.getServiceProviders(it),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
export function useIntegration(integrationId: number, enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['integration-details', integrationId, ...commonQueryKeys],
    queryFn: () => IntegrationService.getIntegration(integrationId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
