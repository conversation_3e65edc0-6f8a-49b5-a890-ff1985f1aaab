import { TagsService } from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useTagsByIds(enabled = true, ids: number[]) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['agentsTagsbyIds', ids, ...commonQueryKeys],
    queryFn: () => TagsService.getByIds(ids),
    enabled: enabled,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
