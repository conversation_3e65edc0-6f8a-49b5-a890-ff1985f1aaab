/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import useTeams from './useTeams';
import { useAssignmentRules } from './useLearningModulesV2';
import {
  LearningModuleAssignmentRule,
  LearningModuleAssignmentRuleType,
} from '@/lib/LearningModule/types';
import { formatDate } from 'date-fns';

export default function useLearningModulesAssignmentRules(templateId: string) {
  const { data: teams } = useTeams(0, 500);

  const { data: assignmentRules } = useAssignmentRules(templateId, true);

  const getDisplayByRule = (rule: LearningModuleAssignmentRule) => {
    return rule.criterias
      .filter((criteria) => !!criteria.value)
      .map((criteria) => {
        const criteriaValue = criteria?.value as any;
        if (!criteriaValue) {
          return '';
        }

        const displayByCriteria = {
          [LearningModuleAssignmentRuleType.TEAM]: () => ({
            title: 'Team',
            value: `${criteriaValue.operator === 'in' ? 'is' : criteriaValue.operator} ${criteriaValue.teams?.map((team: number) => teams?.find((t) => t.id === team)?.name).join(', ')}`,
          }),
          [LearningModuleAssignmentRuleType.SIGN_UP_DATE]: () => ({
            title: 'Sign up date',
            value: `${criteriaValue.operator === 'in' ? 'is' : criteriaValue.operator} ${criteriaValue.date ? formatDate(criteriaValue.date, 'MMM dd, yyyy') : `${formatDate(criteriaValue.from, 'MMM dd, yyyy')} - ${formatDate(criteriaValue.to, 'MMM dd, yyyy')}`}`,
          }),
          [LearningModuleAssignmentRuleType.COMPANY_START_DATE]: () => ({
            title: 'Company start date',
            value: `${criteriaValue.operator === 'in' ? 'is' : criteriaValue.operator} ${criteriaValue.date ? formatDate(criteriaValue.date, 'MMM dd, yyyy') : `${formatDate(criteriaValue.from, 'MMM dd, yyyy')} - ${formatDate(criteriaValue.to, 'MMM dd, yyyy')}`}`,
          }),
          [LearningModuleAssignmentRuleType.REGION]: () => ({
            title: 'Region',
            value: `${criteriaValue.operator === 'in' ? 'is' : criteriaValue.operator} ${criteriaValue.region}`,
          }),
          [LearningModuleAssignmentRuleType.ROLE]: () => ({
            title: 'Role',
            value: `${criteriaValue.operator === 'in' ? 'is' : criteriaValue.operator} ${criteriaValue.roles?.join(', ')}`,
          }),
          [LearningModuleAssignmentRuleType.PROFILE]: () => ({
            title: '',
            value: ``,
          }),
        };
        return (
          <p key={criteria.id}>
            <span className="font-medium">
              {
                displayByCriteria[
                  criteria.type as LearningModuleAssignmentRuleType
                ]?.().title
              }
            </span>
            {'  '}
            <span className="text-primary">
              {
                displayByCriteria[
                  criteria.type as LearningModuleAssignmentRuleType
                ]?.().value
              }
            </span>
          </p>
        );
      });
  };

  return {
    teams,
    assignmentRules,
    getDisplayByRule,
  };
}
