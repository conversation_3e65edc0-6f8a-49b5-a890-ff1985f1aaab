import { AgentStatus } from '@/lib/Agent/types';
import useOrgAgents from './useOrgAgents';
import useUserSession from './useUserSession';

export const useCompetitionOrgAgent = () => {
  const { isCompetitionOrg } = useUserSession();
  const { data: orgAgents, ...rest } = useOrgAgents(
    undefined,
    isCompetitionOrg,
    undefined,
    1,
    undefined,
    AgentStatus.ACTIVE,
  );
  const { data: orgAgentsInactive, ...rest2 } = useOrgAgents(
    undefined,
    isCompetitionOrg,
    undefined,
    1,
    undefined,
    AgentStatus.INACTIVE,
  );
  if (orgAgents?.length) {
    const agent = orgAgents?.[0];
    return {
      data: agent,
      ...rest,
    };
  }
  const agent = orgAgentsInactive?.[0];
  return {
    data: agent,
    ...rest2,
  };
};
