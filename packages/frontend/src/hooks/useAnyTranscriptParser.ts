import AiService from '@/lib/Ai';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';
import { useMemo } from 'react';

export default function useAnyTranscriptParser(anyTranscript: string) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['anyTranscript', anyTranscript],
    queryFn: async () => {
      const trimmed = anyTranscript.replace(/^\s*[\r\n]/gm, '').trim();
      return AiService.realCallsParse(trimmed);
    },
    enabled: false, // Disable automatic fetching
    retry: 0, // No automatic retries
    staleTime: 0, // Data is immediately marked as stale
  });

  const fetchQuery = async () => {
    if (!isLoggedIn || !anyTranscript) return;
    return await query.refetch();
  };

  const totalWords = useMemo(
    () =>
      // Calculate total words, handling whitespace
      anyTranscript?.trim().split(/\s+/).length || 0,
    [anyTranscript],
  );

  return { ...query, totalWords, fetchQuery };
}
