import KnowledgeGapService from '@/lib/KnowledgeGap';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useKnowledeGap(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['knowledge-gap', ...commonQueryKeys],
    queryFn: () => KnowledgeGapService.get(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 2,
  });

  return query;
}
