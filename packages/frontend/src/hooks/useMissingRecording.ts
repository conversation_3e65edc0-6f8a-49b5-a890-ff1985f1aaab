import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useMissingRecording(vapiId: string, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['missing-recording', vapiId, ...commonQueryKeys],
    queryFn: () => CallService.getMissingRecording(vapiId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}
