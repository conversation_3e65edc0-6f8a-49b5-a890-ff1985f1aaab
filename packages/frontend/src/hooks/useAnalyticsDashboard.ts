import AnalyticsService from '@/lib/Analytics';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { DashboardDto } from '@/lib/Analytics/types';

export default function useAnalyticsDashboard(
    enabled: boolean = true,
    dashboard: DashboardDto | undefined = undefined
) {
    const commonQueryKeys = useCommonQueryKeys();

    const query = useQuery({
        queryKey: ['analytics-dashboard', ...commonQueryKeys, dashboard?.uuid],
        queryFn: () => AnalyticsService.getDashboard(dashboard),
        enabled: enabled,
        staleTime: 2000,
        refetchOnWindowFocus: false,
        retry: 2,
    });

    return query;
}

export function useAvailableAnalyticsDashboards(
    enabled: boolean = true
) {
    const commonQueryKeys = useCommonQueryKeys();


    const query = useQuery({
        queryKey: ['available-analytics-dashboard', ...commonQueryKeys],
        queryFn: () => AnalyticsService.getAvailableDashboards(),
        enabled: enabled,
        staleTime: 2000,
        refetchOnWindowFocus: false,
        retry: 2,
    });

    return query;
}