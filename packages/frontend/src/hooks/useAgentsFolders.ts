import { FoldersService } from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export function useAgentsFolders(teamId?: number, enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['agents-folders', teamId, ...commonQueryKeys],
    queryFn: () => FoldersService.getAll(teamId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useAgentsWithNoFolder(
  start: number = 0,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['agents-with-no-folder', start, ...commonQueryKeys],
    queryFn: () => FoldersService.getAgentsWithNoFolder(start),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useAgentsFoldersFavourite(
  teamId?: number,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['agents-folders-favourites', teamId, ...commonQueryKeys],
    queryFn: () => FoldersService.getFavourites(teamId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
