import { FilterState } from '@/common/Calls/Real/List';
import { DateFilterType } from '@/lib/Analytics/types';
import { RealCallsService } from '@/lib/Integrations';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export function useImportTasks(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['import-tasks-list', ...commonQueryKeys],
    queryFn: () => RealCallsService.getImportTasks(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useListRealCalls(
  filters: FilterState,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['list-real-calls', JSON.stringify(filters), ...commonQueryKeys],
    queryFn: () => RealCallsService.listCalls(filters),
    enabled: enabled,
    staleTime: 60000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function userListCallsFromProvider(
  integrationId: number,
  filters: FilterState,
  cursor: string,
  userId?: string,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'list-calls-from-provider',
      integrationId,
      filters,
      cursor,
      userId,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      RealCallsService.listCallsFromProvider(
        integrationId,
        filters,
        cursor,
        userId,
      ),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useExternalUsers(
  integrationId: number,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['list-all-users', integrationId, ...commonQueryKeys],
    queryFn: () => RealCallsService.listUsers(integrationId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useCall(callId: number, enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['real-call-details', callId, ...commonQueryKeys],
    queryFn: () => RealCallsService.getCall(callId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useRealCallPublicDetails(
  callId: number,
  pwd?: string,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['real-call-public-details', callId, ...commonQueryKeys],
    queryFn: () => RealCallsService.getPublicDetails(callId, pwd),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useRealCallScorecardNew(
  callId: number,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['real-call-scorecard-new', callId, ...commonQueryKeys],
    queryFn: () => RealCallsService.getScorecardNew(callId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useScorecard(
  integrationId: number,
  callId: string,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: [
      'real-call-scorecard',
      integrationId,
      callId,
      ...commonQueryKeys,
    ],
    queryFn: () => RealCallsService.getScorecard(integrationId, callId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useCoachingInfo(callId: number, enabled: boolean = true) {
  const query = useQuery({
    queryKey: ['real-call-coaching-info', callId],
    queryFn: () => RealCallsService.getCoachingInfo(callId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useCategorizationRules(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['real-calls-categorization-rules', ...commonQueryKeys],
    queryFn: () => RealCallsService.getAlCategorizationRules(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function usePrivacyRules(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['real-calls-privacy-rules', ...commonQueryKeys],
    queryFn: () => RealCallsService.getAlPrivacyRules(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
