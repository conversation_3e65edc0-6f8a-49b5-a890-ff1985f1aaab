import { RoleEnum } from '@/lib/User/types';
import useUserSession from './useUserSession';
import { useMemo } from 'react';

export default function useOrgRoles() {
  const { hiddenRolesAllowed } = useUserSession();

  const allowedRoles = useMemo(() => {
    return Object.values(RoleEnum).filter((r) => {
      return (
        (r !== RoleEnum.OWNER &&
          r !== RoleEnum.CUSTOM &&
          r !== RoleEnum.TEMP) ||
        hiddenRolesAllowed.some((role) => role === r.toString())
      );
    });
  }, [hiddenRolesAllowed]);

  return { allowedRoles };
}
