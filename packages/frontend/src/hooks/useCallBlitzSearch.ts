import { useQuery } from '@tanstack/react-query';
import CallBlitzService from '@/lib/CallBlitz';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useEffect, useState } from 'react';

export default function useCallBlitzSearch(
  from: number = 0,
  numberOfResults: number = 10,
  searchString: string = '',
  repsFilter: number[] = [],
  enabled = true,
  debounceMs = 0,
) {
  const [debouncedSearch, setDebouncedSearch] = useState(searchString);

  useEffect(() => {
    const handler = setTimeout(
      () => setDebouncedSearch(searchString),
      debounceMs,
    );
    return () => clearTimeout(handler);
  }, [searchString, debounceMs]);

  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: [
      'call-blitz-search',
      debouncedSearch,
      from,
      numberOfResults,
      repsFilter,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      CallBlitzService.load(from, numberOfResults, debouncedSearch, repsFilter),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}

export function useCallBlitzByIds(callBlitz: number[] = [], enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['call-blitz-by-ids', callBlitz, ...commonQueryKeys],
    queryFn: () => CallBlitzService.loadByIds(callBlitz),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}
