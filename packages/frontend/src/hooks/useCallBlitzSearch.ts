import { useQuery } from '@tanstack/react-query';
import CallBlitzService from '@/lib/CallBlitz';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallBlitzSearch(
  from: number = 0,
  numberOfResults: number = 10,
  searchString: string = '',
  repsFilter: number[] = [],
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'call-blitz-search',
      searchString,
      from,
      numberOfResults,
      repsFilter,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      CallBlitzService.load(from, numberOfResults, searchString, repsFilter),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}

export function useCallBlitzByIds(callBlitz: number[] = [], enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['call-blitz-by-ids', callBlitz, ...commonQueryKeys],
    queryFn: () => CallBlitzService.loadByIds(callBlitz),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}
