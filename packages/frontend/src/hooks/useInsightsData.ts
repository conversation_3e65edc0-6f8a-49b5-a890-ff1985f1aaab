import AnalyticsService from '@/lib/Analytics';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { AnalyticsFilterState } from '@/lib/Analytics/types';

export default function useInsightsData(
  sectionType: string,
  filters: AnalyticsFilterState,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['dashboardChartData', sectionType, filters, ...commonQueryKeys],
    queryFn: async () => AnalyticsService.getInsightsData(sectionType, filters),
    enabled: enabled,
    staleTime: 10000,
    refetchOnWindowFocus: 'always',
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
