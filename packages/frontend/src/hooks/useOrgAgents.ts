import { AgentCallType, AgentStatus } from './../lib/Agent/types';
import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useOrgAgents(
  callType?: AgentCallType,
  enabled = true,
  from = 0,
  numberOfResults = 0,
  search = '',
  agentStatus?: AgentStatus,
  includeVariationsBots = false,
  sortByLastUpdated?: string,
  teams?: number[],
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'orgAgents',
      callType,
      from,
      numberOfResults,
      search,
      agentStatus,
      sortByLastUpdated,
      ...commonQueryKeys,
      ...(teams ?? []),
    ],
    queryFn: () =>
      AgentService.getOrgAgents(
        callType,
        from,
        numberOfResults,
        search,
        agentStatus,
        includeVariationsBots,
        sortByLastUpdated,
        teams,
      ),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
