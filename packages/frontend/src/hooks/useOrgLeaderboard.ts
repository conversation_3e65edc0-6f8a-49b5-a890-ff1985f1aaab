import ScorecardService from '@/lib/Scorecard';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useOrgLeaderboard(
  fromDate: Date,
  toDate: Date,
  agentId?: number,
  enabled = true,
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['orgLeaderboard', fromDate, toDate, agentId, ...commonQueryKeys],
    queryFn: () =>
      ScorecardService.getOrgLeaderboard(fromDate, toDate, agentId),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
