import LeaderboardService from '@/lib/Leaderboard';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useTopLeaderboard(
  agentId: number,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: ['topLeaderboard', agentId, ...commonQueryKeys],
    queryFn: () => LeaderboardService.getTopLeaderboard(agentId, 7),
    enabled:
      enabled &&
      !!agentId &&
      (!isLoggedIn ||
        (isLoggedIn && hbDemoInboundForm?.presClubChallengeRegistered)),
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 2,
  });

  return query;
}
