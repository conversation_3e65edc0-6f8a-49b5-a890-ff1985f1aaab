import { useQuery } from '@tanstack/react-query';
import Team from '@/lib/User/Team';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useTeams(
  from: number = 0,
  numberOfResults: number = 10,
  searchString: string = '',
  loadMembers = false,
  enabled = true,
  loadSubTeams = false,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'teams',
      searchString,
      from,
      numberOfResults,
      loadMembers,
      loadSubTeams,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      Team.getAll(
        from,
        numberOfResults,
        searchString,
        loadMembers,
        loadSubTeams,
      ),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}

export function useTeamsByIds(ids: number[], enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['teams-by-id', ids, ...commonQueryKeys],
    queryFn: () => Team.getByIds(ids),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}

export function useUserTeams(
  from: number = 0,
  numberOfResults: number = 10,
  searchString: string = '',
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'user-teams',
      searchString,
      from,
      numberOfResults,
      ...commonQueryKeys,
    ],
    queryFn: () => Team.getAllForUser(from, numberOfResults, searchString),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}
