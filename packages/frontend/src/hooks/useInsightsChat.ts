import { useState, useCallback, useEffect } from 'react';
import InsightsService from '@/lib/Insights';
import type { ChatMessageDto, ToolCallDto } from '@/lib/Insights/types';
import type { AnalyticsFilterState, InsightsCallType } from '@/lib/Analytics/types';
import type { SSEResponseChunk } from '@/lib/SSE/types';
import useOrg from './useOrg';

export function useInsightsChat() {
  const { data: org } = useOrg();
  const [messageHistory, setMessageHistory] = useState<ChatMessageDto[]>(() => {
    const storedHistory = localStorage.getItem(`${org?.id ?? ''}-scorecard-insights-message-history`);
    let parsedHistory = storedHistory ? JSON.parse(storedHistory) : [];
    parsedHistory = parsedHistory.filter((msg: ChatMessageDto) => msg.content != '');
    return parsedHistory;
  });
  const [isResponding, setIsResponding] = useState(false);

  const sendMessage = useCallback(
    async (message: string, filters: AnalyticsFilterState, type: InsightsCallType) => {
      const userMessage: ChatMessageDto = {
        role: 'user',
        createdAt: (new Date()).toISOString(),
        content: message,
      };

      const assistantMessage: ChatMessageDto = {
        role: 'assistant',
        createdAt: (new Date()).toISOString(),
        content: '',
      };

      setMessageHistory((prev) => [...prev, userMessage, assistantMessage]);

      try {
        setIsResponding(true);

        const minimalMessageHistory = messageHistory
          .filter(msg => msg.error === undefined)
          .map(msg => ({
            role: msg.role,
            content: msg.content,
          }));

        for await (const chunk of InsightsService.sendChatMessage(message, filters, type, minimalMessageHistory)) {
          // Append content.
          if (chunk.type === 'content')
            formatAndAppendContentChunk(chunk);

          // Append tool call.
          else if (chunk.type === 'tool_call')
            formatAndAppendToolCallChunk(chunk as SSEResponseChunk & { tool_calls: { function: { name: string, arguments: string[] } }[] });

          // Error out.
          else if (chunk.type === 'error') {
            setMessageHistory((prev) => {
              const lastMsg = prev[prev.length - 1];
              const updatedAssistantMsg = {
                ...lastMsg,
                content: lastMsg.content,
                error: {
                  message: chunk.error,
                },
              };
              return [...prev.slice(0, prev.length - 1), updatedAssistantMsg];
            });
            break;
          }

          // Finish stream.
          else if (chunk.type === 'done' || chunk.type === 'raw') {
            setMessageHistory((prev) => {
              const lastMsg = prev[prev.length - 1];

              // If the last message is still empty, error out.
              if (lastMsg.content === '') {
                const updatedAssistantMsg = {
                  ...lastMsg,
                  error: {
                    message: 'Error while responding, please try again.',
                  },
                };
                return [...prev.slice(0, prev.length - 1), updatedAssistantMsg];
              }

              return prev;
            });
            break;
          }
        }
      } catch (error) {
        console.error('Error processing stream:', error);
      } finally {
        setIsResponding(false);
      }
    },
    [messageHistory]
  );

  function formatAndAppendContentChunk(chunk: SSEResponseChunk & { content: string }) {
    setMessageHistory((prev) => {
      const lastMsg = prev[prev.length - 1];
      const updatedAssistantMsg = {
        ...lastMsg,
        content: lastMsg.content + chunk.content,
      };
      return [...prev.slice(0, prev.length - 1), updatedAssistantMsg];
    });
  }

  function formatAndAppendToolCallChunk(chunk: SSEResponseChunk & { tool_calls: { function: { name: string, arguments: string[] } }[] }) {
    let toolCalls: ToolCallDto[] = [];

    for (const toolCall of chunk.tool_calls) {
      toolCalls.push({
        name: toolCall.function.name,
        arguments: toolCall.function.arguments,
      });
    }

    setMessageHistory((prev) => {
      const lastMsg = prev[prev.length - 1];
      const updatedAssistantMsg = {
        ...lastMsg,
        toolCalls: toolCalls,
      };
      return [...prev.slice(0, prev.length - 1), updatedAssistantMsg];
    });
  }

  // Whenever messageHistory changes, save it to localStorage.
  // Also enforce a maximum of 25 messages.
  useEffect(() => {
    if (messageHistory.length > 25) {
      const trimmedHistory = messageHistory.slice(-25);
      setMessageHistory(trimmedHistory);
      localStorage.setItem(
        `${org?.id ?? ''}-scorecard-insights-message-history`,
        JSON.stringify(trimmedHistory)
      );
    } else {
      localStorage.setItem(
        `${org?.id ?? ''}-scorecard-insights-message-history`,
        JSON.stringify(messageHistory)
      );
    }
  }, [messageHistory]);

  function clearMessageHistory() {
    if (isResponding) return;
    localStorage.removeItem(`${org?.id ?? ''}-scorecard-insights-message-history`);
    setMessageHistory([]);
  }

  return { messageHistory, isResponding, sendMessage, clearMessageHistory };
}