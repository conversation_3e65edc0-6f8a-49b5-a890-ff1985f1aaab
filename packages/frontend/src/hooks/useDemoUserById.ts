import DemoService from '@/lib/Demo';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useDemoUserById(id: number, enabled = true) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['demoUsers', id, ...commonQueryKeys],
    queryFn: () => DemoService.getDemoInboundFormResponseById(id),
    enabled: enabled && !isLoggedIn,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
