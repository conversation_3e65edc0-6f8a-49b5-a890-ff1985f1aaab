import { useMemo } from 'react';

const useTranscriptProgress = (totalWords: number) => {
  const intervalDurationInSeconds = 2; // Update interval in seconds
  const intervalDuration = intervalDurationInSeconds * 1000; // Update interval in seconds

  // Calculate the number of intervals based on totalWords
  const numberOfIntervals = useMemo(() => {
    const wordsPerSecond = 30;
    const expectedDurationInSeconds = totalWords / wordsPerSecond;
    // Ensure totalWords is greater than 0
    if (totalWords <= 0 || expectedDurationInSeconds <= 0) {
      console.error(
        'Invalid totalWords or expectedDuration, returning 0 for numberOfIntervals.',
      );
      return 0; // Return 0 if invalid
    }

    const intervals = Math.ceil(
      expectedDurationInSeconds / intervalDurationInSeconds,
    ); // Calculate number of intervals
    return intervals > 1 ? intervals : 2;
  }, [totalWords]);

  return { numberOfIntervals, intervalDuration };
};

export default useTranscriptProgress;
