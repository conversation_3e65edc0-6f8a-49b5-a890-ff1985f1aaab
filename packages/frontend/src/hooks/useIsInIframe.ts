import { usePathname } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

export const useIsInIframe = () => {
  const [hydrated, setHydrated] = useState(false);
  useEffect(() => {
    setHydrated(true);
  }, []);
  const pathname = usePathname();
  const isInIframe = useMemo(() => {
    let res = pathname.includes('/embed');
    if (typeof window !== 'undefined') {
      res = res || window !== window?.parent;
    }
    return res;
  }, []);
  if (!hydrated) {
    return false;
  }
  return isInIframe;
};
