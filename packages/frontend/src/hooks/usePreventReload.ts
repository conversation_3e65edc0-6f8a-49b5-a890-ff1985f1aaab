import { useEffect, useState } from 'react';

const usePreventReload = () => {
  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    // Warn before page reload or close
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isDirty) {
        event.preventDefault();
      }
    };

    // Prevent back button navigation
    const handleBackNavigation = (event: PopStateEvent) => {
      if (
        isDirty &&
        !window.confirm('You have unsaved changes. Leave anyway?')
      ) {
        event.preventDefault();
        history.pushState(null, '', window.location.href); // Restore state
      }
    };

    // Initialize history state
    history.pushState(null, '', window.location.href);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handleBackNavigation);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handleBackNavigation);
    };
  }, [isDirty]);

  return { setIsDirty };
};

export default usePreventReload;
