import ScorecardConfigService from '@/lib/ScorecardConfig';
import { useQuery } from '@tanstack/react-query';
import useUserSession from './useUserSession';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useScorecardConfigMaterials(
  scorecardConfigId: number,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const { isLoggedIn } = useUserSession();

  const query = useQuery({
    queryKey: ['scorecardConfigMaterials', scorecardConfigId, ...commonQueryKeys],
    queryFn: () =>
      ScorecardConfigService.getLearningMaterials(scorecardConfigId),
    enabled: enabled && isLoggedIn,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
