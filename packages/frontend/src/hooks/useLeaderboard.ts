import LeaderboardService from '@/lib/Leaderboard';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useLeaderboard(
  agentId: number,
  enabled: boolean = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['leaderboard', agentId, ...commonQueryKeys],
    queryFn: () => LeaderboardService.getLeaderboard(agentId),
    enabled: enabled, //&& !!agentId && (!isLoggedIn || (authInfo?.isLoggedIn && hbDemoInboundForm?.presClubChallengeRegistered))
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 2,
  });

  return query;
}
