import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from '../useCommonQueryKeys';
import { useIsLoggedIn } from '../useIsLoggedIn';

export default function useAgent(
  vapiId: string,
  enabled: boolean = true,
  withFolders: boolean = false, // Accept withFolders as a param
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['agent', vapiId, withFolders, ...commonQueryKeys], // Include withFolders in the key
    queryFn: () => AgentService.getAgentByVapiId(vapiId, withFolders),
    enabled: enabled && !!vapiId && isLoggedIn,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
