import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from '../useCommonQueryKeys';
import { useIsLoggedIn } from '../useIsLoggedIn';

export default function useAgent(
  providerId: string,
  enabled: boolean = true,
  withFolders: boolean = false, // Accept withFolders as a param
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['agent', providerId, withFolders, ...commonQueryKeys], // Include withFolders in the key
    queryFn: () => AgentService.getAgentByProviderId(providerId, withFolders),
    enabled: enabled && !!providerId && isLoggedIn,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
