import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallScorecard(
  vapiId: string,
  isDemo = false,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: [
      isDemo ? 'demoCallScorecard' : 'callScorecard',
      isDemo,
      vapiId,
      hbDemoInboundForm?.id,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      isDemo
        ? CallService.getDemoCallScorecard(
            vapiId,
            hbDemoInboundForm?.id as number,
          )
        : CallService.getCallScorecard(vapiId),
    enabled: enabled && !!vapiId && (isDemo ? !!hbDemoInboundForm?.id : true),
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 4000,
    retry: 10,
  });

  return query;
}
