import { CALL_TYPE_OPTIONS } from '@/common/CreateBuyerForm/constants';
import useUserSession from './useUserSession';
import { AgentCallType } from '@/lib/Agent/types';

export default function useCallTypeOptions() {
  const { showManagerCallType } = useUserSession();

  return {
    callTypeOptions: CALL_TYPE_OPTIONS.filter(
      (ct) =>
        ct.value !== AgentCallType.MANAGER_ONE_ON_ONE || showManagerCallType,
    ),
    showManagerCallType,
  };
}
