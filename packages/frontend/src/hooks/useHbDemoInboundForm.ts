import DemoService from '@/lib/Demo';
import Analytics from '@/system/Analytics';
import { useQuery } from '@tanstack/react-query';
import posthog from 'posthog-js';

export default function useHbDemoInboundForm(enabled = true) {
  const query = useQuery({
    queryKey: [process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY],
    queryFn: async () => {
      const data = JSON.parse(
        localStorage.getItem(
          process.env.NEXT_PUBLIC_DEMO_LOCAL_STORAGE_KEY as string,
        ) || '{}',
      );
      if (data?.id) {
        const demoInboundFormResponse =
          await DemoService.getDemoInboundFormResponseById(data?.id);
        posthog.identify(
          `demo-${demoInboundFormResponse?.id}`, // Replace 'distinct_id' with your user's unique identifier
          demoInboundFormResponse, // optional: set additional user properties
        );
        Analytics.identify(`demo-${demoInboundFormResponse?.id}`, {
          isDemo: true,
        });
        return demoInboundFormResponse;
      }

      return null;
    },
    enabled: enabled,
    staleTime: 0,
    refetchOnWindowFocus: true,
    retry: 2,
  });

  return query;
}
