import UserService from '@/lib/User';
import { useQuery } from '@tanstack/react-query';
import { useIsLoggedIn } from './useIsLoggedIn';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useOrgUserbyId(id: number, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['orgUsers', id, ...commonQueryKeys],
    queryFn: () => UserService.getOrgUserById(id),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
