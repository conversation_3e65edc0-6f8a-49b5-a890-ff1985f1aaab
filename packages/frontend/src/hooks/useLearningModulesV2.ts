/* eslint-disable @typescript-eslint/no-explicit-any */
import LearningModuleService from '@/lib/LearningModule';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import useUserSession from './useUserSession';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { LearningModuleTemplateStatus } from '@/lib/LearningModule/types';
import { toast } from 'react-toastify';

export default function useLearningModulesV2(
  status?: LearningModuleTemplateStatus,
  archived?: boolean,
  search?: string,
  start?: number,
  numberOfResults?: number,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const { isLoggedIn } = useUserSession();

  const query = useQuery({
    queryKey: [
      'learning-modules-v2',
      status,
      archived,
      search,
      start,
      numberOfResults,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      LearningModuleService.getAllV2WithFilters(
        status,
        archived,
        search,
        start,
        numberOfResults,
      ),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}

export function useLearningModuleV2(id: string, enabled = true) {
  if (!id?.length) {
    return {
      data: null,
      isLoading: false,
      isError: false,
    };
  }

  const commonQueryKeys = useCommonQueryKeys();
  const { isLoggedIn } = useUserSession();

  const query = useQuery({
    queryKey: ['learning-module-v2', id, ...commonQueryKeys],
    queryFn: () => LearningModuleService.getV2(id),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}

export function useCreateTemplate() {
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  return useMutation({
    mutationFn: LearningModuleService.createTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['learning-modules-v2', ...commonQueryKeys],
      });
      toast.success('Template created successfully');
    },
    onError: (error) => {
      console.error('Error creating template:', error);
      toast.error('Failed to create template');
    },
  });
}

export function useUpdateTemplate() {
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  return useMutation({
    mutationFn: ({ id, template }: { id: string; template: any }) =>
      LearningModuleService.updateTemplate(id, template),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: ['learning-modules-v2', ...commonQueryKeys],
      });
      queryClient.invalidateQueries({
        queryKey: ['learning-module-v2', id, ...commonQueryKeys],
      });
      toast.success('Template updated successfully');
    },
    onError: (error) => {
      console.error('Error updating template:', error);
      toast.error('Failed to update template');
    },
  });
}

export function useDeleteTemplate() {
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  return useMutation({
    mutationFn: LearningModuleService.deleteTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['learning-modules-v2', ...commonQueryKeys],
      });
      toast.success('Template deleted successfully');
    },
    onError: (error) => {
      console.error('Error deleting template:', error);
      toast.error('Failed to delete template');
    },
  });
}

export function useArchiveTemplate() {
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  return useMutation({
    mutationFn: LearningModuleService.archiveTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['learning-modules-v2', ...commonQueryKeys],
      });
      toast.success('Template archived successfully');
    },
    onError: (error) => {
      console.error('Error archiving template:', error);
      toast.error('Failed to archive template');
    },
  });
}

export function useUnarchiveTemplate() {
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  return useMutation({
    mutationFn: LearningModuleService.unarchiveTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['learning-modules-v2', ...commonQueryKeys],
      });
      toast.success('Template unarchived successfully');
    },
    onError: (error) => {
      console.error('Error unarchiving template:', error);
      toast.error('Failed to unarchive template');
    },
  });
}

export function useCloneTemplate() {
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  return useMutation({
    mutationFn: LearningModuleService.cloneTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['learning-modules-v2', ...commonQueryKeys],
      });
      toast.success('Template cloned successfully');
    },
    onError: (error) => {
      console.error('Error cloning template:', error);
      toast.error('Failed to clone template');
    },
  });
}

export function useLearningModuleAssignments(
  templateId: string,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  return useQuery({
    queryKey: ['learning-module-assignments', templateId, ...commonQueryKeys],
    queryFn: () => LearningModuleService.getTemplateAssignments(templateId),
    enabled: enabled && !!templateId,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 2,
  });
}

export function useAssignmentRules(templateId: string, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  return useQuery({
    queryKey: [
      'learning-module-assignment-rules',
      templateId,
      ...commonQueryKeys,
    ],
    queryFn: () => LearningModuleService.getTemplateAssignmentRules(templateId),
    enabled: enabled && !!templateId,
    staleTime: 60000,
    refetchOnWindowFocus: false,
    retry: 2,
  });
}

export function useAssignTemplate() {
  const queryClient = useQueryClient();
  const commonQueryKeys = useCommonQueryKeys();

  return useMutation({
    mutationFn: ({
      id,
      assignees,
      dueDate,
      startDate,
      assigneesDueDate,
      notifyUsersOnCompletion,
    }: {
      id: string;
      assignees: number[];
      dueDate: Date;
      startDate?: Date;
      assigneesDueDate?: Array<{ userId: number; dueDate: Date }>;
      notifyUsersOnCompletion?: number[];
    }) =>
      LearningModuleService.assignTemplate(
        id,
        assignees,
        dueDate,
        startDate,
        assigneesDueDate,
        notifyUsersOnCompletion,
      ),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: ['learning-modules-v2', ...commonQueryKeys],
      });
      queryClient.invalidateQueries({
        queryKey: ['learning-modules', ...commonQueryKeys],
      });
      queryClient.invalidateQueries({
        queryKey: ['learning-module-assignments', id, ...commonQueryKeys],
      });
      toast.success('Template assigned successfully');
    },
    onError: (error) => {
      console.error('Error assigning template:', error);
      toast.error('Failed to assign template');
    },
  });
}
