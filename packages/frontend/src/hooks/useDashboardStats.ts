import { AnalyticsFilters } from '@/contexts/AnalyticsContext';
import AnalyticsService from '@/lib/AnalyticsOld';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useDashboardStats(
  filters: AnalyticsFilters,
  enabled = true,
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['dashboardStats', filters, ...commonQueryKeys],
    queryFn: async () => AnalyticsService.getDashboardStats(filters),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
