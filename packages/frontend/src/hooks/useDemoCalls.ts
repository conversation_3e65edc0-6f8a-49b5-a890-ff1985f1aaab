import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import {
  CallsSortingParam,
  FilterType,
} from '../common/Calls/AIRoleplay/List/common';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useDemoCalls(
  enabled = true,
  from: number = 0,
  numberOfResults: number = 0,
  sortBy: CallsSortingParam[] = [],
  filterBy: Record<FilterType, any> = {
    [FilterType.BUYERS]: [],
    [FilterType.REPS]: [],
    [FilterType.PLAYLISTS]: [],
    [FilterType.DATE]: {
      fromDate: undefined,
      toDate: undefined,
    },
    [FilterType.CALL_TYPES]: [],
    [FilterType.TAGS]: [],
    [FilterType.CALL_BLITZ]: [],
    [FilterType.TEAMS]: [],
    [FilterType.STATUS]: [],
    [FilterType.ROLES]: [],
    [FilterType.LANGUAGES]: [],
    [FilterType.SCORECARDS]: [],
    [FilterType.SCORECARDS_SECTIONS]: [],
    [FilterType.SCORECARDS_CRITERIONS]: [],
    [FilterType.SCORECARDS_CRITERIONS_STATUS]: [],
    [FilterType.SEARCH]: undefined,
    [FilterType.MIN_DURATION]: undefined,
    [FilterType.MAX_DURATION]: undefined,
    [FilterType.FROM]: 0,
    [FilterType.NUM_RESULTS]: 20,
    [FilterType.OBJECTIONS]: [],
  },
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();
  const {
    data: hbDemoInboundForm,
    isLoading,
    isFetched,
  } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: [
      'demoCalls',
      hbDemoInboundForm?.id,
      from,
      numberOfResults,
      sortBy,
      filterBy,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      CallService.getDemoCalls(
        hbDemoInboundForm?.id as number,
        from,
        numberOfResults,
        sortBy,
        filterBy,
      ),
    enabled: enabled && !isLoggedIn && !!hbDemoInboundForm?.id,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
