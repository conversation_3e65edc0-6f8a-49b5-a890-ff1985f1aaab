import ScorecardConfigService from '@/lib/ScorecardConfig';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useScorecardConfigsForOrg(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['scorecardConfigsOptions', ...commonQueryKeys],
    queryFn: () => ScorecardConfigService.getAllScorecardConfigsForOrg(),
    enabled: enabled && isLoggedIn,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}

export function useGetShareHistory(
  scorecardId: number | undefined,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: [
      'get-scorecard-config-share-history',
      scorecardId,
      ...commonQueryKeys,
    ],
    queryFn: () => ScorecardConfigService.GetShareHistory(scorecardId),
    enabled: enabled && isLoggedIn,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
