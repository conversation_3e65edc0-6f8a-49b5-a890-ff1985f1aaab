import CompetitionLeaderboardService from '@/lib/Competition/Leaderboard';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCompetitionDetails(
  competitionTag: string,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['competitionDetails', competitionTag, ...commonQueryKeys],
    queryFn: () =>
      CompetitionLeaderboardService.getCompetitionDetails(competitionTag),
    enabled: enabled,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
