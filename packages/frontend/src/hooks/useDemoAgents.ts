import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useDemoAgents(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const {
    data: hbDemoInboundForm,
    isLoading,
    isFetched,
  } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: ['demoAgents', hbDemoInboundForm?.id, ...commonQueryKeys],
    queryFn: () => AgentService.getDemoAgents(hbDemoInboundForm?.id),
    enabled: enabled,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
