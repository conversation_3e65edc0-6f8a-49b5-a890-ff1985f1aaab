import { convertToParsedTranscriptMessages } from '@/common/CreateBuyerForm/Main/utils';
import { CreateBuyerBotEditFormContext } from '@/contexts/CreateBuyerBotContext/CreateBuyerBotEditForm';
import useUserSession from '@/hooks/useUserSession';
import { useContext, useEffect, useMemo } from 'react';

export interface MessageProp extends ParsedTranscriptMessage {
  avatar: string;
  altText: string;
  isInput?: boolean;
}

export default function useTranscript(): {
  transcript: MessageProp[];
  switchRoles: VoidFunction;
} {
  const { user } = useUserSession();
  const { form, currentMessages, setCurrentMessages, resumeCalls } = useContext(
    CreateBuyerBotEditFormContext,
  );

  function switchRoles(): void {
    setCurrentMessages(
      transcript
        .slice(1, transcript.length - 1)
        .map(({ id, message, role }) => ({
          id,
          message,
          role: role === 'buyer' ? 'seller' : 'buyer',
        })),
    );
  }

  const { avatar: botAvatar } = form.getValues();
  const botAssets = useMemo(
    () => ({
      avatar: botAvatar,
      altText: "Bot's Avatar",
    }),
    [botAvatar],
  );

  const userAssets = useMemo(
    () => ({
      avatar: `${user?.pictureUrl}`,
      altText: "User's Avatar",
    }),
    [user],
  );

  useEffect(() => {
    setCurrentMessages(convertToParsedTranscriptMessages(resumeCalls));
  }, [resumeCalls]);

  const transcript = useMemo(
    () =>
      currentMessages?.map((m) =>
        m.role === 'buyer'
          ? {
              ...m,
              ...botAssets,
            }
          : {
              ...m,
              ...userAssets,
            },
      ),
    [currentMessages, botAssets, userAssets],
  );

  return {
    transcript,
    switchRoles,
  };
}
