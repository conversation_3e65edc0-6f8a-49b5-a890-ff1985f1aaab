import { useQuery } from '@tanstack/react-query';
import CallBlitzService from '@/lib/CallBlitz';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallBliz(id: number, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['get-call-blitz', id, ...commonQueryKeys],
    queryFn: () => CallBlitzService.get(id),
    enabled: enabled,
    staleTime: 1000,
    refetchOnWindowFocus: true,
    retry: 2,
  });

  return query;
}
