import AnalyticsService from '@/lib/AnalyticsOld';
import { AnalyticsFilterState } from '@/lib/AnalyticsOld/types';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useDashboardChartData(
  chartType: string,
  filters: AnalyticsFilterState,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['dashboardChartData', chartType, filters, ...commonQueryKeys],
    queryFn: async () => AnalyticsService.getChartData(chartType, filters),
    enabled: enabled,
    staleTime: 10000,
    refetchOnWindowFocus: 'always',
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
