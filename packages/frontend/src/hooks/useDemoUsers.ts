import UserService from '@/lib/User';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useDemoUsers(enabled = true) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();
  const {
    data: hbDemoInboundForm,
    isLoading,
    isFetched,
  } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: ['demoUsers', hbDemoInboundForm?.id, ...commonQueryKeys],
    queryFn: () => UserService.getDemoUsers(hbDemoInboundForm?.id as number),
    enabled: enabled && !isLoggedIn && !!hbDemoInboundForm?.id,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
