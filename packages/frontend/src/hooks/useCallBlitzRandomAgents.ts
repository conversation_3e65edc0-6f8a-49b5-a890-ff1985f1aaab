import { useQuery } from '@tanstack/react-query';
import CallBlitzService from '@/lib/CallBlitz';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallBlizRandomAgents(
  sessionId: number,
  numberOfAgents: number = 10,
  skipAgents: number[] = [],
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'call-blitz-random-agents',
      sessionId,
      numberOfAgents,
      skipAgents,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      CallBlitzService.getRandomAgents(sessionId, numberOfAgents, skipAgents),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
