import { useQuery } from '@tanstack/react-query';
import UserService from '@/lib/User';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useUserImprovements(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['user-improvements', ...commonQueryKeys],
    queryFn: () => UserService.getUserImprovements(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}
