import { useEffect, useMemo, useRef, useState } from 'react';
import * as Sentry from '@sentry/nextjs';
import { AgentCallType, AnyAgentDto } from '@/lib/Agent/types';
import CallService from '@/lib/Call';
import { CallDto, VapiCallMetadata } from '@/lib/Call/types';
import { VAPI_EVENTS, VapiManager } from '@/lib/vapi-config';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Vapi from '@vapi-ai/web';
import { Id, toast } from 'react-toastify';
import useUserSession from './useUserSession';
import useHbDemoInboundForm from './useHbDemoInboundForm';

export class BotStatus {
  volume: number = 0;
  isTalking: boolean = false;
  isThinking: boolean = false;
}

export class CallStatus {
  isStarting: boolean = false;
  started: boolean = false;
  ended: boolean = false;
}

class UserMicMonitor {
  micMediaStream: MediaStream | undefined;
  audioContext: AudioContext | undefined;
  analyser: AnalyserNode | undefined;
  microphone: MediaStreamAudioSourceNode | undefined;

  constructor() {
    this.micMediaStream = undefined;
    this.audioContext = undefined;
    this.analyser = undefined;
    this.microphone = undefined;
  }

  async init() {
    const constraints = {
      video: false,
      audio: true,
    };

    this.micMediaStream =
      await navigator.mediaDevices.getUserMedia(constraints);

    this.audioContext = new AudioContext();
    this.analyser = this.audioContext.createAnalyser();
    this.microphone = this.audioContext.createMediaStreamSource(
      this.micMediaStream,
    );

    this.analyser.fftSize = 2048;

    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    this.analyser.getByteTimeDomainData(dataArray);

    this.microphone.connect(this.analyser);
  }

  getVolume(): number {
    if (this.analyser) {
      const array = new Uint8Array(this.analyser.frequencyBinCount);
      this.analyser.getByteFrequencyData(array);
      const arraySum = array.reduce((a, value) => a + value, 0);
      const average = arraySum / array.length;
      return Math.round(average);
    }
    return 0;
  }

  clear() {
    if (this.micMediaStream) {
      this.micMediaStream.getAudioTracks().forEach((track) => {
        track.stop();
      });
    }
  }
}
export default function useCallSimulation(
  agent: AnyAgentDto,
  onCallStarts?: () => void,
  onCallEnds?: (call: CallDto, isGatekeeperConnectingToBuyer: boolean) => void,
  onError?: (error: Error) => void,
  callBlitzSessionId?: number,
  onScreenShared?: (participant: {
    tracks: { screenVideo: { state: string } };
  }) => void,
  onMediaProblems?: (audioProblems: boolean, videoProblems: boolean) => void,
) {
  const queryClient = useQueryClient();
  const [call, setCall] = useState<CallDto>();
  const callRef = useRef<CallDto | null>(null); //needed to acces state from vapi events

  const { isLoggedIn } = useUserSession();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const createCallMutation = useMutation({
    mutationFn: isLoggedIn
      ? CallService.createCall
      : CallService.createDemoCall,
    onSuccess: (call) => {
      callRef.current = call;
      setCall(call);
      queryClient.invalidateQueries({ queryKey: ['orgCalls'] });
    },
    onError: () => {
      if (onError) {
        onError(new Error('There was an error creating a call in the db'));
      }
    },
  });

  const [isLoading, setIsLoading] = useState(true);
  const [callStatus, setCallStatus] = useState<CallStatus>(new CallStatus());
  const [botStatus, setBotStatus] = useState<BotStatus>(new BotStatus());
  const [callerStatus, setCallerStatus] = useState<BotStatus>(new BotStatus());
  const syncCallstarted = useRef<boolean>(false);

  /*********************************************************/
  /******************** USER MIC MONITOR *******************/
  /*********************************************************/

  const timeoutId = useRef<number | null>(null);

  const [userMicMonitor] = useState<UserMicMonitor>(new UserMicMonitor());

  const initUserMicMonitor = async () => {
    await userMicMonitor.init();
    monitorUserMic();
  };

  const clearMonitorUserMic = () => {
    //clear mic monitor
    userMicMonitor.clear();

    if (timeoutId.current) {
      clearTimeout(timeoutId.current);
    }
  };

  const monitorUserMic = () => {
    timeoutId.current = window.setTimeout(() => {
      const v: number = userMicMonitor.getVolume();
      if (v > 10) {
        setCallerStatus((old) => ({ ...old, isTalking: true }));
      } else {
        setCallerStatus((old) => ({ ...old, isTalking: false }));
      }
      monitorUserMic();
    }, 200);
  };

  /*********************************************************/
  /*********************** INIT ****************************/
  /*********************************************************/

  const vapi = useRef<Vapi | null>(null);

  const audioPickUp: HTMLAudioElement = useMemo(
    () => new Audio(`/audio/iphone-pickup.mp3`),
    [],
  );
  const audioRinging: HTMLAudioElement = useMemo(
    () => new Audio(`/audio/start-call.mp3`),
    [],
  );
  const audioEnded: HTMLAudioElement = useMemo(
    () => new Audio('/audio/call-end-tone.mp3'),
    [],
  );

  const errorToastId = useRef<Id | null>(null);

  const initVapiClient = async (n?: number) => {
    if (agent && agent.vapiId) {
      if (!n) {
        n = 0;
      }
      if (!vapi.current) {
        if (n && n > 5) {
          if (!toast.isActive(errorToastId.current as Id)) {
            errorToastId.current = toast.error(
              'We cannot reach our servers. Please reload the page or check your internet connection.',
            );
          }
        } else {
          let _vapi: Vapi | undefined = undefined;
          try {
            _vapi = await VapiManager.getClient(!isLoggedIn, agent.vapiId);
          } catch (e) {
            console.log(e);
            _vapi = undefined;
          }

          if (_vapi) {
            _vapi.on(VAPI_EVENTS.SPEECH_START, _onSpeechStarts);
            _vapi.on(VAPI_EVENTS.SPEECH_END, _onSpeechEnds);
            _vapi.on(VAPI_EVENTS.CALL_START, _onCallStarts);
            _vapi.on(VAPI_EVENTS.CALL_END, _onCallEnds);
            _vapi.on(VAPI_EVENTS.VOLUME_LEVEL, _onVolumeLevel);
            _vapi.on(VAPI_EVENTS.ERROR, _onVapiError);
            _vapi.on(VAPI_EVENTS.MESSAGE, _onMessage);

            vapi.current = _vapi;
            setIsLoading(false);
          } else {
            n++;
            setTimeout(() => {
              initVapiClient(n);
            }, 500);
          }
        }
      }
    }
  };

  const hasVapiClientBeenInitialized = useRef(false);
  useEffect(() => {
    if (!agent?.vapiId || hasVapiClientBeenInitialized.current) {
      return;
    }
    hasVapiClientBeenInitialized.current = true;
    initVapiClient();

    return () => {
      clearMonitorUserMic();
      if (audioRinging) {
        audioRinging.pause();
      }
      if (syncCallstarted.current) {
        _doEndCall(false);
      }
    };
  }, [agent?.vapiId]);

  /*********************************************************/
  /******************* EVENTS MANAGEMENT *******************/
  /*********************************************************/

  const _onSpeechStarts = () => {
    setBotStatus((old) => ({ ...old, isTalking: true }));
  };

  const _onSpeechEnds = () => {
    setBotStatus((old) => ({ ...old, isTalking: false }));
  };

  const _onCallStarts = () => {
    audioRinging.pause();
    if (audioPickUp) {
      audioPickUp.play();
    }

    setCallStatus({ isStarting: false, started: true, ended: false });
  };

  const _onCallEnds = () => {};

  const _onVolumeLevel = (volume: number) => {
    setBotStatus((old) => ({ ...old, volume }));
  };

  const _onVapiError = (e: { errorMsg?: string; error?: { type: string } }) => {
    console.log('Vapi Error: ', e, JSON.stringify(e));
    audioRinging.pause();
    let errorMsgToReturn =
      'There was an error with the recording. Please try again.';
    if (e.errorMsg == 'Meeting has ended' && e.error?.type == 'no-room') {
      //call exceeded max duration
      errorMsgToReturn = 'EXCEEDED_MAX_DURATION';
    } else if (
      e.errorMsg == 'not allowed' ||
      (e.error && e.error.type == 'permissions')
    ) {
      errorMsgToReturn = 'MIC_OR_VIDEO_PERMISSION_DENIED';
    }
    Sentry.captureException(
      new Error(errorMsgToReturn, {
        cause: e,
      }),
    );
    _onError(new Error(errorMsgToReturn));
  };

  const _onMessage = (message: {
    role: string;
    type: string;
    status?: string;
    transcriptType?: string;
    transcript?: string;
  }) => {
    if (message.role != 'user') {
      setBotStatus((old) => ({ ...old, isThinking: false }));
    } else if (
      message.type == 'speech-update' &&
      message.role == 'user' &&
      message.status == 'stopped'
    ) {
      setBotStatus((old) => ({ ...old, isThinking: true }));
    }

    //for focus, block assistant after first line
    if (agent.callType == AgentCallType.FOCUS) {
      if (
        message.role == 'assistant' &&
        message.type == 'speech-update' &&
        message.status == 'stopped' &&
        vapi.current
      ) {
        vapi.current.send({ type: 'control', control: 'mute-assistant' });
      }
    }

    if (
      message.transcriptType === 'final' &&
      message.role === 'assistant' &&
      message.transcript &&
      (message.transcript.includes('Goodbye') ||
        message.transcript.includes('goodbye') ||
        message.transcript.includes('revoir') ||
        message.transcript.includes('Adiós') ||
        message.transcript.includes('adiós') ||
        message.transcript.toLowerCase().includes("i'll connect you") ||
        message.transcript.toLowerCase().includes('i will connect you') ||
        message.transcript.toLowerCase().includes('connecting you now'))
    ) {
      if (
        message.transcript.toLowerCase().includes("i'll connect you") ||
        message.transcript.toLowerCase().includes('i will connect you') ||
        message.transcript.toLowerCase().includes('connecting you now')
      ) {
        _doEndCall(true);
      } else {
        _doEndCall(false);
      }
    }
  };

  /*********************************************************/
  /********************** USER ACTIONS *********************/
  /*********************************************************/

  const startCall = (useVideoCall: boolean = false) => {
    syncCallstarted.current = true;
    setCallStatus({ isStarting: true, started: false, ended: false });
    if (audioRinging) {
      audioRinging.currentTime = 0;
      audioRinging.loop = true;
      audioRinging.play();
    }

    _doStartCall(useVideoCall);
  };

  const _doStartCall = (useVideoCall: boolean = false) => {
    if (!syncCallstarted.current) {
      return;
    }

    try {
      vapi.current
        ?.start?.(agent.vapiId, {
          artifactPlan: {
            videoRecordingEnabled: useVideoCall,
          },
        })
        ?.then?.((vapiCall) => {
          if (vapiCall) {
            console.log('Call Created', vapiCall?.id, vapiCall?.status);
            const createCallParams = {
              vapiId: vapiCall?.id,
              assistantId: vapiCall?.assistantId as string,
              createdAt: vapiCall?.createdAt,
              updatedAt: vapiCall?.updatedAt,
              type: vapiCall?.type as string,
              status: vapiCall?.status || '',
              vapiMetadata: vapiCall as VapiCallMetadata,
              ...(isLoggedIn
                ? {
                    callBlitzSessionId,
                  }
                : {
                    demoInboundFormResponseId: hbDemoInboundForm?.id,
                  }),
            };
            createCallMutation.mutate(createCallParams);
          }
          if (onCallStarts) {
            onCallStarts();
          }
          initUserMicMonitor();
        });
    } catch (err) {
      console.log('CALL INIT ERROR:', err);
      audioRinging.pause();
      _onError(new Error('Recording could not be started. Try again later.'));
    }

    setCallStatus({ isStarting: false, started: true, ended: false });

    checkUserMedia(useVideoCall);
  };

  const _doEndCall = (isGatekeeperConnectingToBuyer: boolean) => {
    syncCallstarted.current = false;

    let skipVapiCall = false;
    if (callStatus.started == false) {
      if (audioRinging) {
        audioRinging.pause();
      }
      skipVapiCall = true;
      try {
        vapi.current?.stop();
      } catch (e) {
        console.log(e);
      }
    }

    clearMonitorUserMic();
    if (audioEnded) {
      audioEnded.play();
    }
    setCallStatus({ isStarting: false, started: false, ended: true });

    if (!skipVapiCall) {
      try {
        //delay needed or it would truncate agent's last message
        setTimeout(() => {
          vapi.current?.stop();
        }, 1000);
      } catch (e) {
        console.log(e);
      }
    }

    if (onCallEnds) {
      if (call) {
        onCallEnds(call, isGatekeeperConnectingToBuyer);
      } else if (callRef.current) {
        onCallEnds(callRef.current, isGatekeeperConnectingToBuyer);
      }
    }
  };

  const endCall = () => {
    _doEndCall(false);
  };

  const checkUserMedia = async (useVideoCall: boolean) => {
    const call = vapi.current?.getDailyCallObject();
    if (call) {
      const devices = await call.getInputDevices();
      let errorAudio = true;
      let errorVideo = true;
      if (devices) {
        if (devices.mic) {
          if (Object.keys(devices.mic).length > 0) {
            errorAudio = false;
          }
        }
        if (useVideoCall) {
          if (devices.camera) {
            if (Object.keys(devices.camera).length > 0) {
              errorVideo = false;
            }
          }
        }
      }
      if (!useVideoCall) {
        errorVideo = false;
      }

      if (errorAudio || errorVideo) {
        if (onMediaProblems) {
          onMediaProblems(errorAudio, errorVideo);
        }
      }
    }
  };

  const getCallInfo = (): CallDto | undefined => {
    if (call) {
      return call;
    } else if (callRef.current) {
      return callRef.current;
    } else {
      return undefined;
    }
  };

  const _onError = (error: Error) => {
    if (onError) {
      onError(error);
    }
  };

  const getLocalPartecipant = () => {
    const call = vapi.current?.getDailyCallObject();
    const p = call?.participants();
    return p?.local;
  };

  const onScreenShare = (e: {
    participant?: {
      tracks?: {
        screenVideo?: {
          state: string;
        };
      };
    };
  }) => {
    if (!e) return;

    if (
      e.participant?.tracks?.screenVideo?.state === 'playable' &&
      onScreenShared &&
      e.participant
    ) {
      onScreenShared(
        e.participant as { tracks: { screenVideo: { state: string } } },
      );
    }
  };

  const startScreenShare = () => {
    const call = vapi.current?.getDailyCallObject();
    if (call) {
      call.on('participant-updated', onScreenShare);
    }

    call?.startScreenShare(); //{ displayMediaOptions: { video: true, audio: false }, }
  };

  const stopScreenShare = () => {
    const call = vapi.current?.getDailyCallObject();
    if (call) {
      call.off('participant-updated', onScreenShare);
    }
    call?.stopScreenShare();
  };

  return {
    startCall,
    endCall,
    getCallInfo,
    isLoading,
    callStatus,
    botStatus,
    callerStatus,
    getLocalPartecipant,
    startScreenShare,
    stopScreenShare,
  };
}
