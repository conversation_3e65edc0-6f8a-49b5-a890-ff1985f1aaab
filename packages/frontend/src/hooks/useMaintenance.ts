import MaintenanceService from '@/lib/Maintenance';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useMaintenance(isDemo?: boolean, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['maintenance', isDemo, ...commonQueryKeys],
    queryFn: async () => {
      let data;
      if (isDemo) {
        data = await MaintenanceService.getDemoLatestActiveMaintenance();
      } else {
        data = await MaintenanceService.getLatestActiveMaintenance();
      }
      return !data ? null : data;
    },
    enabled: enabled,
    staleTime: 20000,
    refetchOnWindowFocus: true,
  });

  return query;
}
