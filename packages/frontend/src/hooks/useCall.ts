import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCall(
  vapiId: string,
  isDemo = false,
  enabled = true,
) {
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      isDemo ? 'demoCall' : 'call',
      isDemo,
      vapiId,
      hbDemoInboundForm?.id,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      isDemo
        ? CallService.getDemoCall(vapiId, hbDemoInboundForm?.id as number)
        : CallService.getCall(vapiId),
    enabled: enabled && !!vapiId && (isDemo ? !!hbDemoInboundForm?.id : true),
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
