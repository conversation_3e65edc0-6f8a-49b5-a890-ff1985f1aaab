import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCall(
  callId: string,
  isDemo = false,
  enabled = true,
) {
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: [
      isDemo ? 'demoCall' : 'call',
      isDemo,
      callId,
      hbDemoInboundForm?.id,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      isDemo
        ? CallService.getDemoCall(callId, hbDemoInboundForm?.id as number)
        : CallService.getCall(callId),
    enabled: enabled && !!callId && (isDemo ? !!hbDemoInboundForm?.id : true),
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });
  return query;
}
