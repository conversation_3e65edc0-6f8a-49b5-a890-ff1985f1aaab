import AiService from '@/lib/Ai';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';
import { useMemo } from 'react';

export default function useResumeCallsParser(messages: string) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['aiService', messages], // Cache based on 'messages'
    queryFn: () => {
      const trimmed = messages.replace(/^\s*[\r\n]/gm, '').trim();
      return AiService.resumeCallsParse(trimmed);
    },
    enabled: false, // Disable automatic fetching
    retry: 0, // No automatic retries
    staleTime: 0, // Data is immediately marked as stale
  });

  const totalWords = useMemo(
    () =>
      // Calculate total words, handling whitespace
      messages?.trim().split(/\s+/).length || 0,
    [messages],
  );

  const fetchQuery = async () => {
    if (!isLoggedIn || !messages) return;

    return await query.refetch();
  };

  return { ...query, fetchQuery, totalWords };
}
