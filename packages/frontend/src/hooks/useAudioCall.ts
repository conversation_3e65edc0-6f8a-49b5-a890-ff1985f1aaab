import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useAudioCall(
  callVapiId: string,
  canFetch = false,
  isDemo = false,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const params = useParams();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: [
      isDemo ? 'demoAudioCall' : 'audioCall',
      isDemo,
      callVapiId,
      params?.id,
      hbDemoInboundForm?.id,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      isDemo
        ? CallService.getDemoAudioCall(
            params?.id as string,
            hbDemoInboundForm?.id as number,
          )
        : CallService.getAudioCall(callVapiId),
    enabled: canFetch,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
