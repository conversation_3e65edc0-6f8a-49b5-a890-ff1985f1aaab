import { CallScorecardDto } from '@/lib/Call/types';
import { useMemo } from 'react';
import useUserSession from './useUserSession';

export default function useCallScoreInfo(scorecard?: CallScorecardDto) {
  const { isCompetitionOrg, scoreMessages, showCallScoreTitle } =
    useUserSession();

  const getTitleAndColorFromScore = (score: number) => {
    let title =
        scoreMessages?.default ?? "Just the beginning, you'll get there!",
      color = 'rgb(239, 68, 68)';
    if (score === 100) {
      title = scoreMessages?.['100'] ?? 'Awesome, this call was perfect!';
      color = 'rgb(34, 197, 94)';
    } else if (score > 80 && score < 100) {
      title = scoreMessages?.['80'] ?? "Great job, you're on track!";
      color = 'rgb(34, 197, 94)';
    } else if (score > 60) {
      title = scoreMessages?.['60'] ?? "Good job, you're almost there!";
      color = 'rgb(234, 179, 8)';
    } else if (score > 40) {
      title = scoreMessages?.['40'] ?? 'Uh oh, you need some work!';
      color = 'rgb(249, 115, 22)';
    }
    return { title, color };
  };

  let overallCallScore = 'N/A';
  let { title: overallCallScoreTitle, color: overallCallScoreColor } =
    getTitleAndColorFromScore(0);

  if (scorecard) {
    let _overallCallScore = Number(
      (scorecard?.passedScore / scorecard?.totalScore) * 100 || 0,
    );

    if (scorecard?.passedScore == 0) {
      _overallCallScore = 0;
    }

    overallCallScore = _overallCallScore.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    ({ title: overallCallScoreTitle, color: overallCallScoreColor } =
      getTitleAndColorFromScore(_overallCallScore));
  }

  const info = useMemo(() => {
    let callScoreTitle = '',
      callScore = '',
      callScoreColor = '';

    if (isCompetitionOrg) {
      const aggScore = (scorecard?.aggregateScore || 0) * 100;
      const aggScoreStr = aggScore.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 1,
      });
      callScore = aggScoreStr;
      callScoreTitle = 'Click on the score to view the breakdown';
      ({ color: callScoreColor } = getTitleAndColorFromScore(aggScore));
    } else {
      const overallScore = scorecard
        ? Number((scorecard.passedScore / scorecard.totalScore) * 100)
        : 0;
      const overallScoreStr = overallScore.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      });
      callScore = overallScoreStr;
      ({ title: callScoreTitle, color: callScoreColor } =
        getTitleAndColorFromScore(overallScore));
      callScoreTitle = showCallScoreTitle ? callScoreTitle : 'Your Score';
    }

    return {
      callScoreTitle,
      callScore,
      callScoreColor,
    };
  }, [
    isCompetitionOrg,
    overallCallScore,
    overallCallScoreTitle,
    overallCallScoreColor,
  ]);

  return info;
}
