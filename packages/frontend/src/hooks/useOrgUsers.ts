import UserService from '@/lib/User';
import { useQuery } from '@tanstack/react-query';
import { useIsLoggedIn } from './useIsLoggedIn';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useOrgUsers(
  enabled = true,
  from: number = 0,
  numberOfResults: number = 0,
  search: string = '',
  includePending = false,
  sorting: Record<string, 'asc' | 'desc'> = {},
  teams: number[] = [],
  roles: string[] = [],
  status: string[] = [],
) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: [
      'get-all-org-users',
      from,
      numberOfResults,
      search,
      sorting,
      teams,
      roles,
      status,
      includePending,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      UserService.getOrgUsers(
        from,
        numberOfResults,
        search,
        includePending,
        sorting,
        teams,
        roles,
        status,
      ),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
