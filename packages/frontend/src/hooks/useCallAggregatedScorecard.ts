import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallAggregatedScorecard(
  vapiId: string,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['callAggregatedScorecard', vapiId, ...commonQueryKeys],
    queryFn: () => CallService.getCallAggregatedScorecard(vapiId),
    enabled: enabled && !!vapiId,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 3000,
    retry: 5,
  });

  return query;
}
