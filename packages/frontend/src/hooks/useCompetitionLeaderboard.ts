import CompetitionLeaderboardService from '@/lib/Competition/Leaderboard';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCompetitionLeaderboards(
  competitionTag: string,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['competitionLeaderboard', competitionTag, ...commonQueryKeys],
    queryFn: () =>
      CompetitionLeaderboardService.getCompetitionLeaderboards(competitionTag),
    enabled: enabled,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
