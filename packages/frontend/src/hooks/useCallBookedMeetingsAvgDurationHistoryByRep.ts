import { AnalyticsFilters } from '@/contexts/AnalyticsContext';
import AnalyticsService from '@/lib/AnalyticsOld';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useCallBookedMeetingsAvgDurationHistoryByRep(
  filters: AnalyticsFilters,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: [
      'callBookedMeetingsAvgDurationHistoryByRep',
      filters,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      AnalyticsService.getCallBookedMeetingsAvgDurationHistoryByRep(filters),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
