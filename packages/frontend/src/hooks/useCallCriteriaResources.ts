import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallCriteriaResources(
  callId: string,
  agentId: number,
  scorecardConfigId: number,
  criteria: string,
  finalCallScore: number,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'call-criteria-resources',
      callId,
      agentId,
      scorecardConfigId,
      criteria,
      finalCallScore,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      CallService.getCallCoachingResources(
        callId,
        agentId,
        scorecardConfigId,
        criteria,
        finalCallScore,
      ),
    enabled: enabled && !!scorecardConfigId,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}
