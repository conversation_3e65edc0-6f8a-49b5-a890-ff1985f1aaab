import { AnalyticsFilters } from '@/contexts/AnalyticsContext';
import AnalyticsService from '@/lib/Analytics';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useAvgScorecardStatsByRep(
  statsTitle:
    | 'FillerWords'
    | 'LongestMonologue'
    | 'TalkListenRatio'
    | 'TalkSpeed',
  filters: AnalyticsFilters,
  enabled = true,
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'avgScorecardStatsByRep',
      statsTitle,
      filters,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      AnalyticsService.getAvgScorecardStatsByRep(statsTitle, filters),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
