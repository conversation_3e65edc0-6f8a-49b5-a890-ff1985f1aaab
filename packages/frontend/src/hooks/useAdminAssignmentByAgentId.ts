import AssignmentService from '@/lib/Assignment';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useAdminAssignmentByAgentId(
  agentId: number,
  enabled = true,
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['adminAssignmentsByAgentId', agentId, ...commonQueryKeys],
    queryFn: () => AssignmentService.getAdminAssignmentByAgentId(agentId),
    enabled: enabled && isLoggedIn && !!agentId,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
