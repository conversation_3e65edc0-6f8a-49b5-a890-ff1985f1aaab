import UserService from '@/lib/User';
import { useQuery } from '@tanstack/react-query';
import { useIsLoggedIn } from './useIsLoggedIn';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useOrgUsersByIds(
  includeUsers: number[] = [],
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['orgUsersByIds', includeUsers, ...commonQueryKeys],
    queryFn: () => UserService.getOrgUsersByIds(includeUsers),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
