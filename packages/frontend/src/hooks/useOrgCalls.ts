import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import {
  CallsSortingParam,
  FilterType,
} from '../common/Calls/AIRoleplay/List/common';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

//
// f=from = start loading here
// n=number of results to be returned
//
export default function useOrgCalls(
  from: number,
  numberOfResults: number,
  sortBy: CallsSortingParam[],
  filterBy?: Record<FilterType, any>,
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'orgCalls',
      from,
      numberOfResults,
      sortBy,
      filterBy,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      CallService.getOrgCalls(from, numberOfResults, sortBy, filterBy),
    enabled: isLoggedIn,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
