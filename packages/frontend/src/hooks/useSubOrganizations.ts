import OrganizationService from '@/lib/Organization';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useSubOrganizations(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['sub-orgs', ...commonQueryKeys],
    queryFn: () => OrganizationService.getSubOrganizations(),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
