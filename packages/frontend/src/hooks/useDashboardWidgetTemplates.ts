import AnalyticsService from '@/lib/AnalyticsOld';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useDashboardWidgetTemplates(enabled: boolean = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['dashboard-widget-emplates', ...commonQueryKeys],
    queryFn: () => AnalyticsService.getWidgetTemplates(),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
