import ScorecardConfigService from '@/lib/ScorecardConfig';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useScorecardsSections(
  enabled = true,
  scorecards: number[] = [],
  from = 0,
  numberOfResults = 10,
  search = '',
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'scorecardConfigsSections',
      scorecards,
      from,
      numberOfResults,
      search,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      ScorecardConfigService.getSections(
        scorecards,
        from,
        numberOfResults,
        search,
      ),
    enabled: enabled,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
