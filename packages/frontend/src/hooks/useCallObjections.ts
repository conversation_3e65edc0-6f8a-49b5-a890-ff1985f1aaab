import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallObjections(
  callId: string,
  isDemo = false,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: [
      isDemo ? 'demoCallObjections' : 'callObjections',
      isDemo,
      callId,
      hbDemoInboundForm?.id,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      isDemo
        ? CallService.getDemoCallObjections(
            callId,
            hbDemoInboundForm?.id as number,
          )
        : CallService.getCallObjections(callId),
    enabled: enabled && !!callId && (isDemo ? !!hbDemoInboundForm?.id : true),
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
