import { useQuery } from '@tanstack/react-query';
import Team from '@/lib/User/Team';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useTeam(teamId: number, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['get-team', teamId, ...commonQueryKeys],
    queryFn: () => Team.get(teamId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: true,
    retry: 3,
  });

  return query;
}
