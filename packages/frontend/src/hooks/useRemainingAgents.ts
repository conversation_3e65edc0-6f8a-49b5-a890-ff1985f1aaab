import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useRemainingAgents(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: ['remainingAgents', ...commonQueryKeys],
    queryFn: () => AgentService.getRemainingAgents(),
    enabled: enabled,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
