import { StepOneData } from '@/lib/Agent/types';
import ScorecardConfigDto from '@/lib/ScorecardConfig/types';
import { useMemo, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface UseFilteredScorecardConfigOptionsProps {
  scorecardConfigOptions: ScorecardConfigDto[] | undefined;
  callType: string;
  form: UseFormReturn<StepOneData, unknown, undefined>;
}

export function useFilteredScorecardConfigOptions({
  scorecardConfigOptions,
  callType,
  form,
}: UseFilteredScorecardConfigOptionsProps) {
  const filteredScorecardConfigOptions = useMemo(() => {
    if (!scorecardConfigOptions) {
      return [];
    }
    return scorecardConfigOptions.filter((sc) => {
      if (!sc.callTypes?.length) {
        return true; // Applicable to all call types if none are specified
      }
      return sc.callTypes.some((c) => c.callType === callType);
    });
  }, [scorecardConfigOptions, callType]);

  useEffect(() => {
    let defaultScorecardConfigId = '';

    for (const scorecardConfig of filteredScorecardConfigOptions) {
      if (scorecardConfig.callTypes && scorecardConfig.id) {
        const callTypeOpt = scorecardConfig.callTypes.find(
          (ct) => ct.callType === callType,
        );

        if (callTypeOpt) {
          defaultScorecardConfigId = scorecardConfig.id.toString();
          if (callTypeOpt.isDefaultForCallType) {
            break; // Prioritize the default scorecard for the call type
          }
        }
      }
    }

    if (defaultScorecardConfigId) {
      form.setValue('scorecardConfigId', defaultScorecardConfigId);
    }
  }, [filteredScorecardConfigOptions, callType, form]);

  return filteredScorecardConfigOptions;
}
