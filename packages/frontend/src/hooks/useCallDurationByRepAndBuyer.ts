import { AnalyticsFilters } from '@/contexts/AnalyticsContext';
import AnalyticsService from '@/lib/Analytics';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useCallDurationByRepAndBuyer(
  filters: AnalyticsFilters,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['callDurationByRepAndBuyer', filters, ...commonQueryKeys],
    queryFn: async () => AnalyticsService.getCallDurationByRepAndBuyer(filters),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
