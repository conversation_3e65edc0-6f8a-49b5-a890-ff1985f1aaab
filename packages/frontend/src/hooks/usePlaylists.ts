import PlaylistService from '@/lib/Playlist';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function usePlaylists(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['playlists', ...commonQueryKeys],
    queryFn: () => PlaylistService.getPlaylists(),
    enabled: enabled && isLoggedIn,
    staleTime: 1000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
