import UserService from '@/lib/User';
import Analytics from '@/system/Analytics';
import { useQuery } from '@tanstack/react-query';
import posthog from 'posthog-js';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useOrgCurrentUser(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['orgCurrentUser', ...commonQueryKeys],
    queryFn: async () => {
      const user = await UserService.getOrgCurrentUser();

      posthog.identify(
        String(user.id), // Replace 'distinct_id' with your user's unique identifier
        user, // optional: set additional user properties
      );
      Analytics.identify(String(user.id));

      return user;
    },
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
