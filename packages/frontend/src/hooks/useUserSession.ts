import { RoleEnum } from '@/lib/User/types';
import {
  useActiveOrg,
  useAuthInfo,
  useRedirectFunctions,
  useOrgHelper,
} from '@propelauth/react';
import dayjs from 'dayjs';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useMemo } from 'react';
import useOrg from './useOrg';
import useOrgCurrentUser from './useOrgCurrentUser';
import useSubOrganizations from './useSubOrganizations';
import { CALL_SCENARIO_OPTIONS as DEFAULT_CALL_SCENARIO_OPTIONS } from '@/common/CreateBuyerForm/constants';
import { AgentCallType } from '@/lib/Agent/types';
import { blacklistedOrgIds } from '@/app/auth-wrapper';
import {
  hyperboundMeetingRecorderProviderName,
  zoomLiveRecorderProviderName,
} from '@/common/Integrations';
import { useIsInIframe } from './useIsInIframe';
import useUserOrgs from './useUserOrgs';
import { OrgStatus } from '@/lib/Organization/types';
import { AppPermissions, ROLES_BY_PERMISSION } from '@/lib/permissions';
import { AnalyticsFilterDateRange } from '@/lib/AnalyticsOld/types';

export enum RedirectTo {
  BACK = '-back-',
  HOME = '/',
  PAGE_NOT_FOUND = '/404',
  LOGIN = '-login-',
  SIGN_UP = '-sign-up-',
  ORG_PAGE = '-org-page-',
  ACCOUNT_PAGE = '-account-page-',
}

export type BrandingLogo = {
  url: string;
  altText?: string;
  width: string;
  height: string;
};

export type PoweredByBranding = {
  text?: string;
  logo?: BrandingLogo;
  iconLogo?: BrandingLogo;
};

export default function useUserSession(
  restrictAccessToLoggedUsers?: boolean,
  redirectTempUsers?: boolean,
  restrictAccessToAdmin?: boolean,
  redirectTo?: RedirectTo | string,
) {
  const authInfo = useAuthInfo();
  const orgHelper = useOrgHelper();
  const router = useRouter();
  const pathname = usePathname();
  const org = useActiveOrg();
  const { data: userOrgs } = useUserOrgs();
  const { data: dbOrg, isLoading: isDbOrgLoading } = useOrg();
  const { data: dbUser, isLoading: isDbUserLoading } = useOrgCurrentUser();
  const { data: subOrganizations } = useSubOrganizations();
  const {
    redirectToLoginPage,
    redirectToSignupPage,
    redirectToOrgPage,
    redirectToAccountPage,
  } = useRedirectFunctions();
  const isPilotEnded = dayjs(
    dbOrg?.pilotDetails?.expiryDate ?? new Date(),
  ).isBefore(dayjs());
  const manageRedirect = () => {
    if (redirectTo && redirectTo != '') {
      if (Object.values<string>(RedirectTo).includes(redirectTo)) {
        if (redirectTo == RedirectTo.BACK) {
          router.back();
        } else if (redirectTo == RedirectTo.LOGIN) {
          redirectToLoginPage();
        } else if (redirectTo == RedirectTo.SIGN_UP) {
          redirectToSignupPage();
        } else if (redirectTo == RedirectTo.ORG_PAGE) {
          redirectToOrgPage();
        } else if (redirectTo == RedirectTo.ACCOUNT_PAGE) {
          redirectToAccountPage();
        } else {
          router.push(redirectTo);
        }
      } else {
        router.push(redirectTo);
      }
    }
  };

  if (restrictAccessToLoggedUsers) {
    if (!authInfo.isLoggedIn) {
      manageRedirect();
    }
  }
  const isOrgActive = useMemo(() => {
    return !dbOrg || dbOrg.status === OrgStatus.ACTIVE;
  }, [dbOrg?.status]);

  useEffect(() => {
    if (authInfo.isLoggedIn && !isOrgActive && pathname !== 'home') {
      router.replace('/home');
    }
  }, [isOrgActive, pathname, authInfo.isLoggedIn]);

  // useEffect to manage redirecto to restricted page
  useEffect(() => {
    if (
      dbOrg?.frontEndConf?.isHyperboundAppRestricted &&
      !isDbOrgLoading &&
      pathname !== '/restricted'
    ) {
      router.replace('/restricted');
    }
  }, [
    dbOrg?.frontEndConf?.isHyperboundAppRestricted,
    isDbOrgLoading,
    pathname,
  ]);

  const isTemp = authInfo.accessHelper?.isRole(
    org?.orgId as string,
    RoleEnum.TEMP,
  );
  if (redirectTempUsers && isTemp) {
    manageRedirect();
  }

  if (restrictAccessToAdmin) {
    if (
      !authInfo.accessHelper?.isAtLeastRole(
        org?.orgId as string,
        RoleEnum.ADMIN,
      )
    ) {
      manageRedirect();
    }
  }

  let useOldSimulationPanel = false;
  let blurLeaderboard = false;
  let blurAiRoleplayPage = !!dbOrg?.isCompetitionOrg;
  let canAccessIntegrations = false;
  const betaIntegrations = [
    'Zoom',
    hyperboundMeetingRecorderProviderName,
    'HubSpot',
    'Google',
    'Microsoft',
    'Orum',
    'Salesloft',
    'Chorus',
    'ZoomPhone',
    'Aircall',
    'Glyphic',
    'GONG',
  ];
  const betaIntegrationsAllowed: string[] = [];
  let canAccessRealCallsScoring = false;
  // video calls enabled for the demo site
  let videoCallEnabled = !authInfo.isLoggedIn;
  let useNewSidebar = false;
  let blurSecondaryPages = false;
  let competitionEndDate = null;
  let hideHomePage = false;
  let useNewScorecardsUI = true;
  let aiCoachTextOverride = '';
  // dont show the logo when db org is loading to prevent flashing
  let hideHyperboundLogoInIframe = isDbOrgLoading;
  let showLeaderboardInHomePage = false;
  const isInIframe = useIsInIframe();
  let poweredByBranding: PoweredByBranding | undefined;
  let hideChatButtonInIframe = false;
  let showTeamsInLeaderboard = false;
  let showPlanAssessment = false;
  let onlyAccessFromIframes = false;
  let onlyAdminsCanDeleteUserCalls = false;
  let onlyAdminsCanExportCalls = false;
  let canRescoreCalls = true;
  let allowAgentPublicToggle = false;
  let allBotsPage_showTagsForBots = false;
  let useBotBuilderV2 = true;
  let useNewPostCall = true;
  let useMultiParty = false;
  let scoreMessages: Record<string, string> = {};
  let hideCoaching = false;
  let hideSimilarCalls = false;
  let hideLearningMaterials = false;
  let canAccessInternalCompetitions = true;
  let hideCallContext = false;
  let isCallHistoryLimitedUi = false;
  let onlyAdminsCanViewMedia = false;
  let onlyAdminsCanSeeMembersPage = false;
  let onlyAdminsCanFilterCalls = false;
  let onlyAdminsCanCreatePlaylists = false;
  let defaultLeaderboardDateRange = AnalyticsFilterDateRange.LAST_NINETY_DAYS;
  let hiddenRolesAllowed: string[] = [];
  let useIntegrationsV2 = false;
  let canAccessScorecardsMaterials = false;
  let showCallScoreTitle = true;
  let hideAiRoleplay = false;
  let hideAllBots = false;
  let isScormEnabled = true;
  let isRepInstructionsMandatory = false;
  let showManagerCallType = false;
  let useLearningModulesV2 = false;
  let showCopySSOUrlButton = false;
  let isManagerHierarchyEnabled = false;
  const userOrgsWithIsAdmin = useMemo(() => {
    return userOrgs?.map((uo) => ({
      ...uo,
      isAdmin: !!authInfo.accessHelper?.isAtLeastRole(
        uo.orgUid,
        RoleEnum.ADMIN,
      ),
    }));
  }, [userOrgs, authInfo?.accessHelper]);

  const loadedAllOrgs = useMemo(
    () =>
      orgHelper?.orgHelper
        ?.getOrgs()
        ?.every((org) =>
          userOrgs?.find((userOrg) => org.orgId === userOrg.orgUid),
        ),
    [orgHelper, userOrgs],
  );

  const isAdmin = !!authInfo.accessHelper?.isAtLeastRole(
    org?.orgId as string,
    RoleEnum.ADMIN,
  );

  const role =
    RoleEnum[
      Object.keys(RoleEnum).find((roleKey) =>
        authInfo.accessHelper?.isRole(
          org?.orgId as string,
          RoleEnum[roleKey as keyof typeof RoleEnum],
        ),
      ) as keyof typeof RoleEnum
    ];

  const canAccess = (permission: AppPermissions): boolean =>
    ROLES_BY_PERMISSION[permission].some((item) => item === role);

  if (dbOrg?.frontEndConf) {
    if (dbOrg.frontEndConf.useOldSimulationPanel) {
      useOldSimulationPanel = true;
    }
    if (
      dbOrg.frontEndConf.blurLeaderboard &&
      !canAccess(AppPermissions.VIEW_LEADERBOARD)
    ) {
      blurLeaderboard = true;
    }
    if (dbOrg.frontEndConf.allowAgentPublicToggle) {
      allowAgentPublicToggle = true;
    }
    if (dbOrg.frontEndConf.canAccessIntegrations) {
      canAccessIntegrations = true;
    }
    if (dbOrg.frontEndConf.canAccessRealCallsScoring) {
      canAccessRealCallsScoring = true;
    }
    if (dbOrg.frontEndConf.useBotBuilderV2) {
      useBotBuilderV2 = dbOrg.frontEndConf.useBotBuilderV2;
    }

    if (dbOrg.frontEndConf.useNewPostCall !== undefined) {
      useNewPostCall = dbOrg.frontEndConf.useNewPostCall;
    }

    if (dbOrg.frontEndConf.useMultiParty !== undefined) {
      useMultiParty = dbOrg.frontEndConf.useMultiParty;
    }

    if (dbOrg.frontEndConf.onlyAdminsCanSeeMembersPage) {
      onlyAdminsCanSeeMembersPage = true;
    }

    if (dbOrg.frontEndConf.onlyAdminsCanFilterCalls) {
      onlyAdminsCanFilterCalls = true;
    }

    if (dbOrg.frontEndConf.onlyAdminsCanCreatePlaylists) {
      onlyAdminsCanCreatePlaylists = true;
    }

    if (dbOrg.frontEndConf.defaultLeaderboardDateRange) {
      defaultLeaderboardDateRange = dbOrg.frontEndConf
        .defaultLeaderboardDateRange as AnalyticsFilterDateRange;
    }

    if (dbOrg.frontEndConf.betaIntegrationsAllowed) {
      betaIntegrationsAllowed.push(
        ...dbOrg.frontEndConf.betaIntegrationsAllowed,
      );

      // for backwards compatibility
      if (
        dbOrg.frontEndConf.betaIntegrationsAllowed
          .map((b: string) => b.toLowerCase())
          .includes(zoomLiveRecorderProviderName.toLowerCase())
      ) {
        betaIntegrationsAllowed.push(hyperboundMeetingRecorderProviderName);
      }
    }

    if (dbOrg.frontEndConf.videoCallEnabled) {
      videoCallEnabled = true;
    }
    if (dbOrg.frontEndConf.hideCoaching) {
      hideCoaching = dbOrg.frontEndConf.hideCoaching;
    }
    if (dbOrg.frontEndConf.useNewSidebar) {
      useNewSidebar = true;
    }
    if (dbOrg.frontEndConf.blurSecondaryPages) {
      blurSecondaryPages = true;
    }
    if (dbOrg.frontEndConf.competitionEndDate) {
      competitionEndDate = dbOrg?.frontEndConf.competitionEndDate;
    }
    if (dbOrg.frontEndConf.hideHomePage) {
      hideHomePage = true;
    }
    if (dbOrg.frontEndConf.useOldScorecardsUI) {
      useNewScorecardsUI = false;
    }
    if (dbOrg.frontEndConf.showLeaderboardInHomePage) {
      showLeaderboardInHomePage = true;
    }

    aiCoachTextOverride = dbOrg.frontEndConf.aiCoachTextOverride || '';
    if (dbOrg.frontEndConf.hideHyperboundLogoInIframe) {
      hideHyperboundLogoInIframe = true;
    }
    if (dbOrg.frontEndConf.poweredByBranding) {
      poweredByBranding = dbOrg.frontEndConf.poweredByBranding;
    }
    if (dbOrg.frontEndConf.hideChatButtonInIframe) {
      hideChatButtonInIframe = dbOrg.frontEndConf.hideChatButtonInIframe;
    }
    if (dbOrg.frontEndConf.showTeamsInLeaderboard) {
      showTeamsInLeaderboard = dbOrg.frontEndConf.showTeamsInLeaderboard;
    }
    if (dbOrg.frontEndConf.showPlanAssessment) {
      showPlanAssessment = dbOrg.frontEndConf.showPlanAssessment;
    }
    if (dbOrg.frontEndConf.allBotsPage_showTagsForBots) {
      allBotsPage_showTagsForBots =
        dbOrg.frontEndConf.allBotsPage_showTagsForBots;
    }
    if (dbOrg.frontEndConf.onlyAdminsCanDeleteUserCalls) {
      onlyAdminsCanDeleteUserCalls =
        dbOrg.frontEndConf.onlyAdminsCanDeleteUserCalls;
    }
    if (dbOrg.frontEndConf.onlyAdminsCanViewMedia) {
      onlyAdminsCanViewMedia = dbOrg.frontEndConf.onlyAdminsCanViewMedia;
    }
    if (dbOrg.frontEndConf.onlyAdminsCanRescoreCalls) {
      canRescoreCalls = canAccess(AppPermissions.RESCORE_CALLS);
    }

    // if only iframes are allowed, and a regular user is trying to access stuff directly
    if (!isInIframe && dbOrg.frontEndConf.onlyAccessFromIframes && !isAdmin) {
      blurSecondaryPages = true;
      blurAiRoleplayPage = true;
      onlyAccessFromIframes = true;
    }

    if (
      dbOrg.frontEndConf.scoreMessages &&
      Object.keys(dbOrg.frontEndConf.scoreMessages)?.length > 0
    ) {
      scoreMessages = dbOrg.frontEndConf.scoreMessages;
    }

    if (dbOrg.settings?.onlyAdminsCanExportCalls) {
      onlyAdminsCanExportCalls = dbOrg.settings.onlyAdminsCanExportCalls;
    }
    if (dbOrg.frontEndConf?.hideSimilarCalls) {
      hideSimilarCalls = true;
    }
    if (dbOrg.frontEndConf?.hideLearningMaterials) {
      hideLearningMaterials = true;
    }
    if (dbOrg.frontEndConf?.canAccessInternalCompetitions) {
      canAccessInternalCompetitions =
        dbOrg.frontEndConf?.canAccessInternalCompetitions;
    }
    if (dbOrg.frontEndConf?.hideCallContext) {
      hideCallContext = true;
    }
    if (dbOrg.frontEndConf?.isCallHistoryLimitedUi) {
      isCallHistoryLimitedUi = true;
    }
    if (dbOrg.frontEndConf?.hiddenRolesAllowed) {
      hiddenRolesAllowed = dbOrg.frontEndConf.hiddenRolesAllowed;
    }

    if (dbOrg.frontEndConf?.useIntegrationsV2) {
      useIntegrationsV2 = dbOrg.frontEndConf.useIntegrationsV2;
    }
    if (dbOrg.frontEndConf?.canAccessScorecardsMaterials) {
      canAccessScorecardsMaterials =
        dbOrg.frontEndConf.canAccessScorecardsMaterials;
    }
    if (dbOrg.frontEndConf?.showCallScoreTitle) {
      showCallScoreTitle = dbOrg.frontEndConf.showCallScoreTitle;
    }
    if (dbOrg.frontEndConf?.isScormEnabled) {
      isScormEnabled = dbOrg.frontEndConf.isScormEnabled;
    }
    if (dbOrg.frontEndConf?.hideAiRoleplayFromMembers) {
      if (
        authInfo.accessHelper?.isRole(org?.orgId as string, RoleEnum.MEMBER) ||
        authInfo.accessHelper?.isRole(
          org?.orgId as string,
          RoleEnum.MEMBER_PLUS,
        )
      ) {
        hideAiRoleplay = true;
      }
    }

    if (dbOrg.frontEndConf?.hideAllBotsFromMembers) {
      if (
        authInfo.accessHelper?.isRole(org?.orgId as string, RoleEnum.MEMBER) ||
        authInfo.accessHelper?.isRole(
          org?.orgId as string,
          RoleEnum.MEMBER_PLUS,
        )
      ) {
        hideAllBots = true;
      }
    }

    if (dbOrg.frontEndConf?.isRepInstructionsMandatory) {
      isRepInstructionsMandatory =
        dbOrg.frontEndConf.isRepInstructionsMandatory;
    }
    if (dbOrg.frontEndConf?.showManagerCallType) {
      showManagerCallType = dbOrg.frontEndConf.showManagerCallType;
    }
    if (dbOrg.frontEndConf?.useLearningModulesV2) {
      useLearningModulesV2 = dbOrg.frontEndConf.useLearningModulesV2;
    }

    if (dbOrg.frontEndConf?.showCopySSOUrlButton) {
      showCopySSOUrlButton = dbOrg.frontEndConf.showCopySSOUrlButton;
    }

    if (dbOrg.frontEndConf?.isManagerHierarchyEnabled) {
      isManagerHierarchyEnabled = dbOrg.frontEndConf.isManagerHierarchyEnabled;
    }
  }

  if (dbOrg?.parentOrganization) {
    const po = dbOrg?.parentOrganization;
    if (po.frontEndConf) {
      if (po.frontEndConf.poweredByBranding) {
        poweredByBranding = po.frontEndConf.poweredByBranding;
      }
    }
  }

  const CALL_SCENARIO_OPTIONS = useMemo(() => {
    const callScenarioOptions = DEFAULT_CALL_SCENARIO_OPTIONS;

    if (dbOrg?.id == 96 || dbOrg?.parentOrganization?.id == 96) {
      //uplift
      let add = true;
      callScenarioOptions[AgentCallType.DISCOVERY].map(
        (cs: { value: string; label: string; description?: string }) => {
          if (cs.value == 'Wants to build their business and needs your help') {
            add = false;
          }
        },
      );
      if (add) {
        callScenarioOptions[AgentCallType.DISCOVERY].push({
          value: 'Wants to build their business and needs your help',
          label: 'Wants to build their business and needs your help',
          description: 'Wants to build their business and needs your help',
        });
      }
    }

    return callScenarioOptions;
  }, [dbOrg]);

  let isHyperboundUser = false;

  if (authInfo.user?.email.toLowerCase().includes('@hyperbound.ai')) {
    isHyperboundUser = true;
  }

  const isMember = authInfo.accessHelper?.isRole(
    org?.orgId as string,
    RoleEnum.MEMBER,
  );

  const sortedOrgs = useMemo(() => {
    const orgsShortlist =
      authInfo?.orgHelper
        ?.getOrgs()
        ?.sort?.((o1, o2) => o1.orgName.localeCompare(o2.orgName)) || [];
    // TODO: remove linkedin specific hack
    const shouldRemoveLinkedIn = isMember;
    return orgsShortlist.filter(
      (o) =>
        !blacklistedOrgIds.includes(o.orgId) &&
        // TODO: remove linkedin specific hack
        (!shouldRemoveLinkedIn || o.orgName !== 'LinkedIn'),
    );
  }, [authInfo?.orgHelper]);

  const betaIntegrationsAllowedSet = new Set(
    betaIntegrationsAllowed.map((i) => i.toLowerCase()),
  );

  return {
    isLoggedIn: authInfo.isLoggedIn,
    isOrgActive,
    userId: dbUser?.id,
    accessToken: authInfo.accessToken,
    loading: authInfo.loading,
    firstName: authInfo.user?.firstName,
    lastName: authInfo.user?.lastName,
    email: authInfo.user?.email,
    avatar: authInfo.user?.pictureUrl,
    isAdmin,
    role,
    isOwner: authInfo.accessHelper?.isRole(
      org?.orgId as string,
      RoleEnum.OWNER,
    ),
    isMember,
    isMemberPlus: authInfo.accessHelper?.isRole(
      org?.orgId as string,
      RoleEnum.MEMBER_PLUS,
    ),
    isTemp: isTemp,
    isCustom: authInfo.accessHelper?.isRole(
      org?.orgId as string,
      RoleEnum.CUSTOM,
    ),
    isObserver: authInfo.accessHelper?.isRole(
      org?.orgId as string,
      RoleEnum.OBSERVER,
    ),
    sortedOrgs,
    org,
    orgId: org?.orgId,
    dbOrg,
    subOrganizations,
    dbUser,
    isDbUserLoading: isDbUserLoading,
    user: authInfo.user,
    redirectToLoginPage,
    redirectToSignupPage,
    redirectToOrgPage,
    redirectToAccountPage,
    canAccess,
    isPilotEnded,
    competitionEndDate,
    onlyAccessFromIframes,
    blurLeaderboard,
    blurAiRoleplayPage,
    isHyperboundUser,
    canAccessIntegrations,
    canAccessRealCallsScoring,
    betaIntegrations: new Set(betaIntegrations.map((i) => i.toLowerCase())),
    betaIntegrationsAllowed: betaIntegrationsAllowedSet,
    isHyperboundMeetingRecorderAllowed: betaIntegrationsAllowedSet.has(
      hyperboundMeetingRecorderProviderName.toLowerCase(),
    ),
    canCreateSubOrgs: !!dbOrg?.canCreateSubOrgs,
    isCompetitionOrg: !!dbOrg?.isCompetitionOrg,
    competitionTag: dbOrg?.competitionTag,
    videoCallEnabled,
    useNewSidebar,
    blurSecondaryPages,
    hideHomePage,
    useNewScorecardsUI,
    aiCoachTextOverride,
    isInIframe,
    hideHyperboundLogoInIframe,
    showLeaderboardInHomePage,
    poweredByBranding,
    hideChatButtonInIframe,
    showTeamsInLeaderboard,
    showPlanAssessment,
    CALL_SCENARIO_OPTIONS,
    useOldSimulationPanel,
    allowAgentPublicToggle,
    allBotsPage_showTagsForBots,
    onlyAdminsCanDeleteUserCalls,
    scoreMessages,
    onlyAdminsCanExportCalls,
    useBotBuilderV2,
    useNewPostCall,
    useMultiParty,
    hideCoaching,
    hideLearningMaterials,
    hideSimilarCalls,
    userOrgs: userOrgsWithIsAdmin,
    loadedAllOrgs,
    canAccessInternalCompetitions,
    canRescoreCalls,
    hideCallContext,
    isCallHistoryLimitedUi: !isAdmin && isCallHistoryLimitedUi,
    canViewMedia: isAdmin || !onlyAdminsCanViewMedia,
    onlyAdminsCanSeeMembersPage,
    onlyAdminsCanFilterCalls,
    onlyAdminsCanCreatePlaylists,
    defaultLeaderboardDateRange,
    hiddenRolesAllowed,
    useIntegrationsV2,
    canAccessScorecardsMaterials,
    showCallScoreTitle,
    hideAiRoleplay,
    hideAllBots,
    isScormEnabled,
    isRepInstructionsMandatory,
    showManagerCallType,
    useLearningModulesV2,
    showCopySSOUrlButton,
    isManagerHierarchyEnabled,
  };
}
