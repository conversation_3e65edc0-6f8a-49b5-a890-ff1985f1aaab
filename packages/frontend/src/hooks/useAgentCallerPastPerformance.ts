import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useAgentCallerPastPerformance(
  agentId: number,
  callerId: number,
  isDemo = false,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      isDemo ? 'demoAgentCallerPastPerformance' : 'agentCallerPastPerformance',
      isDemo,
      agentId,
      callerId,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      isDemo
        ? CallService.getDemoAgentPastPerformanceForCaller(agentId, callerId)
        : CallService.getAgentPastPerformanceForCaller(agentId, callerId),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
