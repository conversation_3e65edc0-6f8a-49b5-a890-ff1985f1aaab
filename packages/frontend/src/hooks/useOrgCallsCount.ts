import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { FilterType } from '../common/Calls/AIRoleplay/List/common';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useOrgCallsCount(filterBy: Record<FilterType, any>) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['orgCallsCount', filterBy, ...commonQueryKeys],
    queryFn: () => CallService.getCountCalls(filterBy),
    enabled: isLoggedIn,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
