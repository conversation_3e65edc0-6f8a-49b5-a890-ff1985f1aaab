import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useEffect, useMemo, useRef, useState } from 'react';

export interface UrlParam {
  name: string;
  value: string;
}

export default function useRouting(onUrlChange?: () => void) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [curSearchParams, setCurSearchParams] = useState<URLSearchParams>(
    new URLSearchParams(searchParams.toString()),
  );

  useEffect(() => {
    setCurSearchParams(new URLSearchParams(searchParams.toString()));
    if (onUrlChange) {
      onUrlChange();
    }
  }, [searchParams]);

  const setUrlParameter = useMemo(
    () => (name: string, value: string) => {
      curSearchParams.set(name, value);
      setCurSearchParams(curSearchParams);

      router.push(`${pathname}?${curSearchParams.toString()}`);
    },
    [searchParams, router],
  );

  const setMultipleUrlParameters = useMemo(
    () => (params: Record<string, string | null | undefined>) => {
      const newSearchParams = new URLSearchParams(searchParams.toString());

      Object.entries(params).forEach(([key, value]) => {
        if (value === null || value === undefined) {
          newSearchParams.delete(key);
        } else {
          newSearchParams.set(key, value);
        }
      });

      router.push(`${pathname}?${newSearchParams.toString()}`);
    },
    [searchParams, router, pathname],
  );

  const deleteUrlParameter = useMemo(
    () => (name: string) => {
      curSearchParams.delete(name);
      setCurSearchParams(curSearchParams);
      router.replace(`${pathname}?${curSearchParams.toString()}`);
    },
    [searchParams, router],
  );

  const getUrlParameter = useMemo(
    () =>
      (name: string): string | null => {
        return curSearchParams.get(name);
      },
    [searchParams],
  );

  const goToPage = useMemo(
    () => (url: string) => {
      router.push(url);
    },
    [router],
  );

  const appendToUrl = (s: string) => {
    router.replace(`${pathname}/${s}`);
  };

  const appendToUrlAndSaveToClipboard = (params: UrlParam[]) => {
    for (const p of params) {
      curSearchParams.set(p.name, p.value);
    }
    const newUrl = `${window.location.origin}${pathname}?${curSearchParams.toString()}`;

    navigator.clipboard.writeText(newUrl);
  };

  return {
    setUrlParameter,
    setMultipleUrlParameters,
    getUrlParameter,
    goToPage,
    deleteUrlParameter,
    appendToUrl,
    appendToUrlAndSaveToClipboard,
  };
}
