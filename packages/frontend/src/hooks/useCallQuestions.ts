import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useCallQuestions(
  vapiId: string,
  isDemo = false,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: [
      isDemo ? 'demoCallQuestions' : 'callQuestions',
      isDemo,
      vapiId,
      hbDemoInboundForm?.id,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      isDemo
        ? CallService.getDemoCallQuestions(
            vapiId,
            hbDemoInboundForm?.id as number,
          )
        : CallService.getCallQuestions(vapiId),
    enabled: enabled && !!vapiId && (isDemo ? !!hbDemoInboundForm?.id : true),
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
