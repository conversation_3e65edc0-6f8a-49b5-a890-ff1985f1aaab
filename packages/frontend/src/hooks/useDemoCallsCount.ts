import CallService from '@/lib/Call';
import { useQuery } from '@tanstack/react-query';
import { FilterType } from '../common/Calls/AIRoleplay/List/common';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useDemoCallsCount(filterBy: Record<FilterType, any>) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();

  const {
    data: hbDemoInboundForm,
    isLoading,
    isFetched,
  } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: [
      'demoCallsCount',
      hbDemoInboundForm?.id,
      filterBy,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      CallService.getCountDemoCalls(hbDemoInboundForm?.id as number, filterBy),
    enabled: !isLoggedIn && !!hbDemoInboundForm?.id,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
