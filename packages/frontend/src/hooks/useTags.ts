import { TagsService } from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useTags(
  enabled = true,
  from: number = 0,
  numberOfResults: number = 10,
  search: string = '',
  orderBy: string = 'name',
  order: string = 'asc',
) {
  const commonQueryKeys = useCommonQueryKeys();
  const query = useQuery({
    queryKey: [
      'agentsTags',
      from,
      numberOfResults,
      search,
      orderBy,
      order,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      TagsService.getAll(from, numberOfResults, search, orderBy, order),
    enabled: enabled,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
