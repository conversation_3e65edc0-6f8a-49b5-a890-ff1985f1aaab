import ScorecardConfigService from '@/lib/ScorecardConfig';
import { useQuery } from '@tanstack/react-query';
import useUserSession from './useUserSession';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { ScorecardConfigAgentsQuery } from '@/lib/ScorecardConfig/types';

export default function useScorecardConfigAgents(
  scorecardConfigId: number | undefined,
  enabled = true,
  params?: ScorecardConfigAgentsQuery,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const { isLoggedIn } = useUserSession();

  const query = useQuery({
    queryKey: [
      'scorecardConfigAgentsById',
      scorecardConfigId,
      params?.page,
      params?.pageSize,
      params?.search,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      !scorecardConfigId
        ? {
            agents: [],
            page: params?.page || 1,
            pageSize: params?.pageSize || 10,
            totalCount: 0,
          }
        : ScorecardConfigService.getScorecardConfigAgents(
            scorecardConfigId,
            params,
          ),
    enabled: enabled && isLoggedIn,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
