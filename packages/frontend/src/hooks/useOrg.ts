import OrganizationService from '@/lib/Organization';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useOrg(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['org', ...commonQueryKeys],
    queryFn: () => OrganizationService.getOrganization(),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}

export function useOrgAccountsUsedPerRole(enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const isLoggedIn = useIsLoggedIn();

  const query = useQuery({
    queryKey: ['get-org-accounts-used-per-role', ...commonQueryKeys],
    queryFn: () => OrganizationService.getAccountsUsedPerRole(),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retryDelay: 2000,
    retry: 3,
  });

  return query;
}
