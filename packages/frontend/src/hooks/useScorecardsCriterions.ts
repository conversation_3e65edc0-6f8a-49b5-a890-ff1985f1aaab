import ScorecardConfigService from '@/lib/ScorecardConfig';
import { useQuery } from '@tanstack/react-query';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useScorecardsCriterions(
  enabled = true,
  sections: string[] = [],
  from = 0,
  numberOfResults = 10,
  search = '',
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: [
      'scorecardConfigCriterions',
      sections,
      from,
      numberOfResults,
      search,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      ScorecardConfigService.getCriterions(
        sections,
        from,
        numberOfResults,
        search,
      ),
    enabled: enabled,
    staleTime: 0,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
