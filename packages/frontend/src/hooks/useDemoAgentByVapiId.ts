import AgentService from '@/lib/Agent';
import { useQuery } from '@tanstack/react-query';
import { useParams, usePathname, useSearchParams } from 'next/navigation';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useDemoAgentByVapiId(
  enabled = true,
  agentIdOverride = '',
) {
  const commonQueryKeys = useCommonQueryKeys();
  const params = useParams();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const {
    data: hbDemoInboundForm,
    isLoading,
    isFetched,
  } = useHbDemoInboundForm();

  let agentId = agentIdOverride || params.id;

  if (searchParams.get('id')) {
    agentId = searchParams.get('id') as string;
  }

  const query = useQuery({
    queryKey: [
      'demoAgentByVapiId',
      agentId,
      hbDemoInboundForm?.id,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      AgentService.getDemoAgentByVapiId(
        agentId as string,
        hbDemoInboundForm?.id,
      ),
    enabled: enabled && !!agentId,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
