import { useQuery } from '@tanstack/react-query';
import UserService from '@/lib/User';
import { useCommonQueryKeys } from './useCommonQueryKeys';

export default function useGenBotDrafts(
  from: number,
  numberOfResults: number,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const query = useQuery({
    queryKey: ['GenBotDrafts', from, numberOfResults, ...commonQueryKeys],
    queryFn: () => UserService.getGenerateAgentDrafts(from, numberOfResults),
    enabled: enabled,
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retryDelay: 3000,
    retry: 5,
  });

  return query;
}
