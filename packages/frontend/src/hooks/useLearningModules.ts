import LearningModuleService from '@/lib/LearningModule';
import { useQuery } from '@tanstack/react-query';
import useUserSession from './useUserSession';
import {
  CompetitionInfoForAgentPanel,
  LearningModuleStatus,
} from '@/lib/LearningModule/types';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { AppPermissions } from '@/lib/permissions';

export default function useLearningModules(
  assignedTo: number[] = [],
  status?: LearningModuleStatus,
  search: string = '',
  orderByDate: string = 'asc',
  archived = false,
  start = 0,
  numberOfResults = 100,
  isCompetition = false,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();

  const { isLoggedIn, canAccess } = useUserSession();

  const query = useQuery({
    queryKey: [
      'learning-modules',
      assignedTo,
      status,
      search,
      orderByDate,
      archived,
      start,
      numberOfResults,
      isCompetition,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      LearningModuleService.getAll(
        canAccess(AppPermissions.ADMIN_LEARNING_MODULES) || false,
        assignedTo,
        status,
        search,
        orderByDate,
        archived,
        start,
        numberOfResults,
        isCompetition,
      ),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}

export function useLearningModule(
  id: string,
  isAdmin: boolean,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const { isLoggedIn } = useUserSession();

  const query = useQuery({
    queryKey: ['learning-module', id, isAdmin, ...commonQueryKeys],
    queryFn: () => LearningModuleService.get(id, isAdmin),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}

export function useCompetitionStats(id: string, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const { isLoggedIn } = useUserSession();

  const query = useQuery({
    queryKey: ['competition-stats', id, ...commonQueryKeys],
    queryFn: () => LearningModuleService.getCompetitionStats(id),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}

export function useLearningModuleStats(id: string, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const { isLoggedIn } = useUserSession();

  const query = useQuery({
    queryKey: ['learning-module-stats', id, ...commonQueryKeys],
    queryFn: () => LearningModuleService.getStats(id),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}

export function useCompetitionInfoForAgentPanel(
  agentId: number,
  enabled = true,
) {
  const commonQueryKeys = useCommonQueryKeys();
  const { isLoggedIn } = useUserSession();

  const query = useQuery<CompetitionInfoForAgentPanel[]>({
    queryKey: ['competition-info-for-agent-pnl', agentId, ...commonQueryKeys],
    queryFn: () =>
      LearningModuleService.getCompetitionInfoForAgentPanel(agentId),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}

export function useCompetitionInfoForCall(vapiCallId: string, enabled = true) {
  const commonQueryKeys = useCommonQueryKeys();
  const { isLoggedIn } = useUserSession();

  const query = useQuery({
    queryKey: ['competition-info-for-call', vapiCallId, ...commonQueryKeys],
    queryFn: () => LearningModuleService.getCompetitionsInfoForCall(vapiCallId),
    enabled: enabled && isLoggedIn,
    staleTime: 10000,
    refetchOnWindowFocus: true,
    retry: 5,
  });

  return query;
}
