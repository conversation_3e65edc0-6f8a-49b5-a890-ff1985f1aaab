import LeaderboardService from '@/lib/Leaderboard';
import { useQuery } from '@tanstack/react-query';
import useHbDemoInboundForm from './useHbDemoInboundForm';
import { useCommonQueryKeys } from './useCommonQueryKeys';
import { useIsLoggedIn } from './useIsLoggedIn';

export default function useLeaderboardByDemoInboundFormResponseId(
  demoInboundFormResponseId: number,
  agentId: number,
  enabled: boolean = true,
) {
  const isLoggedIn = useIsLoggedIn();
  const commonQueryKeys = useCommonQueryKeys();
  const { data: hbDemoInboundForm } = useHbDemoInboundForm();

  const query = useQuery({
    queryKey: [
      'leaderboardItems',
      demoInboundFormResponseId,
      agentId,
      ...commonQueryKeys,
    ],
    queryFn: () =>
      LeaderboardService.getLeaderboardItemsByDemoInboundFormResponseId(
        demoInboundFormResponseId,
        agentId,
      ),
    enabled:
      enabled &&
      !!demoInboundFormResponseId &&
      (!isLoggedIn ||
        (isLoggedIn && hbDemoInboundForm?.presClubChallengeRegistered)),
    staleTime: 2000,
    refetchOnWindowFocus: false,
    retry: 2,
  });

  return query;
}
