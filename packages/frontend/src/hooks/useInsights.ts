import {
  AnalyticsFilterState,
} from '@/lib/Analytics/types';
import { useState, useEffect } from 'react';
import { SectionType } from '@/lib/Analytics/types';
import useInsightsData from './useInsightsData';

export default function useInsights(
  sectionType: SectionType,
  filters: AnalyticsFilterState,
  enabledDataLoading = true,
) {
  const [filterState, setFilterState] =
    useState<AnalyticsFilterState>(filters);

  const { data, isLoading, refetch } = useInsightsData(
    sectionType,
    filterState,
    enabledDataLoading,
  );
  const [currentData, setCurrentData] = useState<any>(data);

  useEffect(() => {
    if (!isLoading && (data || data == 0)) {
      setCurrentData(data);
    }
  }, [isLoading, data]);

  useEffect(() => {
    if (!isLoading) {
      refetch();
    }
  }, [filterState]);

  useEffect(() => {
    if (filters) {
      setFilterState(filters);
    }
  }, [filters]);

  const updateFiltersState = (filters: AnalyticsFilterState) => {
    setFilterState(filters);
  };

  return { filterState, updateFiltersState, isLoading, data: currentData };
}
