declare global {
  interface Window {
    analytics: any;
  }
}

export default class Analytics {
  /**
   * Tracks the specified Segment event with optional properties
   *
   * @param event {string} Analytics event name
   * @param props {Record<string, any>} Optional --> Properties to track with
   */
  static track(event: string, props?: Record<string, any>) {
    if (process.env.NODE_ENV === 'development')
      return console.log('TRACK::', event);
    // @ts-ignore
    global.analytics.track(event, { ...(props || {}) });
  }

  /**
   * Identifies the specified userId with optional traits
   *
   * @param userId {string} User id to to identify, will automatically pad front with 0s to make sure id is max length
   * @param traits {Record<string, any>} Optional --> Properties to identify by
   */
  static identify(userId: string, traits?: Record<string, any>) {
    if (process.env.NODE_ENV === 'development') return;
    // @ts-ignore
    global.analytics.identify(userId, {
      ...(traits || {}),
    });
  }
}
