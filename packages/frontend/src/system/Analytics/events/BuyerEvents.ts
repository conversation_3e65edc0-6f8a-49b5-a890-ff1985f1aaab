export enum BuyerEvents {
  SELECTED_FROM_BUYERS = 'BUYER:selected_from_buyers',
  SEARCHED_BUYERS = 'BUYER:searched_buyers',
  START_CALL_CLICKED = 'BUYER:start_call_clicked',
  START_CALL_CLICKED_BLACKLISTED = 'BUYER:start_call_clicked_blacklisted',
  START_CALL_WARNING_MODAL = 'BUYER:start_call_warning_modal',
  VIEW_CALLS_CLICKED = 'BUYER:view_calls_clicked',
  VIEW_LEADERBOARD_CLICKED = 'BUYER:view_leaderboard_clicked',
  CUSTOMIZE_BUYER_BOT_HOVERED = 'BUYER:customize_buyer_bot_hovered',
  CALL_STARTED = 'BUYER:call_started',
  CALL_STARTED_VAPI_ERROR = 'BUYER:call_started_vapi_error',
  CALL_INITIALIZED_SUCCESS = 'BUYER:call_initialized_success',
  CALL_INITIALIZED_ERROR = 'BUYER:call_initialized_error',
  CALL_STOPPED = 'BUYER:call_stopped',
}
