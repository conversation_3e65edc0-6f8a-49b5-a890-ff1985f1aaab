export enum CallEvents {
  TAB_CLICKED = 'CALL:tab_clicked',
  SUMMARY_SHOW_MORE_CLICKED = 'CALL:summary_show_more_clicked',
  RECORDING_CLICKED = 'CALL:recording_clicked',
  TRANSCRIPT_MESSAGE_CLICKED = 'CALL:transcript_message_clicked',
  STAT_CARD_INFO_HOVERED = 'CALL:stat_card_info_hovered',
  REP_BADGE_CLICKED = 'CALL:rep_badge_clicked',
  START_NEW_CALL_CLICKED = 'CALL:start_new_call_clicked',
  VIEW_TEAM_CALLS_CLICKED = 'CALL:view_team_calls_clicked',
  RECORDING_DOWNLOAD_HOVERED = 'CALL:recording_download_hovered',
  RECORDING_DOWNLOADED = 'CALL:recording_downloaded',
  TRANSCRIPT_DOWNLOAD_HOVERED = 'CALL:transcript_download_hovered',
  TRANSCRIPT_DOWNLOADED = 'CALL:transcript_downloaded',
  TRANSCRIPT_COPY_HOVERED = 'CALL:transcript_copy_hovered',
  TRANSCRIPT_COPY_CLICKED = 'CALL:transcript_copy_clicked',

  SELECTED_FROM_CALLS = 'CALL:selected_from_calls',
  COPY_URL_CLICKED = 'CALL:copy_url_clicked',
  SUMMARY_COPY_CLICKED = 'CALL:summary_copy_clicked',

  DELETE_CLICKED = 'CALL:delete_clicked',
  DELETE_SUCCESS = 'CALL:delete_success',
  DELETE_ERROR = 'CALL:delete_error',

  BULK_DELETE_CLICKED = 'CALL:bulk_delete_clicked',
  BULK_DELETE_SUCCESS = 'CALL:bulk_delete_success',
  BULK_DELETE_ERROR = 'CALL:bulk_delete_error',
}
