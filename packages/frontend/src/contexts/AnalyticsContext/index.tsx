import { createContext } from 'react';
import { DateRange } from 'react-day-picker';

export interface AnalyticsFilters {
  dateRange: DateRange; // dateRange
  buyerIdsBlacklist: number[]; // list of buyerIds to NOT include in the analytics,
  userIdsBlacklist: number[]; // list of userIds to NOT include in the analytics,
  playlistIdsBlacklist: number[]; // list of playlistIds to NOT include in the analytics,
}

export const AnalyticsContext = createContext<{
  filters: AnalyticsFilters;
  setDateRange: (dateRange: DateRange) => void;
  setBuyerIdsBlacklist: (buyerIdsBlacklist: number[]) => void;
  setUserIdsBlacklist: (userIdsBlacklist: number[]) => void;
  setPlaylistIdsBlacklist: (playlistIdsBlacklist: number[]) => void;
  repId?: number; // If on the rep/:id page, this will be the repId
}>({
  filters: {
    dateRange: {
      from: new Date(),
      to: new Date(),
    },
    buyerIdsBlacklist: [],
    userIdsBlacklist: [],
    playlistIdsBlacklist: [],
  },
  setDateRange: () => {},
  setBuyerIdsBlacklist: () => {},
  setUserIdsBlacklist: () => {},
  setPlaylistIdsBlacklist: () => {},
  repId: undefined,
});

export const AnalyticsContextProvider = AnalyticsContext.Provider;
