'use client';
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  PropsWithChildren,
} from 'react';

const flags = {
  dev: false,
};

export const FeatureFlagsContext = createContext({
  flags,
  toggleFeatureFlag: (flag: keyof typeof flags) => {},
});

// FeatureFlagsProvider component that will wrap your app
export const FeatureFlagsProvider = ({ children }: PropsWithChildren) => {
  // Example: Default feature flags (can be fetched from an API or local storage)
  const [featureFlags, setFeatureFlags] = useState({
    dev: process.env.NODE_ENV === 'development',
  });

  // Optionally, fetch feature flags from an API or some external source
  // useEffect(() => {
  //   const fetchFeatureFlags = async () => {
  //     // Replace with actual API call
  //     const fetchedFlags = await fetch('/api/feature-flags')
  //       .then((res) => res.json())
  //       .catch(() => ({})); // Fallback to empty object if fetch fails

  //     setFeatureFlags(fetchedFlags);
  //   };

  //   fetchFeatureFlags();
  // }, []);

  // Function to toggle a specific feature flag
  const toggleFeatureFlag = (flag: keyof typeof flags) => {
    setFeatureFlags((prevFlags) => ({
      ...prevFlags,
      [flag]: !prevFlags[flag],
    }));
  };

  return (
    <FeatureFlagsContext.Provider
      value={{ flags: featureFlags, toggleFeatureFlag }}
    >
      {children}
    </FeatureFlagsContext.Provider>
  );
};
