import {
  PropsWithChildren,
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useForm } from 'react-hook-form';
import { useParams, useSearchParams } from 'next/navigation';
import {
  AgentCallType,
  AgentFolderDto,
  CreateBuyerBotEditFormValues,
  StepOneData,
} from '@/lib/Agent/types';
import { CreateBuyerBotContext } from '.';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  formSchema,
  AdvancedSettingsFormSchema,
  BasicDetailsFormSchema,
  CompanyDetailsFormSchema,
  OpinionsFormSchema,
  PersonalDetailsFormSchema,
  PrioritiesAndObjectionsFormSchema,
  ScorecardFormSchema,
  getZodSchemaFieldsShallow,
  ConfigDetailsFormSchema,
  getCurrentMessages,
  PersonalDetailsFocusFormSchema,
  focusFormSchema,
  ScenarioNameFormSchema,
  multipartyFormSchema,
} from '@/common/CreateBuyerForm/Main/utils';

import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import { useAgent } from '@/hooks/useAgent';
import _ from 'lodash';
import { useFilteredScorecardConfigOptions } from '@/hooks/useFilteredScorecardConfigOptions';
import { ZodType } from 'zod';
import usePreventReload from '@/hooks/usePreventReload';
import { ParsedTranscriptMessage } from '@/lib/Ai/types';
const withFolders = true;

export const CreateBuyerBotEditFormContext =
  createContext<CreateBuyerBotEditFormValues>(
    {} as CreateBuyerBotEditFormValues,
  );
interface Iprops extends PropsWithChildren {
  baseRoute: string;
}

export default function CreateBuyerBotEditFormProvider({
  baseRoute,
  ...props
}: Iprops) {
  const {
    forms,
    defaultValues: inheritedValues,
    isEditMode,
    supportingAgentInfo,
    setSupportingAgentInfo,
  } = useContext(CreateBuyerBotContext);

  const [
    createBuyerBotEditFormSubmitButtonState,
    setCreateBuyerBotEditFormSubmitButtonState,
  ] = useState({
    disabled: false,
    loading: false,
  });

  const params = useParams();
  const searchParams = useSearchParams();
  const callType = searchParams.get('callType') as AgentCallType;
  const cloneBuyerId = searchParams?.get('cloneBuyerId');
  const queryString = useMemo(
    () => (searchParams.size > 0 ? `?${searchParams.toString()}` : ''),
    [searchParams],
  );

  const { data: existingAgent } = useAgent(
    params?.id as string,
    isEditMode,
    withFolders,
  );

  const { data: scorecardConfigOptions } = useScorecardConfigsForOrg();

  const [resumeCalls, setResumeCalls] = useState<string[]>(
    getCurrentMessages(existingAgent),
  );

  const [isCreatingScorecard, setIsCreatingScorecard] = useState(false);
  const [isEditingScorecard, setIsEditingScorecard] = useState(false);

  const [folderForContent, setFolderForContent] = useState<AgentFolderDto[]>(
    [],
  );

  const [currentMessages, setCurrentMessages] = useState<
    ParsedTranscriptMessage[]
  >([]);

  const [currentTab, setCurrentTab] = useState<'individual' | 'multi-party'>(
    () => {
      // Lazy initialization
      if (isEditMode) {
        return existingAgent?.supportingAgentInfo &&
          existingAgent?.supportingAgentInfo?.length > 0
          ? 'multi-party'
          : 'individual';
      } else {
        return (
          (searchParams.get('tab') as 'individual' | 'multi-party') ||
          'individual'
        );
      }
    },
  );

  useEffect(() => {
    if (
      isEditMode &&
      existingAgent?.supportingAgentInfo?.length &&
      existingAgent?.supportingAgentInfo?.length > 0
    ) {
      setCurrentTab('multi-party');
      setSupportingAgentInfo &&
        setSupportingAgentInfo(existingAgent?.supportingAgentInfo);
    }
  }, [isEditMode, existingAgent]);

  useEffect(() => {
    return () => {
      if (setSupportingAgentInfo) {
        // Reset on unmounting so that cache does not affect the next time user makes a new multi party bot
        setSupportingAgentInfo([]);
      }
    };
  }, []);

  const defaultValues = useMemo(
    () => forms?.main?.getValues() || inheritedValues?.main,
    [forms.main, inheritedValues.main],
  );

  const form = useForm<StepOneData>({
    mode: 'onTouched',
    resolver: zodResolver(
      callType === AgentCallType.FOCUS
        ? focusFormSchema
        : currentTab === 'multi-party'
          ? multipartyFormSchema
          : formSchema,
    ),
    defaultValues,
  });

  const { setIsDirty } = usePreventReload();

  const isSubFieldValid = (schema: ZodType) => {
    const result = schema.safeParse(
      _.pick(
        form?.getValues() || inheritedValues.main,
        getZodSchemaFieldsShallow(schema),
      ),
    ).success;

    return result;
  };

  const isConfigDetailsValid = isSubFieldValid(ConfigDetailsFormSchema);
  const isBasicDetailsValid = isSubFieldValid(BasicDetailsFormSchema);
  const isPersonalDetailsValid =
    callType === AgentCallType.FOCUS
      ? isSubFieldValid(PersonalDetailsFocusFormSchema)
      : isSubFieldValid(PersonalDetailsFormSchema);
  const isCompanyDetailsValid =
    callType === AgentCallType.FOCUS
      ? true
      : isSubFieldValid(CompanyDetailsFormSchema);
  const isPrioritiesAndObjectionsValid =
    callType === AgentCallType.FOCUS
      ? true
      : isSubFieldValid(PrioritiesAndObjectionsFormSchema);
  const isOpinionsValid =
    callType === AgentCallType.FOCUS
      ? true
      : isSubFieldValid(OpinionsFormSchema);
  const isScorecardValid = isSubFieldValid(ScorecardFormSchema);
  const isAdvancedSettingsValid = isSubFieldValid(AdvancedSettingsFormSchema);
  const isScenarioNameValid = isSubFieldValid(ScenarioNameFormSchema);
  useEffect(() => {
    // This will explicitly set the form values to `defaultValues` after initialization
    form.reset(defaultValues);
  }, [defaultValues]);
  useEffect(() => {
    if (inheritedValues.main) {
      form.reset(inheritedValues.main); // Reset form values to the updated inherited values
    }
    const messages = getCurrentMessages(existingAgent);
    setResumeCalls(messages);
  }, [existingAgent]);

  useEffect(() => {
    setIsDirty(form?.formState?.isDirty);
  }, [form?.formState?.isDirty]);

  const filteredScorecardConfigOptions = useFilteredScorecardConfigOptions({
    scorecardConfigOptions,
    callType,
    form,
  });
  return (
    <CreateBuyerBotEditFormContext.Provider
      value={{
        form,
        isEditMode,
        isConfigDetailsValid,
        isBasicDetailsValid,
        isPersonalDetailsValid,
        isCompanyDetailsValid,
        isPrioritiesAndObjectionsValid,
        isOpinionsValid,
        isScorecardValid,
        isAdvancedSettingsValid,
        isScenarioNameValid,
        filteredScorecardConfigOptions,
        callType,
        cloneBuyerId,
        queryString,
        baseRoute,
        existingAgent,
        resumeCalls,
        currentMessages,
        setCurrentMessages,
        createBuyerBotEditFormSubmitButtonState,
        setCreateBuyerBotEditFormSubmitButtonState,
        isSubFieldValid,
        setResumeCalls,
        isCreatingScorecard,
        setIsCreatingScorecard,
        isEditingScorecard,
        setIsEditingScorecard,
        currentTab,
        setCurrentTab,
        supportingAgentInfo,
        setSupportingAgentInfo,
        folderForContent,
        setFolderForContent,
      }}
      {...props}
    />
  );
}
