import {
  PropsWithChildren,
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useForm } from 'react-hook-form';
import { useParams, useSearchParams } from 'next/navigation';
import {
  AgentCallType,
  CreateBuyerBotEditFormValues,
  StepOneData,
} from '@/lib/Agent/types';
import { CreateBuyerBotContext } from '.';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  formSchema,
  AdvancedSettingsFormSchema,
  BasicDetailsFormSchema,
  CompanyDetailsFormSchema,
  OpinionsFormSchema,
  PersonalDetailsFormSchema,
  PrioritiesAndObjectionsFormSchema,
  ScorecardFormSchema,
  getZodSchemaFieldsShallow,
  ConfigDetailsFormSchema,
  getCurrentMessages,
} from '@/common/CreateBuyerForm/Main/utils';

import useScorecardConfigsForOrg from '@/hooks/useScorecardConfigsForOrg';
import { useAgent } from '@/hooks/useAgent';
import _ from 'lodash';
import { useFilteredScorecardConfigOptions } from '@/hooks/useFilteredScorecardConfigOptions';
import { ZodType } from 'zod';
import usePreventReload from '@/hooks/usePreventReload';
const withFolders = true;

export const CreateBuyerBotEditFormContext =
  createContext<CreateBuyerBotEditFormValues>(
    {} as CreateBuyerBotEditFormValues,
  );
interface Iprops extends PropsWithChildren {
  baseRoute: string;
}

export default function CreateBuyerBotEditFormProvider({
  baseRoute,
  ...props
}: Iprops) {
  const {
    forms,
    defaultValues: inheritedValues,
    isEditMode,
  } = useContext(CreateBuyerBotContext);

  const [
    createBuyerBotEditFormSubmitButtonState,
    setCreateBuyerBotEditFormSubmitButtonState,
  ] = useState({
    disabled: false,
    loading: false,
  });

  const params = useParams();
  const searchParams = useSearchParams();
  const callType = searchParams.get('callType') as AgentCallType;
  const cloneBuyerId = searchParams?.get('cloneBuyerId');
  const queryString = useMemo(
    () => (searchParams.size > 0 ? `?${searchParams.toString()}` : ''),
    [searchParams],
  );

  const { data: existingAgent } = useAgent(
    params?.id as string,
    isEditMode,
    withFolders,
  );

  const { data: scorecardConfigOptions } = useScorecardConfigsForOrg();

  const [resumeCalls, setResumeCalls] = useState<string[]>(
    getCurrentMessages(existingAgent),
  );

  const [currentMessages, setCurrentMessages] = useState<
    ParsedTranscriptMessage[]
  >([]);

  const defaultValues = useMemo(
    () => forms?.main?.getValues() || inheritedValues?.main,
    [forms.main, inheritedValues.main],
  );

  const form = useForm<StepOneData>({
    mode: 'onTouched',
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const { setIsDirty } = usePreventReload();

  const isSubFieldValid = (schema: ZodType) => {
    const result = schema.safeParse(
      _.pick(
        form?.getValues() || inheritedValues.main,
        getZodSchemaFieldsShallow(schema),
      ),
    ).success;

    return result;
  };

  const isConfigDetailsValid = isSubFieldValid(ConfigDetailsFormSchema);
  const isBasicDetailsValid = isSubFieldValid(BasicDetailsFormSchema);
  const isPersonalDetailsValid = isSubFieldValid(PersonalDetailsFormSchema);
  const isCompanyDetailsValid = isSubFieldValid(CompanyDetailsFormSchema);
  const isPrioritiesAndObjectionsValid = isSubFieldValid(
    PrioritiesAndObjectionsFormSchema,
  );
  const isOpinionsValid = isSubFieldValid(OpinionsFormSchema);
  const isScorecardValid = isSubFieldValid(ScorecardFormSchema);
  const isAdvancedSettingsValid = isSubFieldValid(AdvancedSettingsFormSchema);

  useEffect(() => {
    // This will explicitly set the form values to `defaultValues` after initialization
    form.reset(defaultValues);
  }, [defaultValues]);

  useEffect(() => {
    if (inheritedValues.main) {
      form.reset(inheritedValues.main); // Reset form values to the updated inherited values
    }
    const messages = getCurrentMessages(existingAgent);
    setResumeCalls(messages);
  }, [existingAgent]);

  useEffect(() => {
    setIsDirty(form?.formState?.isDirty);
  }, [form?.formState?.isDirty]);

  const filteredScorecardConfigOptions = useFilteredScorecardConfigOptions({
    scorecardConfigOptions,
    callType,
    form,
  });

  return (
    <CreateBuyerBotEditFormContext.Provider
      value={{
        form,
        isConfigDetailsValid,
        isBasicDetailsValid,
        isPersonalDetailsValid,
        isCompanyDetailsValid,
        isPrioritiesAndObjectionsValid,
        isOpinionsValid,
        isScorecardValid,
        isAdvancedSettingsValid,
        filteredScorecardConfigOptions,
        callType,
        cloneBuyerId,
        queryString,
        baseRoute,
        existingAgent,
        resumeCalls,
        currentMessages,
        setCurrentMessages,
        createBuyerBotEditFormSubmitButtonState,
        setCreateBuyerBotEditFormSubmitButtonState,
        isSubFieldValid,
        setResumeCalls,
      }}
      {...props}
    />
  );
}
