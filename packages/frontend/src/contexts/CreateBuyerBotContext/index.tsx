import {
  AgentDto,
  CreateBuyerDefaultFormValues,
  CreateBuyerFormState,
} from '@/lib/Agent/types';
import { Dispatch, SetStateAction, createContext } from 'react';

export const CreateBuyerBotContext = createContext<{
  defaultValues: CreateBuyerDefaultFormValues;
  isEditMode?: boolean;
  forms: CreateBuyerFormState;
  setForms: Dispatch<SetStateAction<CreateBuyerFormState>>;
  baseRoute: string;
  supportingAgentInfo?: AgentDto[];
  setSupportingAgentInfo?: Dispatch<SetStateAction<AgentDto[]>>;
}>({
  defaultValues: {},
  forms: {},
  setForms: () => {},
  isEditMode: false,
  baseRoute: '',
  supportingAgentInfo: [],
  setSupportingAgentInfo: () => {},
});

export const CreateBuyerBotProvider = CreateBuyerBotContext.Provider;
