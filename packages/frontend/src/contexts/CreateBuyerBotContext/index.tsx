import {
  CreateBuyerDefaultFormValues,
  CreateBuyerFormState,
} from '@/lib/Agent/types';
import { Dispatch, SetStateAction, createContext } from 'react';

export const CreateBuyerBotContext = createContext<{
  defaultValues: CreateBuyerDefaultFormValues;
  isEditMode?: boolean;
  forms: CreateBuyerFormState;
  setForms: Dispatch<SetStateAction<CreateBuyerFormState>>;
  baseRoute: string;
}>({
  defaultValues: {},
  forms: {},
  setForms: () => {},
  isEditMode: false,
  baseRoute: '',
});

export const CreateBuyerBotProvider = CreateBuyerBotContext.Provider;
