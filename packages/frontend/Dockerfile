# Stage 1: Build environment
FROM node:22.9.0-alpine AS build

ARG APP_ENV
ARG INFISICAL_CLIENT_ID
ARG INFISICAL_CLIENT_SECRET

# Set the working directory
WORKDIR /frontend

# Install necessary dependencies for scripts
RUN apk add --no-cache bash curl

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Install dependencies with cleanup to reduce image size
RUN yarn install && yarn cache clean

# Copy the rest of the application code
COPY next.config.js tailwind.config.js postcss.config.js tsconfig.json ./
COPY src/ ./src
COPY public/ ./public
COPY components.json ./components.json
COPY scripts/ ./scripts/

# Make scripts executable in the build stage
RUN chmod +x ./scripts/infisical-install.sh ./scripts/infisical-universal-login.sh ./scripts/inject-secrets.sh ./scripts/run-with-secrets.sh

# Run with secrets
RUN sh ./scripts/run-with-secrets.sh $APP_ENV "yarn build"

# Stage 2: Production image - much smaller than build image
FROM node:22.9.0-alpine AS final

ARG APP_ENV
ENV APP_ENV=$APP_ENV
ENV NODE_ENV=production

# Set the working directory for the final image
WORKDIR /frontend

# Install Infisical CLI in the final stage
RUN apk add --no-cache bash curl
COPY --from=build /frontend/scripts/infisical-install.sh ./scripts/
RUN chmod +x ./scripts/infisical-install.sh && sh ./scripts/infisical-install.sh

# Copy only the necessary files from the build stage
COPY --from=build /frontend/package.json /frontend/yarn.lock ./
COPY --from=build /frontend/next.config.js ./
COPY --from=build /frontend/public ./public
COPY --from=build /frontend/.next ./.next
COPY --from=build /frontend/scripts ./scripts

# Install only production dependencies
RUN yarn install --production && yarn cache clean

# Ensure scripts are executable in the final stage
RUN chmod +x ./scripts/infisical-universal-login.sh ./scripts/inject-secrets.sh

# Expose port 3000
EXPOSE 3000

# Start the Next.js app with the correct environment
CMD sh ./scripts/run-with-secrets.sh $APP_ENV "yarn start"
