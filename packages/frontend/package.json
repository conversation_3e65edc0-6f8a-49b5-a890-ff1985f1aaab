{"name": "frontend", "version": "0.1.0", "private": true, "license": "UNLICENSED", "engines": {"node": "22.9.0"}, "scripts": {"inject-secrets:dev": "./scripts/inject-secrets.sh dev ${0}", "inject-secrets:staging": "./scripts/inject-secrets.sh staging ${0}", "inject-secrets:prod": "./scripts/inject-secrets.sh prod ${0}", "dev": "yarn inject-secrets:dev \"next dev\"", "build": "next build", "build:dev": "yarn inject-secrets:dev 'yarn build'", "build:staging": "yarn inject-secrets:staging 'yarn build'", "build:prod": "yarn inject-secrets:prod 'yarn build'", "start": "next start", "start:dev": "yarn inject-secrets:dev 'yarn start'", "start:staging": "yarn inject-secrets:staging 'yarn start'", "start:prod": "yarn inject-secrets:prod 'yarn start'", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "yarn prettier . --write"}, "dependencies": {"@babel/core": "^7.18.5", "@babel/runtime": "^7.8.7", "@hookform/resolvers": "^3.3.2", "@lightdash/sdk": "^0.1818.0", "@nangohq/frontend": "^0.42.14", "@propelauth/react": "^2.0.27", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.7", "@remixicon/react": "^4.5.0", "@segment/snippet": "^5.2.0", "@sentry/nextjs": "^8.55.0", "@tailwindcss/typography": "^0.5.13", "@tanstack/query-sync-storage-persister": "^5.14.0", "@tanstack/react-query": "^5.14.0", "@tanstack/react-query-devtools": "^5.28.14", "@tanstack/react-query-persist-client": "^5.14.0", "@tanstack/react-table": "^8.11.0", "@tremor/react": "^3.17.2", "@typeform/embed-react": "^3.9.0", "@types/qs": "^6.9.12", "@types/react-grid-layout": "^1.3.5", "@vapi-ai/web": "^2.3.1", "apexcharts": "^3.53.0", "axios": "^1.8.2", "blurhash-gradients": "^0.0.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^0.2.0", "date-fns": "^3.0.6", "dayjs": "^1.11.10", "embla-carousel-react": "^8.0.0", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "input-otp": "^1.1.0", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.446.0", "mammoth": "^1.9.0", "mini-svg-data-uri": "^1.4.4", "next": "15.3.3", "next-themes": "^0.2.1", "openai": "^4.54.0", "papaparse": "^5.4.1", "pdfjs-dist": "4.10.38", "plyr": "^3.7.8", "posthog-js": "^1.131.4", "qs": "^6.12.0", "react": "19.1.0", "react-apexcharts": "^1.4.1", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.0", "react-dom": "19.1.0", "react-dropzone": "^14.2.3", "react-grid-layout": "^1.4.4", "react-hook-form": "^7.50.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^1.0.7", "react-toastify": "^11.0.5", "recharts": "^3.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remixicon": "^4.5.0", "rtf-parser": "^1.3.3", "sharp": "^0.33.4", "smartlook-client": "^8.3.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.32.0", "@tanstack/eslint-plugin-query": "^5.12.1", "@types/lodash": "^4.14.202", "@types/node": "^20", "@types/papaparse": "^5.3.14", "@types/prop-types": "^15.7.15", "@types/react": "19.1.8", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "19.1.6", "autoprefixer": "^10.0.1", "eslint": "^9.32.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.5", "globals": "^15.13.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.3.0", "ts-to-zod": "^3.15.0", "typescript": "^5", "typescript-eslint": "^8.17.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": ["prettier --write", "eslint --max-warnings=0 --fix"]}, "resolutions": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}, "overrides": {"react-is": "19.1.0-rc-69d4b800-20241021"}}