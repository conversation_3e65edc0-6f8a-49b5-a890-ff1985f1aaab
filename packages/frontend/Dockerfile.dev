# Stage 1: Build environment
FROM node:22.9.0-alpine AS build

# Set the working directory
WORKDIR /frontend

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Temporarily copy the .env file from the correct path
COPY ../.env .env 

# Install dependencies
RUN yarn install

# Copy the rest of the application code
COPY next.config.js tailwind.config.js postcss.config.js tsconfig.json ./
COPY src/ ./src
COPY public/ ./public
COPY components.json ./components.json

# Build the Next.js app
RUN yarn build

# Remove the .env file before finalizing the image
RUN rm -f .env

# Stage 2: Final image
FROM node:22.9.0-alpine AS final

# Set the working directory for the final image
WORKDIR /frontend

# Copy the built app from the build stage
COPY --from=build /frontend /frontend

# Expose port 3000
EXPOSE 3000

# Start the Next.js app
CMD ["yarn", "dev"]
