<svg width="780" height="770" viewBox="0 0 780 770" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_888_506)">
<path d="M764.031 328.372H389.802C338.802 328.372 313.266 389.225 349.34 424.835L445.154 519.416C424.941 527.511 402.498 531.345 378.976 529.606C306.504 524.351 248.059 466.232 243.348 394.657C237.773 310.123 305.569 239.826 390.018 239.826C413.936 239.826 436.522 245.471 456.484 255.483C476.445 265.495 499.212 271.744 522.122 271.744H528.092C564.994 271.744 600.385 257.258 626.496 231.518L695.048 163.849C699.867 159.091 700.263 151.529 696.055 146.239C677.208 122.736 655.629 101.434 631.819 82.8297C626.46 78.6403 618.799 79.0663 613.98 83.8238L514.677 181.849C503.456 192.926 486.623 196.192 471.985 190.192C471.841 190.121 471.661 190.05 471.518 189.979C456.879 184.015 447.348 169.92 447.348 154.298V15.7281C447.348 9.01789 442.241 3.37283 435.443 2.59176C420.553 0.887588 405.376 0 390.018 0C374.66 0 359.483 0.887588 344.593 2.59176C337.831 3.37283 332.688 9.01789 332.688 15.7281V154.369C332.688 169.991 323.157 184.086 308.518 190.05C308.375 190.121 308.195 190.192 308.051 190.263C293.413 196.263 276.58 192.997 265.359 181.92L166.056 83.8948C161.237 79.1373 153.576 78.7468 148.217 82.9007C124.407 101.505 102.828 122.807 83.9812 146.31C79.7372 151.6 80.1688 159.162 84.9882 163.92L184.291 261.945C195.513 273.022 198.821 289.638 192.743 304.088C192.671 304.23 192.599 304.407 192.527 304.549C186.485 318.999 172.206 328.407 156.381 328.407H15.933C9.13543 328.407 3.41679 333.449 2.62554 340.159C0.899156 354.858 0 369.84 0 385C0 400.16 0.899156 415.142 2.62554 429.841C3.41679 436.516 9.13543 441.593 15.933 441.593H156.381C172.206 441.593 186.485 451.001 192.527 465.451C192.599 465.593 192.671 465.77 192.743 465.912C198.821 480.362 195.513 496.978 184.291 508.055L84.9882 606.08C80.1688 610.838 79.7731 618.4 83.9812 623.69C102.828 647.193 124.407 668.495 148.217 687.099C153.576 691.289 161.237 690.863 166.056 686.105L265.359 588.08C276.58 577.003 293.413 573.737 308.051 579.737C308.195 579.808 308.375 579.879 308.518 579.95C323.157 585.914 332.688 600.009 332.688 615.631V754.272C332.688 760.982 337.795 766.627 344.593 767.408C359.483 769.112 374.66 770 390.018 770C405.376 770 420.553 769.112 435.443 767.408C442.205 766.627 447.348 760.982 447.348 754.272V615.631C447.348 600.009 456.879 585.914 471.518 579.95C471.661 579.879 471.841 579.808 471.985 579.772C486.623 573.772 503.456 577.038 514.677 588.116L613.98 686.141C618.799 690.898 626.46 691.289 631.819 687.135C655.665 668.531 677.244 647.229 696.091 623.69C700.299 618.436 699.939 610.909 695.12 606.151C666.958 578.281 576.179 488.457 576.215 488.386L528.416 441.628H764.067C770.865 441.628 776.583 436.587 777.374 429.876C779.101 415.178 780 400.195 780 385.035C780 369.875 779.101 354.893 777.374 340.195C776.583 333.52 770.865 328.443 764.067 328.443L764.031 328.372Z" fill="#FFC267"/>
</g>
<defs>
<filter id="filter0_i_888_506" x="0" y="0" width="780" height="778" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.758109 0 0 0 0 0.573758 0 0 0 0 0.298741 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_888_506"/>
</filter>
</defs>
</svg>
