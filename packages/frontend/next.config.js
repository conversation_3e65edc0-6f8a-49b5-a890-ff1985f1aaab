/* eslint-disable @typescript-eslint/no-require-imports */
const { withSentryConfig } = require('@sentry/nextjs');

const isProduction = process.env.NODE_ENV === 'production';
const developmentOrigins = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://app.workramp.test',
];

const productionOrigins = [
  'https://hyperbound.ai',
  'https://www.hyperbound.ai',
  'https://beta.hyperbound.ai',
  'https://app.hyperbound.ai',
  'https://vl.veeam.com',
  'https://www.vl.veeam.com',
  'https://my721.com',
  'https://www.alchemistlearningjourney.com',
  'https://veeam.alchemistlearningjourney.com',
  'https://app.pclub.io',
  'https://dev.hyperbound.ai',
  'https://hyperbound-frontend-prod.onrender.com',
  'https://www.workramp.com',
  'https://app.workramp.com',
  'https://*.app.workramp.com',
  'https://sales-coach.com',
  'https://evalueight.ai',
  'https://will-jenkins-recruiter.webflow.io',
  'https://www.journeydelivers.com',
  'https://sellbetter.xyz',
  'https://federicopresicci.com',
  'https://flockjay.com',
  'https://demo.flockjay.com',
  'https://cloud.scorm.com',
  'https://www.cloud.scorm.com',
  'https://*.scorm.com',
  'https://*.highspot.com',
  'https://*.showpad.com',
  'https://*.staging.showpad.biz',
  'https://*.showpad.biz',
  'https://coachem.io',
  'https://*.coachem.io',
  'https://*.dcbstatic.com',
  'https://dcbstatic.com',
];

const cspScriptSrc = [
  'https://cdn.segment.com',
  'https://widget.usepylon.com',
  'https://unpkg.com',
  'https://us-assets.i.posthog.com',
  'https://app.termly.io',
  'https://www.googletagmanager.com',
  'https://snap.licdn.com',
  'https://googleads.g.doubleclick.net',
  'https://opps-widget.getwarmly.com',
];

// Use only production origins in production, otherwise allow all
const trustedOrigins = isProduction
  ? productionOrigins
  : [...developmentOrigins, ...productionOrigins];
/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.output.filename = 'static/chunks/[name].[contenthash].js';
      config.output.chunkFilename = 'static/chunks/[id].[contenthash].js';
    }
    return config;
  },
  async headers() {
    const corsHeaders = trustedOrigins.map((o) => ({
      source: '/(.*?)',
      has: [
        {
          type: 'header',
          key: 'Origin',
          value: `(${o.replace('*', '.*')})`,
        },
        {
          type: 'header',
          key: 'Origin',
          value: `(?<origin>.*)`,
        },
      ],
      headers: [
        {
          key: 'Access-Control-Allow-Origin',
          value: ':origin',
        },
      ],
    }));

    return [
      ...corsHeaders,
      // ✅ Allow caching for other pages & API routes
      {
        source: '/(.*?)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Cross-Origin-Resource-Policy',
            value: 'cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: [
              `frame-ancestors 'self' ${trustedOrigins.join(' ')};`,
              `script-src 'unsafe-inline' 'unsafe-eval' 'self' blob: ${cspScriptSrc.join(' ')};`,
              `worker-src 'self' blob: https://unpkg.com;`,
              `object-src 'none';`,
            ].join(' '),
          },
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Methods', value: 'GET' },
          {
            key: 'Permissions-Policy',
            value: 'microphone=*',
          },
        ],
      },
      // ✅ Ensure correct CORS policy for embeds
      {
        source: '/(.*?)',
        has: [
          {
            type: 'query',
            key: 'embed',
            value: 'true',
          },
        ],
        headers: [
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp',
          },
        ],
      },
      // ✅ Cache chunks and images for 10 minutes
      {
        source: '/(_next|images)/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=600, must-revalidate',
          },
        ],
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'copwjmrddotaxcdlpdsz.supabase.co',
        port: '',
        pathname: '/storage/**',
      },
    ],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

if (process.env.NEXT_PUBLIC_SENTRY_ENABLED === 'true') {
  module.exports = withSentryConfig(nextConfig, {
    sentryUrl: process.env.NEXT_PUBLIC_SENTRY_URL,
    org: process.env.NEXT_PUBLIC_SENTRY_ORG,
    project: process.env.NEXT_PUBLIC_SENTRY_PROJECT,

    // An auth token is required for uploading source maps.
    authToken: process.env.NEXT_PUBLIC_SENTRY_AUTH_TOKEN,
    widenClientFileUpload: true,
    sourcemaps: {
      deleteSourcemapsAfterUpload: true,
    },
  });
} else {
  module.exports = nextConfig;
}
